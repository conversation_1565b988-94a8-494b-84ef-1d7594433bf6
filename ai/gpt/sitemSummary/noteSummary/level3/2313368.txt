SAP Note 2313368 provides detailed information on functional limitations within SAP Master Data Governance (MDG) version 9.0, specifically pertaining to the governance of Business Partners, Customers, and Suppliers.

Key restrictions highlighted in the note include:

1. **Business Partner Relationships**: MDG does not differentiate relationship types for Business Partners. Relationship categories that use a differentiation type, such as "FI0200", "FI0210", "FI0300", "UKM001", and "UKMSB0" cannot be maintained in MDG. Users are advised to check table TBZ9 for potentially affected relationship categories.

2. **Replication Scenarios Using SOAP**: When replicating data using SOAP services between source systems and MDG, it's critical to ensure that both data models align to avoid data loss. The transaction code `MDCMODEL` and report `USMD_DISPLAY_DATA_MODEL` can help check data model consistency.

3. **Central Governance of Addresses**: Regional structure validations such as city and street lookups are unsupported by MDG. For multiple ERP Customer/Supplier assignments to a single Business Partner, the address maintained is always the standard address of the Business Partner, and no unique address assignments are supported.

4. **Data Model Enhancements**: Users must consult SAP note 2984675 before adding enhancements as some central business partner functionalities impose constraints on entity modifications within the BP data model.

5. **Field Properties**: When creating a new Business Partner, the field properties reflect the standard Business Partner grouping for internal numbering. However, if groupings are altered, the new field statuses of attributes may not update on the user interface due to restrictions in the BOL/genIL component.

6. **IDoc Based Replication of Deleted Data**: IDoc replication processes always use the current state of master data, which may cause issues when the last segment of data is deleted, necessitating manual deletion or using Business Partner Web Services instead.

7. **Longtexts**: With MDG 9.0, longtexts for ERP Customers and Suppliers can be maintained but are unformatted to plain ASCII text; formatted longtexts are not supported.

8. **Additional Constraints in Relationships and Tax Numbers**: There are specific customizing restrictions for relationship categories, and maintaining relationship data always requires complete information including both Business Partners and relationships. Moreover, displaying tooltips for changed tax number values does not work as intended due to storage differences.

9. **Consolidation and Mass Processing**: Several features, such as Business Partner Relationships, Longtexts data, and Multiple Assignments cannot be supported during Consolidation or changed via Mass Processing. In consolidation, further assignments cannot be loaded or generated, and in mass processing, only the 'standard' ERP Customer/Supplier assignments linked to the Business Partner can be changed.

10. **Other Terms**: The note lists keywords related to different facets of MDG such as Central Governance, MDGBP, MDG-BP, MDGC, MDG-C, MDGS, MDG-S, indicating the various modules affected by these restrictions.

The note does not articulate specific prerequisites or solutions, as it primarily serves as an informational document outlining restrictions the users should be cognizant of when using MDG 9.0 for governance of Business Partners, Customers, and Suppliers.