**SAP Note 2023030 - Deletion & Blocking of Customer and Vendor in PSM Summary:**

This note is about changes introduced to the Public Sector Management (PSM) component to adapt it for simplified blocking and deletion of customer and vendor data as recommended by SAP Information Lifecycle Management (ILM), based on the guidelines provided in SAP Note 2007926.

Key points from SAP Note 2023030:

- **Symptom**: PSM holds data related to customers or vendors. The note describes changes needed to properly block and delete this data following ILM procedures.

- **Other Terms**: The note references terms such as Data Privacy, ILM, Deletion, Blocking, and Personal Data, which are pertinent to understanding the context and impact of the solution.

- **Reason and Prerequisites**: This note is a follow-up to note 2007926 for more specific applications within PSM.

- **Solution**: The new functionality for simplifying the blocking and deletion process in PSM will be available in specific SAP releases and support package levels:
  - SAP_FIN 617 SP 05
  - SAP_FIN 700 SP 02
  - EA-PS 617 SP 05

  It introduces end-of-purpose (EoP) checks for customers and vendors using ILM objects FI_ACCPAYB and FI_ACCRECV in PSM. The solution involves setup configurations such as:
  - Registration of application names for EoP checks.
  - Support for ILM rule groups and application rule variants for managing residency and retention periods.
  - Registration of classes for EoP checks of customer and vendor data.

- **Details**: Once a customer or vendor is blocked, their personal data will no longer be accessible, preventing any new business transactions with them in PSM. The note specifies that this functionality applies to requests, earmarked documents, and reporting within PSM.

The note references several other SAP Notes (2024169, 2019636, 2017349, 2014170, 2013944, 2012936, 2008518, 2007926, and 1825544) that address related issues or provide further instruction and context for ILM enablement, access to personal data, and the ILM process within PSM and different SAP components.