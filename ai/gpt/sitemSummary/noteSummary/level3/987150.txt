SAP Note 987150 addresses changes in User Exits and BAdIs (Business Add-Ins) for customers using SAP for Automotive who are planning to upgrade to ERP 2005. The note details the changes in the program structure for inbound delivery processing as part of an ERP 2005 upgrade, which includes the integration of a new layer called ID_HANDLING.

Here is a summary of the SAP Note:

- **Reason for the Note**: ERP 2005 introduces a redesign of the program structure for inbound delivery processing within the SAP for Automotive solution that impacts User Exits and BAdIs. A new layer called ID_HANDLING is introduced to ensure consistent inbound delivery data processing across UI, IDOC processing, and BAPI interface.

- **User Exits and BAdIs Affected**: 
  - User Exit EXIT_SAPMBORGR_001 is replaced by BAdI BORGR_DIALOG in ERP 2005.
  - User Exit EXIT_SAPMBORGR_002 was not used prior to ERP 2005 and does not need replacement.
  - BAdIs BORGR_DIALOG, BORGR_POD_DETERMIN, and BORGR_REGISTRATION can still be used in ERP 2005.

- **IDOC Processing Changes**: 
  - The function module /SPE/IDOC_INPUT_DESADV1 replaces BORES_IDOC_INPUT_DESADV1.
  - User Exit USER_EXIT_004 is replaced by the method PROCESS_IDOC_DELVRY03 of BAdI /SPE/BADI_IBDLV_CONTROL (further details in SAP Note 1045312).
  - User Exit USER_EXIT_011 is replaced by the method PARSING_IDOC of BAdI /SPE/INB_ID_HANDLING.
  - User Exit USER_EXIT_012 is no longer available; manipulation of delivery data should be done through methods ENRICH_HEAD and ENRICH of BAdI /SPE/INB_ID_HANDLING. Additional BAdI methods will be available soon as stated in future updates of the note.
  - User Exit USER_EXIT_013 is replaced by the method SAVE of BAdI /SPE/INB_ID_HANDLING. For manipulation of the IDOC status and messages, a new BAdI method will be provided after applying SAP Note 2126235.

It is important to review this note carefully and consider any custom implementations of the affected User Exits and BAdIs to ensure they align with the new structures in ERP 2005 and retain proper functionality post-upgrade.