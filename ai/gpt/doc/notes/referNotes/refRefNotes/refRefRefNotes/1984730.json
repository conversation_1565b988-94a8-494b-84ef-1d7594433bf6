{"Request": {"Number": "1984730", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 658, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000011718092017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001984730?language=E&token=279731BDA390AD4C791ED2505342806D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001984730", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001984730/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1984730"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.08.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-UTL-INR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Repository Infosystem"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Workbench Utilities", "value": "BC-DWB-UTL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-UTL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Repository Infosystem", "value": "BC-DWB-UTL-INR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-UTL-INR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1984730 - Job EU_INIT should be stopped"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The job EU_INIT is automatically scheduled following an upgrade or Support Package. This automatic scheduling is not stopped despite the execution of the report SAPRSEUB_STOP.</p>\r\n<p>You want to generally deactivate the where-used list formation in a production system, for example. However, the job EU_INIT is repeatedly started again.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Upgrade, SAPRSEUB, where-used list, navigation index</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Due to the change to the restart mechanism with SAP Note 1949236, the report SAPRSEUB (job EU_INIT) is automatically started anew following an upgrade or Support Package if the where-used list was previously used for the SAP objects in the system, too.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the corrections from this SAP Note or import the relevant Support Package.</p>\r\n<p>Following the implementation of the correction, calling the report SAPRSEUB_STOP stops the rescheduling of the EU_INIT. If an update job is currently running, it is stopped.</p>\r\n<p>For the execution of the program, the radio button for setting the where-used status is set to the option for updating customer objects only on the selection screen. This means that when the update is started again, only customer objects are analyzed for the where-used list index. This is the default setting.</p>\r\n<p>If, in the case of another update later on, all objects are to be investigated, please set the radio button for setting the where-used status to the option for updating all objects.</p>\r\n<p>Execute the program.</p>\r\n<p>For more information about the status of the where-used list and the job EU_INIT, see SAP Note 2234970.</p>\r\n<p>To generally deactivate the where-used list in the system, please start the program RS_WHERE_USED_SWITCH_OFF following the implementation of this SAP Note. As a result, no index build is started. This is only recommended for production systems.</p>\r\n<p>You can delete existing table content with the program RS_DEL_WBCROSSGT to avoid using memory space unnecessarily.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DWB-TOO (Workbench Tools: Editors, Painter, Modeler)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D030328)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001984730/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984730/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984730/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984730/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984730/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984730/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984730/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984730/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984730/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1949236", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Start of job EU_INIT following upgrade or SP import; WBCROSSGT", "RefUrl": "/notes/1949236"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2801600", "RefComponent": "BC-DWB-UTL", "RefTitle": "How to stop the Where-Used List", "RefUrl": "/notes/2801600 "}, {"RefNumber": "2234970", "RefComponent": "BC-DWB-UTL", "RefTitle": "Job EU_INIT", "RefUrl": "/notes/2234970 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "752", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70033", "URL": "/supportpackage/SAPKB70033"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70031", "URL": "/supportpackage/SAPKB70031"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70034", "URL": "/supportpackage/SAPKB70034"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70118", "URL": "/supportpackage/SAPKB70118"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70116", "URL": "/supportpackage/SAPKB70116"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70218", "URL": "/supportpackage/SAPKB70218"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70216", "URL": "/supportpackage/SAPKB70216"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73012", "URL": "/supportpackage/SAPKB73012"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73015", "URL": "/supportpackage/SAPKB73015"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73112", "URL": "/supportpackage/SAPKB73112"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73118", "URL": "/supportpackage/SAPKB73118"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74007", "URL": "/supportpackage/SAPKB74007"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74020", "URL": "/supportpackage/SAPKB74020"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74015", "URL": "/supportpackage/SAPKB74015"}, {"SoftwareComponentVersion": "SAP_BASIS 750", "SupportPackage": "SAPK-75012INSAPBASIS", "URL": "/supportpackage/SAPK-75012INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 750", "SupportPackage": "SAPK-75011INSAPBASIS", "URL": "/supportpackage/SAPK-75011INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 750", "SupportPackage": "SAPK-75003INSAPBASIS", "URL": "/supportpackage/SAPK-75003INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75107INSAPBASIS", "URL": "/supportpackage/SAPK-75107INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75106INSAPBASIS", "URL": "/supportpackage/SAPK-75106INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75203INSAPBASIS", "URL": "/supportpackage/SAPK-75203INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75202INSAPBASIS", "URL": "/supportpackage/SAPK-75202INSAPBASIS"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 16, "URL": "/corrins/0001984730/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70004 - SAPKB70032&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB70117&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70201 - SAPKB70217&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73001 - SAPKB73014&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73101 - SAPKB73117&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 740&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKB74014&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 750&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-75002INSAPBASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Create the following text elements for the program SAPRSEUB_STOP:<br/>001 Set where-used list for customer objects as active<br/>002 Set where-used list for all SAP objects (incl. customer)<br/><br/>Give both text elements the maximum length of 80. (Field mLen)<br/>Save and activate.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 16, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 5, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1984730 ", "URL": "/notes/1984730 ", "Title": "Job EU_INIT should be stopped", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "759407 ", "URL": "/notes/759407 ", "Title": "EU_INIT job is started repeatedly/you want to stop this job", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1984730 ", "URL": "/notes/1984730 ", "Title": "Job EU_INIT should be stopped", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1984730 ", "URL": "/notes/1984730 ", "Title": "Job EU_INIT should be stopped", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1984730 ", "URL": "/notes/1984730 ", "Title": "Job EU_INIT should be stopped", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1984730 ", "URL": "/notes/1984730 ", "Title": "Job EU_INIT should be stopped", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "1984730 ", "URL": "/notes/1984730 ", "Title": "Job EU_INIT should be stopped", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2297460 ", "URL": "/notes/2297460 ", "Title": "SAPRSEUB runs only briefly, index is not built, SYCM_DOWNLOAD_REPOSITORY_INFO", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2384858 ", "URL": "/notes/2384858 ", "Title": "Size of WBCROSSGT, where-used list index was also compiled for SAP objects", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "750", "Number": "2291102 ", "URL": "/notes/2291102 ", "Title": "EU_INIT bzw. SAPRSEUB wird nach erfolgreicher Ausf&#x00FC;hrung automatisch wieder gestartet", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "752", "Number": "1984730 ", "URL": "/notes/1984730 ", "Title": "Job EU_INIT should be stopped", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2297460 ", "URL": "/notes/2297460 ", "Title": "SAPRSEUB runs only briefly, index is not built, SYCM_DOWNLOAD_REPOSITORY_INFO", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2384858 ", "URL": "/notes/2384858 ", "Title": "Size of WBCROSSGT, where-used list index was also compiled for SAP objects", "Component": "BC-DWB-UTL"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}