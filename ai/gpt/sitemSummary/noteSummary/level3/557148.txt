SAP Note 557148 addresses an issue with conversion exits in the scheduler of BW, where executing these conversions for values, especially numeric fields, results in unexpected results such as missing separators like "." or "-". This problem is due to a program error.

The note specifies that to resolve this issue, customers should import SAPI 30B patch 6. The note also provides detailed information on advance corrections that should be imported based on the Basis release status:

- For Basis Releases as of 3.1I, the transport request files are K001185.BS3 and R001185.BS3.
- For Basis Releases as of 4.0B, the files are K000641.BS4 and R000641.BS4.
- For Basis Releases as of 6.10, the files are K000210.BS5 and R000210.BS5.

These transport files can be found in the directory $home/general/R3server/abap/note.0557148 on the SAP server indicated as either SAPSERV3, SAPSERV4, etc. The note further advises to see OSS note 13719 for additional information on importing the advance correction.

Key terms related to this issue include OLTP, Service API (SAPI), APCO, InfoPackage, selection, scheduler, conversion exits, and the error message codes R8045, R8045, and R845.