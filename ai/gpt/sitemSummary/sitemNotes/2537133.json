{"Request": {"Number": "2537133", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 462, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": true}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": true}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019720102017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002537133?language=E&token=875654C5DA1931F99155895D75A6EFB1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002537133", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002537133/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2537133"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 21}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "HotNews"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.08.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-NA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Note Assistant"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Note Assistant", "value": "BC-UPG-NA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-NA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2537133 - FAQ - Digitally Signed SAP Notes"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This FAQ addresses the following.</p>\r\n<ol>\r\n<li>Important timelines</li>\r\n<li>How to consume&#160;digitally signed SAP Notes in Note Assistant (SNOTE transaction).</li>\r\n<li>Security note <a target=\"_blank\" href=\"/notes/2408073\">2408073</a> - Handling of Digitally Signed notes in SAP Note Assistant.</li>\r\n<li>Initial trouble shooting tips</li>\r\n<li>Fixes for download of digitally signed SAP Notes</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>2408073, Handling of Digitally Signed notes in SAP Note Assistant, Digitally Signed SAP Note, Upload of Digitally Signed SAP Note, Download of digitally signed SAP Notes</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>With the release of security note 2408073, there are questions about the note and what to expect further regarding the topic of digitally signed SAP Notes.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Question 1.</strong> What are the next steps? <strong>(Important Timelines)</strong><br /> <strong>Answer:&#160;</strong>It is important to prepare the ABAP systems in your landscape&#160;to be able to consume digitally signed SAP Notes by the end of 2019 as the download of unsigned SAP Notes will not be&#160;possible from&#160;January 2020.</p>\r\n<p><strong>To facilitate the enablement process, for applicable SAP_BASIS releases, we offer a guided approach which bundles all the actions required into an SAP Note <a target=\"_blank\" href=\"/notes/2836302\">2836302</a>, saving you considerable amount of time. Else, find below the individual steps according to your SAP_BASIS release.</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 1033px; height: 556px;\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"174\">\r\n<p><strong>SAP_BASIS Release</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"228\">\r\n<p><strong>Starting 2020</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"246\">\r\n<p><strong>Preparation for 2020</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"174\">\r\n<p>Below 700</p>\r\n</td>\r\n<td valign=\"top\" width=\"228\">\r\n<p>Manual process to consume digitally signed SAP Notes</p>\r\n</td>\r\n<td valign=\"top\" width=\"246\">\r\n<p>ABAP systems cannot be enabled to consume digitally signed SAP Notes automatically, hence manual process needs to be followed*</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"174\">\r\n<p>700 to 731</p>\r\n</td>\r\n<td valign=\"top\" width=\"228\">\r\n<p>SAPOSS/SAPSNOTE will work only with S-user (recommended Technical Communication User)</p>\r\n</td>\r\n<td valign=\"top\" width=\"246\">\r\n<ul>\r\n<li>Implement SAP Note <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2508268\">2508268</a></span> or SAP Note <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2576306\">2576306</a></span> (TCI)</li>\r\n<li>Implement SAP Note&#160;<a target=\"_blank\" href=\"/notes/2928592\">2928592</a>&#160;to enable HTTP procedure of download</li>\r\n</ul>\r\n<p>Enable one of the following download procedures.</p>\r\n<ul>\r\n<li>Download Service application</li>\r\n</ul>\r\n<p>or</p>\r\n<ul>\r\n<li>HTTP protocol</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"174\">\r\n<p>740 and above</p>\r\n</td>\r\n<td valign=\"top\" width=\"228\">\r\n<p>SAPOSS/SAPSNOTE will not work.</p>\r\n</td>\r\n<td valign=\"top\" width=\"246\">\r\n<p>Implement SAP Note <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2508268\">2508268</a></span> or SAP Note <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2576306\">2576306</a></span> (TCI)</p>\r\n<p>Enable one of the following download procedures.</p>\r\n<ul>\r\n<li>HTTP protocol (The SAP Kernel must be 7.42 PL400 above)</li>\r\n</ul>\r\n<p>or</p>\r\n<ul>\r\n<li>Download service application</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>*</strong> <strong>Please refer the manual process described below.</strong></p>\r\n<p><strong>The <span style=\"text-decoration: underline;\">manual process</span> described below is relevant for the following cases:</strong></p>\r\n<ul>\r\n<li><strong>For ABAP systems below 700</strong></li>\r\n<li><strong>For ABAP systems 700 and above, not enabled to work with digitally signed SAP Notes post 2020.&#160;</strong></li>\r\n</ul>\r\n<ol>\r\n<li>Download digitally signed SAP Notes from SAP One Support Launchpad</li>\r\n</ol>\r\n<p><em>Note: If your ABAP system is having SAP_BASIS 700 and above and the Security Note </em><a target=\"_blank\" href=\"/notes/2408073\">2408073</a><em> is implemented in the ABAP system, the .SAR file (download from the SAP One Support Launchpad) can be directly uploaded in the SNOTE transaction using GoTo-&gt; Upload SAP Note. If not, the subsequent steps below become relevant</em></p>\r\n<ol start=\"2\">\r\n<li>Verify the digital signature using SAPCAR utility and extract the ZIP file</li>\r\n</ol>\r\n<p style=\"padding-left: 30px;\">&#160; &#160; &#160;You can download the new version of the SAPCAR tool as follows.</p>\r\n<ul style=\"list-style-type: circle;\">\r\n<ul>\r\n<li><span style=\"font-size: 14px;\">In SAP Support Portal Software Center, choose &#8216;Downloads&#8217; and enter the search term &#8216;SAPCAR&#8217;.</span></li>\r\n<li><span style=\"font-size: 14px;\">Choose the latest version of SAPCAR (Example SAPCAR 7.21)</span></li>\r\n<li><span style=\"font-size: 14px;\">Then, choose the type of system in the drop-down list. The SAPCAR exe for the selected type will be available for download.</span></li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><em>Note: For more information on SAPCAR utility (Refer the SAP Notes 2178665 and 1634894.</em> <em>Refer knowledge base articles (KBA) <a target=\"_blank\" href=\"/notes/2377859\">2377859</a>, <a target=\"_blank\" href=\"/notes/2234938\">2234938</a>.)</em></p>\r\n<ol start=\"3\">\r\n<li>Unzip the note ZIP file (&lt;note number&gt;.ZIP) to get the TXT file (&lt;note number&gt;.TXT)&#160;</li>\r\n<li>Upload the note TXT file in SNOTE transaction using menu GoTo -&gt; Upload SAP Note.</li>\r\n<li>The SAP Note is now uploaded into the system and ready for implementation (If the SAP Note uploaded is a SAP Note Transport-based Correction Instruction (TCI) and the system is enabled for TCI consumption, then, the relevant TCI package too needs to be uploaded using GoTo -&gt; Upload TCI.)</li>\r\n</ol>\r\n<p><strong>Question 2.</strong>&#160;What to expect with the SAP Notes <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2508268\">2508268</a></span> or <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2576306\">2576306</a></span> (TCI) bringing the feature to download digitally signed SAP Note&#160;in SNOTE transaction?<br /><strong>Answer:&#160;</strong></p>\r\n<p>a. Brings different&#160;procedures for downloading which is customizable.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 1075px; height: 614px;\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"216\">\r\n<p>Download Procedure</p>\r\n</td>\r\n<td valign=\"top\" width=\"84\">\r\n<p>Default Mechanism</p>\r\n</td>\r\n<td valign=\"top\" width=\"414\">\r\n<p>Additional Information</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"216\">\r\n<p>Use RFC protocol</p>\r\n</td>\r\n<td valign=\"top\" width=\"84\">\r\n<p>&#160;Yes</p>\r\n</td>\r\n<td valign=\"top\" width=\"414\">\r\n<ul>\r\n<li>Available from SAP_BASIS release 700 onwards and is&#160;existing from before.</li>\r\n<li>Downloads by default the digitally signed SAP Notes.</li>\r\n<li>Will continue to be available for SAP_BASIS releases 700 to 731 even after 2019.</li>\r\n<li>But, the user within the RFC destinations SAPOSS/SAPSNOTE have to be replaced with S-user (recommended is technical communication&#160;S-user)</li>\r\n<li><strong>From January 2020 this procedure will not work&#160;for SAP_BASIS release 740 and above</strong>. <strong>From November 30, 2020, this procedure will not work for SAP_BASIS 700 to 731.</strong> Alternative is to use HTTP&#160;procedure or Download Service application.&#160;Please refer to&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/2508268\">2508268</a>&#160;or&#160;<a target=\"_blank\" href=\"/notes/2576306\">2576306</a>&#160;(TCI) for SAP_BASIS 740 and above and, SAP Note&#160;<a target=\"_blank\" href=\"/notes/2928592\">2928592</a>&#160;for SAP_BASIS 700 to 731 for&#160;details</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"216\">\r\n<p>Use HTTP protocol</p>\r\n</td>\r\n<td valign=\"top\" width=\"84\">\r\n<p>&#160;No</p>\r\n</td>\r\n<td valign=\"top\" width=\"414\">\r\n<ul>\r\n<li>Available from SAP_BASIS release 700 and above.</li>\r\n<li>Can be customized. Please refer to&#160;SAP Note <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2508268\">2508268</a></span> or <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2576306\">2576306</a></span> (TCI) for SAP_BASIS 740 and above and, SAP Note <a target=\"_blank\" href=\"/notes/2928592\">2928592</a>&#160;for SAP_BASIS 700 to 731 for details</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"216\">\r\n<p>Use download service</p>\r\n</td>\r\n<td valign=\"top\" width=\"84\">\r\n<p>&#160;No</p>\r\n</td>\r\n<td valign=\"top\" width=\"414\">\r\n<ul>\r\n<li>Available from SAP_BASIS release 700 and above.</li>\r\n<li>Can be customized. Please refer to&#160;SAP Note <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2508268\">2508268</a></span> or <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2576306\">2576306</a></span> (TCI) for details.</li>\r\n<li>Download service is coupled with SAP Solution Manager 7.2. It is also available with the SAP Solution Manager 7.1 but from higher support packages of SAP_BASIS releases (for example SAP_BASIS release 702, support package 17 onwards).</li>\r\n<li>Basically, any ABAP system having download service can be used as download system and linked to the managed system (where SAP Note is to be downloaded) through RFC connection.</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><em><span style=\"text-decoration: underline;\">Note regarding the download procedure &#8211; HTTP Protocol: </span></em></p>\r\n<ul>\r\n<li><em>SAP Note <a target=\"_blank\" href=\"/notes/2827658\">2827658</a> creates automatically the new RFC destinations needed for the new Support Backbone Communication. For Digitally Signed Note download using HTTP Protocol, the required RFC destinations can be created automatically using the above SAP Note.</em></li>\r\n</ul>\r\n<p><em><span style=\"text-decoration: underline;\">Note regarding the download procedure &#8211; Download Service:</span></em><em></em></p>\r\n<ul>\r\n<li><em>Any ABAP system having the Download Service application installed in it is referred to as &#8216;Download system&#8217;. It is not mandatory that only Solution Manager system should act as Download System. Any ABAP system in the landscape could become a 'Download System'.</em></li>\r\n<li><em>The RFC destination used to connect to the download system (from the managed system), must have a user with the role SAP_BC_SDS_TASK_USER. This role is delivered with ABAP download service and allows to execute a download within the download system.&#160;The user must also have permission to execute the RFC APIs (</em><em>SDS_GET_DL_XML, SDS_GET_DOWNLOAD_FILE, SDS_NOTES_REQUEST_INFO, STC_TM_*) </em><em>of the download service application and Task Manager. i.e. the S_RFC authorization must include the above-mentioned RFC APIs.</em></li>\r\n<li><em>At present the download service does not offer to delete a downloaded file from the download directory (Ex: DIR_TRANS/EPS/in/). This is to a certain point on purpose, as default configuration will download each file only once if it still exists and has the correct size. This makes sense for SAP Notes, as the same SAP Notes might be implemented on multiple systems, so no additional download is needed for the second download request. The customer in addition has the possibility to adjust the download location via the FILE transaction according to the ABAP download service documentation(in SAP Note <a target=\"_blank\" href=\"/notes/2554853\">2554853</a>). So, you can also put things to a directory which is cleared periodically.</em></li>\r\n</ul>\r\n<p>b. The possibility to download unsigned SAP Note can be customized even after the implementation of the new feature (download of digitally signed SAP Note from SNOTE transaction). But, will be supported only until end of 2019.</p>\r\n<p><strong>Question 3.</strong>&#160;What about ABAP systems where SAP Notes are always uploaded using GoTo-&gt;Upload SAP Note (that is, no direct download is allowed)?<br /><strong>Answer:&#160;</strong>If the process in your company is to always upload the SAP Notes, make sure that the security SAP Notes <a target=\"_blank\" href=\"/notes/2408073\">2408073</a> and <a target=\"_blank\" href=\"/notes/2546220\">2546220</a> are implemented. Then, the downloaded &lt;note number&gt;.SAR file from the SAP One Support Launchpad can be directly uploaded in SNOTE.</p>\r\n<p><strong>Question 4.</strong>&#160;When is the digitally signed SAP Notes expected to be released by SAP?&#160;<br /><strong>Answer:&#160;</strong></p>\r\n<p>a. All SAP Notes released for customers are digitally signed.<br /> b. <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"https://support.sap.com/en/index.html\">SAP Support Portal Home&#160;</a></span>provides only digitally signed SAP Notes (live from 29th November 2017).<br /> c. Feature to download digitally signed SAP Notes (as SAR files) directly&#160;in the SNOTE transaction is available via SAP Notes <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2508268\">2508268</a></span> or <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"/notes/2576306\">2576306</a></span> (TCI)</p>\r\n<p><strong>Question 5.</strong>&#160;From when SAP Note 2408073 implementation is mandatory? <br /> <strong>Answer:</strong> To be able to upload or consume&#160;the digitally signed SAP Notes in the SNOTE transaction, the Security Note&#160;<a target=\"_blank\" href=\"/notes/2408073\">2408073</a> is mandatory. Also&#160;another subsequent Security Note&#160;<a target=\"_blank\" href=\"/notes/2546220\">2546220</a> has to be implemented.</p>\r\n<p><strong>Question 6.</strong>&#160;Is there a need to update SAP_BASIS component?<br /> <strong>Answer:</strong> No. You do not require to update SAP_BASIS component to consume digitally signed SAP Notes. Please refer to the <a target=\"_blank\" href=\"https://support.sap.com/content/dam/support/en_us/library/ssp/my-support/knowledge-base/note-assistant/cheatsheet-for-digsignnotes_snote_tci.pdf\">cheat sheet </a>in Support portal to know about the enabling of SNOTE in your respective SAP_BASIS release.</p>\r\n<p><strong>Question 7.</strong>&#160;Will it be possible to upload unsigned SAP Notes using SNOTE after the implementation of the SAP Note <a target=\"_blank\" href=\"/notes/2408073\">2408073</a>, If yes, until when?<br /> <strong>Answer:</strong> Unsigned SAP Notes will be supported by SNOTE transaction until end of 2019. However, download of unsigned SAP Notes (as ZIP files) from&#160;<span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"https://support.sap.com/en/index.html\">SAP Support Portal Home </a></span>is&#160;stopped&#160;from end of November 2017.<br /> Download of unsigned SAP notes in SNOTE would still be made possible&#160;through RFC connection to OSS system until end of 2019.</p>\r\n<p><strong>Question 8.</strong> 'Error occurred during character conversion' occurs while uploading SAR file.<br /> <strong>Answer:</strong> The root cause of the issue is that the system being Non-Unicode system. For Non-Unicode system, some characters in the digitally signed note file (SAR) cannot be converted to text during upload and hence the issue. Please use Unicode system which can support character conversions for digitally signed SAP notes or use the workaround to extract the SAR file in your system using SAPCAR utility and upload the TXT file using SNOTE transaction. Please refer to the manual process described under question 1 above.</p>\r\n<p><strong>Question 9.</strong> Is it necessary to enable all ABAP systems in the landscape for digitally signed SAP Notes?<br /><strong>Answer:&#160;</strong>Usually SAP Notes are implemented in the development systems and later transported to the subsequent systems in the landscape. If you implement notes in test or productive systems during update or upgrade, it becomes necessary to also prepare them for digitally signed SAP Notes.</p>\r\n<p><strong>Question 10.</strong> What is Technical Communication user, and should it be created one per each ABAP system?<br /><strong>Answer:&#160;</strong>Please see FAQ <a target=\"_blank\" href=\"https://support.sap.com/en/release-upgrade-maintenance/maintenance-information/connectivity-to-sap/sap-support-backbone-update-faq.html\">https://support.sap.com/en/release-upgrade-maintenance/maintenance-information/connectivity-to-sap/sap-support-backbone-update-faq.html</a>, especially chapter 'User handling', for options and recommendation.</p>\r\n<p><strong>Question 11.</strong>&#160;How to check if the ABAP system is rightly configured or set up for download of Digitally Signed SAP Notes?<br /><strong>Answer:&#160;</strong>Once the steps required for enabling SNOTE for Digitally Signed SAP Notes are completed, you could perform a download of any SAP Note or SAP Note 2424539 (test note) and check in the corresponding note log for the message that states that &#8220;digitally signed&#8221; note was downloaded. Or, you could implement the SAP Note <a target=\"_blank\" href=\"/notes/2836302\">2836302</a> and relaunch the SNOTE transaction. If the SNOTE is not meeting the expected set up, a pop-up will appear with the needed information. The SAP Note <a target=\"_blank\" href=\"/notes/2836302\">2836302</a> also brings a report that gives guided steps to get the right set up.</p>\r\n<p><strong>Question 12.</strong>&#160;Error verifying the digital signature during download/upload of Digitally Signed SAP Notes. For example, \"Digital Signature of file &lt;name&gt; not valid\"&#160;<br /><strong>Answer:&#160;</strong>This could be due to the lower kernel levels. The kernel levels in the ABAP system are expected to be as follows.</p>\r\n<div>\r\n<div>\r\n<div>\r\n<div>\r\n<p>kernel 721 PL916</p>\r\n<p>kernel 722 PL413</p>\r\n<p>kernel 745 PL610</p>\r\n<p>kernel 749 PL315</p>\r\n<p>kernel 753 PL18</p>\r\n<p>or any subsequent kernel release</p>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n<p><span style=\"text-decoration: underline;\"><strong>Initial trouble shooting tips:</strong></span></p>\r\n<p><strong><span style=\"text-decoration: underline;\">A. RFC destinations to be checked</span></strong></p>\r\n<p>The download of digitally signed SAP Notes does not work even after following the guide&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/9b304f6aea25485192e911d8a9c026fe/latest/en-US/ca605ed5d77e441ebbea0e5b254b2180.html\">Enabling Note Assistant for Digitally Signed SAP Notes</a>&#160;or <a target=\"_blank\" href=\"https://support.sap.com/en/my-support/knowledge-base/note-assistant.html\">Note Assistant</a>&#160;page in Support Portal. Below are some of the checks that can ensure that the basic things are done correctly in the system.</p>\r\n<p>1. Execute the RCWB_SNOTE_DWNLD_PROC_CONFIG to know the note download procedure chosen. And based on the option perform the checks given below.</p>\r\n<ul>\r\n<li>RFC option selected or no option is selected:</li>\r\n</ul>\r\n<p>This option will not work from November 30, 2020. Please do not use this option. Please refer to SAP Note <a target=\"_blank\" href=\"/notes/2923799\">2923799</a> for details.</p>\r\n<ul>\r\n<li>HTTP Protocol selected</li>\r\n</ul>\r\n<p>This option is available only from SAP_BASIS release 700 and above. Check if the connection test in SM59 of the RFC destination SAP- SUPPORT_PORTAL (H type) is 200 and that of SAP-SUPPORT_NOTE_DOWNLOAD (G type) is 400 (this is ok to be 400). The Technical communication user credentials must be maintained in each of these destinations.</p>\r\n<ul>\r\n<li>Download Service selected</li>\r\n</ul>\r\n<p>This option is available from SAP_BASIS 700 and above. The <em>RFC Destination for Download System</em>&#160;field must have the name of the RFC destination that is pointing to the ABAP system where the download service application is installed. The connection test of the RFC destination must be successful. If the download service application is installed in the same system where you are doing these checks, the value 'NONE' must be maintained in the field&#160;<em>RFC Destination for Download System</em>. Please refer to SAP Note <a target=\"_blank\" href=\"/notes/2554853\">2554853</a> to set up download service application in any ABAP system.</p>\r\n<p>&#160;2. The SAPCAR executable in your system should be of 7.20 patch level 2 or higher version. If not, the digital signature verification fails and the files are not extracted.</p>\r\n<p>&#160;3. The ABAP user working with SNOTE has the authorizations for executing SAPCAR as given in the chapter 1.3 in the PDF attached with SAP Note <a target=\"_blank\" href=\"/notes/2508268\">2508268</a> or <a target=\"_blank\" href=\"/notes/2576306\">2576306</a></p>\r\n<p><strong><span style=\"text-decoration-line: underline;\">B. Authorizations required for user downloading SAP Notes in SNOTE</span></strong></p>\r\n<p>The user in the ABAP system using the Note Assistant tool to download or upload digitally signed SAP Note must have the following authorization objects.</p>\r\n<ul>\r\n<li><strong>S_LOG_COM</strong></li>\r\n</ul>\r\n<p>The verification of digital signature is performed by the Note Assistant<em> </em>tool using the SAPCAR utility and for executing SAPCAR command during signature verification the above authorization object is needed. Please refer to SAP Note <a target=\"_blank\" href=\"/notes/854060\">854060</a> for more details.</p>\r\n<ul>\r\n<li><strong>S_DATASET</strong></li>\r\n</ul>\r\n<p>The download or upload of digitally signed SAP Note in Note Assistant writes the Note SAR file into the application server of the system. This requires the above authorization object.</p>\r\n<ul>\r\n<li><strong>S_RFC_ADM</strong></li>\r\n</ul>\r\n<p>During the download of digitally signed SAP Notes in Note Assistant relevant RFC destinations are read. This requires the above authorization object.</p>\r\n<ul>\r\n<li><strong>S_APPL_LOG</strong></li>\r\n</ul>\r\n<p>If the verification of digital signature for an SAP Note fails, the Note Assistant tool logs the security event in the application server using log object (CWBDS). To view the application logs, you should have the above authorization object.</p>\r\n<p><strong>Fixes for download of digitally signed SAP Notes:</strong></p>\r\n<p>The following composite SAP Note contains fixes for issues with handling of digitally signed SAP Notes. These have to be implemented after the enablement of SNOTE for digitally signed SAP Notes&#160;by SAP Note&#160;<a target=\"_blank\" href=\"/notes/2508268\">2508268</a> or <a target=\"_blank\" href=\"/notes/2576306\">2576306</a>&#160;(TCI)</p>\r\n<p><a target=\"_blank\" href=\"/notes/2869143\">2869143</a> - Composite note for handling of Digitally Signed SAP Notes in Note Assistant (SNOTE transaction)</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I043353)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I043353)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002537133/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002537133/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002537133/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002537133/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002537133/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002537133/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002537133/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002537133/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002537133/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2737826", "RefComponent": "XX-SER-NET", "RefTitle": "SAP is going to close its proprietary RFC communication for automated data exchange between Customers & SAP Support Backbone (SAPOSS) by July 2020.", "RefUrl": "/notes/2737826 "}, {"RefNumber": "2191130", "RefComponent": "BC-UPG-NA", "RefTitle": "Problems with the SAPOSS or SAP-OSS destination", "RefUrl": "/notes/2191130 "}, {"RefNumber": "2881984", "RefComponent": "BC-UPG-NA", "RefTitle": "Getting popup for credentials of SAP system O71/O72 when operating with SNOTE", "RefUrl": "/notes/2881984 "}, {"RefNumber": "2882471", "RefComponent": "BC-UPG-NA", "RefTitle": "SPAU: Error when getting status of note <note_number>; problem with RFC connection", "RefUrl": "/notes/2882471 "}, {"RefNumber": "2879805", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Name or password is incorrect (repeat logon)", "RefUrl": "/notes/2879805 "}, {"RefNumber": "2798692", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWN 810: NONE when downloading SAP notes using the Download Service Application option", "RefUrl": "/notes/2798692 "}, {"RefNumber": "2740667", "RefComponent": "XX-SER-NET", "RefTitle": "SAP is going to close its proprietary RFC communication for automated data exchange between Customers & SAP Support Backbone (SAPOSS) by July 2020.", "RefUrl": "/notes/2740667 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "753", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}