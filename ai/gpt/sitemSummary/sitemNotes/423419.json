{"Request": {"Number": "423419", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 329, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015104722017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000423419?language=E&token=9A6977145588A9D10CD9E8C7651439B8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000423419", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000423419/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "423419"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.09.2007"}, "SAPComponentKey": {"_label": "Component", "value": "IS-B"}, "SAPComponentKeyText": {"_label": "Component", "value": "Bank"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Bank", "value": "IS-B", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-B*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "423419 - Additional info about 4.6C upgrade BANKING 4.63/CFM 2.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note includes current information about the upgrade to R/3 Release 4.6C with the add-on SAP BANKING or the add-on SAP TR-TM-PO.<br />There is no add-on-specific guide.<br /><br />This note contains all add-on-specific additional information that is relevant for the upgrade with the following upgrade procedure:<br /> <B>R3up</B>&#x00A0;&#x00A0;(exchange upgrade -<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;including an add-on upgrade in the SAP R/3 upgrade)<br />or <B>SAINT</B> (delta upgrade<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R/3 release (in this case Release 4.6C) remains the same)<br /><br />BANK/CFM 463_20 is the technical name for</p> <UL><LI>SAP BANKING 4.63</LI></UL> <UL><LI>SAP CFM 2.0 (Corporate Finance Management)</LI></UL> <p><br />Information: If you want to import SAP BANKING 4.63 or SAP CFM 2.0 for the first time and you already use R/3 Release 4.6C, you you must perform an add-on installation. For detailed information about this, see release strategy note 339451 or in installation note 423364.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />Upgrade, R/3 add-on, BANKING, CFM, TR-TM-PO<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />The following exchange upgrade is supported:</p> <UL><LI>R/3 4.0B with add-on BANKING 4.03A</LI></UL> <UL><LI>R/3 4.6B with add-on BANKING 4.61 or</LI></UL> <UL><LI>R/3 4.6B with add-on TR-TM-PO 1.0</LI></UL> <p><br />Or delta upgrade</p> <UL><LI>R/3 4.6C with add-on BANK/CFM 462_10</LI></UL> <p><br />To the target release<br />R/3 4.6C with add-on BANK/CFM 463_20<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>This note is changed frequently. Make sure you have the latest version when starting the upgrade.</B><br /><br />Change history<br />Date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Chapter&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description<br />------------------------------------------------------------------------<br />31.07.2001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1. Release of the note<br />08.08.2001&#x00A0;&#x00A0;IV-B/1&#x00A0;&#x00A0;Delta upgrade with plug-in PI 2001_1_46C<br />17.09.2001&#x00A0;&#x00A0;IV-B/1+2 Update or preparation for Insurance systems<br />10.10.2001&#x00A0;&#x00A0;V/1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade and implementation of SP/CRTs<br />15.10.2001&#x00A0;&#x00A0;IV-A/3&#x00A0;&#x00A0; BIND_PATCH<br />15.10.2001&#x00A0;&#x00A0;III/5&#x00A0;&#x00A0;&#x00A0;&#x00A0;New R3up<br />10.11.2001&#x00A0;&#x00A0;V/2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(only SAINT) XPRA / RH_ACTIVATE_PDOBJECT_AFTER_IMP<br />28.11.2001&#x00A0;&#x00A0;V/3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(only R3up) Missing languages in English<br />06.03.2002&#x00A0;&#x00A0;I/6&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additional note for the local currency changeover<br />14.08.2002&#x00A0;&#x00A0;V/2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(only SAINT) XPRA / RH_ACTIVATE_PDOBJECT_AFTER_IMP<br />18.09.2002&#x00A0;&#x00A0;VIII&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note 398888<br />17.01.2003&#x00A0;&#x00A0;V/2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TRANSACTIONS_SEC_LOAD_V_BCKI (FVV2|08)<br />18.03.2003&#x00A0;&#x00A0;III&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Obtain the CURRENT R3up (&gt;= 8.012). Otherwise, patches may not be imported.<br /></p> <b>Contents</b><br /> <p><br />&#x00A0;&#x00A0;I/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Important General Information<br />&#x00A0;&#x00A0;II/&#x00A0;&#x00A0; Errors on the CD-ROM<br />&#x00A0;&#x00A0;III/&#x00A0;&#x00A0;Check Prior to the Upgrade<br />&#x00A0;&#x00A0;IV-A/ Additional information about the exchange upgrade<br />&#x00A0;&#x00A0;IV-B/ Additional information about the delta upgrade<br />&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problems after the Import<br />&#x00A0;&#x00A0;VI/&#x00A0;&#x00A0; R3up keyword<br />&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;Activities after the Upgrade<br />&#x00A0;&#x00A0;VIII/ Other Notes which have to be Considered<br /><br /></p> <b>&#x00A0;&#x00A0;I/ Important General Information</b><br /> <OL>1. Required SAP R/3 and SAP BANKING, SAP TR-TM-PO or SAP BANK/CFM release status</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For the upgrade procedures described here, you require one of the following releases: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exchange upgrade (R3up): <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP R/3 4.0B with SAP BANKING 4.03A <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP R/3 4.6B with SAP BANKING 4. 61 and PI 1999_1_46B, 2000_1_46B or higher <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP R/3 4.6B with SAP TR-TM-PO 1.0 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Delta upgrade (SAINT) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP R/3 Release 4. 6C with SAP BANK/CFM 462_10 and PI 2000_2_46C <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<OL>2. Supplementary CD</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;When you perform an exchange upgrade to upgrade an SAP R/3 Release to Release 4.6C, the system determines that an ABAP add-on is installed and requests an additional CD for the add-on. As a result, the ABAP add-on is also brought to the relevant status during the upgrade. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the upgrade, the system also changes the add-on name. The technical name of the add-on changes from \"BANKING\" or \"TR-TM-PO\" to \"BANK/CFM\". <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the delta upgrade, the current version of the SAP release is not changed, while the add-on release is upgraded to a new release (in this case Banking 4.63 or CFM 2.0). <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>CAUTION:</B> <OL>3. Prerequisites for BANK/CFM 463_20</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The add-on BANK/CFM 463_20 requires the add-on <B>PI 2000.2 PatchLevel-5</B> or higher. You can include the add-on in the upgrade with a PI additional CD (further information in III/2). We recommend that you use the current PI release. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Furthermore, BANK/CFM 463_20 requires ABA Support Package level 22. (For more information, see III/3.) <p></p> <OL>4. Additional note for using securities management</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you use securities management, see Note 370686, which describes the required preparation and follow-ups for the upgrade. <OL>5. Additional note for the local currency changeover</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to carry out a local currency changeover under CFM 2.0 / BANKING 4.63, contact SAP. For more information, see Note 501239. <p></p> <b>&#x00A0;&#x00A0;II/ Error on the CD-ROM</b><br /> <p></p> <b>&#x00A0;&#x00A0;III/ Check Prior to the Upgrade</b><br /> <OL>1. Required upgrade CDs</OL> <UL><LI>For the exchange upgrade (R3up)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CD with material number: ********</p> <UL><LI>For the delta upgrade (SAINT)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CD with material number: ******** (upgrade from BANKING 4.62)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;******** (upgrade from CFM 1.0)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Make sure you use the R/3 4. 6C Support Release 2 (4.6C SR2) upgrade CDs for the upgrade to BANK/CFM 463_20. <OL>2. Preparations for the required add-on PI</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you use BANKING 4. 61 or BANK/CFM 462_10, PI 1999_1_46B or PI 2000_1_46B (or higher) is already installed in your system. You have to upgrade this PI release to source release 2000_2_46B or 2001_1_46B to be able to include it in the upgrade. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You require an additional CD for PI for the upgrade.&#x00A0;&#x00A0;This CD contains the required PI Release 2000_2_46C. When you import the data, see Notes 214503 and 181255. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PI 2000_2_46C requires patch level 5, which you can download from SAP Service Marketplace. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to use PI 2001_1_46C, you require the relevant additional CD, and you should consider Note 214503 and 181255. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommend that you use the current PI release. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you are currently using BANKING 4. 03A or TR-TM-PO 1.0, the add-on PI is usually not yet installed. To include PI in the upgrade, you must make preparations before starting the upgrade. The preparations are described in Note 214502 and Note 329949.&#x00A0;&#x00A0;Follow the instructions for PI 2001_1_46C. <OL>3. Including additional ABA Support Packages in the upgrade</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The software BANK/CFM 463_20 requires 22 4. 6C ABA Support Packages. R/3 46C Upgrade Support Release 2 contains all Support Packages up to 15. If you perform the R/3 4.6C SR2 upgrade, you have to include seven additional ABA Support Packages because the upgrade contains only 15 ABA Support Packages. <OL>4. Including additional ABA Support Packages in the upgrade</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The BANK/CFM 462_10 software requires 15 4.6C Support Packages, which are already contained in the R/3 4.6C SR 2 upgrade. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you use the 4. 6C SR2 exchange upgrade, you can include further R/3 Support Packages in the upgrade. However, you must make sure that the higher Support Package level is supported by a BANK/CFM Support Package (you can check this in the SAP software distribution center: http://service.sap.com/patches &gt;&gt; Entry by Application Group &gt;&gt; Industry-Specific Components &gt;&gt; BANKING &gt;&gt; Check BANKING 4.63/CFM 2.0.) Moreover, you have to observe the instructions listed in section IV-A/3 (BIND_PATCH) to be able to perform the upgrade. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please note that the standard procedure supports only SP0-15 and SP22 with CRT1, SP23 with CRT2 and SP24 with CRT3. SP16-21 are NOT supported. <OL>5. A new R3up is required (only for R/3 4.6C SR2). After you have unpacked the upgrade data from the 4.6C SR2 KernelCD and before you start PREPARE (at the latest before the PREPARE phase CD_READ), you have to exchange the file R3up or R3up.exe with a newer version. The newer version is on the sapservX in the directory:<br />general/R3server/patches/rel46D/&lt;OSType&gt;/&lt;OSys&gt;/R3up46CSR2_3.CAR<br />Replace &lt;OSType&gt; and &lt;OSys&gt; with your operating system. (There may already be more recent R3up patches; in this case, increase the number of the CAR archive).<br />Unpack the CAR archive using CAR -xvf &lt;Archive Name&gt; and replace R3up in the directory /usr/sap/put/bin for UNIX or replace R3up.exe in the directory \\usr\\sap\\put\\exe for NT. (If you have already started the PREPARE, you must stop the PREPARE and the Upgrade Assistant and start them again). When you call R3up -V, the system must display the following:<br />This is R3up version 4.6/C.2, patch level 8.012<br />or higher.</OL> <OL>6. SPAM version 27 or higher required</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you carry out an exchange upgrade to R/3 Release 4.6C SR2, you require at least version 0027 of transaction SPAM in the source release before starting the upgrade. Check the SPAM version by calling transaction SPAM, if required, and import the latest SPAM update for your source release before you start the PREPARE. <p></p> <b>&#x00A0;&#x00A0;IV-A/&#x00A0;&#x00A0; Additional information about the exchange upgrade</b><br /> <OL>1. Enhancements for PREPARE</OL> <OL><OL>a) Phase IS_CHK</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In this phase, the system checks the software products that are installed in the SAP System in addition to the standard components. The system indicates that BANKING or TR-TM-PO and PI are installed. If you have not installed any further add-on, choose \"nothing else\". <OL><OL>b) Phase IS_READ</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In this phase, you have to include the additional PI CD (see section III.). Confirm the relevant prompt by choosing \"CD mounted\". If you did not install any further ABAP add-ons after you imported the BANK/CFM CD and PI CD, you do not require any additional add-on CDs. <OL><OL>c) Phase BIND_PATCH:</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you included additional Support Packages (higher than 15), you must include further BANK/CFM Support Packages. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example for BIND_PATCH in the 4.6C SR2 upgrade:<br />&gt; Store the *.PAT files of the BANK/CFM Support Packages to be included (and other Support Packages) in the EPS/in directory (as described in the upgrade guide).<br />&gt; During the phase BIND_PATCH, confirm the search for new Support Packages in the EPS/in directory with 'yes'.<br />&gt; The queue calculator calculates a queue. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Important: Do not include more Support Packages than are supported by the BANK/CFM Support Packages.) <OL>2. Enhancements for R3up</OL> <OL><OL>a) Phase KEY_CHECK</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The system requires the password for BANK/CFM and refers to Note 86985. Note 86985 provides a general description for releasing SAP R/3 Releases for all add-ons. In the case of BANK/CFM and 4.6C, the note refers to Note 335139, the note you are currently reading. You can find the password below in chapter VII. <p></p> <b>&#x00A0;&#x00A0;IV-B/&#x00A0;&#x00A0; Additional information about the delta upgrade</b><br /> <OL>1. Delta upgrade with plug-in PI 2001_1_46C</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After you have met the prerequisites for the delta upgrade to BANK/CFM 463_20, use transaction SAINT to perform the upgrade. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you already imported PI 2001_1_46C into your system before the BANK/CFM upgrade, SAINT does not perform the BANK/CFM upgrade because only PI 2000_2_46C was released in the delivered import conditions (by mistake). To allow you the import for PI 2001_1_46C, proceed as follows: <UL><UL><LI>Download the BANK/CFM 463_20 Add-On Support Package/CRT 01 (SAPKIPBJ01) from SAP Service Marketplace so that it is displayed in transaction SPAM under \"New Support Packages\". In transaction SPAM choose \"Utilities &gt; Reload Package Attributes\" and specify the package SAPKIPBJ01.</LI></UL></UL> <UL><UL><LI>Then you can perform the upgrade with SAINT.</LI></UL></UL> <OL>2. Preparation with INSURANCE systems</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to carry out the upgrade to BANK/CFM 463_20 to a system that already contains the INSURANCE add-on, you have to perform the following steps before starting the import of BANK/CFM. <UL><UL><LI>Download the BANK/CFM 463_20 Add-On Support Package/CRT 01 (SAPKIPBJ01) from SAP Service Marketplace so that it is displayed in transaction SPAM under \"New Support Packages\".</LI></UL></UL> <UL><UL><LI>In transaction SPAM choose \"Utilities &gt; Reload Package Attributes\" and specify the package SAPKIPBJ01.</LI></UL></UL><p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These actions prevent the system from displaying conflicts with the INSURANCE add-on when importing the add-on BANK/CFM. <b>&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problems after the import</b><br /> <OL>1. October 10, 2001: termination in MAINIMPORT</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the upgrade to BANK/CFM 463_20 and the simultaneous inclusion of Support Package 22/CRT1, Support Package 23/CRT2, the system overwrites function modules in the function group TBDI. This affects the function modules \"TB_DATAFEED_MODIFY_TWH01\" and \"TB_DATAFEED_MODIFY_ATVO5\". <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description of error: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The upgrade terminates in the phase MAINIMPORT with the message \"function TB_DATAFEED_MODIFY_ATUO5 (TBDI 19) does not fit into the existing function group (TBDI 18)\". <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problem solution: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you have implemented SP24/CRT3, the system automatically corrects the function group. If you have implemented SP22/CRT1 or SP23/CRT2, you require a correction that correctly includes the overwritten function module again. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import the transport HL8K000086 into your system. Transport HL8K000086 is stored on sapserv in the directory /general/R3server/abap/note.0423364. For additional information about importing sapserv transports, see Note 13719. <OL>2. 10.11.2001, (only SAINT) XPRA / RH_ACTIVATE_PDOBJECT_AFTER_IMP</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The system reports an error during the upgrade from BANK/CFM 462_10 to BANK/CFM 463_20 with the following message in the phase XPRA: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description of error:<br />Subsequent processing RH_ACTIVATE_PDOBJECT_AFTER_IMP for PDST T startet<br />Start activation of object RY ******** (client: 000) 091049<br />Error while activating<br />...<br />Errors occurred during post-handling RH_ACTIVATE_PDOBJECT_AFTER_IMP fo<br />The errors affect the following components:<br />BC-BMT-OM (Organizational Management)<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause:<br />The system supplies data during the upgrade, and this data may cause errors of this type in the customer system.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problem solution:<br /> This error has no effect and can be ignored. However, the other parts of the XPRA phase must be executed so that only this particular error has to be skipped. If this error occurs in your system, download the file AISB16G.SAP from the directory sapserv3 general/R3server/abap/note.0423419 and save it in the directory DIR_PUT/cofiles. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Then repeat the phase XPRA.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;14.08.2002 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Under certain circumstances (depending on Customizing), the allow file AISB16G.SAP may not be sufficient, and the phase XPRA encounters return code 8. If you find the following error text in the XPRA log: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4EE5W602 Please maintain entry PLOGI WORKF or PLOGI PLOGI in table T77S0 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use transaction SOBJ to change the transport object PDST: Delete the method entry \"AFTER_IMP\". <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Then repeat the XPRA phase. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<OL>3. 27.11.2001 Missing languages in English (only R3up/CFM 2.0)</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Some texts in CFM 2. 0 are not included in English in the installation or the upgrade after the R3up procedure; therefore, they are displayed in German. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use transaction SMLT to subsequently import the missing texts. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import the language import EN423419. PAT into your system. The import is provided on sapserv, in the directory /general/R3server/abap/note.0423419. For additional information about importing sapservX transports, see Note 13719. Then start transaction SMLT and import the file of the operating system level into your R/3 system. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For control purposes: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(18.630.652 bytes / Nov 28 13:27 / EN423419. PAT) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;17.01.2003 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Under certain circumstances, the upgrade terminates in SHADOW_IMPORT_REP. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description of error: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Function \"TRANSACTIONS_SEC_LOAD_V_BCKI (FVV2|08)\" does not fit into <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; the existing function group (\"(FVBV|02)\"). \" <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The solution is the same as described in maintenance note 352072, section II.2. Load the specified allow file to the directory DIR_PUT/cofiles. Repeat the phase. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<b>&#x00A0;&#x00A0;VI/&#x00A0;&#x00A0; R3up keyword</b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The password in phase KEY_CHK is: 270340 <p></p> <b>&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;Activities after the upgrade</b><br /> <OL>1. Publish Internet Application Components (IACs)</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to use add-on-specific&#x00A0;&#x00A0;Internet Application Components (IACs), you must publish them from the SAP system on the Internet Transaction Server (ITS). Publishing IACs is described in Note 325149 and you can do this using program W3_PUBLISH_SERVICE. This program requires a transport request. Specify the transport request that contains all add-on-specific IACs. <UL><LI>If you perform the delta upgrade using transaction SAINT: SAPKISB16G</LI></UL> <UL><LI>If you perform the exchange upgrade with R3up: SAPKISB15R</LI></UL> <p></p> <OL>2. Conflicts with Support Packages</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you included more Support Packages than required by BANK/CFM 463_20, you may have to import CRTs after the installation. For R/3 Support Packages this is absolutely necessary, however, with Basis, ABA and HR Support Packages they are generally not required. Before starting, make sure you are informed about the released Support Package versions for BANK/CFM 463_20. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;With R/3 4. 6C SR2, you can directly include the BANK/CFM Support Packages/CRTs in the upgrade. <OL>3. Additional language imports</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The languages German and English have been automatically imported for SAP BANKING and SAP CFM with the upgrade. The following languages are also available for SAP BANKING and SAP CFM:<br /><br />SAP BANKING (not for Bank Customer Accounts (BCAs)): French, Korean<br />SAP CFM : French, Italian, Korean, Dutch<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Spanish, Portuguese, Japanese, Dutch, Polish<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and Hungarian <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use the language files that are stored in the directory LANGUAGE on the upgrade CD. To do this, call transaction SMLT and enter the directory &lt;CD-Mountpoint&gt;/LANGUAGE for the import. You can ignore the activation error concerning the index FLPQ2GRP-SEQ that occurs during the import. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;There are further language packages available for BANK/CFM 463_20.<br />CD ******** contains current and enhanced language packages for the following languages:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;French, Italian, Korean, Dutch<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Spanish, Portuguese, Hungarian, Dutch, Polish<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Japanese<br /><br />You can use transaction SMLT to import these languages as usual.<br />However, keep in mind that the status of these language transports may conflict with the status of the Support Packages. These conflicts are described in Note 195442. There is no easy solution for these conflicts.<br /> <b>&#x00A0;&#x00A0;VIII/ Other Notes which have to be Considered<br />Note 398888: BP_TR1: Composite note and FAQs about bus partner conversion</b><br /> <p><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Other Components", "Value": "TR (Treasury)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033006)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D024152)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000423419/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423419/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423419/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423419/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423419/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423419/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423419/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423419/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000423419/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "403451", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "Sample Customizing missing for paral.position management", "RefUrl": "/notes/403451"}, {"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888"}, {"RefNumber": "384558", "RefComponent": "FS-CML", "RefTitle": "Missing data elements TFM_PPAYMENT, TFM_AMMRHYZV", "RefUrl": "/notes/384558"}, {"RefNumber": "366273", "RefComponent": "FS-CML-PO", "RefTitle": "RZH: Syntax error in function group FVD_BO_OL", "RefUrl": "/notes/366273"}, {"RefNumber": "352072", "RefComponent": "IS-B", "RefTitle": "Maintenance strategy for SAP BANKING and SAP CFM", "RefUrl": "/notes/352072"}, {"RefNumber": "339736", "RefComponent": "IS-B", "RefTitle": "Overview: Notes on add-on SAP BANKING and SAP CFM", "RefUrl": "/notes/339736"}, {"RefNumber": "329949", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/329949"}, {"RefNumber": "214503", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/214503"}, {"RefNumber": "214502", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/214502"}, {"RefNumber": "181255", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181255"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "352072", "RefComponent": "IS-B", "RefTitle": "Maintenance strategy for SAP BANKING and SAP CFM", "RefUrl": "/notes/352072 "}, {"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "339736", "RefComponent": "IS-B", "RefTitle": "Overview: Notes on add-on SAP BANKING and SAP CFM", "RefUrl": "/notes/339736 "}, {"RefNumber": "403451", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "Sample Customizing missing for paral.position management", "RefUrl": "/notes/403451 "}, {"RefNumber": "366273", "RefComponent": "FS-CML-PO", "RefTitle": "RZH: Syntax error in function group FVD_BO_OL", "RefUrl": "/notes/366273 "}, {"RefNumber": "384558", "RefComponent": "FS-CML", "RefTitle": "Missing data elements TFM_PPAYMENT, TFM_AMMRHYZV", "RefUrl": "/notes/384558 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "BANK/CFM", "From": "462_10", "To": "462_10", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}