{"Request": {"Number": "1753745", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 446, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010425312017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001753745?language=E&token=2D7A9E7A87DA629E8F2193CB0EB43930"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001753745", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001753745/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1753745"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.04.2014"}, "SAPComponentKey": {"_label": "Component", "value": "KM-KW"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Knowledge Warehouse"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Knowledge Management", "value": "KM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'KM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Knowledge Warehouse", "value": "KM-KW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'KM-KW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1753745 - Virus scan profile: Enhancement for SAP KW"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You can use SAP GUI / Knowledge Workbench to upload files as info objects to the SAP Knowledge Warehouse system. However, the creation is terminated for objects with display format (created for Word/PowerPoint/Visio).<br/><br/>You need a new virus scan profile using which you can upload files to the SAP KW system again.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Virus scan profile, document, upload, create, interface, file, file format, virus scan interface (VSI)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This is a function enhancement; the interface has been changed. SAP Notes <strong>1585787</strong> and <strong>1744879</strong> are required to implement this SAP Note that focuses on SAP Knowledge Warehouse functions.<br/><br/>You have activated the virus scan profiles /SCMS/KPRO_CREATE, /SCMS/KPRO_XML_CREATE, and /SCET/GUI_UPLOAD in transaction VSCANPROFILE.<br/><br/>You select file formats like DOC (Word), PPT (PowerPoint), and PDF for the upload to the SAP Knowledge Warehouse system. Although these files do not contain a virus, they do not match the settings for the virus scan profiles already active in the SAP system. You cannot create the info objects.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the correction using the Note Assistant, or import the relevant Support Package.</p>\r\n<p>When you use the Knowledge Workbench (KWB) for creating or editing info objects, refer to SAP Note 1880269 regarding the required KW add-on patch level. The following information only refers to the steps you have to carry out in the SAP system (back end).<br/><br/><strong>Links to the documentation of the virus scan interface (VSI):</strong><br/>[1] - Virus Scan Interface http://help.sap.com/saphelp_nw73ehp1/helpdata/en/e3/aa783f181b0866e10000000a114084/frameset.htm<br/>[2] - Defining Virus Scan Profiles http://help.sap.com/saphelp_nw73ehp1/helpdata/en/4e/0ba5960499001be10000000a42189c/frameset.htm<br/>[3] - Delivered Parameters http://help.sap.com/saphelp_nw73ehp1/helpdata/en/4d/f581e4472d41c4e10000000a42189c/frameset.htm</p>\r\n<p><strong><strong>Creating the virus scan profile /SIWB/KW_UPLOAD_CREATE</strong></strong></p>\r\n<p>In transaction VSCANPROFILE, create the entry /SIWB/KW_UPLOAD_CREATE, as in the previously listed&#x00A0;links to the documentation of the virus scan interfaces.</p>\r\n<p><strong>Activation of the virus scan profile /SIWB/KW_UPLOAD_CREATE</strong></p>\r\n<ol>1. In the SAP Implementation Guide (transaction SPRO: &quot;SAP Reference IMG&quot;), choose &quot;SAP NetWeaver -&gt; Application Server -&gt; System Administration -&gt; Virus Scan Interface -&gt; Define Virus Scan Profiles&quot; (transaction VSCANPROFILE) and switch to change mode if required. The following view is then displayed: &quot;Change View &#39;Virus Scan Profile&#39;: Overview&quot;.</ol><ol>2. Double-click the virus scan profile /SIWB/KW_UPLOAD_CREATE here.</ol><ol>3. In the &quot;Virus Scan Profile&quot; section, select the checkboxes &quot;Active&quot; and &quot;Evaluate Profile Configuration Param.&quot;.</ol><ol>4. Double-click the &quot;Profile Configuration Parameters&quot; node to create the profile configuration parameters.</ol><ol>5. Choose &quot;New Entries&quot;. Enter the following data (or select this data using the F4 help) to define the profile configuration parameters:</ol>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"padding-left: 30px;\">\r\n<tbody style=\"padding-left: 30px;\">\r\n<tr style=\"padding-left: 30px;\">\r\n<td>Parameter:</td>\r\n<td>Value:</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>CUST_CHECK_MIME_TYPE</td>\r\n<td>1 (Activate MIME type check)</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>CUST_MIME_TYPES_ARE_BLACKLIST</td>\r\n<td>1 (Specified MIME types are locked; other file formats are allowed)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ol>It only makes sense to make settings for the MIME type check if the used virus scanner also supports the MIME type check.</ol><ol>For more information about the configuration parameters, see SAP Note 1640285.</ol>\r\n<p style=\"padding-left: 30px;\">6. Save your entries.</p>\r\n<p style=\"padding-left: 30px;\">7. Double-click the &quot;MIME Types&quot; node and enter the following MIME types:</p>\r\n<p style=\"padding-left: 60px;\">application/ecmascript</p>\r\n<p style=\"padding-left: 60px;\">application/java-archive</p>\r\n<p style=\"padding-left: 60px;\">application/javascript</p>\r\n<p style=\"padding-left: 60px;\">application/x-alf</p>\r\n<p style=\"padding-left: 60px;\">application/x-alf-descr</p>\r\n<p style=\"padding-left: 60px;\">application/x-jar</p>\r\n<p style=\"padding-left: 60px;\">application/x-java-jar</p>\r\n<p style=\"padding-left: 60px;\">application/x-javascript</p>\r\n<p style=\"padding-left: 60px;\">application/x-shockwave-flash</p>\r\n<p style=\"padding-left: 60px;\">application/x-silverlight</p>\r\n<p style=\"padding-left: 60px;\">application/x-silverlight-2</p>\r\n<p style=\"padding-left: 60px;\">application/x-silverlight-app</p>\r\n<p style=\"padding-left: 60px;\">text/javascript</p>\r\n<p style=\"padding-left: 60px;\">text/thtml</p>\r\n<p style=\"padding-left: 60px;\">a) This list should only be considered a proposal. If you want to block other MIME types, insert them into the list. If you require file formats from the list mentioned above, remove these MIME types.</p>\r\n<p style=\"padding-left: 60px;\">b) Note the following when you add new MIME types in the SAP KW environment:</p>\r\n<p style=\"padding-left: 60px;\">text/html: If this MIME type is not allowed, you cannot transfer objects of classes with the display formats &quot;Topic&quot;, &quot;Slide&quot; and &quot;Visio&quot; to the KW. The same applies to HTML objects (KWN_HTML) from the &quot;HTML-Based Documents&quot; area (KWNET).</p>\r\n<p style=\"padding-left: 60px;\">application/xhtml+xml: Blocks objects in the XML areas, for example, the class BCO_COMMON.</p>\r\n<p style=\"padding-left: 60px;\">c) The used virus scanner has to support the MIME type check.</p>\r\n<p style=\"padding-left: 60px;\">If this is not the case, files that have MIME types from the blacklist cannot be rejected regardless of the settings you have made in the SAP system.</p>\r\n<p style=\"padding-left: 30px;\">8. Save your entries.</p>\r\n<p><strong>Assignment of PHIO classes to virus scan profiles</strong></p>\r\n<p>If the behavior of all object classes of the SAP Knowledge Warehouse should be the same, you do not have to carry out the steps described in this chapter.<br/><br/>Entries in the table KWD_CL_VSP are only needed if you require a specific behavior of selected object classes (PHIO classes). Note that there may be customer-specific PHIO classes when you create the entries.<br/><br/>Examples for PHIO classes:</p>\r\n<ul>\r\n<li>Slides from the training area (IWBTRAIN)</li>\r\n</ul>\r\n<p>Existing PHIO classes in the standard data model:</p>\r\n<ul>\r\n<ul>\r\n<li>IWB2PPTDST&#x00A0;&#x00A0;(display format)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>IWB2PPTSRC&#x00A0;&#x00A0;(sourc format/PowerPoint)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Topic from the documentation area (IWBHELP)</li>\r\n</ul>\r\n<p>Existing PHIO classes in the standard data model:</p>\r\n<ul>\r\n<ul>\r\n<li>IWB_DSTTXT&#x00A0;&#x00A0;(display format)</li>\r\n<li>IWB_SRCTXT&#x00A0;&#x00A0;(source format/Word)</li>\r\n</ul>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D062598)"}, {"Key": "Processor                                                                                           ", "Value": "C5007534"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001753745/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753745/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753745/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753745/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753745/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753745/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753745/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753745/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001753745/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1880269", "RefComponent": "KM-KW", "RefTitle": "KWB: Termination due to active virus scan profiles", "RefUrl": "/notes/1880269"}, {"RefNumber": "1810660", "RefComponent": "BC-FES-GUI", "RefTitle": "GUI_UPLOAD: Parameter ISSCANPERFORMED, Excpetions synchron.", "RefUrl": "/notes/1810660"}, {"RefNumber": "1800442", "RefComponent": "SV-SMG-IMP", "RefTitle": "Replacement for Virus Scan Profile /SCMS/KPRO_CREATE", "RefUrl": "/notes/1800442"}, {"RefNumber": "1799216", "RefComponent": "BC-DOC-TER", "RefTitle": "Virus scan profile in SDOK module - terminology tools", "RefUrl": "/notes/1799216"}, {"RefNumber": "1779699", "RefComponent": "BC-FES-GUI", "RefTitle": "GUI_UPLOAD: Change for DP call", "RefUrl": "/notes/1779699"}, {"RefNumber": "1744879", "RefComponent": "BC-FES-CTL", "RefTitle": "Unauthorized modification of stored content in Data Provider", "RefUrl": "/notes/1744879"}, {"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285"}, {"RefNumber": "1585787", "RefComponent": "BC-SRV-KPR-DMF", "RefTitle": "Virus Scan profile enhancement for KPro", "RefUrl": "/notes/1585787"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1800442", "RefComponent": "SV-SMG-IMP", "RefTitle": "Replacement for Virus Scan Profile /SCMS/KPRO_CREATE", "RefUrl": "/notes/1800442 "}, {"RefNumber": "1880269", "RefComponent": "KM-KW", "RefTitle": "KWB: Termination due to active virus scan profiles", "RefUrl": "/notes/1880269 "}, {"RefNumber": "1585787", "RefComponent": "BC-SRV-KPR-DMF", "RefTitle": "Virus Scan profile enhancement for KPro", "RefUrl": "/notes/1585787 "}, {"RefNumber": "1810660", "RefComponent": "BC-FES-GUI", "RefTitle": "GUI_UPLOAD: Parameter ISSCANPERFORMED, Excpetions synchron.", "RefUrl": "/notes/1810660 "}, {"RefNumber": "1799216", "RefComponent": "BC-DOC-TER", "RefTitle": "Virus scan profile in SDOK module - terminology tools", "RefUrl": "/notes/1799216 "}, {"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64031", "URL": "/supportpackage/SAPKB64031"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64032", "URL": "/supportpackage/SAPKB64032"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70028", "URL": "/supportpackage/SAPKB70028"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70113", "URL": "/supportpackage/SAPKB70113"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70213", "URL": "/supportpackage/SAPKB70213"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71016", "URL": "/supportpackage/SAPKB71016"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71111", "URL": "/supportpackage/SAPKB71111"}, {"SoftwareComponentVersion": "SAP_BASIS 720", "SupportPackage": "SAPKB72008", "URL": "/supportpackage/SAPKB72008"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73009", "URL": "/supportpackage/SAPKB73009"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73106", "URL": "/supportpackage/SAPKB73106"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 9, "URL": "/corrins/0001753745/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 640&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB64029 - SAPKB64031&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70026 - SAPKB70027&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 710&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB71014 - SAPKB71015&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 711&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB71108 - SAPKB71110&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70111 - SAPKB70112&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70209 - SAPKB70212&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73006 - SAPKB73008&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB72006 - SAPKB72007&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73101 - SAPKB73105&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Create the following objects in the package SIWB:</P> <OL>1. Domain IW_CL_VSP</OL> <OL><OL>a) Short Description: Upload / Download Files</OL></OL> <OL><OL>b) \"Definition\" tab page:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Data Type&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; CHAR (character string) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No. Characters: 1 <OL><OL>a) \"Value Range\" tab page:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;U Upload <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;D Download <OL>1. Data element IW_CL_VSP</OL> <OL><OL>a) Short Description: Upload / Download Files</OL></OL> <OL><OL>b) \"Data Type\" tab page:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Elementary Type: Domain: IW_CL_VSP <OL><OL>a) \"Field Label\" tab page:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Length&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Field Label<br/>Short&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;10&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Up/Down<br/>Medium&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Upload/Download<br/>Long&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;20&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Upload / Download<br/>Heading&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;45&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Upload / Download Files <OL>1. Transparent table KWD_CL_VSP</OL> <OL><OL>a) Short Description: Virus Scan Profile for PHIO Class</OL></OL> <OL><OL>b) \"Delivery and Maintenance\" tab page:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Delivery Class: G <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A reservation of the customer namespace is not required. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Data Browser/Table View Maint.: Display/Maintenance allowed <OL><OL>a) \"Fields\" tab page:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Field&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Key&nbsp;&nbsp;Data element<br/>PH_CLASS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Yes&nbsp;&nbsp;SDOK_CLASS<br/>UP_DOWN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Yes&nbsp;&nbsp;IW_CL_VSP<br/>VSCAN_PROFILE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;VSCAN_PROFILE <OL><OL>a) \"Entry help/check\" tab page:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set a foreign key to the field PH_CLASS of the field PH_CLASS of the  table SDOKPHCL. Dynpro check: Option \"Check desired\" is selected. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set a foreign key to the field VSCAN_PROFILE of the fields MANDT and  PROFILE of the table VSCAN_PROF. Dynpro check: Option \"Check desired\" is selected. <OL>1. Text element 42 from the report RSIWB_XML_IMPORT with the maximum length of 75 characters:<br/>Storage of content (PHIO &amp;1 &amp;2) failed with sy-subrc:  &amp;3</OL> <P><br/>Activate your changes.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 9, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 6, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "640", "Number": "1744879 ", "URL": "/notes/1744879 ", "Title": "Unauthorized modification of stored content in Data Provider", "Component": "BC-FES-CTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1543725 ", "URL": "/notes/1543725 ", "Title": "Class-based info object list", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1585787 ", "URL": "/notes/1585787 ", "Title": "Virus Scan profile enhancement for KPro", "Component": "BC-SRV-KPR-DMF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1682423 ", "URL": "/notes/1682423 ", "Title": "Removing incorrect objects", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1543725 ", "URL": "/notes/1543725 ", "Title": "Class-based info object list", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1585787 ", "URL": "/notes/1585787 ", "Title": "Virus Scan profile enhancement for KPro", "Component": "BC-SRV-KPR-DMF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1682423 ", "URL": "/notes/1682423 ", "Title": "Removing incorrect objects", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "731", "Number": "1744879 ", "URL": "/notes/1744879 ", "Title": "Unauthorized modification of stored content in Data Provider", "Component": "BC-FES-CTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1585787 ", "URL": "/notes/1585787 ", "Title": "Virus Scan profile enhancement for KPro", "Component": "BC-SRV-KPR-DMF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1682423 ", "URL": "/notes/1682423 ", "Title": "Removing incorrect objects", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1543725 ", "URL": "/notes/1543725 ", "Title": "Class-based info object list", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1585787 ", "URL": "/notes/1585787 ", "Title": "Virus Scan profile enhancement for KPro", "Component": "BC-SRV-KPR-DMF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1682423 ", "URL": "/notes/1682423 ", "Title": "Removing incorrect objects", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1585787 ", "URL": "/notes/1585787 ", "Title": "Virus Scan profile enhancement for KPro", "Component": "BC-SRV-KPR-DMF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1682423 ", "URL": "/notes/1682423 ", "Title": "Removing incorrect objects", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "711", "ValidTo": "711", "Number": "1543725 ", "URL": "/notes/1543725 ", "Title": "Class-based info object list", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "711", "ValidTo": "711", "Number": "1585787 ", "URL": "/notes/1585787 ", "Title": "Virus Scan profile enhancement for KPro", "Component": "BC-SRV-KPR-DMF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "711", "ValidTo": "711", "Number": "1682423 ", "URL": "/notes/1682423 ", "Title": "Removing incorrect objects", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "720", "ValidTo": "720", "Number": "1543725 ", "URL": "/notes/1543725 ", "Title": "Class-based info object list", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "720", "ValidTo": "720", "Number": "1585787 ", "URL": "/notes/1585787 ", "Title": "Virus Scan profile enhancement for KPro", "Component": "BC-SRV-KPR-DMF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "720", "ValidTo": "720", "Number": "1682423 ", "URL": "/notes/1682423 ", "Title": "Removing incorrect objects", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1543725 ", "URL": "/notes/1543725 ", "Title": "Class-based info object list", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1585787 ", "URL": "/notes/1585787 ", "Title": "Virus Scan profile enhancement for KPro", "Component": "BC-SRV-KPR-DMF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1682423 ", "URL": "/notes/1682423 ", "Title": "Removing incorrect objects", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1585787 ", "URL": "/notes/1585787 ", "Title": "Virus Scan profile enhancement for KPro", "Component": "BC-SRV-KPR-DMF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1682423 ", "URL": "/notes/1682423 ", "Title": "Removing incorrect objects", "Component": "KM-KW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1695317 ", "URL": "/notes/1695317 ", "Title": "KW: Control-based structure editor", "Component": "KM-KW"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1585787", "RefTitle": "Virus Scan profile enhancement for KPro", "RefUrl": "/notes/0001585787"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}