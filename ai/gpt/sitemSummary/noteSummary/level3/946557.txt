SAP Note 946557 addresses a specific issue where the scheduler for data loading processes in SAP BW terminates with an error message. Here is a summary of the information found in this note:

**Symptom:**
When attempting to load data into data targets that no longer exist, the scheduler process terminates with error message R7 118, indicating that the data target "XYZ" does not exist in version A.

**Other Terms:**
- R7 118: The message number associated with this error.
- Scheduler: The process in SAP BW used for scheduling data loading tasks.
- InfoPackage: The set of parameters used to define data extraction and loading processes.
- Data target: The object in SAP BW, such as an InfoCube or ODS (Operational Data Store), where data is loaded to.
- A message: An error type that typically indicates a process has terminated and requires user action to remedy.

**Reason and Prerequisites:**
The issue is caused due to a program error.

**Solution:**
To resolve the problem, SAP recommends importing Support Package 09 for BW 7.0 (BW7.0 Patch 09 or SAPKW70009) into the affected BW system. This support package is detailed in Note 0914303, titled "SAPBWNews BW 7.0 Support Package 09". The note may be released in a preliminary version prior to the final release of the support package.

By applying the suggested support package, the scheduler will no longer terminate due to non-existent data targets, as the correction included in the package eliminates this issue.