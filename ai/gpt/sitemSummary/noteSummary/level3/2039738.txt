SAP Note 2039738 addresses the need for the deletion of personal data within CRM software components to comply with data privacy requirements. The note provides guidance on using the SAP Information Lifecycle Management (ILM) functions for simplified data deletion in CRM systems.

Key aspects of the note include:

1. Applications built with CRM components may contain personal data that needs to be deleted according to privacy laws, as also detailed in SAP Note 1825544.
  
2. The functionality for simplified data deletion is available from CRM 7.0 EhP3, support package SP05 onwards.

3. Personal data must be destroyed when there is no legal or business need to retain it anymore. For deletion purposes, data is segmented using archiving objects or destruction objects; these objects group related business data with the same retention period for collective deletion.

4. An interim storage phase in an archive is possible when the data is not needed, but the retention period is still ongoing. The actual deletion occurs directly from the archive after the expiration of the retention period.

5. Preparation steps for implementing simplified data deletion:
   - Refer to the "References" section of the note and implement relevant referenced notes applicable to your scenarios.
   - Assign required ILM objects to a custom audit area using transaction ILMARA.
   - Define retention periods for each ILM object using transaction IRMPOL, which can also be adjusted later.
   - Execute a conversion run for already archived data if retention periods change.

In summary, the note provides procedures for setting up and managing the deletion of personal data in CRM systems using SAP ILM, ensuring compliance with data protection regulations.