{"Request": {"Number": "551944", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1313, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015282992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000551944?language=E&token=B6F1F2C0EC0A88F98CB008BB392B000B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000551944", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000551944/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "551944"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.06.2003"}, "SAPComponentKey": {"_label": "Component", "value": "PSM-FG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Federal Government Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Public Sector Management", "value": "PSM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Federal Government Functions", "value": "PSM-FG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PSM-FG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "551944 - Corrections to advance delivery of report SF-224"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Corrections to advance delivery of report SF-224</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SF224</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You are using the SAP US-Federal Government functionality and applied note 547156.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Transport PI7K009244 containes corrections to the advance delivery and is attached to the note.<br />See attached note 13719 how to apply the transport.<br />Please consider note 550375. This note containes corrections to the line item report used by SF-224</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PSM-FM-IS (Information System)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I804464)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I803939)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551944/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551944/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551944/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551944/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551944/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551944/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551944/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551944/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551944/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "9244.zip", "FileSize": "40", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001706982002&iv_version=0004&iv_guid=90009D316B6414428A467280A3E61FE9"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "944503", "RefComponent": "PSM-FG", "RefTitle": "Addition of TAS Suffix in SF-224: Improper line item display", "RefUrl": "/notes/944503"}, {"RefNumber": "937887", "RefComponent": "PSM-FG", "RefTitle": "Partial SF-224 Statement of Transactions", "RefUrl": "/notes/937887"}, {"RefNumber": "916086", "RefComponent": "PSM-FG", "RefTitle": "Addition of Treasury Account Symbol Suffix in SF-224", "RefUrl": "/notes/916086"}, {"RefNumber": "891766", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/891766"}, {"RefNumber": "890376", "RefComponent": "PSM-FG", "RefTitle": "Display of additional prior-month records in SF-224", "RefUrl": "/notes/890376"}, {"RefNumber": "648482", "RefComponent": "PSM-FG-IS", "RefTitle": "Export of SF-224 Details data to local file", "RefUrl": "/notes/648482"}, {"RefNumber": "640395", "RefComponent": "PSM-FG-IS", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/640395"}, {"RefNumber": "618598", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/618598"}, {"RefNumber": "617513", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/617513"}, {"RefNumber": "617084", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/617084"}, {"RefNumber": "616039", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/616039"}, {"RefNumber": "608175", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/608175"}, {"RefNumber": "560807", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery of report SF-224", "RefUrl": "/notes/560807"}, {"RefNumber": "550375", "RefComponent": "PSM-FM-IS", "RefTitle": "Extend selection screen to specify line item record", "RefUrl": "/notes/550375"}, {"RefNumber": "547156", "RefComponent": "PSM-FG", "RefTitle": "Advance delivery of Report SF-224 and Enhancement of BL", "RefUrl": "/notes/547156"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1016700", "RefComponent": "PSM-FG", "RefTitle": "First prior-month amount after 10 months missing in SF-224", "RefUrl": "/notes/1016700"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "916086", "RefComponent": "PSM-FG", "RefTitle": "Addition of Treasury Account Symbol Suffix in SF-224", "RefUrl": "/notes/916086 "}, {"RefNumber": "1016700", "RefComponent": "PSM-FG", "RefTitle": "First prior-month amount after 10 months missing in SF-224", "RefUrl": "/notes/1016700 "}, {"RefNumber": "937887", "RefComponent": "PSM-FG", "RefTitle": "Partial SF-224 Statement of Transactions", "RefUrl": "/notes/937887 "}, {"RefNumber": "944503", "RefComponent": "PSM-FG", "RefTitle": "Addition of TAS Suffix in SF-224: Improper line item display", "RefUrl": "/notes/944503 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "890376", "RefComponent": "PSM-FG", "RefTitle": "Display of additional prior-month records in SF-224", "RefUrl": "/notes/890376 "}, {"RefNumber": "648482", "RefComponent": "PSM-FG-IS", "RefTitle": "Export of SF-224 Details data to local file", "RefUrl": "/notes/648482 "}, {"RefNumber": "640395", "RefComponent": "PSM-FG-IS", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/640395 "}, {"RefNumber": "547156", "RefComponent": "PSM-FG", "RefTitle": "Advance delivery of Report SF-224 and Enhancement of BL", "RefUrl": "/notes/547156 "}, {"RefNumber": "618598", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/618598 "}, {"RefNumber": "617084", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/617084 "}, {"RefNumber": "617513", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/617513 "}, {"RefNumber": "616039", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/616039 "}, {"RefNumber": "608175", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery report SF-224", "RefUrl": "/notes/608175 "}, {"RefNumber": "560807", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery of report SF-224", "RefUrl": "/notes/560807 "}, {"RefNumber": "550375", "RefComponent": "PSM-FM-IS", "RefTitle": "Extend selection screen to specify line item record", "RefUrl": "/notes/550375 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-PS", "From": "462", "To": "462", "Subsequent": ""}, {"SoftwareComponent": "ISPSADIN", "From": "10A", "To": "10A", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}