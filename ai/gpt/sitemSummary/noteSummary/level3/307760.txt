SAP Note 307760 addresses an issue where field exits do not function as expected in the new "Enjoy" purchase order transaction (ME21N) and the Enjoy Purchase Requisition transaction (ME51N) starting from Release 4.6c. Field exits may not work at all or may not work as well as they did in the older versions of these transactions (e.g., ME21).

The reason for this is due to the architectural changes in the new transactions, where the entry and processing of data are strictly separated, and the system no longer requires a logical sequence of data entry (such as entering header data first, then item data, and finally account assignment data). This new approach aims to offer users more flexibility and does not constrain them to navigate through a tight, predefined screen sequence. As a result, this affects the availability of the correct data required for field exits to perform meaningful verification. For instance, entering the G/L account on the account assignment screen does not guarantee that the purchase order item in structure EKPO is also displayed, whereas in the old transactions, it was guaranteed.

As a solution, SAP recommends not using field exits for verifications. Instead, users should utilize enhancement MM06E005 for the Purchase order or MEREQ001 for Purchase Requisitions, as these enhancements provide extensive user exits. Furthermore, changing default fields via field exits is no longer supported in ME21N, ME22N, ME51N, and ME52N transactions.