{"Request": {"Number": "523191", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 668, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015221742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000523191?language=E&token=B95EE19AC1D7FFD99B1AA71DB9D0D0B4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000523191", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000523191/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "523191"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.07.2003"}, "SAPComponentKey": {"_label": "Component", "value": "BW"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Business Warehouse"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "523191 - SAPBWNews BW 2.0B Support Package 32"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Support Package 32 for BW Release 2.0B</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAPBWNEWS, Support Package 32 for 2.0B, BW Support Packages</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note contains the SAPBWNews for Support Package 32 of BW Release 2.0B. It provides a list of all notes describing corrections or enhancements in Support Package xx. This note is updated as soon as further notes are added.<br /><br />The following information is subdivided into 4 areas:</p> <UL><UL><LI>Note the following when importing the Support Package</LI></UL></UL> <UL><UL><LI>Errors which can occur after you import the Support Package</LI></UL></UL> <UL><UL><LI>This Support Package corrects the following errors in particular</LI></UL></UL> <UL><UL><LI>Improvements delivered with this Support Package</LI></UL></UL> <p><br />Note 531700 contains other important and necessary information on importing Support Packages for BW 2.0B, which does not refer to a special Support Package (for example, note 531700 contains the required patch level for the Basis system or required default values for importing the Support Packages and information on problems that may only become evident in BW although they are caused by other systems.<br />You will also find the planned Support Package release dates in note 531700. The note is continually updated to the current information status (last update was May 13, 03).<br />We therefore urgently recommend that you refer to this note before importing the Support Package.To simply subscribe to this note, read the instructions in note 487366.<br /><br />This Support Package 32 corresponds to the technological status of BW 2.1C Support Package 24 (SAPBWNews 523204).<br /></p> <b>Note the following when you import the Support Package:</b><br /> <p><br />You will find a summary of the content-specific topics in composite note 630333.<br /><br />Currency translation:0FISCPER = 2003000. Refer to note 612747 for additional information.<br /><br />Currency translation type texts missing after content activation.See note 629762.<br /></p> <b>Errors that can occur after you import the Support Package (you will find the corresponding advance corrections in the code part of the notes mentioned below):</b><br /> <p><br />The implementation of note 610169 can cause error situations during the transfer rule generation. See note 632442.<br /></p> <b>This Support Package corrects the following errors in particular:</b><br /> <UL><LI>Corrections in the OLAP technology area:</LI></UL> <UL><UL><LI>Limited key figure with #, MultiCube.You will find more detailed information in note 613854.</LI></UL></UL> <UL><LI>Corrections in the Warehouse Management area:</LI></UL> <UL><UL><LI>Syntax error in RSTMPL9A when you load data from a file.See note 620152.</LI></UL></UL> <p></p> <b>Improvements delivered with this Support Package:</b><br /> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D035910)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000523191/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000523191/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000523191/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000523191/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000523191/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000523191/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000523191/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000523191/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000523191/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "627223", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Status determination without assistant", "RefUrl": "/notes/627223"}, {"RefNumber": "625404", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Monitor: Long search in EDIDC and EDI40", "RefUrl": "/notes/625404"}, {"RefNumber": "623667", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:P32:deleting all data in the data target does not work", "RefUrl": "/notes/623667"}, {"RefNumber": "622972", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Master data where-used list with line item dimensions", "RefUrl": "/notes/622972"}, {"RefNumber": "622591", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSMDEXITON: Short dump GETWA_NOT_ASSIGNED", "RefUrl": "/notes/622591"}, {"RefNumber": "622518", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/622518"}, {"RefNumber": "622492", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: DB2 UDB EEE statistics for temporary BW tables", "RefUrl": "/notes/622492"}, {"RefNumber": "622488", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "PFCG: Error when calling F4 help", "RefUrl": "/notes/622488"}, {"RefNumber": "622344", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in program SAPLRSDRS", "RefUrl": "/notes/622344"}, {"RefNumber": "622153", "RefComponent": "BW-WHM-AWB", "RefTitle": "P32:P13:AWB Monitoring: Icons are white in InfoPackage view", "RefUrl": "/notes/622153"}, {"RefNumber": "622138", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Long runtimes when you activate InfoObjects", "RefUrl": "/notes/622138"}, {"RefNumber": "621621", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Loading master data: Queue time for program generation", "RefUrl": "/notes/621621"}, {"RefNumber": "621382", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Reading master data for key fig. attr.: Error _REQ_ATTR-01-", "RefUrl": "/notes/621382"}, {"RefNumber": "621127", "RefComponent": "BW-WHM", "RefTitle": "ORA-14085 with transfer rule activation in SM21(SYS LOG)", "RefUrl": "/notes/621127"}, {"RefNumber": "621024", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Incorrect data after delta from reconstruction in change run", "RefUrl": "/notes/621024"}, {"RefNumber": "620455", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Command sequence is not processed correctly", "RefUrl": "/notes/620455"}, {"RefNumber": "620152", "RefComponent": "BW-WHM-DST", "RefTitle": "Syntax error in RSTMPL9A during data loading from a file", "RefUrl": "/notes/620152"}, {"RefNumber": "619888", "RefComponent": "BW", "RefTitle": "Alpha conversion: \"Conversion for usage type IOBJ ...\"", "RefUrl": "/notes/619888"}, {"RefNumber": "619843", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Syntax error different parameter number in FORM and PERFORM", "RefUrl": "/notes/619843"}, {"RefNumber": "619051", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:P32:SDL: post-processing may be carried out twice", "RefUrl": "/notes/619051"}, {"RefNumber": "618876", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "Transporting settings/packages: no deletion", "RefUrl": "/notes/618876"}, {"RefNumber": "618848", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination SAPSQL_ARRAY_INSERT_DUPREC with alpha convers.", "RefUrl": "/notes/618848"}, {"RefNumber": "618448", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:P32:SDL: hierarchy Ipak save without error messages", "RefUrl": "/notes/618448"}, {"RefNumber": "618312", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "RSAR 347 during transport and InfoObject activation", "RefUrl": "/notes/618312"}, {"RefNumber": "618217", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:P32:SDL:Saving/requesting data from/with InfoPack.", "RefUrl": "/notes/618217"}, {"RefNumber": "618065", "RefComponent": "BW-WHM-DST", "RefTitle": "P32:P13:Massive parallel loading & simultaneous compression", "RefUrl": "/notes/618065"}, {"RefNumber": "617709", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:P32:SDL:output length in conv exit sel fields too short", "RefUrl": "/notes/617709"}, {"RefNumber": "617294", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Realignment run does not run in parallel", "RefUrl": "/notes/617294"}, {"RefNumber": "617148", "RefComponent": "BW-WHM-DST", "RefTitle": "P32:P13:Attrib-Hier-RealRun: Start time is empty", "RefUrl": "/notes/617148"}, {"RefNumber": "616453", "RefComponent": "BW-WHM", "RefTitle": "BW2.0B/2.1C (SP 32/24): ALPHA converter - SQL 615", "RefUrl": "/notes/616453"}, {"RefNumber": "616361", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Problems in changing key figures on fixed currency/unit", "RefUrl": "/notes/616361"}, {"RefNumber": "616147", "RefComponent": "BW-WHM-DST", "RefTitle": "P32:P13:Mon:Qualok sett. in RSMDATASTATE after request load", "RefUrl": "/notes/616147"}, {"RefNumber": "616030", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Error in deleting the source system assignment", "RefUrl": "/notes/616030"}, {"RefNumber": "615343", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Setup logs in 'old' log", "RefUrl": "/notes/615343"}, {"RefNumber": "615083", "RefComponent": "BW-WHM-MTD", "RefTitle": "Objects w/ composite roles missing in transport connection", "RefUrl": "/notes/615083"}, {"RefNumber": "614925", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Missing leaves in hierarchy with interval", "RefUrl": "/notes/614925"}, {"RefNumber": "614355", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Complex authorizations with intervals in the F4 help", "RefUrl": "/notes/614355"}, {"RefNumber": "613890", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Lock problems during rollup and the realignment run", "RefUrl": "/notes/613890"}, {"RefNumber": "613854", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Restricted key figure with #, <PERSON><PERSON><PERSON><PERSON>", "RefUrl": "/notes/613854"}, {"RefNumber": "613841", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Using collective search helps in BW", "RefUrl": "/notes/613841"}, {"RefNumber": "613440", "RefComponent": "BW-WHM-DST", "RefTitle": "Red requests w/ error during PSA loading; RSAR 130", "RefUrl": "/notes/613440"}, {"RefNumber": "613391", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Master data where-used list: Cubes with LineItem dimension", "RefUrl": "/notes/613391"}, {"RefNumber": "613119", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Dimension content also deleted when you delete InfoCube data", "RefUrl": "/notes/613119"}, {"RefNumber": "613069", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect rounding for key figures with NODIM operator", "RefUrl": "/notes/613069"}, {"RefNumber": "613024", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299(BRAIN), system error in SAPLRRK0, SINGLE PAGE-01-", "RefUrl": "/notes/613024"}, {"RefNumber": "612304", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in the time derivation for stock queries", "RefUrl": "/notes/612304"}, {"RefNumber": "611154", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "RSAR 245: Error code 7 in the long text", "RefUrl": "/notes/611154"}, {"RefNumber": "610169", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Text inconsist. between PSA display/SE 16/technical.Struc.", "RefUrl": "/notes/610169"}, {"RefNumber": "581597", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination with F4 help for nodes during  selection", "RefUrl": "/notes/581597"}, {"RefNumber": "531700", "RefComponent": "BW", "RefTitle": "BW 2.0B: Information for Support Packages", "RefUrl": "/notes/531700"}, {"RefNumber": "523204", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.1C Support Package 24", "RefUrl": "/notes/523204"}, {"RefNumber": "512317", "RefComponent": "BW-SYS-DB-INF", "RefTitle": "BW 2.0B / 2.1C : Korrekturen in Patches ab 4/2002 (Informix)", "RefUrl": "/notes/512317"}, {"RefNumber": "447341", "RefComponent": "BW", "RefTitle": "values for ALPHA conversion routine", "RefUrl": "/notes/447341"}, {"RefNumber": "181945", "RefComponent": "BW-SYS-DB-INF", "RefTitle": "Performance guide: BW on Informix", "RefUrl": "/notes/181945"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "512317", "RefComponent": "BW-SYS-DB-INF", "RefTitle": "BW 2.0B / 2.1C : Korrekturen in Patches ab 4/2002 (Informix)", "RefUrl": "/notes/512317 "}, {"RefNumber": "531700", "RefComponent": "BW", "RefTitle": "BW 2.0B: Information for Support Packages", "RefUrl": "/notes/531700 "}, {"RefNumber": "523204", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.1C Support Package 24", "RefUrl": "/notes/523204 "}, {"RefNumber": "447341", "RefComponent": "BW", "RefTitle": "values for ALPHA conversion routine", "RefUrl": "/notes/447341 "}, {"RefNumber": "622492", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: DB2 UDB EEE statistics for temporary BW tables", "RefUrl": "/notes/622492 "}, {"RefNumber": "622138", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Long runtimes when you activate InfoObjects", "RefUrl": "/notes/622138 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "181945", "RefComponent": "BW-SYS-DB-INF", "RefTitle": "Performance guide: BW on Informix", "RefUrl": "/notes/181945 "}, {"RefNumber": "625404", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Monitor: Long search in EDIDC and EDI40", "RefUrl": "/notes/625404 "}, {"RefNumber": "613440", "RefComponent": "BW-WHM-DST", "RefTitle": "Red requests w/ error during PSA loading; RSAR 130", "RefUrl": "/notes/613440 "}, {"RefNumber": "627223", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Status determination without assistant", "RefUrl": "/notes/627223 "}, {"RefNumber": "622972", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Master data where-used list with line item dimensions", "RefUrl": "/notes/622972 "}, {"RefNumber": "623667", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:P32:deleting all data in the data target does not work", "RefUrl": "/notes/623667 "}, {"RefNumber": "621621", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Loading master data: Queue time for program generation", "RefUrl": "/notes/621621 "}, {"RefNumber": "616361", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Problems in changing key figures on fixed currency/unit", "RefUrl": "/notes/616361 "}, {"RefNumber": "622153", "RefComponent": "BW-WHM-AWB", "RefTitle": "P32:P13:AWB Monitoring: Icons are white in InfoPackage view", "RefUrl": "/notes/622153 "}, {"RefNumber": "621382", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Reading master data for key fig. attr.: Error _REQ_ATTR-01-", "RefUrl": "/notes/621382 "}, {"RefNumber": "621127", "RefComponent": "BW-WHM", "RefTitle": "ORA-14085 with transfer rule activation in SM21(SYS LOG)", "RefUrl": "/notes/621127 "}, {"RefNumber": "621024", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Incorrect data after delta from reconstruction in change run", "RefUrl": "/notes/621024 "}, {"RefNumber": "619843", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Syntax error different parameter number in FORM and PERFORM", "RefUrl": "/notes/619843 "}, {"RefNumber": "619051", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:P32:SDL: post-processing may be carried out twice", "RefUrl": "/notes/619051 "}, {"RefNumber": "618448", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:P32:SDL: hierarchy Ipak save without error messages", "RefUrl": "/notes/618448 "}, {"RefNumber": "618312", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "RSAR 347 during transport and InfoObject activation", "RefUrl": "/notes/618312 "}, {"RefNumber": "618217", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:P32:SDL:Saving/requesting data from/with InfoPack.", "RefUrl": "/notes/618217 "}, {"RefNumber": "618065", "RefComponent": "BW-WHM-DST", "RefTitle": "P32:P13:Massive parallel loading & simultaneous compression", "RefUrl": "/notes/618065 "}, {"RefNumber": "617709", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:P32:SDL:output length in conv exit sel fields too short", "RefUrl": "/notes/617709 "}, {"RefNumber": "617294", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Realignment run does not run in parallel", "RefUrl": "/notes/617294 "}, {"RefNumber": "617148", "RefComponent": "BW-WHM-DST", "RefTitle": "P32:P13:Attrib-Hier-RealRun: Start time is empty", "RefUrl": "/notes/617148 "}, {"RefNumber": "616147", "RefComponent": "BW-WHM-DST", "RefTitle": "P32:P13:Mon:Qualok sett. in RSMDATASTATE after request load", "RefUrl": "/notes/616147 "}, {"RefNumber": "616030", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Error in deleting the source system assignment", "RefUrl": "/notes/616030 "}, {"RefNumber": "615343", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Setup logs in 'old' log", "RefUrl": "/notes/615343 "}, {"RefNumber": "615083", "RefComponent": "BW-WHM-MTD", "RefTitle": "Objects w/ composite roles missing in transport connection", "RefUrl": "/notes/615083 "}, {"RefNumber": "614925", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Missing leaves in hierarchy with interval", "RefUrl": "/notes/614925 "}, {"RefNumber": "613841", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Using collective search helps in BW", "RefUrl": "/notes/613841 "}, {"RefNumber": "613391", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Master data where-used list: Cubes with LineItem dimension", "RefUrl": "/notes/613391 "}, {"RefNumber": "611154", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "RSAR 245: Error code 7 in the long text", "RefUrl": "/notes/611154 "}, {"RefNumber": "610169", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Text inconsist. between PSA display/SE 16/technical.Struc.", "RefUrl": "/notes/610169 "}, {"RefNumber": "622344", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in program SAPLRSDRS", "RefUrl": "/notes/622344 "}, {"RefNumber": "613069", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect rounding for key figures with NODIM operator", "RefUrl": "/notes/613069 "}, {"RefNumber": "613119", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Dimension content also deleted when you delete InfoCube data", "RefUrl": "/notes/613119 "}, {"RefNumber": "581597", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination with F4 help for nodes during  selection", "RefUrl": "/notes/581597 "}, {"RefNumber": "622591", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSMDEXITON: Short dump GETWA_NOT_ASSIGNED", "RefUrl": "/notes/622591 "}, {"RefNumber": "622488", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "PFCG: Error when calling F4 help", "RefUrl": "/notes/622488 "}, {"RefNumber": "616453", "RefComponent": "BW-WHM", "RefTitle": "BW2.0B/2.1C (SP 32/24): ALPHA converter - SQL 615", "RefUrl": "/notes/616453 "}, {"RefNumber": "620152", "RefComponent": "BW-WHM-DST", "RefTitle": "Syntax error in RSTMPL9A during data loading from a file", "RefUrl": "/notes/620152 "}, {"RefNumber": "620455", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Command sequence is not processed correctly", "RefUrl": "/notes/620455 "}, {"RefNumber": "618848", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination SAPSQL_ARRAY_INSERT_DUPREC with alpha convers.", "RefUrl": "/notes/618848 "}, {"RefNumber": "619888", "RefComponent": "BW", "RefTitle": "Alpha conversion: \"Conversion for usage type IOBJ ...\"", "RefUrl": "/notes/619888 "}, {"RefNumber": "613854", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Restricted key figure with #, <PERSON><PERSON><PERSON><PERSON>", "RefUrl": "/notes/613854 "}, {"RefNumber": "618876", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "Transporting settings/packages: no deletion", "RefUrl": "/notes/618876 "}, {"RefNumber": "613890", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Lock problems during rollup and the realignment run", "RefUrl": "/notes/613890 "}, {"RefNumber": "614355", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Complex authorizations with intervals in the F4 help", "RefUrl": "/notes/614355 "}, {"RefNumber": "613024", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299(BRAIN), system error in SAPLRRK0, SINGLE PAGE-01-", "RefUrl": "/notes/613024 "}, {"RefNumber": "612304", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in the time derivation for stock queries", "RefUrl": "/notes/612304 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "20B", "To": "20B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}