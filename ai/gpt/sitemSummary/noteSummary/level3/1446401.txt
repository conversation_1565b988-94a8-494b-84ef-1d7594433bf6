SAP Note 1446401 provides detailed instructions for implementing additional buckets in the Retail Method of Accounting (RMA) stock ledger in the SAP system. This enhancement allows users to track additional types of inventory movements or value adjustments beyond the standard set provided by SAP.

Prerequisites:
- SAP ECC 6.03 and BI_CONT 7.04 or higher.
- Business function ISR_RETAIL_RMA is activated in ECC.

Summary of the Solution:
1. Additional goods receipts bucket based on purchase order type:
   - Modification of ECC and BW structures to include a new field (e.g., BSART for PO type).
   - Data replication and enhancement from ECC to BW.
   - BAdI implementation and modification of start routines to handle new data.
   - Introduction of new transaction keys, condition types, and mapping of data in BW to reflect the new buckets.

2. Additional cost allocation bucket for capitalizing certain costs:
   - Configuration of new bucket in ECC including creating a new condition group and possibly a billing type.
   - BW configuration includes assigning transaction keys, creating condition types, and updating RMA calculation schema.
   - Adjustment of existing coding to support the new bucket and ensure proper display in the RMA audit trail.

3. Enhancements for the RMA audit trail to support the new buckets.
   - Customization of the audit trail report to include the new buckets and access condition types.

4. Remarks on mapping additional custom key figures for custom RMA buckets, which may be needed if non-standard key figures are used.

5. Instructions for configuring a custom key figure as a source field in the RMA raw data DSO when it's outside the SAP namespace.

Implementing these enhancements involves several steps across the ECC and BW systems, including updates to data structures, data replication, condition type creation, and coding adjustments. Users should have technical knowledge of ECC and BW to carry out these changes.