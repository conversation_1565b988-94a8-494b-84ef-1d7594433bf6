{"Request": {"Number": "1321293", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 391, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016760262017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001321293?language=E&token=F9903F9B2815B97838276BFBCE9F221A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001321293", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001321293/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1321293"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.06.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1321293 - BI_CONT 7.05: Information about installation and upgrade"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains information about <B>Business Intelligence Content 705</B> in relation to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add-on installation and delta upgrade<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Basis upgrade to 70X<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAINT, installation, upgrade, BI_CONT, add-on, AOI, AOX, SAPK-705IHINBICONT, SAPK-705DHINBICONT, SAPK-705DHINBICONT<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You want to install Business Intelligence Content (BI_CONT 705).<br />You want to perform a delta upgrade from BI_CONT 704 or lower to BI_CONT 705.<br />You want to perform an upgrade to Basis 70X.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>This note is updated constantly. Make sure you have the current version of this note before you start the installation.</B><br /><br /><br /></p> <b>Contents</b><br /> <OL>1. Change history</OL> <OL>2. Important general information</OL> <OL><OL>a) qRFC version</OL></OL> <OL><OL>b) Updates</OL></OL> <OL>3. Installation, delta upgrade</OL> <OL><OL>a) Prerequisite</OL></OL> <OL><OL>b) Preparations</OL></OL> <OL><OL>c) Performing the installation</OL></OL> <OL><OL>d) Performing the delta upgrade<br />a) Including BI_CONT 705 in the installation of Enhancement Packages using SAPehpi.<br />b) Delta upgrade to BI_CONT 705 with SAINT.</OL></OL> <OL>4. Upgrades</OL> <OL><OL>a) Prerequisite</OL></OL> <OL><OL>b) Preparations</OL></OL> <OL><OL>c) Additional information about the prepare</OL></OL> <OL><OL>d) Password</OL></OL> <OL>5. Postprocessing the BI_CONT 705 installation/upgrade</OL> <p><br /></p> <b>Contents</b><br /> <OL>1. Change history<br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Date</TH><TH ALIGN=LEFT> Topic</TH><TH ALIGN=LEFT> Short Description</TH></TR> <TR><TD>25.05.2011</TD><TD> Known Errors</TD><TD> Generation error inserted</TD></TR> <TR><TD>22.06.2011</TD><TD> Important Information</TD><TD> BI_CONT 705 and SAP Solution Manager 7.01<br /></TD></TR> </TABLE></OL> <OL>2. Important general information</OL> <OL><OL>a) BI_CONT 705 can only be installed together with BI_CONT 706 in an \"SAP Solution Manager 7.0 EHP1\" system. In an \"SAP Solution Manager 7.0 EHP1 SPS18-SPS26\" system, only BI_CONT 704 is supported. As of \"SAP Solution Manager 7.0 EHP1 SPS27\", only BI_CONT 706 is supported.</OL></OL> <OL><OL>b) qRFC version</OL></OL> <UL><UL><LI>Note that the qRFC version must be 45 or higher (see Note 0498484).</LI></UL></UL> <OL><OL>c) Updates</OL></OL> <UL><UL><LI>Process your V3 update entries before you carry out the upgrade. Otherwise, there is a risk that you may no longer be able to update entries if changes are introduced into the interface structures of the V3 update modules by the patch or upgrade (see Note 328181).</LI></UL></UL> <UL><UL><LI>Before the upgrade, process your entries in the extraction queues. Otherwise, there is a risk that you may no longer be able to update these entries if changes to the interface structures of the qRFC function modules are introduced by the patch or the upgrade (see Note 328181).</LI></UL></UL> <UL><UL><LI>Before the upgrade, delete your entries in the reconstruction tables for the logistics extraction applications. Otherwise, there is a risk that you may no longer be able to use these entries if changes to the extraction structures are introduced by the patch or the upgrade (see Note 328181).</LI></UL></UL> <p></p> <OL>3. Installation, delta upgrade</OL> <OL><OL>a) Prerequisite</OL></OL> <UL><UL><LI>Import the latest SPAM/SAINT update.</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Refer to the following notes before you start the installation:</TH></TR> <TR><TD>Add-ons: General conditions </TD><TD> 70228</TD></TR> <TR><TD>Note about the release strategy</TD><TD> 153967</TD></TR> <TR><TD>Known problems in transaction SAINT</TD><TD> 179116</TD></TR> </TABLE></UL></UL> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Required component Support Packages for BI_CONT 705:</TH></TR> </TABLE> <UL><LI><B>SAP NetWeaver 7.0</B><br />SAP NetWeaver 7.0 Support Package stack 21</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>SAP_BASIS</TD><TD> 700</TD><TD> Support Package 21</TD></TR> <TR><TD>SAP_ABA</TD><TD> 700</TD><TD> Support Package 21</TD></TR> <TR><TD>SAP_BW</TD><TD> 700</TD><TD> Support Package 23</TD></TR> <TR><TD>PI_BASIS</TD><TD> 2005_1_700</TD><TD> Support Package 21</TD></TR> <TR><TD> or </TD><TD> 2006_1_700</TD><TD> Support Package 10</TD></TR> </TABLE></UL> <p></p> <UL><LI><B>SAP EHP1 for SAP NetWeaver 7.0</B><br />SAP EHP1 for SAP NetWeaver 7.0 Support Package stack 06:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>SAP_BASIS</TD><TD> 701</TD><TD> Support Package 06</TD></TR> <TR><TD>SAP_ABA</TD><TD> 701</TD><TD> Support Package 06</TD></TR> <TR><TD>SAP_BW</TD><TD> 701</TD><TD> Support Package 06</TD></TR> <TR><TD>PI_BASIS</TD><TD> 701</TD><TD> Support Package 06</TD></TR> </TABLE></UL> <p></p> <UL><LI><B>SAP EHP2 for SAP NetWeaver 7.0</B><br />SAP EHP2 for SAP NetWeaver 7.0 Support Package stack 03:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>SAP_BASIS</TD><TD> 702</TD><TD> Support Package 03</TD></TR> <TR><TD>SAP_ABA</TD><TD> 702</TD><TD> Support Package 03</TD></TR> <TR><TD>SAP_BW</TD><TD> 702</TD><TD> Support Package 03</TD></TR> <TR><TD>PI_BASIS</TD><TD> 702</TD><TD> Support Package 03</TD></TR> <TR><TD></TD></TR> <TR><TD></TD></TR> </TABLE></UL> <p></p> <UL><LI><B>Component Support Packages that are also required for the delta upgrade to BI_CONT 705:</B></LI></UL> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>BI_CONT</TD><TD> 704</TD><TD> Support Package 03</TD></TR> </TABLE> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you have not yet installed the required component Support Packages, you can include them in the installation or delta upgrade.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For the delta upgrade of BI_CONT 703 to BI_CONT 705, in addition to the BI_CONT 705 delta upgrade package, the BI_CONT 704 delta upgrade package SAPK-704DHINBICONT must also be made available and must be included in the delta upgrade. In this case, ensure that the prerequisites for the BI_CONT 704 delta upgrade are met. These are provided in Note 1172899.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BI_CONT 705 must not be installed if BANK-ALYZE 50 has already been installed in the system. In this case, use BI_CONT 703.<br /><br />-----------------------------------------------------------<br /></p> <OL><OL>a) Preparations</OL></OL> <UL><UL><LI>Download the installation or upgrade package from SAP Service Marketplace (quick link /INSTALLATIONS) into a temporary directory, or install the DVD&#x00A0;&#x00A0;with material number: ********.<br /><br />Installation:&#x00A0;&#x00A0;&lt;SAR archive&gt; = K-705IHINBICONT.SAR<br />on the DVD ******** in the subdirectory /BI_CONT/INST<br /><br />Delta upgrade: &lt;SAR archive&gt; = K-705DHINBICONT.SAR<br />on the DVD ******** in the subdirectory /BI_CONT/DELT</LI></UL></UL> <UL><UL><LI>Switch to the transport directory of your SAP System. You can display the transport directory by calling transaction AL11 and selecting the DIR_TRANS parameter.</LI></UL></UL> <UL><UL><LI>Unpack the required SAP archive in the directory EPS/in.</LI></UL></UL> <UL><UL><LI>You must now be able to find the following file in the /EPS/in directory:<br />CSR0120031469_0043843.PAT (installation)<br />CSR0120031469_0043846.PAT (delta upgrade)</LI></UL></UL> <UL><UL><LI>Load the add-on package from there into your SAP system (transaction SPAM -&gt; \"Support Package\"-&gt; \"Load Packages\"-&gt; \"From Application Server\").</LI></UL></UL> <OL><OL>b) Performing the installation</OL></OL> <UL><UL><LI>Call transaction SAINT and choose 'Start'.</LI></UL></UL> <UL><UL><LI>If you want to use the option of an import with several background processes, proceed as described in Note 705192.</LI></UL></UL> <UL><UL><LI>Select the BI_CONT 705 add-on and choose 'Continue'. If all of the necessary conditions for importing the add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the add-on package and can also contain Support Packages and other add-on packages. To start the installation process, choose \"Continue\".</LI></UL></UL> <UL><UL><LI>For more information, call transaction SAINT and choose \"Info\" on the application toolbar.</LI></UL></UL> <UL><UL><LI>The system prompts you to enter a password for BI_CONT 705 (SAPK-705IHINBICONT). The password is: <B>8058D78799</B><br /><br /></LI></UL></UL> <OL><OL>c) Performing the delta upgrade<br /><br /><B> I<B>) Including BI_CONT 705 in the installation of Enhancement Packages using SAPehpi.</B></B></OL></OL> <UL><UL><LI>If you do not want to install an Enhancement Package and you are planning a pure delta upgrade with SAINT, ignore this section and continue reading under point b).</LI></UL></UL> <UL><UL><LI>The general approach for the installation using SAPehpi is described in the guides for the relevant Enhancement Package for SAP NetWeaver 7.0 or SAP ERP 6.0 at https://service.sap.com/erp-inst.</LI></UL></UL><UL><UL><LI>When you perform an upgrade with a target release of SAP_BASIS 701 (SR1), the system requests a decision in the phase ADDON_DYN. Select the \"Keep Add-on BI_CONT\" option.</LI></UL></UL> <UL><UL><LI>In the phase IS_SELECT, you will be asked for a decision about dealing with the add-on BI_CONT.<br />Select target release 705 and the following upgrade type: \"Upgrade with SAINT package\".</LI></UL></UL> <UL><UL><LI>The system prompts you to enter a password for BI_CONT 705. The password is: <B>3202946</B><br /><br /><B><B>II) Delta upgrade to BI_CONT 705 with SAINT</B></B></LI></UL></UL> <UL><UL><LI>Call transaction SAINT and choose 'Start'.</LI></UL></UL> <UL><UL><LI>If you want to use the option of an import with several background processes, proceed as described in Note 705192.</LI></UL></UL> <UL><UL><LI>Select the BI_CONT 705 add-on and choose 'Continue'. If all of the required conditions for importing the add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the add-on package and can also contain Support Packages and other add-on packages. To start the installation process, choose \"Continue\".</LI></UL></UL> <UL><UL><LI>For more information, call transaction SAINT and choose \"Info\" on the application toolbar.</LI></UL></UL> <UL><UL><LI>The system prompts you to enter a password for BI_CONT 705 (SAPK-705DHINBICONT). The password is: <B>8058D78A99</B></LI></UL></UL> <OL><OL>d) <B>Known errors</B></OL></OL> <UL><UL><LI>During the installation of BI_CONT 706 in a system with SAP_BW 701 Support Package level 07 or 08, the following generation errors occur:<br /><br />Program CL_BW_BCT_CRM_EXPLORER_AUTH===CP, include CL_BW_BCT_CRM_EXPLORER_AUTH===CU: Syntax error in line 000010 Type 'IF_RSDDTPS_AUTH' is unknown<br /><br />Program RSCXP_CRM_BI_AUTH_TEST: Syntax error in line 000058 Type 'IF_RSDDTPS_AUTH=&gt;TN_T_USER' is unknown<br /><br />Solution:<br />Proceed as described in Note 1571255. </LI></UL></UL> <OL>4. Upgrades</OL> <OL><OL>a) Prerequisite</OL></OL> <UL><UL><LI>Import the latest SPAM/SAINT update.</LI></UL></UL> <UL><UL><LI>The upgrades of the following BI_CONT releases are supported:<br />BI_CONT 330 and higher</LI></UL></UL> <OL><OL>b) Preparation</OL></OL> <UL><UL><LI>Download the upgrade package from SAP Service Marketplace (quick link INSTALLATIONS) to a temporary directory, or install the CD with the material number: ********. The add-on upgrade package is unpacked automatically from the CD. To do this, in the UPLOAD_REQUEST phase, specify the directory under which you want to mount the Add-On upgrade CD.<br />If you downloaded the add-on upgrade package directly from SAP Service Marketplace, unpack it in the &lt;DIR_EPS_ROOT&gt;/in directory.<br />Information: The CSR0120031469_0043833.PAT file should be in the &lt;DIR_EPS_ROOT&gt;/in directory (after the UPLOAD_REQUEST phase at the latest).</LI></UL></UL> <p></p> <OL><OL>c) Additional information about the prepare</OL></OL> <UL><UL><LI>Entries when performing an upgrade with target release SAP_BASIS 701 (SR1)<br /><br />The system requests a decision in the phase ADDON_SPEC3. Select the 'Stay on BI_CONT' option.</LI></UL></UL> <UL><UL><LI>Entries when performing an upgrade with target release SAP_BASIS 700 (SR3)<br /><br />The system requests a decision in the phase IS_MOVEMENT. Select the option 'BICONT'.</LI></UL></UL> <UL><UL><LI>IS_SELECT phase: For BI_CONT, select target release 705 and the following upgrade type:<br />\"Upgrade with SAINT Package</LI></UL></UL> <UL><UL><LI>Phase PATCH_CHK: Include the available Add-On Support Packages in the upgrade.</LI></UL></UL> <OL><OL>d) Known errors</OL></OL> <UL><LI>Table and runtime object exist without DDIC reference (\"Transp. table\")<br /><br />Symptom:<br />The system displays the following two errors in the log file RDDNTPUR.&lt;SID&gt;:<br />Table and runtime object \"CRMPROMOTXT1\" exist without DDIC reference (\"Transp. table\")<br />Table and runtime object \"CRMRESPONTXT1\" exist without DDIC reference (\"Transp. table\")<br /><br />Solution:<br />If you do NOT use CRM Real Time Offer Management (RTOM), you can ignore these errors.</LI></UL> <p></p> <OL><OL>a) The password in the phase KEY_CHK is <B>3202946</B>.<br /></OL></OL> <OL>5. Postprocessing the BI_CONT 705 installation/upgrade</OL> <UL><LI>The current support packages for BI_CONT 705 are available on SAP Service Marketplace under the quick link /patches.</LI></UL> <UL><LI>In addition to German and English, BI_CONT 705 supports the following languages:<br />Arabic<br />Bulgarian<br />Chinese<br />Trad. Chinese<br />Danish<br />Finnish<br />French<br />Greek<br />Hebrew<br />Italian<br />Japanese<br />Catalan<br />Korean<br />Croatian<br />Dutch<br />Norwegian<br />Polish<br />Portuguese<br />Romanian<br />Russian<br />Swedish<br />Slovakian<br />Slovenian<br />Spanish<br />Czech<br />Turkish<br />Hungarian</LI></UL> <UL><LI>No further postprocessing is necessary for any languages that were already installed as a default language before the add-on installation. If you wish to install one of these languages at a later stage, proceed as follows: You must install the desired default language before you install the add-on language file. Perform the add-on language import in accordance with Note 195442.</LI></UL> <UL><LI>When you import language packages, errors may occur in the method execution (\"OBJECTS_NOT_CHARLIKE\") if the system has NW04s Support Package Level 07. The error symptom and the solution are described in Note 925752.</LI></UL> <UL><LI>If you use budget planning and the budget monitor (transaction UPARI_BUDGA) Business Content functions in the retail area, carry out the postprocessing steps described in Note 897072.</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D038076)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D040549)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001321293/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001321293/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001321293/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001321293/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001321293/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001321293/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001321293/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001321293/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001321293/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967"}, {"RefNumber": "1480716", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installing BI_CONT_XT on SAP NetWeaver 7.02 and higher", "RefUrl": "/notes/1480716"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1000822", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: SAP Notes for the add-ons BI_CONT and BI_CONT_XT", "RefUrl": "/notes/1000822 "}, {"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967 "}, {"RefNumber": "1480716", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Installing BI_CONT_XT on SAP NetWeaver 7.02 and higher", "RefUrl": "/notes/1480716 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "705", "To": "705", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}