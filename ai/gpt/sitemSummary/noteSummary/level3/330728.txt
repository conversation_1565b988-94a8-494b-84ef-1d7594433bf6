SAP Note 330728 addresses an issue observed in the List Viewer (also known as ALV Grid Control), where problems occur when users attempt to expand or collapse data rows associated with total lines. Users may encounter three specific symptoms:
1. Data rows related to a totals line may not display correctly after expanding or collapsing.
2. Clicking on a totals line may not be responsive if the line contains hotspots (columns that respond to clicks).
3. Expanding or collapsing the overall total may not work at all.

The root cause of these issues is identified as a program error within the SAP front end.

The resolution recommended in the note is to install the latest front-end patch for SAP. The note specifies the patch levels needed for different SAPGUI versions:
- For SAPGUI 46D, the required patch level is 632 or higher.
- For SAPGUI 620, the patch level should be 30 or higher.

To obtain these patches, users are directed to the SAP Service Marketplace at the provided URL. Additional guidance on downloading and installing patches can be found in SAP Note 96885.

In summary, SAP Note 330728 provides a solution for the malfunctioning expansion and collapse functionality within the ALV Grid Control, directing users to update their SAP front end with the specified patches to correct the issue.