SAP Note 2436688 provides guidance on using the ABAP Test Cockpit (ATC) and the Custom Code Migration Fiori app for S/4HANA custom code checks. The note emphasizes the importance of applying a series of other SAP Notes to ensure that the latest versions of Note Assistant, SCWB/SNOTE Activation, and Note Analyzer are used.

The solution section advises following a sequence of steps to process the SAP Notes using Note Analyzer for both local/central check systems and checked systems. In remote analysis scenarios or when using the Fiori app for Custom Code Migration, additional SAP Notes (e.g., 2599695, 2888880, 2889487, 2742368) should be installed in the checked system.

The note provides a comprehensive list of SAP Notes pertinent to different SAP BASIS releases that should be applied regularly, as they are updated frequently. These notes offer valuable corrections and enhancements to support the migration and checks for S/4HANA custom code.

Users are advised to manually apply the SAP Notes if the Note Analyzer is inaccessible. This comprehensive list of recommended notes aims to facilitate smooth transition processes when checking S/4HANA readiness and performing custom code adaptations during system conversion.

Additionally, SAP Note 2436688 refers to other related notes, including:

- 2866977, which details quick fixes available for S/4HANA custom code checks starting from version 1809 FPS2 or higher.
- 2865234, which addresses the consistency of findings in S/4HANA custom code checks.
- 2861842, which provides instructions for setting up the SAP Cloud Connector for the Custom Code Migration App in the SAP BTP, ABAP Environment.
- 2364938, which contains infrastructure components for S/4HANA readiness checks.
- 2364916, which recommends specific notes for using ATC to perform remote analyses and ensures the most recent SAP_BASIS release for the central check system.

By following the detailed instructions and applying the necessary SAP Notes outlined in this document, users will be better prepared to assess and adapt their custom ABAP code for S/4HANA.