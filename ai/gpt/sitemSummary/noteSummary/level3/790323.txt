SAP Note 790323 provides instructions on how to use the Reporting Authorization Check Log (RSSM log) in SAP BW to analyze authorization checks during query execution. Here's a summary based on the provided SAP Note content:

**Symptom:**
Users need detailed information on authorization checks when executing queries in BW to understand why access might be denied. The RSSM log is used for this purpose.

**Contents:**
The note explains how to create and simplify the RSSM log and how to use it as a spool request, plus the contents of the log. It details the log header, buffer filling, selection display, RS and RSR authorization checks, and hierarchy authorization checks.

**Creating a Log:**
1. Start transaction RSSM and enter the user ID for the authorization check log.
2. Perform the action to be checked (usually query execution).
3. After action completion, use "Display Log" to view the recent log.
4. Remember to delete the user ID from the log records after testing.

**Simplifying the Log:**
- Use fixed filters instead of authorization variables.
- Avoid F4 input help.
- Use the "Bookmark" function instead of navigating within the query.
- Simplify logs to understand them better.

**RSSM Log as a Spool Request:**
- Spool requests can be viewed using transaction SP01.
- Consider timestamps and user IDs when looking at old logs.
- Logs might be split into multiple spool requests.
- Longer spool requests may require settings adjustments for full display.
- Logs are created in the logon language; for SAP Support, use English and export logs in HTML format.

**Contents of the RSSM Log:**
- Log header: includes timestamps and system information.
- Filling the Buffer: shows how user authorizations are saved and managed.
- Displaying the Selection: shows user selections, hierarchy information, and checks against authorized values.
- RS and RSR authorizations: explains the check for reporting authorizations and the basic principles used.
- Hierarchy Authorization Block: details on the hierarchy check process.
- End of Log: contains compatibility settings and overall result of the authorization check.

**Important Notes:**
- The RSSM authorization log is intended for administrators and not for end users.
- The log can be volatile as BI development might alter its parts.
- In cases of failed checks, the query returns error BRAIN 804.

By following this note, administrators can effectively work with the RSSM log to troubleshoot and resolve authorization issues in SAP BW.