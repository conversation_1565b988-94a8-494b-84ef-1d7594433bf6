The SAP Note 570161 addresses the need for a specialized Business Add-In (BAdI) to enable customers to implement custom logic for time-based authorization checks in Personnel Administration (PA) without having to completely replace the standard SAP authorization check. The BAdI provided for this purpose is HRPAD00CHECK_TIME.

**Symptom:**
The note recognizes that some customers require a different time logic for PA authorization checks which relies on specific infotypes (INFTY) or authorization levels (LEVEL). However, for changes only in the time logic, using the standard BAdI HRPAD00CHECK_AUTH that replaces the whole authorization check is too complex.

**Solution:**
BAdI HRPAD00CHECK_TIME is provided to allow the implementation of custom time logic for infotypes and/or authorization levels. For instance, for uniform time logic for both reading and writing authorizations (LEVEL dependent), HRPAD00CHECK_TIME can be used. However, if the logic is dependent on employee groups (like employee subgroup PERSK), then HRPAD00CHECK_AUTH should be used instead.

The note gives a detailed step-by-step guide to implementing the BAdI HRPAD00AUTH_TIME, including using SAP transactions SE18 and SE19 for creating and implementing the BAdI, and writing the custom ABAP code within the provided interface. The example provided illustrates how to use the BAdI for infotype 0008, checking for an authorization period that includes the current system date (SY-DATUM) to grant access to the infotype for a specific date range if the period exists, otherwise, the user has no authorization.

**Other Terms Mentioned:**
- P_ORGIN
- P_ORGXX

Implementation of this BAdI requires knowledge of ABAP programming and is intended for a development class environment. The specific support package needed for the BAdI can be found in the attachment provided with the SAP Note.