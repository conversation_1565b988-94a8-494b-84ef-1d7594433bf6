{"Request": {"Number": "1318389", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 632, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016755022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001318389?language=E&token=01D7C3E38E9C954696234C40E385C0DC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001318389", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001318389/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1318389"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.12.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CTS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Change and Transport System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Change and Transport System", "value": "BC-CTS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1318389 - How to Use .SAR/.CAR files and Risks Involved"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Some objects must be delivered urgently. If the regular delivery method takes too long or cannot be used for other reasons, the objects must be delivered in a transport request.<br />The transport request is placed in a .SAR/.CAR file. This delivery method implies some risks and you must follow a series of steps to implement a .SAR/.CAR file.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Special transport CAR SAR file</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The delivery of .SAR/.CAR files is restricted to the following scenarios:</p>\r\n<ol>1. A pilot delivery that cannot be shipped in a Support Package (SP).</ol><ol>2. An urgent legal change, where an SP would be shipped too late to comply with the validity of the legal change.</ol><ol>3. A show-stopper that must be solved urgently.</ol>\r\n<p><br />Additionally, the following prerequisites must be met:</p>\r\n<ul>\r\n<li>The required implementation effort for customers is more than one person day (PD)</li>\r\n</ul>\r\n<ul>\r\n<li>The changes cannot be implemented using SNOTE.</li>\r\n</ul>\r\n<p><br />Given that a delivery by means of a .SAR/.CAR file is completely disconnected from regular delivery methods, a series of risks are associated with the files:</p>\r\n<ul>\r\n<li>SAP standard checks are not available:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>No memory-related space checks</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Object anonymity: The object owner still appears in the object attribute</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The delivery class of tables is not checked (for example, a .SAR/.CAR file might contain entries in customer namespace)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Objects might get reset to an older version:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Imports of .SAR/.CAR files are not treated like repairs.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>During the next release upgrade, these objects will be overwritten without warning.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Changes/corrections will not appear in change comparisons that you can perform using SPDD and SPAU.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Delivering DDIC changes is risky:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The possibility of data loss is high.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The severity of any damage caused is very high.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Customer modifications will be overwritten without warning:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If a .SAR/.CAR file contains objects that already exist in your system, and you have modified these objects, the objects in your system will be overwritten and the modifications will be lost.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>The future use of SNOTE will cause an inconsistency:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The system SP level will not match the SP/correction instruction (CI) level of the objects.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Objects shipped in a .SAR/.CAR file might not work in your system:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Objects shipped in a .SAR/.CAR file are based on the latest SP environment; as such, the objects in the .SAR file might not work in your system.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>.SAR/.CAR files do not contain translations:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The objects in .SAR/.CAR files are shipped in the original language of the objects only. Any translations are available in SPs only.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>.SAR/.CAR files do not contain changes to the area menu (the menus on the SAP Easy Access screen) or Implementation Guide (IMG):</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If a .SAR/.CAR file contains a new report or view, you have to call up these objects manually, for example, using transaction SA38 for reports and transaction SM30 for views.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>You might use the correct .SAR/.CAR file, but at the wrong time:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For example, two years after an SAP Note containing a .SAR/.CAR file is released, you import the .SAR/.CAR file. The file deletes any new object versions that have been shipped in the meantime (there is no check or import verification as with SPs and correction instructions).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>You might use the wrong .SAR/.CAR file:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For example, if you have a 4.6C system and you import a file intended for 4.70, the file will damage your 4.6C installation.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Incompatibilities between SAPCAR and CAR Tools.</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The files genereated via the old tool CAR can be incompatible with tool SAPCAR. See SAP Note 212876 for more details on the SAPCAR Tool.</li>\r\n</ul>\r\n</ul>\r\n<p><br />To implement .SAR/.CAR files, follow the procedure described in the SAP Note to which the files are attached. Pay attention to the release validity of the files and the order in which you must implement the files.<br />For more information about the technical steps involved in importing .SAR/.CAR files, see SAP Notes 13719 and 480180.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D036645)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D036645)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001318389/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001318389/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001318389/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001318389/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001318389/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001318389/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001318389/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001318389/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001318389/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "1884607", "RefComponent": "PY-FR", "RefTitle": "Loi relative à la sécurisation de l'emploi: Payroll Log", "RefUrl": "/notes/1884607"}, {"RefNumber": "1812089", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1812089"}, {"RefNumber": "1811063", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1811063"}, {"RefNumber": "1776403", "RefComponent": "PY-US-TR", "RefTitle": "TR: W-2 creation when earns only box 12 DD", "RefUrl": "/notes/1776403"}, {"RefNumber": "1760103", "RefComponent": "PY-JP", "RefTitle": "LC2012: Tax System Revision for Income Tax", "RefUrl": "/notes/1760103"}, {"RefNumber": "1731206", "RefComponent": "PY-FR", "RefTitle": "DNAC-AED: AED statement viewer", "RefUrl": "/notes/1731206"}, {"RefNumber": "1678400", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1678400"}, {"RefNumber": "1647554", "RefComponent": "RE-FX-LC-CH", "RefTitle": "National Register of Buildings and Dwellings (GWR)", "RefUrl": "/notes/1647554"}, {"RefNumber": "1547506", "RefComponent": "PY-AT", "RefTitle": "Einkommensbericht aufgrund Gleichbehandlungsgesetz", "RefUrl": "/notes/1547506"}, {"RefNumber": "1545704", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "IS-U Localization RU: corrections", "RefUrl": "/notes/1545704"}, {"RefNumber": "1523958", "RefComponent": "XX-CSC-BR-REP", "RefTitle": "EFD-Contributions New Legal File", "RefUrl": "/notes/1523958"}, {"RefNumber": "1497982", "RefComponent": "PY-BR", "RefTitle": "Ordinance 1620: Homolognet", "RefUrl": "/notes/1497982"}, {"RefNumber": "1417026", "RefComponent": "PY-JP", "RefTitle": "LC2010 Labor Standards Act Legal Change Phase 2", "RefUrl": "/notes/1417026"}, {"RefNumber": "1417025", "RefComponent": "PY-JP", "RefTitle": "LC2010 Labor Standards Act Legal Change Phase 1", "RefUrl": "/notes/1417025"}, {"RefNumber": "1402100", "RefComponent": "PY-US-TX", "RefTitle": "TAX: Wisconsin's Advanced Earned Income Credit (EIC) Payment", "RefUrl": "/notes/1402100"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3418787", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP02/2024 - Tax certificates 2024 - Attachm.", "RefUrl": "/notes/3418787 "}, {"RefNumber": "3334579", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP06/2023 - SI Enhancement I. - Attachm.", "RefUrl": "/notes/3334579 "}, {"RefNumber": "3327756", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP06/2023 - RPSMKFH2 Workforce cost entry - Attachm.", "RefUrl": "/notes/3327756 "}, {"RefNumber": "3288515", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP02/2023 - Payroll-M30-1405/1668 customizing 2023 - Attachm.", "RefUrl": "/notes/3288515 "}, {"RefNumber": "3287836", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP02/2023 - Data sheet on termination of employment - Attachm.", "RefUrl": "/notes/3287836 "}, {"RefNumber": "2910013", "RefComponent": "PY-AT", "RefTitle": "COVID-19-<PERSON><PERSON><PERSON><PERSON><PERSON>: Customizing via SAR-File", "RefUrl": "/notes/2910013 "}, {"RefNumber": "2853758", "RefComponent": "PY-AT", "RefTitle": "JW2019/20: ELDA Customizing", "RefUrl": "/notes/2853758 "}, {"RefNumber": "2733416", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2018 Phase III for U.S. Tax Reporter", "RefUrl": "/notes/2733416 "}, {"RefNumber": "2723165", "RefComponent": "PY-US-TX", "RefTitle": "BSI: Tax Types 098 to 103 (Washington Paid Family and Medical Leave) [CE]", "RefUrl": "/notes/2723165 "}, {"RefNumber": "2697516", "RefComponent": "PY-US-NT-GR", "RefTitle": "GARN: IRS Publication 1494 for tax year 2018 (SAR Files)", "RefUrl": "/notes/2697516 "}, {"RefNumber": "2698729", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2018 Phase II for U.S. Tax Reporter", "RefUrl": "/notes/2698729 "}, {"RefNumber": "2618262", "RefComponent": "PY-US-TR", "RefTitle": "TR: Q1/2018 Functional changes for U.S. Tax Reporter [IM]", "RefUrl": "/notes/2618262 "}, {"RefNumber": "2571096", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2017 Phase III for U.S. Tax Reporter", "RefUrl": "/notes/2571096 "}, {"RefNumber": "2067935", "RefComponent": "PY-AT", "RefTitle": "Buchung: Reporterweiterungen für NGL", "RefUrl": "/notes/2067935 "}, {"RefNumber": "2167502", "RefComponent": "PA-PA-US-BN", "RefTitle": "BEN: Main Note - Affordable Care Act (ACA)", "RefUrl": "/notes/2167502 "}, {"RefNumber": "2161214", "RefComponent": "PY-XX-TL", "RefTitle": "Delivery of HR Development Packages with correct software components", "RefUrl": "/notes/2161214 "}, {"RefNumber": "2131405", "RefComponent": "PY-XX", "RefTitle": "Template - Software delivery for SAP Note YYYYYYY", "RefUrl": "/notes/2131405 "}, {"RefNumber": "2122002", "RefComponent": "PY-BR", "RefTitle": "eSocial: ABAP dictionary - Release 2015/02", "RefUrl": "/notes/2122002 "}, {"RefNumber": "2106250", "RefComponent": "XX-CSC-EE-FI", "RefTitle": "Estonian Value-Added TAX report for ECC500 version", "RefUrl": "/notes/2106250 "}, {"RefNumber": "2047104", "RefComponent": "PY-JP", "RefTitle": "LC2014: Income Tax Rate Revision from 2015", "RefUrl": "/notes/2047104 "}, {"RefNumber": "2008866", "RefComponent": "PY-BR", "RefTitle": "eSocial: ABAP dictionary - Release 2014/08", "RefUrl": "/notes/2008866 "}, {"RefNumber": "2008864", "RefComponent": "PY-BR", "RefTitle": "eSocial: ABAP dictionary - Release 2014/08", "RefUrl": "/notes/2008864 "}, {"RefNumber": "1997972", "RefComponent": "PY-US-TR", "RefTitle": "TR: Electronic Confirmation Number for W-2/W-2c PR paper", "RefUrl": "/notes/1997972 "}, {"RefNumber": "1996677", "RefComponent": "PY-BR", "RefTitle": "eSocial: ABAP dictionary - Release 2014/03", "RefUrl": "/notes/1996677 "}, {"RefNumber": "1995906", "RefComponent": "PY-BR", "RefTitle": "eSocial: ABAP dictionary - Release 2014/06", "RefUrl": "/notes/1995906 "}, {"RefNumber": "1961195", "RefComponent": "PY-SG", "RefTitle": "Overtime eligibility", "RefUrl": "/notes/1961195 "}, {"RefNumber": "1862873", "RefComponent": "XX-CSC-SK-HR", "RefTitle": "HRSK: SEPA in SK master data - transport", "RefUrl": "/notes/1862873 "}, {"RefNumber": "1920654", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2013 Phase II for U.S. Tax Reporter", "RefUrl": "/notes/1920654 "}, {"RefNumber": "1523958", "RefComponent": "XX-CSC-BR-REP", "RefTitle": "EFD-Contributions New Legal File", "RefUrl": "/notes/1523958 "}, {"RefNumber": "1845468", "RefComponent": "PY-US-TX", "RefTitle": "TAX: Wage types for Additional Medicare tax", "RefUrl": "/notes/1845468 "}, {"RefNumber": "1870139", "RefComponent": "PY-US-TR", "RefTitle": "Q2/2013: Functional changes for U.S. Tax Reporter.", "RefUrl": "/notes/1870139 "}, {"RefNumber": "1879924", "RefComponent": "PY-US-TR", "RefTitle": "Q2/2013: Functional changes for U.S. Tax Reporter.", "RefUrl": "/notes/1879924 "}, {"RefNumber": "1877805", "RefComponent": "PY-US-TR", "RefTitle": "TR: New format for SUI Magmedia for Michigan", "RefUrl": "/notes/1877805 "}, {"RefNumber": "1884607", "RefComponent": "PY-FR", "RefTitle": "Loi relative à la sécurisation de l'emploi: Payroll Log", "RefUrl": "/notes/1884607 "}, {"RefNumber": "1811063", "RefComponent": "PY-AT", "RefTitle": "JW 2012/13 Beitragsnachweisung für überlassene Dienstnehmer", "RefUrl": "/notes/1811063 "}, {"RefNumber": "1859625", "RefComponent": "XX-CSC-SK-HR", "RefTitle": "HRSK: The Rounding of the Tax Calculation is Wrong", "RefUrl": "/notes/1859625 "}, {"RefNumber": "1849244", "RefComponent": "PY-US-TX", "RefTitle": "TAX: PA LST lump-sum/prorated amount customizing", "RefUrl": "/notes/1849244 "}, {"RefNumber": "1836869", "RefComponent": "PY-US-TX", "RefTitle": "TAX: PA LST lump-sum/prorated amount customizing", "RefUrl": "/notes/1836869 "}, {"RefNumber": "1826041", "RefComponent": "PY-US-TR", "RefTitle": "Q1/2013: Functional changes for U.S. Tax Reporter.", "RefUrl": "/notes/1826041 "}, {"RefNumber": "1803358", "RefComponent": "PY-US-TR", "RefTitle": "TR: SUI Monthly Reporting for the State of Illinois.", "RefUrl": "/notes/1803358 "}, {"RefNumber": "1799443", "RefComponent": "XX-CSC-RU-FI", "RefTitle": "J3RTAXREP:Customizng of Financial statement forms:Transports", "RefUrl": "/notes/1799443 "}, {"RefNumber": "1827904", "RefComponent": "PY-US-TR", "RefTitle": "Online W-2: Several W-2 displayed for overwrite scenarios", "RefUrl": "/notes/1827904 "}, {"RefNumber": "1776403", "RefComponent": "PY-US-TR", "RefTitle": "TR: W-2 creation when earns only box 12 DD", "RefUrl": "/notes/1776403 "}, {"RefNumber": "1647554", "RefComponent": "RE-FX-LC-CH", "RefTitle": "National Register of Buildings and Dwellings (GWR)", "RefUrl": "/notes/1647554 "}, {"RefNumber": "1825466", "RefComponent": "PY-US-TR", "RefTitle": "TR: Delete Tax Reporter Test Execution", "RefUrl": "/notes/1825466 "}, {"RefNumber": "1789179", "RefComponent": "PY-US-TR", "RefTitle": "Corrections to Online W-2: Wave 7", "RefUrl": "/notes/1789179 "}, {"RefNumber": "1755902", "RefComponent": "RE-FX-LC-CH", "RefTitle": "National Register of Buildings and Dwellings (GWR)", "RefUrl": "/notes/1755902 "}, {"RefNumber": "1814638", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2012 latest changes to magnetic media files", "RefUrl": "/notes/1814638 "}, {"RefNumber": "1812089", "RefComponent": "SRM-EBP-SHP", "RefTitle": "Database changes in SRM UI Add-on", "RefUrl": "/notes/1812089 "}, {"RefNumber": "1764123", "RefComponent": "PY-US-TR", "RefTitle": "Corrections to Online W-2: Wave 6", "RefUrl": "/notes/1764123 "}, {"RefNumber": "1804308", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2012 Additional Changes for U.S. Tax Reporter", "RefUrl": "/notes/1804308 "}, {"RefNumber": "1808129", "RefComponent": "PY-AT", "RefTitle": "JW2012-2013: Änderungen zum Jahreswechsel nicht vorhanden", "RefUrl": "/notes/1808129 "}, {"RefNumber": "1789868", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2012 Phase III for U.S. Tax Reporter", "RefUrl": "/notes/1789868 "}, {"RefNumber": "1768878", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2012 Phase II for U.S. Tax Reporter", "RefUrl": "/notes/1768878 "}, {"RefNumber": "1760103", "RefComponent": "PY-JP", "RefTitle": "LC2012: Tax System Revision for Income Tax", "RefUrl": "/notes/1760103 "}, {"RefNumber": "1748448", "RefComponent": "PY-US-TR", "RefTitle": "Q3/2012: Functional changes for U.S. Tax Reporter.", "RefUrl": "/notes/1748448 "}, {"RefNumber": "1680953", "RefComponent": "PY-XX-RS", "RefTitle": "HR Attributes Improvements", "RefUrl": "/notes/1680953 "}, {"RefNumber": "1468244", "RefComponent": "PY-XX-RS", "RefTitle": ".Pilot Delivery: HR Attributes", "RefUrl": "/notes/1468244 "}, {"RefNumber": "1497982", "RefComponent": "PY-BR", "RefTitle": "Ordinance 1620: Homolognet", "RefUrl": "/notes/1497982 "}, {"RefNumber": "1731206", "RefComponent": "PY-FR", "RefTitle": "DNAC-AED: AED statement viewer", "RefUrl": "/notes/1731206 "}, {"RefNumber": "1533198", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2010 Phase III for U.S. Tax Reporter", "RefUrl": "/notes/1533198 "}, {"RefNumber": "1497930", "RefComponent": "PY-US-TR", "RefTitle": "Q3/2010: Functional changes for U.S. Tax Reporter.", "RefUrl": "/notes/1497930 "}, {"RefNumber": "1731559", "RefComponent": "PY-US-TR", "RefTitle": "TR: Magmedia for W-2c Puerto Rico", "RefUrl": "/notes/1731559 "}, {"RefNumber": "1590055", "RefComponent": "PY-XX-RS", "RefTitle": "HR Attributes Improvements I", "RefUrl": "/notes/1590055 "}, {"RefNumber": "1665864", "RefComponent": "PY-US-RP", "RefTitle": "New York Labor Law 195.1 - Employee Notice", "RefUrl": "/notes/1665864 "}, {"RefNumber": "1722799", "RefComponent": "PY-US", "RefTitle": "Pennsylvania Act 32 - PSD Codes - May 2012", "RefUrl": "/notes/1722799 "}, {"RefNumber": "1723443", "RefComponent": "PY-US", "RefTitle": "TEMPLATE Note: Pennsylvania Act 32 - PSD Codes - Year 2012", "RefUrl": "/notes/1723443 "}, {"RefNumber": "1699419", "RefComponent": "PA-PA-NL", "RefTitle": "EIR: Electronic Absence Reporting 2nd .SAR file delivery", "RefUrl": "/notes/1699419 "}, {"RefNumber": "1578055", "RefComponent": "PY-AT", "RefTitle": "Pilotauslieferung: Einkommensbericht", "RefUrl": "/notes/1578055 "}, {"RefNumber": "1673410", "RefComponent": "PY-FR", "RefTitle": "N4DS transport files (so-called SAR-CAR files) availability", "RefUrl": "/notes/1673410 "}, {"RefNumber": "1671641", "RefComponent": "XX-CSC-RU-FI", "RefTitle": "Customs Union: Goods Import Declaration: Transports", "RefUrl": "/notes/1671641 "}, {"RefNumber": "1652864", "RefComponent": "PY-US", "RefTitle": "Pennsylvania Act 32 - PSD Codes - November 2011", "RefUrl": "/notes/1652864 "}, {"RefNumber": "1658622", "RefComponent": "PY-US-NT-GR", "RefTitle": "GRN: IRS Publication 1494 for tax year 2012", "RefUrl": "/notes/1658622 "}, {"RefNumber": "1645396", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2011 Phase II for U.S. Tax Reporter", "RefUrl": "/notes/1645396 "}, {"RefNumber": "1639029", "RefComponent": "PY-US", "RefTitle": "Pennsylvania Act 32 - November 2011 CLC", "RefUrl": "/notes/1639029 "}, {"RefNumber": "1653698", "RefComponent": "XX-CSC-SI-HR", "RefTitle": "HRSI: SEPA implementation in HR-SI - objects", "RefUrl": "/notes/1653698 "}, {"RefNumber": "1657373", "RefComponent": "XX-CSC-CN-EPIC", "RefTitle": "EPIC: preliminary delivery of Note 1622119", "RefUrl": "/notes/1657373 "}, {"RefNumber": "1626095", "RefComponent": "PA-PA-NL", "RefTitle": "EIR: Electronic Absence Reporting .SAR file delivery", "RefUrl": "/notes/1626095 "}, {"RefNumber": "1601000", "RefComponent": "XX-CSC-SI-HR", "RefTitle": "HRSI: New education and profession classifications - objects", "RefUrl": "/notes/1601000 "}, {"RefNumber": "1609765", "RefComponent": "XX-CSC-SK-HR", "RefTitle": "HRSK: Health Insurance statement - File 514 - Before LCP", "RefUrl": "/notes/1609765 "}, {"RefNumber": "1547506", "RefComponent": "PY-AT", "RefTitle": "Einkommensbericht aufgrund Gleichbehandlungsgesetz", "RefUrl": "/notes/1547506 "}, {"RefNumber": "1479550", "RefComponent": "PY-US-BSI", "RefTitle": "CONS: TAX: Arizona Resident Tax Withholding Percentage", "RefUrl": "/notes/1479550 "}, {"RefNumber": "1571815", "RefComponent": "PY-US-BSI", "RefTitle": "PY: Pennsylvania ACT 32 Reciprocal Formula", "RefUrl": "/notes/1571815 "}, {"RefNumber": "1567318", "RefComponent": "PY-AT", "RefTitle": "Korrekturen zur gesetzlichen Änderung des Montageprivilegs", "RefUrl": "/notes/1567318 "}, {"RefNumber": "1545704", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "IS-U Localization RU: corrections", "RefUrl": "/notes/1545704 "}, {"RefNumber": "1565471", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Add-on Archiving:Extension of selection screens", "RefUrl": "/notes/1565471 "}, {"RefNumber": "1553669", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukraine:Order 969 12.2010 Tax Voucher and correction change", "RefUrl": "/notes/1553669 "}, {"RefNumber": "1546682", "RefComponent": "PY-US-NT-GR", "RefTitle": "GRN: IRS Publication 1494 for tax year 2011.", "RefUrl": "/notes/1546682 "}, {"RefNumber": "1417025", "RefComponent": "PY-JP", "RefTitle": "LC2010 Labor Standards Act Legal Change Phase 1", "RefUrl": "/notes/1417025 "}, {"RefNumber": "1514526", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2010 Phase II for U.S. Tax Reporter", "RefUrl": "/notes/1514526 "}, {"RefNumber": "1402100", "RefComponent": "PY-US-TX", "RefTitle": "TAX: Wisconsin's Advanced Earned Income Credit (EIC) Payment", "RefUrl": "/notes/1402100 "}, {"RefNumber": "1417026", "RefComponent": "PY-JP", "RefTitle": "LC2010 Labor Standards Act Legal Change Phase 2", "RefUrl": "/notes/1417026 "}, {"RefNumber": "1447707", "RefComponent": "PY-AT", "RefTitle": "Pilotauslieferung: Zahlungen zum oder nach dem Austritt", "RefUrl": "/notes/1447707 "}, {"RefNumber": "1248983", "RefComponent": "PY-AR", "RefTitle": "Variable Remuneration for Vacation Payment", "RefUrl": "/notes/1248983 "}, {"RefNumber": "1352793", "RefComponent": "XX-CSC-SI-HR", "RefTitle": "HRSI: New statistical report Obrazec ZAP-SD/4L", "RefUrl": "/notes/1352793 "}, {"RefNumber": "1329325", "RefComponent": "XX-CSC-SI-HR", "RefTitle": "HRSI: eM-4 solution for 2008, XML reporting", "RefUrl": "/notes/1329325 "}, {"RefNumber": "1331101", "RefComponent": "XX-CSC-SI-HR", "RefTitle": "HRSI: eM-4 enhancements and corrections", "RefUrl": "/notes/1331101 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}