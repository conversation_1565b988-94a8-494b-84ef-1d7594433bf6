{"Request": {"Number": "3205320", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000657292022"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003205320?language=E&token=F4A8727EE8D25D4D80CF98607989819F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003205320", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003205320/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3205320"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.05.2022"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-MON-BPM-ANA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Analytics"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Monitoring & Alerting", "value": "SV-SMG-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Process Operations", "value": "SV-SMG-MON-BPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-MON-BPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analytics", "value": "SV-SMG-MON-BPM-ANA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-MON-BPM-ANA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3205320 - Repair report for syntax errors preventing implementation of SAP note 2745851 - Business Process Improvement Content for \"SAP Readiness Check 2.0\""}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>When attempting to implement SAP note 2745851 - Business Process Improvement Content for \"SAP Readiness Check 2.0\" and \"Process Discovery (evolution of SAP Business Scenario Recommendations)\" and \"SAP Innovation and Optimization Pathfinder\" within an SAP ERP system, syntax errors are preventing the complete implementation of the note and may appear as, but not limited to, the following:</p>\r\n<ul>\r\n<li>&#160;\"&lt;Obect Type&gt; &lt;Object Name&gt; has already been implemented\"</li>\r\n</ul>\r\n<p>The experienced syntax errors are appearing for the following ABAP coding objects delivered by SAP note 2745851:</p>\r\n<ul>\r\n<li>Class&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; /SDF/CL_S4RC20 BPA&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;Readiness Check 2.0</li>\r\n<li>Class&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; /SDF/CL_S4RC20_FACTORY&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; Readiness Check BPA Factory</li>\r\n<li>Class&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; /SDF/CL_S4RC20_REALCHECK&#160; &#160; &#160; &#160; &#160; &#160;BP Reality Check</li>\r\n<li>Class&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; /SDF/CL_S4RC20_VALUEOPP&#160; &#160; &#160; &#160; &#160; &#160; &#160; Value Opportunity Report</li>\r\n<li>Class Interface&#160; &#160; &#160;/SDF/IF_S4RC20&#160;</li>\r\n<li>Function Module&#160; /SDF/BPM_7X_S4RC20_GET_DATA&#160; &#160; &#160;Get Data</li>\r\n<li>Function Module&#160; /SDF/BPM_7X_S4RC20_GET_KPIS&#160; &#160; &#160; Get KPI List</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><span style=\"font-size: 14px;\">Within the Development system in which implementation of SAP note 2745851 is being implemented, complete the following:</span></p>\r\n<ol>\r\n<li>In the program name field enter report ZNOTE_2745851_REPAIR and select Create;</li>\r\n<li>IN the ABAP Program Attributes pop-up, provide a Title for the report, e.g. \"Note 274851 repair report\";</li>\r\n<li>In the Attributes section, select Type: Executable Program and Status: Test Program and select Save;</li>\r\n<li>In the Create Object Directory Entry pop-up, select Local Object;</li>\r\n<li>In the ABAP Editor window for the z report, copy the report coding provided in the text file attached to this note;</li>\r\n<li>Select Check, Save and Activate, and upon completion of this, select Execute to test execution of the report.<br /><br /></li>\r\n</ol>\r\n<p>The created report&#160;ZNOTE_2745851_REPAIR provides the following functionality to assist in repair of ABAP package /SDF/STPI_7X:</p>\r\n<ul>\r\n<li>An initial implementation status check to confirm if SAP note 2745851 has been de-implemented from the system and check if package /SDF/STPI_7X requires repair;</li>\r\n<li>A repair function to delete inconsistent coding objects created by SAP note 2745851 in package /SDF/STPI_7X which failed to be deleted during note de-implementation.<br /><br /></li>\r\n</ul>\r\n<p>To repair package /SDF/STPI_7X in the development system and complete implementation of SAP note 2745851, complete the following:</p>\r\n<ol>\r\n<li>De-implement SAP note 2745851 from the development system;</li>\r\n<li>Run tr. SE38 and execute report&#160;ZNOTE_2745851_REPAIR;</li>\r\n<li>Select option \"Check Note implementation Status\";</li>\r\n<li>The report will execute a note check to confirm de-implementation of note 2745851 and check package&#160;/SDF/STPI_7X to confirm if a cleanup is required;</li>\r\n<li>If the report output confirms a cleanup is required, execute the \"Cleanup package /SDF/STPI_7X\";</li>\r\n<li>The report will check and confirm the existence or absence of coding objects created by SAP note 2745851 in the system and if found, will delete these from package /SDF/STPI_7X;</li>\r\n<li>Deletion of the objects will be assigned to the open transport task assigned to the user profile executing the report;</li>\r\n<li>Upon output of the report that all found objects were successfully deleted from package /SDF/STPI_7X, run transaction SNOTE and re-implement SAP note 2745851 in the system;</li>\r\n<li>Implementation of the note will then complete without issue.</li>\r\n</ol>\r\n<p><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Should the cleanup function output message&#160;\"&lt;Object Type&gt;&lt;Object Name&gt; not deleted. Requires manual deletion.\" for any found objects, open a support incident for component SV-SMG-MON-BPM-ANA for assistance in manual cleanup of package /SDF/STPI_7X.</span></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I313371)"}, {"Key": "Processor                                                                                           ", "Value": "I520733"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003205320/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003205320/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003205320/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003205320/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003205320/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003205320/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003205320/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003205320/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003205320/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ZNOTE_2745851_REPAIR.txt", "FileSize": "17", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001750752022&iv_version=0004&iv_guid=00109B36D5CA1EDCB7827345F9BF0900"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3221283", "RefComponent": "SV-SCS-S4R", "RefTitle": "Dumps or Warnings/Errors/Inactive objects when implementing Note 2758146", "RefUrl": "/notes/3221283 "}, {"RefNumber": "2977422", "RefComponent": "SV-SCS-S4R", "RefTitle": "Process Discovery, SPIDE & SAP Pathfinder report - troubleshooting guide", "RefUrl": "/notes/2977422 "}, {"RefNumber": "2968380", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check Report 2.0 - troubleshooting guide", "RefUrl": "/notes/2968380 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}