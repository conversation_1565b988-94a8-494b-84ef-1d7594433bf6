{"Request": {"Number": "2265103", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 740, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018239072017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002265103?language=E&token=DE3A5E4A3CC621D5F555BCEF86BABEFA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002265103", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002265103/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2265103"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.05.2019"}, "SAPComponentKey": {"_label": "Component", "value": "HAN-DB"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP HANA Database"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP HANA", "value": "HAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA Database", "value": "HAN-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2265103 - Known issues detected in SAP HANA 1 SPS11"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Known issues for SAP HANA 1 SPS11</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Issues, Workarounds, Fixes, Notes</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note provides an overview of issues known in SAP HANA 1 SPS11.</p>\r\n<p>The list can be found in the section \"This document is referenced by\".</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D059764)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I035208)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002265103/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002265103/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002265103/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002265103/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002265103/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002265103/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002265103/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002265103/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002265103/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3438984", "RefComponent": "HAN-CLS-HC", "RefTitle": "Unexpected Results When Accessing INDEX of Type HASH of Docstore Collection", "RefUrl": "/notes/3438984 "}, {"RefNumber": "3435373", "RefComponent": "HAN-DB", "RefTitle": "Alert 21 Flooding \"Too many jobs collecting disk info\" and \"Disk info job stuck for very long time\"", "RefUrl": "/notes/3435373 "}, {"RefNumber": "3435077", "RefComponent": "HAN-DB", "RefTitle": "Nameserver Hangs or Experience Unexpected Shutdown due to High Load on Nameserver", "RefUrl": "/notes/3435077 "}, {"RefNumber": "3434285", "RefComponent": "HAN-DB", "RefTitle": "Nameserver Unresponsive due to Failing Disk Polling Caused by Faulty NFS Mount", "RefUrl": "/notes/3434285 "}, {"RefNumber": "3430125", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Query::compile_parse_tree", "RefUrl": "/notes/3430125 "}, {"RefNumber": "3191224", "RefComponent": "HAN-CLS-HC", "RefTitle": "Understand the reason for SQL statements terminating with an \"Allocation failed\" error message in SAP HANA Cloud", "RefUrl": "/notes/3191224 "}, {"RefNumber": "3417100", "RefComponent": "HAN-DB", "RefTitle": "Backup or Recovery via Backint is slow", "RefUrl": "/notes/3417100 "}, {"RefNumber": "3415906", "RefComponent": "HAN-DB", "RefTitle": "Could Not Query From M_SERVICE_MEMORY_", "RefUrl": "/notes/3415906 "}, {"RefNumber": "3408381", "RefComponent": "HAN-DB", "RefTitle": "unexpected result when using count(1) and where filter for calcview", "RefUrl": "/notes/3408381 "}, {"RefNumber": "3400434", "RefComponent": "HAN-DB-SDA", "RefTitle": "open", "RefUrl": "/notes/3400434 "}, {"RefNumber": "3393877", "RefComponent": "HAN-DB-SEC", "RefTitle": "<open>", "RefUrl": "/notes/3393877 "}, {"RefNumber": "3376933", "RefComponent": "HAN-DB", "RefTitle": "HANA Savepoint blocked by method call UnifiedTable::PagedInvIndexStorage::prepare", "RefUrl": "/notes/3376933 "}, {"RefNumber": "2964111", "RefComponent": "HAN-DB", "RefTitle": "Scriptserver Crash at MemoryManager::SmallBlockAllocator::delayedDeallocate", "RefUrl": "/notes/2964111 "}, {"RefNumber": "3362580", "RefComponent": "HAN-DB", "RefTitle": "Direct Connection to Scriptserver instead of Indexserver", "RefUrl": "/notes/3362580 "}, {"RefNumber": "3362042", "RefComponent": "HAN-DB", "RefTitle": "Long Running Parallel G<PERSON><PERSON><PERSON> Threads Executing Method 'TransactionManager::NestedSubtransactionUndo::cleanup'", "RefUrl": "/notes/3362042 "}, {"RefNumber": "3362024", "RefComponent": "HAN-DB-PER", "RefTitle": "GCJob Threads are Blocking Data Load during HANA Start-up", "RefUrl": "/notes/3362024 "}, {"RefNumber": "3358425", "RefComponent": "HAN-DB-HA", "RefTitle": "Indexserver on the primary site of a HANA System Replication environment crashes at DataRecovery::LogSegmentFactory::enqueueFreeSegment", "RefUrl": "/notes/3358425 "}, {"RefNumber": "3356697", "RefComponent": "HAN-DB", "RefTitle": "BW Query Fails with <PERSON>rror \"could not get attribute info for *\"", "RefUrl": "/notes/3356697 "}, {"RefNumber": "3349477", "RefComponent": "HAN-DB", "RefTitle": "Longer Execution Time and Higher Memory Consumption Caused by Bad Execution Plan on HANA2.0 Rev070", "RefUrl": "/notes/3349477 "}, {"RefNumber": "3349254", "RefComponent": "HAN-DB", "RefTitle": "Currency / Unit Conversion on CDS View Failing", "RefUrl": "/notes/3349254 "}, {"RefNumber": "3349119", "RefComponent": "HAN-DB", "RefTitle": "Currency/Unit Conversion Error on HANA2.0 Rev070", "RefUrl": "/notes/3349119 "}, {"RefNumber": "3344144", "RefComponent": "HAN-DB-HA", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/3344144 "}, {"RefNumber": "3343457", "RefComponent": "HAN-DB", "RefTitle": "Column Store Table with BEFORE INSERT trigger accepts NULL value for non-nullable columns upon insert", "RefUrl": "/notes/3343457 "}, {"RefNumber": "3341301", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Crash at Hierarchies::DataSourceDescriptionFactory::dataSourceCommonsFromJson", "RefUrl": "/notes/3341301 "}, {"RefNumber": "3339554", "RefComponent": "HAN-DB-BAC", "RefTitle": "Point-in-time Recovery fails with Error: \"RECOVER DATA finished with error: [448] recovery could not be completed, [1000000] Invalid volumeId\"", "RefUrl": "/notes/3339554 "}, {"RefNumber": "3333831", "RefComponent": "HAN-DB", "RefTitle": "Remote Data Source Queries With CAST or SUBSTRING Predicates and Enabled ABAPVARCHARMODE Show Suboptimal Performance", "RefUrl": "/notes/3333831 "}, {"RefNumber": "3322793", "RefComponent": "HAN-DB-SEC", "RefTitle": "Password Lifetime is not Effective for HANA SYSTEM User Even After Being Enabled", "RefUrl": "/notes/3322793 "}, {"RefNumber": "3326085", "RefComponent": "HAN-DB", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/3326085 "}, {"RefNumber": "3324142", "RefComponent": "HAN-DB-ENG", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/3324142 "}, {"RefNumber": "2866563", "RefComponent": "HAN-DB", "RefTitle": "Memory Leak Caused by Mishandling of Temporary Index Used by Calculation Operation After Query Cancellation", "RefUrl": "/notes/2866563 "}, {"RefNumber": "3313432", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes at Synchronization::Mutex::lockInternal", "RefUrl": "/notes/3313432 "}, {"RefNumber": "3313441", "RefComponent": "HAN-DB", "RefTitle": "Index of a Global Temporary Table is Not Imported", "RefUrl": "/notes/3313441 "}, {"RefNumber": "3310877", "RefComponent": "HAN-DB", "RefTitle": "Number of Session Variables Exceeds the Limit and Does Not Decrease", "RefUrl": "/notes/3310877 "}, {"RefNumber": "3303499", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::qo::PhysicalOperator::cloneSubtree With \"STACK OVERFLOW: SIGNAL 11 (SIGSEGV) caught\"", "RefUrl": "/notes/3303499 "}, {"RefNumber": "3304554", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::PlanInfoTemplate() with an Error \"STACK OVERFLOW: SIGNAL 11 (SIGSEGV) caught\"", "RefUrl": "/notes/3304554 "}, {"RefNumber": "3301386", "RefComponent": "HAN-DB-SCR", "RefTitle": "A Query Fails with an error \"fatal error: Unexpected null pointer detected in scalar subquery within table function call\"", "RefUrl": "/notes/3301386 "}, {"RefNumber": "3301379", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Crash in Evaluator::ThreeCode::run", "RefUrl": "/notes/3301379 "}, {"RefNumber": "3298176", "RefComponent": "HAN-DB", "RefTitle": "<schema>:<table>~<partition id>: Unexpected return type: 83, function ...", "RefUrl": "/notes/3298176 "}, {"RefNumber": "3297388", "RefComponent": "HAN-DB", "RefTitle": "open", "RefUrl": "/notes/3297388 "}, {"RefNumber": "3290204", "RefComponent": "HAN-DB", "RefTitle": "Table replication is disabled", "RefUrl": "/notes/3290204 "}, {"RefNumber": "3286088", "RefComponent": "HAN-DB", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/3286088 "}, {"RefNumber": "3283808", "RefComponent": "HAN-DB-ENG", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/3283808 "}, {"RefNumber": "3276890", "RefComponent": "HAN-DB", "RefTitle": "Table Consistency Check Returns an Error \"Inconsistency found: Ptime metadata mismatch with UTMetadata Ptime metadata: <table> UTmetadata: <table>\"", "RefUrl": "/notes/3276890 "}, {"RefNumber": "3273181", "RefComponent": "HAN-DB", "RefTitle": "A Language Fails to be set to Session Variable \"LOCALE\" and \"SAP_LOCALE\" on HANA Database", "RefUrl": "/notes/3273181 "}, {"RefNumber": "3270609", "RefComponent": "HAN-DB", "RefTitle": "Used Memory not Consistent across the Views SYS.M_LOAD_HISTORY_SERVICE and SYS.M_SERVICE_MEMORY", "RefUrl": "/notes/3270609 "}, {"RefNumber": "3268522", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crash at ptime::ExternalConnection::ExternalConnection with \"Cannot delete Block / maybe double delete\"", "RefUrl": "/notes/3268522 "}, {"RefNumber": "3261692", "RefComponent": "HAN-DB", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/3261692 "}, {"RefNumber": "3259457", "RefComponent": "HAN-DB", "RefTitle": "<302867> - indexserver crashed many times at sqlopt::Func::toStringHead during CS-QO expression conversion for Calc Views", "RefUrl": "/notes/3259457 "}, {"RefNumber": "2915064", "RefComponent": "HAN-DB", "RefTitle": "Adding a New GENERATED ALWAYS AS Calculated Column Fails due to Already Deleted Records", "RefUrl": "/notes/2915064 "}, {"RefNumber": "3255083", "RefComponent": "HAN-DB-ENG-MDS", "RefTitle": "Indexserver Crash at MemoryManager::SmallBlockAllocator::delayedDeallocate With Exception \"Cannot delete Block\"", "RefUrl": "/notes/3255083 "}, {"RefNumber": "3254869", "RefComponent": "HAN-DB-SDA", "RefTitle": "Accessing a Smart Data Access Remote Source Sporadically Fails with Error \"Cannot reconnect to <source> in current state : cursors/queries active in other threads\"", "RefUrl": "/notes/3254869 "}, {"RefNumber": "3254370", "RefComponent": "HAN-DB", "RefTitle": "Importing Catalog Object Fails With Error 'SAP DBTech JDBC: [257]: sql syntax error: incorrect syntax near \"ACTIVATE_TREX_CACHE\"'", "RefUrl": "/notes/3254370 "}, {"RefNumber": "3251660", "RefComponent": "HAN-DB", "RefTitle": "Suboptimal Performance of SQL Command \"ALTER TABLE ADD COLUMN [...]\"", "RefUrl": "/notes/3251660 "}, {"RefNumber": "3249156", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver crash at Metadata::StorageWrapperImpl::getLatestVersion", "RefUrl": "/notes/3249156 "}, {"RefNumber": "3247034", "RefComponent": "HAN-DB", "RefTitle": "Transaction Rollback Reporting Enhanced for DDL AUTOCOMMIT OFF Transactions", "RefUrl": "/notes/3247034 "}, {"RefNumber": "3244460", "RefComponent": "HAN-DB", "RefTitle": "Language and HANA Text Analysis", "RefUrl": "/notes/3244460 "}, {"RefNumber": "3229655", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at expr::types::qe_trex_utils::setColumnValue", "RefUrl": "/notes/3229655 "}, {"RefNumber": "3242466", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ltt::string_base()", "RefUrl": "/notes/3242466 "}, {"RefNumber": "3237108", "RefComponent": "HAN-DB-CLI", "RefTitle": "Indexserver Crash at BcdIterator::fromBINdeclets", "RefUrl": "/notes/3237108 "}, {"RefNumber": "3232653", "RefComponent": "HAN-DB", "RefTitle": "Indexing Error \"5181: Language model file not found\" for Kazakh (\"뱋\") and Hindi (\"묩\") Language", "RefUrl": "/notes/3232653 "}, {"RefNumber": "3232114", "RefComponent": "HAN-DB-ENG", "RefTitle": "Calculation Views Failed to Activate/Deploy Due to: Inconsistent Calculation Model", "RefUrl": "/notes/3232114 "}, {"RefNumber": "3224886", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexAPI::TRexApiSearch::doSearch(TRexAPI::TRexApiSearchTableResult&)", "RefUrl": "/notes/3224886 "}, {"RefNumber": "3223614", "RefComponent": "HAN-DB", "RefTitle": "CALL CHECK_TABLE_CONSISTENCY fails with \"string is too long: input string is longer than the maximum length\"", "RefUrl": "/notes/3223614 "}, {"RefNumber": "3221279", "RefComponent": "HAN-DB", "RefTitle": "FDA Query Fails With Error \"ERROR [CODE-11] invalid state: oabapstream internal error: insufficient row segment space for the next row\"", "RefUrl": "/notes/3221279 "}, {"RefNumber": "3221220", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crash at \"AttributeEngine::Delta::tree_iterator<TrexTypes::StringAttributeValue>::stringToKey_limitsave\"", "RefUrl": "/notes/3221220 "}, {"RefNumber": "3220777", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::iabapstream::readString()", "RefUrl": "/notes/3220777 "}, {"RefNumber": "3220537", "RefComponent": "HAN-DB", "RefTitle": "Datashipping Does not Proceed (Bug 285528)", "RefUrl": "/notes/3220537 "}, {"RefNumber": "3217315", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in DataAccess::DisasterRecoveryCompressionChannel::streamClosed", "RefUrl": "/notes/3217315 "}, {"RefNumber": "3216709", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Execution::impl::JobPreallocatedSpaceAllocator::checkEmpty", "RefUrl": "/notes/3216709 "}, {"RefNumber": "3214214", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crash during log replay with exception \"AttributeEngine call failed on [<table>] because of error [2754] : [IndexMgr: illegal transition]\"", "RefUrl": "/notes/3214214 "}, {"RefNumber": "3212842", "RefComponent": "HAN-DB", "RefTitle": "<PERSON><PERSON><PERSON>lock Between Savepoint and Post Commit Handler", "RefUrl": "/notes/3212842 "}, {"RefNumber": "3206714", "RefComponent": "HAN-DB", "RefTitle": "<282072> - Early exit bug related to forbidden shrink in other thread", "RefUrl": "/notes/3206714 "}, {"RefNumber": "3197615", "RefComponent": "HAN-DB-MDX", "RefTitle": "Indexserver Crash at Hierarchies::HierarchyBlob::Hierarchy::getPeriodsToDat()", "RefUrl": "/notes/3197615 "}, {"RefNumber": "3196140", "RefComponent": "HAN-DB", "RefTitle": "HANA Dynamic Tiering Backup Uses Backint Parameter File From SystemDB Even Though Tenant DB is Configured with Separate Backint Parameter File", "RefUrl": "/notes/3196140 "}, {"RefNumber": "3194033", "RefComponent": "HAN-DB-SEC", "RefTitle": "Creating a SAML Identity Provider in the XS Admin Tool Fails With Error \"4236 - EntityID already exists\"", "RefUrl": "/notes/3194033 "}, {"RefNumber": "3185915", "RefComponent": "HAN-DB", "RefTitle": "Fail to Apply Workload Class Occasionally in ABAP Environment", "RefUrl": "/notes/3185915 "}, {"RefNumber": "3166743", "RefComponent": "HAN-DB", "RefTitle": "Activation with Zero Elimination Fails for Partitioned Tables", "RefUrl": "/notes/3166743 "}, {"RefNumber": "3156450", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash During Log Replay With an Exceprion \"Attribute load failed; $[1]$=BTreeAttribute: m_columnWriter handle invalid for table\"", "RefUrl": "/notes/3156450 "}, {"RefNumber": "3156075", "RefComponent": "HAN-DB", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/3156075 "}, {"RefNumber": "3154000", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at sqlscript::exe::ScalarUDFResources::~ScalarUDFResources With Exception \"Trying to lock a destroyed mutex\"", "RefUrl": "/notes/3154000 "}, {"RefNumber": "3149165", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With Exception \"Terminate was Called\" when a Query Execution is Closed Abnormally", "RefUrl": "/notes/3149165 "}, {"RefNumber": "3150007", "RefComponent": "HAN-DB-ENG", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/3150007 "}, {"RefNumber": "3105417", "RefComponent": "HAN-DB-SDA", "RefTitle": "A Query Referencing a Remote Table Returns Inconsistent Results", "RefUrl": "/notes/3105417 "}, {"RefNumber": "3147465", "RefComponent": "HAN-DB-PER", "RefTitle": "Schedule LOB Garbage Collection Periodically at Fixed Times", "RefUrl": "/notes/3147465 "}, {"RefNumber": "3142505", "RefComponent": "HAN-DB-PER", "RefTitle": "Limit the Maximum Size Used by HANA Log Segments", "RefUrl": "/notes/3142505 "}, {"RefNumber": "3136802", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexCommonObjects::DataIndexContainer::checkLifetime", "RefUrl": "/notes/3136802 "}, {"RefNumber": "3068154", "RefComponent": "HAN-DB", "RefTitle": "[template:STACKOVERFLOW] Indexserver Crash Due to STACK OVERFLOW on/at xxxx", "RefUrl": "/notes/3068154 "}, {"RefNumber": "3133049", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at BxmlReader::destroy or FxHashTableDestroy After Query Cancellation", "RefUrl": "/notes/3133049 "}, {"RefNumber": "3131151", "RefComponent": "HAN-WDE-DBX", "RefTitle": "HANA Database Explorer can Block Garbage Collection", "RefUrl": "/notes/3131151 "}, {"RefNumber": "3127221", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Metadata::Referential::checkPersistString at Consistency Check", "RefUrl": "/notes/3127221 "}, {"RefNumber": "3126629", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With Exception \"Assertion failed: !hasResettedParentPointer()\"", "RefUrl": "/notes/3126629 "}, {"RefNumber": "3119071", "RefComponent": "HAN-DB-ENG", "RefTitle": "Calculation View With Input Parameter NOW() Returns the Same Time Instead of Current Timestamp", "RefUrl": "/notes/3119071 "}, {"RefNumber": "3113866", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crash at TransactionManager::TransactionControlBlock::assertActiveFailed - when using explicit PARALLEL EXECUTION", "RefUrl": "/notes/3113866 "}, {"RefNumber": "3112773", "RefComponent": "HAN-DB", "RefTitle": "Recovery of SYSTEM DB Does Not Progress", "RefUrl": "/notes/3112773 "}, {"RefNumber": "3110555", "RefComponent": "HAN-DB", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/3110555 "}, {"RefNumber": "3110413", "RefComponent": "HAN-DB", "RefTitle": "Length Calculation of UTF-8 Strings Changed in SAP HANA 2 Server With Python 3", "RefUrl": "/notes/3110413 "}, {"RefNumber": "3107983", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::ExternalStatement::checkExternalStatementResultShipping()", "RefUrl": "/notes/3107983 "}, {"RefNumber": "3103576", "RefComponent": "HAN-DB", "RefTitle": "SQL IMPORT FROM Statement Does not Block an Empty String for a Delimiter", "RefUrl": "/notes/3103576 "}, {"RefNumber": "3085799", "RefComponent": "HAN-DB", "RefTitle": "A Temporary Table with Inconsistent Metadata is Created due to Missing Check for Invalid CREATE TABLE Statement", "RefUrl": "/notes/3085799 "}, {"RefNumber": "3091361", "RefComponent": "HAN-DB", "RefTitle": "A Query Fails With an Error \"[2981] Failed to insert requested attribute \"<attribute_name>:<n>\" into plan: Lob type not support\"", "RefUrl": "/notes/3091361 "}, {"RefNumber": "3100574", "RefComponent": "HAN-DB", "RefTitle": "An Error Message \"Configuration parameter startup_error_shutdown_instance set to unsupported value\" is Detected", "RefUrl": "/notes/3100574 "}, {"RefNumber": "3090975", "RefComponent": "HAN-DB-ENG-TXT", "RefTitle": "Preprocessor Crash at SkBitmapDevice::imageInfo During Text Analysis", "RefUrl": "/notes/3090975 "}, {"RefNumber": "3085861", "RefComponent": "HAN-DB-SEC", "RefTitle": "Nameserver Crash During Recovery at Licensing::AbstractLicenseManager::renameOnPermanentLicense", "RefUrl": "/notes/3085861 "}, {"RefNumber": "3089358", "RefComponent": "HAN-DB", "RefTitle": "Long Startup After Unclean Shutdown When Replaying Redo Logs on Table With Generated Always Column And Timezone Conversion", "RefUrl": "/notes/3089358 "}, {"RefNumber": "3087512", "RefComponent": "HAN-DB", "RefTitle": "HANA Service is at Standstill due to Deadlock Between Savepoint and Transaction Rollback", "RefUrl": "/notes/3087512 "}, {"RefNumber": "3034915", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Empty Result When Using Order-dependent Aggregate Functions", "RefUrl": "/notes/3034915 "}, {"RefNumber": "3081578", "RefComponent": "HAN-DB-SDA", "RefTitle": "HANA Service Crash When Using ODBC Driver From  SAP HANA SMART DATA ACCESS Package And Encrypted Connections", "RefUrl": "/notes/3081578 "}, {"RefNumber": "3080996", "RefComponent": "HAN-DB", "RefTitle": "Accessing Monitoring View M_CS_MVCC Terminates with \"A received argument has an invalid value\" due to Inconsistencies in Topology", "RefUrl": "/notes/3080996 "}, {"RefNumber": "3079962", "RefComponent": "HAN-DB", "RefTitle": "XSEngine Crash With Exception 'Duplicate key when registering'", "RefUrl": "/notes/3079962 "}, {"RefNumber": "3068931", "RefComponent": "HAN-DB", "RefTitle": "FDA READ Queries Fail With an Error \"SQL message: invalid state: invalid abapstream state\" After Site Takeover", "RefUrl": "/notes/3068931 "}, {"RefNumber": "3071891", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash Due to STACK OVERFLOW on expr::ExpressionTree::Node::clean", "RefUrl": "/notes/3071891 "}, {"RefNumber": "3077271", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With an Exception \"ltt::overflow_error::overflow_error\"", "RefUrl": "/notes/3077271 "}, {"RefNumber": "3076455", "RefComponent": "HAN-DB-SCR", "RefTitle": "Indexserver Crash at __parameter__Daydate When a Table User-Defined Function is Called", "RefUrl": "/notes/3076455 "}, {"RefNumber": "3075482", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::SessionCodecNewdb::dAbapStreamWrite During liveCache Procedure Call", "RefUrl": "/notes/3075482 "}, {"RefNumber": "3075493", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::LVCProcedure::execute During a liveCache Procedure Call", "RefUrl": "/notes/3075493 "}, {"RefNumber": "3067455", "RefComponent": "HAN-DB", "RefTitle": "Indexserver or Scriptserver Crash at ltt::tree_node_base* ltt::bin_tree During Session Cancellation", "RefUrl": "/notes/3067455 "}, {"RefNumber": "3073266", "RefComponent": "HAN-DB-SDA", "RefTitle": "[SDA] SUBSTRING (or SUBSTR) Behaves Differently Between HANA and Other Databases", "RefUrl": "/notes/3073266 "}, {"RefNumber": "3072433", "RefComponent": "HAN-DB", "RefTitle": "Stub note for unlikely case any customer runs into an error referencing this note", "RefUrl": "/notes/3072433 "}, {"RefNumber": "3071935", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Cancel Session during addInnerXMLPlan()﻿", "RefUrl": "/notes/3071935 "}, {"RefNumber": "3070591", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at qo3::CompareTermNodesByProperty::operator()", "RefUrl": "/notes/3070591 "}, {"RefNumber": "3068687", "RefComponent": "HAN-DB", "RefTitle": "hdblcm aborts with \"There are no SAP HANA Platform components that could be installed or updated\"", "RefUrl": "/notes/3068687 "}, {"RefNumber": "3065633", "RefComponent": "HAN-DB", "RefTitle": "A View That is Dependent on a Calculation View Becomes Invalidated When a Dependency of Calculation View is Changed", "RefUrl": "/notes/3065633 "}, {"RefNumber": "3065382", "RefComponent": "HAN-DB", "RefTitle": "[template : due to incorrect exception handling] Indexserver Crash at xxx During xxx", "RefUrl": "/notes/3065382 "}, {"RefNumber": "3063101", "RefComponent": "HAN-DB", "RefTitle": "Indexserver or XS Engine Crash at jsvm::CompileBoxImpl::CompileBoxImpl", "RefUrl": "/notes/3063101 "}, {"RefNumber": "3062171", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at __dynamic_cast When SubmitThread Accesses Its Statistics", "RefUrl": "/notes/3062171 "}, {"RefNumber": "3057810", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in PleStringColumnDict::fillFromColumnFast Due to FOX", "RefUrl": "/notes/3057810 "}, {"RefNumber": "3057067", "RefComponent": "HAN-DB", "RefTitle": "HANA System Replication takeover unexpectedly deletes running services on the secondary system", "RefUrl": "/notes/3057067 "}, {"RefNumber": "3053336", "RefComponent": "HAN-DB-SEC", "RefTitle": "Indexserver Crash at Authentication::Error::trace During SPNEGO Autentication With Exception \"terminate was called\"", "RefUrl": "/notes/3053336 "}, {"RefNumber": "3048734", "RefComponent": "HAN-DB", "RefTitle": "HCMT Tool Prints \"Resource temporarily unavailable\" in Scale Out System with Different File System for Each Node", "RefUrl": "/notes/3048734 "}, {"RefNumber": "3048728", "RefComponent": "HAN-DB", "RefTitle": "Memory leak issue on Pool/RowEngine/GlobalHeap due to expr::types::to_snumc_for_fixed", "RefUrl": "/notes/3048728 "}, {"RefNumber": "3018767", "RefComponent": "HAN-DB", "RefTitle": "Calculation View Fails With \"[339]: invalid number: search table error: [6930] attribute value is not a number\"", "RefUrl": "/notes/3018767 "}, {"RefNumber": "3045718", "RefComponent": "HAN-DB", "RefTitle": "Precision And Scale Undefined When Mathematical Operator is Applied on BIGINT", "RefUrl": "/notes/3045718 "}, {"RefNumber": "3043589", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in mds::utils::ValueReadOnly::formatTimestamp", "RefUrl": "/notes/3043589 "}, {"RefNumber": "3043345", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in ltt::basic_filebuf<char, ltt::char_traits<char>>::underflow()", "RefUrl": "/notes/3043345 "}, {"RefNumber": "3042310", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexUtils::BucketIndexHashMap", "RefUrl": "/notes/3042310 "}, {"RefNumber": "3040552", "RefComponent": "HAN-DB", "RefTitle": "Execution Error \"too many parameters are set\"", "RefUrl": "/notes/3040552 "}, {"RefNumber": "3032765", "RefComponent": "HAN-DB", "RefTitle": "Logs For Automatic Rowstore Reorganization Are Not Captured in Database Trace", "RefUrl": "/notes/3032765 "}, {"RefNumber": "3032369", "RefComponent": "HAN-DB", "RefTitle": "Many to exact one cardinality joins - Unexpected results - Unexpected join pruning", "RefUrl": "/notes/3032369 "}, {"RefNumber": "3032221", "RefComponent": "HAN-DB", "RefTitle": "A Suboptimal Host Is Selected for Statement Routing With Replica Table Involved", "RefUrl": "/notes/3032221 "}, {"RefNumber": "3028170", "RefComponent": "HAN-DB", "RefTitle": "HANA System replication: Startup of nameserver error while resolving database id <n> to stat", "RefUrl": "/notes/3028170 "}, {"RefNumber": "3027852", "RefComponent": "HAN-DB", "RefTitle": "Join Query Fail With Error \"Can't create plan for join query\"", "RefUrl": "/notes/3027852 "}, {"RefNumber": "3024498", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash Due to STACK OVERFLOW at JoinEvaluator::PlanCreator::doEstimation", "RefUrl": "/notes/3024498 "}, {"RefNumber": "3024512", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ImportExport::DBConn::prepare_insert When Importing Data From a CSV File", "RefUrl": "/notes/3024512 "}, {"RefNumber": "3023222", "RefComponent": "HAN-DB", "RefTitle": "Increasing MVCC Version Count but Unable to Cancel the Blocking Transaction/Connection", "RefUrl": "/notes/3023222 "}, {"RefNumber": "3020877", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Transaction::partial_abort_(bool) with \"Read only statement tried index rollback;\"", "RefUrl": "/notes/3020877 "}, {"RefNumber": "3018456", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Backup::BackupMgrClient_BackupListRequest::BackupMgrClient_BackupListRequest", "RefUrl": "/notes/3018456 "}, {"RefNumber": "3018310", "RefComponent": "HAN-DB", "RefTitle": "indexserver Crash at ptime::Qc2QoConverter::materialize_expression", "RefUrl": "/notes/3018310 "}, {"RefNumber": "3011616", "RefComponent": "HAN-DB", "RefTitle": "Model Inconsistency Is Caused by Incorrect Handling of ROOT_NODE_VISIBILITY for Leveled Hierarchies", "RefUrl": "/notes/3011616 "}, {"RefNumber": "2940517", "RefComponent": "HAN-DB", "RefTitle": "MDCDispatcher Crash at backup directory with permission denied", "RefUrl": "/notes/2940517 "}, {"RefNumber": "3010808", "RefComponent": "HAN-DB", "RefTitle": "Note used to described JobEx Emergency Procedure", "RefUrl": "/notes/3010808 "}, {"RefNumber": "3008300", "RefComponent": "HAN-DB-HA", "RefTitle": "HANA Service Crash at DataAccess::LogBufferHandlerImpl::ReplayLogCache::getCachedBufferWriteCallBack on Secondary Site of HANA System Replication", "RefUrl": "/notes/3008300 "}, {"RefNumber": "3003665", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in OMS_Context::GetVarObjectFromKernel", "RefUrl": "/notes/3003665 "}, {"RefNumber": "3003512", "RefComponent": "HAN-DB", "RefTitle": "Input Parameters of Type Derived From Table Are Prompted Even Though They Are Not Input Enabled", "RefUrl": "/notes/3003512 "}, {"RefNumber": "3002929", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::ShmInfo::dump When Generating Runtime Dump", "RefUrl": "/notes/3002929 "}, {"RefNumber": "2924587", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Hierarchies::HierarchySetExpression::toPrettyString When Executing MDX Query", "RefUrl": "/notes/2924587 "}, {"RefNumber": "3001687", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::RowStoreReplayMgr::closeTransaction After Getting an Exception", "RefUrl": "/notes/3001687 "}, {"RefNumber": "3000839", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Value may be Inserted Into a Decimal Column When Using a Cursor in SAP HANA 2 SPS03", "RefUrl": "/notes/3000839 "}, {"RefNumber": "3000614", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Proc<PERSON>hecker::is_undetermined_any_table_out_param When Creating a Procedure", "RefUrl": "/notes/3000614 "}, {"RefNumber": "2996070", "RefComponent": "HAN-AS-XS", "RefTitle": "XSEngine or Indexserver Crash at js::AutoEnterOOMUnsafeRegion::crash", "RefUrl": "/notes/2996070 "}, {"RefNumber": "2992118", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at \"TRexAPI::TableUpdate::execute\" With \"Assertion failed: getLastError().getCode() != 0\"", "RefUrl": "/notes/2992118 "}, {"RefNumber": "2987467", "RefComponent": "HAN-DB", "RefTitle": "XSEngine Crash at IctIHttpAddHeaderField After OOM", "RefUrl": "/notes/2987467 "}, {"RefNumber": "2985023", "RefComponent": "HAN-DB", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/2985023 "}, {"RefNumber": "2983856", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash During Binary Import", "RefUrl": "/notes/2983856 "}, {"RefNumber": "2964070", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at DataAccess::PersistenceManagerSPI::getHistoryManager()", "RefUrl": "/notes/2964070 "}, {"RefNumber": "2982247", "RefComponent": "HAN-DB", "RefTitle": "Memory Leak in Pool/RowEngine/QueryExecution/SearchAlloc", "RefUrl": "/notes/2982247 "}, {"RefNumber": "2982111", "RefComponent": "HAN-DB", "RefTitle": "ceUnionPop: Failed to append column UNION_COL<n>; current column type = NullColumn<seconddate> other column type =VectorColumn<longdate>", "RefUrl": "/notes/2982111 "}, {"RefNumber": "2981396", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::CopySplitsJob After an OOM, Cancel Request or Other Exception", "RefUrl": "/notes/2981396 "}, {"RefNumber": "2980992", "RefComponent": "HAN-DB-HA", "RefTitle": "Target Site of HANA SAP System Replication Environment Crashes with Exception \"Allowed rowcount exceeded\"", "RefUrl": "/notes/2980992 "}, {"RefNumber": "2971782", "RefComponent": "HAN-DB", "RefTitle": "SSL Error in BIO_write during OOM", "RefUrl": "/notes/2971782 "}, {"RefNumber": "2974593", "RefComponent": "HAN-DB", "RefTitle": "XS Engine Crash at JS_EncodeStringToUTF8", "RefUrl": "/notes/2974593 "}, {"RefNumber": "2973560", "RefComponent": "HAN-DB-PER", "RefTitle": "Data Volume Reclaim Finishes Early", "RefUrl": "/notes/2973560 "}, {"RefNumber": "2969651", "RefComponent": "HAN-DB-HA", "RefTitle": "Indexserver Crash During Logreplay on Systems With Memory Shortage", "RefUrl": "/notes/2969651 "}, {"RefNumber": "2967900", "RefComponent": "HAN-DB", "RefTitle": "HANA Alert ID 48: \"Long-running uncommitted write transaction\" either False or Duration of the Uncommited Transaction is Inaccurate", "RefUrl": "/notes/2967900 "}, {"RefNumber": "2965927", "RefComponent": "HAN-DB", "RefTitle": "Python Script loadAllTables.py Does not Load all Column Tables Into the Main Memory", "RefUrl": "/notes/2965927 "}, {"RefNumber": "2965456", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes at TransTokenConnector::deserialize_subtransaction_data", "RefUrl": "/notes/2965456 "}, {"RefNumber": "2962600", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in UnifiedTable::MVCCObject::allocateAndReComputeRowState", "RefUrl": "/notes/2962600 "}, {"RefNumber": "2962952", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Evaluator::ExpressionMemory::~ExpressionMemory", "RefUrl": "/notes/2962952 "}, {"RefNumber": "2962334", "RefComponent": "HAN-DB", "RefTitle": "Let Consistency Check Unload Column Store Tables Which Were not Loaded Before", "RefUrl": "/notes/2962334 "}, {"RefNumber": "2962057", "RefComponent": "HAN-DP-SDI", "RefTitle": "Indexserver Crash due to Infinite Loop of xmlGetGlobalState__internal_alias() and __xmlGenericError__internal_alias()", "RefUrl": "/notes/2962057 "}, {"RefNumber": "2959890", "RefComponent": "HAN-DB", "RefTitle": "Limitation: Indexserver Crash Due to STACK OVERFLOW at ptime::qo_KeyCollector::compose_compact_key", "RefUrl": "/notes/2959890 "}, {"RefNumber": "2957768", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::BTreeLeafNodeGroupNode::getEntryIndexF8", "RefUrl": "/notes/2957768 "}, {"RefNumber": "2957405", "RefComponent": "HAN-DB", "RefTitle": "Changed Behavior of Converting Numeric Types to DECIMAL/SMALLDECIMAL Types", "RefUrl": "/notes/2957405 "}, {"RefNumber": "2956553", "RefComponent": "HAN-DB", "RefTitle": "UPDATE on LOB column can cause OOM and Crash", "RefUrl": "/notes/2956553 "}, {"RefNumber": "2956477", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes When an Update Distributed Transaction is Executed.", "RefUrl": "/notes/2956477 "}, {"RefNumber": "2954874", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crashes at QueryMediator::PtimeInfo::getQoTable", "RefUrl": "/notes/2954874 "}, {"RefNumber": "2954310", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in FileAccess::LocalFileCompletionThread::run Upon Memory Shortage", "RefUrl": "/notes/2954310 "}, {"RefNumber": "2954027", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexAPI::CS_Statistics::getColumnViewHistogramForJE", "RefUrl": "/notes/2954027 "}, {"RefNumber": "2953186", "RefComponent": "HAN-DB", "RefTitle": "Optimizing Memory Garbage Collection Performance", "RefUrl": "/notes/2953186 "}, {"RefNumber": "2952156", "RefComponent": "HAN-DB", "RefTitle": "XS Engine Crash in js::ReportOutOfMemory", "RefUrl": "/notes/2952156 "}, {"RefNumber": "2951602", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at OMS_KernelVersionMergeKeyIter::OMS_KernelVersionMergeKeyIter", "RefUrl": "/notes/2951602 "}, {"RefNumber": "2923890", "RefComponent": "HAN-DB", "RefTitle": "HANA Processes Startup Fails with Invalid TypedEnumeration <AsyncWriteSubmitActive> when Setting a Wrong Value for Param async_write_submit_blocks", "RefUrl": "/notes/2923890 "}, {"RefNumber": "2950691", "RefComponent": "HAN-DB", "RefTitle": "Table has old delta log entries and requires a migration / Cannot load table <colum store tablename> in online transaction due to too old persistence format from before SP9", "RefUrl": "/notes/2950691 "}, {"RefNumber": "2949144", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at federation::FederationTxnCallback::execute After Cancellation of a Query Using Smart Data Access", "RefUrl": "/notes/2949144 "}, {"RefNumber": "2946863", "RefComponent": "HAN-DB", "RefTitle": "Missing Values in Key Columns After Activating BW DSO Data Requests", "RefUrl": "/notes/2946863 "}, {"RefNumber": "2945651", "RefComponent": "HAN-DB", "RefTitle": "SYNONYMS.IS_VALID Does not Reflect The Validity of Referenced Object", "RefUrl": "/notes/2945651 "}, {"RefNumber": "2944915", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crash at ptime::QueryOptimizer::_computeRoutingInfo() When Using an Invalid Parameter Format for a ROUTE_BY Hint", "RefUrl": "/notes/2944915 "}, {"RefNumber": "2940358", "RefComponent": "HAN-DB", "RefTitle": "HANA Service Crash in TransTokenThreadContainer::popToken During the Transaction Token Cleanup", "RefUrl": "/notes/2940358 "}, {"RefNumber": "2939376", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Execution::JobObjectImpl::wait With \"Wrong Waiter State\"", "RefUrl": "/notes/2939376 "}, {"RefNumber": "2938765", "RefComponent": "HAN-DB", "RefTitle": "Hanging Threads on BW4HANA System and Cancellation not Working", "RefUrl": "/notes/2938765 "}, {"RefNumber": "2280391", "RefComponent": "HAN-DB", "RefTitle": "Join Query Fails with \"Can't create plan for join query\"", "RefUrl": "/notes/2280391 "}, {"RefNumber": "2937168", "RefComponent": "HAN-DB-MON", "RefTitle": "Monitoring View M_EXPENSIVE_STATEMENTS Shows an Inaccurate MEMORY_SIZE for Recorded Request", "RefUrl": "/notes/2937168 "}, {"RefNumber": "2937167", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in federation::FederationContext::GetLastException", "RefUrl": "/notes/2937167 "}, {"RefNumber": "2935456", "RefComponent": "HAN-DB", "RefTitle": "Docstore Crash at ltt::exception_node::expand During Log Backup Upon Memory Shortage", "RefUrl": "/notes/2935456 "}, {"RefNumber": "2933035", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexUtils::IndexHashMap<>::merge_no_resize", "RefUrl": "/notes/2933035 "}, {"RefNumber": "2929295", "RefComponent": "HAN-DB", "RefTitle": "Memory Leak Caused by Temporary Tables Not Being Removed After Query Cancellation", "RefUrl": "/notes/2929295 "}, {"RefNumber": "2932513", "RefComponent": "HAN-DB", "RefTitle": "Table Consistency Check Crashes While Checking an Inconsistent Rowstore Table", "RefUrl": "/notes/2932513 "}, {"RefNumber": "2922577", "RefComponent": "HAN-DB", "RefTitle": "Unexpected result returned from the query with Extract function", "RefUrl": "/notes/2922577 "}, {"RefNumber": "2931155", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at xsengine::RequestJobWrapper::getDetails", "RefUrl": "/notes/2931155 "}, {"RefNumber": "2914403", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash due to the Memory Overwriting With Stack \"dealloc at offset 480\"", "RefUrl": "/notes/2914403 "}, {"RefNumber": "2927343", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Transaction::getTransTerminateType", "RefUrl": "/notes/2927343 "}, {"RefNumber": "2925206", "RefComponent": "HAN-DB", "RefTitle": "Multi-Column Inverted Value Index Does Not Support Load Unit Change", "RefUrl": "/notes/2925206 "}, {"RefNumber": "2924560", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With Exception \"Assertion failed: oldRefCount != INVALID_PATTERN\"", "RefUrl": "/notes/2924560 "}, {"RefNumber": "2922337", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at sse_icc_lib::mgeti_AVX2 During Optimize Compression", "RefUrl": "/notes/2922337 "}, {"RefNumber": "2919931", "RefComponent": "HAN-DB", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Product and Development Support", "RefUrl": "/notes/2919931 "}, {"RefNumber": "2918131", "RefComponent": "HAN-DB", "RefTitle": "Adjusting Timezone on OS Level While HANA is Running", "RefUrl": "/notes/2918131 "}, {"RefNumber": "2917228", "RefComponent": "HAN-DB", "RefTitle": "Query Involving Guided Navigation Fails With \"Fatal Error: ColDicVal (1000010,3) Not Found\"", "RefUrl": "/notes/2917228 "}, {"RefNumber": "2916672", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at JoinEvaluator::mergeDictsAndDocs When Executing a TREXviaDBSL Call", "RefUrl": "/notes/2916672 "}, {"RefNumber": "2916611", "RefComponent": "HAN-DB", "RefTitle": "Old Statement Hints Are Kept Being Used After Being Removed", "RefUrl": "/notes/2916611 "}, {"RefNumber": "2916365", "RefComponent": "HAN-DB", "RefTitle": "Inappropriate value of \"M_CS_PARTITIONS\".\"SUBPARTITION\" Column for Single-Level Linear Partitioned Columnstore Tables", "RefUrl": "/notes/2916365 "}, {"RefNumber": "2915561", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in Join_hashed_disjunctive::do_open_iterator After an Exception", "RefUrl": "/notes/2915561 "}, {"RefNumber": "2912383", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in RowEngine::RowTableDelete::flushMemSlots", "RefUrl": "/notes/2912383 "}, {"RefNumber": "2909860", "RefComponent": "HAN-DB", "RefTitle": "SELECT FROM Table UDF Containing View Defined With WITH Clause Does Not Unfold", "RefUrl": "/notes/2909860 "}, {"RefNumber": "2908888", "RefComponent": "HAN-DB-MDX", "RefTitle": "Indexserver Crash at mdx::processor::AddCalculatedMemberHierAggrViews When Using CURRENTMEMBER Function in MDX Statement", "RefUrl": "/notes/2908888 "}, {"RefNumber": "2908770", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexAPI::TRexApiSearch::createInternalTable", "RefUrl": "/notes/2908770 "}, {"RefNumber": "2908517", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ltt::allocated_refcounted::destroyImp", "RefUrl": "/notes/2908517 "}, {"RefNumber": "2908470", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at SessionCodecNewdb::dFdaRequestMetadata", "RefUrl": "/notes/2908470 "}, {"RefNumber": "2907844", "RefComponent": "HAN-DB", "RefTitle": "Performance Improvement of Table Preload When Using Persistent Memory or Systems With Many NUMA Nodes", "RefUrl": "/notes/2907844 "}, {"RefNumber": "2907045", "RefComponent": "HAN-LM-PLT", "RefTitle": "HANA Installation With a Storage Connector Fails With \"SyntaxError: Missing parentheses in call to 'print'. Did you mean print(\"Usage:\")?\"", "RefUrl": "/notes/2907045 "}, {"RefNumber": "2904837", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at MemoryManager::MemoryPool::deallocate With Exception “Deallocating not-in-use memory block", "RefUrl": "/notes/2904837 "}, {"RefNumber": "2906598", "RefComponent": "HAN-DB", "RefTitle": "Parallel Execution of Scalar UDF Query and Delta Merge Causes Deadlock", "RefUrl": "/notes/2906598 "}, {"RefNumber": "2904593", "RefComponent": "BC-DB-LCA", "RefTitle": "Indexserver Crash at DataAccess::PersistenceSession When Recording lvcprocedure Debug Trace", "RefUrl": "/notes/2904593 "}, {"RefNumber": "2905632", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash When Plan Trace is Enabled", "RefUrl": "/notes/2905632 "}, {"RefNumber": "2904175", "RefComponent": "HAN-DB", "RefTitle": "Query Against CalculationView with Currency Conversion Fails with <PERSON><PERSON><PERSON> \"Failed to find column '<column name>' in itab\"", "RefUrl": "/notes/2904175 "}, {"RefNumber": "2904036", "RefComponent": "HAN-DB", "RefTitle": "Sporadic High Memory Consumption When Accessing Compatibility View /SAPAPO/MATLOC", "RefUrl": "/notes/2904036 "}, {"RefNumber": "2903906", "RefComponent": "HAN-CLS-DB", "RefTitle": "Use This as a Template for SAP HANA Service SAP Notes", "RefUrl": "/notes/2903906 "}, {"RefNumber": "2902020", "RefComponent": "HAN-DB-HA", "RefTitle": "Secondary Log Shipping Timeout Observed in delta_datashipping Operation Mode", "RefUrl": "/notes/2902020 "}, {"RefNumber": "2899942", "RefComponent": "HAN-DB", "RefTitle": "Memory Pressure by Rowstore Segments on Secondary System", "RefUrl": "/notes/2899942 "}, {"RefNumber": "2898351", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at getValuesWithFullLOBMaterializationForCrossDB", "RefUrl": "/notes/2898351 "}, {"RefNumber": "2896743", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crash Join_hashed_disjunctive", "RefUrl": "/notes/2896743 "}, {"RefNumber": "2894967", "RefComponent": "HAN-DB", "RefTitle": "HAN-DB-PERF. FAQ: troubleshooting HANA performance issues", "RefUrl": "/notes/2894967 "}, {"RefNumber": "2892201", "RefComponent": "HAN-DB", "RefTitle": "Startup Failures After Takeover Due to Stopped Tenants During Replication Initialization", "RefUrl": "/notes/2892201 "}, {"RefNumber": "2887773", "RefComponent": "HAN-DB", "RefTitle": "TrexNet error : read from channel failed; resetting buffer with 0 bytes. (connection broken)", "RefUrl": "/notes/2887773 "}, {"RefNumber": "2890958", "RefComponent": "HAN-DB", "RefTitle": "Query on Partitioned Replicated Table Fails With Column Store Error After Repartitioning", "RefUrl": "/notes/2890958 "}, {"RefNumber": "2887331", "RefComponent": "HAN-DB", "RefTitle": "The Values Larger Than the Declared Column Size of the Table can be Inserted through SQL Script.", "RefUrl": "/notes/2887331 "}, {"RefNumber": "2887549", "RefComponent": "HAN-DB-MON", "RefTitle": "Nameserver Crash due to Missed or Changed Permission of hdbmdcdispatcher or/and mdc Directory", "RefUrl": "/notes/2887549 "}, {"RefNumber": "2886632", "RefComponent": "HAN-DB", "RefTitle": "Query Fails During JE Plan Generation With 'Internal error during join: invalid input'", "RefUrl": "/notes/2886632 "}, {"RefNumber": "2884201", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexUtils::Parallel::Z<PERSON><PERSON>", "RefUrl": "/notes/2884201 "}, {"RefNumber": "2882979", "RefComponent": "HAN-DB-CLI", "RefTitle": "Commit Fails in Scale-Out Environment With Error 129 \"Transaction rolled back\"", "RefUrl": "/notes/2882979 "}, {"RefNumber": "2882994", "RefComponent": "HAN-DB-SDA", "RefTitle": "SAP HANA Unresponsive During Cancellation of SDA Remote Connection", "RefUrl": "/notes/2882994 "}, {"RefNumber": "2878319", "RefComponent": "HAN-DB", "RefTitle": "XSengine Crash at TrexNet::BufferedIO::getBytes()", "RefUrl": "/notes/2878319 "}, {"RefNumber": "2878279", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Crash in AttributeEngine::DeltaIter<...>::createTranslator Due to Memory Overwrite", "RefUrl": "/notes/2878279 "}, {"RefNumber": "2876895", "RefComponent": "HAN-DB", "RefTitle": "A <PERSON>als<PERSON> of Long Running Log Backups - <PERSON><PERSON>: ID 65 '<PERSON><PERSON> Runtime of the log backups currently running'", "RefUrl": "/notes/2876895 "}, {"RefNumber": "2876107", "RefComponent": "HAN-DB", "RefTitle": "Recovery Fails if a Backup Header is Large", "RefUrl": "/notes/2876107 "}, {"RefNumber": "2875087", "RefComponent": "HAN-DB-SDA", "RefTitle": "Unable to Create a Virtual Tables Based on Vora Remote Source", "RefUrl": "/notes/2875087 "}, {"RefNumber": "2875274", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With Exception \"mutex StatisticsMonitor::RowList\"", "RefUrl": "/notes/2875274 "}, {"RefNumber": "2874997", "RefComponent": "HAN-DB", "RefTitle": "PlanViz Execution Time of a Query Involving a Scalar UDF is Factors Longer Compared to The Query Execution Time Without PlanViz", "RefUrl": "/notes/2874997 "}, {"RefNumber": "2874885", "RefComponent": "HAN-DB", "RefTitle": "Query Execution Fails With \"Failed to find temp table\" or \"TableConfig is invalid for table\" in Scale-Out System After Coordinator <PERSON><PERSON> or Crash", "RefUrl": "/notes/2874885 "}, {"RefNumber": "2874804", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAPDBCTRL CCL config for PSE secure credential access", "RefUrl": "/notes/2874804 "}, {"RefNumber": "2817738", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexCommonObjects::MultiValueHash::HashFunction::operator()", "RefUrl": "/notes/2817738 "}, {"RefNumber": "2871711", "RefComponent": "HAN-DB-CDS", "RefTitle": "Keeping Partition Schema For a CDS Entity(.hdbdd file) in XS Classic", "RefUrl": "/notes/2871711 "}, {"RefNumber": "2871550", "RefComponent": "HAN-DB", "RefTitle": "One of the SAP HANA Services Crashes Due to Failed Distributed Transaction Rollbacks", "RefUrl": "/notes/2871550 "}, {"RefNumber": "2871509", "RefComponent": "HAN-DB", "RefTitle": "Service Crash in ptime::Query::QueryRuntimeTextCallBack::printSelfContextInfo", "RefUrl": "/notes/2871509 "}, {"RefNumber": "2869767", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With a Crash Stack Which Contains Excessive Recursion on ApproximateSubstringMatcher::match", "RefUrl": "/notes/2869767 "}, {"RefNumber": "2868164", "RefComponent": "HAN-DB", "RefTitle": "Deadlock With Distributed Transaction Cancellation", "RefUrl": "/notes/2868164 "}, {"RefNumber": "2868172", "RefComponent": "HAN-DB", "RefTitle": "Full Data Shipping after Re-registering Secondary System if Connection to the Primary System Could not be Established", "RefUrl": "/notes/2868172 "}, {"RefNumber": "2867814", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::fnBwGetAggregateMeasures2", "RefUrl": "/notes/2867814 "}, {"RefNumber": "2859727", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at sqlhint::HintArgument::get_mixture_int due to RESULT_LAG Hint", "RefUrl": "/notes/2859727 "}, {"RefNumber": "2867194", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes at ptime::RemoteQueryExecution::fetch", "RefUrl": "/notes/2867194 "}, {"RefNumber": "2865997", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Stream::SynchronousPoolCopyHandler::doCopy During Backup in Scale-Out", "RefUrl": "/notes/2865997 "}, {"RefNumber": "2866390", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::TraceUtil::getParameterValue When Creating Runtime Dump", "RefUrl": "/notes/2866390 "}, {"RefNumber": "2865738", "RefComponent": "HAN-DB", "RefTitle": "Query Cannot be Canceled and Keeps Running for a Very Long Time in TRexCommonObjects::checkDelimiter", "RefUrl": "/notes/2865738 "}, {"RefNumber": "2865627", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Crash in JoinEvaluator::accessHashMapRows", "RefUrl": "/notes/2865627 "}, {"RefNumber": "2863395", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ApproximateSubstringMatcher::match", "RefUrl": "/notes/2863395 "}, {"RefNumber": "2863044", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes When Jo<PERSON> is Evaluated Before Filter Condition", "RefUrl": "/notes/2863044 "}, {"RefNumber": "2860805", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashed at \"ltt::string_base<char, ltt::char_traits<char> >::append\" When Repository Object is Activated", "RefUrl": "/notes/2860805 "}, {"RefNumber": "2859308", "RefComponent": "HAN-DB", "RefTitle": "Scaleout system fails to start: unable to connect storage devices", "RefUrl": "/notes/2859308 "}, {"RefNumber": "2859171", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Transaction in a Distributed Landscape", "RefUrl": "/notes/2859171 "}, {"RefNumber": "2854052", "RefComponent": "HAN-DB", "RefTitle": "<PERSON><PERSON> Job Fails With <PERSON><PERSON><PERSON> \"indexhandle shared lock failed\"", "RefUrl": "/notes/2854052 "}, {"RefNumber": "2857020", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With Exception \"ERROR [CODE-295] too long multi key length\"", "RefUrl": "/notes/2857020 "}, {"RefNumber": "2855145", "RefComponent": "HAN-DB-CLI", "RefTitle": "Cannot Connect to SAP HANA Using the \"SAP HANA Client for Excel\"", "RefUrl": "/notes/2855145 "}, {"RefNumber": "2854693", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crash at Metadata::PersistString::ptr()", "RefUrl": "/notes/2854693 "}, {"RefNumber": "2854529", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::ViewCacheUtils::getAggrCalcType", "RefUrl": "/notes/2854529 "}, {"RefNumber": "2854324", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in ptime::Join_hash::do_fetch", "RefUrl": "/notes/2854324 "}, {"RefNumber": "2854182", "RefComponent": "HAN-DB", "RefTitle": "XSengine crash at IctParseHttpBody2", "RefUrl": "/notes/2854182 "}, {"RefNumber": "2854105", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes at AttributeEngine::BtreeAttribute::LockedInvertedIndex::addIndexValue", "RefUrl": "/notes/2854105 "}, {"RefNumber": "2852908", "RefComponent": "HAN-DB", "RefTitle": "Undefined Behavior of UPSERT Statements With Sub-Selects That Return Multiple Records With the Same Primary Keys", "RefUrl": "/notes/2852908 "}, {"RefNumber": "2850307", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ResourceManager::ResourceHeader::addReferenceIfManaged(bool) after the limit of the reference count has been reached in the ResourceManager.", "RefUrl": "/notes/2850307 "}, {"RefNumber": "2849922", "RefComponent": "HAN-DB-SEC", "RefTitle": "Kerberos SSO Returns \"Key version is not available\" Error", "RefUrl": "/notes/2849922 "}, {"RefNumber": "2846681", "RefComponent": "HAN-DB-BAC", "RefTitle": "Indexserver Crash at Stream::ChannelPoolImpl::removeChannel When Recovering Backup Using Backint", "RefUrl": "/notes/2846681 "}, {"RefNumber": "2844918", "RefComponent": "HAN-DB-PER", "RefTitle": "hdbpersdiag Reports Error: \"ERROR: Failed to load ContainerDirectory entry from the restart page: Invalid logical page number [invalid]..\"", "RefUrl": "/notes/2844918 "}, {"RefNumber": "2844097", "RefComponent": "HAN-DB", "RefTitle": "Indexserver on Secondary Site Crashes at CSAccessor::Dml::DmlExecutor::validateRowPosCount", "RefUrl": "/notes/2844097 "}, {"RefNumber": "2844131", "RefComponent": "HAN-DB-BAC", "RefTitle": "Indexserver Crash at Crypto::Provider::CommonCryptoProvider::encryptUpdate WHEN data_backup_buffer_size is SET to >= 2048", "RefUrl": "/notes/2844131 "}, {"RefNumber": "2844050", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at qo3::ColCounter::convertKeys", "RefUrl": "/notes/2844050 "}, {"RefNumber": "2843100", "RefComponent": "HAN-DB", "RefTitle": "Memory Leak in Pool/PersistenceLayer", "RefUrl": "/notes/2843100 "}, {"RefNumber": "2841478", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in ptime::TraceContextMeasurementScope", "RefUrl": "/notes/2841478 "}, {"RefNumber": "2841380", "RefComponent": "HAN-DB", "RefTitle": "Nameserver Crash at 0x0000000000000000 After TRexUtils::IndexVectorRef::mgetSearch", "RefUrl": "/notes/2841380 "}, {"RefNumber": "2840890", "RefComponent": "HAN-DB", "RefTitle": "<NOT released for customers><bug 191347> Crash at js::CrashAtUnhandlableOOM / js::AutoEnterOOMUnsafeRegion", "RefUrl": "/notes/2840890 "}, {"RefNumber": "2834040", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crashed at _ZN3ltt11string_baseIcNS_11char_traitsIcEEED2Ev", "RefUrl": "/notes/2834040 "}, {"RefNumber": "2832946", "RefComponent": "HAN-DB", "RefTitle": "Version Count is Increasing But There is neither Garbage Collection Blocker nor Column Store Blocking Statement Identified", "RefUrl": "/notes/2832946 "}, {"RefNumber": "2832967", "RefComponent": "HAN-DB", "RefTitle": "Performance Degradation When Using Input Parameters on Hierarchies via MDS / MDX", "RefUrl": "/notes/2832967 "}, {"RefNumber": "2832847", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in TransactionManager::SubTransactionData::unsetStartedVolumeIDAll", "RefUrl": "/notes/2832847 "}, {"RefNumber": "2832843", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes With AttributeEngine::BtreeAttribute::Operation::InlistSearchOperation", "RefUrl": "/notes/2832843 "}, {"RefNumber": "2832750", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in LVC_liveCacheSink::CreateContainer", "RefUrl": "/notes/2832750 "}, {"RefNumber": "2830093", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at federation::OdbcUtils::ConvertFromUnicode With Signal 11 When Accessing a Virtual Table Using Smart Data Access", "RefUrl": "/notes/2830093 "}, {"RefNumber": "2826766", "RefComponent": "HAN-DB-HA", "RefTitle": "Trace Files *.perspage.*.corrupt.dmp Created on Secondary Site After Re-registration", "RefUrl": "/notes/2826766 "}, {"RefNumber": "2826275", "RefComponent": "HAN-DB-HA", "RefTitle": "HA/DR Provider in Hook Method srConnectionChanged can Report Incorrect Overall Status", "RefUrl": "/notes/2826275 "}, {"RefNumber": "2825229", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With Signal 11 Due to Race Condition After DataAccess::BackupReadCallback::finishTriggerRead While Creating a Backup", "RefUrl": "/notes/2825229 "}, {"RefNumber": "2823930", "RefComponent": "HAN-DB-CLI", "RefTitle": "Indexserver Crash During Startup With Exception 3000610 \"Could not get table container for container id\" While Processing Undo", "RefUrl": "/notes/2823930 "}, {"RefNumber": "2822978", "RefComponent": "HAN-DB", "RefTitle": "Statement Execution Fails In SAP HANA Studio after PlanViz Generation Failed", "RefUrl": "/notes/2822978 "}, {"RefNumber": "2821454", "RefComponent": "HAN-DB-HA", "RefTitle": "Indexserver Crash With Exception \"allocation failed\" in PageAccess::PageAllocator::allocate During DataAccess::DisasterRecoverySecondaryHandlerImpl::preloadTablesDuringLogReplayInt on Secondary Site", "RefUrl": "/notes/2821454 "}, {"RefNumber": "2819959", "RefComponent": "HAN-DB", "RefTitle": "Indexerver Crash at ptime::Transaction::postcommit if Newdb::PreemptiveTokenRegisterGuard::PreemptiveTokenRegisterGuard Cannot Allocate Memory", "RefUrl": "/notes/2819959 "}, {"RefNumber": "2819401", "RefComponent": "HAN-DB", "RefTitle": "Errors Related to the _SYS_XB User and Schema When Running the SAP HANA System Migration Tool to Migrate From SAP HANA 1.0 to SAP HANA 2.0 on IBM Power", "RefUrl": "/notes/2819401 "}, {"RefNumber": "2817420", "RefComponent": "HAN-DB", "RefTitle": "Potentially Incorrect Visibility in Scale-Out Environments With 2 or More Worker Hosts", "RefUrl": "/notes/2817420 "}, {"RefNumber": "2813240", "RefComponent": "HAN-DB", "RefTitle": "Nameserver Crash in NameServerCmd::CmdActionConvertToMultiDB::convertTopologyToMDC() While Converting a Single DB to a MDC", "RefUrl": "/notes/2813240 "}, {"RefNumber": "2809493", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::cpb::TypeUtil::decodeCpKey", "RefUrl": "/notes/2809493 "}, {"RefNumber": "2767844", "RefComponent": "HAN-DB", "RefTitle": "Too Old Snapshot Timestamp Reported in Indexserver/Nameserver Traces", "RefUrl": "/notes/2767844 "}, {"RefNumber": "2757734", "RefComponent": "HAN-DB", "RefTitle": "System Wide Slow Performance at ptime::Transaction::syncTs", "RefUrl": "/notes/2757734 "}, {"RefNumber": "2737221", "RefComponent": "HAN-DB", "RefTitle": "Insufficient privilege error when Table User Defined Function or Scripted View is unfolded", "RefUrl": "/notes/2737221 "}, {"RefNumber": "2807653", "RefComponent": "HAN-DB", "RefTitle": "Recovering a Tenant Database After Recovering the SYSTEMDB Fails", "RefUrl": "/notes/2807653 "}, {"RefNumber": "2803911", "RefComponent": "HAN-DB", "RefTitle": "Calculation Engine not Supporting Synonyms in SAP HANA 1", "RefUrl": "/notes/2803911 "}, {"RefNumber": "2802061", "RefComponent": "HAN-DB", "RefTitle": "Service Crash at Stream::SynchronousPoolCopyHandler::finishCopy During Backup", "RefUrl": "/notes/2802061 "}, {"RefNumber": "2791590", "RefComponent": "HAN-DB", "RefTitle": "Indexserver can crash at ptime::ExternalStatement::ExecutionResponse::serialize", "RefUrl": "/notes/2791590 "}, {"RefNumber": "2796610", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at JoinEvaluator::JEAggregate::cutHavingSorted", "RefUrl": "/notes/2796610 "}, {"RefNumber": "2669155", "RefComponent": "HAN-DB", "RefTitle": "Hardware Failure of One Host Leads a Hang of a Whole HANA System", "RefUrl": "/notes/2669155 "}, {"RefNumber": "2794333", "RefComponent": "HAN-DB", "RefTitle": "Analyzing \"New time value (<x> ns) is too much less than the previous one (<y> ns)\" Errors or Crashes", "RefUrl": "/notes/2794333 "}, {"RefNumber": "2793583", "RefComponent": "HAN-DB-SDA", "RefTitle": "Indexserver or Scriptserver Crash With Signal 6 When Using libgssapi_krb5.so From /usr/lib64/", "RefUrl": "/notes/2793583 "}, {"RefNumber": "2686767", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes at \"Diagnose::StatisticsData::Value::materializeString(...)\" When Selecting Monitoring Views Related to SQL Plan Cache", "RefUrl": "/notes/2686767 "}, {"RefNumber": "2792071", "RefComponent": "HAN-DB", "RefTitle": "Indexserver might crashes at \"ptime::cpb::Tree::route()\" on IBM Power platform.", "RefUrl": "/notes/2792071 "}, {"RefNumber": "2791754", "RefComponent": "HAN-DB", "RefTitle": "SQL Statements Fail With SQL Code 257 While Plantrace is Active", "RefUrl": "/notes/2791754 "}, {"RefNumber": "2791406", "RefComponent": "HAN-DB-SDA", "RefTitle": "SDA: Querying a Virtual table Over ODBC Converts Empty Dates to NULL", "RefUrl": "/notes/2791406 "}, {"RefNumber": "2662546", "RefComponent": "HAN-DB", "RefTitle": "HDBLCM - Error on the Update of SAP HANA Instance Integration on Remote Hosts Failed", "RefUrl": "/notes/2662546 "}, {"RefNumber": "2789669", "RefComponent": "HAN-DB", "RefTitle": "Compressing an InfoCube Fails With Error \"indexhandle shared lock failed\"", "RefUrl": "/notes/2789669 "}, {"RefNumber": "2789644", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at expr::UnicodeCollator::getCollator When Running Query With Huge Number of Nesting Expressions", "RefUrl": "/notes/2789644 "}, {"RefNumber": "2789560", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::JobMergeCountsParallel::run When Selecting From M_CS_COLUMNS for Sparse Columns", "RefUrl": "/notes/2789560 "}, {"RefNumber": "2737476", "RefComponent": "HAN-DB", "RefTitle": "Swapping Worker and Standby Role of two SAP HANA DB Hosts has no Effect", "RefUrl": "/notes/2737476 "}, {"RefNumber": "2787280", "RefComponent": "HAN-DB", "RefTitle": "Column Store Table Option IS_LOG_DELTA is Reset to TRUE Upon Restart", "RefUrl": "/notes/2787280 "}, {"RefNumber": "2782487", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Transaction::getMetadataContext()", "RefUrl": "/notes/2782487 "}, {"RefNumber": "2778141", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes at \"AttributeEngine::OlapMultiJoin::bwMergeDictGetDocids\".", "RefUrl": "/notes/2778141 "}, {"RefNumber": "2779135", "RefComponent": "HAN-DB", "RefTitle": "High Number of Row Store Orphan LOB", "RefUrl": "/notes/2779135 "}, {"RefNumber": "2776953", "RefComponent": "HAN-DB", "RefTitle": "Query Fails With Error \"failed to execute the external statement: no such data type:..\" in HANA Scale-out System", "RefUrl": "/notes/2776953 "}, {"RefNumber": "2777775", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::qo_Rel::checkPartAwareStmtRouting", "RefUrl": "/notes/2777775 "}, {"RefNumber": "2775180", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash with Signal 11 and Stack Overflow while parsing invalid MDX statement", "RefUrl": "/notes/2775180 "}, {"RefNumber": "2772020", "RefComponent": "HAN-DB", "RefTitle": "Repository Objects are not migrated after Multi-Database Container conversion", "RefUrl": "/notes/2772020 "}, {"RefNumber": "2752976", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Executor::PlanExecutor::doCalculateSingleThreaded During Query Execution", "RefUrl": "/notes/2752976 "}, {"RefNumber": "2771757", "RefComponent": "HAN-DB", "RefTitle": "Query Execution Error: \"Column does not fit to attribute\"", "RefUrl": "/notes/2771757 "}, {"RefNumber": "2766537", "RefComponent": "HAN-DB", "RefTitle": "A Limitation on Unfolding Script Scripted Calculation Views For Compile-Time Non-Constant Functions", "RefUrl": "/notes/2766537 "}, {"RefNumber": "2728265", "RefComponent": "HAN-DB", "RefTitle": "Procedure call returns \"invalid schema error\"", "RefUrl": "/notes/2728265 "}, {"RefNumber": "2728275", "RefComponent": "HAN-DB", "RefTitle": "Cancel session hangs with procedure call _SYS_STATISTICS.STATISTICS_SCHEDULABLEWRAPPER('Timer', ?, ?, ?, ?) or queries on specific monitoring views", "RefUrl": "/notes/2728275 "}, {"RefNumber": "2728285", "RefComponent": "HAN-DB", "RefTitle": "Large heap allocator AllocateOnlyAllocator-unlimited/FLA-UL<...>/MemoryMapLevel2Blocks", "RefUrl": "/notes/2728285 "}, {"RefNumber": "2716784", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::btree::BtreeMempool::collect", "RefUrl": "/notes/2716784 "}, {"RefNumber": "2763394", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at PageAccess::ConverterPageControlBlock::setModified", "RefUrl": "/notes/2763394 "}, {"RefNumber": "2761275", "RefComponent": "HAN-DB", "RefTitle": "Nameserver Crash on Secondary Site During MDC Conversion After Metadata::MDRedoLogHandler::redo", "RefUrl": "/notes/2761275 "}, {"RefNumber": "2760782", "RefComponent": "HAN-DB", "RefTitle": "Peak Memory of Executed Statements not Collected From Worker Nodes if statement_memory_limit is not Configured", "RefUrl": "/notes/2760782 "}, {"RefNumber": "2756405", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::idattribute::BlockHandle::uncompress During a SQL Statement Execution or Table Consistency Check", "RefUrl": "/notes/2756405 "}, {"RefNumber": "2754379", "RefComponent": "HAN-DB", "RefTitle": "Data Volume Encryption is Only Propagated to Secondary Site for Newly Written Pages", "RefUrl": "/notes/2754379 "}, {"RefNumber": "2754452", "RefComponent": "HAN-DB", "RefTitle": "Chunkwise Execution of DELETE Statement Fails With \"RowPos xx (of RowID yyy) in fragment nnn of schema_name.table_name is already marked as deleted.\"", "RefUrl": "/notes/2754452 "}, {"RefNumber": "2753994", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With \"Error during asynchronous file transfer (io_getevents), rc=12: Cannot allocate memory\"", "RefUrl": "/notes/2753994 "}, {"RefNumber": "2752072", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::SessionCodecNewdb::dSessionContextPart() due to Wrongly Handled Connection Requests", "RefUrl": "/notes/2752072 "}, {"RefNumber": "2733001", "RefComponent": "HAN-DB", "RefTitle": "Slower Performance During Table Consistency Check due to Blocked DMLs", "RefUrl": "/notes/2733001 "}, {"RefNumber": "2746716", "RefComponent": "HAN-DB", "RefTitle": "openLDAP server support for LDAP Authentication or Authorization in SAP HANA", "RefUrl": "/notes/2746716 "}, {"RefNumber": "2741635", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at JoinEvaluator::JEAssembleResults::getRelevanceAsBitVector", "RefUrl": "/notes/2741635 "}, {"RefNumber": "2721385", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash After federation::FederationContext::SetErrorState While Executing a Query Using SDA", "RefUrl": "/notes/2721385 "}, {"RefNumber": "2743948", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With \"JoinEvaluator::JEPrepareData::adjustNegativeDocIds\"", "RefUrl": "/notes/2743948 "}, {"RefNumber": "2742255", "RefComponent": "HAN-DB", "RefTitle": "HANA Crashes During Startup After Failing to Allocate Unrealistically <PERSON><PERSON> Amount of Memory", "RefUrl": "/notes/2742255 "}, {"RefNumber": "2740826", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash or OOM at AabapSysTimezone", "RefUrl": "/notes/2740826 "}, {"RefNumber": "2738849", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash After Metadata::Referential::checkPersistLobType While Performing Metadata Consistency Check", "RefUrl": "/notes/2738849 "}, {"RefNumber": "2734669", "RefComponent": "HAN-DB-SDA", "RefTitle": "Indexserver Crash at \"Trying to lock a destroyed mutex m_Futex\" in ODBC Context", "RefUrl": "/notes/2734669 "}, {"RefNumber": "2733497", "RefComponent": "HAN-DB", "RefTitle": "Shutdown Failure of ThreadManager due to AntiAger Threads Not Released During Indexserver Shutdown", "RefUrl": "/notes/2733497 "}, {"RefNumber": "2731045", "RefComponent": "HAN-DB", "RefTitle": "Service Crash in DataContainer::PageChainContainerPrefetchIterator::PrefetchCallback::resetDisposition", "RefUrl": "/notes/2731045 "}, {"RefNumber": "2727515", "RefComponent": "HAN-DB", "RefTitle": "New Hint for Multi-Column Join", "RefUrl": "/notes/2727515 "}, {"RefNumber": "2727193", "RefComponent": "HAN-DB", "RefTitle": "High Number of Rows in Table _SYS_STATISTICS.HOST_RECORD_LOCKS_BASE", "RefUrl": "/notes/2727193 "}, {"RefNumber": "2727192", "RefComponent": "HAN-DB", "RefTitle": "Indexerver Crash at ptime::get_cpkey_len When Selecting from a Rowstore Table", "RefUrl": "/notes/2727192 "}, {"RefNumber": "2727039", "RefComponent": "HAN-DB", "RefTitle": "System Replication Failing After Registration With Error \"Invalid secondary replication mode: ReplicationMode_\"", "RefUrl": "/notes/2727039 "}, {"RefNumber": "2726853", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at LVC_LockManager::Lock_LockManagerImpl::LockWithinData When Using LiveCache", "RefUrl": "/notes/2726853 "}, {"RefNumber": "2725255", "RefComponent": "HAN-DB", "RefTitle": "Hanging Situation During Lock Contention", "RefUrl": "/notes/2725255 "}, {"RefNumber": "2705933", "RefComponent": "HAN-DB", "RefTitle": "BW Unit of Measure Conversion on HANA With Conversion Factor 0", "RefUrl": "/notes/2705933 "}, {"RefNumber": "2724392", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes at ptime::Query::QueryRuntimeTextCallBack::printSelfContextInfo With Signal 11", "RefUrl": "/notes/2724392 "}, {"RefNumber": "2722137", "RefComponent": "HAN-DB-MDX", "RefTitle": "Change in Behavior of MDX When Using a HANA Calculation View With a HANA Hierarchy Variable or Input Parameters to Filter a Hierarchy", "RefUrl": "/notes/2722137 "}, {"RefNumber": "2719984", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash When Deadlock Detected During Rowstore Rollback", "RefUrl": "/notes/2719984 "}, {"RefNumber": "2700356", "RefComponent": "HAN-DB", "RefTitle": "Nameserver Crash at Backup::BackupCatalog_MemoryCatalog During Snapshot Operation", "RefUrl": "/notes/2700356 "}, {"RefNumber": "2714921", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Synchronization::ReadWriteLock::~ReadWriteLock During Rollback at System Startup", "RefUrl": "/notes/2714921 "}, {"RefNumber": "2701282", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::FuzzyQueryTermMappingCombination<int>::_fillTokenInfo", "RefUrl": "/notes/2701282 "}, {"RefNumber": "2654931", "RefComponent": "HAN-DB", "RefTitle": "XS Engine Crashes With xsengine::TupelList::get()", "RefUrl": "/notes/2654931 "}, {"RefNumber": "2711998", "RefComponent": "BC-DB-LCA", "RefTitle": "Indexserver Crash at OMS_StreamBody::BuildCurrentRow After Cancelling an Operation Accessing LiveCache", "RefUrl": "/notes/2711998 "}, {"RefNumber": "2709806", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash After TRexAPI::FreeStyleExecutor::process When Using Fuzzy Search", "RefUrl": "/notes/2709806 "}, {"RefNumber": "2710439", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Worker Thread in Sleeping State After Executing a Planning Sequence", "RefUrl": "/notes/2710439 "}, {"RefNumber": "2709808", "RefComponent": "HAN-DB", "RefTitle": "High Number of SQL Plan Cache Entries for User _SYS_STATISTICS", "RefUrl": "/notes/2709808 "}, {"RefNumber": "2708968", "RefComponent": "HAN-DB-HA", "RefTitle": "Inconsistent Host Roles After One-Sided XSA Installation and Takeover", "RefUrl": "/notes/2708968 "}, {"RefNumber": "2708798", "RefComponent": "HAN-DB", "RefTitle": "SqlScript Execution Fails with \"Attempt to access metadata of an invalid address (already deleted or corrupted, etc.) - Could not derive table type for variable\"", "RefUrl": "/notes/2708798 "}, {"RefNumber": "2707736", "RefComponent": "BC-CST-WDP", "RefTitle": "Webdispatcher Crash due to Malloc Allocator Map Overlap", "RefUrl": "/notes/2707736 "}, {"RefNumber": "2705056", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::SQLPlanCacheStatistics::getSqlStringHash()", "RefUrl": "/notes/2705056 "}, {"RefNumber": "2704762", "RefComponent": "HAN-DB", "RefTitle": "Very Long Running Optimize Compression of Column Store Tables on System With a High Number of NUMA Nodes", "RefUrl": "/notes/2704762 "}, {"RefNumber": "2704393", "RefComponent": "HAN-DB", "RefTitle": "Scale-out Installation or Add Host Fails with Error LIBSSH2_ERROR_KEY_EXCHANGE_FAILURE", "RefUrl": "/notes/2704393 "}, {"RefNumber": "2701292", "RefComponent": "HAN-DB-HA", "RefTitle": "Impaired System Replication When Having Network Issues and OOM on Primary Site Simultaneously", "RefUrl": "/notes/2701292 "}, {"RefNumber": "2700123", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at PageAccess::PageFlushCallback::page<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> With \"Unexpected offset\"", "RefUrl": "/notes/2700123 "}, {"RefNumber": "2699968", "RefComponent": "HAN-DB", "RefTitle": "Indexserver of a Worker Node Crashes When Replaying the log Triggered by CSAccessor::Redo::RedoHandler::redoDml", "RefUrl": "/notes/2699968 "}, {"RefNumber": "2364120", "RefComponent": "HAN-DB-ENG", "RefTitle": "Coordinator Indexserver Hanging With Threads Callstacks Related to EPoch Communication", "RefUrl": "/notes/2364120 "}, {"RefNumber": "2699655", "RefComponent": "HAN-DB", "RefTitle": "Select From a View is Hanging if the View is Selecting From a Public Synonym With the Same Name", "RefUrl": "/notes/2699655 "}, {"RefNumber": "2695461", "RefComponent": "HAN-DB-HA", "RefTitle": "Indexserver Crash at DataRecovery::RecoveryHandlerImpl::reportReplayError on Worker Node of Secondary System as a Result of Redo log Being Replayed out of Order", "RefUrl": "/notes/2695461 "}, {"RefNumber": "2693120", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash with SIGNAL 11 During Recovery of a Tenant Database due to Inconsistent Topology Information in the SystemDB", "RefUrl": "/notes/2693120 "}, {"RefNumber": "2693048", "RefComponent": "HAN-DB", "RefTitle": "The Indexserver Crash at ptime::Transaction::partial_abort_(bool)", "RefUrl": "/notes/2693048 "}, {"RefNumber": "2691947", "RefComponent": "HAN-DB-HA", "RefTitle": "Service Crash After Takeover With 'log position inconsistency detected' in SAP HANA System Replication Setup With Mode Delta_Datashipping", "RefUrl": "/notes/2691947 "}, {"RefNumber": "2691720", "RefComponent": "HAN-DB", "RefTitle": "Active SAP HANA Node Does not Listen on SQL Port", "RefUrl": "/notes/2691720 "}, {"RefNumber": "2691805", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Results In Planning Scenarios During Disaggregation", "RefUrl": "/notes/2691805 "}, {"RefNumber": "2683989", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Results When Querying a Calculation View Where a Left Outer Join and a HANA View Node Share Filter Condition on Another HANA View Node", "RefUrl": "/notes/2683989 "}, {"RefNumber": "2690674", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at OlapEngine::Parallel::ParallelHashMap::CompressFemsParallel During OLAP Queries with FEMs Filter Execution", "RefUrl": "/notes/2690674 "}, {"RefNumber": "2689387", "RefComponent": "HAN-DB", "RefTitle": "Wrong Results After Creating a Table on a Standby Host", "RefUrl": "/notes/2689387 "}, {"RefNumber": "2687785", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::qo_position::set_position When Executing SQL Query", "RefUrl": "/notes/2687785 "}, {"RefNumber": "2686498", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Result Due to Invalid Predefined Session Variables Value Update", "RefUrl": "/notes/2686498 "}, {"RefNumber": "2685632", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Result Using Complex SQL Analytic Privileges With Distinct Count", "RefUrl": "/notes/2685632 "}, {"RefNumber": "2685891", "RefComponent": "HAN-DB", "RefTitle": "Misleading \"connection refused\" Error Messages on Secondary Coordinator Host after Coordinator Host Failed Over on Primary Site", "RefUrl": "/notes/2685891 "}, {"RefNumber": "2686011", "RefComponent": "HAN-DB", "RefTitle": "Accounting for Shared Memory Size is Wrong on IBM Power", "RefUrl": "/notes/2686011 "}, {"RefNumber": "2685802", "RefComponent": "HAN-DB", "RefTitle": "Nameserver of SystemDB is Hanging During Startup When systemdb_reserved_memory is Set", "RefUrl": "/notes/2685802 "}, {"RefNumber": "2684913", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes When Remote Server Connected by SDA Goes Down", "RefUrl": "/notes/2684913 "}, {"RefNumber": "2679900", "RefComponent": "HAN-DB", "RefTitle": "Inconsistent Rounding Behavior of ERP EMU Currency Conversion", "RefUrl": "/notes/2679900 "}, {"RefNumber": "2671657", "RefComponent": "HAN-DB", "RefTitle": "Catalog Export Gets Failed After Coordinator <PERSON><PERSON> Failover Event Occurred", "RefUrl": "/notes/2671657 "}, {"RefNumber": "2679735", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Ordering of Result or Indexserver Crash After ptime::NBaseString::compare", "RefUrl": "/notes/2679735 "}, {"RefNumber": "2679520", "RefComponent": "HAN-DB", "RefTitle": "xsengine Crash at TrexThreads::Thread::join(int)", "RefUrl": "/notes/2679520 "}, {"RefNumber": "2678622", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at DataRecovery::LogSegmentFactory::LoadSegments During Takeover", "RefUrl": "/notes/2678622 "}, {"RefNumber": "2672487", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Results When Using SQL Set Operators With Large Data Sets", "RefUrl": "/notes/2672487 "}, {"RefNumber": "2676528", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at FederationException::getExceptionText if Accessing a Virtual Table is Interrupted by an Exception", "RefUrl": "/notes/2676528 "}, {"RefNumber": "2675192", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at businessDB::ConversionRatesTable::performInitialSetup While Executing a Query Performing Currency Conversion", "RefUrl": "/notes/2675192 "}, {"RefNumber": "2674483", "RefComponent": "HAN-DB", "RefTitle": "Update or Delete Statements Using Only Inverted Hash Primary Key Fields in the WHERE Clause Do not Succeed for Column Store Tables", "RefUrl": "/notes/2674483 "}, {"RefNumber": "2467279", "RefComponent": "HAN-DB", "RefTitle": "Immediate Invalidation of Plan Cache Entry due to Invalid Records Containing old System Tables", "RefUrl": "/notes/2467279 "}, {"RefNumber": "2670614", "RefComponent": "HAN-DB", "RefTitle": "Increasing Memory Allocation on Pool/RowEngine/MonitorView or Pool/parallel/compactcol", "RefUrl": "/notes/2670614 "}, {"RefNumber": "2670586", "RefComponent": "HAN-DB-ENG", "RefTitle": "Blocked Delta Merges and Blocked Queries due to Long Running LIKE Searches", "RefUrl": "/notes/2670586 "}, {"RefNumber": "2669798", "RefComponent": "HAN-DB", "RefTitle": "Query Execution Leads to an Out of Memory Situation Though Statement Memory Limit is Set", "RefUrl": "/notes/2669798 "}, {"RefNumber": "2668174", "RefComponent": "HAN-DB", "RefTitle": "System Startup Hangs at startSegmentLoad()", "RefUrl": "/notes/2668174 "}, {"RefNumber": "2668627", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at DataAccess::GarbageCollectorJob::run", "RefUrl": "/notes/2668627 "}, {"RefNumber": "2662209", "RefComponent": "HAN-DB", "RefTitle": "ALTER SYSTEM ADD STATEMENT HINT With Multi-lined Statements", "RefUrl": "/notes/2662209 "}, {"RefNumber": "2662768", "RefComponent": "HAN-DB", "RefTitle": "Offline Reorganization is Skipped in Worker Node", "RefUrl": "/notes/2662768 "}, {"RefNumber": "2667097", "RefComponent": "HAN-DB-HA", "RefTitle": "Primary Site Unresponsive After Logshipping Timeout Happened", "RefUrl": "/notes/2667097 "}, {"RefNumber": "2666678", "RefComponent": "HAN-DB", "RefTitle": "Optimize Compression not Happening Automatically for Columnstore Tables With Deactivated Auto Merge", "RefUrl": "/notes/2666678 "}, {"RefNumber": "2666338", "RefComponent": "HAN-DB-SEC", "RefTitle": "Error 300013 \"SSL handshake failed: Channel closed during handshake\" Reported in Trace File When Closing Encrypted Client Connection", "RefUrl": "/notes/2666338 "}, {"RefNumber": "2664440", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::ResultSet::close When Executing a SELECT Which Includes a Conversion From BLOB to VARCHAR for a Rowstore Table", "RefUrl": "/notes/2664440 "}, {"RefNumber": "2665124", "RefComponent": "HAN-DB", "RefTitle": "Point in Time Recovery Hangs or Leads to an Indexserver Crash With \"Invalid PersistenceManager state\"", "RefUrl": "/notes/2665124 "}, {"RefNumber": "2665391", "RefComponent": "HAN-DB-SEC", "RefTitle": "Kerberos/SPNEGO Authentication Hanging Due To Lockwaits in Kerberos Libraries", "RefUrl": "/notes/2665391 "}, {"RefNumber": "2665390", "RefComponent": "HAN-DB", "RefTitle": "Lock Contention due to Record Locks on Already Deleted Records for a Columnstore Table", "RefUrl": "/notes/2665390 "}, {"RefNumber": "2665340", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at MemoryManager::PoolAllocator::chooseMemoryPoolForAllocation", "RefUrl": "/notes/2665340 "}, {"RefNumber": "2665323", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at sqlscript::util::createCalcResultName When Accessing a Calculation Scenario That has Been Created With SAP HANA 1.0 <= SPS08", "RefUrl": "/notes/2665323 "}, {"RefNumber": "2657966", "RefComponent": "HAN-DB-CLI", "RefTitle": "Incorrect Handling of Data Type Mapping for Decimal Type in HANA ADO.NET", "RefUrl": "/notes/2657966 "}, {"RefNumber": "2662858", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in ptime::oabapstream::init", "RefUrl": "/notes/2662858 "}, {"RefNumber": "2662717", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash After \"Executor::RemoteExecutor::serialize\"", "RefUrl": "/notes/2662717 "}, {"RefNumber": "2658787", "RefComponent": "HAN-DB", "RefTitle": "Table Load or Delta Merge on Column Store Table With Invalid Data Structures Hangs", "RefUrl": "/notes/2658787 "}, {"RefNumber": "2658652", "RefComponent": "HAN-DB", "RefTitle": "HDBLCM Fails to Resume a HANA Upgrade When Virtual Hostnames are Used as HANA Hostnames", "RefUrl": "/notes/2658652 "}, {"RefNumber": "2655468", "RefComponent": "HAN-DB", "RefTitle": "Parameters of Section [persistence] Are Not Considered After Restart", "RefUrl": "/notes/2655468 "}, {"RefNumber": "2653490", "RefComponent": "HAN-DB-BAC", "RefTitle": "Increased Used Size in Data Volume After Backup Failed With Error Message \"Extended storage backup error : [SAP][ODBC Driver]There is already a backup or selective restore in progress.\"", "RefUrl": "/notes/2653490 "}, {"RefNumber": "2653396", "RefComponent": "HAN-DB", "RefTitle": "Statement Memory Tracking Showing Higher Usage After Upgrade", "RefUrl": "/notes/2653396 "}, {"RefNumber": "2653354", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash After Execution::CPUStatisticsCollector::stopMeasurement When Using PlanViz Execution", "RefUrl": "/notes/2653354 "}, {"RefNumber": "2653240", "RefComponent": "HAN-DB", "RefTitle": "Using DISTINCT or GROUP BY in CDS View Used for Enterprise Search Causes Column Store Error", "RefUrl": "/notes/2653240 "}, {"RefNumber": "2643784", "RefComponent": "HAN-DB", "RefTitle": "Nameserver crash at Backup::BackupMgr_DeltaBackupTracker::initCheckAddedVolumesAfterBackupOrRecovery during system backup", "RefUrl": "/notes/2643784 "}, {"RefNumber": "2649721", "RefComponent": "HAN-DB-HA", "RefTitle": "Lock Contention in TrexNet_Requestor_EndPointSetLock Causes Unresponsiveness of Primary Site in HANA System Replication Environment", "RefUrl": "/notes/2649721 "}, {"RefNumber": "2648134", "RefComponent": "HAN-DB", "RefTitle": "Query Execution on a Calculation Scenario Fails With Error \"Attribute engine failed, Column does not fit to attribute\"", "RefUrl": "/notes/2648134 "}, {"RefNumber": "2647332", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at DataAccess::PersistenceManagerImpl::writeRestartData", "RefUrl": "/notes/2647332 "}, {"RefNumber": "2644935", "RefComponent": "HAN-DB", "RefTitle": "Incorrect Value in LOB Column of Columnstore Table After a Batch Update Statement Using Parameters", "RefUrl": "/notes/2644935 "}, {"RefNumber": "2644815", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Execution::ServiceThreadSamplerThread::createThreadSamples()  When Running MDS Query", "RefUrl": "/notes/2644815 "}, {"RefNumber": "2644402", "RefComponent": "HAN-DB-SCR", "RefTitle": "Indexserver Crash at ptime::TransTokenConnector::get_persistencesession During Procedure Execution", "RefUrl": "/notes/2644402 "}, {"RefNumber": "2644212", "RefComponent": "HAN-DB", "RefTitle": "Service of a Seconary Site Crashes in DataAccess::PersistenceSessionRegistry::processUndoFile", "RefUrl": "/notes/2644212 "}, {"RefNumber": "2643953", "RefComponent": "HAN-DB", "RefTitle": "Sporadic Indexserver Crash at sqlscript::SeTempTable::dropTempTable()", "RefUrl": "/notes/2643953 "}, {"RefNumber": "2643308", "RefComponent": "HAN-DB", "RefTitle": "Sporadic Indexserver Crash at ptime::TrexSearchMetaFactory::destroy", "RefUrl": "/notes/2643308 "}, {"RefNumber": "2643257", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at DataRecovery::LogReplayCoordinator::nextReplayStep When Re-Enabling HANA System Replication", "RefUrl": "/notes/2643257 "}, {"RefNumber": "2642673", "RefComponent": "HAN-DB", "RefTitle": "Crash at DataAccess::PersistenceSession::txRollbackCallback", "RefUrl": "/notes/2642673 "}, {"RefNumber": "2641642", "RefComponent": "HAN-DB", "RefTitle": "A Replica Table Drop Fails Even if Drop Statement is Executed Without Error", "RefUrl": "/notes/2641642 "}, {"RefNumber": "2385974", "RefComponent": "HAN-DB", "RefTitle": "Migration of the Synchronous Replication Tables From HANA 1.0 to HANA 2.0", "RefUrl": "/notes/2385974 "}, {"RefNumber": "2641301", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Result in Calculated Columns When Evaluated in Row Store", "RefUrl": "/notes/2641301 "}, {"RefNumber": "2633549", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexCommonObjects::SortedVector::~SortedVector() due to Memory Overwrite", "RefUrl": "/notes/2633549 "}, {"RefNumber": "2639241", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Synchronization::SharedHandle::copy/copyLL", "RefUrl": "/notes/2639241 "}, {"RefNumber": "2638028", "RefComponent": "HAN-DB", "RefTitle": "Behavior Correction of Invalid Updatable View Creation", "RefUrl": "/notes/2638028 "}, {"RefNumber": "2639246", "RefComponent": "HAN-DB", "RefTitle": "Host-Auto Failover or Backup Not Working in a Scale-Out Environment Due to Invalid host_config_roles and host_config_subpath", "RefUrl": "/notes/2639246 "}, {"RefNumber": "2638713", "RefComponent": "HAN-DB", "RefTitle": "Performance Degradation During Object Re-Validation", "RefUrl": "/notes/2638713 "}, {"RefNumber": "2637433", "RefComponent": "HAN-DB", "RefTitle": "Conversion Error Occurs When CASE or BOX Function is Defined in Filter Expression on Calculation View", "RefUrl": "/notes/2637433 "}, {"RefNumber": "2631932", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash While Removing Plan Cache Entry", "RefUrl": "/notes/2631932 "}, {"RefNumber": "2631225", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at DataAccess::ConsistentChange::release()", "RefUrl": "/notes/2631225 "}, {"RefNumber": "2628203", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes on ptime::QueryPlanGenerator::convert_expr_identity after changing schema of table", "RefUrl": "/notes/2628203 "}, {"RefNumber": "2629103", "RefComponent": "HAN-DB", "RefTitle": "SQL Traces Stop Writing When it Exceeds the Maximum Filesize.", "RefUrl": "/notes/2629103 "}, {"RefNumber": "2622234", "RefComponent": "HAN-DB", "RefTitle": "A Numeric Constant With Precision More than 38 is Truncated to Precision 34 in DML Statements.", "RefUrl": "/notes/2622234 "}, {"RefNumber": "2622273", "RefComponent": "HAN-DB", "RefTitle": "DECIMAL Value With Precision Higher than 34 is Converted to Precision 34 By Rounding When It Should be Truncated", "RefUrl": "/notes/2622273 "}, {"RefNumber": "2603589", "RefComponent": "HAN-DB", "RefTitle": "Composite OOM in orawstream::reserve", "RefUrl": "/notes/2603589 "}, {"RefNumber": "2618564", "RefComponent": "HAN-DB", "RefTitle": "Optimizing High DML Commit Time During Log Backups", "RefUrl": "/notes/2618564 "}, {"RefNumber": "2360711", "RefComponent": "HAN-DB-ENG", "RefTitle": "\"Unable to initialize UT container handle\" When Selecting From a Table", "RefUrl": "/notes/2360711 "}, {"RefNumber": "2376428", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Crash at ptime::qo_Exp::clone_subtree When Executing an MDS Query", "RefUrl": "/notes/2376428 "}, {"RefNumber": "2382829", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Indexserver Crash at PlanningEngine::PleFormulaPop::executePlePop While Executing Planning Function", "RefUrl": "/notes/2382829 "}, {"RefNumber": "2466493", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Crash at OlapEngine::Parallel::findSubTotals", "RefUrl": "/notes/2466493 "}, {"RefNumber": "2617548", "RefComponent": "HAN-DB", "RefTitle": "Upgrade Recommendation for SAP HANA 1.0 Installations", "RefUrl": "/notes/2617548 "}, {"RefNumber": "2616774", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at JoinEvaluator::JEAssembleResults::doItThePreparedWay", "RefUrl": "/notes/2616774 "}, {"RefNumber": "2616230", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in ptime::fastvector<type>::__deallocate()", "RefUrl": "/notes/2616230 "}, {"RefNumber": "2616088", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at PageAccess::PageInfoCollector::print When Executing hdbcons \"pageaccess analyse\"", "RefUrl": "/notes/2616088 "}, {"RefNumber": "2612022", "RefComponent": "HAN-DB", "RefTitle": "Increased Memory Allocator <PERSON>ze After Distributed Query Execution Failed due to OOM", "RefUrl": "/notes/2612022 "}, {"RefNumber": "2611580", "RefComponent": "HAN-DB-SEC", "RefTitle": "Adding In-Memory Certificate Fails With Error", "RefUrl": "/notes/2611580 "}, {"RefNumber": "2611293", "RefComponent": "HAN-DB", "RefTitle": "Indexserver on Secondary Site Crashes at AttributeEngine::BtreeAttribute::LockedInvertedIndex::addIndexValue", "RefUrl": "/notes/2611293 "}, {"RefNumber": "2599504", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexConfig::TablePlacementImpl::cutServer When Moving a Table to Another Node", "RefUrl": "/notes/2599504 "}, {"RefNumber": "2556443", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With Exception \"COMMIT not allowed on parent session\"", "RefUrl": "/notes/2556443 "}, {"RefNumber": "2498587", "RefComponent": "HAN-DB", "RefTitle": "Inconsistency Warning Between Metadata and OBJECT_DEPENDENCIES Table", "RefUrl": "/notes/2498587 "}, {"RefNumber": "2609695", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Services Crash After PageAccess::FreeBlockManagerImpl::reset After OOM on Secondary Site of SAP HANA System Replication Environment", "RefUrl": "/notes/2609695 "}, {"RefNumber": "2608324", "RefComponent": "HAN-DB", "RefTitle": "Expensive Statement Trace Parameters Maxfiles And Maxfilesize Are Not Considered if use_in_memory_tracing is Enabled", "RefUrl": "/notes/2608324 "}, {"RefNumber": "2606180", "RefComponent": "HAN-DB", "RefTitle": "Procedure Call Getting Stuck During Cancellation in ptime::Transaction::invalidate_external_inuse_count", "RefUrl": "/notes/2606180 "}, {"RefNumber": "2606241", "RefComponent": "HAN-DB", "RefTitle": "Script Server Replication Status Becomes ERROR After Upgrade Secondary Site to HANA2.0 SPS03 or Higher", "RefUrl": "/notes/2606241 "}, {"RefNumber": "2599513", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::TrexResultIMS::getGeometryValue", "RefUrl": "/notes/2599513 "}, {"RefNumber": "2603773", "RefComponent": "HAN-DB-SEC", "RefTitle": "SAP HANA Service Crash at Crypto::X509::CommonCrypto::CertificateStoreImpl::getOwnCertificate", "RefUrl": "/notes/2603773 "}, {"RefNumber": "2603514", "RefComponent": "HAN-DB-SEC", "RefTitle": "SYSTEM User Password Reset Fails With Error \"transaction serialization failure\"", "RefUrl": "/notes/2603514 "}, {"RefNumber": "2603378", "RefComponent": "HAN-DB-ENG", "RefTitle": "Crash at AttributeEngine::SingleMergePipeline During Delta Merge", "RefUrl": "/notes/2603378 "}, {"RefNumber": "2601873", "RefComponent": "HAN-DB", "RefTitle": "Timeout Happens While Changing Configuration by 'WITH RECONFIGURE'", "RefUrl": "/notes/2601873 "}, {"RefNumber": "2601881", "RefComponent": "HAN-DB-BAC", "RefTitle": "Log Backup Stops Working After Restart of Nameserver or Coordinator Indexserver", "RefUrl": "/notes/2601881 "}, {"RefNumber": "2601405", "RefComponent": "HAN-DB", "RefTitle": "Query on Calculation View Fails With Error \"A Plan Operation Received the Wrong Number of Arguments; ceQoPop ...\"", "RefUrl": "/notes/2601405 "}, {"RefNumber": "2599488", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TransactionManager::Transaction::acquireSubTransaction in Scale-Out System", "RefUrl": "/notes/2599488 "}, {"RefNumber": "2599979", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Diagnose::CallStackManager::getStack When Using \"top\", \"callgraph\" or \"blocklist\" of Category \"mm\" in hdbcons", "RefUrl": "/notes/2599979 "}, {"RefNumber": "2573499", "RefComponent": "HAN-DB", "RefTitle": "SYSTEM User Password Reset for SYSTEMDB Fails on MDC System with High Isolation", "RefUrl": "/notes/2573499 "}, {"RefNumber": "2598453", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash With Orphaned timestamp in TransactionManager::TransactionControlBlockFactory::getTimestampState", "RefUrl": "/notes/2598453 "}, {"RefNumber": "2598280", "RefComponent": "HAN-DB", "RefTitle": "Crash After ptime::Transaction::roll_back During OOM Situation", "RefUrl": "/notes/2598280 "}, {"RefNumber": "2598159", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crashed with \"_ZN13JoinEvaluator14checkPostQuery...\"", "RefUrl": "/notes/2598159 "}, {"RefNumber": "2598105", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::idattribute::IdAttribute<AttributeEngine::idattribute::IdBlockValues>::sortDocumentIds", "RefUrl": "/notes/2598105 "}, {"RefNumber": "2597676", "RefComponent": "HAN-DB-HA", "RefTitle": "Indexserver Crash on Primary Site When Collection of Statistics of Secondary Site Failed", "RefUrl": "/notes/2597676 "}, {"RefNumber": "2597712", "RefComponent": "HAN-DB-CLI", "RefTitle": "SQLDBC Client Crashes After SQLDBC::TraceWriter::write When Name or Size is Changed While Trace is Active", "RefUrl": "/notes/2597712 "}, {"RefNumber": "2596636", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Stream::PipeChannel::triggerWrite/Backup::BackupDest_DestCallback::streamError With \"rc=9: Bad file descriptor\" When Running Backint-based Backup", "RefUrl": "/notes/2596636 "}, {"RefNumber": "2595174", "RefComponent": "HAN-DB-SDA", "RefTitle": "Indexserver Crash Within Simba::<PERSON>::<PERSON>... of Simba Driver When Using Smart Data Access To Hadoop", "RefUrl": "/notes/2595174 "}, {"RefNumber": "2594739", "RefComponent": "HAN-DB", "RefTitle": "HANA Calculation Engine Support Mode Tracing", "RefUrl": "/notes/2594739 "}, {"RefNumber": "2592954", "RefComponent": "HAN-DB-BAC", "RefTitle": "Backup Using Backint Fails With Expected Size Less Than Reported Size", "RefUrl": "/notes/2592954 "}, {"RefNumber": "2589674", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::qo_Const::disambiguate When Using Expression With Conversion Function Longdate", "RefUrl": "/notes/2589674 "}, {"RefNumber": "2589630", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Diagnose::StatisticsBuffer::decodeLocalOffset With Exception 2120031", "RefUrl": "/notes/2589630 "}, {"RefNumber": "2589626", "RefComponent": "HAN-DB-SDA", "RefTitle": "SAP HANA Service Crash With Expection 2010025 \"Error in Mutex destructor: locked unexpected name=FederationOdbcAccess\" When Executing SDA Query", "RefUrl": "/notes/2589626 "}, {"RefNumber": "2587067", "RefComponent": "HAN-DB", "RefTitle": "Failure to Create or Drop Temporary Tables in an SDA Source", "RefUrl": "/notes/2587067 "}, {"RefNumber": "2587465", "RefComponent": "HAN-DB-HA", "RefTitle": "Delta Shipping Frequency Decreases When Using Operation Mode Delta_Datashipping", "RefUrl": "/notes/2587465 "}, {"RefNumber": "2587339", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in TransactionManager::TransTokenManager::destroySnapshotToken During OOM", "RefUrl": "/notes/2587339 "}, {"RefNumber": "2586672", "RefComponent": "HAN-DB-SDA", "RefTitle": "Indexserver Crashes When Accessing a Remote HANA Source", "RefUrl": "/notes/2586672 "}, {"RefNumber": "2584492", "RefComponent": "HAN-DB-ENG", "RefTitle": "Querying a CompositeProvider Fails With Error \"SAP DBTech JDBC: [2048]: column store error: search table error\" [2981] or [2018]", "RefUrl": "/notes/2584492 "}, {"RefNumber": "2585974", "RefComponent": "HAN-DB-SDA", "RefTitle": "Querying a Database Object Based on Virtual Objects Integrated Using Smart Data Access Fails with Syntax Error at Opening Cursor for Remote Database", "RefUrl": "/notes/2585974 "}, {"RefNumber": "2584388", "RefComponent": "HAN-DB", "RefTitle": "High Memory Usage in Allocator Connection/XXXXXX/Statement/YYYYYYYY/IMPLICIT by User _SYS_STATISTICS", "RefUrl": "/notes/2584388 "}, {"RefNumber": "2583885", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Query::param_t::get_ptr_at During Expensive Statement Tracing if SQL Statement Fails", "RefUrl": "/notes/2583885 "}, {"RefNumber": "2581786", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TrexCalculationEngine::ceJoinPop::execute", "RefUrl": "/notes/2581786 "}, {"RefNumber": "2581728", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TransactionManager::SubTransactionData::cleanup()", "RefUrl": "/notes/2581728 "}, {"RefNumber": "2581702", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Hierarchies::HierarchyBlob::Hierarchy::mergeNotAssignedHierarchy", "RefUrl": "/notes/2581702 "}, {"RefNumber": "2581540", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Transaction::getSessionContext While Using Embedded XSEngine", "RefUrl": "/notes/2581540 "}, {"RefNumber": "2580590", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes After Failing Table Move Operation with DataContainer::VarSizeEntryContainerImpl::freeEntryImpl", "RefUrl": "/notes/2580590 "}, {"RefNumber": "2580609", "RefComponent": "HAN-DB-HA", "RefTitle": "Service on Secondary Site Crashes After Full Data Shipment", "RefUrl": "/notes/2580609 "}, {"RefNumber": "2580557", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::BtreeAttribute::Delta::DeltaBTree::BTree_Key::findLeafNode When Reaching Columnstore Rowlimit", "RefUrl": "/notes/2580557 "}, {"RefNumber": "2275745", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash on Coordinator Node of ScaleOut Environment During PlanViz Execution or Plan Trace Creation", "RefUrl": "/notes/2275745 "}, {"RefNumber": "2580168", "RefComponent": "HAN-DB", "RefTitle": "Service Crashes in MemoryManager::callAllocationOverflowError", "RefUrl": "/notes/2580168 "}, {"RefNumber": "2579371", "RefComponent": "HAN-DB-BAC", "RefTitle": "SAP HANA Service Crashes With Assertion \"!(fillSize % PageAccess::getPageSize(PageAccess::SIZE_CLASS_MAX))\" When Backup to Backint Partly Fails due to Connection Issue", "RefUrl": "/notes/2579371 "}, {"RefNumber": "2578019", "RefComponent": "HAN-DB-HA", "RefTitle": "Service Crashes in DataAccess::PersistenceManagerImpl::endOfDataRecovery", "RefUrl": "/notes/2578019 "}, {"RefNumber": "2577314", "RefComponent": "HAN-DB", "RefTitle": "Possible Row Store Inconsistency on IBM Power Platform", "RefUrl": "/notes/2577314 "}, {"RefNumber": "2576669", "RefComponent": "HAN-DB-HA", "RefTitle": "Increasing Log Volume Size on Secondary Site of a Multitier System Replication Setup With Operation Mode Logreplay", "RefUrl": "/notes/2576669 "}, {"RefNumber": "2576611", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash After Metadata::StorageWrapperImpl::getVersionCid", "RefUrl": "/notes/2576611 "}, {"RefNumber": "2575979", "RefComponent": "HAN-DB", "RefTitle": "Precision and Scale Checking on DECIMAL Column Could be Bypassed When INSERT Into Column Table", "RefUrl": "/notes/2575979 "}, {"RefNumber": "2572012", "RefComponent": "HAN-DB", "RefTitle": "Reconnect of Secondary Site and Recreation of Log Segment Directory File Fail When Log Segment Size Larger Than 1GB", "RefUrl": "/notes/2572012 "}, {"RefNumber": "2434600", "RefComponent": "HAN-DB-HA", "RefTitle": "Switching from operation mode logreplay to delta datashipping can cause corrupted data", "RefUrl": "/notes/2434600 "}, {"RefNumber": "2566786", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Crash During Delta Merge or Normal Read/Write Due to Inconsistent Column-Store Table", "RefUrl": "/notes/2566786 "}, {"RefNumber": "2570439", "RefComponent": "HAN-DB", "RefTitle": "HANA is not Coming up After Abnormal Termination of Data Import With Log Mode Overwrite", "RefUrl": "/notes/2570439 "}, {"RefNumber": "2566685", "RefComponent": "HAN-DB-SDA", "RefTitle": "Indexserver Crash at ptime::FederationQuery::convertCISBinarytoITab When Using Smart Data Access", "RefUrl": "/notes/2566685 "}, {"RefNumber": "2567235", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash During Query Compilation on ptime::ColumnSearch::ColumnJoin::addJoinLocalCondition", "RefUrl": "/notes/2567235 "}, {"RefNumber": "2564712", "RefComponent": "HAN-DB-BAC", "RefTitle": "Point in Time Recovery Hanging When Performing Multiple Recovery Attempts", "RefUrl": "/notes/2564712 "}, {"RefNumber": "2564032", "RefComponent": "HAN-DB", "RefTitle": "Crash in ptime::codegen_qp2so::addStmtBeforeCurrent When Creating SQL Script Procedure Which Uses Array Parameter in Where Clause", "RefUrl": "/notes/2564032 "}, {"RefNumber": "2402786", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at UnifiedTable::DeltaBitVec::clearBits During Conccurent DML Operations on a Column Store Table", "RefUrl": "/notes/2402786 "}, {"RefNumber": "2557912", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in ptime::ControlFlowGraphBuilder::generate When Calling a Procedure", "RefUrl": "/notes/2557912 "}, {"RefNumber": "2559204", "RefComponent": "HAN-DB", "RefTitle": "Misleading \"Unsupported file System ext4\" Warnings on Standby Host", "RefUrl": "/notes/2559204 "}, {"RefNumber": "2558785", "RefComponent": "HAN-DB-ENG-TXT", "RefTitle": "Indexserver Crash at AttributeEngine::(anonymous namespace)::filterByLanguage", "RefUrl": "/notes/2558785 "}, {"RefNumber": "2558613", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database Service Crashes at Execution::JobExecutorImpl::createJob During Start of System Replication Listener", "RefUrl": "/notes/2558613 "}, {"RefNumber": "2558117", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at  __strcmp_sse2_unaligned When Executing a Query That Contains a Subquery", "RefUrl": "/notes/2558117 "}, {"RefNumber": "2557493", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Crash in TransactionManager::CSRecordLockInfo::getLockMode", "RefUrl": "/notes/2557493 "}, {"RefNumber": "2556362", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash After Hierarchies::HierarchyBlob::Hierarchy::getParentChildColumnNames When Hierarchy Views are Queried While a new Version is Deployed", "RefUrl": "/notes/2556362 "}, {"RefNumber": "2555833", "RefComponent": "HAN-DB", "RefTitle": "Index Server Crash at MemoryManager::MemoryPool::deallocate", "RefUrl": "/notes/2555833 "}, {"RefNumber": "2555376", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash When Running a Parallel Query in SAP HANA  Scale Out Configuration", "RefUrl": "/notes/2555376 "}, {"RefNumber": "2554185", "RefComponent": "HAN-DB", "RefTitle": "Obsolete Entries in Topology After MDC Conversion Lead to Failures of SLD Registration or Fulltext index Synchronization or False INI File Mismatch Alerts", "RefUrl": "/notes/2554185 "}, {"RefNumber": "2553642", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Results Returned for SQL Query on Partitioned Table When Guided Navigation is Used", "RefUrl": "/notes/2553642 "}, {"RefNumber": "2543566", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Studio DB-Properties - License displayes: System locked; licensed memory has been exceeded / Time at OS-Level changed to future", "RefUrl": "/notes/2543566 "}, {"RefNumber": "2550152", "RefComponent": "HAN-DB-SDA", "RefTitle": "Indexserver Crash in federation::FederationSqlGenerator::GetCaps While Executing a SDA Query", "RefUrl": "/notes/2550152 "}, {"RefNumber": "2549632", "RefComponent": "HAN-DB", "RefTitle": "Defects When SAP HANA Time Series Data are Used With Compression Type LRLE", "RefUrl": "/notes/2549632 "}, {"RefNumber": "2549180", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Constraint Check Failure during UPDATE statement", "RefUrl": "/notes/2549180 "}, {"RefNumber": "2549013", "RefComponent": "HAN-DB", "RefTitle": "Hanging Situation During Record Lock Contention", "RefUrl": "/notes/2549013 "}, {"RefNumber": "2548563", "RefComponent": "HAN-DB", "RefTitle": "User Filter for Performance Trace is Disabled After Reconfigure", "RefUrl": "/notes/2548563 "}, {"RefNumber": "2546137", "RefComponent": "HAN-DB", "RefTitle": "After Failover to Standby the Error \"failed to send listPlan request\" is Repeated Many Times in the Indexserver Trace", "RefUrl": "/notes/2546137 "}, {"RefNumber": "2536290", "RefComponent": "HAN-DB", "RefTitle": "XSEngine Crash when at Least Two OData finish at The Same Time.", "RefUrl": "/notes/2536290 "}, {"RefNumber": "2547516", "RefComponent": "HAN-DB", "RefTitle": "Consistency Check Execution Causes Growth of Pool/malloc/libhdbbasement.so", "RefUrl": "/notes/2547516 "}, {"RefNumber": "2547543", "RefComponent": "HAN-DB", "RefTitle": "High CPU Used by DataContainer::VarSizeEntryUserDataHandler::getUnusedEntryFromFreeList", "RefUrl": "/notes/2547543 "}, {"RefNumber": "2547082", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in RowSegment::SegAddrMap::printNullSegmentError During Offline Row Store Reorganization", "RefUrl": "/notes/2547082 "}, {"RefNumber": "2546890", "RefComponent": "HAN-DB", "RefTitle": "Dpserver Crashes at DPFramework::AgentPool::removeAgent When Data Provisioning Agent is Down", "RefUrl": "/notes/2546890 "}, {"RefNumber": "2543762", "RefComponent": "HAN-DB", "RefTitle": "Indexsever Crashes at onParallelReadWriteTransScope(bool) When Executing Nested Procedure With Parallel Update Execution Blocks", "RefUrl": "/notes/2543762 "}, {"RefNumber": "2427582", "RefComponent": "HAN-DB-SCR", "RefTitle": "Indexserver crash on Metadata::RSViewInfo::searchViewNameIndex", "RefUrl": "/notes/2427582 "}, {"RefNumber": "2544075", "RefComponent": "HAN-DB-HA", "RefTitle": "HSR Primary System Unresponsive After Connection to Secondary Site is Lost", "RefUrl": "/notes/2544075 "}, {"RefNumber": "2544123", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash Due To Stack Overflow Caused By Hundrets of ptime::qo_Disj::disambiguate Calls", "RefUrl": "/notes/2544123 "}, {"RefNumber": "2543652", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::BtreeAttribute::BTreeIndex::flushInsertBuf During Delta Merge With Large Delta (> 1.5 Billion Rows)", "RefUrl": "/notes/2543652 "}, {"RefNumber": "2543470", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash After TrexCalculationEngine::ceQOBuilder::buildNode", "RefUrl": "/notes/2543470 "}, {"RefNumber": "2543448", "RefComponent": "HAN-DB", "RefTitle": "Crash in ptime::RowStoreTransactionCallback::replayQueryOpenTransactionState", "RefUrl": "/notes/2543448 "}, {"RefNumber": "2543398", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in JoinEvaluator::mapIVA_native<int, int>", "RefUrl": "/notes/2543398 "}, {"RefNumber": "2542932", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Crash at ptime::iterative_find_exp_ftc When Executing a SQL Statement Which Calls a UDF", "RefUrl": "/notes/2542932 "}, {"RefNumber": "2404082", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at UnifiedTable::MVCCWriter::iterator::loadImpl When a DML and a Delta Merge Operation Run Concurrently", "RefUrl": "/notes/2404082 "}, {"RefNumber": "2534844", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash During Startup due to Insufficient Shared Memory Segment", "RefUrl": "/notes/2534844 "}, {"RefNumber": "2537553", "RefComponent": "HAN-DB", "RefTitle": "Performance Issue due to Contention on Huge Alignment Allocator", "RefUrl": "/notes/2537553 "}, {"RefNumber": "2541463", "RefComponent": "HAN-DB", "RefTitle": "Auto Optimize Compression is Disabled for Imported Column Store Tables", "RefUrl": "/notes/2541463 "}, {"RefNumber": "2541336", "RefComponent": "HAN-DB-HA", "RefTitle": "Crash with Exception \"Error in Mutex destructor: locked unexpected name=Lo<PERSON>ForceLogBackup\" During Takeover", "RefUrl": "/notes/2541336 "}, {"RefNumber": "2539853", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::BtreeAttribute::MultiValueIndex::switchSearch When Executing Query on a Text Column", "RefUrl": "/notes/2539853 "}, {"RefNumber": "2539573", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash due to PlanViz Query Visualization", "RefUrl": "/notes/2539573 "}, {"RefNumber": "2539459", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TRexAPI::LateMatColumnSource::~LateMatColumnSource When Inserting/Upserting Into Columnstore Table Fails With an Error", "RefUrl": "/notes/2539459 "}, {"RefNumber": "2538199", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash on IBM Power Platform Because of Failed Assertion \"!(old_state & JobStateAnyToken)\"", "RefUrl": "/notes/2538199 "}, {"RefNumber": "2537251", "RefComponent": "HAN-DB-SEC", "RefTitle": "Indexserver Crash at PSEInfo::setPrivateKey() While Setting an Own Certificate in PSE", "RefUrl": "/notes/2537251 "}, {"RefNumber": "2537059", "RefComponent": "HAN-DB", "RefTitle": "Accessing the Column Table Which has More Than 2147483645 Records Failed With \"Udiv overflow\"", "RefUrl": "/notes/2537059 "}, {"RefNumber": "2536769", "RefComponent": "HAN-DB", "RefTitle": "Query execution is slow because a filter was not pushed down", "RefUrl": "/notes/2536769 "}, {"RefNumber": "2536154", "RefComponent": "HAN-DB", "RefTitle": "Querying a Calculation View Using Currency Conversion in an MDC Environment Fails or Returns Unexpected Results", "RefUrl": "/notes/2536154 "}, {"RefNumber": "2536039", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash Because of \"Using consistent view MVCCCV[snapshot=XXXXXXXX] from a different transaction for session Session\"", "RefUrl": "/notes/2536039 "}, {"RefNumber": "2535996", "RefComponent": "HAN-DB", "RefTitle": "Crash in OLAP Engine QueryMediator::FilterTransportOptimization::apply", "RefUrl": "/notes/2535996 "}, {"RefNumber": "2535286", "RefComponent": "HAN-DB-ENG", "RefTitle": "Unexpected Results From a LEFT OUTER JOIN With NOT EQUAL Join Condition", "RefUrl": "/notes/2535286 "}, {"RefNumber": "2535110", "RefComponent": "HAN-DB", "RefTitle": "Memory Leak on Pool/parallel/compactcol and Pool/parallel/aggregates or Pool/itab", "RefUrl": "/notes/2535110 "}, {"RefNumber": "2534341", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes After ptime::CSVWriterThread::run() During CSV Export", "RefUrl": "/notes/2534341 "}, {"RefNumber": "2533352", "RefComponent": "HAN-DB-ENG", "RefTitle": "Memory Leak on \"Pool/JoinEvaluator/JERequestedAttributes/Results\"", "RefUrl": "/notes/2533352 "}, {"RefNumber": "2501814", "RefComponent": "HAN-DB-MDX", "RefTitle": "MDX Query Returns Unexpected Result in Case of Unbalanced Hierarchy", "RefUrl": "/notes/2501814 "}, {"RefNumber": "2532199", "RefComponent": "HAN-DB", "RefTitle": "Optimization of the HANA Memory Allocator Pool/Statistics Usage", "RefUrl": "/notes/2532199 "}, {"RefNumber": "2532475", "RefComponent": "HAN-DB", "RefTitle": "Long Startup Time Due to Long Running Garbage Collection", "RefUrl": "/notes/2532475 "}, {"RefNumber": "2532330", "RefComponent": "HAN-DB-SEC", "RefTitle": "Indexserver Crash at sec_SecCipher_more_GCM", "RefUrl": "/notes/2532330 "}, {"RefNumber": "2529937", "RefComponent": "HAN-DB", "RefTitle": "Import of a Hybrid LOB Column Blocks Savepoint Process for a Longer Period of Time", "RefUrl": "/notes/2529937 "}, {"RefNumber": "2523627", "RefComponent": "HAN-DB", "RefTitle": "Index Server Crashes When a \"INSERT INTO ... VALUES(SELECT...)\"  Statement is Executed", "RefUrl": "/notes/2523627 "}, {"RefNumber": "2523692", "RefComponent": "HAN-DB", "RefTitle": "NULL Value is Inserted Into NOT NULL Columns by 'INSERT INTO ... VALUES(SELECT...)'", "RefUrl": "/notes/2523692 "}, {"RefNumber": "2527251", "RefComponent": "HAN-DB", "RefTitle": "Memory Leak in Pool/RowEngine/QueryCompilation", "RefUrl": "/notes/2527251 "}, {"RefNumber": "2517029", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash Involving NetworkChannel", "RefUrl": "/notes/2517029 "}, {"RefNumber": "2522580", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Secondary(New Primary) Site During Takeover With \"Assertion failed: !reassignShm || oldShmID == sharedMemoryID\"", "RefUrl": "/notes/2522580 "}, {"RefNumber": "2518952", "RefComponent": "HAN-DB", "RefTitle": "Indexsever Crashes With \"Trying to write on a persistence session\" When Activating a Role", "RefUrl": "/notes/2518952 "}, {"RefNumber": "2518701", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash in PlanningEngine::pleBuildRefDictInternal", "RefUrl": "/notes/2518701 "}, {"RefNumber": "2517443", "RefComponent": "HAN-DB", "RefTitle": "Filter push down missing for TREXviaDBSL calls on Hana native calculation view when FEMS are used", "RefUrl": "/notes/2517443 "}, {"RefNumber": "2516807", "RefComponent": "HAN-DB", "RefTitle": "jeReadIndex on Compressed Column of Index Type FULL is Slow", "RefUrl": "/notes/2516807 "}, {"RefNumber": "2516024", "RefComponent": "HAN-DB", "RefTitle": "System Replication on Secondary Site Does Not Proceed in Delta Datashipping Operation Mode", "RefUrl": "/notes/2516024 "}, {"RefNumber": "2512208", "RefComponent": "HAN-DB", "RefTitle": "SQL Query on Rowstore Table fails with PersistVector index out of range", "RefUrl": "/notes/2512208 "}, {"RefNumber": "2463884", "RefComponent": "HAN-DB", "RefTitle": "SQL error \"SQL code: -10108\" Occurred While Accessing Table After Failover From Worker Node to Standby", "RefUrl": "/notes/2463884 "}, {"RefNumber": "2143937", "RefComponent": "HAN-DB", "RefTitle": "Use This as a Template for SAP HANA Database SAP Notes from Development Support", "RefUrl": "/notes/2143937 "}, {"RefNumber": "2497655", "RefComponent": "HAN-DB-R", "RefTitle": "SAP HANA R: HANA - R Connections Closed Unexpectedly due to large TCP Keepalive Value", "RefUrl": "/notes/2497655 "}, {"RefNumber": "2507106", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA indexserver crash with <PERSON>ser<PERSON> failed: blockIdx <= mvccObject.getTSIndex(..)", "RefUrl": "/notes/2507106 "}, {"RefNumber": "2501997", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Crash at ptime::DevLeakedRSPages::processStatistics", "RefUrl": "/notes/2501997 "}, {"RefNumber": "2504438", "RefComponent": "HAN-DB", "RefTitle": "Child Process of Webdispatcher Hanging in Defunct State", "RefUrl": "/notes/2504438 "}, {"RefNumber": "2495375", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes in sse_icc_lib::mgeti_AVX2 After Changing Underlying CPU", "RefUrl": "/notes/2495375 "}, {"RefNumber": "2495165", "RefComponent": "HAN-DYT", "RefTitle": "Indexserver may Fail to Restart after Upgrading DT to SAP Hana 2.0", "RefUrl": "/notes/2495165 "}, {"RefNumber": "2238679", "RefComponent": "HAN-DB", "RefTitle": "High CPU Consumption Caused by UnifiedTable::MVCCObject::generateOLAPBitmapMVCC", "RefUrl": "/notes/2238679 "}, {"RefNumber": "2489444", "RefComponent": "HAN-DB", "RefTitle": "Stopping Strace of Indexserver Thread Causes Standstill Situation", "RefUrl": "/notes/2489444 "}, {"RefNumber": "2489363", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::TrexResultIMS::getFixed12Value", "RefUrl": "/notes/2489363 "}, {"RefNumber": "2485022", "RefComponent": "HAN-DB", "RefTitle": "Connection Leakage in Tenant of MDC System Caused by Statistics Service Monitoring Queries", "RefUrl": "/notes/2485022 "}, {"RefNumber": "2481352", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes With Error \"ParallelObjectId does not match!\" at UnifiedTable::TableContainerSPI::setAEMainContainerID", "RefUrl": "/notes/2481352 "}, {"RefNumber": "2329108", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ssl3_write_bytes With SSL Enabled", "RefUrl": "/notes/2329108 "}, {"RefNumber": "2434562", "RefComponent": "HAN-DB-HA", "RefTitle": "System Replication Hanging in Status \"SYNCING\" or \"ERROR\" With Status Detail \"Missing Log\" or \"Invalid backup size\"", "RefUrl": "/notes/2434562 "}, {"RefNumber": "2477482", "RefComponent": "HAN-DB-ENG", "RefTitle": "\"Storage object does not exist: DISK_BLOB:0:000000000\" Encountered After Table Repartitioning", "RefUrl": "/notes/2477482 "}, {"RefNumber": "2477165", "RefComponent": "HAN-DB-ENG", "RefTitle": "SAP HANA Database Service Crashes at ptime::Connection::~Connection() During Database Shutdown", "RefUrl": "/notes/2477165 "}, {"RefNumber": "2476109", "RefComponent": "HAN-DB", "RefTitle": "Query With a Join Fails with Error: rc -1; message not found", "RefUrl": "/notes/2476109 "}, {"RefNumber": "2474345", "RefComponent": "HAN-DB-ENG", "RefTitle": "CE Model Cache is Invalidated Periodically on all Worker Nodes", "RefUrl": "/notes/2474345 "}, {"RefNumber": "2473480", "RefComponent": "HAN-DB-HA", "RefTitle": "Disk LOB data not available after Table Move and System Replication Takeover", "RefUrl": "/notes/2473480 "}, {"RefNumber": "2473074", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at UnifiedTable::DeltaVarDictionaryStorage After Upgrading to SAP HANA Database Revision of SPS11", "RefUrl": "/notes/2473074 "}, {"RefNumber": "2472783", "RefComponent": "HAN-DB-ENG", "RefTitle": "Improve NUMA Behavior of Large Scale Systems on SAP HANA 1.0 database SPS12", "RefUrl": "/notes/2472783 "}, {"RefNumber": "2470758", "RefComponent": "HAN-DB", "RefTitle": "Application User Information not Available When Resolving Procedure-Based Variable Within Calculation Engine", "RefUrl": "/notes/2470758 "}, {"RefNumber": "2469706", "RefComponent": "HAN-DB-ENG", "RefTitle": "Incorrectly Grouped Result Set for Queries With a Join Involving a Partitioned Table", "RefUrl": "/notes/2469706 "}, {"RefNumber": "2468839", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ltt::operator<< When Using The Command hdbcons 'log info' During Server Startup", "RefUrl": "/notes/2468839 "}, {"RefNumber": "2468680", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at federation::OdbcAccess::MapType When Accessing a Virtual Table Including an Unsupported Datatype Using Smart Data Access", "RefUrl": "/notes/2468680 "}, {"RefNumber": "2462236", "RefComponent": "HAN-DB", "RefTitle": "Initialization of a Service Fails With the Error Message \"Error message : fatal error: exceed maximum value of OID\"", "RefUrl": "/notes/2462236 "}, {"RefNumber": "2461276", "RefComponent": "HAN-STD-ADM-DBA", "RefTitle": "Parameters in the Table Redistribution Options Cannot be Changed", "RefUrl": "/notes/2461276 "}, {"RefNumber": "2462174", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::SparkSQLQuery::execute When Using Debug Level of Fedtrace With SDA to Apache Spark Database", "RefUrl": "/notes/2462174 "}, {"RefNumber": "2460995", "RefComponent": "HAN-DB", "RefTitle": "Index server crash at TrexCalculationEngine::NodeInstantiate::processAggregationOp", "RefUrl": "/notes/2460995 "}, {"RefNumber": "2460816", "RefComponent": "HAN-DB", "RefTitle": "Execution of Regex-rSQLScript with error \"internal error: Compile failed: CompilationFailedException: No details\"", "RefUrl": "/notes/2460816 "}, {"RefNumber": "2460504", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Indexserver Crash at ptime::Connection::getAbapVarcharMode", "RefUrl": "/notes/2460504 "}, {"RefNumber": "2460123", "RefComponent": "HAN-DB-ENG", "RefTitle": "SAP HANA Database is Unresponsive due to Parallel Login Requests", "RefUrl": "/notes/2460123 "}, {"RefNumber": "2458531", "RefComponent": "HAN-DB", "RefTitle": "Long Running GRANT or REVOKE Statements", "RefUrl": "/notes/2458531 "}, {"RefNumber": "2458366", "RefComponent": "HAN-DB", "RefTitle": "Wrong Session Context Lead to Sporadic Errors", "RefUrl": "/notes/2458366 "}, {"RefNumber": "2457669", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Indexserver Crash at JoinEvaluator::JoinEvaluator::checkParts", "RefUrl": "/notes/2457669 "}, {"RefNumber": "2455792", "RefComponent": "HAN-DB", "RefTitle": "Dangling external transactions", "RefUrl": "/notes/2455792 "}, {"RefNumber": "2455763", "RefComponent": "HAN-DB", "RefTitle": "System replication takeover failed with error Invalid logical page number", "RefUrl": "/notes/2455763 "}, {"RefNumber": "2447887", "RefComponent": "HAN-DB", "RefTitle": "Removing Multiple Services from HANA Scale Out Environment Can Lead to Data Loss", "RefUrl": "/notes/2447887 "}, {"RefNumber": "2437999", "RefComponent": "HAN-DB-MON", "RefTitle": "Monitoring View M_EXPENSIVE_STATEMENTS Shows an Incorrect ERROR_CODE for a Recorded Request", "RefUrl": "/notes/2437999 "}, {"RefNumber": "2444315", "RefComponent": "HAN-DB", "RefTitle": "SQL Function LOCATE Fails With \"feature not supported\" When Searching Backwards For an Occurrence Other Than the First one", "RefUrl": "/notes/2444315 "}, {"RefNumber": "2443575", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::PlanInfo::getBufferId During PlanViz Execution or Plan Trace Creation", "RefUrl": "/notes/2443575 "}, {"RefNumber": "2443040", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at UnifiedTable::FragmentObject::getMaxRowID While Checking a Corrupt Partitioned Columnstore Table for Consistency", "RefUrl": "/notes/2443040 "}, {"RefNumber": "2308083", "RefComponent": "HAN-DB", "RefTitle": "Disk Size of Table CS_AUDIT_LOG_ Huge Even After Clearing", "RefUrl": "/notes/2308083 "}, {"RefNumber": "2426040", "RefComponent": "HAN-DB-SCR", "RefTitle": "Creating a Table With a Foreign Key Constraint to Itself Fails With \"Error: invalid table name: TREE\"", "RefUrl": "/notes/2426040 "}, {"RefNumber": "2440717", "RefComponent": "HAN-DB-ENG", "RefTitle": "Crash at ptime::PageRelocatorForRestart::getEqualOr<PERSON>owestUpperPointer", "RefUrl": "/notes/2440717 "}, {"RefNumber": "2440532", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::RSTableMonitor::updateOwnValueInMemory When Looking up Local Temporary Tables in M_TABLES", "RefUrl": "/notes/2440532 "}, {"RefNumber": "2440094", "RefComponent": "HAN-DB-HA", "RefTitle": "Indexserver Crash at ptime::Var::is_sane When Re-registering HANA System Replication After Using \"-sr_cleanup --force\"", "RefUrl": "/notes/2440094 "}, {"RefNumber": "2392153", "RefComponent": "HAN-DB-CLI", "RefTitle": "Data truncated error appears when trying to connect Microsoft Excel with a SAP HANA database using Microsoft Query", "RefUrl": "/notes/2392153 "}, {"RefNumber": "2436931", "RefComponent": "HAN-DB", "RefTitle": "Quick Sequence of sr_disable and sr_enable Commands Over Short Period of Time Can Lead to Unintentional Stop of System Replication", "RefUrl": "/notes/2436931 "}, {"RefNumber": "2436619", "RefComponent": "HAN-DB", "RefTitle": "Inconsistency After Repartitioning a Table Using Inverted Hash Indexes", "RefUrl": "/notes/2436619 "}, {"RefNumber": "2432360", "RefComponent": "HAN-DB", "RefTitle": "Unexpected Null Values in Column Store Tables in SAP HANA 1.0 SPS11/SPS12 or SAP HANA 2.0 SPS 00", "RefUrl": "/notes/2432360 "}, {"RefNumber": "2435307", "RefComponent": "HAN-DB-HA", "RefTitle": "\"deliver log position\" Error Messages on Secondary Site While Full Data Shipping is in Progress", "RefUrl": "/notes/2435307 "}, {"RefNumber": "2434657", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Futex::lock While Checking a Corrupt Rowstore Table for Consistency", "RefUrl": "/notes/2434657 "}, {"RefNumber": "2420519", "RefComponent": "HAN-DB", "RefTitle": "Error When Exporting Objects Referring to Objects Owned by User SYS With SAP HANA Content Export in \"SAP Support Mode\"", "RefUrl": "/notes/2420519 "}, {"RefNumber": "2404418", "RefComponent": "HAN-DB-HA", "RefTitle": "System Replication Takeover is not Completing due to a Previously Interrupted Takeover", "RefUrl": "/notes/2404418 "}, {"RefNumber": "2427296", "RefComponent": "HAN-DB", "RefTitle": "HANA TrexNet BadParam//channel not in list", "RefUrl": "/notes/2427296 "}, {"RefNumber": "2425682", "RefComponent": "HAN-DB-HA", "RefTitle": "Logreplay hanging on secondary site", "RefUrl": "/notes/2425682 "}, {"RefNumber": "2421600", "RefComponent": "HAN-DB", "RefTitle": "Move Column Store Table Fails with  'Could not find source UT container in objectInfos'", "RefUrl": "/notes/2421600 "}, {"RefNumber": "2419889", "RefComponent": "HAN-DB", "RefTitle": "Alert for High Database Disk Usage in SAP HANA", "RefUrl": "/notes/2419889 "}, {"RefNumber": "2399151", "RefComponent": "HAN-DB-ENG", "RefTitle": "ALTER TABLE <table_name> PARTITION BY <partition_expression> Runs for a Long Time and Blocks the Savepoint", "RefUrl": "/notes/2399151 "}, {"RefNumber": "2416519", "RefComponent": "HAN-DB", "RefTitle": "Correction for update column table in SQLScript", "RefUrl": "/notes/2416519 "}, {"RefNumber": "2416014", "RefComponent": "HAN-DB", "RefTitle": "Log Segments With 'RetainedFree'  in the Former Secondary and Third Systems", "RefUrl": "/notes/2416014 "}, {"RefNumber": "2414438", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Import Row Store Table Fails with Error \"Import finalization failed;finalizeImportAll has failed\"", "RefUrl": "/notes/2414438 "}, {"RefNumber": "2386154", "RefComponent": "HAN-DB", "RefTitle": "CHECK_CATALOG detects inconsistencies of type in Metadata::GrantedPrivInfo with objectId <oID> there is a type mismatch between 2 and MONITORVIEW", "RefUrl": "/notes/2386154 "}, {"RefNumber": "2412682", "RefComponent": "HAN-DB-HA", "RefTitle": "HANA System Replication hangs on secondary site after restart", "RefUrl": "/notes/2412682 "}, {"RefNumber": "2409618", "RefComponent": "HAN-DB", "RefTitle": "Thread Hangs in __lll_lock_wait+0x20", "RefUrl": "/notes/2409618 "}, {"RefNumber": "2408032", "RefComponent": "HAN-DB", "RefTitle": "Reading From LOB Binary Stream After Closing the ResultSet via JDBC Fails With Error \"invalid lob locator id (piecewise lob reading)\"", "RefUrl": "/notes/2408032 "}, {"RefNumber": "2405237", "RefComponent": "HAN-DB", "RefTitle": "Memory Leak in Pool/planviz/common/strings When Using Plan Trace", "RefUrl": "/notes/2405237 "}, {"RefNumber": "2405763", "RefComponent": "HAN-DB-HA", "RefTitle": "SAP HANA DB: Log <PERSON>lay on HSR Secondary Site Hangs", "RefUrl": "/notes/2405763 "}, {"RefNumber": "2405212", "RefComponent": "HAN-DB", "RefTitle": "System Crashes During Start After Hanging Situation When a Full System Info Dump was Triggered", "RefUrl": "/notes/2405212 "}, {"RefNumber": "2403088", "RefComponent": "HAN-DB", "RefTitle": "Join Engine Query Returns Unexpected Results in Aggregation of Large Intermediate Result Sets in 122.04", "RefUrl": "/notes/2403088 "}, {"RefNumber": "2402833", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at PageAccess::ConverterPageControlBlock::setModified", "RefUrl": "/notes/2402833 "}, {"RefNumber": "2402771", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash After ptime::Catalog::getFieldInfo When Executing Select Statement", "RefUrl": "/notes/2402771 "}, {"RefNumber": "2402615", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at Basis::strcasecmp When Using hdbcons With an Incomplete Subcommand", "RefUrl": "/notes/2402615 "}, {"RefNumber": "2350932", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash ptime::BuiltinProcedure::getExecuteAtMaster() with Signal 11", "RefUrl": "/notes/2350932 "}, {"RefNumber": "2400798", "RefComponent": "HAN-DB-SEC", "RefTitle": "Reoccurence of Insufficient Privilege Error After Each Database Restart", "RefUrl": "/notes/2400798 "}, {"RefNumber": "2401093", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Indexserver crash with exception - ptime::BLocator::getConsistentView", "RefUrl": "/notes/2401093 "}, {"RefNumber": "2400515", "RefComponent": "HAN-DB", "RefTitle": "Systemfreeze with high CPU during Deadlock Situations", "RefUrl": "/notes/2400515 "}, {"RefNumber": "2399114", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::TrexStructuredPrivHandlerFactory::destroy() When Accessing a View With Analytic Privileges", "RefUrl": "/notes/2399114 "}, {"RefNumber": "2391552", "RefComponent": "HAN-DB", "RefTitle": "Heap Memory of Allocator Pool/RowStoreTables/LockTable is not Released", "RefUrl": "/notes/2391552 "}, {"RefNumber": "2392594", "RefComponent": "HAN-DB", "RefTitle": "Possible Wrong Results With Like Predicate", "RefUrl": "/notes/2392594 "}, {"RefNumber": "2380351", "RefComponent": "HAN-DB", "RefTitle": "Point in Time Recovery Skips Volume With Information: \"Volume <x> does not participate in recovery, because it was removed before target point in time\"", "RefUrl": "/notes/2380351 "}, {"RefNumber": "2387680", "RefComponent": "HAN-DB-ENG", "RefTitle": "Possible Wrong Results of Queries executed on Partitioned Tables With Active Runtime Pruning", "RefUrl": "/notes/2387680 "}, {"RefNumber": "2389132", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ResourceManager::ResourceContainerImpl::testEquivTryAndIncreaseRefWithDisp", "RefUrl": "/notes/2389132 "}, {"RefNumber": "2385992", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Hanging Connection Requests When Trying to Open Secure Connections to HANA in Parallel", "RefUrl": "/notes/2385992 "}, {"RefNumber": "2385658", "RefComponent": "HAN-DB", "RefTitle": "Possible Data Corruption after Activating Data in advanced DataStore Objects", "RefUrl": "/notes/2385658 "}, {"RefNumber": "2381769", "RefComponent": "HAN-DB-ENG", "RefTitle": "SAP HANA DB: Indexserver Crash at ptime::PageHeader::getOffset", "RefUrl": "/notes/2381769 "}, {"RefNumber": "2381750", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Catalog::getOwnerOidFromConnection With Signal 11 or Signal 24", "RefUrl": "/notes/2381750 "}, {"RefNumber": "2381733", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash During BW Fact Table Compression After Creating or Adjusting Workload Classes", "RefUrl": "/notes/2381733 "}, {"RefNumber": "2380961", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at  transientData.m_ValueBlocks.size() > 0 After Enabling Data Aging", "RefUrl": "/notes/2380961 "}, {"RefNumber": "2380217", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at UnifiedTable::DeltaVarDictionaryStorage<UnifiedTable::String>::flush With Assertion failed: \"blockPtr.is_valid()\" or \"blockPtr->getFirstVid() == h->m_StartValueID\"", "RefUrl": "/notes/2380217 "}, {"RefNumber": "2378141", "RefComponent": "HAN-DB-CLI", "RefTitle": "The SAP HANA client and server are on different Endian (byte order) platforms: client info values could be incorrect", "RefUrl": "/notes/2378141 "}, {"RefNumber": "2379010", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash on qo3::Node::sortByEstimation", "RefUrl": "/notes/2379010 "}, {"RefNumber": "2375691", "RefComponent": "HAN-DB-ENG", "RefTitle": "Possible Column Store Table Corruption", "RefUrl": "/notes/2375691 "}, {"RefNumber": "2375732", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crash in TransactionManager::PersistenceSessionContainer::logPrepareCommit", "RefUrl": "/notes/2375732 "}, {"RefNumber": "2375267", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash At UnifiedTable::MVCCCrossFragmentOperations::copyCTSBlockDelta", "RefUrl": "/notes/2375267 "}, {"RefNumber": "2374829", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at mdx::processor::HierarchyExpression::getAggregationExpressions When Using Wrong Parameter Type in MDX Function LastPeriods", "RefUrl": "/notes/2374829 "}, {"RefNumber": "2363632", "RefComponent": "HAN-DB-ENG", "RefTitle": "Indexserver Errors of jeSearchValueIdCounts failed for attribute", "RefUrl": "/notes/2363632 "}, {"RefNumber": "2295696", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Data display problem in case of CUBES after IMO conversion", "RefUrl": "/notes/2295696 "}, {"RefNumber": "2374526", "RefComponent": "HAN-DB", "RefTitle": "XS Engine Crash at odata::SimplePropertiesIterator::next When Executing an http PUT Operation on a Table Without Non-Key Fields", "RefUrl": "/notes/2374526 "}, {"RefNumber": "2374328", "RefComponent": "HAN-DB", "RefTitle": "Query does not Return any Results when Using LIMIT and OFFSET", "RefUrl": "/notes/2374328 "}, {"RefNumber": "2374124", "RefComponent": "HAN-DB-SEC", "RefTitle": "Upgrade or restart of SAP HANA Database failed with internal error \"There can be only on type of dependencies under the same object\"", "RefUrl": "/notes/2374124 "}, {"RefNumber": "2373312", "RefComponent": "HAN-DB", "RefTitle": "Upsert on Partitioned Columnstore Table is Slow With Many Threads in TrexStore::UdivListContainerMVCC::checkValidEqualSSN", "RefUrl": "/notes/2373312 "}, {"RefNumber": "2371292", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at UnifiedTable::MainVarDictionaryStorageBase::getImpl When Accessing Table where Data Aging is Activated", "RefUrl": "/notes/2371292 "}, {"RefNumber": "2370684", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Transaction::getMetadataContext with Signal 11", "RefUrl": "/notes/2370684 "}, {"RefNumber": "2368981", "RefComponent": "HAN-DB-ENG", "RefTitle": "SAP HANA DB: Queries fail with error \"exceed maximum number of transaction\" During Normal Load", "RefUrl": "/notes/2368981 "}, {"RefNumber": "2368929", "RefComponent": "HAN-DB", "RefTitle": "Memory Leak in Pool/RowEngine/RSTempPage when Row Store Temp Tables without Variable Length Columns are Used", "RefUrl": "/notes/2368929 "}, {"RefNumber": "2365678", "RefComponent": "HAN-DB-ENG", "RefTitle": "Dropping a Virtual Table Fails with Error Message \"unique constraint violated: Table(P_OBJECTDEPENDENCY_), Index(<index>)\"", "RefUrl": "/notes/2365678 "}, {"RefNumber": "2365540", "RefComponent": "HAN-DB", "RefTitle": "Functions AVG() and STDDEV() Return ? / NULL Values When Used in Combination With STRING_AGG or LAST_VALUE Including an ORDER BY or GROUP BY Clause", "RefUrl": "/notes/2365540 "}, {"RefNumber": "2362759", "RefComponent": "HAN-DB-ENG", "RefTitle": "Row table memory leak on SPS11 (Rev111~112.05) and SPS12 (Rev120~122.01)", "RefUrl": "/notes/2362759 "}, {"RefNumber": "2363696", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash ptime::BuiltinProcedure::getExecuteAtMaster() with Signal 11", "RefUrl": "/notes/2363696 "}, {"RefNumber": "2295074", "RefComponent": "HAN-DB", "RefTitle": "SQL Queries Fail With The Error: \"[6906] Attribute engine index not found\"", "RefUrl": "/notes/2295074 "}, {"RefNumber": "2361979", "RefComponent": "HAN-DB", "RefTitle": "Crash at RowEngine::IntegrityChecker::hasValidContainerTableInfo during restart", "RefUrl": "/notes/2361979 "}, {"RefNumber": "2360533", "RefComponent": "HAN-DB-BAC", "RefTitle": "Multistreaming Backint-based data backups sporadically failing", "RefUrl": "/notes/2360533 "}, {"RefNumber": "2360509", "RefComponent": "HAN-DB", "RefTitle": "Converting to Multitenant Database Container Fails with \"ltt exception\" when Using Special Characters in SYSTEM Password", "RefUrl": "/notes/2360509 "}, {"RefNumber": "2360472", "RefComponent": "HAN-DB", "RefTitle": "hdbindexserver -resetUserSystem does not Accept Special Characters", "RefUrl": "/notes/2360472 "}, {"RefNumber": "2358517", "RefComponent": "HAN-DB-SCR", "RefTitle": "Indexserver Crash at ptime::so_util::getUDFLlangCode", "RefUrl": "/notes/2358517 "}, {"RefNumber": "2357713", "RefComponent": "HAN-DB-BAC", "RefTitle": "SAP HANA DB: Log backup does not start when log mode is changed from overwrite back to normal online", "RefUrl": "/notes/2357713 "}, {"RefNumber": "2353016", "RefComponent": "HAN-DB", "RefTitle": "Topologyservice is not Providing Configured SAPLOCALHOSTFULL", "RefUrl": "/notes/2353016 "}, {"RefNumber": "2351926", "RefComponent": "HAN-DB", "RefTitle": "Many \"Delta Merge was not executed successfully\" Alerts For Secondary Site of a HANA System Replication Setup", "RefUrl": "/notes/2351926 "}, {"RefNumber": "2351467", "RefComponent": "HAN-DB", "RefTitle": "Garbage Collection Fails for LOB Files of Row Store Tables", "RefUrl": "/notes/2351467 "}, {"RefNumber": "2350870", "RefComponent": "HAN-DB", "RefTitle": "Crash in \"ptime::TableInfo::getLocation\" During Calc View Execution", "RefUrl": "/notes/2350870 "}, {"RefNumber": "2348480", "RefComponent": "HAN-DB-HA", "RefTitle": "Delta merges on secondary fail repeatedly", "RefUrl": "/notes/2348480 "}, {"RefNumber": "2347988", "RefComponent": "HAN-DB", "RefTitle": "Database Expression \"ABAP_SYSTEM_TIMEZONE\" Keeps Internal Connection Open", "RefUrl": "/notes/2347988 "}, {"RefNumber": "2346215", "RefComponent": "HAN-DB", "RefTitle": "Potential Unique Constraint Violations After Upgrading From SPS8 or Lower", "RefUrl": "/notes/2346215 "}, {"RefNumber": "2346225", "RefComponent": "HAN-DB", "RefTitle": "Potential indexserver crash after upgrade from SAP HANA SPS 9 to SPS 10 or higher", "RefUrl": "/notes/2346225 "}, {"RefNumber": "2344088", "RefComponent": "HAN-DB", "RefTitle": "Nameserver Crash in \"Execution::StackAllocatorImpl::checkDeferredDeallocations\" During Log Backup Writing", "RefUrl": "/notes/2344088 "}, {"RefNumber": "2343426", "RefComponent": "HAN-DB-SEC", "RefTitle": "Indexserver Crash by Memory Corruption detected from Crypto::SSL::CommonCrypto::Engine::~Engine()", "RefUrl": "/notes/2343426 "}, {"RefNumber": "2343177", "RefComponent": "HAN-DB-ENG", "RefTitle": "Memory leak in Pool/RowEngine/MonitorView in scale-out system", "RefUrl": "/notes/2343177 "}, {"RefNumber": "2342846", "RefComponent": "HAN-DB", "RefTitle": "Crash in \"Synchronization::Mutex::~Mutex\" When Using SSL Connections", "RefUrl": "/notes/2342846 "}, {"RefNumber": "2342434", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::deserialize_fv_ConstStr in a Scale-Out Scenario", "RefUrl": "/notes/2342434 "}, {"RefNumber": "2342385", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at AttributeEngine::DictScanJobItab With Exception \"Reached unreachable code\"", "RefUrl": "/notes/2342385 "}, {"RefNumber": "2342336", "RefComponent": "HAN-DB", "RefTitle": "Saving a 10-Character Date Value Into an NVARCHAR Column of Length 8 Works Instead of Failing", "RefUrl": "/notes/2342336 "}, {"RefNumber": "2340583", "RefComponent": "HAN-DB", "RefTitle": "Scale-Out System is Hanging With Many Threads in Synchronization::ReadWriteLock::timedWaitLockExclusive and High CPU Consumption on Worker Nodes", "RefUrl": "/notes/2340583 "}, {"RefNumber": "2338565", "RefComponent": "HAN-DB", "RefTitle": "Query Fails With \"column store error: search table error: [23057] Internal error during join: invalid input\"", "RefUrl": "/notes/2338565 "}, {"RefNumber": "2338166", "RefComponent": "HAN-DB", "RefTitle": "Possible Unique Constraint Violation When Using non-ASCII Characters", "RefUrl": "/notes/2338166 "}, {"RefNumber": "2337799", "RefComponent": "HAN-DB", "RefTitle": "Nameserver Crash at Backup::BackupMgr_DeltaBackupTracker::initCheckAddedVolumesAfterBackupOrRecovery", "RefUrl": "/notes/2337799 "}, {"RefNumber": "2337798", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ltt::range_error::range_error after TRexUtils::RefCountable_TS::releaseRef", "RefUrl": "/notes/2337798 "}, {"RefNumber": "2337796", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Transaction::getMetadataContext", "RefUrl": "/notes/2337796 "}, {"RefNumber": "2337775", "RefComponent": "HAN-DB-SDA", "RefTitle": "Indexserver Crash at ptime::qo_PhysicalEnumerator::requires_virtual_rowid", "RefUrl": "/notes/2337775 "}, {"RefNumber": "2326499", "RefComponent": "HAN-DB", "RefTitle": "Crash in Method text_search::golomb_writer<text_search::block_memory>::operator=(unsigned int)", "RefUrl": "/notes/2326499 "}, {"RefNumber": "2332624", "RefComponent": "HAN-DB", "RefTitle": "CHECK_CATALOG Reports the Error 'invalid address detected: occupied slot' classid : 158", "RefUrl": "/notes/2332624 "}, {"RefNumber": "2332551", "RefComponent": "HAN-DB", "RefTitle": "High CPU Utilization due to SpinLock in query_plan_handle.cc", "RefUrl": "/notes/2332551 "}, {"RefNumber": "2330615", "RefComponent": "HAN-DB-HA", "RefTitle": "Indexserver Crash on Secondary Site with Enabled Persistence Encryption", "RefUrl": "/notes/2330615 "}, {"RefNumber": "2330164", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crashes and <PERSON>not Restart After Merging the Partitions of a Table", "RefUrl": "/notes/2330164 "}, {"RefNumber": "2330013", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Modeler Metadata in BIMC tables under _SYS_BI Schema are Inconsistent", "RefUrl": "/notes/2330013 "}, {"RefNumber": "2329036", "RefComponent": "HAN-DB", "RefTitle": "Invalid Alert \"Status of HANA platform lifecycle management configuration\", Alert ID 90", "RefUrl": "/notes/2329036 "}, {"RefNumber": "2329460", "RefComponent": "HAN-DB-HA", "RefTitle": "Possible Hanging Situation of Log Replay in SAP HANA System Replication", "RefUrl": "/notes/2329460 "}, {"RefNumber": "2327289", "RefComponent": "HAN-DB", "RefTitle": "Delta merge is hanging due to long running upsert statement", "RefUrl": "/notes/2327289 "}, {"RefNumber": "2323568", "RefComponent": "HAN-DB", "RefTitle": "Installation of SAP HANA database Revision 112.02 and SAP HANA XSA fails", "RefUrl": "/notes/2323568 "}, {"RefNumber": "2318089", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash with exception Invalid transaction state; $condition$=tcb_handle_->getState() == TransactionControlBlock::ACTIVE", "RefUrl": "/notes/2318089 "}, {"RefNumber": "2320203", "RefComponent": "HAN-DB", "RefTitle": "Indexserver crash in TrexCalculationEngine::CalculationEngineManager::getCachedItab", "RefUrl": "/notes/2320203 "}, {"RefNumber": "2320048", "RefComponent": "HAN-DB", "RefTitle": "Crash in ptime::ProcChecker::stmt_proc_assign_checkLhsRhs When Assigning a Complete Row to a New Variable", "RefUrl": "/notes/2320048 "}, {"RefNumber": "2320046", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at TrexStore/UdivListContainerMVCC.cpp With Assertion failed: hdl->getLocalMaxUdiv()!=0", "RefUrl": "/notes/2320046 "}, {"RefNumber": "2318281", "RefComponent": "HAN-DB", "RefTitle": "High CPU and Hanging Situation During Query Execution", "RefUrl": "/notes/2318281 "}, {"RefNumber": "2317027", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Indexserver Crash due to Metadata Inconsistency for Tables Containing Preload Columns", "RefUrl": "/notes/2317027 "}, {"RefNumber": "2315336", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Indexserver Crash During DeltaMerge on History Table", "RefUrl": "/notes/2315336 "}, {"RefNumber": "2315085", "RefComponent": "HAN-DB-SCR", "RefTitle": "Query with Multi-Value Parameter on Scripted Calculation View Fails with Incorrect Syntax Error", "RefUrl": "/notes/2315085 "}, {"RefNumber": "2312976", "RefComponent": "HAN-DB", "RefTitle": "Memory Leak in Pool/JoinEvaluator/JERequestedAttributes/Results In SAP HANA 1 SPS10 and SPS11 When Executing DML Statements", "RefUrl": "/notes/2312976 "}, {"RefNumber": "2312994", "RefComponent": "HAN-DB", "RefTitle": "Memory leak in Pool/TransientMetadataAlloc when calling a procedure", "RefUrl": "/notes/2312994 "}, {"RefNumber": "2312983", "RefComponent": "HAN-DB", "RefTitle": "Memory leak in Pool/parallel/aggregates when querying on distributed environment", "RefUrl": "/notes/2312983 "}, {"RefNumber": "2312998", "RefComponent": "HAN-DB", "RefTitle": "Memory leakage in ODBC/SQLDBC client library when connecting to HANA system via ODBC/SQLDBC", "RefUrl": "/notes/2312998 "}, {"RefNumber": "2312539", "RefComponent": "HAN-DB", "RefTitle": "Near Zero Downtime Upgrade To SAP HANA Database SPS11 With ASYNC Replication Mode Doesn't Work", "RefUrl": "/notes/2312539 "}, {"RefNumber": "2308421", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at JoinEvaluator::TupleList::TupleList(JoinEvaluator::TupleList const&, bool, ltt::allocator&)", "RefUrl": "/notes/2308421 "}, {"RefNumber": "2308845", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::PlanVizNode::_serializeNodeContents During PlanViz Execution or Plan Trace Creation", "RefUrl": "/notes/2308845 "}, {"RefNumber": "2307594", "RefComponent": "HAN-DB", "RefTitle": "Inbound Queue Dispatching Causes High Number of CX_AMDP_EXECUTION_FAILED Dumps in Parallel Processing Mode", "RefUrl": "/notes/2307594 "}, {"RefNumber": "2307541", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::Statement::checkDDLStart When Executing a DDL Statement", "RefUrl": "/notes/2307541 "}, {"RefNumber": "2307328", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at OlapEngine::Parallel::BwPopAggregateParallel::executePopEx When Using TABLESAMPLE Clause on Tables With Multiple Partitions", "RefUrl": "/notes/2307328 "}, {"RefNumber": "2306407", "RefComponent": "HAN-DB", "RefTitle": "Crash During Replay Log: \"Alter Column [$name$][208] in [SCHEMA:TABLEen] failed\"", "RefUrl": "/notes/2306407 "}, {"RefNumber": "2306382", "RefComponent": "HAN-DB", "RefTitle": "Crash On Secondary After Takeover With The Error: \"log position inconsistency detected - plz contact to HANA dev support before clearing or throwing away your redo log files\"", "RefUrl": "/notes/2306382 "}, {"RefNumber": "2303938", "RefComponent": "HAN-DB", "RefTitle": "Wrong DB Name in System Landscape Directory for SingleDB Mode Systems in SAP HANA 1 Revisions <= 122.03", "RefUrl": "/notes/2303938 "}, {"RefNumber": "2303061", "RefComponent": "HAN-DB-HA", "RefTitle": "Crash with Exception \"Duplicate log segment ID in new segment LogSegment\" on the Secondary Site of a SAP HANA System Replication scenario", "RefUrl": "/notes/2303061 "}, {"RefNumber": "2301382", "RefComponent": "HAN-DB", "RefTitle": "Increased Used Memory Size due to Pool/PersistenceManager/PersistentSpace/DefaultLPA/Page (Rev. 110 - 122.05)", "RefUrl": "/notes/2301382 "}, {"RefNumber": "2301570", "RefComponent": "HAN-DB", "RefTitle": "Private Lo<PERSON> Buffer May Lead to Crash due to Inconsistent Entries in Redo Logs", "RefUrl": "/notes/2301570 "}, {"RefNumber": "2299260", "RefComponent": "HAN-DB", "RefTitle": "Internal Communication Failures in SAP HANA (Connection Broken)", "RefUrl": "/notes/2299260 "}, {"RefNumber": "2296278", "RefComponent": "HAN-DB", "RefTitle": "XS Engine Logon Attempt Results with: ERROR [SQL-274] inserted value too large for column: Failed in \"CLIENT_IP\"", "RefUrl": "/notes/2296278 "}, {"RefNumber": "2294045", "RefComponent": "HAN-DB-SEC", "RefTitle": "Indexserver crash on 'AnalyticalAuthorization::FilterProvider::getFilterAsQueryEntryStructure'", "RefUrl": "/notes/2294045 "}, {"RefNumber": "2271235", "RefComponent": "HAN-DB", "RefTitle": "Memory Leak in Pool/RowEngine/QueryExecution when Using Batch Inserts on Row Store tables", "RefUrl": "/notes/2271235 "}, {"RefNumber": "2290614", "RefComponent": "HAN-DB", "RefTitle": "Blocked Transactions Query in SAP HANA Studio Returns Incorrect Result in a Distributed System", "RefUrl": "/notes/2290614 "}, {"RefNumber": "2290385", "RefComponent": "HAN-DB", "RefTitle": "CHECK_CATALOG Reports the Error 'invalid address detected : not occupied slot' classid : 31", "RefUrl": "/notes/2290385 "}, {"RefNumber": "2281937", "RefComponent": "HAN-DB-ENG", "RefTitle": "M_DISK_USAGE hangs in TrexService::FdirRecurseDirectory", "RefUrl": "/notes/2281937 "}, {"RefNumber": "2289105", "RefComponent": "HAN-DB-ENG", "RefTitle": "Standstill Situation During OOM in SAP HANA 1 SPS10 and SPS11", "RefUrl": "/notes/2289105 "}, {"RefNumber": "2286215", "RefComponent": "HAN-DB-SCR", "RefTitle": "SAP HANA DB: Cannot Create Procedure due to Error \"insufficient privilege: Not authorized\"", "RefUrl": "/notes/2286215 "}, {"RefNumber": "2287439", "RefComponent": "HAN-DB", "RefTitle": "Batch UPSERT on Row Store Table Fails with SQL Error 301 in SAP HANA 1 SPS10 and SPS11", "RefUrl": "/notes/2287439 "}, {"RefNumber": "2287190", "RefComponent": "HAN-DB", "RefTitle": "SQL Connections are Closed due to \"idle_connection_timeout\" Although They are not Idle", "RefUrl": "/notes/2287190 "}, {"RefNumber": "2286543", "RefComponent": "HAN-DB-HA", "RefTitle": "System Replication Secondary Site Crashes After NZD Upgrade", "RefUrl": "/notes/2286543 "}, {"RefNumber": "2275220", "RefComponent": "HAN-DB", "RefTitle": "Indexserver Crash at ptime::qo_Normalizer::is_possible_to_prefilter_before_view", "RefUrl": "/notes/2275220 "}, {"RefNumber": "2281574", "RefComponent": "HAN-DB", "RefTitle": "CHECK_CATALOG Reports the Error 'invalid address detected : not occupied slot' classid : 39", "RefUrl": "/notes/2281574 "}, {"RefNumber": "2266533", "RefComponent": "HAN-DB", "RefTitle": "Bad Performance of CHECK_TABLE_CONSISTENCY on Large Tables after Upgrade to SAP Hana SPS11", "RefUrl": "/notes/2266533 "}, {"RefNumber": "2272248", "RefComponent": "HAN-DB", "RefTitle": "Possible Wrong Results during the Join of Temporary Tables with Physical Tables", "RefUrl": "/notes/2272248 "}, {"RefNumber": "2269861", "RefComponent": "HAN-DB", "RefTitle": "Possible Wrong Results during JOIN Queries", "RefUrl": "/notes/2269861 "}, {"RefNumber": "2265320", "RefComponent": "HAN-DB-SEC", "RefTitle": "SAP HANA database: Tenant indexserver cannot start after changing SSL certificates", "RefUrl": "/notes/2265320 "}, {"RefNumber": "2262591", "RefComponent": "HAN-DB-MDX", "RefTitle": "SAP HANA MDX NULL behavior changed with Revision 111", "RefUrl": "/notes/2262591 "}, {"RefNumber": "2264778", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database: Wrong Results when Accessing a Large Table with an Inverted Index", "RefUrl": "/notes/2264778 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}