SAP Note 195618 addresses an issue regarding volume-based rebate processing after upgrading from SAP Release 3.0/3.1 to Release 4.0/4.5. The specific problem mentioned is that the scale currency might not be properly set in the document condition following the upgrade.

The root cause of the issue has been identified in the selection process of billing documents by the report ZZFIXIT2, which results in incorrect processing of these documents. This could potentially affect subsequent processing in SDBONTO2, requiring a reset of the condition currency as well.

To resolve the issue, users are advised to implement the correction for report ZZFIXIT2. Once the correction is applied, users must follow a three-step process to ensure all affected documents are rectified:

1. Run ZZFIXIT2 to reset the affected documents.
2. Execute SDBONTO2 to correct the documents, particularly the condition records in table KONV.
3. Use SDS060RB with the parameter INITS060 to reset the setup statistically.

This note serves as a guideline for users who encounter issues with scale currency in document conditions after an upgrade, providing them with a clear solution to correct the documentation errors.