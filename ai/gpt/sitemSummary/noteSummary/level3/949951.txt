SAP Note 949951 addresses an issue where attempts to create a BIA (Business Intelligence Accelerator) index fail with the error message "invalid attribute name 2430". This problem occurs when the InfoCube involves InfoObjects that have time-dependent master data with a prefix that includes a forward-slash ("/xxx/").

The underlying reason for this error is that the BIA server does not accept the forward slash character ("/") in field names, table names, and similar identifiers. Normally, such slashes are replaced with dollar signs ("$"). However, this substitution had not been happening for the DATETO and DATEFROM attributes in the InfoObject names, which led to the error when creating BIA indices.

The solution, as outlined in the SAP Note, is to import Support Package 09 for SAP NetWeaver 2004s BI (BI Patch09 or SAPKW70009) into the BI system. This Support Package addresses the reported issue. The note also refers to SAP Note 0914303, which provides detailed information about this particular Support Package.

In cases where the issue needs to be resolved urgently and the Support Package is not yet released or available, correction instructions can be used as a temporary fix. The note suggests that these instructions may be available in advance of the Support Package's release, but with the designation "preliminary version" in the short text.

To summarize, Note 949951 details a specific BIA index creation error due to inappropriate character use in InfoObject names, outlines the cause, and provides a reference to the Support Package that contains the fix, as well as an immediate workaround via correction instructions.