{"Request": {"Number": "1522367", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 289, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017118352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001522367?language=E&token=7150382510FB5072DB3264E17A22267D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001522367", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001522367/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1522367"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.03.2011"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-GL-A"}, "SAPComponentKeyText": {"_label": "Component", "value": "Posting/Clearing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-GL-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Posting/Clearing", "value": "FI-GL-GL-A", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL-A*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1522367 - Document number gap reason and analysing method"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />The posted FI document can be displayed neither in Tcode FB03 nor in Tcode SE16-&gt;BKPF table, and the document number is listed in report RFBNUM00N.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />RF_BELEG, SNRO, NRIV, NRIV_LOKAL, NRIVSHADOW, SM13, ST22, SM21, FB01, FB05, FB1S, FB1K, FB1D, FB50, FB60, FB70, MIRO, VF01.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />1. Document number gap may occur with update termination after COMMIT WORK.<br /><br />For example, in FB01 the document number is assigned before an update request is executed with a COMMIT WORK. So if any error occurs in the update function module, the update termination will occur. And if you can not do the \"Repeat updates\" run in Tcode SM13, the document number is lost forever and the document number gap occurs.<br /><br />2. Document number gap may occur if an implicit COMMIT is triggered between the document number assignment and COMMIT WORK, however the COMMIT WORK is not executed.<br /><br />For example, an implicit COMMIT is raised in business transaction event 00001030 by calling a synchronous RFC(Remote Function call), so the document number is assigned finally. However if any error occurs after the RFC call and before the COMMIT WORK, no update is started. It means the assigned document number isn't updated into FI table BKPF, therefore the document number gap occurs.<br /><br />For the details about when a document number is finally assigned and how an implicit COMMIT can be triggered, please refer to SAP note 639754.<br /><br />3. If a buffering type is activated and year-dependent number ranges are defined, the document number gap may occur at the end of a fiscal year.<br /><br />4. If 'Main memory buffering' is used, once the system is shut down, all buffered but NOT used document number will be lost.<br /><br />5. If document number buffering type is switched off, the buffered but NOT used document number will be lost.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. If document number gap occurs with update termination after COMMIT WORK.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please analyze the update termination in Tcode SM13. For the details please refer to SAP note 1513587. And the document number gap caused by update termination can be illustrated by report RFVBER00, the gaps in the document number assignment can be listed in report RFBNUM00N. <OL>2. If document number gap occurs because of an implicit COMMIT between the document number assignment and COMMIT WORK.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please check the source codes between document number assignment and COMMIT WORK for finding if an implicit COMMIT is triggered. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Especially check the source codes in BTE 00001030 if it is activated, and also check the customer specific source codes in User Exit. <OL>3. If a buffering type is activated and year-dependent number ranges are defined, the document number gap may occur at the end of a fiscal year.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please check the values in To number(TONUMBER) field and current document number(NRLEVEL) field of the table(NRIV_LOKAL or NRIVSHADOW) for finding last buffered document number, and the current document number, then check the document numbers updated in table BKPF via Tcode SE16 for judging whether the gap occurred at the end of the fiscal year. If yes, the document number gap occurred is a standard system behavior. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Moreover, these document number gaps can be documented in report RFBNUM00N. If \"Local buffering with process ID\" is used in your system, the gaps can also be documented in BC report RSSNR0A1, for details please refer to SAP note 179224. In case of Parallel Buffering you can use report RSSNR0S1, for details please refer to note 599157. <OL>4. If 'Main memory buffering' is used.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please check whether you have shut down the system. If yes, then all buffered but NOT used document numbers are lost. This is a standard system behavior. <OL>5. If the document number buffering type is switched.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can check whether buffering type was changed and who changed it via Tcode SNRO-&gt; Object RF_BELEG-&gt; Goto-&gt; change documents. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Check which value is saved in field 'old value' and field 'New value' (space means 'No buffering', 'P' means 'Local buffering with process ID', 'S' means 'Parallel buffering'). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the buffering type was changed, for example, buffering type was changed from 'No buffering' to 'Parallel buffering', and then it was changed back to 'No buffering' again, if a new document is posted, system will get the next document number from table NRIV. And the buffered but NOT used document numbers in table NRIVSHADOW will not be assigned. However these document number gaps can be documented in report RFBNUM00N. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Addition, there are detail explanation in SAP note 504875 about the advantages and disadvantages for every buffering type. Note 1398444 answers most frequently asked questions for buffering FI number object RF_BELEG. <OL>6. If the document number gap occurs regularly in your Production system without any helpful information, such as NO short dump, NO update termination, NO system log, and you even don't know in which transaction code the document number gap occurs.</OL> <OL><OL>a) Please try to figure out the similar points if the gaps have been occurred for several times.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For example, the information like \"I find the gap occurs in a particular background job which is executed monthly\". And also the information like \"a BTE or a User Exit is activated\". <OL><OL>b) Please implement SAP Pilot Release note 1259909 (also the report ZFIND_MISSING_DOCS enclosed in this note). Details about SAP Pilot Release note, please refer to SAP note 538778.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After the implementation, in case document number gap occurs next time, the header data will be updated into a shadow table ZBKPF directly after document number assignment. Then you can judge which program(such as FI interface program SAPLFACI, FI program SAPMF05A) should be analyzed by referring to the data in table ZBKPF (like CPUDT /CPUTM /AWTYP /TCODE /USNAM). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For example, if ZBKPF-TCODE = MIRO and ZBKPF-AWTYP = RMRP, it indicates this must be a vendor invoice document posted via financial interface (program SAPLFACI), then you can try to analyze the source codes between document number assignment in FI (function RF_GET_DOCUMENT_NUMBER) and COMMIT WORK which is done in sending application (MM invoice verification) for finding whether an implicit COMMIT is triggered. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;And you can send a message to SAP for further analysis. For this example above, a message can be created either in FI-AP-AP-J (FI-Interface) or MM-IV-LIV (Invoice verification) <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Addition, a message created in the right component will be very helpful to ensure this message is processed rapidly and efficiently. So kindly please clarify the component before you create a message to SAP. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Moreover, for preventing an implicit COMMIT triggered after document number assignment before COMMIT WORK in SAP standard program, all E-messages between document number assignment and COMMIT WORK have been changed to A-messages via standard correction in SAP notes: 428391, 1259205, 1420715. <OL>7. Check the system logs for all servers in Tcode SM21 via menu: System log -&gt;Choose-&gt;Central system logs.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Here you can try to find the information which might be relevant for the document number gap. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For example, whether a dialog or a background work process was cancelled, whether a update termination occurred etc. Then you can analyze this dialog work process log in Tcode ST11, the background work process log in SM37 and the update termination in Tcode SM13. <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL-GL-X (Database Inconsistencies)"}, {"Key": "Other Components", "Value": "FI-GL-GL-W (Preliminary Posting/Workflow)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I028612)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D024958)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001522367/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001522367/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001522367/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001522367/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001522367/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001522367/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001522367/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001522367/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001522367/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "792493", "RefComponent": "FI-GL-GL-W", "RefTitle": "Reasons for FI document number gaps when parking", "RefUrl": "/notes/792493"}, {"RefNumber": "639754", "RefComponent": "BC-SRV-NUM", "RefTitle": "When is a number finally assigned?", "RefUrl": "/notes/639754"}, {"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157"}, {"RefNumber": "504875", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering of number ranges", "RefUrl": "/notes/504875"}, {"RefNumber": "175047", "RefComponent": "FI-GL-GL-X", "RefTitle": "Causes for FI document number gaps (RF_BELEG)", "RefUrl": "/notes/175047"}, {"RefNumber": "1596083", "RefComponent": "FI-GL-GL-A", "RefTitle": "Error prevention mechanism for doc. no. gaps for FI postings", "RefUrl": "/notes/1596083"}, {"RefNumber": "1522489", "RefComponent": "MM-IV-GF-UPD", "RefTitle": "Error prevention mechanism for database commits that are not intended", "RefUrl": "/notes/1522489"}, {"RefNumber": "1443008", "RefComponent": "FI-GL-GL-G", "RefTitle": "RFBNUM00N: Doc. number buffer/TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/1443008"}, {"RefNumber": "1420715", "RefComponent": "FI-AP-AP-W", "RefTitle": "FBV1: Gaps in doc number assgmt in parking due to error msg", "RefUrl": "/notes/1420715"}, {"RefNumber": "1398444", "RefComponent": "FI-GL-GL-A", "RefTitle": "Buffering the document number assignment for RF_BELEG", "RefUrl": "/notes/1398444"}, {"RefNumber": "1379085", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1379085"}, {"RefNumber": "1315217", "RefComponent": "XX-CSC-JP", "RefTitle": "Number gaps in FI-documents and Invoice Summary active", "RefUrl": "/notes/1315217"}, {"RefNumber": "1265329", "RefComponent": "XX-CSC-JP", "RefTitle": "Inconsistencies between table isjpinvsumit and table BSEG", "RefUrl": "/notes/1265329"}, {"RefNumber": "1259909", "RefComponent": "FI-GL-GL-A", "RefTitle": "Document number gaps in FI: Logging of document headers", "RefUrl": "/notes/1259909"}, {"RefNumber": "1259205", "RefComponent": "FI-AR-AR-A", "RefTitle": "FI: Document number gaps in FI documents due to error msgs", "RefUrl": "/notes/1259205"}, {"RefNumber": "1070073", "RefComponent": "FI-GL-GL-A", "RefTitle": "Doc number gaps due to dialog module FI_REVERSE_DOCUMENT 2", "RefUrl": "/notes/1070073"}, {"RefNumber": "1060901", "RefComponent": "FI-GL-GL-A", "RefTitle": "Doc number gaps caused by dialog module FI_REVERSE_DOCUMENT", "RefUrl": "/notes/1060901"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2018164", "RefComponent": "AC-INT", "RefTitle": "Error prevention mechanism for document number gaps with BAPI_ACC_DOCUMENT_POST", "RefUrl": "/notes/2018164 "}, {"RefNumber": "1934331", "RefComponent": "PM-WOC-JC", "RefTitle": "Fangschaltung für Belegnummerlücken im Nummernkreisobjekt RF_BELEG im FI", "RefUrl": "/notes/1934331 "}, {"RefNumber": "1522489", "RefComponent": "MM-IV-GF-UPD", "RefTitle": "Error prevention mechanism for database commits that are not intended", "RefUrl": "/notes/1522489 "}, {"RefNumber": "1259909", "RefComponent": "FI-GL-GL-A", "RefTitle": "Document number gaps in FI: Logging of document headers", "RefUrl": "/notes/1259909 "}, {"RefNumber": "504875", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering of number ranges", "RefUrl": "/notes/504875 "}, {"RefNumber": "1596083", "RefComponent": "FI-GL-GL-A", "RefTitle": "Error prevention mechanism for doc. no. gaps for FI postings", "RefUrl": "/notes/1596083 "}, {"RefNumber": "1265329", "RefComponent": "XX-CSC-JP", "RefTitle": "Inconsistencies between table isjpinvsumit and table BSEG", "RefUrl": "/notes/1265329 "}, {"RefNumber": "639754", "RefComponent": "BC-SRV-NUM", "RefTitle": "When is a number finally assigned?", "RefUrl": "/notes/639754 "}, {"RefNumber": "1398444", "RefComponent": "FI-GL-GL-A", "RefTitle": "Buffering the document number assignment for RF_BELEG", "RefUrl": "/notes/1398444 "}, {"RefNumber": "1443008", "RefComponent": "FI-GL-GL-G", "RefTitle": "RFBNUM00N: Doc. number buffer/TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/1443008 "}, {"RefNumber": "1420715", "RefComponent": "FI-AP-AP-W", "RefTitle": "FBV1: Gaps in doc number assgmt in parking due to error msg", "RefUrl": "/notes/1420715 "}, {"RefNumber": "1259205", "RefComponent": "FI-AR-AR-A", "RefTitle": "FI: Document number gaps in FI documents due to error msgs", "RefUrl": "/notes/1259205 "}, {"RefNumber": "1315217", "RefComponent": "XX-CSC-JP", "RefTitle": "Number gaps in FI-documents and Invoice Summary active", "RefUrl": "/notes/1315217 "}, {"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157 "}, {"RefNumber": "1070073", "RefComponent": "FI-GL-GL-A", "RefTitle": "Doc number gaps due to dialog module FI_REVERSE_DOCUMENT 2", "RefUrl": "/notes/1070073 "}, {"RefNumber": "1060901", "RefComponent": "FI-GL-GL-A", "RefTitle": "Doc number gaps caused by dialog module FI_REVERSE_DOCUMENT", "RefUrl": "/notes/1060901 "}, {"RefNumber": "792493", "RefComponent": "FI-GL-GL-W", "RefTitle": "Reasons for FI document number gaps when parking", "RefUrl": "/notes/792493 "}, {"RefNumber": "175047", "RefComponent": "FI-GL-GL-X", "RefTitle": "Causes for FI document number gaps (RF_BELEG)", "RefUrl": "/notes/175047 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}