SAP Note 112771 addresses a problem where vendor capacity is not updated when a vendor is manually assigned or changed in transaction ME52 (which relates to the change of a purchase requisition). The issue is identified as a program error.

The solution provided in the note is a source code correction. The detailed correction instructions can be found on the SAP server 'sapservX' within a specific directory path provided in the note (specific/isafs/AF3K000872).

The note is likely to include steps to apply the source code correction manually or may reference a support package that includes the fix. However, the exact details of the correction are not provided here, so one would need to access the documented path to obtain the source code changes.

To summarize, SAP Note 112771 deals with a bug where vendor capacity does not update when changes are made manually within ME52. The resolution requires a correction to the program’s source code, which can be found on the server 'sapservX' as indicated in the note.