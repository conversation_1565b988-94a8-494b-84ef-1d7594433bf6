SAP Note 1730246 addresses an issue where the count of constraints displayed in the summary of the ST13 transaction, specifically in the VARCONF tool for the CUP project, is incorrect. The symptom is that the constraint count shown is less than the actual number of executed constraints. The root cause of this problem is identified as a program error.

The note is directed at users who perform variant configuration analysis using the mentioned tools and transaction. To resolve the issue, the SAP Note should be applied if users are finding discrepancies between the displayed constraint count and the actual constraint execution count. This note seems to be targeted at resolving this specific problem and ensuring that the correct data is reflected in the ST13-VARCONF-CUP summary.