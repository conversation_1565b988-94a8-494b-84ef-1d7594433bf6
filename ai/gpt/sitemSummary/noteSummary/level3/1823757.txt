SAP Note 1823757 addresses an issue encountered in SAP NetWeaver Business Warehouse (BW) when running business object queries that involve a high number of hierarchies. The error presented is "CX_RSR_HIER_MEMBER_NOT_FOUND," or the system may issue an error message "_INITIALIZE-01-" in the method in the class CL_RSR_HIERARCHY_INCL.

This problem is identified as a program error and affects different versions of SAP NetWeaver BW.

The solution provided for various SAP NetWeaver BW versions is to import specific Support Packages as described below:

1. For SAP NetWeaver BW 7.00, import Support Package 31 (SAPKW70031) as detailed in SAP Note 1782745.
2. For SAP NetWeaver BW 7.01, import Support Package 14 (SAPKW70114) following the guidance in SAP Note 1794836.
3. For SAP NetWeaver BW 7.02, import Support Package 14 (SAPKW70214), as described in SAP Note 1800952.
4. For SAP NetWeaver BW 7.30, import Support Package 10 (SAPKW73010) referencing SAP Note 1810084.
5. For SAP NetWeaver BW 7.31, import Support Package 08 (SAPKW73108), per SAP Note 1813987.
6. For SAP NetWeaver BW 7.40, import Support Package 03 (SAPKW74003) with details in SAP Note 1818593.

Additionally, in urgent cases, the correction instructions can be implemented as an advance correction after reading and understanding SAP Note 875986, which provides information about using transaction SNOTE.

Some SAP Notes mentioned may be available in a "Preliminary version" before the release of the Support Package, and the short text will indicate this status.