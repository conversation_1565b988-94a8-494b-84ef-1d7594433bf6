SAP Note 2357870 addresses the issue where users are unable to add Non Ferrous Metals (NFM) functionalities to the material master, specifically in the "Sales Org. Data 1," "Purchasing," and "MRP 1" tab pages.

The note clarifies that the inability to see NFM data screens is due to missing settings related to material type configurations. To resolve this issue, the note provides the following solution:

1. Users need to add data screens N1, N2, and N3 to the screen sequence by copying these from existing Screen Sequences MC or DI. The navigation path to perform this customizing action in the SAP system is provided.

2. Users then need to add a Function Code (FCode) and processing routine to the N1, N2, and N3 data screens. This step involves assigning a specific FCode and processing routine to each data screen, which is detailed in the note (e.g., For screen N1, FCode PB60 and processing routine /NFM/OKCODE_VERTRIEB).

After completing these customization steps, users will be able to see NFM buttons named "NF Sales Weights," "NF-Purchase Weights," and "NF Struct. Weights" in the mentioned tab pages within transactions MM01 (Create Material), MM02 (Change Material), and MM03 (Display Material). Clicking these buttons will display new screens for adding NFM master data.

The note also provides a link for more information about Non Ferrous Metals processing within the context of SAP S/4HANA.

Additionally, the note references SAP Note 2357837, which addresses the simplification of material types in the Mill Products industry solution for SAP S/4HANA 1610. It suggests creating new material types or enhancing existing ones for Mill Products and provides guidance for customers transitioning from SAP ERP to S/4HANA 1610.