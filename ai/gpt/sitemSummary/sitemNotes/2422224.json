{"Request": {"Number": "2422224", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 520, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018509512017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002422224?language=E&token=B2C5CF78A960A295584C7EE37042CF25"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002422224", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002422224/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2422224"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.07.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-MM-BW"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - MM Content im BW System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Materials Management", "value": "BW-BCT-MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - MM Content im BW System", "value": "BW-BCT-MM-BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-MM-BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2422224 - SAP S/4HANA Long Material number integration with SAP BW / SAP BW/4HANA"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You plan to extract material numbers from your SAP S/4HANA system into a SAP BW or SAP BW/4HANA&#160;system.</p>\r\n<p>There a two scenarios</p>\r\n<p><strong>Scenario 1:</strong></p>\r\n<p>You do <strong>not</strong> plan to use the extended material number functionality in your SAP S/4HANA system, that means the extended material number functionality is <strong>not</strong> switched on in your SAP S/4HANA system (-&gt; your material numbers are <strong>&lt;=&#160;</strong>CHAR <strong>18</strong>).</p>\r\n<p>If you plan to switch on the extended material functionality later, SAP would still recommend to do the described steps for scenario 2 instead of scenario 1 already <strong>now</strong>.</p>\r\n<p><strong>Scenario 2:</strong></p>\r\n<p>You plan to use the extended material number functionality in your SAP S/4HANA system, meaning the extended material number functionality will be switched <strong>on</strong> in your SAP S/4HANA system (-&gt; your material numbers can be longer than CHAR&#160;18&#160;and <strong>&lt;=&#160;</strong>CHAR <strong>40</strong>)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP BW, SAP BW/4HANA, OMSL, TMCNV, MATN1 conversion exit, 0MATERIAL, CONVERSION_EXIT_MATN1_INPUT, MATNB conversion exit, CONVERSION_EXIT_MATNB_INPUT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In your SAP S/4 system the material number domain <strong>MATNR</strong> has been extended to CHAR <strong>40</strong>. All SAP BW extractors, which extract the material number have been adopted to this change and e.g. extract structures have been updated during the conversion to SAP S/4.</p>\r\n<p>Prerequisites are described in the solution below</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Scenario 1 (&lt;= 18 CHAR)</strong></p>\r\n<p>In your SAP S/4HANA system the domain for&#160;material number&#160;(MATNR)&#160;has been extended to CHAR 40, SAP BW extractors which extract the material number have been adapted to this change.</p>\r\n<p>As the extended material number functionality is <strong>not</strong> switched on in your SAP S/4HANA system, only material numbers &lt;= CHAR 18&#160;exist and are extracted to BW. Even though the material domain is CHAR 40 in your SAP&#160;S/4HANA system the extraction into BW works correctly and fills the&#160;CHAR 18&#160;Material number in BW in the right way.</p>\r\n<p>&#160;</p>\r\n<p><strong>SAP BW 7.30 / 7.31 / 7.40 / 7.50 / 7.51 and BI Content 7.37, 7.47, 7.57 </strong></p>\r\n<p>A)&#160;&#160; If the concerned SAP S/4 system is/was a new system for which no DataSources and transformations were already activated, you do not have to do anything extra, just create the needed transformations or install the delivered Business Content with transaction RSOR.</p>\r\n<p>B)&#160;&#160; If the concerned SAP S/4 system is/was an upgraded SAP&#160;ECC-system and was already connected to your SAP&#160;BW system and transformations and DataSources were already activated in the SAP&#160;BW system, you need to do the following:</p>\r\n<ul>\r\n<li><strong>Your S/4 system is connected as a SAPI source system</strong><br />Some reactivation is necessary:<br />Replicate all affected/updated DataSources and (re-)activate them. Reactivate all dependent transformations and DTPs, which have been inactivated by this change. There is <strong>no</strong> conversion of already loaded data necessary.</li>\r\n<li><strong>Your S/4 system is connected as an ODP source system<br /></strong>No further action is needed. There is <strong>no</strong> conversion of already loaded data necessary.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>SAP BW/4HANA 1.0 and BW4CONTB/BW4CONT 1.0</strong></p>\r\n<p>All InfoObjects with conversion exit MATN1 are delivered&#160;as&#160;CHAR 40&#160;(e.g. 0MATERIAL, 0MAT_SALES,&#8230;) in the Business Content. If you have these InfoObjects already active and want to keep them with&#160;CHAR 18, you need to pay attention when installing or reinstalling these InfoObjects from the Business Content so that they are still CHAR 18 afterwards.</p>\r\n<p>If you install the InfoObjects the first time from the Business content, you can decide, if you want to change them to&#160;CHAR 18 (reactivation of all dependent activated objects will be necessary) or leave them with&#160;CHAR 40. In both cases the data (material number length &lt;=&#160;CHAR 18&#160;in the extractors) is loaded correctly into BW and the Conversion Exit MATN1 is handling them in the right way.</p>\r\n<p>Please make sure to set the correct length in transaction OMSL (table TMCNV) in your SAP BW/4 system. Only one conversion exit (MATN1) exists in BW4CONTB and therefore only one customizing entry in TMCNV is possible for all connected source systems.</p>\r\n<p>No further action is needed. There is <strong>no</strong> conversion of already loaded data necessary.</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p><strong>Scenario 2 (&lt;= 40 CHAR)</strong></p>\r\n<p><strong>SAP BW 7.30 / 7.31</strong></p>\r\n<p>With <strong>SAP BW 7.30 / 7.31</strong> you can only extract the long material number into InfoObjects which are not using the MATN1/MATNB conversion functionality. If you require the conversion functionality in your material InfoObjects you can only load max.&#160;CHAR 18&#160;length material numbers to these InfoObjects. In this case the extended material number functionality must <strong>not</strong> be switched on in your SAP S/4HANA system. See SAP Note <a target=\"_blank\" href=\"/notes/0002267140\">2267140</a>.</p>\r\n<p><strong>&#160;</strong></p>\r\n<p><strong>SAP BW 7.40 / 7.50 / 7.51 and BI Content 7.47, 7.57</strong><strong>&#160;</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>Prerequisites: </strong></p>\r\n<ul>\r\n<ul>\r\n<li>SAP BI CONT 7.47 SP 21 or 7.57 SP 14 <strong>AND</strong></li>\r\n<li>SAP BW 7.40&#160;SP18,&#160;SAP BW 7.50&#160;SP09,&#160;SAP BW 7.51&#160;SP04&#160;(or SAP Note&#160;<a target=\"_blank\" href=\"/notes/2469073\">2469073</a>&#160;for installation on lower SPs )</li>\r\n</ul>\r\n</ul>\r\n<ol>\r\n<li>Starting from <strong>SAP BW 7.40</strong> SP18, <strong>SAP BW 7.50</strong> SP09, <strong>SAP BW 7.51</strong> SP04 (see SAP Note <a target=\"_blank\" href=\"/notes/2469073\">2469073</a> for installation on lower SPs ) it is possible to extract data containing material numbers longer than 18 characters into InfoObjects using the conversion exit <strong>MATNB</strong> (replacement of <strong>MATN1</strong> for longer material numbers).<br />Conversion exit <strong>MATNB</strong> is available as of BI CONT 7.47 SP 21 and 7.57 SP 14.</li>\r\n<li>You need to maintain table RSTMCNV with the same entry as table TMCNV in your SAP S/4 system. Please enable table maintenance with according authorization settings. Conversion exit <strong>MATN1</strong> uses customizing settings maintained in table <strong>TMCNV</strong> (transaction OMSL), while conversion exit <strong>MATNB</strong> uses the entry in table <strong>RSTMCNV</strong>.</li>\r\n<li>\r\n<p>A)&#160;&#160; If the concerned SAP S/4 system is/was a new system for which no DataSources and transformations were already activated, continue with 4. and&#160;create the needed transformations or install the delivered Business Content with transaction RSOR.</p>\r\n<p>B)&#160;&#160; If the concerned SAP S/4 system is/was an upgraded SAP&#160;ECC-system and was already connected to your SAP&#160;BW system and transformations and DataSources were already activated in the SAP&#160;BW system, you need to do the following:</p>\r\n</li>\r\n<ul>\r\n<li><strong>Your S/4 system is connected as a SAPI source system</strong><br />Some reactivation is necessary:<br />Replicate all affected/updated DataSources and (re-)activate them. Reactivate all dependent transformations and DTPs, which have been inactivated by this change. There is <strong>no</strong> conversion of already loaded data necessary.</li>\r\n<li><strong>Your S/4 system is connected as an ODP source system<br /></strong>Go to Transaction RSA1, for the S/4 Sourcesystem right mouse click \"activate\".<br />You need to find&#160; out, which active DataSources (or the ones you use) contain fields with conversion exit MATN1.&#160;Go to Table RSDSSEGFD select&#160;Convexit = MATN1 Version=A and logsys = your S/4 system. For these DataSources you need to go to transaction RSDS enter the DataSourcename and the name of the S/4 system, click \"change\", tabstrip \"Proposal\", click \"Refresh Standard System Proposal\". Now the proposed field length is adjusted. Go to tabstrip \"Fields\". Please confirm the notification \"Fieldlist on longer corresponds to proposal. Copy Changes\" with yes and activate the DataSource.</li>\r\n</ul>\r\n<li>If you want to load long material numbers from your SAP S/4 system into SAP BW InfoObjects with conversion exit MATN1 (e.g. 0MATERIAL), you need to change the conversion exit to MATNB and change the InfoObject length to&#160;CHAR 40.</li>\r\n<li>No data conversion is necessary, but reactivation of affected objects from&#160;3. and 4.&#160;(e.g. transformations) will be required.</li>\r\n</ol>\r\n<p>&#160;</p>\r\n<p><strong>SAP BW/4HANA:</strong></p>\r\n<ol>\r\n<li>Install<strong> SAP</strong> <strong>BW/4HANA 1.0</strong> <strong>SP02</strong> or SAP Note <a target=\"_blank\" href=\"/notes/2389076\">2389076</a>.&#160; Ensure that in SE19&#160;-&gt; Enhancement&#160;Implementation&#160;CL_BW4_FLE_TOPICS, tab strip Enh. Implementation Elements&#160;under Runtime&#160;Behaviour&#160;Implementation is marked active and runtime&#160;behaviour says \"Execution depends on runtime&#160;filter values\". If not, please reactivate.</li>\r\n<li>Install SAP Note <a target=\"_blank\" href=\"/notes/2400601\">2400601</a> and <a target=\"_blank\" href=\"/notes/2491070\">2491070</a>&#160;&#160;</li>\r\n<li>Please make sure to set the correct length in transaction OMSL (table TMCNV). Only one conversion exit (MATN1) exists in BW/4CONTB and therefore only one customizing entry in TMCNV is possible for all connected source systems.</li>\r\n<li>A)&#160;&#160; If the concerned SAP S/4 system is/was a new system for which no DataSources and transformations were already activated, continue with 5.&#160;and after that&#160;create the needed transformations or install the delivered Business Content with transaction RSOR.<br /><br />B)&#160;&#160; If the concerned SAP S/4 system is/was an upgraded SAP&#160;ECC-system and was already connected to your SAP&#160;BW system and transformations and DataSources were already activated in the SAP&#160;BW system, you need to do the following:<br />Go to Transaction RSA1, for the S/4 Sourcesystem right mouse click \"activate\".<br />You need to find&#160; out, which active DataSources (or the ones you use) contain fields with conversion exit MATN1.&#160;Go to Table RSDSSEGFD select&#160;Convexit = MATN1 Version=A and logsys = your S/4 system. For these DataSources you need to go to transaction RSDS enter the DataSourcename and the name of the S/4 system, click \"change\", tabstrip \"Proposal\", click \"Refresh Standard System Proposal\". Now the proposed field length is adjusted. Go to tabstrip \"Fields\". Please confirm the notification \"Fieldlist on longer corresponds to proposal. Copy Changes\" with yes and activate the DataSource.</li>\r\n<li>All InfoObjects using conversion exit MATN1 (e.g. 0MATERIAL, ...) are delivered as data type CHAR with length 40 in SAP BW/4CONTB. In case the InfoObject using conversion exit MATN1 is still data type CHAR 18, the InfoObject must be re-installed from the content with Transaction RSOR or manually set to data type CHAR length 40. No data conversion is necessary, but reactivation of affected objects (e.g. transformations) will be required.&#160;Customer owned InfoObjects using this conversion exit need to be set to data type CHAR with length 40. No data conversion is necessary, but reactivation of affected objects (e.g. transformations) will be required.&#160;&#160;</li>\r\n</ol>\r\n<p><strong>For all SAP BW releases:</strong></p>\r\n<p>If you use the InfoObject 0MAT_PLANT, 0MAT_SALES or 0MAT_ST_LOC and the long material number, then please use the following new Datasources for Text extraction:</p>\r\n<p>0MAT_PLANT_LM_TEXT, 0MAT_SALES_LM_TEXT, 0MAT_ST_LOC_LM_TEXT.</p>\r\n<p><br />The old DataSources&#160;0MAT_PLANT_TEXT, 0MAT_SALES_TEXT, 0MAT_ST_LOC_TEXT should only be used with material numbers &lt;= 18, for details see SAP NOTE&#160;3063159.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D028763)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D028763)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002422224/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002422224/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422224/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422224/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422224/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422224/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422224/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422224/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422224/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3063159", "RefComponent": "BW-BCT-LO-MD-MM", "RefTitle": "40-character material number truncated in view DataSources 0MAT_PLANT_TEXT, 0MAT_SALES_TEXT, and 0MAT_ST_LOC_TEXT", "RefUrl": "/notes/3063159"}, {"RefNumber": "2719704", "RefComponent": "BW4-ME-IOBJ", "RefTitle": "Characteristic with conversion exit MATN1 is automatically lengthened to 40 characters in import to target system", "RefUrl": "/notes/2719704"}, {"RefNumber": "2635167", "RefComponent": "CA-LT-BW4", "RefTitle": "Handling of Material InfoObjects with Conversion to SAP BW/4HANA", "RefUrl": "/notes/2635167"}, {"RefNumber": "2469073", "RefComponent": "BW4-ME", "RefTitle": "Use of conversion routine MATNB instead of MATN1 not allowed", "RefUrl": "/notes/2469073"}, {"RefNumber": "2400601", "RefComponent": "LO-MD-MM", "RefTitle": "Problems with the conversion exit MATN1 and string", "RefUrl": "/notes/2400601"}, {"RefNumber": "2400585", "RefComponent": "BW-BCT-GEN", "RefTitle": "Collective Note: SAP BW/4HANA Content 1.0 (BW4CONT 100 & BW4CONTB 100)", "RefUrl": "/notes/2400585"}, {"RefNumber": "2389076", "RefComponent": "BW4-ME", "RefTitle": "BADI for long material number for BW/4HANA", "RefUrl": "/notes/2389076"}, {"RefNumber": "2267140", "RefComponent": "CA-FLE-MAT", "RefTitle": "S4TWL - Material Number Field Length Extension", "RefUrl": "/notes/2267140"}, {"RefNumber": "2215424", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - General Information", "RefUrl": "/notes/2215424"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2719704", "RefComponent": "BW4-ME-IOBJ", "RefTitle": "Characteristic with conversion exit MATN1 is automatically lengthened to 40 characters in import to target system", "RefUrl": "/notes/2719704 "}, {"RefNumber": "2500362", "RefComponent": "BW-BCT-GEN", "RefTitle": "Overview Field length extension in SAP S/4HANA - integration with SAP BW and SAP BW/4HANA", "RefUrl": "/notes/2500362 "}, {"RefNumber": "2481878", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "Corrections for 0MATERIAL MATNB Conversion Exit", "RefUrl": "/notes/2481878 "}, {"RefNumber": "2232396", "RefComponent": "CA-FLE-MAT", "RefTitle": "SAP S/4HANA: Material Number Field Length Extension in Suite Integration: Restriction Note", "RefUrl": "/notes/2232396 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DW4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "DW4CORE", "From": "200", "To": "200", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "751", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}