SAP Note 2684586 addresses an error encountered when using the Revenue Accounting feature (RAI) for a sales order item with an intercompany condition type (internal price). The error, identified as FARR_RAI 842, occurs when the local currencies for the item and the company code do not match, due to a coding error.

Summary:
- The issue arises when the 'IB' event functionality, triggered by an Intercompany Invoice, is activated as described in SAP Note 2651782.
- The specific problem is with sales order items that have an intercompany condition (KNTYP = G) associated with them.
- When processing the RAI for such items, users encounter error message FARR_RAI 842, indicating a mismatch between the local currencies of the item and the company code.
- The cause of this mismatch is a coding error.
- The solution to this problem is to implement the correction instructions provided in the note.

Users who face this error are advised to apply the provided corrections to resolve the issue and ensure the correct local currency is used for the order RAI, aligning with the company code of the selling entity.