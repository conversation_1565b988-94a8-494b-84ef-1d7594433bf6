{"Request": {"Number": "1633725", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 864, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009699942017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001633725?language=E&token=CDD318B092F0AB2187A57FD909A6624A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001633725", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001633725/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1633725"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 32}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.02.2014"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-INS-CFG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Setup and Configuration of the Solution Manager system"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation, Configuration and Upgrade of Solution Manager", "value": "SV-SMG-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Setup and Configuration of the Solution Manager system", "value": "SV-SMG-INS-CFG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-INS-CFG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1633725 - SAP Solution Manager 7.1 SP4 - basic functions"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Note the following: This SAP Note is no longer updated. If you require information about important SAP Notes for SAP Solution Manager 7.1 SP4, create a customer message using the component SV-SMG-INS-CFG.<br/>*********************************************************************<br/>*********************************************************************</p>\r\n<p>This note is the central correction note of SAP Solution Manager 7.1 Support Package (SP) 4. It is a composite note that is linked to additional notes. This note is necessary to guarantee the basic functions of your SAP Solution Manager.<br/><br/>You must implement this SAP Note at least once after an SP update. To make it easier to locate and implement the recommended SAP Notes, we will regularly enhance and update this SAP Note. However, it is not mandatory to implement the central correction note regularly. Similarly, SAP Support does not require an update as a prerequisite for message processing.<br/><br/>The provisional update plan for changes to the central correction note can be found in SDN at http://wiki.sdn.sap.com/wiki/display/SMSETUP/Central_Correction_Notes.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Basic Configuration, basic configuration, SAP Solution Manager</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Before you implement the central correction note, ensure the following:</p>\r\n<ul>\r\n<li>No inactive objects exist in your system.<br/>Tip: You can display a list of inactive objects in the object navigator (transaction SE80). To do this, select &quot;Inactive Objects&quot; from the dropdown menu and enter &quot;*&quot; in the user field.</li>\r\n</ul>\r\n<ul>\r\n<li>All available SAP Notes are updated or have been reset to their original state.<br/>For this, you navigate to the modification adjustment (transaction SPAU) and check the status of the SAP Notes. You can find further information about the modification adjustment in the documentation and in the SDN (for example here: http://scn.sap.com/docs/DOC-10312).</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>You should implement this note by default using the SAP Solution Manager configuration (SOLMAN_SETUP).<br/><br/>Adhere to the following procedure when you implement this SAP Note:</p>\r\n<ol>1. Implement SAP Note 875986 to ensure that you use the current version of the Note Assistant (transaction SNOTE).</ol><ol>2. Call the Note Assistant (transaction SNOTE) again.</ol><ol>3. When you implement an SAP Note, the system does not automatically take into account whether there are newer versions of required SAP Notes that have already been implemented in the SAP Support Portal.</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To ensure that all prerequisite SAP Notes are implemented in the current version, call the Note Assistant (transaction SNOTE), choose &quot;Goto -&gt; SAP Note Browser -&gt; Execute (F8)&quot;, and then choose &quot;Download Latest Version of SAP Notes&quot; in the application toolbar.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As a result, when you implement this SAP Note using the Note Assistant, the system checks whether the necessary SAP Notes have been fully implemented. If this is not the case, the Note Assistant implements the latest version.</p>\r\n<ol>4. Implement this central correction note.</ol>\r\n<p><strong>This note is divided into three sections:</strong></p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I) Corrections with manual steps<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;II) Additional information (SAP Notes for managed systems, FAQ Notes, general SAP Notes)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;III) SAP Notes that can be implemented automatically<br/><br/>The &quot;Date&quot; column contains the day on which the listed note was included in this note, or when a relevant change was made. Go through the notes in the specified order (from top to bottom).<br/><br/>All of the notes are listed again under &quot;Related Notes&quot;.<br/><br/>######################################################################<br/>1) Corrections with manual steps<br/>######################################################################<br/><br/>The notes of this section are divided into three groups:<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I. 1) SAP Notes that you must implement manually (using transaction SNOTE). The reason for this may be, for example, that manual pre-implementation steps are required or that the note requires a large amount of memory space.<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I. 2) SAP Notes for which you must still carry out manual activities after you implement this central correction note<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I. 3) SAP Notes whose manual steps can be carried out automatically using postprocessing in the SAP Solution Manager configuration (SOLMAN_SETUP -&gt; System Preparation -&gt; 3. &quot;Implement SAP Note -&gt; Post-Process&quot;).<br/>Alternatively, execute the function module AGS_SISE_MANU_NOTE_ACTS in the Function Builder (transaction SE37) after you implement the central correction note. Ensure that you carry out these postprocessing steps in the work client.<br/><br/>The category indicates the type of manual activity. Possible values:</p>\r\n<ul>\r\n<li>A = Mandatory; you must carry this out.</li>\r\n</ul>\r\n<ul>\r\n<li>B = Optional, if required (for example, error message texts, starting reports manually)</li>\r\n</ul>\r\n<p><br/>1.a)</p>\r\n<p>05.02.2014</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>\r\n<p>B   1849098     Error handling in content synchronization</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>B   1881857     ST710: AI_CRM_IM_UPDATE_FROM_SAP unnecessary transfers</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>B&#x00A0; &#x00A0;1906937  &#x00A0;&#x00A0; Corrections for unified rendering 702/15 (UR-mimes)</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>15.08.2013</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1718522     Customizing table SMCRM_CUST_APPL - General</td>\r\n</tr>\r\n<tr>\r\n<td>B   1801582     Overwrite of template with Backup</td>\r\n</tr>\r\n<tr>\r\n<td>B   1881857     ST710: AI_CRM_IM_UPDATE_FROM_SAP unnecessary transfers</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>26.11.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B 1677459     Collective corrections for the MEA Directory SP4</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>24.09.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A  1678162     Solution Manager Setup: Performance when copying roles</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>22.08.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1649247     Corrections for unified rendering 702/11 II (ABAP</td>\r\n</tr>\r\n<tr>\r\n<td>B   1649297     Corrections for unified rendering 702/11 II (UR-</td>\r\n</tr>\r\n<tr>\r\n<td>Both of these SAP Notes require a large amount of memory space. Therefore, we recommend that you trigger the corrections separately and manually.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>26.06.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1710384     WD ABAP ALV performance improvements</td>\r\n</tr>\r\n<tr>\r\n<td>B   1664509     POWL: Issues with refresh when RefreshAll option is active</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>09.05.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1570399     Solution Manager BI reporting</td>\r\n</tr>\r\n<tr>\r\n<td>A   1649228     POWL: Change in lead selection not considered</td>\r\n</tr>\r\n<tr>\r\n<td>A   1682370     POWL: Removal of &quot;Reapply Table setting</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>15.03.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1690516     SAP Solution manager data extraction incorrect</td>\r\n</tr>\r\n<tr>\r\n<td>Note the following: This note may cause an error (INCLUDE report &quot;/1CAGTF/IF_LOGFUNC_000500&quot; not found) when you implement it. In this case, call transaction SDCCN (&quot;Service Data Control Center&quot;), choose &quot;Create&quot;, and execute the task &quot;Service Definition Refresh&quot;.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>17.02.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1655599     FPM search comp: Initial search data not displayed</td>\r\n</tr>\r\n<tr>\r\n<td>A   1641006     FPM search comp fluid program termination: CX_WD_CONT</td>\r\n</tr>\r\n<tr>\r\n<td>Both of these SAP Notes require an SAP Note (SAP Note 1611632 &quot;FPM search help: Search variants, deleting input fields&quot;) that requires manual pre-implementation steps. For this reason, these two SAP Notes are not contained in the central correction note and must be implemented manually by the customer.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>08.12.2011</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1572183     Authorizations for SAP Solution Manager RFC</td>\r\n</tr>\r\n<tr>\r\n<td>A   1608457     EarlyWatch Alert Session Not Sent To SAP </td>\r\n</tr>\r\n<tr>\r\n<td>Note the following: This note may cause an error (INCLUDE report &quot;/1CAGTF/IF_LOGFUNC_000463&quot; not found) when you implement it. In this case, call transaction SDCCN (&quot;Service Data Control Center&quot;), choose &quot;Create&quot;, and execute the task &quot;Service Definition Refresh&quot;.</td>\r\n</tr>\r\n<tr>\r\n<td>B   1633482     Correction for unified rendering 702/10 V</td>\r\n</tr>\r\n<tr>\r\n<td>B   1633476     Correction for unified rendering 702/10 V</td>\r\n</tr>\r\n<tr>\r\n<td>Both of these SAP Notes require a large amount of memory space. Therefore, we recommend that you trigger the corrections separately and manually.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>1.b)<br/><br/>15.08.3013</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1733236     Advance Corrections BPMon SM 7.1 ST710</td>\r\n</tr>\r\n<tr>\r\n<td>B   1816471     SE24: Error for POST methods when preferred parameter</td>\r\n</tr>\r\n<tr>\r\n<td>B   1849098     Error handling in content synchronization</td>\r\n</tr>\r\n<tr>\r\n<td>B   1886549     Flag TEMP_INACTIVE in table SMSY_SYSTEM_SAP </td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>26.11.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1579462     Sending data from chart item without check to IGS</td>\r\n</tr>\r\n<tr>\r\n<td>B   1758728     &quot;Active&quot; selection of product versions is removed</td>\r\n</tr>\r\n<tr>\r\n<td>B   1768764     TWB reporting displays incorrect test case status</td>\r\n</tr>\r\n<tr>\r\n<td>B   1769570     Function module SMD_DATA_LOADER101 not found</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>24.09.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B  1734821     E2E Alerting: Enable Metric Filter for Tablespaces</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>22.08.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1700043     E2E Alerting: Miss. Metrics f. Oracle, DB2</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>26.06.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1712091     CRM WebUI scrolls top on roundtrips</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>09.05.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1661166     SMSY: Message server for Java systems is incorrect</td>\r\n</tr>\r\n<tr>\r\n<td>A   1690804     Dual stack: Automatic creation of the technical</td>\r\n</tr>\r\n<tr>\r\n<td>B   1707803     DPC: Grey metrics due to &quot;Agent not regi</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>15.03.2012</td>\r\n</tr>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1617181     BPO Dashboard - Update log to wait for ccms_bi</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>17.02.2012</td>\r\n</tr>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1651279     Corrections for EEM 7.1 SP04</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>20.12.2011</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1604471     SLA-relevant scripts are missing in EEM BI Reporting</td>\r\n</tr>\r\n<tr>\r\n<td>This note is relevant only if you use SAP End-User Experience Monitoring (EEM).</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>08.12.2011</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1614747     Report for creating products creates incorrect prod</td>\r\n</tr>\r\n<tr>\r\n<td>B   1610374     Incorrect data in the metric monitor/inactiv</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>1.c)<br/><br/>26.11.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1599582     Plugin Status Details - Delete sp_level check for ST-A/PI</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>26.06.2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1681889     Notification table update for SP5/SP4/SP3/SP2</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br/>######################################################################<br/>2) Additional information (notes for managed systems, FAQ notes, general notes)<br/>######################################################################<br/><br/>Knowledge Articles:<br/>1643013   Dump SYNTAX_ERROR in SAPLCRM_CONDITION_COM_OW<br/><br/><br/>Implement the following SAP Notes - potentially depending on the<br/>release level of the systems - in the managed systems:<br/><br/>15.08.2013<br/>1535611   Missing authorization check in ST-PI<br/><br/>22.08.2012<br/>1011229   ST-PI: Corrections for E2E Diagnostics<br/>1559499   DPC data providers as of ST-PI 2008_1_XX SP4<br/><br/>09.05.2012<br/>1647542   Incident Management Reporting: Messages are missing<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(This SAP Note must be implemented only in the BW system;<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; it must then be called again in the SAP Solution Manager configuration manually)<br/> <br/><br/>08.12.2011<br/>1572183  Authorizations for SAP Solution Manager<br/><br/>######################################################################<br/>3) Corrections that can be implemented automatically<br/>######################################################################<br/><br/>In this section, you can see the list of notes that can be implemented automatically and that are implemented with this central correction note.</p>\r\n<p>05.02.2014<br/>1826109<br/>1909714</p>\r\n<p>15.08.2013<br/>1094074<br/>1457391<br/>1629551<br/>1652503<br/>1682750<br/>1710170<br/>1717403<br/>1722197<br/>1724761<br/>1727449<br/>1733236<br/>1738276<br/>1739631<br/>1749795<br/>1752517<br/>1761039<br/>1762967<br/>1763274<br/>1775643<br/>1777767<br/>1783371<br/>1797388<br/>1804373<br/>1808259<br/>1813468<br/>1813914<br/>1814921<br/>1816471<br/>1820291<br/>1821638<br/>1821727<br/>1823420<br/>1824197<br/>1833865<br/>1837889<br/>1849566<br/>1851723<br/>1855272<br/>1885765<br/>1886549<br/><br/>26.11.2012<br/>1579462<br/>1587690<br/>1599582<br/>1672890<br/>1717775<br/>1758728<br/>1759409<br/>1763793<br/>1768764<br/>1769570<br/>1770638<br/>1774418<br/>1775883<br/>1780509<br/>1784446<br/>1784936<br/><br/>24.09.2012<br/>1664744<br/>1716300<br/>1734821<br/>1741470<br/>1749774<br/>1753985<br/>1754275<br/>1763065<br/><br/>22.08.2012<br/>1559499<br/>1679697<br/>1700043<br/>1700427<br/>1713085<br/>1719578<br/>1729591<br/>1729753<br/>1733998<br/>1734250<br/>1736425<br/>1736448<br/>1737668<br/>1737700<br/>1738511<br/>1740545<br/>1741080<br/>1743244<br/>1745114<br/>1745879<br/>1745942<br/>1748138<br/>1750618<br/>1752181<br/><br/>27.06.2012<br/>1654623<br/>1668299<br/>1672797<br/>1673201<br/>1675691<br/>1679055<br/>1681889<br/>1685530<br/>1685463<br/>1686681<br/>1687114<br/>1696061<br/>1709694<br/>1710686<br/>1712091<br/>1714900<br/>1715620<br/>1715675<br/>1717888<br/>1719016<br/>1719142<br/>1720336<br/>1721598<br/>1722178<br/>1722791<br/>1726133<br/>1726424<br/>1727730<br/>1728631<br/>1730457<br/>1731652<br/>1732144<br/>1733824<br/><br/>09.05.2012<br/>1576693<br/>1644147<br/>1644615<br/>1648230<br/>1648271<br/>1659631<br/>1659705<br/>1659868<br/>1661166<br/>1661518<br/>1665086<br/>1672297<br/>1672735<br/>1673128<br/>1673661<br/>1674070<br/>1675184<br/>1683279<br/>1683290<br/>1684910<br/>1685607<br/>1685621<br/>1686396<br/>1690276<br/>1690804<br/>1695528<br/>1696106<br/>1696526<br/>1696914<br/>1697052<br/>1697102<br/>1698275<br/>1700486<br/>1700972<br/>1704858<br/>1706339<br/>1707803<br/>1707863<br/>1709230<br/>1710174<br/>1711380<br/>1711748<br/>1712878<br/><br/>15.03.2012<br/>1617181<br/>1655601<br/>1672429<br/>1694308<br/>1694458<br/><br/>29.02.2012<br/>1651510<br/>1655203<br/>1671172<br/>1687237<br/><br/>17.02.2012<br/>1651279<br/>1655460<br/>1656963<br/>1663497<br/>1664933<br/>1665775<br/>1666479<br/>1667344<br/>1670189<br/>1675392<br/>1681591<br/>1681853<br/>1683260<br/>1685128<br/><br/>20.12.2011<br/>1604471<br/>1643760<br/>1644866<br/>1645762<br/>1653216<br/>1657694<br/>1659686<br/>1662088<br/>1662244<br/>1663545<br/>1664916<br/><br/>08.12.2011<br/>1297738<br/>1477732<br/>1487329<br/>1488293<br/>1552978<br/>1589050<br/>1602519<br/>1604209<br/>1609361<br/>1610374<br/>1612172<br/>1612603<br/>1614206<br/>1614747<br/>1615423<br/>1619830<br/>1622329<br/>1623160<br/>1629407<br/>1634575<br/>1639681<br/>1640239<br/>1641387<br/>1644401<br/>1645356<br/>1648712<br/>1648959<br/>1650032<br/>1650322<br/>1650350<br/>1652475<br/>1653580<br/>1655832<br/>1655577<br/>1656789<br/>1657871<br/>1658536</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027512)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D030970)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001633725/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001633725/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001633725/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001633725/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001633725/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001633725/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001633725/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001633725/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001633725/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "875986", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS up to 702", "RefUrl": "/notes/875986"}, {"RefNumber": "1909714", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL:Dump when switching the queries", "RefUrl": "/notes/1909714"}, {"RefNumber": "1906937", "RefComponent": "BC-WD-ABA", "RefTitle": "Corrections for unified rendering 702/15 V (UR-mimes)", "RefUrl": "/notes/1906937"}, {"RefNumber": "1886549", "RefComponent": "SV-SMG-LDB", "RefTitle": "Flag TEMP_INACTIVE in table SMSY_SYSTEM_SAP wrong", "RefUrl": "/notes/1886549"}, {"RefNumber": "1885765", "RefComponent": "SV-SMG-SDG", "RefTitle": "Self-Diagnosis: Performance issues due to connection checks", "RefUrl": "/notes/1885765"}, {"RefNumber": "1881857", "RefComponent": "SV-SMG-SUP", "RefTitle": "ST710: AI_CRM_IM_UPDATE_FROM_SAP unnecessary transfers", "RefUrl": "/notes/1881857"}, {"RefNumber": "1855272", "RefComponent": "SV-SMG-INS-CFG-MNG", "RefTitle": "SOLMAN_SETUP: admin user shouldn't modify S* profiles", "RefUrl": "/notes/1855272"}, {"RefNumber": "1851723", "RefComponent": "SV-SMG-SVC", "RefTitle": "REFRESH_ADMIN_DATA_FROM_SUPPORT no licence data generation", "RefUrl": "/notes/1851723"}, {"RefNumber": "1849566", "RefComponent": "SV-SMG-SVC", "RefTitle": "Users without authorization see the installations", "RefUrl": "/notes/1849566"}, {"RefNumber": "1849098", "RefComponent": "SV-SMG-LDB", "RefTitle": "Error handling in content synchronization", "RefUrl": "/notes/1849098"}, {"RefNumber": "1837889", "RefComponent": "SV-SMG-SUP", "RefTitle": "VAR scenario: Unjustified deactivation of S users", "RefUrl": "/notes/1837889"}, {"RefNumber": "1833865", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Avoid Obsolete Recipient Lists/Recipients during transport", "RefUrl": "/notes/1833865"}, {"RefNumber": "1826109", "RefComponent": "SV-SMG-SUP", "RefTitle": "ITSM: Standard CRM authorization checks skipped for search", "RefUrl": "/notes/1826109"}, {"RefNumber": "1824197", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "STWB_2 - Test plan display requires re-logon", "RefUrl": "/notes/1824197"}, {"RefNumber": "1823420", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: Fatal Error - CX_COMPONENT_VERSION_NOT_FOUND", "RefUrl": "/notes/1823420"}, {"RefNumber": "1821727", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Detail area not updated in accordance with current lead selection", "RefUrl": "/notes/1821727"}, {"RefNumber": "1821638", "RefComponent": "CRM-BTX-SRQ", "RefTitle": "Rule Policy on Multilevel Categorization", "RefUrl": "/notes/1821638"}, {"RefNumber": "1820291", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Project Documentation tab flagged as changed in Template", "RefUrl": "/notes/1820291"}, {"RefNumber": "1816471", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SE24: Error for POST methods for defined preferred parameter", "RefUrl": "/notes/1816471"}, {"RefNumber": "1814921", "RefComponent": "BC-CCM-BTC", "RefTitle": "BAE: Performance issue when using RFC_VERIFY_DESTINATION", "RefUrl": "/notes/1814921"}, {"RefNumber": "1813914", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: ConfigStore bo40.dump_all_xml - SYSTEM_NO_ROLL", "RefUrl": "/notes/1813914"}, {"RefNumber": "1813468", "RefComponent": "BC-WD-ABA", "RefTitle": "Web Dynpro: Conversion for non-printable chars (NON-UNICODE)", "RefUrl": "/notes/1813468"}, {"RefNumber": "1808259", "RefComponent": "SV-SMG-SVD-GSS", "RefTitle": "Wrong information text for column title in Web DSA UI", "RefUrl": "/notes/1808259"}, {"RefNumber": "1804373", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup : The BW content is not active", "RefUrl": "/notes/1804373"}, {"RefNumber": "1797388", "RefComponent": "BC-WD-ABA", "RefTitle": "Web Dynpro: Conversion of non-prinatable characters", "RefUrl": "/notes/1797388"}, {"RefNumber": "1784936", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Transport Custom Metric Variants from one system to other system", "RefUrl": "/notes/1784936"}, {"RefNumber": "1784446", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Custom Metric Variants were not copied during copy template", "RefUrl": "/notes/1784446"}, {"RefNumber": "1783371", "RefComponent": "SV-SMG-MAI", "RefTitle": "MOpz: System landscape data incomplete in Customer Profile", "RefUrl": "/notes/1783371"}, {"RefNumber": "1780509", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Select Options for Metric parameter values is not working", "RefUrl": "/notes/1780509"}, {"RefNumber": "1777767", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Incomplete display of messages in component view", "RefUrl": "/notes/1777767"}, {"RefNumber": "1775883", "RefComponent": "SV-SMG-SVD-GSS", "RefTitle": "GSS: unexpected dump when using webdynpro logon popup in GSS", "RefUrl": "/notes/1775883"}, {"RefNumber": "1775643", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_SDK_SP_GENERATE_BP_V2: diverse corrections III", "RefUrl": "/notes/1775643"}, {"RefNumber": "1774418", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary refresh triggered when using query api", "RefUrl": "/notes/1774418"}, {"RefNumber": "1770638", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Working with Templates in different languages", "RefUrl": "/notes/1770638"}, {"RefNumber": "1769570", "RefComponent": "SV-SMG-DVM", "RefTitle": "Function Moduel SMD_DATA_LOADER101 not found", "RefUrl": "/notes/1769570"}, {"RefNumber": "1768764", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "TWB reporting displays incorrect test case status counters", "RefUrl": "/notes/1768764"}, {"RefNumber": "1763793", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Column headers are rendered incorrectly", "RefUrl": "/notes/1763793"}, {"RefNumber": "1763274", "RefComponent": "BC-WD-ABA", "RefTitle": "Select options: Error when deleting a parameter", "RefUrl": "/notes/1763274"}, {"RefNumber": "1763065", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Reporting API returns too much data and response is slow", "RefUrl": "/notes/1763065"}, {"RefNumber": "1762967", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "DPW: Overlapping records in DBH_CUBE_DATA", "RefUrl": "/notes/1762967"}, {"RefNumber": "1761039", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP08", "RefUrl": "/notes/1761039"}, {"RefNumber": "1759409", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Dump when \"enable default lead selection\" is ON", "RefUrl": "/notes/1759409"}, {"RefNumber": "1758728", "RefComponent": "SV-SMG-SYS", "RefTitle": "\"Active\" selection of product versions is removed", "RefUrl": "/notes/1758728"}, {"RefNumber": "1754275", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Wrong message 'RFC SM_XXXCLNTnnn_BACK does not exist'", "RefUrl": "/notes/1754275"}, {"RefNumber": "1753985", "RefComponent": "SV-SMG-SDG", "RefTitle": "Downloaded XML data for Root Cause Analysis is not correct", "RefUrl": "/notes/1753985"}, {"RefNumber": "1752517", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Managed system configuration - warning in step 7 create user", "RefUrl": "/notes/1752517"}, {"RefNumber": "1752181", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "Grey metrics in Technical System Monitoring", "RefUrl": "/notes/1752181"}, {"RefNumber": "1750618", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "RFC destinations created in SMSU_MANAGED_SYSTEM not delete", "RefUrl": "/notes/1750618"}, {"RefNumber": "1749795", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DPW: Housekeeping does not finish", "RefUrl": "/notes/1749795"}, {"RefNumber": "1749774", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Request popup opens, eventhough template saved under $TMP", "RefUrl": "/notes/1749774"}, {"RefNumber": "1748138", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary update of detail area", "RefUrl": "/notes/1748138"}, {"RefNumber": "1745942", "RefComponent": "SV-SMG", "RefTitle": "DPW: Cube Aggregation Memory Usage", "RefUrl": "/notes/1745942"}, {"RefNumber": "1745879", "RefComponent": "SV-SMG-SDG", "RefTitle": "Failed to get RTCC recommendation due to RFC call failure.", "RefUrl": "/notes/1745879"}, {"RefNumber": "1745114", "RefComponent": "SV-SMG", "RefTitle": "DBA: Dump in CL_DBA_DBH_CUBE_DATA", "RefUrl": "/notes/1745114"}, {"RefNumber": "1743244", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Better description needed in query template dropdown", "RefUrl": "/notes/1743244"}, {"RefNumber": "1741470", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV error when context changes are dispatched", "RefUrl": "/notes/1741470"}, {"RefNumber": "1741080", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "no/wrong metrics collected on SMD Agent assoicated to DB", "RefUrl": "/notes/1741080"}, {"RefNumber": "1740545", "RefComponent": "CRM-IC-BF-CAT", "RefTitle": "Categories trigger entity data change", "RefUrl": "/notes/1740545"}, {"RefNumber": "1739631", "RefComponent": "SV-SMG-SVD", "RefTitle": "Service delivery session information not updated accordingly", "RefUrl": "/notes/1739631"}, {"RefNumber": "1738511", "RefComponent": "SV-SMG-IMP", "RefTitle": "Editing MS Office documents in SAP Solution Manager", "RefUrl": "/notes/1738511"}, {"RefNumber": "1738276", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "DSA: Word creation dumps caused by memory lack", "RefUrl": "/notes/1738276"}, {"RefNumber": "1737700", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_CPY_PROCTYPE: PPF containers missing from transp.req.", "RefUrl": "/notes/1737700"}, {"RefNumber": "1737668", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Data not re-organized after upgrade", "RefUrl": "/notes/1737668"}, {"RefNumber": "1736448", "RefComponent": "SV-SMG-SDG", "RefTitle": "Dump in Self Diagnosis due to duplicate keys.", "RefUrl": "/notes/1736448"}, {"RefNumber": "1736425", "RefComponent": "SV-SMG-LDB", "RefTitle": "Technical Scenario DUAL_STACK: MaxLen violated", "RefUrl": "/notes/1736425"}, {"RefNumber": "1734821", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "E2E Alerting: Enable Metric Filter for Tablespaces", "RefUrl": "/notes/1734821"}, {"RefNumber": "1734250", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Error in detail area update", "RefUrl": "/notes/1734250"}, {"RefNumber": "1733998", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: activate services on managed systems", "RefUrl": "/notes/1733998"}, {"RefNumber": "1733824", "RefComponent": "BC-WD-ABA", "RefTitle": "Dump in CL_WDR_NOTIFICATION", "RefUrl": "/notes/1733824"}, {"RefNumber": "1733236", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP07", "RefUrl": "/notes/1733236"}, {"RefNumber": "1732144", "RefComponent": "SV-SMG-MON-ALR-CLC", "RefTitle": "Wrong count of used metric instances", "RefUrl": "/notes/1732144"}, {"RefNumber": "1731652", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "SMD Agent does not receive a role within role configuration", "RefUrl": "/notes/1731652"}, {"RefNumber": "1730457", "RefComponent": "SV-SMG-SUP", "RefTitle": "VAR: Texts duplicated after exchange with SAP", "RefUrl": "/notes/1730457"}, {"RefNumber": "1729753", "RefComponent": "SV-SMG-SUP", "RefTitle": "ST710: ITSM document locked by own user after saving", "RefUrl": "/notes/1729753"}, {"RefNumber": "1729591", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Error: No Long Name Found in SMD_HASH_TABLE for Hash-ID", "RefUrl": "/notes/1729591"}, {"RefNumber": "1728631", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Extractor scheduling for ALM and ASR", "RefUrl": "/notes/1728631"}, {"RefNumber": "1727730", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: BW host name provided in uper case cause dumps", "RefUrl": "/notes/1727730"}, {"RefNumber": "1727449", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Dump/Time out on selecting the modified metric at MO level", "RefUrl": "/notes/1727449"}, {"RefNumber": "1726424", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Connection failed (RTE:[2] too many database sessions active", "RefUrl": "/notes/1726424"}, {"RefNumber": "1726133", "RefComponent": "SV-SMG-SDA", "RefTitle": "Incorrect calculation of used workload objects", "RefUrl": "/notes/1726133"}, {"RefNumber": "1724761", "RefComponent": "SV-SMG-OP", "RefTitle": "Business Process Monitoring attributes missing in solution", "RefUrl": "/notes/1724761"}, {"RefNumber": "1722791", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Changes by Compare and Adjust on transaction tab are lost", "RefUrl": "/notes/1722791"}, {"RefNumber": "1722197", "RefComponent": "SV-SMG-OP", "RefTitle": "Where-used list for logical components is incorrect", "RefUrl": "/notes/1722197"}, {"RefNumber": "1722178", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "TwinCube reorganization does not work", "RefUrl": "/notes/1722178"}, {"RefNumber": "1721598", "RefComponent": "SV-SMG-DIA", "RefTitle": "Outside Discovery: Improvement to fill missing HANA props", "RefUrl": "/notes/1721598"}, {"RefNumber": "1720336", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Correction to priority when creating automatic incidents", "RefUrl": "/notes/1720336"}, {"RefNumber": "1719578", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Fallback language text for Managed Obj., Scn.type & Category", "RefUrl": "/notes/1719578"}, {"RefNumber": "1719142", "RefComponent": "SV-SMG-SDA", "RefTitle": "Problems solved with ST 710 SP06", "RefUrl": "/notes/1719142"}, {"RefNumber": "1719016", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: obsolete roles still added to users", "RefUrl": "/notes/1719016"}, {"RefNumber": "1717888", "RefComponent": "SV-SMG-ISM", "RefTitle": "Issue Management: bad performance in context and task tab", "RefUrl": "/notes/1717888"}, {"RefNumber": "1717775", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "Metric Troubleshooting Guide shows wrong information", "RefUrl": "/notes/1717775"}, {"RefNumber": "1717403", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Collective Note for Extractor FWK - ST710 (SP05 - SP08)", "RefUrl": "/notes/1717403"}, {"RefNumber": "1716300", "RefComponent": "SV-SMG-LDB", "RefTitle": "Bug fixes for LMDB notification cleanup", "RefUrl": "/notes/1716300"}, {"RefNumber": "1715675", "RefComponent": "SV-SMG-SDA", "RefTitle": "Content upload: missing logical components", "RefUrl": "/notes/1715675"}, {"RefNumber": "1715620", "RefComponent": "SV-SMG-SDA", "RefTitle": "Search result not in visible area for table tree", "RefUrl": "/notes/1715620"}, {"RefNumber": "1714900", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Check of users and roles when the CUA is inactive", "RefUrl": "/notes/1714900"}, {"RefNumber": "1713085", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM Work Center: Disable Multiple Save on User Click", "RefUrl": "/notes/1713085"}, {"RefNumber": "1712878", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "Technical Host returns grey metrics", "RefUrl": "/notes/1712878"}, {"RefNumber": "1712091", "RefComponent": "CRM-FRW-UI", "RefTitle": "CRM WebUI scrolls top on roundtrips", "RefUrl": "/notes/1712091"}, {"RefNumber": "1711748", "RefComponent": "SV-SMG-LDB", "RefTitle": "Improvement of Report RLMDB_ADD_GUIDS", "RefUrl": "/notes/1711748"}, {"RefNumber": "1711380", "RefComponent": "SV-SMG-SUP-IFA", "RefTitle": "External Service Desk interface: Texts are duplicated", "RefUrl": "/notes/1711380"}, {"RefNumber": "1710686", "RefComponent": "SV-SMG-SDG", "RefTitle": "Configuration Check error for Root cause analysis.", "RefUrl": "/notes/1710686"}, {"RefNumber": "1710384", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV performance improvements", "RefUrl": "/notes/1710384"}, {"RefNumber": "1710174", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Wrong user for incident management BI reporting setup", "RefUrl": "/notes/1710174"}, {"RefNumber": "1710170", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager Software Prerequisites", "RefUrl": "/notes/1710170"}, {"RefNumber": "1709694", "RefComponent": "SV-SMG-ISM", "RefTitle": "Bad Performance of Issue Management Functions", "RefUrl": "/notes/1709694"}, {"RefNumber": "1709230", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "Frequency adjustment ignores attribute extractor runtime", "RefUrl": "/notes/1709230"}, {"RefNumber": "1707863", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Alert <PERSON>t reporting: Too much data in time scale cubes", "RefUrl": "/notes/1707863"}, {"RefNumber": "1707803", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "DPC: Grey metrics due to \"Agent not registered\" message", "RefUrl": "/notes/1707803"}, {"RefNumber": "1706339", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "TWB BI reporting extraction: Insufficient load frequencies", "RefUrl": "/notes/1706339"}, {"RefNumber": "1704858", "RefComponent": "SV-SMG", "RefTitle": "Upgrade Dependency Analysis (UDA) Usage - false date", "RefUrl": "/notes/1704858"}, {"RefNumber": "1700972", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_CPY_PROCTYPE: Update - Missing view/table values", "RefUrl": "/notes/1700972"}, {"RefNumber": "1700486", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "System Status not updated in Managed System Overview", "RefUrl": "/notes/1700486"}, {"RefNumber": "1700427", "RefComponent": "BC-DB-SDB-CCM", "RefTitle": "MaxDB: Potential corruption of DB release in DB6NAVSYST", "RefUrl": "/notes/1700427"}, {"RefNumber": "1700043", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "E2E Alerting: Missing Metrics for Oracle, DB2 LUW and HANA", "RefUrl": "/notes/1700043"}, {"RefNumber": "1698275", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Database error during EEM setup", "RefUrl": "/notes/1698275"}, {"RefNumber": "1697102", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Threshold UI fix in Alert Details page", "RefUrl": "/notes/1697102"}, {"RefNumber": "1697052", "RefComponent": "BC-WD-ABA", "RefTitle": "WDA: Link in FormattedTextView causes a dump", "RefUrl": "/notes/1697052"}, {"RefNumber": "1696914", "RefComponent": "SV-SMG-SUP", "RefTitle": "Incident management: Short dump when sending to SAP", "RefUrl": "/notes/1696914"}, {"RefNumber": "1696526", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "Test workbench requests login to local system", "RefUrl": "/notes/1696526"}, {"RefNumber": "1696106", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Fixing Self Diagnosis Checks for SP04", "RefUrl": "/notes/1696106"}, {"RefNumber": "1696061", "RefComponent": "SV-SMG-SVD", "RefTitle": "Performance Improvement for Service Sessions in DSWP", "RefUrl": "/notes/1696061"}, {"RefNumber": "1695528", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Alert Object data fix", "RefUrl": "/notes/1695528"}, {"RefNumber": "1694458", "RefComponent": "BC-CUS-TOL-PAD", "RefTitle": "SOLAR_PROJECT_ADMIN:Select Countries not Cleared/Refreshed", "RefUrl": "/notes/1694458"}, {"RefNumber": "1694308", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1694308"}, {"RefNumber": "1690804", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1690804"}, {"RefNumber": "1690276", "RefComponent": "SV-SMG-LDB", "RefTitle": "Mapping: SMSY description from LMDB for MDM, Unspec, WebDISP", "RefUrl": "/notes/1690276"}, {"RefNumber": "1687237", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solution Mananager Setup, disable flash roadmap", "RefUrl": "/notes/1687237"}, {"RefNumber": "1687114", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Signature correction for clone method", "RefUrl": "/notes/1687114"}, {"RefNumber": "1686681", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Not able to reset managed object-specific changes", "RefUrl": "/notes/1686681"}, {"RefNumber": "1686396", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Duration evaluation displays incorrect values (ST 7.1)", "RefUrl": "/notes/1686396"}, {"RefNumber": "1685621", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "EEM SLA Dashboard: Values too low", "RefUrl": "/notes/1685621"}, {"RefNumber": "1685607", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Cannot delete template though no managed objects use it", "RefUrl": "/notes/1685607"}, {"RefNumber": "1685530", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Newly added metric variants do not work correctly", "RefUrl": "/notes/1685530"}, {"RefNumber": "1685463", "RefComponent": "SV-SMG-SVD", "RefTitle": "Cycle: Missing authority check for cycle creation button", "RefUrl": "/notes/1685463"}, {"RefNumber": "1685128", "RefComponent": "SV-SMG-DIA-SRV-CHK", "RefTitle": "Wrong central SAP note validated in Self Diagnosis", "RefUrl": "/notes/1685128"}, {"RefNumber": "1684910", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Data Provider Value help type fix", "RefUrl": "/notes/1684910"}, {"RefNumber": "1683290", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Transport of templates doesn't update assignment status", "RefUrl": "/notes/1683290"}, {"RefNumber": "1683279", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Dump deleting a custom template with custom metric variants", "RefUrl": "/notes/1683279"}, {"RefNumber": "1683260", "RefComponent": "SV-SMG-DIA-SRV-LSC", "RefTitle": "Error when jump to SelfDiagnosis status detail solman_setup", "RefUrl": "/notes/1683260"}, {"RefNumber": "1682750", "RefComponent": "BC-WD-ABA", "RefTitle": "FormattedTextView: Unexpected characters (such as #)", "RefUrl": "/notes/1682750"}, {"RefNumber": "1682370", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Removal of \"Reapply Table settings\" functionality", "RefUrl": "/notes/1682370"}, {"RefNumber": "1681889", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Notification table update for SP5/SP4/SP3/SP2", "RefUrl": "/notes/1681889"}, {"RefNumber": "1681853", "RefComponent": "SV-SMG-LDB", "RefTitle": "Shared memory causes problems for the content sync", "RefUrl": "/notes/1681853"}, {"RefNumber": "1681591", "RefComponent": "SV-SMG-LDB", "RefTitle": "Correction: SLD --> LMDB synch. connection error handling", "RefUrl": "/notes/1681591"}, {"RefNumber": "1679697", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Selection criteria ignored during POWL rendering", "RefUrl": "/notes/1679697"}, {"RefNumber": "1679055", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "MEAs activated in custom template shown as inactive", "RefUrl": "/notes/1679055"}, {"RefNumber": "1678162", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Solution Manager Setup: Performance when copying roles", "RefUrl": "/notes/1678162"}, {"RefNumber": "1677459", "RefComponent": "SV-SMG-MON-ALR-DIR", "RefTitle": "Collective corrections for the MEA Directory SP4", "RefUrl": "/notes/1677459"}, {"RefNumber": "1675691", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Managed object does not get consumer settings from template", "RefUrl": "/notes/1675691"}, {"RefNumber": "1675392", "RefComponent": "SV-SMG-SUP", "RefTitle": "Correct SAP Solution Manager Usage Count of Service Desk", "RefUrl": "/notes/1675392"}, {"RefNumber": "1675184", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Data inconsistency in MAI content leads to dump", "RefUrl": "/notes/1675184"}, {"RefNumber": "1674070", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Different installed products in different logon languages", "RefUrl": "/notes/1674070"}, {"RefNumber": "1673661", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Enabling setting of Sender for auto-notification emails", "RefUrl": "/notes/1673661"}, {"RefNumber": "1673201", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Row selection lost on Single/Multi selection modes", "RefUrl": "/notes/1673201"}, {"RefNumber": "1673128", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Modified metric variant thresholds do not work", "RefUrl": "/notes/1673128"}, {"RefNumber": "1672890", "RefComponent": "SV-SMG-MON-SFM", "RefTitle": "Collection of Self-Monitoing SP4 corrections", "RefUrl": "/notes/1672890"}, {"RefNumber": "1672797", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_IM_UPDATE_FROM_SAP: Unnecessary update of messages", "RefUrl": "/notes/1672797"}, {"RefNumber": "1672735", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Custom templates cannot be used as default", "RefUrl": "/notes/1672735"}, {"RefNumber": "1672429", "RefComponent": "SV-SMG-DIA-SRV-SET", "RefTitle": "Setup issue for HANA", "RefUrl": "/notes/1672429"}, {"RefNumber": "1672297", "RefComponent": "CRM-MD-BP", "RefTitle": "UIU:NO_EMP_FILTER parameter hidden in AccountSearchHelp (2)", "RefUrl": "/notes/1672297"}, {"RefNumber": "1671172", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_CPY_PROCTYPE: Runtime error ASSIGN_BASE_TO_SHORT", "RefUrl": "/notes/1671172"}, {"RefNumber": "1670189", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solution Manager 7.1 corrections for \"Managed System Config\"", "RefUrl": "/notes/1670189"}, {"RefNumber": "1668299", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Event missing if activated in custom/managed-object template", "RefUrl": "/notes/1668299"}, {"RefNumber": "1667344", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Fix for Importing Content Version", "RefUrl": "/notes/1667344"}, {"RefNumber": "1666479", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: System Value-Help is Empty", "RefUrl": "/notes/1666479"}, {"RefNumber": "1665775", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "Incorrect values in the test plan status evaluation", "RefUrl": "/notes/1665775"}, {"RefNumber": "1665086", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Alert Object fix for REACT_TO_CLOSED_ALERT BAdi API", "RefUrl": "/notes/1665086"}, {"RefNumber": "1664933", "RefComponent": "SV-SMG-SUP", "RefTitle": "Dump when you create an incident", "RefUrl": "/notes/1664933"}, {"RefNumber": "1664916", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_UPDATE_FROM_SAP_ISV", "RefUrl": "/notes/1664916"}, {"RefNumber": "1664744", "RefComponent": "SV-SMG-SDG", "RefTitle": "Self-Diagnosis alerts for EWA and Service Content Update", "RefUrl": "/notes/1664744"}, {"RefNumber": "1664509", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Issues with refresh when RefreshAll option is active", "RefUrl": "/notes/1664509"}, {"RefNumber": "1663545", "RefComponent": "SV-SMG-SUP", "RefTitle": "Correction in module DSWP_GET_BUSTRANS_USAGE", "RefUrl": "/notes/1663545"}, {"RefNumber": "1663497", "RefComponent": "SV-SMG-SUP", "RefTitle": "Open up Incident in authority check", "RefUrl": "/notes/1663497"}, {"RefNumber": "1662244", "RefComponent": "SV-SMG-LDB", "RefTitle": "LMDB->SMSY sync: Deletion of dual stack assignments in SMSY", "RefUrl": "/notes/1662244"}, {"RefNumber": "1662088", "RefComponent": "SV-SMG-LDB", "RefTitle": "Dump during migration of diagnostics-rel. product instances", "RefUrl": "/notes/1662088"}, {"RefNumber": "1661518", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "RFCs: Unable to generate RFCs using load balancing", "RefUrl": "/notes/1661518"}, {"RefNumber": "1661166", "RefComponent": "SV-SMG-LDB", "RefTitle": "SMSY: Message server for Java systems is incorrect", "RefUrl": "/notes/1661166"}, {"RefNumber": "1659868", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Analysis report icons missing in SP3 and SP4", "RefUrl": "/notes/1659868"}, {"RefNumber": "1659705", "RefComponent": "CRM-BF-PD", "RefTitle": "Consolidating note for issue pertaining to Employee BP", "RefUrl": "/notes/1659705"}, {"RefNumber": "1659686", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1659686"}, {"RefNumber": "1659631", "RefComponent": "CRM-BF-PD", "RefTitle": "Searching BP with Chinese characters", "RefUrl": "/notes/1659631"}, {"RefNumber": "1658536", "RefComponent": "BC-CCM-SLD-ABA", "RefTitle": "Bug Fix in CL_CIM_DATETIME", "RefUrl": "/notes/1658536"}, {"RefNumber": "1657871", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_CPY_PROCTYPE: Error in parameter conditions and dump", "RefUrl": "/notes/1657871"}, {"RefNumber": "1657694", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "Master data for message status missing in TWB BI Reporting", "RefUrl": "/notes/1657694"}, {"RefNumber": "1656963", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Commit command for logging the sending of alerts to SAP", "RefUrl": "/notes/1656963"}, {"RefNumber": "1656789", "RefComponent": "SV-SMG-SUP", "RefTitle": "Incident management: Problems with RFC determination", "RefUrl": "/notes/1656789"}, {"RefNumber": "1655832", "RefComponent": "CA-WUI-UI-RT", "RefTitle": "Error when determining status of nested model attributes", "RefUrl": "/notes/1655832"}, {"RefNumber": "1655601", "RefComponent": "HAN-DB", "RefTitle": "Extractors for Performance Warehouse", "RefUrl": "/notes/1655601"}, {"RefNumber": "1655577", "RefComponent": "BC-WD-CMP-FPM", "RefTitle": "FPM: IDR: Initialization of side panel", "RefUrl": "/notes/1655577"}, {"RefNumber": "1655460", "RefComponent": "BC-WD-CMP-FPM", "RefTitle": "FPM search comp: Program termin with attribute with OVS help", "RefUrl": "/notes/1655460"}, {"RefNumber": "1655203", "RefComponent": "CRM-ANA-OR", "RefTitle": "IR: OLTP Reports with Status Filter show no results", "RefUrl": "/notes/1655203"}, {"RefNumber": "1654623", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Problems when copying custom templates", "RefUrl": "/notes/1654623"}, {"RefNumber": "1653580", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_CPY_PROCTYPE: Incorrect data sorting", "RefUrl": "/notes/1653580"}, {"RefNumber": "1653216", "RefComponent": "SV-SMG-LDB", "RefTitle": "CL_LAPI_NOTIFICATION_CONSUMER - Exception CX_SY_OPEN_SQL_DB", "RefUrl": "/notes/1653216"}, {"RefNumber": "1652503", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Advance Corr. BPMon SM 7.1 ST710 SP05", "RefUrl": "/notes/1652503"}, {"RefNumber": "1652475", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Usernames, Password and Port Numbers Lost in DBCON", "RefUrl": "/notes/1652475"}, {"RefNumber": "1651510", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "LMDB upgrade activities missing in solman_setup", "RefUrl": "/notes/1651510"}, {"RefNumber": "1651279", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "Corrections for EEM 7.1 SP04", "RefUrl": "/notes/1651279"}, {"RefNumber": "1650350", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_IM_UPDATE_FROM_SAP: User status not updated", "RefUrl": "/notes/1650350"}, {"RefNumber": "1650322", "RefComponent": "SV-SMG-MON-SYS", "RefTitle": "Incorrect values in system availability app of the dashboard", "RefUrl": "/notes/1650322"}, {"RefNumber": "1650032", "RefComponent": "SV-SMG-DIA-SRV-LSC", "RefTitle": "Landscape LMDB wrapper SP4 related corrections", "RefUrl": "/notes/1650032"}, {"RefNumber": "1649297", "RefComponent": "BC-WD-ABA", "RefTitle": "Corrections for unified rendering 702/11 II (UR-Mimes)", "RefUrl": "/notes/1649297"}, {"RefNumber": "1649247", "RefComponent": "BC-WD-ABA", "RefTitle": "Corrections for unified rendering 702/11 II (ABAP-Renderer)", "RefUrl": "/notes/1649247"}, {"RefNumber": "1649228", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Change in lead selection not considered", "RefUrl": "/notes/1649228"}, {"RefNumber": "1648959", "RefComponent": "SV-SMG-DIA", "RefTitle": "solman_setup Configuration Check reorganization", "RefUrl": "/notes/1648959"}, {"RefNumber": "1648712", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1648712"}, {"RefNumber": "1648271", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Threshold values are not saved for derived metric variant", "RefUrl": "/notes/1648271"}, {"RefNumber": "1648230", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: Character ',' disappears in long text", "RefUrl": "/notes/1648230"}, {"RefNumber": "1647542", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Incident Management Reporting: Messages are missing", "RefUrl": "/notes/1647542"}, {"RefNumber": "1645762", "RefComponent": "SV-SMG-LDB", "RefTitle": "SMSY:Incorr assgmt of Java tech syst for ABAP prod instances", "RefUrl": "/notes/1645762"}, {"RefNumber": "1645356", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: User and Password Should Not Be Read-Only for Local DB", "RefUrl": "/notes/1645356"}, {"RefNumber": "1644866", "RefComponent": "SV-SMG-LDB", "RefTitle": "Initial synchronization of data storages dependent on LMDB", "RefUrl": "/notes/1644866"}, {"RefNumber": "1644615", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: time Changed At in watch list is not correct", "RefUrl": "/notes/1644615"}, {"RefNumber": "1644401", "RefComponent": "SV-SMG-SUP", "RefTitle": "Incident Mgmt:SAP Collaboration assgmt block:Prob w/ Inst no", "RefUrl": "/notes/1644401"}, {"RefNumber": "1644147", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: CRM order lock is lost in edit mode", "RefUrl": "/notes/1644147"}, {"RefNumber": "1643760", "RefComponent": "SV-SMG-LDB", "RefTitle": "No target namespace when settung up LMDB", "RefUrl": "/notes/1643760"}, {"RefNumber": "1641387", "RefComponent": "CA-WUI-UI-TAG", "RefTitle": "WEBCUIF: Exception selecting data in Flex Table Graphics", "RefUrl": "/notes/1641387"}, {"RefNumber": "1640239", "RefComponent": "CRM-BF-TM", "RefTitle": "Texts are determined again when a partner is changed", "RefUrl": "/notes/1640239"}, {"RefNumber": "1633482", "RefComponent": "BC-WD-ABA", "RefTitle": "Correction for unified rendering 702/10 V (UR-Mimes)", "RefUrl": "/notes/1633482"}, {"RefNumber": "1633476", "RefComponent": "BC-WD-ABA", "RefTitle": "Correction for unified rendering 702/10 V (ABAP-Renderer)", "RefUrl": "/notes/1633476"}, {"RefNumber": "1629551", "RefComponent": "BC-WD-ABA", "RefTitle": "Business Graphics: Dump due to missing resources", "RefUrl": "/notes/1629551"}, {"RefNumber": "1629407", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DBA Cockpit: Runtime error OBJECTS_OBJREF_NOT_ASSIGNED", "RefUrl": "/notes/1629407"}, {"RefNumber": "1623160", "RefComponent": "CRM-BF-COM", "RefTitle": "Error message CRM_DOCUMENTS 107 for document template search", "RefUrl": "/notes/1623160"}, {"RefNumber": "1622329", "RefComponent": "CRM-BTX-SVO", "RefTitle": "Display of external reference number does not happen", "RefUrl": "/notes/1622329"}, {"RefNumber": "1619830", "RefComponent": "SV-SMG-OP", "RefTitle": "Performance problem when creating an issue", "RefUrl": "/notes/1619830"}, {"RefNumber": "1617181", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "BPO Dashboard - Update log to wait for ccms_bi job", "RefUrl": "/notes/1617181"}, {"RefNumber": "1615423", "RefComponent": "SV-SMG-SUP", "RefTitle": "Messages are created with the wrong transaction type", "RefUrl": "/notes/1615423"}, {"RefNumber": "1614747", "RefComponent": "SV-SMG", "RefTitle": "Report for creating products creates incorrect product", "RefUrl": "/notes/1614747"}, {"RefNumber": "1614206", "RefComponent": "BC-ESI-WS-ABA-RT", "RefTitle": "Empty response for WS calls for request-response operations", "RefUrl": "/notes/1614206"}, {"RefNumber": "1612603", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "ORA: Mandatory connection parameter 'DBNAME' not specified", "RefUrl": "/notes/1612603"}, {"RefNumber": "1610374", "RefComponent": "BW-BCT-CMS", "RefTitle": "Incorrect data in the metric monitor/inactive BW content", "RefUrl": "/notes/1610374"}, {"RefNumber": "1609361", "RefComponent": "BC-ESI-WS-ABA-RT", "RefTitle": "Statistical records for Web Service calls missing", "RefUrl": "/notes/1609361"}, {"RefNumber": "1608457", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert Session Not Sent To SAP", "RefUrl": "/notes/1608457"}, {"RefNumber": "1604471", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "SLA-relevant scripts are missing in EEM BI Reporting", "RefUrl": "/notes/1604471"}, {"RefNumber": "1604209", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "ORA/MSS: Wrong error message for local system, no DBSL found", "RefUrl": "/notes/1604209"}, {"RefNumber": "1602519", "RefComponent": "BC-ABA-TO", "RefTitle": "FREE_SELECTIONS_RANGE_2_WHERE: Problem w/ special characters", "RefUrl": "/notes/1602519"}, {"RefNumber": "1599582", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Plugin Status Details - Delete sp_level check for ST-A/PI", "RefUrl": "/notes/1599582"}, {"RefNumber": "1589050", "RefComponent": "CRM-IC-BF-CAT", "RefTitle": "No Categorization schema assigned to application area", "RefUrl": "/notes/1589050"}, {"RefNumber": "1587690", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Problems with selective deletion in packages", "RefUrl": "/notes/1587690"}, {"RefNumber": "1579462", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Sending data from chart item without check to IGS", "RefUrl": "/notes/1579462"}, {"RefNumber": "1576693", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Source code corrs: Interactive reporting/IT Performance Rep.", "RefUrl": "/notes/1576693"}, {"RefNumber": "1572183", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users", "RefUrl": "/notes/1572183"}, {"RefNumber": "1570399", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Solution Manager BI reporting", "RefUrl": "/notes/1570399"}, {"RefNumber": "1559499", "RefComponent": "SV-SMG-MON-SYS", "RefTitle": "DPC data providers as of ST-PI 2008_1_XX SP4", "RefUrl": "/notes/1559499"}, {"RefNumber": "1552978", "RefComponent": "SV-SMG-MON-SLR", "RefTitle": "SL Reporting: Downtimes can no longer be maintained", "RefUrl": "/notes/1552978"}, {"RefNumber": "1488293", "RefComponent": "CRM-BTX-SRQ", "RefTitle": "Short dump occurs when creating service req. from template", "RefUrl": "/notes/1488293"}, {"RefNumber": "1487329", "RefComponent": "BC-DWB-TOO-FUB", "RefTitle": "Additional Authorization Check in ABAP Workbench", "RefUrl": "/notes/1487329"}, {"RefNumber": "1477732", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Characteristic filter set by variable exit not refreshed", "RefUrl": "/notes/1477732"}, {"RefNumber": "1457391", "RefComponent": "AP-MD-IBA", "RefTitle": "Bad Performance while accessing table IBST.", "RefUrl": "/notes/1457391"}, {"RefNumber": "1297738", "RefComponent": "BC-INS-CTC", "RefTitle": "To allow CTC APIs to add Non BW system to BW system", "RefUrl": "/notes/1297738"}, {"RefNumber": "1094074", "RefComponent": "CRM-BTX-SVO", "RefTitle": "Unnecessary Authority Check in CRM_DNO_MONITOR", "RefUrl": "/notes/1094074"}, {"RefNumber": "1011229", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-PI: Corrections for E2E Diagnostics", "RefUrl": "/notes/1011229"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1758728", "RefComponent": "SV-SMG-SYS", "RefTitle": "\"Active\" selection of product versions is removed", "RefUrl": "/notes/1758728 "}, {"RefNumber": "1094074", "RefComponent": "CRM-BTX-SVO", "RefTitle": "Unnecessary Authority Check in CRM_DNO_MONITOR", "RefUrl": "/notes/1094074 "}, {"RefNumber": "1710174", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Wrong user for incident management BI reporting setup", "RefUrl": "/notes/1710174 "}, {"RefNumber": "1650322", "RefComponent": "SV-SMG-MON-SYS", "RefTitle": "Incorrect values in system availability app of the dashboard", "RefUrl": "/notes/1650322 "}, {"RefNumber": "1665775", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "Incorrect values in the test plan status evaluation", "RefUrl": "/notes/1665775 "}, {"RefNumber": "1657694", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "Master data for message status missing in TWB BI Reporting", "RefUrl": "/notes/1657694 "}, {"RefNumber": "1706339", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "TWB BI reporting extraction: Insufficient load frequencies", "RefUrl": "/notes/1706339 "}, {"RefNumber": "1700427", "RefComponent": "BC-DB-SDB-CCM", "RefTitle": "MaxDB: Potential corruption of DB release in DB6NAVSYST", "RefUrl": "/notes/1700427 "}, {"RefNumber": "1816471", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SE24: Error for POST methods for defined preferred parameter", "RefUrl": "/notes/1816471 "}, {"RefNumber": "1710170", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager Software Prerequisites", "RefUrl": "/notes/1710170 "}, {"RefNumber": "1570399", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Solution Manager BI reporting", "RefUrl": "/notes/1570399 "}, {"RefNumber": "1804373", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup : The BW content is not active", "RefUrl": "/notes/1804373 "}, {"RefNumber": "1717403", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Collective Note for Extractor FWK - ST710 (SP05 - SP08)", "RefUrl": "/notes/1717403 "}, {"RefNumber": "1739631", "RefComponent": "SV-SMG-SVD", "RefTitle": "Service delivery session information not updated accordingly", "RefUrl": "/notes/1739631 "}, {"RefNumber": "1849098", "RefComponent": "SV-SMG-LDB", "RefTitle": "Error handling in content synchronization", "RefUrl": "/notes/1849098 "}, {"RefNumber": "1297738", "RefComponent": "BC-INS-CTC", "RefTitle": "To allow CTC APIs to add Non BW system to BW system", "RefUrl": "/notes/1297738 "}, {"RefNumber": "1821727", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Detail area not updated in accordance with current lead selection", "RefUrl": "/notes/1821727 "}, {"RefNumber": "1886549", "RefComponent": "SV-SMG-LDB", "RefTitle": "Flag TEMP_INACTIVE in table SMSY_SYSTEM_SAP wrong", "RefUrl": "/notes/1886549 "}, {"RefNumber": "1750618", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "RFC destinations created in SMSU_MANAGED_SYSTEM not delete", "RefUrl": "/notes/1750618 "}, {"RefNumber": "1833865", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Avoid Obsolete Recipient Lists/Recipients during transport", "RefUrl": "/notes/1833865 "}, {"RefNumber": "1885765", "RefComponent": "SV-SMG-SDG", "RefTitle": "Self-Diagnosis: Performance issues due to connection checks", "RefUrl": "/notes/1885765 "}, {"RefNumber": "1572183", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users", "RefUrl": "/notes/1572183 "}, {"RefNumber": "1679697", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Selection criteria ignored during POWL rendering", "RefUrl": "/notes/1679697 "}, {"RefNumber": "1849566", "RefComponent": "SV-SMG-SVC", "RefTitle": "Users without authorization see the installations", "RefUrl": "/notes/1849566 "}, {"RefNumber": "1813468", "RefComponent": "BC-WD-ABA", "RefTitle": "Web Dynpro: Conversion for non-printable chars (NON-UNICODE)", "RefUrl": "/notes/1813468 "}, {"RefNumber": "1738511", "RefComponent": "SV-SMG-IMP", "RefTitle": "Editing MS Office documents in SAP Solution Manager", "RefUrl": "/notes/1738511 "}, {"RefNumber": "1738276", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "DSA: Word creation dumps caused by memory lack", "RefUrl": "/notes/1738276 "}, {"RefNumber": "1851723", "RefComponent": "SV-SMG-SVC", "RefTitle": "REFRESH_ADMIN_DATA_FROM_SUPPORT no licence data generation", "RefUrl": "/notes/1851723 "}, {"RefNumber": "1654623", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Problems when copying custom templates", "RefUrl": "/notes/1654623 "}, {"RefNumber": "875986", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS up to 702", "RefUrl": "/notes/875986 "}, {"RefNumber": "1837889", "RefComponent": "SV-SMG-SUP", "RefTitle": "VAR scenario: Unjustified deactivation of S users", "RefUrl": "/notes/1837889 "}, {"RefNumber": "1855272", "RefComponent": "SV-SMG-INS-CFG-MNG", "RefTitle": "SOLMAN_SETUP: admin user shouldn't modify S* profiles", "RefUrl": "/notes/1855272 "}, {"RefNumber": "1599582", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Plugin Status Details - Delete sp_level check for ST-A/PI", "RefUrl": "/notes/1599582 "}, {"RefNumber": "1682750", "RefComponent": "BC-WD-ABA", "RefTitle": "FormattedTextView: Unexpected characters (such as #)", "RefUrl": "/notes/1682750 "}, {"RefNumber": "1780509", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Select Options for Metric parameter values is not working", "RefUrl": "/notes/1780509 "}, {"RefNumber": "1709230", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "Frequency adjustment ignores attribute extractor runtime", "RefUrl": "/notes/1709230 "}, {"RefNumber": "1770638", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Working with Templates in different languages", "RefUrl": "/notes/1770638 "}, {"RefNumber": "1743244", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Better description needed in query template dropdown", "RefUrl": "/notes/1743244 "}, {"RefNumber": "1719016", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: obsolete roles still added to users", "RefUrl": "/notes/1719016 "}, {"RefNumber": "1629551", "RefComponent": "BC-WD-ABA", "RefTitle": "Business Graphics: Dump due to missing resources", "RefUrl": "/notes/1629551 "}, {"RefNumber": "1769570", "RefComponent": "SV-SMG-DVM", "RefTitle": "Function Moduel SMD_DATA_LOADER101 not found", "RefUrl": "/notes/1769570 "}, {"RefNumber": "1821638", "RefComponent": "CRM-BTX-SRQ", "RefTitle": "Rule Policy on Multilevel Categorization", "RefUrl": "/notes/1821638 "}, {"RefNumber": "1477732", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Characteristic filter set by variable exit not refreshed", "RefUrl": "/notes/1477732 "}, {"RefNumber": "1797388", "RefComponent": "BC-WD-ABA", "RefTitle": "Web Dynpro: Conversion of non-prinatable characters", "RefUrl": "/notes/1797388 "}, {"RefNumber": "1763065", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Reporting API returns too much data and response is slow", "RefUrl": "/notes/1763065 "}, {"RefNumber": "1824197", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "STWB_2 - Test plan display requires re-logon", "RefUrl": "/notes/1824197 "}, {"RefNumber": "1808259", "RefComponent": "SV-SMG-SVD-GSS", "RefTitle": "Wrong information text for column title in Web DSA UI", "RefUrl": "/notes/1808259 "}, {"RefNumber": "1820291", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Project Documentation tab flagged as changed in Template", "RefUrl": "/notes/1820291 "}, {"RefNumber": "1823420", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: Fatal Error - CX_COMPONENT_VERSION_NOT_FOUND", "RefUrl": "/notes/1823420 "}, {"RefNumber": "1813914", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: ConfigStore bo40.dump_all_xml - SYSTEM_NO_ROLL", "RefUrl": "/notes/1813914 "}, {"RefNumber": "1763274", "RefComponent": "BC-WD-ABA", "RefTitle": "Select options: Error when deleting a parameter", "RefUrl": "/notes/1763274 "}, {"RefNumber": "1576693", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Source code corrs: Interactive reporting/IT Performance Rep.", "RefUrl": "/notes/1576693 "}, {"RefNumber": "1814921", "RefComponent": "BC-CCM-BTC", "RefTitle": "BAE: Performance issue when using RFC_VERIFY_DESTINATION", "RefUrl": "/notes/1814921 "}, {"RefNumber": "1559499", "RefComponent": "SV-SMG-MON-SYS", "RefTitle": "DPC data providers as of ST-PI 2008_1_XX SP4", "RefUrl": "/notes/1559499 "}, {"RefNumber": "1761039", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP08", "RefUrl": "/notes/1761039 "}, {"RefNumber": "1736448", "RefComponent": "SV-SMG-SDG", "RefTitle": "Dump in Self Diagnosis due to duplicate keys.", "RefUrl": "/notes/1736448 "}, {"RefNumber": "1677459", "RefComponent": "SV-SMG-MON-ALR-DIR", "RefTitle": "Collective corrections for the MEA Directory SP4", "RefUrl": "/notes/1677459 "}, {"RefNumber": "1651279", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "Corrections for EEM 7.1 SP04", "RefUrl": "/notes/1651279 "}, {"RefNumber": "1685128", "RefComponent": "SV-SMG-DIA-SRV-CHK", "RefTitle": "Wrong central SAP note validated in Self Diagnosis", "RefUrl": "/notes/1685128 "}, {"RefNumber": "1783371", "RefComponent": "SV-SMG-MAI", "RefTitle": "MOpz: System landscape data incomplete in Customer Profile", "RefUrl": "/notes/1783371 "}, {"RefNumber": "1733236", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP07", "RefUrl": "/notes/1733236 "}, {"RefNumber": "1775643", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_SDK_SP_GENERATE_BP_V2: diverse corrections III", "RefUrl": "/notes/1775643 "}, {"RefNumber": "1652503", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Advance Corr. BPMon SM 7.1 ST710 SP05", "RefUrl": "/notes/1652503 "}, {"RefNumber": "1011229", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-PI: Corrections for E2E Diagnostics", "RefUrl": "/notes/1011229 "}, {"RefNumber": "1749774", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Request popup opens, eventhough template saved under $TMP", "RefUrl": "/notes/1749774 "}, {"RefNumber": "1784446", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Custom Metric Variants were not copied during copy template", "RefUrl": "/notes/1784446 "}, {"RefNumber": "1457391", "RefComponent": "AP-MD-IBA", "RefTitle": "Bad Performance while accessing table IBST.", "RefUrl": "/notes/1457391 "}, {"RefNumber": "1716300", "RefComponent": "SV-SMG-LDB", "RefTitle": "Bug fixes for LMDB notification cleanup", "RefUrl": "/notes/1716300 "}, {"RefNumber": "1784936", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Transport Custom Metric Variants from one system to other system", "RefUrl": "/notes/1784936 "}, {"RefNumber": "1759409", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Dump when \"enable default lead selection\" is ON", "RefUrl": "/notes/1759409 "}, {"RefNumber": "1643760", "RefComponent": "SV-SMG-LDB", "RefTitle": "No target namespace when settung up LMDB", "RefUrl": "/notes/1643760 "}, {"RefNumber": "1684910", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Data Provider Value help type fix", "RefUrl": "/notes/1684910 "}, {"RefNumber": "1775883", "RefComponent": "SV-SMG-SVD-GSS", "RefTitle": "GSS: unexpected dump when using webdynpro logon popup in GSS", "RefUrl": "/notes/1775883 "}, {"RefNumber": "1678162", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Solution Manager Setup: Performance when copying roles", "RefUrl": "/notes/1678162 "}, {"RefNumber": "1777767", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Incomplete display of messages in component view", "RefUrl": "/notes/1777767 "}, {"RefNumber": "1774418", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary refresh triggered when using query api", "RefUrl": "/notes/1774418 "}, {"RefNumber": "1763793", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Column headers are rendered incorrectly", "RefUrl": "/notes/1763793 "}, {"RefNumber": "1768764", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "TWB reporting displays incorrect test case status counters", "RefUrl": "/notes/1768764 "}, {"RefNumber": "1734821", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "E2E Alerting: Enable Metric Filter for Tablespaces", "RefUrl": "/notes/1734821 "}, {"RefNumber": "1672797", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_IM_UPDATE_FROM_SAP: Unnecessary update of messages", "RefUrl": "/notes/1672797 "}, {"RefNumber": "1729753", "RefComponent": "SV-SMG-SUP", "RefTitle": "ST710: ITSM document locked by own user after saving", "RefUrl": "/notes/1729753 "}, {"RefNumber": "1762967", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "DPW: Overlapping records in DBH_CUBE_DATA", "RefUrl": "/notes/1762967 "}, {"RefNumber": "1659705", "RefComponent": "CRM-BF-PD", "RefTitle": "Consolidating note for issue pertaining to Employee BP", "RefUrl": "/notes/1659705 "}, {"RefNumber": "1734250", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Error in detail area update", "RefUrl": "/notes/1734250 "}, {"RefNumber": "1700486", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "System Status not updated in Managed System Overview", "RefUrl": "/notes/1700486 "}, {"RefNumber": "1745879", "RefComponent": "SV-SMG-SDG", "RefTitle": "Failed to get RTCC recommendation due to RFC call failure.", "RefUrl": "/notes/1745879 "}, {"RefNumber": "1748138", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary update of detail area", "RefUrl": "/notes/1748138 "}, {"RefNumber": "1741080", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "no/wrong metrics collected on SMD Agent assoicated to DB", "RefUrl": "/notes/1741080 "}, {"RefNumber": "1752517", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Managed system configuration - warning in step 7 create user", "RefUrl": "/notes/1752517 "}, {"RefNumber": "1754275", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Wrong message 'RFC SM_XXXCLNTnnn_BACK does not exist'", "RefUrl": "/notes/1754275 "}, {"RefNumber": "1753985", "RefComponent": "SV-SMG-SDG", "RefTitle": "Downloaded XML data for Root Cause Analysis is not correct", "RefUrl": "/notes/1753985 "}, {"RefNumber": "1752181", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "Grey metrics in Technical System Monitoring", "RefUrl": "/notes/1752181 "}, {"RefNumber": "1700043", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "E2E Alerting: Missing Metrics for Oracle, DB2 LUW and HANA", "RefUrl": "/notes/1700043 "}, {"RefNumber": "1740545", "RefComponent": "CRM-IC-BF-CAT", "RefTitle": "Categories trigger entity data change", "RefUrl": "/notes/1740545 "}, {"RefNumber": "1727449", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Dump/Time out on selecting the modified metric at MO level", "RefUrl": "/notes/1727449 "}, {"RefNumber": "1749795", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DPW: Housekeeping does not finish", "RefUrl": "/notes/1749795 "}, {"RefNumber": "1649228", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Change in lead selection not considered", "RefUrl": "/notes/1649228 "}, {"RefNumber": "1672297", "RefComponent": "CRM-MD-BP", "RefTitle": "UIU:NO_EMP_FILTER parameter hidden in AccountSearchHelp (2)", "RefUrl": "/notes/1672297 "}, {"RefNumber": "1741470", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV error when context changes are dispatched", "RefUrl": "/notes/1741470 "}, {"RefNumber": "1733824", "RefComponent": "BC-WD-ABA", "RefTitle": "Dump in CL_WDR_NOTIFICATION", "RefUrl": "/notes/1733824 "}, {"RefNumber": "1650032", "RefComponent": "SV-SMG-DIA-SRV-LSC", "RefTitle": "Landscape LMDB wrapper SP4 related corrections", "RefUrl": "/notes/1650032 "}, {"RefNumber": "1745114", "RefComponent": "SV-SMG", "RefTitle": "DBA: Dump in CL_DBA_DBH_CUBE_DATA", "RefUrl": "/notes/1745114 "}, {"RefNumber": "1745942", "RefComponent": "SV-SMG", "RefTitle": "DPW: Cube Aggregation Memory Usage", "RefUrl": "/notes/1745942 "}, {"RefNumber": "1719578", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Fallback language text for Managed Obj., Scn.type & Category", "RefUrl": "/notes/1719578 "}, {"RefNumber": "1732144", "RefComponent": "SV-SMG-MON-ALR-CLC", "RefTitle": "Wrong count of used metric instances", "RefUrl": "/notes/1732144 "}, {"RefNumber": "1733998", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: activate services on managed systems", "RefUrl": "/notes/1733998 "}, {"RefNumber": "1737668", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Data not re-organized after upgrade", "RefUrl": "/notes/1737668 "}, {"RefNumber": "1682370", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Removal of \"Reapply Table settings\" functionality", "RefUrl": "/notes/1682370 "}, {"RefNumber": "1604471", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "SLA-relevant scripts are missing in EEM BI Reporting", "RefUrl": "/notes/1604471 "}, {"RefNumber": "1694458", "RefComponent": "BC-CUS-TOL-PAD", "RefTitle": "SOLAR_PROJECT_ADMIN:Select Countries not Cleared/Refreshed", "RefUrl": "/notes/1694458 "}, {"RefNumber": "1737700", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_CPY_PROCTYPE: PPF containers missing from transp.req.", "RefUrl": "/notes/1737700 "}, {"RefNumber": "1736425", "RefComponent": "SV-SMG-LDB", "RefTitle": "Technical Scenario DUAL_STACK: MaxLen violated", "RefUrl": "/notes/1736425 "}, {"RefNumber": "1670189", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solution Manager 7.1 corrections for \"Managed System Config\"", "RefUrl": "/notes/1670189 "}, {"RefNumber": "1612603", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "ORA: Mandatory connection parameter 'DBNAME' not specified", "RefUrl": "/notes/1612603 "}, {"RefNumber": "1610374", "RefComponent": "BW-BCT-CMS", "RefTitle": "Incorrect data in the metric monitor/inactive BW content", "RefUrl": "/notes/1610374 "}, {"RefNumber": "1675691", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Managed object does not get consumer settings from template", "RefUrl": "/notes/1675691 "}, {"RefNumber": "1731652", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "SMD Agent does not receive a role within role configuration", "RefUrl": "/notes/1731652 "}, {"RefNumber": "1730457", "RefComponent": "SV-SMG-SUP", "RefTitle": "VAR: Texts duplicated after exchange with SAP", "RefUrl": "/notes/1730457 "}, {"RefNumber": "1710384", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV performance improvements", "RefUrl": "/notes/1710384 "}, {"RefNumber": "1729591", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Error: No Long Name Found in SMD_HASH_TABLE for Hash-ID", "RefUrl": "/notes/1729591 "}, {"RefNumber": "1728631", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Extractor scheduling for ALM and ASR", "RefUrl": "/notes/1728631 "}, {"RefNumber": "1685463", "RefComponent": "SV-SMG-SVD", "RefTitle": "Cycle: Missing authority check for cycle creation button", "RefUrl": "/notes/1685463 "}, {"RefNumber": "1721598", "RefComponent": "SV-SMG-DIA", "RefTitle": "Outside Discovery: Improvement to fill missing HANA props", "RefUrl": "/notes/1721598 "}, {"RefNumber": "1724761", "RefComponent": "SV-SMG-OP", "RefTitle": "Business Process Monitoring attributes missing in solution", "RefUrl": "/notes/1724761 "}, {"RefNumber": "1667344", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Fix for Importing Content Version", "RefUrl": "/notes/1667344 "}, {"RefNumber": "1719142", "RefComponent": "SV-SMG-SDA", "RefTitle": "Problems solved with ST 710 SP06", "RefUrl": "/notes/1719142 "}, {"RefNumber": "1727730", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: BW host name provided in uper case cause dumps", "RefUrl": "/notes/1727730 "}, {"RefNumber": "1709694", "RefComponent": "SV-SMG-ISM", "RefTitle": "Bad Performance of Issue Management Functions", "RefUrl": "/notes/1709694 "}, {"RefNumber": "1681889", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Notification table update for SP5/SP4/SP3/SP2", "RefUrl": "/notes/1681889 "}, {"RefNumber": "1687114", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Signature correction for clone method", "RefUrl": "/notes/1687114 "}, {"RefNumber": "1726424", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Connection failed (RTE:[2] too many database sessions active", "RefUrl": "/notes/1726424 "}, {"RefNumber": "1726133", "RefComponent": "SV-SMG-SDA", "RefTitle": "Incorrect calculation of used workload objects", "RefUrl": "/notes/1726133 "}, {"RefNumber": "1683290", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Transport of templates doesn't update assignment status", "RefUrl": "/notes/1683290 "}, {"RefNumber": "1717888", "RefComponent": "SV-SMG-ISM", "RefTitle": "Issue Management: bad performance in context and task tab", "RefUrl": "/notes/1717888 "}, {"RefNumber": "1696061", "RefComponent": "SV-SMG-SVD", "RefTitle": "Performance Improvement for Service Sessions in DSWP", "RefUrl": "/notes/1696061 "}, {"RefNumber": "1722178", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "TwinCube reorganization does not work", "RefUrl": "/notes/1722178 "}, {"RefNumber": "1722791", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Changes by Compare and Adjust on transaction tab are lost", "RefUrl": "/notes/1722791 "}, {"RefNumber": "1707803", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "DPC: Grey metrics due to \"Agent not registered\" message", "RefUrl": "/notes/1707803 "}, {"RefNumber": "1722197", "RefComponent": "SV-SMG-OP", "RefTitle": "Where-used list for logical components is incorrect", "RefUrl": "/notes/1722197 "}, {"RefNumber": "1712091", "RefComponent": "CRM-FRW-UI", "RefTitle": "CRM WebUI scrolls top on roundtrips", "RefUrl": "/notes/1712091 "}, {"RefNumber": "1604209", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "ORA/MSS: Wrong error message for local system, no DBSL found", "RefUrl": "/notes/1604209 "}, {"RefNumber": "1683260", "RefComponent": "SV-SMG-DIA-SRV-LSC", "RefTitle": "Error when jump to SelfDiagnosis status detail solman_setup", "RefUrl": "/notes/1683260 "}, {"RefNumber": "1587690", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Problems with selective deletion in packages", "RefUrl": "/notes/1587690 "}, {"RefNumber": "1720336", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Correction to priority when creating automatic incidents", "RefUrl": "/notes/1720336 "}, {"RefNumber": "1717775", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "Metric Troubleshooting Guide shows wrong information", "RefUrl": "/notes/1717775 "}, {"RefNumber": "1714900", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Check of users and roles when the CUA is inactive", "RefUrl": "/notes/1714900 "}, {"RefNumber": "1671172", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_CPY_PROCTYPE: Runtime error ASSIGN_BASE_TO_SHORT", "RefUrl": "/notes/1671172 "}, {"RefNumber": "1711748", "RefComponent": "SV-SMG-LDB", "RefTitle": "Improvement of Report RLMDB_ADD_GUIDS", "RefUrl": "/notes/1711748 "}, {"RefNumber": "1715675", "RefComponent": "SV-SMG-SDA", "RefTitle": "Content upload: missing logical components", "RefUrl": "/notes/1715675 "}, {"RefNumber": "1715620", "RefComponent": "SV-SMG-SDA", "RefTitle": "Search result not in visible area for table tree", "RefUrl": "/notes/1715620 "}, {"RefNumber": "1664509", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Issues with refresh when RefreshAll option is active", "RefUrl": "/notes/1664509 "}, {"RefNumber": "1655601", "RefComponent": "HAN-DB", "RefTitle": "Extractors for Performance Warehouse", "RefUrl": "/notes/1655601 "}, {"RefNumber": "1710686", "RefComponent": "SV-SMG-SDG", "RefTitle": "Configuration Check error for Root cause analysis.", "RefUrl": "/notes/1710686 "}, {"RefNumber": "1579462", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Sending data from chart item without check to IGS", "RefUrl": "/notes/1579462 "}, {"RefNumber": "1696106", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Fixing Self Diagnosis Checks for SP04", "RefUrl": "/notes/1696106 "}, {"RefNumber": "1712878", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "Technical Host returns grey metrics", "RefUrl": "/notes/1712878 "}, {"RefNumber": "1713085", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM Work Center: Disable Multiple Save on User Click", "RefUrl": "/notes/1713085 "}, {"RefNumber": "1662088", "RefComponent": "SV-SMG-LDB", "RefTitle": "Dump during migration of diagnostics-rel. product instances", "RefUrl": "/notes/1662088 "}, {"RefNumber": "1681591", "RefComponent": "SV-SMG-LDB", "RefTitle": "Correction: SLD --> LMDB synch. connection error handling", "RefUrl": "/notes/1681591 "}, {"RefNumber": "1661166", "RefComponent": "SV-SMG-LDB", "RefTitle": "SMSY: Message server for Java systems is incorrect", "RefUrl": "/notes/1661166 "}, {"RefNumber": "1711380", "RefComponent": "SV-SMG-SUP-IFA", "RefTitle": "External Service Desk interface: Texts are duplicated", "RefUrl": "/notes/1711380 "}, {"RefNumber": "1673201", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Row selection lost on Single/Multi selection modes", "RefUrl": "/notes/1673201 "}, {"RefNumber": "1707863", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Alert <PERSON>t reporting: Too much data in time scale cubes", "RefUrl": "/notes/1707863 "}, {"RefNumber": "1704858", "RefComponent": "SV-SMG", "RefTitle": "Upgrade Dependency Analysis (UDA) Usage - false date", "RefUrl": "/notes/1704858 "}, {"RefNumber": "1629407", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DBA Cockpit: Runtime error OBJECTS_OBJREF_NOT_ASSIGNED", "RefUrl": "/notes/1629407 "}, {"RefNumber": "1619830", "RefComponent": "SV-SMG-OP", "RefTitle": "Performance problem when creating an issue", "RefUrl": "/notes/1619830 "}, {"RefNumber": "1647542", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Incident Management Reporting: Messages are missing", "RefUrl": "/notes/1647542 "}, {"RefNumber": "1617181", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "BPO Dashboard - Update log to wait for ccms_bi job", "RefUrl": "/notes/1617181 "}, {"RefNumber": "1690276", "RefComponent": "SV-SMG-LDB", "RefTitle": "Mapping: SMSY description from LMDB for MDM, Unspec, WebDISP", "RefUrl": "/notes/1690276 "}, {"RefNumber": "1615423", "RefComponent": "SV-SMG-SUP", "RefTitle": "Messages are created with the wrong transaction type", "RefUrl": "/notes/1615423 "}, {"RefNumber": "1686681", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Not able to reset managed object-specific changes", "RefUrl": "/notes/1686681 "}, {"RefNumber": "1700972", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_CPY_PROCTYPE: Update - Missing view/table values", "RefUrl": "/notes/1700972 "}, {"RefNumber": "1672890", "RefComponent": "SV-SMG-MON-SFM", "RefTitle": "Collection of Self-Monitoing SP4 corrections", "RefUrl": "/notes/1672890 "}, {"RefNumber": "1695528", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Alert Object data fix", "RefUrl": "/notes/1695528 "}, {"RefNumber": "1698275", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Database error during EEM setup", "RefUrl": "/notes/1698275 "}, {"RefNumber": "1697052", "RefComponent": "BC-WD-ABA", "RefTitle": "WDA: Link in FormattedTextView causes a dump", "RefUrl": "/notes/1697052 "}, {"RefNumber": "1657871", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_CPY_PROCTYPE: Error in parameter conditions and dump", "RefUrl": "/notes/1657871 "}, {"RefNumber": "1697102", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Threshold UI fix in Alert Details page", "RefUrl": "/notes/1697102 "}, {"RefNumber": "1696914", "RefComponent": "SV-SMG-SUP", "RefTitle": "Incident management: Short dump when sending to SAP", "RefUrl": "/notes/1696914 "}, {"RefNumber": "1696526", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "Test workbench requests login to local system", "RefUrl": "/notes/1696526 "}, {"RefNumber": "1648959", "RefComponent": "SV-SMG-DIA", "RefTitle": "solman_setup Configuration Check reorganization", "RefUrl": "/notes/1648959 "}, {"RefNumber": "1644147", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: CRM order lock is lost in edit mode", "RefUrl": "/notes/1644147 "}, {"RefNumber": "1674070", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Different installed products in different logon languages", "RefUrl": "/notes/1674070 "}, {"RefNumber": "1673128", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Modified metric variant thresholds do not work", "RefUrl": "/notes/1673128 "}, {"RefNumber": "1672429", "RefComponent": "SV-SMG-DIA-SRV-SET", "RefTitle": "Setup issue for HANA", "RefUrl": "/notes/1672429 "}, {"RefNumber": "1658536", "RefComponent": "BC-CCM-SLD-ABA", "RefTitle": "Bug Fix in CL_CIM_DATETIME", "RefUrl": "/notes/1658536 "}, {"RefNumber": "1602519", "RefComponent": "BC-ABA-TO", "RefTitle": "FREE_SELECTIONS_RANGE_2_WHERE: Problem w/ special characters", "RefUrl": "/notes/1602519 "}, {"RefNumber": "1652475", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: Usernames, Password and Port Numbers Lost in DBCON", "RefUrl": "/notes/1652475 "}, {"RefNumber": "1685621", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "EEM SLA Dashboard: Values too low", "RefUrl": "/notes/1685621 "}, {"RefNumber": "1685530", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Newly added metric variants do not work correctly", "RefUrl": "/notes/1685530 "}, {"RefNumber": "1651510", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "LMDB upgrade activities missing in solman_setup", "RefUrl": "/notes/1651510 "}, {"RefNumber": "1687237", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solution Mananager Setup, disable flash roadmap", "RefUrl": "/notes/1687237 "}, {"RefNumber": "1686396", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Duration evaluation displays incorrect values (ST 7.1)", "RefUrl": "/notes/1686396 "}, {"RefNumber": "1683279", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Dump deleting a custom template with custom metric variants", "RefUrl": "/notes/1683279 "}, {"RefNumber": "1685607", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Cannot delete template though no managed objects use it", "RefUrl": "/notes/1685607 "}, {"RefNumber": "1663545", "RefComponent": "SV-SMG-SUP", "RefTitle": "Correction in module DSWP_GET_BUSTRANS_USAGE", "RefUrl": "/notes/1663545 "}, {"RefNumber": "1681853", "RefComponent": "SV-SMG-LDB", "RefTitle": "Shared memory causes problems for the content sync", "RefUrl": "/notes/1681853 "}, {"RefNumber": "1664933", "RefComponent": "SV-SMG-SUP", "RefTitle": "Dump when you create an incident", "RefUrl": "/notes/1664933 "}, {"RefNumber": "1644615", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: time Changed At in watch list is not correct", "RefUrl": "/notes/1644615 "}, {"RefNumber": "1679055", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "MEAs activated in custom template shown as inactive", "RefUrl": "/notes/1679055 "}, {"RefNumber": "1659868", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Analysis report icons missing in SP3 and SP4", "RefUrl": "/notes/1659868 "}, {"RefNumber": "1614747", "RefComponent": "SV-SMG", "RefTitle": "Report for creating products creates incorrect product", "RefUrl": "/notes/1614747 "}, {"RefNumber": "1648230", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: Character ',' disappears in long text", "RefUrl": "/notes/1648230 "}, {"RefNumber": "1675392", "RefComponent": "SV-SMG-SUP", "RefTitle": "Correct SAP Solution Manager Usage Count of Service Desk", "RefUrl": "/notes/1675392 "}, {"RefNumber": "1614206", "RefComponent": "BC-ESI-WS-ABA-RT", "RefTitle": "Empty response for WS calls for request-response operations", "RefUrl": "/notes/1614206 "}, {"RefNumber": "1675184", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Data inconsistency in MAI content leads to dump", "RefUrl": "/notes/1675184 "}, {"RefNumber": "1673661", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Enabling setting of Sender for auto-notification emails", "RefUrl": "/notes/1673661 "}, {"RefNumber": "1661518", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "RFCs: Unable to generate RFCs using load balancing", "RefUrl": "/notes/1661518 "}, {"RefNumber": "1672735", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Custom templates cannot be used as default", "RefUrl": "/notes/1672735 "}, {"RefNumber": "1656963", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Commit command for logging the sending of alerts to SAP", "RefUrl": "/notes/1656963 "}, {"RefNumber": "1648271", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Threshold values are not saved for derived metric variant", "RefUrl": "/notes/1648271 "}, {"RefNumber": "1644866", "RefComponent": "SV-SMG-LDB", "RefTitle": "Initial synchronization of data storages dependent on LMDB", "RefUrl": "/notes/1644866 "}, {"RefNumber": "1668299", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Event missing if activated in custom/managed-object template", "RefUrl": "/notes/1668299 "}, {"RefNumber": "1663497", "RefComponent": "SV-SMG-SUP", "RefTitle": "Open up Incident in authority check", "RefUrl": "/notes/1663497 "}, {"RefNumber": "1666479", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: System Value-Help is Empty", "RefUrl": "/notes/1666479 "}, {"RefNumber": "1655203", "RefComponent": "CRM-ANA-OR", "RefTitle": "IR: OLTP Reports with Status Filter show no results", "RefUrl": "/notes/1655203 "}, {"RefNumber": "1664916", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_UPDATE_FROM_SAP_ISV", "RefUrl": "/notes/1664916 "}, {"RefNumber": "1664744", "RefComponent": "SV-SMG-SDG", "RefTitle": "Self-Diagnosis alerts for EWA and Service Content Update", "RefUrl": "/notes/1664744 "}, {"RefNumber": "1665086", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Alert Object fix for REACT_TO_CLOSED_ALERT BAdi API", "RefUrl": "/notes/1665086 "}, {"RefNumber": "1659631", "RefComponent": "CRM-BF-PD", "RefTitle": "Searching BP with Chinese characters", "RefUrl": "/notes/1659631 "}, {"RefNumber": "1662244", "RefComponent": "SV-SMG-LDB", "RefTitle": "LMDB->SMSY sync: Deletion of dual stack assignments in SMSY", "RefUrl": "/notes/1662244 "}, {"RefNumber": "1645762", "RefComponent": "SV-SMG-LDB", "RefTitle": "SMSY:Incorr assgmt of Java tech syst for ABAP prod instances", "RefUrl": "/notes/1645762 "}, {"RefNumber": "1655577", "RefComponent": "BC-WD-CMP-FPM", "RefTitle": "FPM: IDR: Initialization of side panel", "RefUrl": "/notes/1655577 "}, {"RefNumber": "1653216", "RefComponent": "SV-SMG-LDB", "RefTitle": "CL_LAPI_NOTIFICATION_CONSUMER - Exception CX_SY_OPEN_SQL_DB", "RefUrl": "/notes/1653216 "}, {"RefNumber": "1653580", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_CPY_PROCTYPE: Incorrect data sorting", "RefUrl": "/notes/1653580 "}, {"RefNumber": "1655832", "RefComponent": "CA-WUI-UI-RT", "RefTitle": "Error when determining status of nested model attributes", "RefUrl": "/notes/1655832 "}, {"RefNumber": "1656789", "RefComponent": "SV-SMG-SUP", "RefTitle": "Incident management: Problems with RFC determination", "RefUrl": "/notes/1656789 "}, {"RefNumber": "1655460", "RefComponent": "BC-WD-CMP-FPM", "RefTitle": "FPM search comp: Program termin with attribute with OVS help", "RefUrl": "/notes/1655460 "}, {"RefNumber": "1608457", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert Session Not Sent To SAP", "RefUrl": "/notes/1608457 "}, {"RefNumber": "1650350", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_IM_UPDATE_FROM_SAP: User status not updated", "RefUrl": "/notes/1650350 "}, {"RefNumber": "1649247", "RefComponent": "BC-WD-ABA", "RefTitle": "Corrections for unified rendering 702/11 II (ABAP-Renderer)", "RefUrl": "/notes/1649247 "}, {"RefNumber": "1487329", "RefComponent": "BC-DWB-TOO-FUB", "RefTitle": "Additional Authorization Check in ABAP Workbench", "RefUrl": "/notes/1487329 "}, {"RefNumber": "1640239", "RefComponent": "CRM-BF-TM", "RefTitle": "Texts are determined again when a partner is changed", "RefUrl": "/notes/1640239 "}, {"RefNumber": "1649297", "RefComponent": "BC-WD-ABA", "RefTitle": "Corrections for unified rendering 702/11 II (UR-Mimes)", "RefUrl": "/notes/1649297 "}, {"RefNumber": "1645356", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: User and Password Should Not Be Read-Only for Local DB", "RefUrl": "/notes/1645356 "}, {"RefNumber": "1644401", "RefComponent": "SV-SMG-SUP", "RefTitle": "Incident Mgmt:SAP Collaboration assgmt block:Prob w/ Inst no", "RefUrl": "/notes/1644401 "}, {"RefNumber": "1622329", "RefComponent": "CRM-BTX-SVO", "RefTitle": "Display of external reference number does not happen", "RefUrl": "/notes/1622329 "}, {"RefNumber": "1633476", "RefComponent": "BC-WD-ABA", "RefTitle": "Correction for unified rendering 702/10 V (ABAP-Renderer)", "RefUrl": "/notes/1633476 "}, {"RefNumber": "1641387", "RefComponent": "CA-WUI-UI-TAG", "RefTitle": "WEBCUIF: Exception selecting data in Flex Table Graphics", "RefUrl": "/notes/1641387 "}, {"RefNumber": "1552978", "RefComponent": "SV-SMG-MON-SLR", "RefTitle": "SL Reporting: Downtimes can no longer be maintained", "RefUrl": "/notes/1552978 "}, {"RefNumber": "1633482", "RefComponent": "BC-WD-ABA", "RefTitle": "Correction for unified rendering 702/10 V (UR-Mimes)", "RefUrl": "/notes/1633482 "}, {"RefNumber": "1623160", "RefComponent": "CRM-BF-COM", "RefTitle": "Error message CRM_DOCUMENTS 107 for document template search", "RefUrl": "/notes/1623160 "}, {"RefNumber": "1589050", "RefComponent": "CRM-IC-BF-CAT", "RefTitle": "No Categorization schema assigned to application area", "RefUrl": "/notes/1589050 "}, {"RefNumber": "1609361", "RefComponent": "BC-ESI-WS-ABA-RT", "RefTitle": "Statistical records for Web Service calls missing", "RefUrl": "/notes/1609361 "}, {"RefNumber": "1488293", "RefComponent": "CRM-BTX-SRQ", "RefTitle": "Short dump occurs when creating service req. from template", "RefUrl": "/notes/1488293 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST", "NumberOfCorrin": 1, "URL": "/corrins/0001633725/162"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 229, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "BBPCRM", "ValidFrom": "600", "ValidTo": "701", "Number": "1094074 ", "URL": "/notes/1094074 ", "Title": "Unnecessary Authority Check in CRM_DNO_MONITOR", "Component": "CRM-BTX-SVO"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "600", "ValidTo": "702", "Number": "1659631 ", "URL": "/notes/1659631 ", "Title": "Searching BP with Chinese characters", "Component": "CRM-BF-PD"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1488293 ", "URL": "/notes/1488293 ", "Title": "Short dump occurs when creating service req. from template", "Component": "CRM-BTX-SRQ"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1589050 ", "URL": "/notes/1589050 ", "Title": "No Categorization schema assigned to application area", "Component": "CRM-IC-BF-CAT"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1622329 ", "URL": "/notes/1622329 ", "Title": "Display of external reference number does not happen", "Component": "CRM-BTX-SVO"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1623160 ", "URL": "/notes/1623160 ", "Title": "Error message CRM_DOCUMENTS 107 for document template search", "Component": "CRM-BF-COM"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1640239 ", "URL": "/notes/1640239 ", "Title": "Texts are determined again when a partner is changed", "Component": "CRM-BF-TM"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1655203 ", "URL": "/notes/1655203 ", "Title": "IR: OLTP Reports with Status Filter show no results", "Component": "CRM-ANA-OR"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1659705 ", "URL": "/notes/1659705 ", "Title": "Consolidating note for issue pertaining to Employee BP", "Component": "CRM-BF-PD"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1672297 ", "URL": "/notes/1672297 ", "Title": "UIU:NO_EMP_FILTER parameter hidden in AccountSearchHelp (2)", "Component": "CRM-MD-BP"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1740545 ", "URL": "/notes/1740545 ", "Title": "Categories trigger entity data change", "Component": "CRM-IC-BF-CAT"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1821638 ", "URL": "/notes/1821638 ", "Title": "Rule Policy on Multilevel Categorization", "Component": "CRM-BTX-SRQ"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "701", "Number": "1743244 ", "URL": "/notes/1743244 ", "Title": "POWL: Better description needed in query template dropdown", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "702", "Number": "1763793 ", "URL": "/notes/1763793 ", "Title": "POWL: Column headers are rendered incorrectly", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "702", "Number": "1774418 ", "URL": "/notes/1774418 ", "Title": "POWL: Unnecessary refresh triggered when using query api", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "702", "Number": "1759409 ", "URL": "/notes/1759409 ", "Title": "POWL: Dump when \"enable default lead selection\" is ON", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1655460 ", "URL": "/notes/1655460 ", "Title": "FPM search comp: Program termin with attribute with OVS help", "Component": "BC-WD-CMP-FPM"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1655577 ", "URL": "/notes/1655577 ", "Title": "FPM: IDR: Initialization of side panel", "Component": "BC-WD-CMP-FPM"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1673201 ", "URL": "/notes/1673201 ", "Title": "POWL: Row selection lost on Single/Multi selection modes", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1734250 ", "URL": "/notes/1734250 ", "Title": "POWL: Error in detail area update", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1748138 ", "URL": "/notes/1748138 ", "Title": "POWL: Unnecessary update of detail area", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1821727 ", "URL": "/notes/1821727 ", "Title": "POWL: Detail area not updated in accordance with current lead selection", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1909714 ", "URL": "/notes/1909714 ", "Title": "POWL:Dump when switching the queries", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_AP", "ValidFrom": "700", "ValidTo": "700", "Number": "1457391 ", "URL": "/notes/1457391 ", "Title": "Bad Performance while accessing table IBST.", "Component": "AP-MD-IBA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "1738511 ", "URL": "/notes/1738511 ", "Title": "Editing MS Office documents in SAP Solution Manager", "Component": "SV-SMG-IMP"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "730", "Number": "1487329 ", "URL": "/notes/1487329 ", "Title": "Additional Authorization Check in ABAP Workbench", "Component": "BC-DWB-TOO-FUB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "731", "Number": "1602519 ", "URL": "/notes/1602519 ", "Title": "FREE_SELECTIONS_RANGE_2_WHERE: Problem w/ special characters", "Component": "BC-ABA-TO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "731", "Number": "1814921 ", "URL": "/notes/1814921 ", "Title": "BAE: Performance issue when using RFC_VERIFY_DESTINATION", "Component": "BC-CCM-BTC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1609361 ", "URL": "/notes/1609361 ", "Title": "Statistical records for Web Service calls missing", "Component": "BC-ESI-WS-ABA-RT"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1612603 ", "URL": "/notes/1612603 ", "Title": "ORA: Mandatory connection parameter 'DBNAME' not specified", "Component": "BC-DB-ORA-CCM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1614206 ", "URL": "/notes/1614206 ", "Title": "Empty response for WS calls for request-response operations", "Component": "BC-ESI-WS-ABA-RT"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1629551 ", "URL": "/notes/1629551 ", "Title": "Business Graphics: Dump due to missing resources", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1645356 ", "URL": "/notes/1645356 ", "Title": "DB6: User and Password Should Not Be Read-Only for Local DB", "Component": "BC-DB-DB6-CCM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1655601 ", "URL": "/notes/1655601 ", "Title": "Extractors for Performance Warehouse", "Component": "HAN-DB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1658536 ", "URL": "/notes/1658536 ", "Title": "Bug Fix in CL_CIM_DATETIME", "Component": "BC-CCM-SLD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1682750 ", "URL": "/notes/1682750 ", "Title": "FormattedTextView: Unexpected characters (such as &#xd;)", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1694458 ", "URL": "/notes/1694458 ", "Title": "SOLAR_PROJECT_ADMIN:Select Countries not Cleared/Refreshed", "Component": "BC-CUS-TOL-PAD"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1696526 ", "URL": "/notes/1696526 ", "Title": "Test workbench requests login to local system", "Component": "SV-SMG-TWB-PLN"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1697052 ", "URL": "/notes/1697052 ", "Title": "WDA: Link in FormattedTextView causes a dump", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1733824 ", "URL": "/notes/1733824 ", "Title": "Dump in CL_WDR_NOTIFICATION", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1741470 ", "URL": "/notes/1741470 ", "Title": "WD ABAP ALV error when context changes are dispatched", "Component": "BC-WD-CMP-ALV-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1763274 ", "URL": "/notes/1763274 ", "Title": "Select options: Error when deleting a parameter", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1797388 ", "URL": "/notes/1797388 ", "Title": "Web Dynpro: Conversion of non-prinatable characters", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1813468 ", "URL": "/notes/1813468 ", "Title": "Web Dynpro: Conversion for non-printable chars (NON-UNICODE)", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1816471 ", "URL": "/notes/1816471 ", "Title": "SE24: Error for POST methods for defined preferred parameter", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1824197 ", "URL": "/notes/1824197 ", "Title": "STWB_2 - Test plan display requires re-logon", "Component": "SV-SMG-TWB-PLN"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "730", "Number": "1604209 ", "URL": "/notes/1604209 ", "Title": "ORA/MSS: Wrong error message for local system, no DBSL found", "Component": "BC-DB-ORA-CCM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "730", "Number": "1629407 ", "URL": "/notes/1629407 ", "Title": "DBA Cockpit: Runtime error OBJECTS_OBJREF_NOT_ASSIGNED", "Component": "BC-DB-DB6-CCM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "731", "Number": "1652475 ", "URL": "/notes/1652475 ", "Title": "DB6: Usernames, Password and Port Numbers Lost in DBCON", "Component": "BC-DB-DB6-CCM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "731", "Number": "1700427 ", "URL": "/notes/1700427 ", "Title": "MaxDB: Potential corruption of DB release in DB6NAVSYST", "Component": "BC-DB-SDB-CCM"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "702", "Number": "1477732 ", "URL": "/notes/1477732 ", "Title": "Characteristic filter set by variable exit not refreshed", "Component": "BW-BEX-ET-WEB"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "730", "Number": "1579462 ", "URL": "/notes/1579462 ", "Title": "Sending data from chart item without check to IGS", "Component": "BW-BEX-ET-WEB"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "731", "Number": "1587690 ", "URL": "/notes/1587690 ", "Title": "Problems with selective deletion in packages", "Component": "BW-BEX-OT-DBIF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "731", "Number": "1297738 ", "URL": "/notes/1297738 ", "Title": "To allow CTC APIs to add Non BW system to BW system", "Component": "BC-INS-CTC"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "710", "Number": "1696061 ", "URL": "/notes/1696061 ", "Title": "Performance Improvement for Service Sessions in DSWP", "Component": "SV-SMG-SVD"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "710", "Number": "1717888 ", "URL": "/notes/1717888 ", "Title": "Issue Management: bad performance in context and task tab", "Component": "SV-SMG-ISM"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "710", "Number": "1722791 ", "URL": "/notes/1722791 ", "Title": "Changes by Compare and Adjust on transaction tab are lost", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "710", "Number": "1783371 ", "URL": "/notes/1783371 ", "Title": "MOpz: System landscape data incomplete in Customer Profile", "Component": "SV-SMG-MAI"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1576693 ", "URL": "/notes/1576693 ", "Title": "Source code corrs: Interactive reporting/IT Performance Rep.", "Component": "SV-SMG-MON-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1599582 ", "URL": "/notes/1599582 ", "Title": "Plugin Status Details - Delete sp_level check for ST-A/PI", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1604471 ", "URL": "/notes/1604471 ", "Title": "SLA-relevant scripts are missing in EEM BI Reporting", "Component": "SV-SMG-MON-EEM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1614747 ", "URL": "/notes/1614747 ", "Title": "Report for creating products creates incorrect product", "Component": "SV-SMG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1615423 ", "URL": "/notes/1615423 ", "Title": "Messages are created with the wrong transaction type", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1617181 ", "URL": "/notes/1617181 ", "Title": "BPO Dashboard - Update log to wait for ccms_bi job", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1619830 ", "URL": "/notes/1619830 ", "Title": "Performance problem when creating an issue", "Component": "SV-SMG-OP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1643760 ", "URL": "/notes/1643760 ", "Title": "No target namespace when settung up LMDB", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1644147 ", "URL": "/notes/1644147 ", "Title": "Work Center: CRM order lock is lost in edit mode", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1644401 ", "URL": "/notes/1644401 ", "Title": "Incident Mgmt:SAP Collaboration assgmt block:Prob w/ Inst no", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1644615 ", "URL": "/notes/1644615 ", "Title": "Work Center: time Changed At in watch list is not correct", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1644866 ", "URL": "/notes/1644866 ", "Title": "Initial synchronization of data storages dependent on LMDB", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1645762 ", "URL": "/notes/1645762 ", "Title": "SMSY:Incorr assgmt of Java tech syst for ABAP prod instances", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1648230 ", "URL": "/notes/1648230 ", "Title": "Work Center: Character ',' disappears in long text", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1648271 ", "URL": "/notes/1648271 ", "Title": "Threshold values are not saved for derived metric variant", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1648959 ", "URL": "/notes/1648959 ", "Title": "solman_setup Configuration Check reorganization", "Component": "SV-SMG-DIA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1650032 ", "URL": "/notes/1650032 ", "Title": "Landscape LMDB wrapper SP4 related corrections", "Component": "SV-SMG-DIA-SRV-LSC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1650322 ", "URL": "/notes/1650322 ", "Title": "Incorrect values in system availability app of the dashboard", "Component": "SV-SMG-MON-SYS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1650350 ", "URL": "/notes/1650350 ", "Title": "AI_CRM_IM_UPDATE_FROM_SAP: User status not updated", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1651279 ", "URL": "/notes/1651279 ", "Title": "Corrections for EEM 7.1 SP04", "Component": "SV-SMG-MON-EEM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1651510 ", "URL": "/notes/1651510 ", "Title": "LMDB upgrade activities missing in solman_setup", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1652503 ", "URL": "/notes/1652503 ", "Title": "Advance Corr. BPMon SM 7.1 ST710 SP05", "Component": "SV-SMG-MON-BPM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1653216 ", "URL": "/notes/1653216 ", "Title": "CL_LAPI_NOTIFICATION_CONSUMER - Exception CX_SY_OPEN_SQL_DB", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1653580 ", "URL": "/notes/1653580 ", "Title": "AI_CRM_CPY_PROCTYPE: Incorrect data sorting", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1654623 ", "URL": "/notes/1654623 ", "Title": "Problems when copying custom templates", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1656789 ", "URL": "/notes/1656789 ", "Title": "Incident management: Problems with RFC determination", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1656963 ", "URL": "/notes/1656963 ", "Title": "Commit command for logging the sending of alerts to SAP", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1657694 ", "URL": "/notes/1657694 ", "Title": "Master data for message status missing in TWB BI Reporting", "Component": "SV-SMG-TWB-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1657871 ", "URL": "/notes/1657871 ", "Title": "AI_CRM_CPY_PROCTYPE: Error in parameter conditions and dump", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1659868 ", "URL": "/notes/1659868 ", "Title": "Analysis report icons missing in SP3 and SP4", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1661166 ", "URL": "/notes/1661166 ", "Title": "SMSY: Message server for Java systems is incorrect", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1661518 ", "URL": "/notes/1661518 ", "Title": "RFCs: Unable to generate RFCs using load balancing", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1662088 ", "URL": "/notes/1662088 ", "Title": "Dump during migration of diagnostics-rel. product instances", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1662244 ", "URL": "/notes/1662244 ", "Title": "LMDB->SMSY sync: Deletion of dual stack assignments in SMSY", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1663497 ", "URL": "/notes/1663497 ", "Title": "Open up Incident in authority check", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1663545 ", "URL": "/notes/1663545 ", "Title": "Correction in module DSWP_GET_BUSTRANS_USAGE", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1664916 ", "URL": "/notes/1664916 ", "Title": "AI_CRM_UPDATE_FROM_SAP_ISV", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1664933 ", "URL": "/notes/1664933 ", "Title": "Dump when you create an incident", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1665086 ", "URL": "/notes/1665086 ", "Title": "Alert Object fix for REACT_TO_CLOSED_ALERT BAdi API", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1665775 ", "URL": "/notes/1665775 ", "Title": "Incorrect values in the test plan status evaluation", "Component": "SV-SMG-TWB-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1666479 ", "URL": "/notes/1666479 ", "Title": "Work Center: System Value-Help is Empty", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1667344 ", "URL": "/notes/1667344 ", "Title": "Fix for Importing Content Version", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1668299 ", "URL": "/notes/1668299 ", "Title": "Event missing if activated in custom/managed-object template", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1670189 ", "URL": "/notes/1670189 ", "Title": "Solution Manager 7.1 corrections for \"Managed System Config\"", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1671172 ", "URL": "/notes/1671172 ", "Title": "AI_CRM_CPY_PROCTYPE: Runtime error ASSIGN_BASE_TO_SHORT", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1672429 ", "URL": "/notes/1672429 ", "Title": "Setup issue for HANA", "Component": "SV-SMG-DIA-SRV-SET"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1672735 ", "URL": "/notes/1672735 ", "Title": "Custom templates cannot be used as default", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1672797 ", "URL": "/notes/1672797 ", "Title": "AI_CRM_IM_UPDATE_FROM_SAP: Unnecessary update of messages", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1672890 ", "URL": "/notes/1672890 ", "Title": "Collection of Self-Monitoing SP4 corrections", "Component": "SV-SMG-MON-SFM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1673128 ", "URL": "/notes/1673128 ", "Title": "Modified metric variant thresholds do not work", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1673661 ", "URL": "/notes/1673661 ", "Title": "Enabling setting of Sender for auto-notification emails", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1674070 ", "URL": "/notes/1674070 ", "Title": "Different installed products in different logon languages", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1675184 ", "URL": "/notes/1675184 ", "Title": "Data inconsistency in MAI content leads to dump", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1675392 ", "URL": "/notes/1675392 ", "Title": "Correct SAP Solution Manager Usage Count of Service Desk", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1675691 ", "URL": "/notes/1675691 ", "Title": "Managed object does not get consumer settings from template", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1679055 ", "URL": "/notes/1679055 ", "Title": "MEAs activated in custom template shown as inactive", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1681591 ", "URL": "/notes/1681591 ", "Title": "Correction: SLD --> LMDB synch. connection error handling", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1681853 ", "URL": "/notes/1681853 ", "Title": "Shared memory causes problems for the content sync", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1681889 ", "URL": "/notes/1681889 ", "Title": "Notification table update for SP5/SP4/SP3/SP2", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1683260 ", "URL": "/notes/1683260 ", "Title": "Error when jump to SelfDiagnosis status detail solman_setup", "Component": "SV-SMG-DIA-SRV-LSC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1683279 ", "URL": "/notes/1683279 ", "Title": "Dump deleting a custom template with custom metric variants", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1683290 ", "URL": "/notes/1683290 ", "Title": "Transport of templates doesn't update assignment status", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1684910 ", "URL": "/notes/1684910 ", "Title": "Data Provider Value help type fix", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1685128 ", "URL": "/notes/1685128 ", "Title": "Wrong central SAP note validated in Self Diagnosis", "Component": "SV-SMG-DIA-SRV-CHK"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1685463 ", "URL": "/notes/1685463 ", "Title": "Cycle: Missing authority check for cycle creation button", "Component": "SV-SMG-SVD"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1685530 ", "URL": "/notes/1685530 ", "Title": "Newly added metric variants do not work correctly", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1685607 ", "URL": "/notes/1685607 ", "Title": "Cannot delete template though no managed objects use it", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1685621 ", "URL": "/notes/1685621 ", "Title": "EEM SLA Dashboard: Values too low", "Component": "SV-SMG-MON-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1686396 ", "URL": "/notes/1686396 ", "Title": "Duration evaluation displays incorrect values (ST 7.1)", "Component": "SV-SMG-SUP-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1686681 ", "URL": "/notes/1686681 ", "Title": "Not able to reset managed object-specific changes", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1687114 ", "URL": "/notes/1687114 ", "Title": "Signature correction for clone method", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1687237 ", "URL": "/notes/1687237 ", "Title": "Solution Mananager Setup, disable flash roadmap", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1690276 ", "URL": "/notes/1690276 ", "Title": "Mapping: SMSY description from LMDB for MDM, Unspec, WebDISP", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1695528 ", "URL": "/notes/1695528 ", "Title": "Alert Object data fix", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1696106 ", "URL": "/notes/1696106 ", "Title": "Fixing Self Diagnosis Checks for SP04", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1696914 ", "URL": "/notes/1696914 ", "Title": "Incident management: Short dump when sending to SAP", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1697102 ", "URL": "/notes/1697102 ", "Title": "Threshold UI fix in Alert Details page", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1698275 ", "URL": "/notes/1698275 ", "Title": "Database error during EEM setup", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1700043 ", "URL": "/notes/1700043 ", "Title": "E2E Alerting: Missing Metrics for Oracle, DB2 LUW and HANA", "Component": "SV-SMG-MON-ALR"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1700486 ", "URL": "/notes/1700486 ", "Title": "System Status not updated in Managed System Overview", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1700972 ", "URL": "/notes/1700972 ", "Title": "AI_CRM_CPY_PROCTYPE: Update - Missing view/table values", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1704858 ", "URL": "/notes/1704858 ", "Title": "Upgrade Dependency Analysis (UDA) Usage - false date", "Component": "SV-SMG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1706339 ", "URL": "/notes/1706339 ", "Title": "TWB BI reporting extraction: Insufficient load frequencies", "Component": "SV-SMG-TWB-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1707803 ", "URL": "/notes/1707803 ", "Title": "DPC: Grey metrics due to \"Agent not registered\" message", "Component": "SV-SMG-MON-ALR-PRV"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1709230 ", "URL": "/notes/1709230 ", "Title": "Frequency adjustment ignores attribute extractor runtime", "Component": "SV-SMG-TWB-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1709694 ", "URL": "/notes/1709694 ", "Title": "Bad Performance of Issue Management Functions", "Component": "SV-SMG-ISM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1710170 ", "URL": "/notes/1710170 ", "Title": "SAP Solution Manager Software Prerequisites", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1710174 ", "URL": "/notes/1710174 ", "Title": "Wrong user for incident management BI reporting setup", "Component": "SV-SMG-SUP-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1711380 ", "URL": "/notes/1711380 ", "Title": "External Service Desk interface: Texts are duplicated", "Component": "SV-SMG-SUP-IFA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1711748 ", "URL": "/notes/1711748 ", "Title": "Improvement of Report RLMDB_ADD_GUIDS", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1712878 ", "URL": "/notes/1712878 ", "Title": "Technical Host returns grey metrics", "Component": "SV-SMG-MON-ALR-PRV"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1713085 ", "URL": "/notes/1713085 ", "Title": "DVM Work Center: Disable Multiple Save on User Click", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1714900 ", "URL": "/notes/1714900 ", "Title": "Check of users and roles when the CUA is inactive", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1715620 ", "URL": "/notes/1715620 ", "Title": "Search result not in visible area for table tree", "Component": "SV-SMG-SDA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1715675 ", "URL": "/notes/1715675 ", "Title": "Content upload: missing logical components", "Component": "SV-SMG-SDA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1716300 ", "URL": "/notes/1716300 ", "Title": "Bug fixes for LMDB notification cleanup", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1717403 ", "URL": "/notes/1717403 ", "Title": "Collective Note for Extractor FWK - ST710 (SP05 - SP08)", "Component": "SV-SMG-DIA-SRV-EFW"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1717775 ", "URL": "/notes/1717775 ", "Title": "Metric Troubleshooting Guide shows wrong information", "Component": "SV-SMG-MON-ALR"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1719142 ", "URL": "/notes/1719142 ", "Title": "Problems solved with ST 710 SP06", "Component": "SV-SMG-SDA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1719578 ", "URL": "/notes/1719578 ", "Title": "Fallback language text for Managed Obj., Scn.type & Category", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1720336 ", "URL": "/notes/1720336 ", "Title": "Correction to priority when creating automatic incidents", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1721598 ", "URL": "/notes/1721598 ", "Title": "Outside Discovery: Improvement to fill missing HANA props", "Component": "SV-SMG-DIA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1722197 ", "URL": "/notes/1722197 ", "Title": "Where-used list for logical components is incorrect", "Component": "SV-SMG-OP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1724761 ", "URL": "/notes/1724761 ", "Title": "Business Process Monitoring attributes missing in solution", "Component": "SV-SMG-OP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1726133 ", "URL": "/notes/1726133 ", "Title": "Incorrect calculation of used workload objects", "Component": "SV-SMG-SDA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1727449 ", "URL": "/notes/1727449 ", "Title": "Dump/Time out on selecting the modified metric at MO level", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1727730 ", "URL": "/notes/1727730 ", "Title": "SOLMAN_SETUP: BW host name provided in uper case cause dumps", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1728631 ", "URL": "/notes/1728631 ", "Title": "Extractor scheduling for ALM and ASR", "Component": "SV-SMG-MON-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1729591 ", "URL": "/notes/1729591 ", "Title": "Error: No Long Name Found in SMD_HASH_TABLE for Hash-ID", "Component": "SV-SMG-DIA-SRV-EFW"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1729753 ", "URL": "/notes/1729753 ", "Title": "ST710: ITSM document locked by own user after saving", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1730457 ", "URL": "/notes/1730457 ", "Title": "VAR: Texts duplicated after exchange with SAP", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1731652 ", "URL": "/notes/1731652 ", "Title": "SMD Agent does not receive a role within role configuration", "Component": "SV-SMG-MON-ALR-PRV"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1732144 ", "URL": "/notes/1732144 ", "Title": "Wrong count of used metric instances", "Component": "SV-SMG-MON-ALR-CLC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1733236 ", "URL": "/notes/1733236 ", "Title": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP07", "Component": "SV-SMG-MON-BPM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1733998 ", "URL": "/notes/1733998 ", "Title": "SOLMAN_SETUP: activate services on managed systems", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1734821 ", "URL": "/notes/1734821 ", "Title": "E2E Alerting: Enable Metric Filter for Tablespaces", "Component": "SV-SMG-MON-ALR"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1736425 ", "URL": "/notes/1736425 ", "Title": "Technical Scenario DUAL_STACK: MaxLen violated", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1736448 ", "URL": "/notes/1736448 ", "Title": "Dump in Self Diagnosis due to duplicate keys.", "Component": "SV-SMG-SDG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1737700 ", "URL": "/notes/1737700 ", "Title": "AI_CRM_CPY_PROCTYPE: PPF containers missing from transp.req.", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1738276 ", "URL": "/notes/1738276 ", "Title": "DSA: Word creation dumps caused by memory lack", "Component": "SV-SMG-SVD-SWB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1739631 ", "URL": "/notes/1739631 ", "Title": "Service delivery session information not updated accordingly", "Component": "SV-SMG-SVD"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1741080 ", "URL": "/notes/1741080 ", "Title": "no/wrong metrics collected on SMD Agent assoicated to DB", "Component": "SV-SMG-MON-ALR"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1749774 ", "URL": "/notes/1749774 ", "Title": "Request popup opens, eventhough template saved under $TMP", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1750618 ", "URL": "/notes/1750618 ", "Title": "RFC destinations created in SMSU_MANAGED_SYSTEM not delete", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1752181 ", "URL": "/notes/1752181 ", "Title": "Grey metrics in Technical System Monitoring", "Component": "SV-SMG-MON-ALR-PRV"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1752517 ", "URL": "/notes/1752517 ", "Title": "Managed system configuration - warning in step 7 create user", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1753985 ", "URL": "/notes/1753985 ", "Title": "Downloaded XML data for Root Cause Analysis is not correct", "Component": "SV-SMG-SDG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1754275 ", "URL": "/notes/1754275 ", "Title": "Wrong message 'RFC SM_XXXCLNTnnn_BACK does not exist'", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1758728 ", "URL": "/notes/1758728 ", "Title": "\"Active\" selection of product versions is removed", "Component": "SV-SMG-SYS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1761039 ", "URL": "/notes/1761039 ", "Title": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP08", "Component": "SV-SMG-MON-BPM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1763065 ", "URL": "/notes/1763065 ", "Title": "Reporting API returns too much data and response is slow", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1769570 ", "URL": "/notes/1769570 ", "Title": "Function Moduel SMD_DATA_LOADER101 not found", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1770638 ", "URL": "/notes/1770638 ", "Title": "Working with Templates in different languages", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1775643 ", "URL": "/notes/1775643 ", "Title": "AI_SDK_SP_GENERATE_BP_V2: diverse corrections III", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1775883 ", "URL": "/notes/1775883 ", "Title": "GSS: unexpected dump when using webdynpro logon popup in GSS", "Component": "SV-SMG-SVD-GSS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1777767 ", "URL": "/notes/1777767 ", "Title": "Incomplete display of messages in component view", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1780509 ", "URL": "/notes/1780509 ", "Title": "Select Options for Metric parameter values is not working", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1784446 ", "URL": "/notes/1784446 ", "Title": "Custom Metric Variants were not copied during copy template", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1784936 ", "URL": "/notes/1784936 ", "Title": "Transport Custom Metric Variants from one system to other system", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1804373 ", "URL": "/notes/1804373 ", "Title": "Solman_setup : The BW content is not active", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1808259 ", "URL": "/notes/1808259 ", "Title": "Wrong information text for column title in Web DSA UI", "Component": "SV-SMG-SVD-GSS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1813914 ", "URL": "/notes/1813914 ", "Title": "CCDB: ConfigStore bo40.dump_all_xml - SYSTEM_NO_ROLL", "Component": "SV-SMG-DIA-APP-CA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1820291 ", "URL": "/notes/1820291 ", "Title": "Project Documentation tab flagged as changed in Template", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1823420 ", "URL": "/notes/1823420 ", "Title": "CCDB: Fatal Error - CX_COMPONENT_VERSION_NOT_FOUND", "Component": "SV-SMG-DIA-APP-CA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1826109 ", "URL": "/notes/1826109 ", "Title": "ITSM: Standard CRM authorization checks skipped for search", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1833865 ", "URL": "/notes/1833865 ", "Title": "Avoid Obsolete Recipient Lists/Recipients during transport", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1837889 ", "URL": "/notes/1837889 ", "Title": "VAR scenario: Unjustified deactivation of S users", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1849566 ", "URL": "/notes/1849566 ", "Title": "Users without authorization see the installations", "Component": "SV-SMG-SVC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1851723 ", "URL": "/notes/1851723 ", "Title": "REFRESH_ADMIN_DATA_FROM_SUPPORT no licence data generation", "Component": "SV-SMG-SVC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1855272 ", "URL": "/notes/1855272 ", "Title": "SOLMAN_SETUP: admin user shouldn't modify S* profiles", "Component": "SV-SMG-INS-CFG-MNG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1886549 ", "URL": "/notes/1886549 ", "Title": "Flag TEMP_INACTIVE in table SMSY_SYSTEM_SAP wrong", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1610374 ", "URL": "/notes/1610374 ", "Title": "Incorrect data in the metric monitor/inactive BW content", "Component": "BW-BCT-CMS"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1707863 ", "URL": "/notes/1707863 ", "Title": "Alert <PERSON>t reporting: Too much data in time scale cubes", "Component": "SV-SMG-MON-REP"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1722178 ", "URL": "/notes/1722178 ", "Title": "TwinCube reorganization does not work", "Component": "SV-SMG-MON-REP"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1726424 ", "URL": "/notes/1726424 ", "Title": "Connection failed (RTE:[2] too many database sessions active", "Component": "SV-SMG-MON-REP"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1737668 ", "URL": "/notes/1737668 ", "Title": "Data not re-organized after upgrade", "Component": "SV-SMG-MON-REP"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1745114 ", "URL": "/notes/1745114 ", "Title": "DBA: Dump in CL_DBA_DBH_CUBE_DATA", "Component": "SV-SMG"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1745942 ", "URL": "/notes/1745942 ", "Title": "DPW: Cube Aggregation Memory Usage", "Component": "SV-SMG"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1749795 ", "URL": "/notes/1749795 ", "Title": "DPW: Housekeeping does not finish", "Component": "BC-DB-DB6-CCM"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1762967 ", "URL": "/notes/1762967 ", "Title": "DPW: Overlapping records in DBH_CUBE_DATA", "Component": "SV-SMG-MON-REP"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1768764 ", "URL": "/notes/1768764 ", "Title": "TWB reporting displays incorrect test case status counters", "Component": "SV-SMG-TWB-REP"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_46C", "ValidTo": "2008_1_710", "Number": "1719016 ", "URL": "/notes/1719016 ", "Title": "SOLMAN_SETUP: obsolete roles still added to users", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_620", "ValidTo": "2008_1_710", "Number": "1559499 ", "URL": "/notes/1559499 ", "Title": "DPC data providers as of ST-PI 2008_1_XX SP4", "Component": "SV-SMG-MON-SYS"}, {"SoftwareComponent": "ST-SER", "ValidFrom": "701_2010_1", "ValidTo": "701_2010_1", "Number": "1552978 ", "URL": "/notes/1552978 ", "Title": "SL Reporting: Downtimes can no longer be maintained", "Component": "SV-SMG-MON-SLR"}, {"SoftwareComponent": "WEBCUIF", "ValidFrom": "701", "ValidTo": "701", "Number": "1641387 ", "URL": "/notes/1641387 ", "Title": "WEBCUIF: Exception selecting data in Flex Table Graphics", "Component": "CA-WUI-UI-TAG"}, {"SoftwareComponent": "WEBCUIF", "ValidFrom": "701", "ValidTo": "701", "Number": "1712091 ", "URL": "/notes/1712091 ", "Title": "CRM WebUI scrolls top on roundtrips", "Component": "CRM-FRW-UI"}, {"SoftwareComponent": "WEBCUIF", "ValidFrom": "701", "ValidTo": "731", "Number": "1655832 ", "URL": "/notes/1655832 ", "Title": "Error when determining status of nested model attributes", "Component": "CA-WUI-UI-RT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}