SAP Note 2353984 addresses a specific scenario for customers transitioning from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. It outlines changes related to document management, specifically the removal of HTTP-Based Document Management for the check-in/check-out functionality.

Key points from the note:

- The HTTP-Based check-in/check-out feature is no longer available in the SAP S/4HANA version of Portfolio and Project Management.
- This feature previously allowed users to upload and download documents and to checkout and open documents with an appropriate client-side application through HTTP, requiring Java installation and a valid client-side certificate.
- There are no expected impacts on business processes due to this change because the basic functionality to upload and download documents still exists.
- Users do not need to take any additional action as the existing document management capabilities remain operational.
- To determine if this transition worklist item applies to your current configuration, the note instructs users to check the Global Settings in Customizing under SAP Portfolio and Project Management > Project Management > Basic Settings > Override Default Global Settings. If the Master Switch 0007 (for General Default Values) has the sub-setting 0027 set to 'X', indicating HTTP-Based Check-in/Check-out of Documents, this item is relevant.

In summary, SAP Note 2353984 informs about the discontinuation of the HTTP-Based check-in/check-out document management feature in SAP S/4HANA's Portfolio and Project Management, advises that there will be no business process impact, and that standard document upload and download functionalities remain intact. The note also provides a relevancy check procedure for users to determine the applicability of this information to their systems.