SAP Note 522770 addresses an issue with the 2LIS_03_BF extractor used for transferring the PS_PSP_PNR field, which relates to the work breakdown structure planning element (WBS element), to SAP Business Warehouse (BW). This field in BW, identified by InfoObject 0WS_ELEMT, lacks a conversion exit, meaning it can only be displayed in its internal format within BW. As a result, users are unable to view the WBS element in an external format when running queries.

The note outlines that the absence of an external display format for the WBS element in the given DataSource (2LIS_03_BF) is the underlying reason for the problem.

The proposed solution is to enhance the extractor by adding a new field, BWPSPEX, allowing for the external display of the WBS element. The steps include:

1. Adding the BWPSPEX field to the MCMSEGBW structure in the R/3 system using transaction SE11 and activating the structure.
2. Including the BWPSPEX field in the 2LIS_03_BF DataSource using the LO Data Extraction Customizing Cockpit (transaction LBWE).
3. Implementing the source code changes in the R/3 PlugIn system.
4. Replicating the updated 2LIS_03_BF DataSource in BW.
5. Activating the updated 2LIS_03_BF InfoSource in BW.
6. Including the 0WBS_ELM_EX InfoObject in the communication structure.
7. Assigning the BWPSPEX field to the transfer structure in the DataSource/transfer structure tab.
8. Including the assignment of the BWPSPEX field to the 0WBS_ELM_EX InfoObject in the BW transfer rules.

The note also cautions that altering the extract structures necessitates a delta initialization and references SAP Notes 328181 and 396647 for further guidance on this. It additionally mentions that the changes to the extract structure implied by this note cannot be delivered through Hot Packages but are available in the standard system as of PlugIn 2002.2.