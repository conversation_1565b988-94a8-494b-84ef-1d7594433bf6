<h3>SI10: FIN_TRM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270522">2270522 - S4TWL - Drilldown Reporting in Treasury and Risk Management</a></strong></p>
<p><strong>Description:</strong></p>
<p>Currently there is no pre-upgrade check available to check if this simplification item is a valid restriction for your system conversion.</p>
<p>The drilldown reporting has been disabled for <em>Treasury and Risk Management</em>. You can't use your reports defined with the drilldown reporting. For reporting purposes, you can use the following logical databases available for the Treasury and Risk Management:</p>
<ul>
<li>FTI_TR_CASH_FLOWS: Treasury Payment Information</li>
<li>FTI_TR_CASH_FLOWS_2: Treasury Payment Info + additional position attributes</li>
<li>FTI_TR_POSITIONS_2: Treasury: Positions Evaluations + additional position attributes</li>
<li>FTI_TR_THX_HEDGE: Treasury P-Hedge Accounting Reporting</li>
<li>FTI_TR_DEALS: Treasury Transaction Reporting</li>
<li>FTI_TR_EXP_POS: Treasury: Exposure Positions</li>
<li>FTI_TR_HEDGE: Treasury E-Hedge Accounting Reporting</li>
<li>FTI_TR_PERIODS: Treasury: Period-Based Evaluations</li>
<li>FTI_TR_PERIODS_1: Treasury: Period Analysis Projection</li>
<li>FTI_TR_PERIODS_2: Treasury: Period-Based Evaluation + additional position attributes</li>
<li>FTI_TR_PL_CF: Treasury: Revenue and Cash Flow Reporting</li>
<li>FTI_TR_PL_CF_2: Treasury: Revenue and Cash Flow Reporting</li>
<li>FTI_TR_POSITIONS: Treasury Positions</li>
<li>FTI_TR_POSITIONS_1: Treasury Positions Projection</li>
</ul>
<p>Based on these logical databases you can use the SAP Query functionality available in the area menu of the <em>Treasury and Risk Management</em> under <em>Transaction Manager -> Infosystem -> Tools -> SAP Query</em> and define your own queries using <em>Maintain Queries</em> (transaction SQ01), <em>Maintain InfoSets</em> (transaction SQ02) and <em>Maintain User Groups</em> (transaction SQ03).</p>
<p>In addition, you can use the available reports in the area menu of the <em>Treasury and Risk Management</em> under <em>Transaction Manager -> Infosystem -> Reports</em>.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>During conversion project, you must undertake the following mandatory activities:</p>
<ul>
<li>Process Design / Blueprint</li>
<li>Custom Code Adaption</li>
<li>User Training</li>
</ul>
<p>Additionally, the following optional activity should be considered:</p>
<ul>
<li>Customizing / Configuration</li>
</ul>