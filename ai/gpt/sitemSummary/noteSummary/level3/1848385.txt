SAP Note 1848385 addresses an issue where searching for a query in the Analysis Process Designer (APD) in SAP NetWeaver Business Warehouse (BW) results in a system crash with a 'MESSAGE_TYPE_X' runtime error.

The note identifies the cause of the problem as a program error and provides a solution through the importation of specific Support Packages for various versions of SAP NetWeaver BW. These versions and the corresponding Support Packages are:

- BW 7.00: Support Package 31 (SAPKW70031), further described in SAP Note 1782745.
- BW 7.01 (EHP 1): Support Package 14 (SAPKW70114), detailed in SAP Note 1794836.
- BW 7.02 (EHP 2): Support Package 14 (SAPKW70214), elaborated in SAP Note 1800952.
- BW 7.11: Support Package 12 (SAPKW71112), outlined in SAP Note 1797080.
- BW 7.30: Support Package 10 (SAPKW73010), mentioned in SAP Note 1810084.
- BW 7.31 (EHP 1): Support Package 8 (SAPKW73108), referenced in SAP Note 1813987.
- BW 7.40: Support Package 3 (SAPKW74003), described in SAP Note 1818593.

In cases where immediate resolution is required, it is suggested to use correction instructions. However, before proceeding with the correction instructions, customers should check SAP Note 1668882 to ensure they have the latest transaction SNOTE. The note also indicates that the referenced notes might be preliminary versions and may be available before the full Support Package release.