{"Request": {"Number": "376378", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 389, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000376378?language=E&token=18542D75EF1CCFFEE486D9842F425429"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000376378", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000376378/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "376378"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-APO-OCX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use component BC-FES-GUI"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Advanced Planning and Optimization", "value": "SCM-APO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use component BC-FES-GUI", "value": "SCM-APO-OCX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO-OCX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "376378 - Error message when calling the SNP Optimizer"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>APO SNP Optimizer<br />Error message /SAPAPO/SNP 565 \"Error occurred when communicating with control\"</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Patch, Gui, Sapgui, frontend, front, graphics, SNP Optimizer, installation, SAPClient, APO, frontend patch, APOOCX, cockpit, tree, TreeControl, 46C, 46D, SCE01, SCC, SCC01</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>If you start the SNP Opimizer from interactive planning, the system displays error message:<br />/SAPAPO/SNP 565 \"Error occurred when communicating with control\"<br />before you can execute the Optimizer from the graphic user<br />However, you can start the Optimizer.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To solve the problem, you can temporarily deviate from the official patch procedure as follows:</p> <OL>1. Download the affected OCX file 'aposnpop.ocx' from sapservX:<br />sapservx/dist</OL> <OL>2. Copy the OCX file onto the local frontend to the existing APO controls</OL> <OL>3. Register the new OCX control by doubleclick<br />(If required, open the control with regsvr32.exe)</OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031799)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000376378/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000376378/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000376378/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000376378/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000376378/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000376378/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000376378/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000376378/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000376378/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885"}, {"RefNumber": "396640", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Implementing SEM 3.0A Frontend patch", "RefUrl": "/notes/396640"}, {"RefNumber": "367964", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 8 for APO Release 3.0A", "RefUrl": "/notes/367964"}, {"RefNumber": "363063", "RefComponent": "SCM-APO-OCX", "RefTitle": "Importing APO 3.0 frontend SP 7 for FCS customers", "RefUrl": "/notes/363063"}, {"RefNumber": "362198", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/362198"}, {"RefNumber": "361683", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 7 for APO Release 3.0A", "RefUrl": "/notes/361683"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "356408", "RefComponent": "SCM-APO-OCX", "RefTitle": "How to Apply APO 3.0A Frontend Patch", "RefUrl": "/notes/356408"}, {"RefNumber": "353512", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 6 for APO Release 3.0A", "RefUrl": "/notes/353512"}, {"RefNumber": "336491", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 5 for APO Release 3.0A", "RefUrl": "/notes/336491"}, {"RefNumber": "320835", "RefComponent": "SCM-APO-OCX", "RefTitle": "APO 3.0 frontend SP and GUI compatibility", "RefUrl": "/notes/320835"}, {"RefNumber": "314973", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/314973"}, {"RefNumber": "311212", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/311212"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "356408", "RefComponent": "SCM-APO-OCX", "RefTitle": "How to Apply APO 3.0A Frontend Patch", "RefUrl": "/notes/356408 "}, {"RefNumber": "396640", "RefComponent": "FIN-SEM-CPM", "RefTitle": "Implementing SEM 3.0A Frontend patch", "RefUrl": "/notes/396640 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885 "}, {"RefNumber": "320835", "RefComponent": "SCM-APO-OCX", "RefTitle": "APO 3.0 frontend SP and GUI compatibility", "RefUrl": "/notes/320835 "}, {"RefNumber": "353512", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 6 for APO Release 3.0A", "RefUrl": "/notes/353512 "}, {"RefNumber": "363063", "RefComponent": "SCM-APO-OCX", "RefTitle": "Importing APO 3.0 frontend SP 7 for FCS customers", "RefUrl": "/notes/363063 "}, {"RefNumber": "367964", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 8 for APO Release 3.0A", "RefUrl": "/notes/367964 "}, {"RefNumber": "361683", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 7 for APO Release 3.0A", "RefUrl": "/notes/361683 "}, {"RefNumber": "336491", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 5 for APO Release 3.0A", "RefUrl": "/notes/336491 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}