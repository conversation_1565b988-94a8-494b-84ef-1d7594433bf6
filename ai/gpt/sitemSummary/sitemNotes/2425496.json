{"Request": {"Number": "2425496", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 513, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000058442018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002425496?language=E&token=F832EE3002B01ED709D5CF2000A679F2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002425496", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002425496/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2425496"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.01.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2425496 - SAPBWNews BW 7.02 ABAP SP 20"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note deals with ABAP Support Package&#160;20 for BW release 7.02 (SAP NW 7.0 Enhancement Package 02).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for 7.02,&#160;&#160;BW Patches, BI, BI 7.02, SAPBINews</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBWNews for ABAP Support Package 20 for BW release 7.02 (part of SAP NetWeaver 7.0 Enhancement Package 02)<br /><br />It provides a list of all notes describing the corrections or enhancements in Support Package 20. This note will be updated, when other notes are added.</p>\r\n<p>Please note: Release 7.0x is out of maintenace and support packes are no more availalbe as for a maintained release. <br />To find out about Support Packages for SAP NetWeaver 7.0x Business Warehouse in 2018 - 2020 please read note<br />&#160; <a target=\"_blank\" href=\"/notes/2581475\">2581475</a></p>\r\n<p><br /><strong>Factors you must take into account when you import the Support Package:</strong></p>\r\n<ul>\r\n<ul>\r\n<li>After implementing a support package,usually subsequent work with the transaction&#160;&#160;SNOTE is required. Already installed notes can&#160;&#160;become inconsistent and cause malfunction or syntax errors.Use the transaction SNOTE to -implement the inconsistent notes. Not till then your BW system is again in a functional and consistent state.</li>\r\n<li>\r\n<p><strong>Before</strong> you import the Note please read note <a target=\"_blank\" href=\"/notes/2248091\">2248091 - Change to reimplementation handling&#160;</a></p>\r\n</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>Issues that may occur after you import the Support Package:</strong></p>\r\n<ul>\r\n<ul>\r\n<li>For further information on fixes please see the referenced notes</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>Errors corrected/Important Enhancements in this Support Package:</strong></p>\r\n<ul>\r\n<ul>\r\n<li>Please see the <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/supportpackage/\">https://launchpad.support.sap.com/#/supportpackage/</a><a target=\"_blank\" href=\"https://service.sap.com/sap/bc/bsp/spn/spat/index.htm?sp1=SAPKW70218\">SAPKW70220</a></li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><br /><br /><br /></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D031867)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002425496/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002425496/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002425496/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002425496/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002425496/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002425496/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002425496/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002425496/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002425496/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2581475", "RefComponent": "BW", "RefTitle": "Support Packages for SAP NetWeaver 7.0x Business Warehouse in 2018 - 2020", "RefUrl": "/notes/2581475 "}, {"RefNumber": "2552532", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Error in PAK for use of optional formula variables without a value", "RefUrl": "/notes/2552532 "}, {"RefNumber": "2552121", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "GROUP BY error in BW Datafederator text read", "RefUrl": "/notes/2552121 "}, {"RefNumber": "2551214", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOREACH IN VARIABLE", "RefUrl": "/notes/2551214 "}, {"RefNumber": "2550726", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Termination during combination generation planning functions in SAP HANA", "RefUrl": "/notes/2550726 "}, {"RefNumber": "2549488", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "SID column not exposed using F4 view with transitive attribute", "RefUrl": "/notes/2549488 "}, {"RefNumber": "2548299", "RefComponent": "BW-BEX-OT-HCPR", "RefTitle": "Failure during MultiProvider Conversion to HCPR", "RefUrl": "/notes/2548299 "}, {"RefNumber": "2546438", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Critical Program Error during the submit of variables of Single Data Provider after Copy Sheet / Drill down according to <XYZ> in New Worksheets - Backend", "RefUrl": "/notes/2546438 "}, {"RefNumber": "2544422", "RefComponent": "BW-BEX-ET-CTS", "RefTitle": "Error message \"InfoCube ' ' is not available in version ' ' \" in the transport log", "RefUrl": "/notes/2544422 "}, {"RefNumber": "2542033", "RefComponent": "BW-BEX-ET", "RefTitle": "Dummy <PERSON>late Note", "RefUrl": "/notes/2542033 "}, {"RefNumber": "2541094", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Additional functionality for the function RSZ_DB_COMP_WHERE_USED", "RefUrl": "/notes/2541094 "}, {"RefNumber": "2540253", "RefComponent": "BW-BEX-ET-CTS", "RefTitle": "Error activating element 'XYZ...' during installation of a query", "RefUrl": "/notes/2540253 "}, {"RefNumber": "2540201", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime when using the Descendants .... function Leaves for flat hierarchy", "RefUrl": "/notes/2540201 "}, {"RefNumber": "2539186", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "PSA: Copy of Change Log Deletion Variant is not possible when the variant contains patterns in the Info Area.", "RefUrl": "/notes/2539186 "}, {"RefNumber": "2537093", "RefComponent": "BW-BEX-OT-ODP", "RefTitle": "CDS based BW Query: enable the use of CDS Parameter as Query Key Date", "RefUrl": "/notes/2537093 "}, {"RefNumber": "2533700", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Class CL_RSR_MDX_FILTER_SETXX: Runtime optimization", "RefUrl": "/notes/2533700 "}, {"RefNumber": "2535752", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "Last condition cannot be removed", "RefUrl": "/notes/2535752 "}, {"RefNumber": "2535772", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Selector : \"Select Filter Value\"(F4) on an Info Object containing Key Figures as attributes results in an error leading to closing of BEx Analyzer or BEx Query Designer", "RefUrl": "/notes/2535772 "}, {"RefNumber": "2533468", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Filter values are shown as Key in a Workbook", "RefUrl": "/notes/2533468 "}, {"RefNumber": "2532521", "RefComponent": "BW-MT-ADSO", "RefTitle": "BWMT ADSO : Misleading generic error message when using InfoObject without master data as Template in ADSO Wizard", "RefUrl": "/notes/2532521 "}, {"RefNumber": "2531234", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Minor runtime improvements during execution of MDX statements", "RefUrl": "/notes/2531234 "}, {"RefNumber": "2529454", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Master Data: Execution of the Report RSDMDD_DELETE_BATCH_PACKAGE dumps when the Info Object does not contain Attribute Data.", "RefUrl": "/notes/2529454 "}, {"RefNumber": "2528661", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Further optimization of LastPeriods function", "RefUrl": "/notes/2528661 "}, {"RefNumber": "2529045", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Different FIELDNM<num> in Queryprovider in Expert Routines", "RefUrl": "/notes/2529045 "}, {"RefNumber": "2498777", "RefComponent": "BW4-DM-DTO", "RefTitle": "Error corrections", "RefUrl": "/notes/2498777 "}, {"RefNumber": "2520330", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Distribution with reference data in SAP HDB - behavior", "RefUrl": "/notes/2520330 "}, {"RefNumber": "2519247", "RefComponent": "BW-WHM-MTD-CTS", "RefTitle": "MAIN_NEWBAS/XPRAS_AIMMRG: Invalid objects: 'After Import' terminated", "RefUrl": "/notes/2519247 "}, {"RefNumber": "2519271", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Filter with text nodes without children", "RefUrl": "/notes/2519271 "}, {"RefNumber": "2519104", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Text Symbols for the UI elements of the screens introduced for creating Local Exceptions.", "RefUrl": "/notes/2519104 "}, {"RefNumber": "2518671", "RefComponent": "BW-B4H-CNV", "RefTitle": "Check B4H mode also reads D version for archives", "RefUrl": "/notes/2518671 "}, {"RefNumber": "2518508", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "\"SQLCode = -143: Column not found\" for activation of DAP", "RefUrl": "/notes/2518508 "}, {"RefNumber": "2504970", "RefComponent": "BW-MT-ADSO", "RefTitle": "BWMT ADSO - Avoid loading of all InfoObjects when opening planning ADSO's", "RefUrl": "/notes/2504970 "}, {"RefNumber": "2518451", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Convert selected Exception values to correct format.", "RefUrl": "/notes/2518451 "}, {"RefNumber": "2517045", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "Unable to generate HANA View on Query with no key figures", "RefUrl": "/notes/2517045 "}, {"RefNumber": "2513880", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "PAK: FOX, distribution with reference data: System checks too many records", "RefUrl": "/notes/2513880 "}, {"RefNumber": "2512777", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime for use of function LastPeriods", "RefUrl": "/notes/2512777 "}, {"RefNumber": "2512077", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime in method SET_PRECISION of class CL_RSR_MDX_OPERATOR", "RefUrl": "/notes/2512077 "}, {"RefNumber": "2511369", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "BW Aggregates with NavAttr and Textnodes wrong after CR", "RefUrl": "/notes/2511369 "}, {"RefNumber": "2510467", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Function PeriodsToDate - too many key figures are read", "RefUrl": "/notes/2510467 "}, {"RefNumber": "2509239", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Runtime: Calculated member - too many key figures", "RefUrl": "/notes/2509239 "}, {"RefNumber": "2509362", "RefComponent": "BW-WHM-DBA-SDEL", "RefTitle": "Massive deletion in Bank Analyser is not running parallel", "RefUrl": "/notes/2509362 "}, {"RefNumber": "2507360", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Analysis Office launched from NetWeaver via URL with wrong user", "RefUrl": "/notes/2507360 "}, {"RefNumber": "2507153", "RefComponent": "BW4-DM-DTO", "RefTitle": "Exception CX_SY_REGEX_TOO_COMPLEX after a SAP IQ NLS SQL error", "RefUrl": "/notes/2507153 "}, {"RefNumber": "2506987", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Termination in CHECK_VIEW_DATA", "RefUrl": "/notes/2506987 "}, {"RefNumber": "2506311", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "PSA: The report RSAR_PSA_NEWDS_MAPPING_CHECK does not correct the obsolete PSAs for Segmented Datasource which are no longer used.", "RefUrl": "/notes/2506311 "}, {"RefNumber": "2504824", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "BEx Analyzer: BEx messages are not captured after implementing the note 2461596", "RefUrl": "/notes/2504824 "}, {"RefNumber": "2501676", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX reference data selection on attributes.", "RefUrl": "/notes/2501676 "}, {"RefNumber": "2500399", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Runtime optimization for PeriodsToDate function", "RefUrl": "/notes/2500399 "}, {"RefNumber": "2500792", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Termination in PREFETCH_ATTRIBUTES", "RefUrl": "/notes/2500792 "}, {"RefNumber": "2500371", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "[IQ Error SQL99999] when importing archived data", "RefUrl": "/notes/2500371 "}, {"RefNumber": "2499081", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Unclear error message 'SID_INDEXH'", "RefUrl": "/notes/2499081 "}, {"RefNumber": "2498098", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtimes for use of function ORDER", "RefUrl": "/notes/2498098 "}, {"RefNumber": "2497948", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "SID key figures in FOX", "RefUrl": "/notes/2497948 "}, {"RefNumber": "2495340", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Last/latest data update incorrect for aggregation levels", "RefUrl": "/notes/2495340 "}, {"RefNumber": "2495799", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX: External aggregation levels in PAK \"No initial value defined for attribute: xxx\"", "RefUrl": "/notes/2495799 "}, {"RefNumber": "2495406", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime for descendants on flat hierarchy", "RefUrl": "/notes/2495406 "}, {"RefNumber": "2493932", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Unnecessary access to OLAP processor", "RefUrl": "/notes/2493932 "}, {"RefNumber": "2492803", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Missing data after extraction from ADK-archived, write-optimized DSO", "RefUrl": "/notes/2492803 "}, {"RefNumber": "2492250", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Validation of initial SID key figures", "RefUrl": "/notes/2492250 "}, {"RefNumber": "2489433", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS InA Conditions with Formula", "RefUrl": "/notes/2489433 "}, {"RefNumber": "2491209", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS InA Federation System Alias", "RefUrl": "/notes/2491209 "}, {"RefNumber": "2482312", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "Multiple fixes in DTO of adso in BW4", "RefUrl": "/notes/2482312 "}, {"RefNumber": "2489288", "RefComponent": "BW-BEX-ET-BC-PREC", "RefTitle": "Precalculation: The router string computed by merging two router strings(One from SAP GUI and other from the Precalculation Server) in dialog mode is NOT correct.", "RefUrl": "/notes/2489288 "}, {"RefNumber": "2487836", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "High CPU utilization due to SMART MERGE while loading via RSDRI", "RefUrl": "/notes/2487836 "}, {"RefNumber": "2372945", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "NDSO nach Vorlage von ADSO anlegen", "RefUrl": "/notes/2372945 "}, {"RefNumber": "2477412", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Runtime optimization for use of multiple calculated members", "RefUrl": "/notes/2477412 "}, {"RefNumber": "2477059", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX: Key field for internal tables of type I", "RefUrl": "/notes/2477059 "}, {"RefNumber": "2475867", "RefComponent": "BW-WHM-DBA-SDEL", "RefTitle": "Selective Deletion of large datavolume on BW ADSO fails", "RefUrl": "/notes/2475867 "}, {"RefNumber": "2475774", "RefComponent": "BW-MT-ELEM", "RefTitle": "BW-MT: Performance optimization for retrieving of a tree of CKF/RKF in Query Designer", "RefUrl": "/notes/2475774 "}, {"RefNumber": "2474296", "RefComponent": "BW-BEX-OT", "RefTitle": "Variable container raises exception  CX_BICS_PROGRAM_EXCEPTION", "RefUrl": "/notes/2474296 "}, {"RefNumber": "2470783", "RefComponent": "BW-BEX-OT-HCPR", "RefTitle": "Parameter not passed to Script based HANA BW Provider", "RefUrl": "/notes/2470783 "}, {"RefNumber": "2464871", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime for function CurrentMember", "RefUrl": "/notes/2464871 "}, {"RefNumber": "2467563", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "GetServerInfo: Renew RFC Reentrance Ticket", "RefUrl": "/notes/2467563 "}, {"RefNumber": "2466186", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtimes with constants in multiplication/division operations", "RefUrl": "/notes/2466186 "}, {"RefNumber": "2465723", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Execution of Planning Function or Planning Sequence fails when the value of variable based on Info Object without master data does not exists in the corresponding SID table.", "RefUrl": "/notes/2465723 "}, {"RefNumber": "2464672", "RefComponent": "BW-BEX-OT-BICS-RSRT", "RefTitle": "ABAP BICS: Unable to enter variables when using prequery", "RefUrl": "/notes/2464672 "}, {"RefNumber": "2463769", "RefComponent": "BW4-DM-DTO", "RefTitle": "BW4:DTO:Cannot change the partition field once we move data to cold tier", "RefUrl": "/notes/2463769 "}, {"RefNumber": "2457209", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External HANA View Creation Failure Due to Compounded Navigation Attributes", "RefUrl": "/notes/2457209 "}, {"RefNumber": "2461710", "RefComponent": "BW-MT-IOBJ", "RefTitle": "BW4HANA - BWMT InfoObject - Invalid fixed currency allowed for InfoObject", "RefUrl": "/notes/2461710 "}, {"RefNumber": "2461596", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Data Provider assignment is lost after converting the Info Providers via the report RSO_CONVERT_IPRO_TO_HCPR.", "RefUrl": "/notes/2461596 "}, {"RefNumber": "2461035", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX: External aggregation levels - dump in generated program", "RefUrl": "/notes/2461035 "}, {"RefNumber": "2451059", "RefComponent": "BW-WHM-MTD", "RefTitle": "Duplicate log entries for deletion of t-logo objects", "RefUrl": "/notes/2451059 "}, {"RefNumber": "2460131", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Error messages for start of planning sequence from AO or Web", "RefUrl": "/notes/2460131 "}, {"RefNumber": "2459975", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Direct Input for a compounded Info Object on an input ready new line does not work correctly.", "RefUrl": "/notes/2459975 "}, {"RefNumber": "2459505", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Memory problems when using the function LastPeriods (MDX dimension not in slicer)", "RefUrl": "/notes/2459505 "}, {"RefNumber": "2459417", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX: Editing of formulas - dump INHERITED_ERROR", "RefUrl": "/notes/2459417 "}, {"RefNumber": "2456761", "RefComponent": "BW-MT-ADSO", "RefTitle": "BWMT 7.5x - ADSO Planning Mode is not available in 7.50", "RefUrl": "/notes/2456761 "}, {"RefNumber": "2456689", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "System dump CONVT_NO_NUMBER when loading a query", "RefUrl": "/notes/2456689 "}, {"RefNumber": "2453464", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Prevention of saving of inconsistent models", "RefUrl": "/notes/2453464 "}, {"RefNumber": "2453122", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Terminations due to main memory bottlenecks when complex calculated members are used", "RefUrl": "/notes/2453122 "}, {"RefNumber": "2452708", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime for usage of ParallelPeriod function", "RefUrl": "/notes/2452708 "}, {"RefNumber": "2451619", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS InA GetServerInfo Cache-Behaviour", "RefUrl": "/notes/2451619 "}, {"RefNumber": "2451378", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX: Planning function is not executed on SAP HANA DB", "RefUrl": "/notes/2451378 "}, {"RefNumber": "2451477", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Text Symbols for the Language dependent texts in the Local Formula Editor.", "RefUrl": "/notes/2451477 "}, {"RefNumber": "2446399", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS InA: Conditons with two or more thresholds", "RefUrl": "/notes/2446399 "}, {"RefNumber": "2446257", "RefComponent": "BW", "RefTitle": "BACKUP: Korrekturen zu SAP BW / SAP BW/4HANA (ABAP)", "RefUrl": "/notes/2446257 "}, {"RefNumber": "2446218", "RefComponent": "BW", "RefTitle": "BACKUP: Corrections for SAP BW / SAP BW/4HANA (ABAP)", "RefUrl": "/notes/2446218 "}, {"RefNumber": "2443906", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX: External aggregation levels", "RefUrl": "/notes/2443906 "}, {"RefNumber": "2443311", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Repair inconsistent node table entries for remote hierarchies", "RefUrl": "/notes/2443311 "}, {"RefNumber": "2441930", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "BICS and BEx: Avoid inconsistent behavior of zero suppression and universal display hierarchy (BW ABAP)", "RefUrl": "/notes/2441930 "}, {"RefNumber": "2439475", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS InA: Ensure stable sort order in Fixed Filter", "RefUrl": "/notes/2439475 "}, {"RefNumber": "2439823", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime/memory problems if using the LastPeriods function", "RefUrl": "/notes/2439823 "}, {"RefNumber": "2439560", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Current Member variable doesn't work correctly with NCUM key figure", "RefUrl": "/notes/2439560 "}, {"RefNumber": "2438521", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning function for delta execution in PAK: Error message RSPLF 016 - Value of characteristic &1: &2 is not included in the selection", "RefUrl": "/notes/2438521 "}, {"RefNumber": "2437739", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Manage-API: Löschen aller Daten und Löschen eines Datenziels", "RefUrl": "/notes/2437739 "}, {"RefNumber": "2435539", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "The value help for unit convertions shows duplicate entries", "RefUrl": "/notes/2435539 "}, {"RefNumber": "2433785", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Safety belt raises false message even though the total number of cells is less than the limit set.", "RefUrl": "/notes/2433785 "}, {"RefNumber": "2433463", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Editing of key figure values in the Planning Modeler", "RefUrl": "/notes/2433463 "}, {"RefNumber": "2420689", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Index repair functionality for APO InfoCubes", "RefUrl": "/notes/2420689 "}, {"RefNumber": "2431673", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "RSSDA_CREATE_TABLE_STAT shows error: statistics cannot be found (HANA 1)", "RefUrl": "/notes/2431673 "}, {"RefNumber": "2430084", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Time dimension check incorrectly reports inconsistency", "RefUrl": "/notes/2430084 "}, {"RefNumber": "2430248", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "empty cells instead of empty result set", "RefUrl": "/notes/2430248 "}, {"RefNumber": "2429287", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BOC: transient Query ATtributes", "RefUrl": "/notes/2429287 "}, {"RefNumber": "2429247", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS InA: Complex filters with character nodes", "RefUrl": "/notes/2429247 "}, {"RefNumber": "2421634", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Missing cell data for calculated member and default measure II", "RefUrl": "/notes/2421634 "}, {"RefNumber": "2423640", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Long runtime for queries with very large selections", "RefUrl": "/notes/2423640 "}, {"RefNumber": "2427151", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Deletion of detached DTPs", "RefUrl": "/notes/2427151 "}, {"RefNumber": "2427779", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Error BRAIN 641 not suppressible", "RefUrl": "/notes/2427779 "}, {"RefNumber": "2425847", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy attribute \"Do not display leaves for inner-nodes in the Query\" does not work correctly", "RefUrl": "/notes/2425847 "}, {"RefNumber": "2425579", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformationen werden nicht korrekt gesperrt", "RefUrl": "/notes/2425579 "}, {"RefNumber": "2425375", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Exception in transaction RAAOE/RAAOP", "RefUrl": "/notes/2425375 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "702", "To": "702", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}