SAP Note 1160685 details the use of the ASU variant restorer outside of the context of a technical upgrade. The main points of this note are as follows:

1. The transport files associated with this SAP Note are now outdated and should not be imported if the system is running on specified releases/patches as per SAP Note 2783555. Instead, the functionality is included in the standard delivery for those versions. For older releases or patches, users should refer to SAP Note 2915357.

2. The ASU variant restorer is typically delivered with the PREPARE tool import during an upgrade, but there may be a need to use it separately, such as when importing a modifying add-on.

3. The relevant transports (RASUVAR_START_FINISH.zip for SAP_ABA 620 to SAP_ABA 750 SP15, or Y52K900277.zip for Release 4.6C) should only be imported into systems within the specified release range. Corrections for newer systems are included in the standard delivery.

4. The program RASUVAR_START should be run before conversion activities, such as before importing a modifying add-on. It stores necessary information about selection variants in table TASUVAR1.

5. The program RASUVAR_FINISH is used to convert the variants, ensuring they can still be read after conversion and attempting to restore them based on information in table TASUVAR1 if necessary.

6. Both programs allow for restrictions to specific ABAP programs and have options for converting customer-specific variants, SAP system variants, and for creating a spool list of the log file.

7. For optimizing runtime, RASUVAR_START and RASUVAR_FINISH can be run in parallel, with settings to limit the number of processes used.

8. These programs should be executed in the background and must never run concurrently with upgrade programs JOB_RASUVAR1/2 because they could interfere with one another and delete data.

9. There is a provision for automatically adjusting renamed fields of the selection screen through the maintenance of table TASUVAR_RENAME.

In summary, SAP Note 1160685 provides guidance and transports for using the ASU variant restorer tools outside of a technical upgrade, with a specific emphasis on the versions that the instructions and transports apply to. It also cautions against the conflict between these tools and upgrade processes.