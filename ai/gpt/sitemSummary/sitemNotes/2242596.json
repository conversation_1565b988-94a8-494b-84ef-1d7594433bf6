{"Request": {"Number": "2242596", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 352, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018206642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002242596?language=E&token=1D865535315D646F8F3FC7A414D08229"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002242596", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002242596/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2242596"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 31}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.08.2016"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2242596 - Release Information: Changes in Fiori Content in S/4HANA Finance 1605 (sFIN 3.0)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note describes the changes to Fiori content after upgrading to S/4HANA Finance 1605 (sFIN 3.0). Please read the note carefully before applying the frontend component UIAPFI70 300.</p>\r\n<p>Important to understand is&#160;that the software component version UIAPFI70 300 which contains the Fiori content and the Fiori apps&#160;can be used with two different backend components. Either it's used with SAP_FIN 730 (part of S/4HANA Finance 1605 (sFIN 3.0)) or it's used with S4CORE 100&#160;(part of SAP S/4HANA, on-premise edition 1511). This has an impact on factsheets and Smart Business apps. Details are described in the corresponding chapters below.</p>\r\n<p>&#160;</p>\r\n<p><strong>Fiori Content Artifacts</strong></p>\r\n<p>The Fiori content comprises various artifacts that are part of the frontend component UIAPFI70 300. These artifacts are:</p>\r\n<ul>\r\n<li>Technical catalogs</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Technical catalogs can be seen as a repository of apps. The apps are reflected in the catalog by a tile (or app launcher) and a target mapping.</p>\r\n<ul>\r\n<li>Business catalogs</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Business catalogs are technically the same objects as technical catalogs, but they contain only references to tiles and target mappings in technical catalogs. The content of a business catalog is restricted to a&#160;special business area. The content of business catalogs is not directly visible in the Fiori launchpad, but the user can pick tiles (representing apps) out of the catalog and put them onto the launchpad.</p>\r\n<ul>\r\n<li>Business groups</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Business groups contain tiles from business catalogs. The content of business groups are immediately visible on the launchpad.</p>\r\n<ul>\r\n<li>Corresponding PFCG roles</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">PFCG roles containing catalogs and groups give users access to the content of the catalog, and the content of the groups is shown directly&#160;on the launchpad.</p>\r\n<p>&#160;</p>\r\n<p><strong>Fiori Content Model in SAP Simple Finance, on-premise edition 1503 (sFIN 2.0)</strong></p>\r\n<p>In SAP Simple Finance, on-premise edition 1503 (sFIN 2.0) the guiding principal was to store Fiori applications in technical catalogs according to application archetypes like analytical apps, transactional apps, and factsheets. Each app must be part of a business catalog and part of a business group. Each technical catalog must be part of a technical catalog role and each business catalog and business group must be part of a business catalog role. Business catalogs and groups are divided according to application areas. Business roles were not&#160;delivered with this release.</p>\r\n<p>&#160;</p>\r\n<p><strong>Fiori Content Model in SAP&#160;S/4HANA Finance 1605 (sFIN 3.0)</strong></p>\r\n<p>With this shipment the content was completely restructured. The previous separation by app archetype turned out to not be&#160;suitable due to various technolgies being used. Thus the technical catalogs have been&#160;divided according to application areas. In addition, the most used SAPGUI transactions have been added to separate technical catalogs so that they can also be used from the Fiori launchpad.</p>\r\n<ul>\r\n<li>The business catalogs have been restructured as well. They have been broken down by segregation of duty rules, so they are more finely granular, and contain not only Fiori apps but also references to SAPGUI transactions.</li>\r\n<li>Business groups are used to expose those applications directly on the launchpad that are used most often. So not every app is part of a business group. In addition a group can contain apps from various business catalogs.</li>\r\n<li>Business roles are newly introduced artifacts and they contain all business groups and business catalogs that fit&#160;the role of an end user, e.g. an accounts receivable accountant.</li>\r\n<li>Business catalog roles and technical roles are not used any more in this content model.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Technical Catalogs:</strong></p>\r\n<p>The technical catalogs are a central part of the Fiori content model and can be seen as a kind of repository that contains the Fiori apps. In SAP Simple Finance 1503, the technical catalogs are divided according to technologies, while in S/4 Hana Finance they have been divided according to application areas.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td colspan=\"2\">\r\n<p><strong>SAP S/4HANA Finance 1605 (sFIN 3.0)</strong></p>\r\n</td>\r\n<td colspan=\"2\"><strong>SAP Simple Finance, on-premise edition&#160;1503 (sFIN 2.0)</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Technical Catalog</strong></td>\r\n<td><strong>Description</strong></td>\r\n<td><strong>Technical Catalog</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>SAP_TC_FIN_ACC_COMMON</td>\r\n<td>SAP: Financials - Accounting</td>\r\n<td>SAP_SFIN_TC_T_FI</td>\r\n<td>SAP: Financials Add-On - Transactional Apps</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_TC_FIN_FO_COMMON</td>\r\n<td>SAP: Financials - Accounts Payable/Receivable Apps</td>\r\n<td>SAP_SFIN_TC_A</td>\r\n<td>SAP: Financials Add-On - Analytical Apps</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_TC_FIN_CM_COMMON</td>\r\n<td>SAP: Financials - Cash Management Apps</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_TC_FIN_CO_COMMON</td>\r\n<td>SAP: Financials - Controlling Apps</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_TC_FIN_TRM_COMMON</td>\r\n<td>SAP: Financials - Treasury and Risk Management</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_TRC_T</td>\r\n<td>SAP: Financials add-on - Factsheets</td>\r\n<td>SAP_SFIN_TRC_T</td>\r\n<td>sFin2.0 Factsheets</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_TC_SFIN_SMB_COMMON</td>\r\n<td>SAP: Financials add-on - Smart Business Apps</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The technical catalogs SAP_SFIN_TC_T_FI and SAP_SFIN_TC_A have been deleted. Their apps have been moved to one of the new technical catalogs.</p>\r\n<p><strong>Special technical catalogs for SAPGUI transactions:</strong></p>\r\n<p>A large number of commonly used SAPGUI transactions have been made accessible by the Fiori launchpad. For doing so the transactions got an app descriptor object assigned on the backend and clustered in so called technical backend catalogs. Those technical backend catalogs can be made accessible on the frontend server by replicating them.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Technical Backend&#160;Catalog</strong></td>\r\n</tr>\r\n<tr>\r\n<td>SAP_TC_FIN_FO_BE_APPS:S4FIN</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_TC_FIN_CO_BE_APPS:S4FIN</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_TC_FIN_ACC_BE_APPS:S4FIN</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_TC_FIN_CM_BE_APPS:S4FIN</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The content of these technical backend catalogs is already referenced in the new business catalogs. When using them the first time you will recognize broken references until an RFC connection to the backend is provided. Details about the replication and the creation of the RFC destination are described in the note <a target=\"_blank\" href=\"/notes/2269272\">2269272</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Business Catalogs:</strong></p>\r\n<p>The following business catalogs have been deleted with SAP S/4HANA Finance 1605 (sFIN 3.0):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Catalog</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BC_MANAGER</td>\r\n<td>Accounting Information for Managers - Content</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BC_PAY_CLERK</td>\r\n<td>Accounts Payables Accountant - Content</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BC_REC_CLERK</td>\r\n<td>Accounts Receivable Accountant - Content</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BC_CASHMANAGER</td>\r\n<td>Cash Manager - Content</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BC_CONTROLLER</td>\r\n<td>Controller - Content</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BC_COSTMANAGER</td>\r\n<td>Cost Manager - Content</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BC_GLACCOUNTANT</td>\r\n<td>G/L Accountant - Content</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BC_SALESMANAGER</td>\r\n<td>Sales Manager (Financials Add-On) - Content</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Business Catalog Roles and Technical Catalog Roles:</strong></p>\r\n<p>The following business catalog roles and technical catalog roles have been deleted with SAP S/4HANA Finance 1605 (sFIN 3.0):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Role</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>SAP_FSCM_TCR_T</td>\r\n<td>SAP FSCM Technical Catalog Role for Transactions and Factsheets</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_TCR_A</td>\r\n<td>SAP sFIN Technical Catalog Role for Analytics</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_TCR_T</td>\r\n<td>SAP Role for Financials Add-On - Transactional Apps and Fact Sheets</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BCR_CASHMANAGER</td>\r\n<td>Cash Manager - Apps</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BCR_CONTROLLER</td>\r\n<td>Controller - Apps</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BCR_COSTMANAGER</td>\r\n<td>Cost Manager - Apps</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BCR_GLACCOUNTANT</td>\r\n<td>G/L Accountant - Apps</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BCR_MANAGER</td>\r\n<td>Accounting Information for Managers - Apps</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BCR_PAYABLES_CLERK</td>\r\n<td>Accounts Payable Accountant - Apps</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BCR_RECEIVABLES_CLERK</td>\r\n<td>Accounts Receivable Accountant - Apps</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SFIN_BCR_SALESMANAGER</td>\r\n<td>Sales Manager (Simple Finance Add-On) - Apps</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Business Roles</strong></p>\r\n<p>The business catalog roles SAP_SFIN_BCR_* have been replaced by newly introduced business roles. Those roles contain all relevant catalogs and groups.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Role</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_AA_ACCOUNTANT</td>\r\n<td>Asset Accountant</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_AP_ACCOUNTANT&#160;</td>\r\n<td>Accounts Payable Accountant</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_AP_MANAGER</td>\r\n<td>Accounts Payable Manager</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_AR_ACCOUNTANT</td>\r\n<td>Accounts Receivable Accountant</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_AR_MANAGER</td>\r\n<td>Accounts Receivable Manager</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_CASH_MANAGER</td>\r\n<td>Cash Manager</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_CASH_SPECIALIST</td>\r\n<td>Cash Management Specialist</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_CONTROLLER</td>\r\n<td>Controller</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_CREDIT_CONTROLLER</td>\r\n<td>Credit Controller</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_EXTERNAL_AUDITOR</td>\r\n<td>External Auditor</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_GL_ACCOUNTANT&#160;</td>\r\n<td>General Ledger Accountant</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BR_TREASURY_RISK_MANAGER&#160;</td>\r\n<td>Treasury Risk Manager</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Renamed Semantic Objects:</strong></p>\r\n<p>The semantic object \"Vendor\" was renamed to \"Supplier\".&#160;Also the occurrences of \"Vendor\" in the titles of apps have been renamed to \"Supplier\".</p>\r\n<p>This change affects \"saved as tile\" tiles from the apps using the semantic object \"Vendor\". Those saved tiles will not work anymore after the upgrade. Same is for URLs created via \"share in Jam\" or \"send as Email\". Those URLs can be adopted by exchanging \"Vendor\" with \"Supplier\" in the URL.</p>\r\n<p>Applications with changed semantic objects are:</p>\r\n<ul>\r\n<li>Manage Vendor Line Items</li>\r\n<li>Display Vendor Balances</li>\r\n<li>Manage Payment Blocks</li>\r\n<li>Post Outgoing Payments</li>\r\n<li>Clear Outgoing Payments</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Replaced Apps</strong></p>\r\n<p>Following apps have been replaced:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Replaced App</strong></td>\r\n<td><strong>Fiori ID&#160;</strong></td>\r\n<td><strong>Semantic Object</strong></td>\r\n<td><strong>Action</strong></td>\r\n<td><strong>New App</strong></td>\r\n<td><strong>Fiori ID&#160;</strong></td>\r\n<td><strong>Semantic Object</strong></td>\r\n<td><strong>Action</strong></td>\r\n</tr>\r\n<tr>\r\n<td>Journal Entry Analyzer</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0952')\">F0952</a></td>\r\n<td>AccountingDocument</td>\r\n<td>analyzeLineItems</td>\r\n<td>\r\n<p>Audit Journal</p>\r\n<p>Display G/L Account&#160;Line Items</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0997')\">F0997</a></p>\r\n<p><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0706')\">F0706</a></p>\r\n</td>\r\n<td>\r\n<p>AccountingJournalList</p>\r\n<p>GLAccount</p>\r\n</td>\r\n<td>\r\n<p>auditJournal</p>\r\n<p>displayLineItems</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Journal Entry History</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0954')\">F0954</a></td>\r\n<td>AccountingDocument</td>\r\n<td>analyzeDocumentChanges</td>\r\n<td>Audit Journal</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0997')\">F0997</a></td>\r\n<td>AccountingJournalList</td>\r\n<td>auditJournal</td>\r\n</tr>\r\n<tr>\r\n<td>Cash Flow Analysis</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0513')\">F0513&#160;</a></td>\r\n<td>*</td>\r\n<td>analyzeKPIDetails</td>\r\n<td>\r\n<p>Actual Cash Flow</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0513A')\">F0513A</a></p>\r\n</td>\r\n<td>\r\n<p>Bank Account</p>\r\n</td>\r\n<td>\r\n<p>analyzeSBKPIDetailsSoH</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Liquidity Forecast (SFIN)</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0512')\">F0512</a></td>\r\n<td>*</td>\r\n<td>analyzeKPIDetails</td>\r\n<td>\r\n<p>Liquidity Forecast</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0512A')\">F0512A</a></p>\r\n</td>\r\n<td>\r\n<p>Bank Account</p>\r\n</td>\r\n<td>\r\n<p>analyzeSBKPIDetailsSoH</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Cash Position</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0511')\">F0511</a></p>\r\n</td>\r\n<td>*</td>\r\n<td>analyzeKPIDetails</td>\r\n<td>\r\n<p>Cash Position</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F1737')\">F1737</a></p>\r\n</td>\r\n<td>\r\n<p>Bank Account</p>\r\n</td>\r\n<td>\r\n<p>analyzeSBKPIDetailsSoH</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Bank Statement Monitor</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0514')\">F0514</a></p>\r\n</td>\r\n<td>*</td>\r\n<td>analyzeKPIDetails</td>\r\n<td>\r\n<p>Bank Statement Monitor</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F1734')\">F1734</a></p>\r\n</td>\r\n<td>\r\n<p>Bank Account</p>\r\n</td>\r\n<td>\r\n<p>analyzeSBKPIDetailsSoH</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Bank Risk</p>\r\n</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0515')\">F0515</a></td>\r\n<td>*</td>\r\n<td>analyzeKPIDetails</td>\r\n<td>\r\n<p>Bank Risk</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0515A')\">F0515A</a></p>\r\n</td>\r\n<td>\r\n<p>Bank Account</p>\r\n</td>\r\n<td>\r\n<p>analyzeSBKPIDetailsSoH</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Deficit Cash Pool</p>\r\n</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0517')\">F0517</a></td>\r\n<td>*</td>\r\n<td>analyzeKPIDetails</td>\r\n<td>\r\n<p>Deficit Cash Pool</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0517A')\">F0517A</a></p>\r\n</td>\r\n<td>\r\n<p>Bank Account</p>\r\n</td>\r\n<td>\r\n<p>analyzeSBKPIDetailsSoH</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Renamed Apps</strong></p>\r\n<p>Following apps have been renamed:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>New Name</strong></td>\r\n<td><strong>Old Name</strong></td>\r\n<td><strong>Fiori ID</strong></td>\r\n</tr>\r\n<tr>\r\n<td>Check Cash Flow Items</td>\r\n<td>Analyze Payment Details</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0735')\">F0735</a></td>\r\n</tr>\r\n<tr>\r\n<td>Manage Supplier Line Items</td>\r\n<td>Manage Vendor Line Items</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0712')\">F0712</a></td>\r\n</tr>\r\n<tr>\r\n<td>Display Supplier Balances</td>\r\n<td>Display Vendor Balances</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0701')\">F0701</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Apps only working with backend software component&#160;S4CORE:</strong></p>\r\n<p>Following apps are only working if the software component UIAPFI70 300 is connected against a backend with S4CORE 100 or higher.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>App Name</strong></td>\r\n<td><strong>Fiori ID</strong></td>\r\n</tr>\r\n<tr>\r\n<td>Asset Master Worklist</td>\r\n<td><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F1592')\">F1592</a></td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; mso-fareast-language: DE; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-bidi-language: AR-SA;\">Material Inventory Values - Balance Summary</span></td>\r\n<td><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; mso-fareast-language: DE; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F1422')\">F1422</a></span></td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; mso-fareast-language: DE; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-bidi-language: AR-SA;\">Material Inventory Values - Line Items</span></td>\r\n<td><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; mso-fareast-language: DE; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F1423')\">F1423</a></span></td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; mso-fareast-language: DE; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-bidi-language: AR-SA;\">Material Inventory Values - Rounding Differences</span></td>\r\n<td><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; mso-fareast-language: DE; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-bidi-language: AR-SA;\"><a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F1440')\">F1440</a></span></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Factsheets</strong></p>\r\n<p>Because the frontend component UIAPFI70 300 is used for SAP S/4HANA Finance 1605 (sFIN 3.0) and SAP S/4HANA on-premise edition 1511, but the framework for factsheets is different, SAP delivers factsheet target mappings for both versions.</p>\r\n<ul>\r\n<li>The technical catalog&#160;SAP_SFIN_TRC_T contains the target mappings for the factsheets to be used with SAP S/4HANA Finance 1605 (sFIN 3.0).</li>\r\n<li>The technical catalogs SAP_TC_FIN_* contain the target mappings for the factsheets to be used with SAP S/4HANA on-premise edition 1511.</li>\r\n</ul>\r\n<p>That means if you create your own catalogs you must reference the factsheet target mappings in catalog SAP_SFIN_TRC_T if you are using SAP S/4HANA Finance 1605 (sFIN 3.0) and in case you are using SAP S/4HANA on-premise edition 1511 you must reference the factsheet target mappings of catalogs SAP_TC_FIN_*, for instance <em>SAP_SFIN_BC_FACT_GLACC.</em></p>\r\n<p>If you are using the predelivered business roles together with SAP S/4HANA Finance 1605 (sFIN 3.0) please remove the factsheet related catalogs like <em>SAP_SFIN_BC_FACT_GLACC G/L Account Factsheets</em> from the role <em>SAP_BR_GL_ACCOUNTANT</em>, and add the factsheet related catalogs <em>SAP_SFIN_TRC_T</em>.</p>\r\n<p>If you combine the factsheets for both products in one role this will lead to errors.</p>\r\n<p>&#160;</p>\r\n<p><strong>Smart Business Apps based on CDS views</strong></p>\r\n<p>With&#160;SAP S/4HANA Finance 1605 (sFIN 3.0) SAP delivers pre-configured tiles for Smart Business apps for the first time. These tiles are templates for evaluating the capabilities of the apps with less effort, but they should not be used directly in productive use.</p>\r\n<ul>\r\n<li>The Smart Business apps for SAP S/4HANA Finance 1605 (sFIN 3.0) based on CDS views are stored in the technical catalog SAP_TC_SFIN_SMB_COMMON.</li>\r\n<li>The Smart Business apps for SAP S/4HANA on-premise edition 1511 based on CDS views are stored in the area-specific technical catalogs, e.g. SAP_TC_FIN_FO_COMMON. The Smart Business apps within the business catalogs like SAP_SFIN_BC_AR_ANALYTICS are references to the tiles for SAP S/4HANA on-premise edition 1511.</li>\r\n</ul>\r\n<p>If the shipped content will be used, e.g. to evaluate apps, it's important that&#160;you use the tiles from the technical catalog SAP_TC_SFIN_SMB_COMMON&#160;for SAP S/4HANA Finance 1605 (sFIN 3.0)!</p>\r\n<p>Using SmartBusiness apps with the wrong backend component will lead to errors in the content.</p>\r\n<p>For the details changes in SAP Smart Business for SAP S/4HANA Finance 1605, refer to SAP note <a target=\"_blank\" href=\"/notes/2277137\" title=\"2277137  - Release information Note: SAP Smart Business for SAP S/4HANA Finance 1605\">2277137</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2242330\" title=\"2242330  - Release Information Note: Analytics f&#252;r SAP S/4HANA Finance 1605\">2242330</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong>Impact on existing content after the upgrade to SAP S/4HANA Finance 1605 (sFIN 3.0)</strong></p>\r\n<ul>\r\n<li>If you have directly used catalogs or groups that are now deleted, e.g. in roles you need to adjust those roles.</li>\r\n<li>If you have created new catalogs but referenced delivered tiles or target mappings, the references will no longer work after the upgrade. You need to adjust your catalogs by creating references to the new tiles/target mappings. Alternatively you can break your references before you apply software component UIAPFI70 300.</li>\r\n<li>Saved tiles, links in JAM or emails created from applications using the semantic object \"Vendor\" will not work anymore. See chapter about renamed semantic objects for more details.</li>\r\n<li>If you use the predelivered content you will recognize that a lot of apps will initially not work. Those apps are HTMLGUI based apps that require an RFC connection. For more details see chapter \"Special technical catalogs for SAPGUI transactions\"</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Recommendations:</strong></p>\r\n<ul>\r\n<li>If you intent to use predelivered content in an productive environment, be aware that with following shipments apps could be replaced or changed, e.g. a WebDynpro App could be replaced with an HTML5 app or the app might get enhanced by new functionality.</li>\r\n<li>Create your own catalogs according to your needs and your&#160;own segregation of duty rules</li>\r\n<li>Decide whether it is suitable to create catalogs in a layered approach, meaning to have one or more technical catalogs as a repository and further catalogs&#160;containing references to the technical catalogs. This approach is recommended in case apps appear in multiple catalogs, because you need to update in one place only, instead of mutliple, e.g. when changing an app title.</li>\r\n<li>To fill your newly created catalogs with tiles and target mappings, first create references to the predelivered artifacts, e.g. by copying predelivered catalogs or by creating references per artifact, and afterwards breaking those references to the predelivered artifacts by saving them.</li>\r\n<li>To find out&#160;which predelivered catalogs contain&#160;apps and how to get them up and running, check the <a target=\"_blank\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\">Fiori Reference Library</a>.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SFIN, Fiori Content, Business Catalogs, Business Roles, Factsheets, Smart Business, Broken References</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Read the above release information to understand the impact of the Fiori content model changes.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-FIO-AA (Fiori UI for Asset Accounting)"}, {"Key": "Other Components", "Value": "FI-FIO-AP (Fiori UI for Accounts Payable)"}, {"Key": "Other Components", "Value": "FI-FIO-AR (Fiori UI for Accounts Receivable)"}, {"Key": "Other Components", "Value": "FI-FIO-GL (Fiori UI for General Ledger Accounting)"}, {"Key": "Other Components", "Value": "FIN-FIO-CCD (Fiori-UI for SAP Collections Management, SAP Credit Managem.)"}, {"Key": "Other Components", "Value": "CO-FIO (Fiori UI for Overhead Cost Controlling)"}, {"Key": "Other Components", "Value": "FIN-FIO-TRM (Fiori UI for Treasury and Risk Management)"}, {"Key": "Other Components", "Value": "FIN-FIO-CLM (Fiori UI for Cash and Liquidity Management)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D024096)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I027565)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002242596/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002242596/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002242596/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002242596/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002242596/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002242596/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002242596/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002242596/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002242596/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2269272", "RefComponent": "CA-UI2-INT-BE", "RefTitle": "\"Reference Lost\" Error for FLP Tiles after applying new UI component version", "RefUrl": "/notes/2269272"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2387541", "RefComponent": "XX-SER-REL", "RefTitle": "Role UIAPFI70 not available in LPD_CUST after UIAPFI70 upgrade to sFIN 3.0 - S/4HANA Finance 1605", "RefUrl": "/notes/2387541 "}, {"RefNumber": "2062964", "RefComponent": "FI-FIO-GL", "RefTitle": "Release Note for Fiori app Manage G/L Documents / Manage Journal Entries", "RefUrl": "/notes/2062964 "}, {"RefNumber": "2255079", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information: SAP Fiori for SAP S/4HANA Finance 1605", "RefUrl": "/notes/2255079 "}, {"RefNumber": "2288828", "RefComponent": "XX-SER-REL", "RefTitle": "S4TWL - Fiori Applications for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/2288828 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "UIAPFI70", "From": "300", "To": "300", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "UIAPFI70 300", "SupportPackage": "SAPK-30001INUIAPFI70", "URL": "/supportpackage/SAPK-30001INUIAPFI70"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}