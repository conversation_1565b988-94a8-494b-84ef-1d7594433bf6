SAP Note 2668085 pertains to the Aerospace and Defense (A&D) industry solution within SAP S/4HANA 1809, detailing specific restrictions that users in this sector may encounter. Here’s a summary of the key points from this note:

1. Product Designer Workbench (PDN) has been replaced by Product Structure Management (PSM). To access A&D functions that were previously part of the iPPE within PDN, users should use transaction code PPE, as these functions are not included in PSM.

2. Transaction MB11 for material document posting is no longer available in S/4 HANA and has been replaced by transaction MIGO. However, MIGO does not support movement types MT711B and MT712B for Customer Stock load. Instead, movement types MT501B and MT502B should be used. Nevertheless, MT711B and MT712B can still be used in the background for inventory adjustments via transaction code LI21.

3. There are several restrictions specific to Grouping, Pegging, and Distribution (GPD):
   - Valuated GPD is not usable.
   - Delivery costs are not supported.
   - Only certain requirements (from network activities, production orders, sales orders, and planned independent requirements) are supported.
   - By-products and negative stock quantities are not allowed.
   - Model-Unit type of Parameter Effectivity is not supported; alternative planning strategies are recommended.
   - Serial numbers in stock and goods movements are not considered.
   - GPD is not aware of MRP Areas and treats the entire plant as one MRP Area.
   - The Transfer Borrow Loan Payback (TBLP) process is unsupported.
   - Production Order Split functionality is unavailable.
   - PP/DS (Production Planning/Detailed Scheduling) does not integrate with GPD in S/4 HANA.

4. Regarding MRO Subcontracting, there are limitations for users who rely on Extended Warehouse Management (EWM):
   - No support for Aerospace & Defense MRO Subcontracting with EWM users in SAP S/4HANA.
   - Subcontracting orders do not create inbound delivery for the material number on the purchase order item, which EWM requires as an interface.
   - It is recommended to use the core Subcontracting process with the ME2O transaction for monitoring subcontracting stock for suppliers when using EWM, rather than using transaction code ADSUBCON for creating subcontracting orders.

Please note that this SAP Note can be subject to changes. Users impacted by these restrictions should regularly check the note for any updates or workarounds provided by SAP.