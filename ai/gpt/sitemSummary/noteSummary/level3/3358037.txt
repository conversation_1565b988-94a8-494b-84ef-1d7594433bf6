The SAP Note 3358037 addresses an issue that occurs when migrating Accounts Payable (A/P) or Accounts Receivable (A/R) open items into SAP S/4HANA (versions 2020, 2021, 2022, or 2023) using the SAP S/4HANA Migration Cockpit. When a value for the field "Partner Bank Type" is provided during the migration process, users may encounter an error message stating "Vendor/Customer has no bank details with indicator xxxx", even though the bank details are present in the associated business partner master data. This error is misleading because it suggests missing bank details, but manual posting does not show this problem.

This issue happens because the conversion rule for the Partner Bank Type field is using a function "CONVERSION_EXIT_ALPHA_INPUT" that incorrectly formats the value with leading zeros, changing an input like '01' to '0001'.

The solution provided in the note outlines specific Transport-Based Correction Instructions (TCIs) that should be implemented for different SAP S/4HANA releases and support packages:

- For release 2020 (SP00-SP06), implement TCI Note 3364988.
- For release 2021 (SP00-SP04), implement TCI Note 3364959.
- For release 2022 (SP00-SP02), implement TCI Note 3365022.
- For release 2023 (SP00), implement TCI Note 3398244.
- For release 2023 (SP01 and above), the issue should no longer occur.

Alternatively, users can manually update their Z project to exclude the "CONVERSION_EXIT_ALPHA_INPUT" from the conversion rule CVT_BVTYP_BP through the Legacy Transfer Migration Object Modeler (LTMOM).