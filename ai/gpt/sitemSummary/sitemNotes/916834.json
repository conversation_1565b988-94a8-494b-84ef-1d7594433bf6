{"Request": {"Number": "916834", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 312, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000916834?language=E&token=96EA61A075B08B17370298D1CA515880"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000916834", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000916834/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "916834"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "24.06.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-ADDON"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Add-On Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Add-On Components", "value": "BC-UPG-ADDON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-ADDON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "916834 - BI_CONT 7.03: Installation and upgrade information"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains information about <B>Business Intelligence Content 7.03</B> in relation to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add-On installation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Basis (BW) upgrade to SAP_BW 7.00<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Add-On Support Packages<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />SAINT, installation, upgrade, BI_CONT, add-on, AOI, AOX<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You want to install Business Intelligence Content (BI_CONT 7.03).<br />You want to carry out a delta upgrade from BI_CONT 7.02 to BI_CONT 7.03.<br />You want to upgrade to SAP_BW 7.00.<br />You must import add-on Support Packages for BI_CONT 7.03.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><B>This note is updated on a regular basis. You should therefore obtain the latest version before you start the upgrade.</B><br /><br /></p> <b>Contents</b><br /> <OL>1. Change history</OL> <OL>2. Important general information</OL> <OL>3. Installation, delta upgrade</OL> <OL>4. BW upgrade</OL> <OL>5. Postprocessing the BI_CONT 703 installation/upgrade</OL> <p><br /></p> <b>Contents</b><br /> <OL>1. Change history<br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Date</TH><TH ALIGN=LEFT> Section</TH><TH ALIGN=LEFT> Short description</TH></TR> <TR><TD>14.09.07</TD><TD> 4b</TD><TD> Upgrade package from SAP Service Marketplace</TD></TR> </TABLE></OL> <p></p> <OL>2. Important general information</OL> <OL><OL>a) qRFC version</OL></OL> <UL><UL><LI>Note that the qRFC version must be 45 or higher (see Note 0498484).</LI></UL></UL> <OL><OL>b) Updates</OL></OL> <UL><UL><LI>Process your V3 update entries before you carry out the upgrade. Otherwise, there is a risk that you may no longer be able to update entries if changes are introduced into the interface structures of the V3 update modules by the patch or upgrade (see Note 328181).</LI></UL></UL> <UL><UL><LI>Before the upgrade, process your entries in the extraction queues. Otherwise, there is a risk that you may no longer be able to update these entries if changes to the interface structures of the qRFC function modules are introduced by the patch or the upgrade (see Note 328181).</LI></UL></UL> <UL><UL><LI>Before the upgrade, delete your entries in the reconstruction tables for the logistics extraction applications. Otherwise, there is a risk that you may no longer be able to use these entries if changes to the extraction structures are introduced by the patch or the upgrade (see Note 328181).</LI></UL></UL> <OL>3. Installation, delta upgrade</OL> <OL><OL>a) Prerequisite</OL></OL> <UL><UL><LI>Import the latest SPAM/SAINT update.</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Refer to the following notes before you start the installation:</TH></TR> <TR><TD>Add-ons: General conditions </TD><TD> &#x00A0;&#x00A0;70228</TD></TR> <TR><TD>Note about the release strategy</TD><TD> 153967</TD></TR> <TR><TD>Known problems with transaction SAINT</TD><TD> 179116</TD></TR> </TABLE></UL></UL> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Check whether the following prerequisites are met in your system:</TH></TR> <TR><TD>SAP_BASIS</TD><TD> 700</TD><TD> Support Package 06</TD></TR> <TR><TD>SAP_ABA</TD><TD> 700</TD><TD> Support Package 06</TD></TR> <TR><TD>SAP_BW</TD><TD> 700</TD><TD> Support Package 06</TD></TR> <TR><TD>PI_BASIS</TD><TD> 2005_1_700</TD><TD> Support Package 06</TD></TR> <TR><TD> or at least</TD><TD> 2006_1_700</TD></TR> </TABLE> <p></p> <UL><UL><LI>For the delta upgrade, you must have already installed BI_CONT 702 (additional Support Packages are not absolutely necessary).<br /><br />-----------------------------------------------------------</LI></UL></UL> <p></p> <OL><OL>b) Preparations</OL></OL> <UL><UL><LI>Download the installation or upgrade package from SAP Service Marketplace (quick link /INSTALLATIONS) into a temporary directory, or install the DVD&#x00A0;&#x00A0;with material number: <B>51031606</B>.<br />Installation:  &lt;SAR archive&gt; = KIBIIIH.SAR, &lt;TYPE&gt; = INST<br />Delta upgrade: &lt;SAR archive&gt; = KIBIIIC.SAR, &lt;TYPE&gt; = DUPG</LI></UL></UL> <UL><UL><LI>Log on as one of the following users:<br />&lt;sid&gt;adm&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on UNIX,<br />&lt;SID&gt;OFR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on AS/400,<br />&lt;SID&gt;adm&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on Windows NT</LI></UL></UL> <UL><UL><LI>Switch to the transport directory of your SAP System. You can display the transport directory by calling transaction AL11 and selecting the DIR_TRANS parameter.</LI></UL></UL> <UL><UL><LI>Unpack the SAR archive &lt;SAR archive&gt; from the subdirectory &lt;TYPE&gt; using the following statement:<br />SAPCAR -xvf /&lt;CD_DIR&gt;/INST/DATA/&lt;TYPE&gt;/&lt;SAR archive&gt; &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; on UNIX,<br />SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/INST/DATA/&lt;TYPE&gt;/&lt;SAR archive&gt;'&#x00A0;&#x00A0;&#x00A0;&#x00A0;on AS/400,<br />SAPCAR -xvf &lt;CD_DRIVE&gt;:\\INST\\DATA\\&lt;TYPE&gt;\\&lt;SAR archive&gt; &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; on Windows NT.</LI></UL></UL> <UL><UL><LI>You must now be able to find the following file in the /EPS/in directory:<br />CSN0120061532_0023088.PAT (Installation)<br />CSN0120061532_0023081.PAT (Delta-Upgrade)</LI></UL></UL> <OL><OL>c) Execution</OL></OL> <UL><UL><LI>Log onto client 000 as a user with SAP_ALL authorization. Do NOT use SAP* or DDIC.</LI></UL></UL> <UL><UL><LI>Call transaction SAINT and choose 'Load'. After the system displays the list of loaded packages, choose F3 or Back to return to the initial screen of transaction SAINT.</LI></UL></UL> <UL><UL><LI>Installation only: If you want to use the option of an import with several background processes, proceed as described in Note 705192.&#x00A0;&#x00A0;The optimum number of parallel processes in this case is four.</LI></UL></UL> <UL><UL><LI>Start the installation or delta upgrade:<br />Call transaction SAINT and select the BI_CONT 703 add-on. Then choose 'Continue'. If your system meets all the prerequisites for importing the add-on, the relevant queue is displayed. The queue consists of the package and may also contain Support Packages and Add-On Packages. Include all available Add-On Support Packages in the installation or delta upgrade (section 4). Choose 'Continue' to start the procedure. To obtain more information, call transaction SAINT and choose the relevant button in the application toolbar.</LI></UL></UL> <UL><UL><LI>Password for installation:<br /><B>5E7A3DAC4C </B>(SAPKIBIIIH)</LI></UL></UL> <UL><UL><LI>Password for delta upgrade:<br /><B>5E7A3DAC47 </B>(SAPKIBIIIC)<br /></LI></UL></UL> <OL>4. BW upgrade</OL> <OL><OL>a) Prerequisite</OL></OL> <UL><UL><LI>Import the latest SPAM/SAINT update.</LI></UL></UL> <UL><UL><LI>The upgrades of the following BW releases are supported:<br />BW 20B, 21C, 30B, 310 (BI_CONT 330), 350 (BI_CONT 353)</LI></UL></UL> <OL><OL>b) Preparation</OL></OL> <UL><UL><LI>Download the upgrade package from SAP Service Marketplace (quicklink INSTALLATIONS) to a temporary directory and unpack the package to the directory &lt;DIR_EPS_ROOT&gt;/in.<br />Do not use the packed SAR archive (that contains the file CSN0120061532_0023087.PAT) from the CD that has the material number: <B>51031606</B>. However, you should use the file CSR0120031469_0023365.PAT from SAP Service Marketplace.</LI></UL></UL> <OL><OL>c) Additional information about PREPARE</OL></OL> <UL><UL><LI>IS_SELECT phase: Answer the question for BI_CONT with 'Upgrade with SAINT package'.</LI></UL></UL> <UL><UL><LI>Phase PATCH_CHK: Include the available Add-On Support Packages in the upgrade. (See section 4)</LI></UL></UL> <OL><OL>d) Additional information about the upgrade</OL></OL> <UL><UL><LI>Phase ACT_700:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can ignore the following error:<br />1EEDO519X\"Table\" \"WEB_FILES\" could not be activated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After the upgrade, you must manually activate the WEB_FILES table and the relevant WEB_FILES~WEB index (transaction SE11).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This error no longer occurs when you upgrade with Support Release 1.<br /></p> <OL><OL>e) The password in the KEY_CHK phase is:</OL></OL> <UL><UL><LI><B>3740746 (for target release SAP_BW 7.00)</B></LI></UL></UL> <UL><UL><LI><B><B>4215225 (for </B></B><B>target release SAP_BW 7.01)</B></LI></UL></UL> <OL>5. Postprocessing the BI_CONT 703 installation/upgrade</OL> <UL><LI>The current support packages for BI_CONT 703 are available on SAP Service Marketplace under the quick link /patches.</LI></UL> <UL><LI>In addition to German and English, BI_CONT 703 supports the following languages: BG, CS, DA, EL, ES, FI, FR, HE, HR, HU, IT, JA, KO, NL, NO, PL, PT, RO, RU, SK, SL, SV, TR, ZF, ZH.<br />No further postprocessing is necessary for any languages that were already installed as a default language before the add-on installation. If you wish to install one of these languages at a later stage, proceed as follows: You must install the desired default language before you install the add-on language file. Perform the add-on language import in accordance with the guide \"SAP NetWeaver Application Server ABAP 7.0: Language Transport\". The language packages are packed as SAR archive <B>KIBIIIL.SAR</B> on the LANGUAGE directory on the CD that has material number <B>51031606</B>.</LI></UL> <UL><UL><LI>When you import language packages, errors may occur in the method execution (\"OBJECTS_NOT_CHARLIKE\") if the system has NW04s Support Package Level 07. The error symptom and the solution are described in Note <B>925752</B>.</LI></UL></UL> <UL><LI>If you use budget planning and the budget monitor (transaction UPARI_BUDGA) Business Content functions in the retail area, carry out the postprocessing steps described in Note 897072.</LI></UL> <p><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028323)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028323)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000916834/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000916834/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916834/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916834/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916834/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916834/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916834/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916834/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000916834/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "note_554255.CAR", "FileSize": "6", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000061842006&iv_version=0015&iv_guid=D6C8D7DF1F20F84C9F194883A5629F77"}, {"FileName": "R3ADDONPAR.ZIP", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000061842006&iv_version=0015&iv_guid=9B7DD64EBB9C3F48A4B98F729BD0F278"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "955647", "RefComponent": "BC-UPG-ADDON", "RefTitle": "BI_CONT 703:Information on Add-On Support Packages", "RefUrl": "/notes/955647"}, {"RefNumber": "954973", "RefComponent": "SRM-EBP", "RefTitle": "SRM 5.0 SP-Stack 05 (07/2006) (SAPKIBKT05): Release/Info Not", "RefUrl": "/notes/954973"}, {"RefNumber": "929197", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/929197"}, {"RefNumber": "925752", "RefComponent": "BW-SYS", "RefTitle": "Termination after import of text parts (LANG)", "RefUrl": "/notes/925752"}, {"RefNumber": "921995", "RefComponent": "IS-DFS", "RefTitle": "BI Content for DFPS", "RefUrl": "/notes/921995"}, {"RefNumber": "855383", "RefComponent": "FIN-SEM", "RefTitle": "Installation of SAP SEM Release 6.0", "RefUrl": "/notes/855383"}, {"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967"}, {"RefNumber": "1293745", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NetWeaver 7.0 EHP 1 SR1 (ABAP)", "RefUrl": "/notes/1293745"}, {"RefNumber": "1172899", "RefComponent": "BC-UPG-ADDON", "RefTitle": "BI_CONT 7.04: Information about installation and upgrade", "RefUrl": "/notes/1172899"}, {"RefNumber": "1000822", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: SAP Notes for the add-ons BI_CONT and BI_CONT_XT", "RefUrl": "/notes/1000822"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "153967", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content Release Strategy", "RefUrl": "/notes/153967 "}, {"RefNumber": "1000822", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Overview: SAP Notes for the add-ons BI_CONT and BI_CONT_XT", "RefUrl": "/notes/1000822 "}, {"RefNumber": "955647", "RefComponent": "BC-UPG-ADDON", "RefTitle": "BI_CONT 703:Information on Add-On Support Packages", "RefUrl": "/notes/955647 "}, {"RefNumber": "1293745", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NetWeaver 7.0 EHP 1 SR1 (ABAP)", "RefUrl": "/notes/1293745 "}, {"RefNumber": "1172899", "RefComponent": "BC-UPG-ADDON", "RefTitle": "BI_CONT 7.04: Information about installation and upgrade", "RefUrl": "/notes/1172899 "}, {"RefNumber": "921995", "RefComponent": "IS-DFS", "RefTitle": "BI Content for DFPS", "RefUrl": "/notes/921995 "}, {"RefNumber": "855383", "RefComponent": "FIN-SEM", "RefTitle": "Installation of SAP SEM Release 6.0", "RefUrl": "/notes/855383 "}, {"RefNumber": "954973", "RefComponent": "SRM-EBP", "RefTitle": "SRM 5.0 SP-Stack 05 (07/2006) (SAPKIBKT05): Release/Info Not", "RefUrl": "/notes/954973 "}, {"RefNumber": "925752", "RefComponent": "BW-SYS", "RefTitle": "Termination after import of text parts (LANG)", "RefUrl": "/notes/925752 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "703", "To": "703", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}