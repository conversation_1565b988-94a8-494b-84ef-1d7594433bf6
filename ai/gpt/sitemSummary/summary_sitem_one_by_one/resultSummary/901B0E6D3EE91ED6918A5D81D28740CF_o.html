<h3>SI5_FIN_GL</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2332547">2332547 - 2332547 - S4TWL - Closing Cockpit with S/4HANA OP</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The functionality of the standard Closing cockpit (without license) is available, but defined as "not the target architecture" in SAP S/4HANA 1511, 1610 or subsequent releases. The Business Functions FIN_ACC_LOCAL_CLOSE, FIN_ACC_LOCAL_CLOSE_2, FIN_ACC_LOCAL_CLOSE_3 (requiring a license for SAP Financial Closing cockpit) are flagged as obsolete in S/4HANA OP. If any of these Business Functions are active:</p>
<ul>
<li>Under SAP S/4HANA 1610 OP, consider using the Add-On Financials Closing Cockpit 2.0.</li>
<li>Under SAP S/4HANA OP 2019, use the <a href="https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/8b1d76cd5e7644caa0553fcf338f3982/3fe49e8767d648a6b1b5edeec8849b8d.html" target="_blank">SAP S/4HANA Financial Closing cockpit</a>.</li>
<li>With SAP S/4HANA OP 2019 ff., consider using <a href="https://help.sap.com/docs/advanced-financial-closing?locale=en-US" target="_blank">SAP Advanced Financial Closing</a>.</li>
</ul>
<p><strong>Required and Recommended Actions:</strong></p>
<p>
<ul>
<li>Customizing / Configuration: If you want to use "SAP S/4HANA Financial Closing cockpit" then execute configuration guide steps (During conversion project, Conditional).</li>
<li>Custom Code Adaption: Mandatory action during the conversion project.</li>
<li>Data correction: If you want to use your customer-specific reports in the Closing Cockpit functionality, and if you want to use SAP Financial Closing cockpit for SAP S/4HANA, please include two function modules according to this SAP Note (Before conversion project, Conditional).</li>
<li>Technical System Configuration: If you decide for implementing closing cockpit 2.0, then please implement the Add-on mentioned in the SAP Note (During conversion project, Conditional).</li>
<li>Fiori Implementation: Implement Fiori Apps for Financial Closing Cockpit (During or after conversion project, Optional).</li>
<li>Data migration: If you switch to Financial Closing Cockpit, migrate your task list templates (During or after conversion project, Conditional).</li>
<li>User Training: Inform users about new UIs and features of SAP S/4HANA Financial Closing Cockpit (During or after conversion project, Conditional).</li>
<li>Business Decision: Evaluate SAP S/4HANA Financial Closing Cockpit and decide if to replace the classical Closing Cockpit with it (Before conversion project, Mandatory).</li>
</ul>
</p>