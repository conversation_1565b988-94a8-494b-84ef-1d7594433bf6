{"Request": {"Number": "1222993", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 582, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007140842017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001222993?language=E&token=9E557E0D271AB5E9655969D27D74422A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001222993", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001222993/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1222993"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "PY-PT-PS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Public Sector Payroll"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portugal", "value": "PY-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Public Sector Payroll", "value": "PY-PT-PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT-PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1222993 - Evaluation classes 18, 19 and 20 replaced"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />Entries in customer range of evaluation classes have been delivered by SAP.<br /><br />The evaluation classes 18, 19, and 20 are being used by SAP standard.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Evaluation Class 18; Evaluation Class 19; Evaluation Class 20; BDAP; CGA; Social Balance; Public Sector</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The evaluation classes 18, 19, and 20 belong to the customer range.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>3</td>\r\n<td>July 20, 2016</td>\r\n<td>SAR files were removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The evaluation class 18 was used by CGA Magnetic File Report (RPCCGAPT0PBS) and from now on the report is going to use the evaluation class 15 (Assignment of wage types to \"CGA Mag. File\" situations) instead.<br /><br />The evaluation class 19 was used by Public Administration Human Resources' Database (BDAP) Report (RPCBDAPT0PBS) and from now on the report is going to use a screen parameter instead of this evaluation class instead.<br /><br />The evaluation class 20 was used by Social Balance Report for Public Sector(RPCSOCPT0PBS) and from now on the report is going to use the evaluation class 14 (Payments and Allowances - \"Social Balance\") instead.<br /><br />If you have installed the support package 22 (SAPKE60022) or higher and before the installation you were using these evaluation classes, you may recover your original customizing and continue to use them. For this, you need to check and adjust the evaluation classes 18, 19, and 20 descriptions (using the table view V_T52D3) and their values (using the table view V_T52D4). Be aware that you have to check the value assigned to these evaluation classes for your wage types customizing.<br /><br /></p>\r\n<p><strong>Public Administration Human Resources' Database (BDAP) Report</strong></p>\r\n<p>In the BDAP report the evaluation class 19 was replaced by a parameter in the selection screen of the report (RPCBDAPT0PBS). The new parameter allows the selection of the wage types by a range of wage types regarding to the Service Remuneration.<br /><br /></p>\r\n<p><strong>Social Balance Report for Public Sector</strong></p>\r\n<p>The following entry was created using the table view V_T52D3:<br /><br />EvCl.- Description<br />------------------------<br />14&#160;&#160;&#160;&#160;Payments and Allowances - \"Social Balance for Public Sector\"<br /><br />The following entries were created using the table view V_T52D4:<br /><br />EvCl. - Value&#160;&#160;- Description<br />--------------------------------<br />14&#160;&#160;&#160;&#160;01&#160;&#160;&#160;&#160; Basic Remuneration / Basic Pay<br />14&#160;&#160;&#160;&#160;02&#160;&#160;&#160;&#160; Overtime<br />14&#160;&#160;&#160;&#160;03&#160;&#160;&#160;&#160; Normal Night Time Work<br />14&#160;&#160;&#160;&#160;04&#160;&#160;&#160;&#160; Weekend Work, Monday and Holidays<br />14&#160;&#160;&#160;&#160;05&#160;&#160;&#160;&#160; Permanent Available<br />14&#160;&#160;&#160;&#160;06&#160;&#160;&#160;&#160; Other Special Regimes of Work Protection<br />14&#160;&#160;&#160;&#160;07&#160;&#160;&#160;&#160; Risk<br />14&#160;&#160;&#160;&#160;08&#160;&#160;&#160;&#160; Suburbs Fixation<br />14&#160;&#160;&#160;&#160;09&#160;&#160;&#160;&#160; Shifts Work<br />14&#160;&#160;&#160;&#160;10&#160;&#160;&#160;&#160; Fault Allowance<br />14&#160;&#160;&#160;&#160;11&#160;&#160;&#160;&#160; Meeting Participation<br />14&#160;&#160;&#160;&#160;12&#160;&#160;&#160;&#160; Per Diems (Travel Allowance)<br />14&#160;&#160;&#160;&#160;13&#160;&#160;&#160;&#160; City Transference<br />14&#160;&#160;&#160;&#160;14&#160;&#160;&#160;&#160; Representation<br />14&#160;&#160;&#160;&#160;15&#160;&#160;&#160;&#160; Sectretariat<br />14&#160;&#160;&#160;&#160;16&#160;&#160;&#160;&#160; Other Payments<br />14&#160;&#160;&#160;&#160;30&#160;&#160;&#160;&#160; Family Allowance<br />14&#160;&#160;&#160;&#160;31&#160;&#160;&#160;&#160; Wedding Allowance<br />14&#160;&#160;&#160;&#160;32&#160;&#160;&#160;&#160; Born Allowance<br />14&#160;&#160;&#160;&#160;33&#160;&#160;&#160;&#160; Brest Feeding Allowance<br />14&#160;&#160;&#160;&#160;34&#160;&#160;&#160;&#160; Child and Youngman handicaps Allowance<br />14&#160;&#160;&#160;&#160;35&#160;&#160;&#160;&#160; Special Education Allowance<br />14&#160;&#160;&#160;&#160;36&#160;&#160;&#160;&#160; Long Life Allowance<br />14&#160;&#160;&#160;&#160;37&#160;&#160;&#160;&#160; Funeral Allowance<br />14&#160;&#160;&#160;&#160;38&#160;&#160;&#160;&#160; Meal Allowance<br />14&#160;&#160;&#160;&#160;39&#160;&#160;&#160;&#160; Complementary Social Action Allowance<br />14&#160;&#160;&#160;&#160;40&#160;&#160;&#160;&#160; Death Allowance<br />14&#160;&#160;&#160;&#160;41&#160;&#160;&#160;&#160; Other Social Allowances<br /><br /></p>\r\n<p><strong>CGA Magnetic File Report</strong></p>\r\n<p>The following entry was updated using the table view V_T52D3:<br /><br />EvCl.- Description<br />------------------------<br />15&#160;&#160;&#160;&#160;Assignment of wage types to \"CGA Mag. File\" situations<br /><br /><br />The following entries were created/updated using the table view V_T52D4:<br /><br />EvCl. - Value&#160;&#160;- Description<br />--------------------------------<br />15&#160;&#160;&#160;&#160;01&#160;&#160;&#160;&#160; Base for remunaration deductions<br />15&#160;&#160;&#160;&#160;02&#160;&#160;&#160;&#160; Obsolete (do not use)<br />15&#160;&#160;&#160;&#160;03&#160;&#160;&#160;&#160; Obsolete (do not use)<br />15&#160;&#160;&#160;&#160;08&#160;&#160;&#160;&#160; Base for seniority days deductions<br />15&#160;&#160;&#160;&#160;10&#160;&#160;&#160;&#160; Base for permanent renumeration deductions<br />15&#160;&#160;&#160;&#160;20&#160;&#160;&#160;&#160; Base for variable renumeration deductions<br />15&#160;&#160;&#160;&#160;30&#160;&#160;&#160;&#160; Base for holiday allowance<br />15&#160;&#160;&#160;&#160;32&#160;&#160;&#160;&#160; Base for Christmas allowance<br />15&#160;&#160;&#160;&#160;81&#160;&#160;&#160;&#160; First time count for retirement debt<br />15&#160;&#160;&#160;&#160;82&#160;&#160;&#160;&#160; Second time count for retirement debt<br />15&#160;&#160;&#160;&#160;83&#160;&#160;&#160;&#160; Third time count for retirement debt<br />15&#160;&#160;&#160;&#160;84&#160;&#160;&#160;&#160; Fourth time count for retirement debt<br />15&#160;&#160;&#160;&#160;85&#160;&#160;&#160;&#160; Fifth time count for retirement debt<br />15&#160;&#160;&#160;&#160;86&#160;&#160;&#160;&#160; Sixth time count for retirement debt<br />15&#160;&#160;&#160;&#160;87&#160;&#160;&#160;&#160; Seventh time count for retirement debt<br />15&#160;&#160;&#160;&#160;88&#160;&#160;&#160;&#160; Eighth time count for retirement debt<br />15&#160;&#160;&#160;&#160;89&#160;&#160;&#160;&#160; Ninth time count for retirement debt<br />15&#160;&#160;&#160;&#160;90&#160;&#160;&#160;&#160; Regimen of survival debt<br /><br />The following wage types were customized to use the evaluation class 15:<br />&#160;&#160; /174 - customized with value 32<br />&#160;&#160; /175 - customized with value 30<br />&#160;&#160; M70U - customized with value 01<br />&#160;&#160; SE11 - customized with value 08 <br /><br /><br />The correction described in this note will be included in an HR Support Package, as indicated in item \"Reference to Support Packages\".<br /><br />The support package includes:</p>\r\n<ul>\r\n<li>change in the includes RPCSOCPT5PBS, RPCBDAPT0PBS, RPCBDAPTDPBS, RPCBDAPTFPBS, and RPCBDAPTSPBS.</li>\r\n</ul>\r\n<ul>\r\n<li>in the class CL_HR_PT_PBS_BDAP (BDAP report) changes were made in the methods: CONSTRUCTOR, FILL_BLOCK_J4, LOAD_WT_EVAL_CLASS, READ_T001P, READ_T503, READ_T5P0P, READ_T5PFD, READ_T5PPBS8A, READ_T5PPBS9L, READ_T5PPBS9N, READ_T5PPBS9O, READ_T5PPBS9P, READ_T5PPBSAB, and READ_T5PPBSRM in order to improve performance.</li>\r\n</ul>\r\n<ul>\r\n<li>new text elements in the report RPCBDAPT0PBS:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Selection text: PWT_SERR - Wage Types</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Text Symbols: 005 - Services Remunerations</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>changes on the following table view entries:</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Table view V_512W_D<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Table T512W<br /> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Keys: <br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;000 19 /174 99991231<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;000 19 /175 99991231<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;000 19 M70U 99991231<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;000 19 SE11 99991231<br /><br />An Advanced Delivery including changes done in the Data Dictionary and ABAP code is available in the attached files according to the following list (\"xxxxxx\" means numbers):</p>\r\n<ul>\r\n<li>L7DKxxxxxx_600_SYST.CAR - Release 600 (ERP 2005)</li>\r\n</ul>\r\n<p><br />An Advanced Delivery including Customizing changes is available in the attached files according to the following list(\"xxxxxx\" means numbers):</p>\r\n<ul>\r\n<li>L7DKxxxxxx_600_CUST.CAR - Release 600 (ERP 2005)</li>\r\n</ul>\r\n<p><br />For more details about Advance Delivery installation procedure please read the notes listed in \"Related Notes\".<br /><br /></p>\r\n<p><strong>IMPORTANT:</strong></p>\r\n<p>Be aware of an Advance Delivery delivers the last version of the object, it means that if you do not have the last HR Support Package installed in you system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the changes manually according to the Correction Instructions available in this note.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PY-PT (Portugal)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I811676)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I827735)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001222993/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001222993/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001222993/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001222993/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001222993/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001222993/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001222993/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001222993/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001222993/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "1396180", "RefComponent": "PY-PT-PS", "RefTitle": "HR-PT-PS: Portuguese texts for Evaluation Classes 14 and 15", "RefUrl": "/notes/1396180"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1177281", "RefComponent": "PY-XX-BS", "RefTitle": "Evaluation classes in the customer namespace", "RefUrl": "/notes/1177281"}, {"RefNumber": "1173260", "RefComponent": "PY-PT-PS", "RefTitle": "Legal Change for CGA Magnetic File report", "RefUrl": "/notes/1173260"}, {"RefNumber": "1078752", "RefComponent": "PY-PT-PS", "RefTitle": "HCM PT PS: Social Balance", "RefUrl": "/notes/1078752"}, {"RefNumber": "1073524", "RefComponent": "PY-PT-PS", "RefTitle": "HCM PT PS: BDAP - Public Admin. Human Resources Data Base", "RefUrl": "/notes/1073524"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1396180", "RefComponent": "PY-PT-PS", "RefTitle": "HR-PT-PS: Portuguese texts for Evaluation Classes 14 and 15", "RefUrl": "/notes/1396180 "}, {"RefNumber": "1173260", "RefComponent": "PY-PT-PS", "RefTitle": "Legal Change for CGA Magnetic File report", "RefUrl": "/notes/1173260 "}, {"RefNumber": "1177281", "RefComponent": "PY-XX-BS", "RefTitle": "Evaluation classes in the customer namespace", "RefUrl": "/notes/1177281 "}, {"RefNumber": "1073524", "RefComponent": "PY-PT-PS", "RefTitle": "HCM PT PS: BDAP - Public Admin. Human Resources Data Base", "RefUrl": "/notes/1073524 "}, {"RefNumber": "1078752", "RefComponent": "PY-PT-PS", "RefTitle": "HCM PT PS: Social Balance", "RefUrl": "/notes/1078752 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HRCPT 600", "SupportPackage": "SAPK-60035INSAPHRCPT", "URL": "/supportpackage/SAPK-60035INSAPHRCPT"}, {"SoftwareComponentVersion": "SAP_HRCPT 600", "SupportPackage": "SAPK-60036INSAPHRCPT", "URL": "/supportpackage/SAPK-60036INSAPHRCPT"}, {"SoftwareComponentVersion": "SAP_HRCPT 604", "SupportPackage": "SAPK-60402INSAPHRCPT", "URL": "/supportpackage/SAPK-60402INSAPHRCPT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HRCPT", "NumberOfCorrin": 2, "URL": "/corrins/0001222993/6497"}, {"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 1, "URL": "/corrins/0001222993/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1159263 ", "URL": "/notes/1159263 ", "Title": "Splitting TemSe report for public and private sector", "Component": "PY-PT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}