The SAP Note 2270408 addresses the issue with Activity-Based Costing (CO-OM-ABC) during the system conversion to SAP S/4HANA, specifically concerning the use of delta versions, which are not supported in S/4HANA.

**Symptom:**
During a system conversion, the error FI_GL_08 occurs if an Activity-Based Costing (ABC) delta version is found, indicating it is not usable in SAP S/4HANA.

**Other Terms:**
The note refers to Activity Based Costing as CO-OM-ABC.

**Reason and Prerequisites:**
The task involves checking whether any controlling area has ABC activated, especially if it's used for parallel calculation, which uses delta versions and is not supported in S/4HANA. The note provides directions for using transactions SPRO, SE16, maintain controlling area settings, and check whether delta versions have been created or not.

**Rating:**
If "Component Active for Parallel Calculation" or its integrated counterpart is set, or if you have a CO version using a Reference Version for ABC, the note is relevant.

**Solution:**
Since delta versions are not available in SAP S/4HANA, you must either:
- Work without delta versions post-conversion and ignore the check.
- Delete the data of delta versions in the ECC system prior to conversion as per SAP Note 3126356 to avoid errors.
- Or, skip the error message in conversion checks and opt not to map delta versions to a ledger for migration.

**Business Process related information:**
- Allocating costs in parallel ABC is no longer supported in S/4HANA.
- Certain transactions have been replaced with new ones; for example, CPV5 with KSV5, signifying the elimination of the need for separate transactions.

**Required and Recommended Actions:**
- Activate Operational Activity Based Costing.
- Adjust assigned transactions in roles if necessary.

**Related Notes:**
The note points to pre-checks for conversion regarding controlling area settings and version settings to determine the active type of ABC. 

In summary, SAP Note 2270408 guides users on how to address the issue of unsupported ABC delta versions during the conversion to SAP S/4HANA, instructing them to check configurations and providing recommendations on transitioning away from the unsupported feature.