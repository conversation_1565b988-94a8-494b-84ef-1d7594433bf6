SAP Note 87861 addresses the requirement for logging changes made to infotype records related to personnel planning within SAP systems. These infotypes refer to tables named HRPnnnn, HRTnnnn, and HRPADnn.

The objective is to ensure that any changes to these records are captured and can be tracked. Unfortunately, in releases prior to SAP Web Application Server 6.40 or ERP 1.0, there is no inbuilt logging function for these infotypes, unlike HR master data infotypes which are logged in table T585A.

As a workaround, users must manually activate the general log function for the relevant personnel planning infotype tables by setting the "Log data changes" indicator in Transaction SE13 (ABAP/4 Dictionary: Technical settings). For changes to be logged, it is also necessary for the system profile parameter rec/client to be set appropriately. Simply enabling the indicator without the proper profile setting will not activate the logging function.

Once logged, the database changes can be evaluated using the report RSTBPROT or RSVTPROT (for systems on Release 4.5A or later). When running the report, users need to set the "Tables" parameter to an interval that includes all the relevant personnel planning infotype tables (e.g., 'HRP1000' to 'HRP9999', 'HRT1000' to 'HRT9999', 'HRPAD*').