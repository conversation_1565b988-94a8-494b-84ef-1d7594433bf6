SAP Note 1013140 provides information on general usage and limitations of the SAP Business Explorer (BEx) frontend for BI 7.x and BW 3.5 that comes with SAP GUI 7.10. 

Key points from the note:

- The Business Explorer frontend is available in two versions: 7.x and 3.5.
- With version 7.x, BEx tools have been made standalone, meaning they can be installed independently without needing the SAP GUI.
- Version 3.5 is still offered as an add-on to SAP GUI 7.10.
- After installation, the Business Explorer menu will include the Analyzer, Query Designer, and Report Designer. The WebApplicationDesigner (WAD) is accessible only if SAP GUI is installed.
- There are several front-end patches (FEPs) that provide specific functionalities and support, including:
  - With FEP 2, Report Designer works except for context menu functionality on rows and columns.
  - BEx Analyzer supports Windows Vista starting from FEP 1, and Excel 2007 in the "old" file format (.xls) with FEP 2. Support for the new Excel 2007 formats (.xlsx, .xlsm, .xlsb) starts from FEP 300.
  - BI 7.x is operational on 64-bit systems with FEP 401.
- The BW 3.5 add-on does not support the new Excel 2007 formats; refer to note 1052761 for more information and workarounds.
- An open issue exists with the BW 3.x tools within the SAP GUI 7.10 environment, detailed in note 1039191.
- Customers can install both versions (7.x and 3.5) simultaneously using 'setupall.exe' from the CD.

Prior to installing BI 7.x, it is advised to consult with additional SAP Notes for hardware and software requirements (1013201), installation pre-requisites (1013139), and an installation guide (1013207).