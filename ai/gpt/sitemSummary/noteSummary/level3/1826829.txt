SAP Note 1826829 addresses an issue in the report RSSM_SM12_OLD where the function module ENQUE_READ is improperly called. This call to ENQUE_READ is not acceptable and has been changed into a comment within the program to prevent its execution.

The reason for this note is identified as a program error.

To resolve this issue, the note provides detailed solution instructions for different SAP NetWeaver BW (Business Warehouse) versions by recommending the import of specific Support Packages for each version as follows:

- For SAP NetWeaver BW 7.00: Import Support Package 31 (SAPKW70031) as detailed in SAP Note 1782745.
- For SAP NetWeaver BW 7.01: Import Support Package 14 (SAPKW70114) as detailed in SAP Note 1794836.
- For SAP NetWeaver BW 7.02: Import Support Package 14 (SAPKW70214) as detailed in SAP Note 1800952.
- For SAP NetWeaver BW 7.11: Import Support Package 12 (SAPKW71112) as detailed in SAP Note 1797080.
- For SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010) as detailed in SAP Note 1810084.
- For SAP NetWeaver BW 7.31: Import Support Package 08 (SAPKW73108) as detailed in SAP Note 1813987.
- For SAP NetWeaver BW 7.40: Import Support Package 03 (SAPKW74003) as detailed in SAP Note 1818593.

The note also suggests that, in urgent situations, the correction instructions can be implemented in advance as an interim solution.

Note 1826829 specifies that before performing any corrections using transaction SNOTE, the user must read SAP Note 875986, which provides important information on how to use the SNOTE transaction.

Finally, it is mentioned that the SAP Notes referenced for each Support Package may be available before the release of the respective Support Package and may be labeled as "Preliminary version" until they are finalized for customer release.