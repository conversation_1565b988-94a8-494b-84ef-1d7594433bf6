{"Request": {"Number": "2011192", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1068, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017864662017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002011192?language=E&token=7507274F18A138E79CDB46ABF5C86C83"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002011192", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002011192/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2011192"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 59}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-OCS-SPA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Support package tools for ABAP"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)", "value": "BC-UPG-OCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-OCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Support package tools for ABAP", "value": "BC-UPG-OCS-SPA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-OCS-SPA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2011192 - Uninstallation of ABAP add-ons"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note provides information about uninstalling ABAP add-ons with the SAP Add-On Installation Tool (transaction SAINT).</p>\r\n<p><strong>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</strong></p>\r\n<p><strong>Due to the rapidly increasing number of uninstallable </strong></p>\r\n<p><strong>add-ons, this list is not complete.&#x00A0;<strong><strong>Additional SAP Notes about uninstallable add-ons </strong></strong></strong></p>\r\n<p><strong><strong>are located in the reference list of this SAP Note.</strong></strong></p>\r\n<p><strong><strong><strong>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</strong> </strong></strong></p>\r\n<p><strong>A list of deletable objects is contained in the attachment \"Deletable_Objects\". The </strong></p>\r\n<p><strong>list is updated with every SPAM update. </strong></p>\r\n<p><strong>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAINT, add-on uninstallation</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>As of Version 0053 of the SAP Add-On Installation Tool, not only can you install ABAP add-ons, but you can also uninstall them again in certain circumstances.<br />The following sections provide the most important information about uninstalling ABAP add-ons as well as a list of previously available add-ons that can be uninstalled (<strong>examples</strong>).</p>\r\n<p><strong>Caution</strong>: The uninstallation of ABAP add-ons can result in unintentional data loss. Read this SAP Note thoroughly before starting an uninstallation. It contains further information and steps that might be required for the preparation and postprocessing of the uninstallation.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Technical Prerequisites:</strong></p>\r\n<ul>\r\n<li>The system is based on SAP NetWeaver Release 7.0 or above.</li>\r\n<li>You have installed at least SPAM/SAINT Version 0053.</li>\r\n<li>You use a kernel with at least Release 7.2.</li>\r\n<li>The transport tool tp&#x00A0;has at least Version 380.07.22.</li>\r\n<li>The transport tool R3trans has at least the version from AUG/06/2013.</li>\r\n<li>The last ACP of the add-on to be uninstalled has been installed in the system.</li>\r\n</ul>\r\n<p>During the uninstall process, the system checks and ensures the existence of the minimum versions of the kernel, tp, and R3trans.</p>\r\n<p><strong>Uninstall process:</strong></p>\r\n<p>When you trigger the uninstall process for an add-on with the SAP Add-On Installation Tool, the tool searches the system for content that belongs to the add-on that is to be deleted:</p>\r\n<ul style=\"list-style-type: square;\">\r\n<li>All content contained in installation packages, upgrade packages, and support packages of the add-on</li>\r\n<li>Content generated automatically by SAP Notes for the add-on</li>\r\n<li>Content created manually in development packages that belong to the add-on</li>\r\n</ul>\r\n<p>Based on this, the tool creates a list of objects that must be deleted to uninstall the add-on. The tool also checks the following:</p>\r\n<ul>\r\n<li>Are there any dependencies between the objects in the piece list and other content?</li>\r\n<li>Have the objects been modified by the customer?</li>\r\n<li>Are the objects active?</li>\r\n<li>Do the objects belong to different software components?</li>\r\n</ul>\r\n<p>If objects with errors are identified during these checks, these objects cannot be deleted until the errors have been corrected. This occurs at the end of the check phase, partly automatically and partly manually.</p>\r\n<p>When all checks have been successfully completed, the SAP Add-On Installation Tool removes the objects to be deleted from the system and changes the system status accordingly.</p>\r\n<p>The product data of the add-on can then be deleted using the CISI process as described in SAP Note 1816146.</p>\r\n<p><strong></strong> <strong>List of ABAP add-ons that can be uninstalled:</strong></p>\r\n<p><strong><em>CAUTION:</em></strong> <em>This list is not complete. Please do not make any changes or additions.&#x00A0;</em></p>\r\n<p>Since the deletion of add-ons without errors cannot be ensured only by using the functions of the SAP Add-On Installation Tool, the add-ons must fulfill the relevant prerequisites, must be explicitly earmarked for deletion, and must be flagged as deletable.</p>\r\n<p>Currently, the following add-ons can be uninstalled:</p>\r\n<ul>\r\n<li>ARIBA CLOUD INT SAP ERP 1.0 (component ARBCI1 100, ARBCI2 100, SAP Note 3243704)</li>\r\n<li>Ariba Network Integration 1.0 for SAP Business Suite (components ARBFNDI1 100, ARBFNDI2 100, ARBSRMI1 100, ARBERPI1 100, SAP Note 2067891)</li>\r\n<li>Cloud Lifecycle Management 100, SAP Note 2398413</li>\r\n<li>Concur Integration (components CTE_HCM 100, CTE_FIN 100, CTE_INV 100 , CTE_FND 100, CTE_FGM 100, SAP Note 2313330)</li>\r\n<li>Concur Integration (components CTE_HCM 10S&#x00A0;, CTE_FIN&#x00A0;10S, CTE_INV&#x00A0;10S, CTE_FND 10S, SAP Note 2407737)</li>\r\n<li>DATA XCHANGE SWISS UTILITY 1.0 (component IDXPF 604, SAP Note 2525068)</li>\r\n<li>Desktop Connection for SAP CRM (component CRMGWS 700, SAP Note 2200413)</li>\r\n<li>Duet Enterprise 2.0 (component IW_TNG 200, SAP Note 2503641)</li>\r\n<li>Flexible Benefits for Utilities UI 600 (component FB4R 600, SAP Note 2152381)</li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 10.5pt; font-family: 'Verdana',sans-serif; color: black; line-height: 107%; mso-fareast-font-family: Calibri; mso-bidi-font-family: Arial; mso-fareast-language: EN-US; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-bidi-language: AR-SA; mso-bidi-theme-font: minor-bidi;\">IDEX F SWISS ELECTRIC COMP 1.0 (component IDEXCH 604, SAP Note 2609756)</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 10.5pt; font-family: 'Verdana',sans-serif; color: black; line-height: 107%; mso-fareast-font-family: Calibri; mso-bidi-font-family: Arial; mso-fareast-language: EN-US; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-bidi-language: AR-SA; mso-bidi-theme-font: minor-bidi;\">Integration to ERP 6.0 Mobile Instance value application ERP SD 601 (component </span><span lang=\"EN-US\" style=\"font-size: 10.5pt; font-family: 'Verdana',sans-serif; color: black; line-height: 107%; mso-fareast-font-family: Calibri; mso-bidi-font-family: Arial; mso-fareast-language: EN-US; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-bidi-language: AR-SA; mso-bidi-theme-font: minor-bidi;\">MIVERPSD 601, SAP Note 2313327)</span></li>\r\n<li>RCS Asset Accounting Multiple Calendar Tool (component MTO 100, SAP Note 2132260)</li>\r\n<li>Regulatory reporting 3.0 by iBS (component RRA 300_BA70, SAP Note 2244901)</li>\r\n<li>SAP Access Approver 2.0.0 (component LWMGRC01 100, SAP Note 2662186)</li>\r\n<li>SAP ACCESS CONTROL 10.1/SAP Process CONTROL 10.1 (component GRCPIERP&#x00A0;V1100_700, SAP Note 2456300, components GRCPINW&#x00A0;V1100_731 &amp; 710 &amp; 700, SAP Note 2453593, components GRCFND_A V800 &amp; V1100, SAP Note 2463636)</li>\r\n<li>SAP ACCESS CONTROL 10.0/SAP Process CONTROL 10.0 (component GRCPIERP V1000_700, SAP Note 2456300, components GRCPINW V1000_700 &amp; V1000_731, SAP Note 2453593, component GRCFND_A V800, SAP Note 2463636)</li>\r\n<li>SAP Add-On Assembly Kit 4.0 (component AOFTOOLS 400_*, SAP Note 921103)</li>\r\n<li>SAP Add-On Assembly Kit 5.0 (component AOFTOOLS 500_7*, SAP Note 2179441)</li>\r\n<li>SAP Advanced Track and Trace for Pharmaceuticals 1.0&#x00A0;(component STTP 100, SAP Note 2441827)</li>\r\n<li>SAP AR Warehouse Picker 1.0, QR code generator (component NWMQC01 100, SAP Note 2202923)</li>\r\n<li>SAP Citizen Connect 1.0.0 (component LWMCR002 700, SAP Note 2206380)</li>\r\n<li>SAP Cloud for Customer 1208 integration with SAP ERP (component CODEXTCT 600, SAP Note 2373504)</li>\r\n<li>SAP Cloud for Customer 4.0, integration with SAP ERP (component CODEXTCT 600, SAP Note 2373504 and NWSEXTFW 600, SAP Note 2228009)</li>\r\n<li>SAP Cloud for Travel and Expense integration 4.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\r\n<li>SAP Cloud for Travel and Expense integration 4.0 (components TEMODFI 600, OTM_EXTR 600, ODTHCMER 600, ODTGEN 600, ODTFINCO 600, ODTFINCC 600, and DCFLPROC 600, SAP Note 2227939)</li>\r\n<li>SAP Cloud for Travel and Expense integration 5.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\r\n<li>SAP Cloud for Travel and Expense integration 5.0 (components TEMODFI 600, OTM_EXTR 600, ODTHCMER 600, ODTGEN 600, ODTFINCO 600, ODTFINCC 600, and DCFLPROC 600, SAP Note 2227939)</li>\r\n<li>SAP Configure, Price, and Quote for Solution Sales Configuration 2.0 (component SLCE 607, SAP Note 2437183)</li>\r\n<li>SAP Cross-Channel Order Management for Retail 2.0 (component WOM 200, SAP Note 2529018)</li>\r\n<li>SAP Customer Financial Fact Sheet 2.0.0 (component LWMFI001 600, SAP Note 2373183)</li>\r\n<li>SAP Deduction Management Component 6.0 (Component GSCDMC 600, SAP Note 2189971)</li>\r\n<li>SAP DMP 2.0 (component FMFMS 604, SAP Note 2319813)</li>\r\n<li>SAP DMP 2.0 (component PSFMS 600, SAP Note 2319803)</li>\r\n<li>SAP EHP1 for SAP NetWeaver Mobile 7.3 (component SUPDOE 731, SAP Note 2420945)</li>\r\n<li>SAP EHP 4 for SAP ERP 6.0, localization for Russia for public sector accounting (components GSEAFS 100 &amp; 604, SAP Note 2410217)</li>\r\n<li>SAP EHS Management, Add-In for Genifix 2.2 (component TDAGGF 220_600, SAP Note 2307907)</li>\r\n<li>SAP Employee Lookup 2.0.0 (component MIVHREMP 601, SAP Note 2356000)</li>\r\n<li>SAP ERP Add-On for MULTICHANNEL UTILITIES/PS 1.0 (component UMCERP01 604, SAP Note 2461782)</li>\r\n<li>SAP ERP add-on for shop floor dispatching and monitoring tool 1.0 (component SFDM_ABAP 100, SAP Note 2261544)</li>\r\n<li>SAP ERP Order Status 600&#x00A0;(component LWMSD001&#x00A0;600, SAP Note 2333985)</li>\r\n<li>SAP ERP Quality Issue 1.0.0 (component&#x00A0;LWMQAMMI 600, SAP Note 2318477)</li>\r\n<li>SAP EWM 9.3 (components SCM_EXT 713 &amp; SCMB_EXT 713, SAP Note 2403053)</li>\r\n<li>SAP EWM 9.3/9.4 (component SCM_BASIS 713/714, SAP Note 2403053)</li>\r\n<li>SAP EWM 9.4 (components SCMEWMUI 940 &amp; SCMEWM 940, SAP Note 2360806)</li>\r\n<li>SAP Extended Warehouse Management 9.3 (components SCMEWM UI 930 &amp; SCMEWM 930, SAP Note 2360806, component SCM_BASIS 713 &amp; 714,&#x00A0;SAP Note 2403053, component SCM_EXT 713, SAP Note 2403053, component QIE 200, SAP Note 2403694)</li>\r\n<li>SAP Fiori App Implementation Foundation 1.0 (component SAPUIFT&#x00A0;100, SAP Note 2622613)</li>\r\n<li>SAP Fiori 1.0 for SAP Demand Signal Management (component UIDSM001 100, SAP Note 2409404)</li>\r\n<li>SAP Fiori Front-End Server 2.0 (component UIBAS001 100, SAP Note 2256000)</li>\r\n<li>SAP Fiori 1.0 for SAP Business Suite foundation component (component UIFND001 100, SAP Note 2217323)</li>\r\n<li>SAP Fiori 1.0 for SAP Commercial Project Management (component CPD001 100, SAP Note 2217279)</li>\r\n<li>SAP Fiori 1.0 for SAP Customer Activity Repository retail applications bundle (components UIRAP001 100 and UISCAR01, SAP Note 2217230)</li>\r\n<li>SAP Fiori 1.0 for SAP Demand Signal Management (components UIDDF001 100 and UIDDF0011 100, SAP Note 2409404)</li>\r\n<li>SAP Fiori 1.0 for SAP EHS Management (component UIEHSM01 100, SAP Note 2203691)</li>\r\n<li>SAP Fiori 1.0 for SAP ERP HCM (component UIHR001 100, SAP Note 2167372)</li>\r\n<li>SAP Fiori 1.0 for SAP Event Management (component UIEM001 100, SAP Note 2209418)</li>\r\n<li>SAP Fiori 1.0 for SAP HANA Live for SAP Advanced Planning and Optimization (component UIHSCM01 100, SAP Note 2176724)</li>\r\n<li>SAP Fiori 1.0 for SAP HANA Live for SAP solutions for GRC (component UIHGRC01 100, SAP Note 2200172)</li>\r\n<li>SAP Fiori 1.0 for SAP hybris Marketing (component UICUAN 100, SAP Note 2200656)</li>\r\n<li>SAP Fiori 1.0 for SAP Information Lifecycle Management (component UIILM001 100, SAP Note 2195035)</li>\r\n<li>SAP Fiori 1.0 for SAP Master Data Governance (component UIMDG001 100, SAP Note 2209288)</li>\r\n<li>SAP Fiori 1.0 for SAP Portfolio and Project Management (component UIPPM001 100, SAP Note 2176725)</li>\r\n<li>SAP Fiori 1.0 for SAP solutions for GRC (component UIGRC001 100, SAP Note 2176696)</li>\r\n<li>SAP Fiori 1.0 for SAP SRM (component UISRM200 100, SAP Note 2176792)</li>\r\n<li>SAP Fiori 1.0 for the SAP Simple Finance add-on for SAP Business Suite powered by SAP HANA (component UIFSCM70 100, SAP Note 2144806, und component UIAPFI70 100, SAP Note 2144806)</li>\r\n<li>SAP Fiori 2.0 for SAP Customer Activity Repository retail applications bundle (component UICAR001 100, SAP Note 2433703)</li>\r\n<li>SAP Fiori for request approvals 1.0 (component UIX01CA1 100, SAP Note 2217255)</li>\r\n<li>SAP Fiori for SAP DMIS 1.0 (component UICSLO01 100, SAP Note 2209387)</li>\r\n<li>SAP Fiori for SAP ERP 1.0 (components UIEAAP01 100, UIEAPS01 100, UIFICA01 100, UIGLT001 100, UIISPSCA 100, UIRT401 100, SAP Note 2134432)</li>\r\n<li>SAP Fiori for SAP ERP HCM 1.0 (components GBX01HR 600 and GBX01HRS5 605, SAP Note 2180598)</li>\r\n<li>SAP Fiori for SAP S/4HANA 1709 (component UIS4HOP1 300, SAP Note 2408541)</li>\r\n<li>SAP Fiori oData component for Approve Purchase Orders 1.0 (component GBAPP002 600, SAP Note 2131368)</li>\r\n<li>SAP Fiori oData component for Approve Purchase Contracts 1.0 (component SRA001 600, SAP Note 2128051)</li>\r\n<li>SAP Fiori oData component for Approve Timesheets 1.0 (component SRA010 600, SAP Note 2131301)</li>\r\n<li>SAP Fiori oData component for Approve Travel Expenses 1.0 (component SRA008 600, SAP Note 2131274)</li>\r\n<li>SAP Fiori oData component for Approve Travel Requests 1.0 (component SRA009 600, SAP Note 2131278)</li>\r\n<li>SAP Fiori oData component for Change Sales Orders 1.0 (component SRA003 600, SAP Note 2131814)</li>\r\n<li>SAP Fiori oData component for Check Price and Availability 1.0 (component SRA016 600, SAP Note 2131351)</li>\r\n<li>SAP Fiori oData component for Create Sales Orders 1.0 (component SRA017 600, SAP Note 2131352)</li>\r\n<li>SAP Fiori oData component for Customer Invoices 1.0 (component SRA021 600, SAP Note 2131364)</li>\r\n<li>SAP Fiori oData component for My Benefits 1.0 (component SRA007 600, SAP Note 2131187)</li>\r\n<li>SAP Fiori oData component for My Paystubs 1.0 (component SRA006 600, SAP Note 2131186)</li>\r\n<li>SAP Fiori oData component for My Spend 1.0 (component SRA012 600, SAP Note 2131303)</li>\r\n<li>SAP Fiori oData component for My Timesheet 1.0 (component SRA002 600, SAP Note 2131147)</li>\r\n<li>SAP Fiori oData component for My Travel Requests 1.0 (component SRA004 600, SAP Note 2131183)</li>\r\n<li>SAP Fiori oData component for Order from Requisitions 1.0 (component SRA013 600, SAP Note 2131310)</li>\r\n<li>SAP Fiori oData component for Track Purchase Orders 1.0 (component SRA020 600, SAP Note 2131360)</li>\r\n<li>SAP Fiori oData component for Track Sales Orders 1.0 (component SRA018 600, SAP Note 2131353)</li>\r\n<li>SAP Fiori oData component for Track Shipments 1.0 (component SRA019 600, SAP Note 2131358)</li>\r\n<li>SAP Fiori principal apps 1.0 for SAP SRM (component UIX01SRM 100, SAP Note 2217260)</li>\r\n<li>SAP Fiori principal apps for SAP ERP 1.0 - Central App (component UIX01EAP 100, SAP Note 2167334)</li>\r\n<li>SAP Fiori principal apps for SAP ERP 1.0 - HCM (component UIX01HCM 100, SAP Note 2091515, component GBHCM002 600, SAP Note 2131381, component GBHCM003, SAP Note 2131383)</li>\r\n<li>SAP Fiori principal apps for SAP ERP 1.0 - Travel (component UIX01TRV 100, SAP Note 2167334)</li>\r\n<li>SAP Fiori Travel Expense Approval 2.0 (component GBTRV002 600, SAP Note 2124793)</li>\r\n<li>SAP Fiori UI SAP ANALYTICAL SERVICES 1.0 (component UISSB001 100, SAP Note 2266130)</li>\r\n<li>SAP Fiori UI Approve Leave Requests 1.0 (component UIHCM003 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Approve Purchase Contracts 1.0 (component UISRA001 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Approve Purchase Orders 1.0 (component UIAPP002 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Approve Requests 1.0 (component UIGIB001 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Approve Requisitions 1.0 (component UIAPP001 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Approve Shopping Carts 1.0 (component UISRM001 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Approve Timesheets 1.0 (component UISRA010 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Approve Travel Expenses 1.0 (component UISRA008 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Approve Travel Requests 1.0 (component UISRA009 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Change Sales Orders 1.0 (component UISRA003 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Check Price and Availability 1.0 (component UISRA016 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Create Sales Orders 1.0 (component UISRA017 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Customer Invoices 1.0 (component UISRA021 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI for SAP ERP, add-on for embedded production planning and detailed scheduling 1.0 (component UIPPDS01 100, SAP Note 2280928)</li>\r\n<li>\r\n<div>SAP Fiori for SAP Fashion Management 1.0 (component UIFM001 100, SAP Note 2229796)</div>\r\n</li>\r\n<li>SAP Fiori UI for SAP S/4HANA Finance 1605 (component UIAPPL01 100, SAP Note 2280900)</li>\r\n<li>SAP Fiori UI for SAP Master Data Governance 1.0&#x00A0;(component <span lang=\"EN-US\" style=\"font-size: 12pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; mso-fareast-language: DE; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-bidi-language: AR-SA;\">UIMDC001 100</span>, SAP Note <span lang=\"EN-US\" style=\"font-size: 12pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-bidi-font-family: 'Times New Roman'; mso-fareast-language: DE; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-bidi-language: AR-SA;\">2230429</span>)</li>\r\n<li>SAP Fiori UI for SAP SCM 1.0 (component SCMB_UI 100, SAP Note 2203656)</li>\r\n<li>SAP Fiori UI for SAP Simple Finance On-Premise Edition 1503 (component UIAPPFI702 600, SAP Note 2238575)</li>\r\n<li>SAP Fiori UI for SAP Supply Network Collaboration 1.0 (component SCMSNCE1 100, SAP Note 2176346)</li>\r\n<li>SAP Fiori UI for SAP S/4HANA (component UIX01CA1 200, SAP Note 2280715)</li>\r\n<li>SAP Fiori UI My Benefits 1.0 (component UISRA007 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI My Leave Requests 1.0 (component UIHCM002 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI My Paystubs 1.0 (component UISRA006 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI My Shopping Cart 1.0 (component UISRA014 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI My Spend 1.0 (component UISRA012 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI My Timesheet 1.0 (component UISRA002 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI My Travel Requests 1.0 (component UISRA004 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Order from Requisitions 1.0 (component UISRA013 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Track Purchase Orders 1.0 (component UISRA020 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Track Sales Orders 1.0 (component UISRA018 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Track Shipments 1.0 (component UISRA019 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI Track Shopping Carts 1.0 (component UISRA011 100, SAP Note 2034588)</li>\r\n<li>SAP Fiori UI SAP ANALYTICAL SERVICES 1.0 (component UISSB001 100, SAP Note 2266130)</li>\r\n<li>SAP Fiori transactional apps 1.0 for SAP CRM (components UIX02CRM 100 and UICRM001 100, SAP Note 2189023)</li>\r\n<li>SAP Fiori transactional apps for SAP ERP 1.0 (component UIX02EA4 100, SAP Note 2125074, component UIX02EAP 100, SAP Note 2125074, and component UIX02RT4 100, SAP Note 2125074)</li>\r\n<li>SAP Fiori/Hana Live Content for ERP 100 (component UIHERP01 100, SAP Note 2131580)</li>\r\n<li>SAP Fiori/Hana Live Content for Sentiment Analysis 100 (component UIHFND01 100, SAP Note 2132758)</li>\r\n<li>SAP Funding Management 3.0 (component 300, SAP Note 2198972)</li>\r\n<li>SAP GATEWAY 2.0 (components IW_CBS 200 &amp; IW_CNT 200, SAP Note 2312680, component IW_FNDGC 100, SAP Note 2319096, component IW_GIL, SAP Note <span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\" style=\"width: 69.92%;\">2319114</span>, component IW_SPI 100, SAP Note 2313222 &amp; component IW_SCS 200, SAP Note 2319097, component WEBCUIF 746 &amp; 747 &amp; 748, SAP Note 2417905)</li>\r\n<li>SAP Global Batch Trace 1.0 ERP Integration (component GBTRINT 100, SAP Note 2149065)</li>\r\n<li>SAP Global Trade Services (GTS) 10.0 (component SLL-LEG 900, SAP Note 2356692)</li>\r\n<li>SAP Global Trade Services (GTS) 10.1 (component SLL-LEG 901, SAP Note 2356692)</li>\r\n<li>SAP Global Trade Services (GTS) 7.0 (component SLL-LEG 7.00, SAP Note 2356692)</li>\r\n<li>SAP Global Trade Services (GTS) 7.1 (component SLL-LEG 710, SAP Note 2356692)</li>\r\n<li>SAP Global Trade Services (GTS) 7.2 (component SLL-LEG 720, SAP Note 2356692)</li>\r\n<li>SAP Global Trade Services (GTS) 8.0 (component SLL-LEG 800, SAP Note 2356692)</li>\r\n<li>SAP Global Trade Services, identity-based preference processing 1.0 (add-on IBPP_100, SAP Note 2795075, add-on IBPP_PI 100, SAP Note 2832367)</li>\r\n<li>SAP Global Trade Services, identity-based preference processing 2.0 (add-on IBPP_200, SAP Note 2795075, add-on IBPP_PI 200 and IBPP_PI 800, SAP Note 2832367)</li>\r\n<li>SAP GRC Access Control 5.3 (components VIRSANH 530_700 &amp; 530_731, SAP Note 2536230)</li>\r\n<li>SAP GTS 11.0 (component SLL-LEG V1100, SAP Note 2356692)</li>\r\n<li>SAP In-Store Product Lookup 1.0.0 (component LWMRT401 604, SAP Note 2209914)</li>\r\n<li>SAP Manager Insight 1.0.0 (component LWMHR401 604, SAP Note 2611360)</li>\r\n<li>SAP Management of Change 1.0 (component MOC 100, SAP Note 2355120)</li>\r\n<li>SAP MOB FIELD SERV ERP INT 2.0.0 (component MCRMFERP 200, SAP Note 2370516)</li>\r\n<li>SAP Multichannel Foundation for Utilities and Public Sector 1.0 (component UMCUI501 100, SAP Note 2217321)</li>\r\n<li>SAP Multiresource Scheduling 9.0 (components MRSS 900 and MRSS_NW 900, SAP Note 2169600)</li>\r\n<li>SAP NETWEAVER 7.5 (component BW4HANA 100, SAP Note 2246699)</li>\r\n<li>SAP NetWeaver Master Data Management 7.1 (component MDM_TECH 710_700, SAP Note 2342708)</li>\r\n<li>SAP Payment Approvals 2.0.0 (component LWMFI401 604, SAP Note 2571634)</li>\r\n<li>SAP PC 10.1 For SAP NW (component POASBC 100_731, SAP Note 2322477)</li>\r\n<li>SAP ProductivityPak by RWD adapter 1.0 for SAP Solution Manager 7.0 (component ST-SPA, SAP Note 1109650)</li>\r\n<li>SAP QIM 1.0 (component QAM 100, SAP Note 2357898)</li>\r\n<li>SAP REACH COMPLIANCE 2.0 (component TDAGBCA 200_600, SAP Note 2298456)</li>\r\n<li>SAP&#x00A0;RealSpend 1.0 (component LWMSIM01 600, SAP Note 2234715)</li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 10.5pt; font-family: 'Verdana',sans-serif; color: black; line-height: 107%; mso-fareast-font-family: Calibri; mso-bidi-font-family: Arial; mso-fareast-language: EN-US; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-bidi-language: AR-SA; mso-bidi-theme-font: minor-bidi;\">SAP Retail Execution integration with SAP CRM (component MOB_CRM 100, SAP Note 2324260)</span></li>\r\n<li>SAP Screen Personas 1.0 (component PERSOS 100, SAP Note 2226123)</li>\r\n<li>SAP Screen Personas 2.0 (component PERSOS 200, SAP Note 2226123)</li>\r\n<li>SAP Screen Personas 3.0 (component PERSONAS&#x00A0;300, SAP Note 2246593)</li>\r\n<li>SAP Solution Manager adapter for SAP Quality Center 1.0 by HP (component ST-QCA, SAP Note 1109650)</li>\r\n<li>SAP Smart Business 1.0 for retail promotion execution (component UISRTL01 100, SAP Note 2201852)</li>\r\n<li>SAP Smart Business 1.0 for SAP CRM (component UISCRM01 100, SAP Note 2176772)</li>\r\n<li>SAP Smart Business 1.0 for SAP ERP (component UISERP01 100, SAP Note 2176775)</li>\r\n<li>SAP Smart Business 1.0 for SAP Fashion Management (component UISFM001 100, SAP Note 2201905)</li>\r\n<li>SAP Smart Business 1.0 for SAP Information Lifecycle Management (component UISDTG01 100, SAP Note 2201850)</li>\r\n<li>SAP Smart Business 1.0 for SAP PLM (component UIHPLM01 100, SAP Note 2201843)</li>\r\n<li>SAP Smart Business 1.0 foundation component (component UISAFND1 100, SAP Note 2201856)</li>\r\n<li>SAP Smart Business 1.0, component for KPI modeling (component UISKPI01 100, SAP Note 2176779)</li>\r\n<li>SAP Smart Business for the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (component UIHSFIN01 100, SAP Note 2201846)</li>\r\n<li>SAP Supplier Lifecycle Management 1.0 (component SMCERPIT 100, SAP Note 2192644)</li>\r\n<li>SAP Supplier Lifecycle Management 2.0 (component SMCERPIT 100, SAP Note 2192644)</li>\r\n<li>SAP Supplier Lifecycle Management 3.0 (component SMCERPIT 100, SAP Note 2192644)</li>\r\n<li>SAP Travel Expense Report 1.0.0 (component GBTRV001 600, SAP Note 2162719)</li>\r\n<li>SAP Travel Receipt Capture 2.0.0 (component MIVHRTRV 601, SAP Note 2195588)</li>\r\n<li>SAP Travel OnDemand integration 1.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\r\n<li>SAP Travel OnDemand integration 1.0 (components TEMODFI 600 and DCFLPROC 600, SAP Note 2227939)</li>\r\n<li>SAP Travel OnDemand integration 3.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\r\n<li>SAP Travel OnDemand integration 3.0 (components TEMODFI 600, ODTFINCO 600, and DCFLPROC 600, SAP Note 2227939)</li>\r\n<li>SAP Workforce Deployment for Retail and Wholesale Distribution 1.0 (component WFMCORE 200, SAP Note 2409846, component LCAPPS 2005_700, SAP Note 2424906)</li>\r\n<li>SAP Utilities Customer Engagement 1.0.0, 2.1.0 &amp; 2.1.0, cloud edition&#x00A0;(component MUTILCRM 100, SAP Note 2388251)</li>\r\n<li>SRM Shopping Cart Approval 700&#x00A0;(component GBSRM001 700, SAP Note: 2362137)</li>\r\n<li>SuccessFactors Employee Central Payroll 1.0 (component Cloud Pay 100, SAP Note 2457573)</li>\r\n<li>SuccessFactors Integration Add-on 1.0&#x00A0;(component SFIHCM01 600, SAP Note: 2375289)</li>\r\n<li>SuccessFactors Integration Add-on 2.0&#x00A0;(component SFIHCM02 600, SAP Note: 2375320)</li>\r\n<li>SuccessFactors Integration Add-on 3.0&#x00A0;(component SFIHCM03 600, SAP Note: 2276816)</li>\r\n<li>Web interface for SAP EHS Management 2.6 (component TDAGWI 260_600, SAP Note 2307624)</li>\r\n</ul>\r\n<p><strong>More information:</strong></p>\r\n<p>For detailed information about the uninstall process, see transaction SAINT in your ABAP system.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D038236)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D038236)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002011192/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002011192/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002011192/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002011192/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002011192/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002011192/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002011192/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002011192/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002011192/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Deletable_Objects_0087.pdf", "FileSize": "931", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000500982014&iv_version=0059&iv_guid=00109B36BC961EEEB2B07CE203AA3765"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1816146", "RefComponent": "BC-UPG-MP", "RefTitle": "Correction of installed software information (CISI)", "RefUrl": "/notes/1816146"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3370344", "RefComponent": "BC-UPG-MP", "RefTitle": "OCS package SAPK-200AHINUI700 does not match the current software", "RefUrl": "/notes/3370344 "}, {"RefNumber": "2601174", "RefComponent": "BC-UPG-MP", "RefTitle": "OCS package SAPK-100AHINBW4HANA does not match the current software component vector", "RefUrl": "/notes/2601174 "}, {"RefNumber": "2958294", "RefComponent": "CA-FLP-ABA-DT", "RefTitle": "SAP standard Fiori Catalog deleted on CONF layer, how to restore?", "RefUrl": "/notes/2958294 "}, {"RefNumber": "2920938", "RefComponent": "FI-TV-ODT-MTR", "RefTitle": "Using transaction SAINT to uninstall SAP Fiori OData component SRA004 1.0", "RefUrl": "/notes/2920938 "}, {"RefNumber": "2560351", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Installed Software Component Versions don't align with Installed Product Versions in system status", "RefUrl": "/notes/2560351 "}, {"RefNumber": "2554079", "RefComponent": "MDM-FN-API-ABA", "RefTitle": "ACP file for undeploying MDM_TECH", "RefUrl": "/notes/2554079 "}, {"RefNumber": "2507679", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Deinstallable Tab is not displayed in SAINT", "RefUrl": "/notes/2507679 "}, {"RefNumber": "2470235", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Unable to uninstall the addon SCM_BASIS 713 and SCMB_EXT 713 from SAINT", "RefUrl": "/notes/2470235 "}, {"RefNumber": "3431821", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on converting to SAP S/4HANA or SAP BW/4HANA using SUM 2.0 SP20", "RefUrl": "/notes/3431821 "}, {"RefNumber": "3412681", "RefComponent": "PPM-PRO", "RefTitle": "Uninstalling Add-On CPRXRPM", "RefUrl": "/notes/3412681 "}, {"RefNumber": "3422160", "RefComponent": "CA-DS4", "RefTitle": "Uninstalling Add-On DSiM BW/4HANA 200", "RefUrl": "/notes/3422160 "}, {"RefNumber": "3418514", "RefComponent": "TM-LOC-RU", "RefTitle": "Uninstalling ERP Add-On TMRU", "RefUrl": "/notes/3418514 "}, {"RefNumber": "3410465", "RefComponent": "XX-PROJ-CDP-017", "RefTitle": "Uninstalling Add-On BCON 602", "RefUrl": "/notes/3410465 "}, {"RefNumber": "3407920", "RefComponent": "BC-UPG-ADDON", "RefTitle": "SOAR (ARO) Uninstallation", "RefUrl": "/notes/3407920 "}, {"RefNumber": "3400631", "RefComponent": "XX-PROJ-CDP-TEST-713", "RefTitle": "Uninstalling Add-On HEMCRM", "RefUrl": "/notes/3400631 "}, {"RefNumber": "3400697", "RefComponent": "XX-PROJ-CDP-TEST-713", "RefTitle": "Uninstalling Add-On HEMECC", "RefUrl": "/notes/3400697 "}, {"RefNumber": "3400682", "RefComponent": "IS-H-MOB", "RefTitle": "Uninstalling ISHMOBIL", "RefUrl": "/notes/3400682 "}, {"RefNumber": "3390357", "RefComponent": "IS-DRY", "RefTitle": "Uninstalling MSGFELIX", "RefUrl": "/notes/3390357 "}, {"RefNumber": "3387930", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on converting to SAP S/4HANA or SAP BW/4HANA using SUM 2.0 SP19", "RefUrl": "/notes/3387930 "}, {"RefNumber": "3355373", "RefComponent": "CRM-IU-S-PCT", "RefTitle": "Uninstalling add-on CALC", "RefUrl": "/notes/3355373 "}, {"RefNumber": "3345113", "RefComponent": "FS-AM", "RefTitle": "Uninstalling UICB4H", "RefUrl": "/notes/3345113 "}, {"RefNumber": "3338941", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on converting to SAP S/4HANA or SAP BW/4HANA using SUM 2.0 SP18", "RefUrl": "/notes/3338941 "}, {"RefNumber": "3338478", "RefComponent": "IS-CTS-FIO", "RefTitle": "Uninstalling SAP S/4HANA Life Sciences-UI, product Add-On (ISLSUI)", "RefUrl": "/notes/3338478 "}, {"RefNumber": "3337278", "RefComponent": "IS-CTS", "RefTitle": "Uninstalling SAP S/4HANA Life Sciences, product Add-On (ISLS)", "RefUrl": "/notes/3337278 "}, {"RefNumber": "3331164", "RefComponent": "FS-RI-UI", "RefTitle": "Uninstalling UIFSRI", "RefUrl": "/notes/3331164 "}, {"RefNumber": "3328095", "RefComponent": "XX-PROJ-CDP-488", "RefTitle": "Uninstalling Add-On SAAP", "RefUrl": "/notes/3328095 "}, {"RefNumber": "3322962", "RefComponent": "GRC-SAC-ARQ", "RefTitle": "Uninstalling GRCPCRTA", "RefUrl": "/notes/3322962 "}, {"RefNumber": "3322875", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling OEE_ERP Add on for SAP MII Product", "RefUrl": "/notes/3322875 "}, {"RefNumber": "3312457", "RefComponent": "XX-PROJ-CDP-683", "RefTitle": "Uninstalling Add-On RER", "RefUrl": "/notes/3312457 "}, {"RefNumber": "3312057", "RefComponent": "MM-IV-HUB-CIM", "RefTitle": "Uninstalling HUBERPI2", "RefUrl": "/notes/3312057 "}, {"RefNumber": "3276612", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on UIACS 16S", "RefUrl": "/notes/3276612 "}, {"RefNumber": "3276663", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on UIACS 160", "RefUrl": "/notes/3276663 "}, {"RefNumber": "3276685", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on SAPFRA 16S", "RefUrl": "/notes/3276685 "}, {"RefNumber": "3276684", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on SAPFRA 160", "RefUrl": "/notes/3276684 "}, {"RefNumber": "3237964", "RefComponent": "CA-EPC", "RefTitle": "Uninstalling Add-On EPROJCON", "RefUrl": "/notes/3237964 "}, {"RefNumber": "3295853", "RefComponent": "XX-PROJ-CDP-307", "RefTitle": "RCS Enhanced Security Issuance Management: Uninstallation of the WPV Add-On", "RefUrl": "/notes/3295853 "}, {"RefNumber": "3292693", "RefComponent": "XX-PROJ-CDP-490", "RefTitle": "Uninstallation Information Note for Add-On: DSFW", "RefUrl": "/notes/3292693 "}, {"RefNumber": "3277625", "RefComponent": "TM-ADP-CSL-OTC", "RefTitle": "Uninstalling add-on TMO2C 200", "RefUrl": "/notes/3277625 "}, {"RefNumber": "3277115", "RefComponent": "TM-ADP-CSL", "RefTitle": "Uninstalling Add-on TMCSL 200", "RefUrl": "/notes/3277115 "}, {"RefNumber": "3283116", "RefComponent": "IS-A-DBM", "RefTitle": "Uninstalling Add-On DFD", "RefUrl": "/notes/3283116 "}, {"RefNumber": "3277608", "RefComponent": "TM-ADP-CSL-L2A", "RefTitle": "Uninstalling add-on TML2A 100", "RefUrl": "/notes/3277608 "}, {"RefNumber": "3276939", "RefComponent": "TM-ADP-CSL-NAO", "RefTitle": "Uninstalling add-on TMNAO 100", "RefUrl": "/notes/3276939 "}, {"RefNumber": "3260035", "RefComponent": "LOD-SF-INT", "RefTitle": "Uninstalling Add-On SFIECPEP", "RefUrl": "/notes/3260035 "}, {"RefNumber": "3269225", "RefComponent": "XX-PROJ-CDP-309", "RefTitle": "Uninstalling Add-On AOCSO", "RefUrl": "/notes/3269225 "}, {"RefNumber": "3268164", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Add-On ARBCI1 - ARIBA CLOUD INT SAP ERP 1.0", "RefUrl": "/notes/3268164 "}, {"RefNumber": "3256795", "RefComponent": "XX-PROJ-CDP-306", "RefTitle": "Uninstalling of Add-On OERRR", "RefUrl": "/notes/3256795 "}, {"RefNumber": "3252062", "RefComponent": "IS-PHA-REG", "RefTitle": "Uninstalling Add-On UIIDMP", "RefUrl": "/notes/3252062 "}, {"RefNumber": "3249725", "RefComponent": "IS-PHA-REG", "RefTitle": "Uninstalling Add-On IDMP", "RefUrl": "/notes/3249725 "}, {"RefNumber": "3239266", "RefComponent": "XX-PROJ-CDP-372", "RefTitle": "Uninstalling Add-on FSTPS", "RefUrl": "/notes/3239266 "}, {"RefNumber": "3243704", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Add-ons  'ARIBA CLOUD INT SAP ERP 1.0', 'ARIBA CLOUD INT SAP S/4HANA 1.0' and 'ARIBA CLOUD INT SAP ERP SELLER 1.0'", "RefUrl": "/notes/3243704 "}, {"RefNumber": "3224468", "RefComponent": "FS-CYT", "RefTitle": "Deinstallation CYT 800", "RefUrl": "/notes/3224468 "}, {"RefNumber": "3230193", "RefComponent": "TM-LOC-RU", "RefTitle": "Uninstalling Add-On TMRUS4H", "RefUrl": "/notes/3230193 "}, {"RefNumber": "3227080", "RefComponent": "FS-RI", "RefTitle": "Uninstalling FS-RI", "RefUrl": "/notes/3227080 "}, {"RefNumber": "3222372", "RefComponent": "IS-PHA-IHLS", "RefTitle": "Uninstalling Add-On LSCH", "RefUrl": "/notes/3222372 "}, {"RefNumber": "3221018", "RefComponent": "XX-PROJ-CDP-TEST-247", "RefTitle": "Uninstalling Add-On AARE", "RefUrl": "/notes/3221018 "}, {"RefNumber": "3224088", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-CRM60", "RefUrl": "/notes/3224088 "}, {"RefNumber": "3219427", "RefComponent": "LO-AGR-LSP", "RefTitle": "Uninstalling Add-On LSPUI", "RefUrl": "/notes/3219427 "}, {"RefNumber": "3219393", "RefComponent": "LO-AGR-LSP", "RefTitle": "Uninstalling Add-On LSP", "RefUrl": "/notes/3219393 "}, {"RefNumber": "3107792", "RefComponent": "FS-FPS", "RefTitle": "Uninstalling S4FPSL", "RefUrl": "/notes/3107792 "}, {"RefNumber": "3217902", "RefComponent": "MM-PUR-HUB-CTR", "RefTitle": "Uninstalling HUBS4IC", "RefUrl": "/notes/3217902 "}, {"RefNumber": "3213576", "RefComponent": "XX-PROJ-CDP", "RefTitle": "Uninstalling Add-On GEP (Pool Car Management)", "RefUrl": "/notes/3213576 "}, {"RefNumber": "3207198", "RefComponent": "IS-PS-BFM", "RefTitle": "Uninstalling SAP Bulk Fuel Management 2.0 - Add-On OGFM", "RefUrl": "/notes/3207198 "}, {"RefNumber": "3201499", "RefComponent": "LOD-EC-INT-ORG", "RefTitle": "Uninstalling ECS4HCM add-on", "RefUrl": "/notes/3201499 "}, {"RefNumber": "3197963", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-BW", "RefUrl": "/notes/3197963 "}, {"RefNumber": "3197899", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-SCM", "RefUrl": "/notes/3197899 "}, {"RefNumber": "3197942", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-CRM70", "RefUrl": "/notes/3197942 "}, {"RefNumber": "3197906", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-CRM", "RefUrl": "/notes/3197906 "}, {"RefNumber": "3191581", "RefComponent": "CA-FE-CFG", "RefTitle": "SAP Fiori front-end server 2022 for SAP S/4HANA", "RefUrl": "/notes/3191581 "}, {"RefNumber": "3170006", "RefComponent": "CA-DEC", "RefTitle": "Uninstalling DECSERMG", "RefUrl": "/notes/3170006 "}, {"RefNumber": "3146547", "RefComponent": "XX-PROJ-CDP-658", "RefTitle": "Uninstallation S4CARDPE", "RefUrl": "/notes/3146547 "}, {"RefNumber": "3146548", "RefComponent": "XX-PROJ-CDP-658", "RefTitle": "Uninstallation S4CARDCB", "RefUrl": "/notes/3146548 "}, {"RefNumber": "3167254", "RefComponent": "PLM-INT-TC", "RefTitle": "Uninstalling PLM SYST INTEG 2.0 FOR S/4HANA UIPLMSI", "RefUrl": "/notes/3167254 "}, {"RefNumber": "3159658", "RefComponent": "CA-GTF-AIF", "RefTitle": "Uninstalling BAEBOP", "RefUrl": "/notes/3159658 "}, {"RefNumber": "2998384", "RefComponent": "TM-ADP-CSL-OTC", "RefTitle": "Uninstalling add-on TMO2C", "RefUrl": "/notes/2998384 "}, {"RefNumber": "2998385", "RefComponent": "TM-ADP-CSL-NAO", "RefTitle": "Uninstalling add-on TMNAO", "RefUrl": "/notes/2998385 "}, {"RefNumber": "2999474", "RefComponent": "TM-ADP-CSL-L2A", "RefTitle": "Uninstalling add-on TML2A", "RefUrl": "/notes/2999474 "}, {"RefNumber": "2998564", "RefComponent": "TM-ADP-CSL", "RefTitle": "Uninstalling add-on TMCSLUI", "RefUrl": "/notes/2998564 "}, {"RefNumber": "2999445", "RefComponent": "TM-ADP-CSL", "RefTitle": "Uninstalling Add-on TMCSL", "RefUrl": "/notes/2999445 "}, {"RefNumber": "3047034", "RefComponent": "GRC-DC", "RefTitle": "Uninstalling SDC-ECC: SAP Data Custodian Application Control for SAP ERP", "RefUrl": "/notes/3047034 "}, {"RefNumber": "3045811", "RefComponent": "GRC-DC", "RefTitle": "Uninstalling SDCAC - SAP Data Custodian Application Controls (SDC-ABAC)", "RefUrl": "/notes/3045811 "}, {"RefNumber": "3141416", "RefComponent": "FI-GL-MIG-TL", "RefTitle": "Uninstalling NMI_CONT", "RefUrl": "/notes/3141416 "}, {"RefNumber": "3127872", "RefComponent": "XX-PROJ-CDP-529", "RefTitle": "Uninstalling EWMCP", "RefUrl": "/notes/3127872 "}, {"RefNumber": "3128706", "RefComponent": "CRM-SLC", "RefTitle": "Uninstalling SLCUI Add-On", "RefUrl": "/notes/3128706 "}, {"RefNumber": "3126063", "RefComponent": "LO-CMM-DC", "RefTitle": "Deal Capture: DMUI Uninstallation", "RefUrl": "/notes/3126063 "}, {"RefNumber": "3120149", "RefComponent": "CRM-SLC", "RefTitle": "Uninstalling SLCC Add-On", "RefUrl": "/notes/3120149 "}, {"RefNumber": "3045210", "RefComponent": "CEC-SRV-FSM", "RefTitle": "Uninstalling S4PACG", "RefUrl": "/notes/3045210 "}, {"RefNumber": "3045055", "RefComponent": "CEC-SRV-FSM", "RefTitle": "Uninstalling PACG", "RefUrl": "/notes/3045055 "}, {"RefNumber": "3111894", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling the add-on BWBEKA (Bundeswehr Beschaffungskanal)", "RefUrl": "/notes/3111894 "}, {"RefNumber": "3106031", "RefComponent": "PLM-INT", "RefTitle": "Uninstalling ABAP Add-on PLMSI 100 (PLM System Integration - Extension Layer 100)", "RefUrl": "/notes/3106031 "}, {"RefNumber": "3105677", "RefComponent": "PLM-INT", "RefTitle": "Uninstalling ABAP Add-on PLMSIFND 100 (PLM System Integration - Foundation Layer 100)", "RefUrl": "/notes/3105677 "}, {"RefNumber": "3104219", "RefComponent": "GRC-FIO-SAC", "RefTitle": "Uninstalling LWMGRC02 100", "RefUrl": "/notes/3104219 "}, {"RefNumber": "3104993", "RefComponent": "GRC-FIO-SAC", "RefTitle": "Uninstalling LWMGRC03 100", "RefUrl": "/notes/3104993 "}, {"RefNumber": "3103667", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstalling IW_HDB", "RefUrl": "/notes/3103667 "}, {"RefNumber": "3103719", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstalling IW_BEP", "RefUrl": "/notes/3103719 "}, {"RefNumber": "3103674", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstalling GW_CORE", "RefUrl": "/notes/3103674 "}, {"RefNumber": "3103648", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstalling IW_FND", "RefUrl": "/notes/3103648 "}, {"RefNumber": "3101353", "RefComponent": "OPU-BSC-TGW", "RefTitle": "Uninstallation of the add-on IW_PGW 100 using transaction SAINT", "RefUrl": "/notes/3101353 "}, {"RefNumber": "3099907", "RefComponent": "SCM-EM-MGR-ADM", "RefTitle": "Uninstalling SAP Event Management on SAP S/4HANA as Add-on on S/4HANA", "RefUrl": "/notes/3099907 "}, {"RefNumber": "3095545", "RefComponent": "MOB-APP-CRM-DSD", "RefTitle": "Uninstalling add-on MOBDSDCI", "RefUrl": "/notes/3095545 "}, {"RefNumber": "3073903", "RefComponent": "EPM-SCP", "RefTitle": "Uninstallation of Add-On ANASCPM", "RefUrl": "/notes/3073903 "}, {"RefNumber": "3090294", "RefComponent": "MDM-FN", "RefTitle": "SAP S/4HANA and SAP NetWeaver Master Data Management 7.1", "RefUrl": "/notes/3090294 "}, {"RefNumber": "3089095", "RefComponent": "MOB-APP-ERP-DSD", "RefTitle": "Uninstalling add-on MOBDSDEI", "RefUrl": "/notes/3089095 "}, {"RefNumber": "3068639", "RefComponent": "XX-PROJ-CDP-524", "RefTitle": "Uninstalling IDXCH2", "RefUrl": "/notes/3068639 "}, {"RefNumber": "3067735", "RefComponent": "XX-PROJ-CDP-324", "RefTitle": "Uninstalling PREPAY", "RefUrl": "/notes/3067735 "}, {"RefNumber": "3068649", "RefComponent": "XX-PROJ-CDP-310", "RefTitle": "Uninstalling IDXAT2", "RefUrl": "/notes/3068649 "}, {"RefNumber": "3084610", "RefComponent": "OPU-ASA-FG", "RefTitle": "ASAFGEE - Technical Requirements for Connectivity", "RefUrl": "/notes/3084610 "}, {"RefNumber": "3030191", "RefComponent": "SCM-EWM-FIO-ILO", "RefTitle": "Uninstalling ILOUI", "RefUrl": "/notes/3030191 "}, {"RefNumber": "3030372", "RefComponent": "SCM-EWM-ILO", "RefTitle": "Uninstalling ILO", "RefUrl": "/notes/3030372 "}, {"RefNumber": "3082662", "RefComponent": "CA-GTF-AIF", "RefTitle": "Uninstalling the Add-On AIFX", "RefUrl": "/notes/3082662 "}, {"RefNumber": "3082028", "RefComponent": "IS-DFS-MIL", "RefTitle": "Uninstalling DEF", "RefUrl": "/notes/3082028 "}, {"RefNumber": "3075684", "RefComponent": "FS-FBS-CML-LWP", "RefTitle": "Uninstalling Add-On LOANSWPU", "RefUrl": "/notes/3075684 "}, {"RefNumber": "3075431", "RefComponent": "PLM-PDM", "RefTitle": "Uninstalling TDMI", "RefUrl": "/notes/3075431 "}, {"RefNumber": "2858136", "RefComponent": "LO-AGR-BT", "RefTitle": "Uninstalling ACMTM", "RefUrl": "/notes/2858136 "}, {"RefNumber": "3071544", "RefComponent": "CA-TDM", "RefTitle": "Uninstalling DMIS_CNT: Process and Pre-requisites", "RefUrl": "/notes/3071544 "}, {"RefNumber": "3070081", "RefComponent": "CA-TDM", "RefTitle": "Objects needed for uninstallation of DMIS_CNT", "RefUrl": "/notes/3070081 "}, {"RefNumber": "3070079", "RefComponent": "LO-AGR-BT", "RefTitle": "Uninstalling ACMTMUI", "RefUrl": "/notes/3070079 "}, {"RefNumber": "3069809", "RefComponent": "FS-FBS-CML-LWP", "RefTitle": "Uninstalling Add-On LOANSWP", "RefUrl": "/notes/3069809 "}, {"RefNumber": "3032697", "RefComponent": "EPM-SA", "RefTitle": "Uninstalling Add-On ANAXSA", "RefUrl": "/notes/3032697 "}, {"RefNumber": "3062389", "RefComponent": "IS-U-EEG", "RefTitle": "EEG-Billing: Deinstallation des Add-On ENBIL", "RefUrl": "/notes/3062389 "}, {"RefNumber": "3059018", "RefComponent": "IS-U-EEG", "RefTitle": "EEG Billing: Uninstalling the add-on ENBI4", "RefUrl": "/notes/3059018 "}, {"RefNumber": "3050199", "RefComponent": "CA-FE-CFG", "RefTitle": "SAP Fiori front-end server 2021 for SAP S/4HANA", "RefUrl": "/notes/3050199 "}, {"RefNumber": "3036724", "RefComponent": "IS-PS-SBP", "RefTitle": "Uninstalling Add-On PBFBI", "RefUrl": "/notes/3036724 "}, {"RefNumber": "3049768", "RefComponent": "IS-PS-SBP", "RefTitle": "Uninstalling Add-On BPPS", "RefUrl": "/notes/3049768 "}, {"RefNumber": "3044003", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling HUBERPI add-on (Integration Component in SAP S/4HANA for Central Procurement)", "RefUrl": "/notes/3044003 "}, {"RefNumber": "3012590", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on EXTCB4H", "RefUrl": "/notes/3012590 "}, {"RefNumber": "2426707", "RefComponent": "IS-A-DBM", "RefTitle": "Uninstallation of Add-on DBM (Dealer Business Management)", "RefUrl": "/notes/2426707 "}, {"RefNumber": "3034102", "RefComponent": "SLL-LEG-CON-SPL", "RefTitle": "Uninstallation of LWMGTS01 100", "RefUrl": "/notes/3034102 "}, {"RefNumber": "3032105", "RefComponent": "PA-ER", "RefTitle": "Uninstalling SAP E-Recruiting for SAP S/4HANA", "RefUrl": "/notes/3032105 "}, {"RefNumber": "3030174", "RefComponent": "TM-ADP-ML-ICR", "RefTitle": "Uninstalling ICRUI", "RefUrl": "/notes/3030174 "}, {"RefNumber": "3030112", "RefComponent": "TM-ADP-ML-ICR", "RefTitle": "Uninstalling ICR", "RefUrl": "/notes/3030112 "}, {"RefNumber": "3025266", "RefComponent": "BC-MOB-DOE", "RefTitle": "Uninstalling CRMSPGWY Add-On", "RefUrl": "/notes/3025266 "}, {"RefNumber": "3024896", "RefComponent": "XX-PROJ-CDP-589", "RefTitle": "Uninstalling GRISG", "RefUrl": "/notes/3024896 "}, {"RefNumber": "3019307", "RefComponent": "XX-PROJ-CDP-176", "RefTitle": "Uninstalling EPAY", "RefUrl": "/notes/3019307 "}, {"RefNumber": "3018272", "RefComponent": "CA-RT-CAR", "RefTitle": "Uninstalling RTLPOSDM", "RefUrl": "/notes/3018272 "}, {"RefNumber": "3018271", "RefComponent": "CA-RT-SYN", "RefTitle": "Uninstalling RTLCONS", "RefUrl": "/notes/3018271 "}, {"RefNumber": "3018250", "RefComponent": "CA-RT-CAR", "RefTitle": "Uninstalling RTLCAR", "RefUrl": "/notes/3018250 "}, {"RefNumber": "3018248", "RefComponent": "CA-RT-AP", "RefTitle": "Uninstalling RTLRAP", "RefUrl": "/notes/3018248 "}, {"RefNumber": "3018247", "RefComponent": "CA-RT-PMR", "RefTitle": "Uninstalling RTLPROMO", "RefUrl": "/notes/3018247 "}, {"RefNumber": "3018245", "RefComponent": "CA-DDF-RT", "RefTitle": "Uninstalling RTLDDF", "RefUrl": "/notes/3018245 "}, {"RefNumber": "3018135", "RefComponent": "CA-DDF-RT", "RefTitle": "Uninstalling Add-On RTLDDF", "RefUrl": "/notes/3018135 "}, {"RefNumber": "3014719", "RefComponent": "XX-PROJ-CDP-774", "RefTitle": "Uninstalling ACMFPUI", "RefUrl": "/notes/3014719 "}, {"RefNumber": "3013717", "RefComponent": "IS-DFS-OF", "RefTitle": "Uninstalling ADFDIF", "RefUrl": "/notes/3013717 "}, {"RefNumber": "2998983", "RefComponent": "CRM-IPS-FRM", "RefTitle": "Uninstalling add-on FRMBI", "RefUrl": "/notes/2998983 "}, {"RefNumber": "3010466", "RefComponent": "PA-ER", "RefTitle": "Uninstalling SAP E-Recruiting", "RefUrl": "/notes/3010466 "}, {"RefNumber": "3006487", "RefComponent": "PA-ER-LOC", "RefTitle": "LOCFEREC - Add-on Uninstallation", "RefUrl": "/notes/3006487 "}, {"RefNumber": "2995938", "RefComponent": "CRM-IPS-FRM", "RefTitle": "Uninstalling add-on FRMCM", "RefUrl": "/notes/2995938 "}, {"RefNumber": "2994020", "RefComponent": "SLL-LEG-FUN", "RefTitle": "Uninstalling SAP Fiori for SAP Global Trade Services", "RefUrl": "/notes/2994020 "}, {"RefNumber": "2991208", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling SAP S/4HANA integration with SAP Easy Document Management RCS", "RefUrl": "/notes/2991208 "}, {"RefNumber": "2987986", "RefComponent": "XX-PROJ-CDP-001", "RefTitle": "Uninstalling FB4U", "RefUrl": "/notes/2987986 "}, {"RefNumber": "2988020", "RefComponent": "XX-PROJ-CDP-001", "RefTitle": "Uninstalling FB4UUI", "RefUrl": "/notes/2988020 "}, {"RefNumber": "2985137", "RefComponent": "RE-FX-YC", "RefTitle": "Uninstalling RECA", "RefUrl": "/notes/2985137 "}, {"RefNumber": "2977299", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling FAAM 604 --- <PERSON><PERSON>", "RefUrl": "/notes/2977299 "}, {"RefNumber": "2975712", "RefComponent": "CEC-MKT-ITC", "RefTitle": "Plugin to enable uninstallation of SAP_CUAN", "RefUrl": "/notes/2975712 "}, {"RefNumber": "2975691", "RefComponent": "CEC-MKT-ITC", "RefTitle": "Uninstalling of hybris Marketing 1.1 or 1.2 (SAP_CUAN 110 and 120)", "RefUrl": "/notes/2975691 "}, {"RefNumber": "2969579", "RefComponent": "PA-FIO", "RefTitle": "Uninstalling SAP Fiori OData component GBX01HR5", "RefUrl": "/notes/2969579 "}, {"RefNumber": "2970254", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling FAAM 604", "RefUrl": "/notes/2970254 "}, {"RefNumber": "2947416", "RefComponent": "FS-PE", "RefTitle": "Uninstalling PES4UI 100", "RefUrl": "/notes/2947416 "}, {"RefNumber": "2955487", "RefComponent": "FS-PE", "RefTitle": "Uninstalling PEUI 500", "RefUrl": "/notes/2955487 "}, {"RefNumber": "2964250", "RefComponent": "LOD-PER", "RefTitle": "FS-PER Rel 3.0 : Uninstalling SAP PaPM (NXI)", "RefUrl": "/notes/2964250 "}, {"RefNumber": "2960693", "RefComponent": "IOT-EDG-BEF", "RefTitle": "Uninstalling S4EF", "RefUrl": "/notes/2960693 "}, {"RefNumber": "2960853", "RefComponent": "XX-PROJ-CDP-487", "RefTitle": "Uninstalling AMSOG", "RefUrl": "/notes/2960853 "}, {"RefNumber": "2953399", "RefComponent": "OPU-ASA-EE", "RefTitle": "Uninstalling ASANWEE 100", "RefUrl": "/notes/2953399 "}, {"RefNumber": "2953056", "RefComponent": "OPU-ASA-FG", "RefTitle": "Uninstalling ASAFGEE 100", "RefUrl": "/notes/2953056 "}, {"RefNumber": "2952924", "RefComponent": "OPU-ASA-FG", "RefTitle": "Plugin Class for Uninstallation of ASAFGEE 100", "RefUrl": "/notes/2952924 "}, {"RefNumber": "2949178", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling TMLSPERP 100", "RefUrl": "/notes/2949178 "}, {"RefNumber": "2939629", "RefComponent": "XX-PROJ-CDP-294", "RefTitle": "Uninstalling USC2B", "RefUrl": "/notes/2939629 "}, {"RefNumber": "2919182", "RefComponent": "CA-FE-CFG", "RefTitle": "SAP Fiori front-end server 2020 for SAP S/4HANA", "RefUrl": "/notes/2919182 "}, {"RefNumber": "2936344", "RefComponent": "CRM-IPS", "RefTitle": "Uninstalling DHSSW", "RefUrl": "/notes/2936344 "}, {"RefNumber": "2931283", "RefComponent": "XX-PROJ-CDP-551", "RefTitle": "Uninstalling add-on FGL", "RefUrl": "/notes/2931283 "}, {"RefNumber": "2931270", "RefComponent": "XX-PROJ-CDP-551", "RefTitle": "Uninstalling add-on FIF", "RefUrl": "/notes/2931270 "}, {"RefNumber": "2322477", "RefComponent": "XAP-SBC-BUI-ABA", "RefTitle": "Uninstallation of Add-on POA SBC", "RefUrl": "/notes/2322477 "}, {"RefNumber": "2806224", "RefComponent": "FS-AM", "RefTitle": "Uninstalling FSAPPL", "RefUrl": "/notes/2806224 "}, {"RefNumber": "2806931", "RefComponent": "FS-XA", "RefTitle": "Uninstalling FSPOT", "RefUrl": "/notes/2806931 "}, {"RefNumber": "2916946", "RefComponent": "EPM-SA", "RefTitle": "Uninstalling Add-On OPMFND", "RefUrl": "/notes/2916946 "}, {"RefNumber": "2916759", "RefComponent": "GRC-SPM-SR", "RefTitle": "Uninstalling Add-On SR_CORE", "RefUrl": "/notes/2916759 "}, {"RefNumber": "2806933", "RefComponent": "FS-XA", "RefTitle": "Uninstalling FSXAL", "RefUrl": "/notes/2806933 "}, {"RefNumber": "2915339", "RefComponent": "CA-MRS", "RefTitle": "Uninstalling MRSS_UI5GT", "RefUrl": "/notes/2915339 "}, {"RefNumber": "2915371", "RefComponent": "CA-MRS", "RefTitle": "Uninstalling MRSS_UI5", "RefUrl": "/notes/2915371 "}, {"RefNumber": "2915332", "RefComponent": "CA-MRS", "RefTitle": "Uninstalling MRSS_NW", "RefUrl": "/notes/2915332 "}, {"RefNumber": "2914011", "RefComponent": "CA-MRS", "RefTitle": "Uninstalling MRSS", "RefUrl": "/notes/2914011 "}, {"RefNumber": "2911053", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation configuration", "RefUrl": "/notes/2911053 "}, {"RefNumber": "2910825", "RefComponent": "LOD-FSN-AGT", "RefTitle": "Uninstalling add-on PSCW", "RefUrl": "/notes/2910825 "}, {"RefNumber": "2909138", "RefComponent": "SCM-YL", "RefTitle": "Uninstalling add-on SAPYL", "RefUrl": "/notes/2909138 "}, {"RefNumber": "2907724", "RefComponent": "LOD-FSN-AGT", "RefTitle": "Uninstalling add-on BSNAGT", "RefUrl": "/notes/2907724 "}, {"RefNumber": "2899706", "RefComponent": "XX-PROJ-CDP-584", "RefTitle": "Uninstallation of EWMCPS4 Add-On", "RefUrl": "/notes/2899706 "}, {"RefNumber": "2866277", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGGW", "RefUrl": "/notes/2866277 "}, {"RefNumber": "2866276", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGIF", "RefUrl": "/notes/2866276 "}, {"RefNumber": "2866282", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGCRM", "RefUrl": "/notes/2866282 "}, {"RefNumber": "2866260", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGBW", "RefUrl": "/notes/2866260 "}, {"RefNumber": "2866268", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGWDA", "RefUrl": "/notes/2866268 "}, {"RefNumber": "2894377", "RefComponent": "XX-PROJ-CDP-283", "RefTitle": "CBCOM: Uninstalling CBCOM 100", "RefUrl": "/notes/2894377 "}, {"RefNumber": "2890079", "RefComponent": "TM-ADP-CSL", "RefTitle": "Uninstalling TMCSL 400", "RefUrl": "/notes/2890079 "}, {"RefNumber": "2890614", "RefComponent": "TM-ADP-CSL-OTC", "RefTitle": "Uninstalling TMO2C 400", "RefUrl": "/notes/2890614 "}, {"RefNumber": "2890947", "RefComponent": "TM-ADP-CSL-NAO", "RefTitle": "Uninstalling TMNAO 300", "RefUrl": "/notes/2890947 "}, {"RefNumber": "2891126", "RefComponent": "TM-ADP-CSL-L2A", "RefTitle": "Uninstalling TML2A 300", "RefUrl": "/notes/2891126 "}, {"RefNumber": "2891297", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling PRATVUI", "RefUrl": "/notes/2891297 "}, {"RefNumber": "2888899", "RefComponent": "GRC-UDS-DO", "RefTitle": "Uninstalling Addon UIMUI5", "RefUrl": "/notes/2888899 "}, {"RefNumber": "2888859", "RefComponent": "GRC-UDS-DO", "RefTitle": "Uninstalling Addon UISM", "RefUrl": "/notes/2888859 "}, {"RefNumber": "2881849", "RefComponent": "XX-PROJ-CDP-566", "RefTitle": "Uninstalling Addon UIMUI5", "RefUrl": "/notes/2881849 "}, {"RefNumber": "2866246", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGSGUI", "RefUrl": "/notes/2866246 "}, {"RefNumber": "2882129", "RefComponent": "BC-INS-TC-CNT", "RefTitle": "Uninstalling PCAI_ENT", "RefUrl": "/notes/2882129 "}, {"RefNumber": "2881878", "RefComponent": "XX-PROJ-CDP-553", "RefTitle": "Uninstalling Addon UIMWUI", "RefUrl": "/notes/2881878 "}, {"RefNumber": "2881905", "RefComponent": "XX-PROJ-CDP-568", "RefTitle": "Uninstalling Addon UIMWDA", "RefUrl": "/notes/2881905 "}, {"RefNumber": "2869393", "RefComponent": "XX-PROJ-CDP-571", "RefTitle": "Uninstalling ABAP Add-On PLVAT", "RefUrl": "/notes/2869393 "}, {"RefNumber": "2881095", "RefComponent": "XX-PROJ-CDP-566", "RefTitle": "Uninstalling Addon UIMGW", "RefUrl": "/notes/2881095 "}, {"RefNumber": "2880997", "RefComponent": "XX-PROJ-CDP-266", "RefTitle": "Uninstalling Addon UIM", "RefUrl": "/notes/2880997 "}, {"RefNumber": "2876462", "RefComponent": "XX-PROJ-CDP-394", "RefTitle": "Uninstalling MNFS", "RefUrl": "/notes/2876462 "}, {"RefNumber": "2873441", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Best Practice add-ons", "RefUrl": "/notes/2873441 "}, {"RefNumber": "2870360", "RefComponent": "IS-U-EPM", "RefTitle": "EPM: UTPFM: Uninstallation of Add-on UTPFM 350", "RefUrl": "/notes/2870360 "}, {"RefNumber": "2868220", "RefComponent": "IS-U-EPM", "RefTitle": "EPM: UTPFM: Uninstallation of Add-on UTPFM 350", "RefUrl": "/notes/2868220 "}, {"RefNumber": "2868198", "RefComponent": "IS-U-EPM", "RefTitle": "EPM: UTPIT: Uninstallation of Add-on UTPIT 350", "RefUrl": "/notes/2868198 "}, {"RefNumber": "2866274", "RefComponent": "XX-PROJ-CDP-ACS-007", "RefTitle": "Uninstalling LOGCOM", "RefUrl": "/notes/2866274 "}, {"RefNumber": "2864344", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling TMAB4 400", "RefUrl": "/notes/2864344 "}, {"RefNumber": "2861037", "RefComponent": "LOD-PER", "RefTitle": "FS-PER Rel 3.0 SP11: Plugin class for uninstalling SAP PaPM (NXI)", "RefUrl": "/notes/2861037 "}, {"RefNumber": "2860461", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling LOGS4H", "RefUrl": "/notes/2860461 "}, {"RefNumber": "2855426", "RefComponent": "SLL-LEG-FUN", "RefTitle": "Uninstalling SAP_AP in the context of SAP GTS", "RefUrl": "/notes/2855426 "}, {"RefNumber": "2849695", "RefComponent": "XX-PROJ-CDP-574", "RefTitle": "Uninstalling CASHREG 100", "RefUrl": "/notes/2849695 "}, {"RefNumber": "2706813", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on CB4HANA", "RefUrl": "/notes/2706813 "}, {"RefNumber": "2706776", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on POCB4H", "RefUrl": "/notes/2706776 "}, {"RefNumber": "2844529", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy and Maintenance Information for the ABAP add-on UICB4H", "RefUrl": "/notes/2844529 "}, {"RefNumber": "2851245", "RefComponent": "FI-AF-SAI", "RefTitle": "Uninstalling SAIUI (S/4 HANA for Accounting Integration 1907)", "RefUrl": "/notes/2851245 "}, {"RefNumber": "2851291", "RefComponent": "FI-AF-SAI", "RefTitle": "Uninstalling SAI (S/4 HANA for Accounting Integration 1907)", "RefUrl": "/notes/2851291 "}, {"RefNumber": "2847388", "RefComponent": "XX-PROJ-CDP-676", "RefTitle": "Uninstalling NNEU", "RefUrl": "/notes/2847388 "}, {"RefNumber": "2847452", "RefComponent": "XX-PROJ-CDP-676", "RefTitle": "Uninstalling NNEB", "RefUrl": "/notes/2847452 "}, {"RefNumber": "2847410", "RefComponent": "XX-PROJ-CDP-676", "RefTitle": "Uninstalling NNUI", "RefUrl": "/notes/2847410 "}, {"RefNumber": "2847343", "RefComponent": "XX-PROJ-CDP-676", "RefTitle": "Uninstalling NNSB", "RefUrl": "/notes/2847343 "}, {"RefNumber": "2846015", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling SLCEUI addon", "RefUrl": "/notes/2846015 "}, {"RefNumber": "2842802", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling TMAB 400", "RefUrl": "/notes/2842802 "}, {"RefNumber": "2822791", "RefComponent": "FS-FND-FCL", "RefTitle": "Uninstalling S4FSFND", "RefUrl": "/notes/2822791 "}, {"RefNumber": "2832367", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Add-on IBPP_PI", "RefUrl": "/notes/2832367 "}, {"RefNumber": "2831306", "RefComponent": "XX-PROJ-CDP-394", "RefTitle": "Uninstalling NFSE", "RefUrl": "/notes/2831306 "}, {"RefNumber": "2831301", "RefComponent": "FS-XA", "RefTitle": "Uninstalling POCB4H", "RefUrl": "/notes/2831301 "}, {"RefNumber": "2821406", "RefComponent": "FS-AM", "RefTitle": "Uninstalling CB4HANA", "RefUrl": "/notes/2821406 "}, {"RefNumber": "2821159", "RefComponent": "XX-PROJ-CDP-539", "RefTitle": "Uninstallation Attribute Based Planning(ABPSCM)", "RefUrl": "/notes/2821159 "}, {"RefNumber": "2821177", "RefComponent": "XX-PROJ-CDP-539", "RefTitle": "ABP: Uninstallation Attribute Based Planning (ABPERP)", "RefUrl": "/notes/2821177 "}, {"RefNumber": "2807957", "RefComponent": "SCM-APO-PPS-ERP", "RefTitle": "Uninstalling SAP SCM PPDS ON ERP", "RefUrl": "/notes/2807957 "}, {"RefNumber": "2807689", "RefComponent": "EPM-NM", "RefTitle": "Uninstalling NOTESMG", "RefUrl": "/notes/2807689 "}, {"RefNumber": "2771798", "RefComponent": "XX-SER-REL", "RefTitle": "S/4HANA readiness of Fiori Business Suite Add-Ons", "RefUrl": "/notes/2771798 "}, {"RefNumber": "2796120", "RefComponent": "XX-PROJ-CDP-539", "RefTitle": "ABP: Uninstallation Attribute Based Planning(ABPSCM, ABPERP, ABPFMS)", "RefUrl": "/notes/2796120 "}, {"RefNumber": "2795075", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Add-on IBPP", "RefUrl": "/notes/2795075 "}, {"RefNumber": "2792183", "RefComponent": "BC-UPG-ADDON", "RefTitle": "STWACS: Uninstalling (Stock Transfer Workbench)", "RefUrl": "/notes/2792183 "}, {"RefNumber": "2792127", "RefComponent": "BC-UPG-ADDON", "RefTitle": "RAERP: Uninstalling (Automated Store Allocation)", "RefUrl": "/notes/2792127 "}, {"RefNumber": "2791460", "RefComponent": "SCM-APO-EMS", "RefTitle": "Uninstalling of MSPLERP 100 using SAINT transaction", "RefUrl": "/notes/2791460 "}, {"RefNumber": "2727004", "RefComponent": "EPM-EA-DES", "RefTitle": "Uninstalling FPMBASIS 200 & 200_730", "RefUrl": "/notes/2727004 "}, {"RefNumber": "2782223", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling CPQUICRM addon", "RefUrl": "/notes/2782223 "}, {"RefNumber": "2781679", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling CPQUIGW addon", "RefUrl": "/notes/2781679 "}, {"RefNumber": "2776914", "RefComponent": "PLM-ECC", "RefTitle": "ECTR: Uninstalling S4ECTR", "RefUrl": "/notes/2776914 "}, {"RefNumber": "2768034", "RefComponent": "CA-FS-POL", "RefTitle": "Uninstalling SAP Process Object Builder", "RefUrl": "/notes/2768034 "}, {"RefNumber": "2768528", "RefComponent": "LOD-EC-INT-ORG", "RefTitle": "Uninstalling PA_SE_IN add-on", "RefUrl": "/notes/2768528 "}, {"RefNumber": "2763956", "RefComponent": "XX-PROJ-CDP-691", "RefTitle": "Uninstalling Cash Application Downport for ERP (CASHAPPE)", "RefUrl": "/notes/2763956 "}, {"RefNumber": "2760565", "RefComponent": "GRC-FIO-RM", "RefTitle": "Uninstallation of UIGRRM01 100: SAP Fiori 1.0 for SAP Risk Management", "RefUrl": "/notes/2760565 "}, {"RefNumber": "2760579", "RefComponent": "GRC-FIO-SPC", "RefTitle": "Uninstallation of UIGRPC01 100: SAP Fiori 1.0 for SAP Process Control", "RefUrl": "/notes/2760579 "}, {"RefNumber": "2757276", "RefComponent": "GRC-BIS", "RefTitle": "Uninstall ASSURANCE AND COMPLIANCE - Software Component UIACS", "RefUrl": "/notes/2757276 "}, {"RefNumber": "2755621", "RefComponent": "EPM-DSM-INS", "RefTitle": "Uninstalling DISCLMG", "RefUrl": "/notes/2755621 "}, {"RefNumber": "2754644", "RefComponent": "FS-PE", "RefTitle": "Uninstalling PAY-ENGINE 500", "RefUrl": "/notes/2754644 "}, {"RefNumber": "2755064", "RefComponent": "PLM-ECC", "RefTitle": "ECTR: Uninstalling ECTR", "RefUrl": "/notes/2755064 "}, {"RefNumber": "2750685", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling Access Control 4.0 PlugIn Component VIRSANH", "RefUrl": "/notes/2750685 "}, {"RefNumber": "2750701", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling Access Control 4.0 PlugIn Component VIRSA", "RefUrl": "/notes/2750701 "}, {"RefNumber": "2732902", "RefComponent": "XX-CSC-US-WE-WM", "RefTitle": "Uninstalling WEC 605", "RefUrl": "/notes/2732902 "}, {"RefNumber": "2749060", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling Access Control 5.2 Plug-In Component VIRSAHR", "RefUrl": "/notes/2749060 "}, {"RefNumber": "2748914", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling Access Control 5.2 PlugIn Component VIRSANH", "RefUrl": "/notes/2748914 "}, {"RefNumber": "2732994", "RefComponent": "XX-PROJ-CDP-157", "RefTitle": "Uninstallation Add-on NEGCON  602", "RefUrl": "/notes/2732994 "}, {"RefNumber": "2729111", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling BP-ERP", "RefUrl": "/notes/2729111 "}, {"RefNumber": "2717731", "RefComponent": "IS-U-IDEX", "RefTitle": "APEU: Uninstalling APEU", "RefUrl": "/notes/2717731 "}, {"RefNumber": "2717705", "RefComponent": "CA-GTF-APE", "RefTitle": "UIAPE: Uninstalling UIAPE", "RefUrl": "/notes/2717705 "}, {"RefNumber": "2715328", "RefComponent": "IS-U-EIM", "RefTitle": "Uninstalling UCOM", "RefUrl": "/notes/2715328 "}, {"RefNumber": "2715355", "RefComponent": "IS-U-EIM", "RefTitle": "Uninstallation of Add-on US4G", "RefUrl": "/notes/2715355 "}, {"RefNumber": "2711685", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation for Addon - GMA RCS, using SAINT", "RefUrl": "/notes/2711685 "}, {"RefNumber": "2711615", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation For Addon - DSiM 3.0, using SAINT", "RefUrl": "/notes/2711615 "}, {"RefNumber": "2708037", "RefComponent": "CA-GTF-APE", "RefTitle": "APE: Uninstalling APE", "RefUrl": "/notes/2708037 "}, {"RefNumber": "2687669", "RefComponent": "GRC-BIS", "RefTitle": "Uninstall ASSURANCE AND COMPLIANCE - Software Component SAPFRA", "RefUrl": "/notes/2687669 "}, {"RefNumber": "2690979", "RefComponent": "CA-GBT", "RefTitle": "Uninstallation of Global Batch Traceability (GBT)", "RefUrl": "/notes/2690979 "}, {"RefNumber": "2660330", "RefComponent": "XX-PROJ-CDP-586", "RefTitle": "Uninstallation of Add-on MOSB 100", "RefUrl": "/notes/2660330 "}, {"RefNumber": "2660905", "RefComponent": "IS-U-EIM", "RefTitle": "IDEIM: Uninstallation of Add-on IDEIM 200", "RefUrl": "/notes/2660905 "}, {"RefNumber": "2664869", "RefComponent": "XX-PROJ-CDP-ACS-008", "RefTitle": "Uninstallation of Add-on CONDET 600", "RefUrl": "/notes/2664869 "}, {"RefNumber": "2664866", "RefComponent": "IS-U-LIB-DE-GF", "RefTitle": "Uninstallation of Add-on IDXGF 100", "RefUrl": "/notes/2664866 "}, {"RefNumber": "2660321", "RefComponent": "IS-U-LIB-DE-MM", "RefTitle": "Uninstallation of Add-on MEMI 100", "RefUrl": "/notes/2660321 "}, {"RefNumber": "2663327", "RefComponent": "IS-U-LIB-DE-GM", "RefTitle": "Uninstallation of Add-on IDEXGM 604", "RefUrl": "/notes/2663327 "}, {"RefNumber": "2663357", "RefComponent": "IS-U-LIB-DE-GM", "RefTitle": "Uninstallation of Add-on IDXGM 200", "RefUrl": "/notes/2663357 "}, {"RefNumber": "2656381", "RefComponent": "XX-PROJ-CDP-578", "RefTitle": "Uninstallation of Add-on IDXGC 200 or IDXGC 802", "RefUrl": "/notes/2656381 "}, {"RefNumber": "2657369", "RefComponent": "IS-U-LIB-DE-CL", "RefTitle": "Uninstallation of Add-on IDXGL 200", "RefUrl": "/notes/2657369 "}, {"RefNumber": "2662186", "RefComponent": "GRC-SAC-ARQ", "RefTitle": "Uninstalling LWMGRC01 100 - SAP Access Approver 2.0.0", "RefUrl": "/notes/2662186 "}, {"RefNumber": "2656877", "RefComponent": "XX-PROJ-CDP-OEW", "RefTitle": "OEWB: Uninstalling SAP OEWB Release 600", "RefUrl": "/notes/2656877 "}, {"RefNumber": "2632454", "RefComponent": "CA-MON", "RefTitle": "Uninstalling Business Process Tracking BPMT 702", "RefUrl": "/notes/2632454 "}, {"RefNumber": "2630240", "RefComponent": "BC-CTS-TMS-CTR", "RefTitle": "Uninstalling CTS_PLUG 200", "RefUrl": "/notes/2630240 "}, {"RefNumber": "2525068", "RefComponent": "XX-PROJ-CDP-184", "RefTitle": "IDXPF: Uninstalling IDXPF 604", "RefUrl": "/notes/2525068 "}, {"RefNumber": "2622613", "RefComponent": "CA-FE-CFG", "RefTitle": "Uninstalling SAPUIFT 100", "RefUrl": "/notes/2622613 "}, {"RefNumber": "2616622", "RefComponent": "FI-AF-ARO", "RefTitle": "Uninstalling SAP ARO - FOM604", "RefUrl": "/notes/2616622 "}, {"RefNumber": "2569045", "RefComponent": "BW-BCT-ISR-PIP", "RefTitle": "Performing the Uninstall Process of RTLPOSDM 100_xxx Using Transaction SAINT", "RefUrl": "/notes/2569045 "}, {"RefNumber": "2611463", "RefComponent": "AIE-AII", "RefTitle": "Uninstall SAP Auto-ID Infrastructure", "RefUrl": "/notes/2611463 "}, {"RefNumber": "2609756", "RefComponent": "XX-PROJ-CDP-524", "RefTitle": "Uninstalling IDEXCH 604 - DATA XCHANGE SWISS UTILITY", "RefUrl": "/notes/2609756 "}, {"RefNumber": "2586823", "RefComponent": "SCM-APO-CA-TOL", "RefTitle": "Uninstalling SCMAPO 7.13 and SCMAPO 7.14 as Add-On to SAP ERP", "RefUrl": "/notes/2586823 "}, {"RefNumber": "2571634", "RefComponent": "MOB-APP-PAP", "RefTitle": "Deinstallation des Add-Ons MOB PAYAPPROVAL INT 2.0.0 (LWMFI401) mit der Transaktion SAINT", "RefUrl": "/notes/2571634 "}, {"RefNumber": "2544275", "RefComponent": "CA-TRA-IN", "RefTitle": "GST - ITR : Uninstallation ITR ( Indain Tax Reform )", "RefUrl": "/notes/2544275 "}, {"RefNumber": "2564073", "RefComponent": "IS-DRY", "RefTitle": "Release strategy for the ABAP add-On SAP Dairy Management by msg for SAP S/4HANA", "RefUrl": "/notes/2564073 "}, {"RefNumber": "2528206", "RefComponent": "XX-PROJ-CDP-040", "RefTitle": "Uninstalling Add-on DMEE 601", "RefUrl": "/notes/2528206 "}, {"RefNumber": "2536453", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling VIRSAHR - SAP Access Control", "RefUrl": "/notes/2536453 "}, {"RefNumber": "2536230", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling VIRSANH - SAP Access Control", "RefUrl": "/notes/2536230 "}, {"RefNumber": "2531823", "RefComponent": "SCM-EM-MGR", "RefTitle": "Uninstalling Add-On SCEMSRV 900 or SCEMSRV 920", "RefUrl": "/notes/2531823 "}, {"RefNumber": "2529018", "RefComponent": "SD-SLS-WOM", "RefTitle": "WOM: Uninstallation WOM 200 (Cross Channel Order Management)", "RefUrl": "/notes/2529018 "}, {"RefNumber": "2513541", "RefComponent": "FIN-FB", "RefTitle": "Uninstallation of add-on FINBASIS 700, 748/FSCM_CCD 618 and above", "RefUrl": "/notes/2513541 "}, {"RefNumber": "2512218", "RefComponent": "MOB-APP-ORD", "RefTitle": "Uninstalling LWMSD002", "RefUrl": "/notes/2512218 "}, {"RefNumber": "2505027", "RefComponent": "FIN-SEM", "RefTitle": "Uninstallation of add-on SEM-BW 700, 748, and above", "RefUrl": "/notes/2505027 "}, {"RefNumber": "2503641", "RefComponent": "OPU-DUE", "RefTitle": "Uninstallation of DUET add-ons with transaction SAINT", "RefUrl": "/notes/2503641 "}, {"RefNumber": "2497991", "RefComponent": "CA-GTF-AIF", "RefTitle": "Uninstalling the add-on AIF (Application Interface Framework)", "RefUrl": "/notes/2497991 "}, {"RefNumber": "70228", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Add-ons: Conditions and upgrade planning", "RefUrl": "/notes/70228 "}, {"RefNumber": "2256000", "RefComponent": "BC-SRV-APS-APL", "RefTitle": "Uninstallation of SAP Fiori UI Component: UI for Basis Applications", "RefUrl": "/notes/2256000 "}, {"RefNumber": "2481113", "RefComponent": "XX-PROJ-CDP-597", "RefTitle": "Uninstalling CPRA Add-ons", "RefUrl": "/notes/2481113 "}, {"RefNumber": "2477324", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling Add-On SECLG1 100", "RefUrl": "/notes/2477324 "}, {"RefNumber": "1922973", "RefComponent": "IS-DRY", "RefTitle": "Release strategy for the ABAP add-on MSGFELIX", "RefUrl": "/notes/1922973 "}, {"RefNumber": "2457573", "RefComponent": "LOD-SF-INT", "RefTitle": "Uninstalling CLOUD_PAY", "RefUrl": "/notes/2457573 "}, {"RefNumber": "2463636", "RefComponent": "GRC-SPC-IU", "RefTitle": "Uninstalling GRCFND_A - SAP Access Control, SAP Process Control, SAP Risk Management", "RefUrl": "/notes/2463636 "}, {"RefNumber": "97620", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Overview of important OCS SAP Notes", "RefUrl": "/notes/97620 "}, {"RefNumber": "2456300", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling GRCPIERP - SAP Access Control, SAP Process Control", "RefUrl": "/notes/2456300 "}, {"RefNumber": "2453593", "RefComponent": "GRC-ACP", "RefTitle": "Uninstalling GRCPINW - SAP Access Control, SAP Process Control", "RefUrl": "/notes/2453593 "}, {"RefNumber": "2441827", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling STTP 100 Add-On", "RefUrl": "/notes/2441827 "}, {"RefNumber": "2438631", "RefComponent": "MDM-FN-API-ABA", "RefTitle": "Uninstallation of add-on MDM TECH 710 700", "RefUrl": "/notes/2438631 "}, {"RefNumber": "2437183", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstalling SLCE addon", "RefUrl": "/notes/2437183 "}, {"RefNumber": "2433703", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UICAR001 100 from the Product  version SAP Fiori 2.0 for SAP Customer Activity Repository retail applications bundle", "RefUrl": "/notes/2433703 "}, {"RefNumber": "2424906", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation of LCAPPS 2005_700", "RefUrl": "/notes/2424906 "}, {"RefNumber": "1905172", "RefComponent": "BC-UPG-ADDON", "RefTitle": "TEMPLATE: Uninstalling <Add-On>", "RefUrl": "/notes/1905172 "}, {"RefNumber": "2420945", "RefComponent": "BC-MOB-DOE", "RefTitle": "Uninstallation of the add-on SUPDOE with transaction SAINT", "RefUrl": "/notes/2420945 "}, {"RefNumber": "2409846", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Uninstallation WFMCORE 200", "RefUrl": "/notes/2409846 "}, {"RefNumber": "2408541", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of Fiori based Front End  Software Components", "RefUrl": "/notes/2408541 "}, {"RefNumber": "2403053", "RefComponent": "SCM-BAS-INT", "RefTitle": "Uninstalling SCM_BASIS 713 and SCM_BASIS 714 as Add-On to ERP", "RefUrl": "/notes/2403053 "}, {"RefNumber": "2360806", "RefComponent": "SCM-EWM-IF", "RefTitle": "Uninstalling SAP EWM 9.3, SAP EWM 9.4 and SAP EWM 9.5 as Add-On to SAP ERP", "RefUrl": "/notes/2360806 "}, {"RefNumber": "2276816", "RefComponent": "PA-SFI-TM", "RefTitle": "Uninstalling add-on SFIHCM03", "RefUrl": "/notes/2276816 "}, {"RefNumber": "2375320", "RefComponent": "PA-SFI-TM", "RefTitle": "Uninstalling add-on SFIHCM02", "RefUrl": "/notes/2375320 "}, {"RefNumber": "2375289", "RefComponent": "PA-SFI-TM", "RefTitle": "Uninstalling add-on SFIHCM01", "RefUrl": "/notes/2375289 "}, {"RefNumber": "2381952", "RefComponent": "LO-INT-COD", "RefTitle": "Uninstalling software component CODERINT 600", "RefUrl": "/notes/2381952 "}, {"RefNumber": "2189708", "RefComponent": "BW-B4H-LM", "RefTitle": "SAP BW/4HANA Add-On Handling and Usage", "RefUrl": "/notes/2189708 "}, {"RefNumber": "2373504", "RefComponent": "MOB-APP-TEA", "RefTitle": "Uninstalling COD_EXT_CNT 6.00 (CODEXTCT 600)", "RefUrl": "/notes/2373504 "}, {"RefNumber": "2217489", "RefComponent": "CA-UI5-DLV", "RefTitle": "Maintenance and Update Strategy for SAP Fiori Front-End Server", "RefUrl": "/notes/2217489 "}, {"RefNumber": "2370516", "RefComponent": "MOB-APP-SAL", "RefTitle": "Uninstalling add-on MCRMFERP (SAP Mobile Service 2.0 ERP Addon) with transaction SAINT", "RefUrl": "/notes/2370516 "}, {"RefNumber": "2307907", "RefComponent": "EHS-SAF-GLM", "RefTitle": "Uninstalling Genifix 2.2", "RefUrl": "/notes/2307907 "}, {"RefNumber": "2362137", "RefComponent": "MOB-APP-CAA", "RefTitle": "Uninstalling GBSRM001", "RefUrl": "/notes/2362137 "}, {"RefNumber": "2357898", "RefComponent": "CA-IAM-QIM", "RefTitle": "Quality Issue Management 1.0: Deinstallation", "RefUrl": "/notes/2357898 "}, {"RefNumber": "2356692", "RefComponent": "SLL-LEG-FUN", "RefTitle": "Uninstalling SAP Global Trade Services", "RefUrl": "/notes/2356692 "}, {"RefNumber": "2355120", "RefComponent": "CA-IAM-MOC", "RefTitle": "Management of Change 1.0: Uninstallation when converting to SAP S/4HANA", "RefUrl": "/notes/2355120 "}, {"RefNumber": "2333985", "RefComponent": "MOB-APP-OST", "RefTitle": "Uninstalling LWMSD001", "RefUrl": "/notes/2333985 "}, {"RefNumber": "2342708", "RefComponent": "MDM-FN-API-ABA", "RefTitle": "Uninstallation of add-on MDM Technology 710 731 and MDM Technology 710 700", "RefUrl": "/notes/2342708 "}, {"RefNumber": "2328813", "RefComponent": "MOB-APP-REX", "RefTitle": "Uninstalling MCRMRERP 200 with transaction SAINT", "RefUrl": "/notes/2328813 "}, {"RefNumber": "2244901", "RefComponent": "FS-BA-AN-REG", "RefTitle": "Uninstallation of RRA (Regulatory Reporting Analyzer by iBS)", "RefUrl": "/notes/2244901 "}, {"RefNumber": "2318477", "RefComponent": "MOB-APP-QIS", "RefTitle": "Uninstalling add-on LWMQAMMI (SAP ERP Quality Issue for iPhone) with transaction SAINT", "RefUrl": "/notes/2318477 "}, {"RefNumber": "2330289", "RefComponent": "MOB-APP-ISA", "RefTitle": "Uninstalling add-on EAM_WORK (SAP ERP COMPONENT FOR SAP EAM Work Order) with transaction SAINT", "RefUrl": "/notes/2330289 "}, {"RefNumber": "2330092", "RefComponent": "MOB-APP-SAL", "RefTitle": "Uninstalling add-on MCRMSERP (SAP ERP COMPONENT FOR CRM MOBILE SALES) with transaction SAINT", "RefUrl": "/notes/2330092 "}, {"RefNumber": "2312680", "RefComponent": "OPU-GW-CNT", "RefTitle": "Uninstallation of the add-ons IW_CNT 200 and IW_CBS 200 using transaction SAINT", "RefUrl": "/notes/2312680 "}, {"RefNumber": "2313222", "RefComponent": "OPU-BSC-SPI", "RefTitle": "Uninstallation of the add-on IW_SPI 100 using transaction SAINT", "RefUrl": "/notes/2313222 "}, {"RefNumber": "2319096", "RefComponent": "OPU-GW-COR", "RefTitle": "Uninstallation of the add-on IW_FNDGC 100 using transaction SAINT", "RefUrl": "/notes/2319096 "}, {"RefNumber": "2319097", "RefComponent": "OPU-GW-CNT", "RefTitle": "Uninstallation of the add-on IW_SCS 200 using transaction SAINT", "RefUrl": "/notes/2319097 "}, {"RefNumber": "2319114", "RefComponent": "OPU-BSC-GIL", "RefTitle": "Uninstallation of the add-on IW_GIL 100 using transaction SAINT", "RefUrl": "/notes/2319114 "}, {"RefNumber": "2319803", "RefComponent": "IS-DP-DMP", "RefTitle": "Uninstallation of add-on PSFMS 600", "RefUrl": "/notes/2319803 "}, {"RefNumber": "2319813", "RefComponent": "IS-DP-DMP", "RefTitle": "Uninstallation of add-on FMFMS 604", "RefUrl": "/notes/2319813 "}, {"RefNumber": "2324260", "RefComponent": "CRM-MSP", "RefTitle": "Uninstalling MOB_CRM 100 using transaction SAINT", "RefUrl": "/notes/2324260 "}, {"RefNumber": "2307624", "RefComponent": "EHS-BD-TLS", "RefTitle": "Uninstalling EHS Web-Interface 2.6", "RefUrl": "/notes/2307624 "}, {"RefNumber": "2298456", "RefComponent": "EHS-SRC", "RefTitle": "Uninstalling SAP Product and REACH Compliance (SPRC) 2.0 or Compliance for Products (CfP) 2.2 -  /TDAG/CPCL_S4_AOF_PLUGIN", "RefUrl": "/notes/2298456 "}, {"RefNumber": "2302601", "RefComponent": "EHS-SRC", "RefTitle": "S4CONV - Support of add-on deletion function", "RefUrl": "/notes/2302601 "}, {"RefNumber": "2310933", "RefComponent": "MM-FIO-PUR", "RefTitle": "Uninstalling SAP Fiori UI components UIS4HOP1 100 using transaction SAINT", "RefUrl": "/notes/2310933 "}, {"RefNumber": "2310351", "RefComponent": "XX-SER-REL", "RefTitle": "Release of Fiori Product Versions for SAP Fiori Frontendserver 3.0", "RefUrl": "/notes/2310351 "}, {"RefNumber": "2200415", "RefComponent": "XX-SER-REL", "RefTitle": "Release of Fiori Product Versions for SAP UI Addon 2.0 / SAP_UI 7.50", "RefUrl": "/notes/2200415 "}, {"RefNumber": "2301626", "RefComponent": "CA-TS", "RefTitle": "Uninstalling GBHCM001 600 (HCM Time Capture / Time sheet entry & approval request 600)", "RefUrl": "/notes/2301626 "}, {"RefNumber": "2203365", "RefComponent": "CA-LT-INS", "RefTitle": "Uninstalling DMIS 2011_1_7xx  / DMIS 2018_1_752", "RefUrl": "/notes/2203365 "}, {"RefNumber": "2280928", "RefComponent": "PP-FIO-MRP", "RefTitle": "Uninstalling SAP Fiori UI components UIPPDS01 100  using transaction SAINT", "RefUrl": "/notes/2280928 "}, {"RefNumber": "2280900", "RefComponent": "FI-GL", "RefTitle": "Uninstalling SAP Fiori UI components UIAPPL01 100  using transaction SAINT", "RefUrl": "/notes/2280900 "}, {"RefNumber": "2280715", "RefComponent": "CA-INB-FIO", "RefTitle": "Uninstalling SAP Fiori UI components UIX01CA1 200  using transaction SAINT", "RefUrl": "/notes/2280715 "}, {"RefNumber": "2276212", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UIAPFI70 300  from the Product versions SAP Fiori for SAP S/4 HANA 1511,  SAP Fiori for SAP  S/4 HANA Finance and SAP Smart Business for SAP S/4 HANA Finance ,on-premise edition 1602", "RefUrl": "/notes/2276212 "}, {"RefNumber": "2266130", "RefComponent": "CA-GTF-SB-S4H-DT", "RefTitle": "Uninstalling SAP Fiori UI components UISSB001 100  using transaction SAINT", "RefUrl": "/notes/2266130 "}, {"RefNumber": "2261544", "RefComponent": "PP-SFC-DMT", "RefTitle": "Uninstallation of the Software Component SFDM_ABAP 100  from the Add-on Product  version SAP ERP add-on for shop floor dispatching and monitoring tool 1.0", "RefUrl": "/notes/2261544 "}, {"RefNumber": "2180598", "RefComponent": "PA-FIO", "RefTitle": "Uninstalling SAP Fiori OData components GBX01HR", "RefUrl": "/notes/2180598 "}, {"RefNumber": "2242727", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of UI Component CC_PSIUI 200 from the Product Version SAP Convergent Pricing Simulation 2.0", "RefUrl": "/notes/2242727 "}, {"RefNumber": "2238575", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UIAPFI70 200  from the Product version SAP Fiori for SAP Simple Finance and SAP Smart Business for SAP Simple Finance ,on-premise edition 1503", "RefUrl": "/notes/2238575 "}, {"RefNumber": "2234715", "RefComponent": "MOB-APP-SRS", "RefTitle": "Uninstalling SAP Mobile OData add-on LWMSIM01  for SAP RealSpend 1.0.0", "RefUrl": "/notes/2234715 "}, {"RefNumber": "2232809", "RefComponent": "BC-PER", "RefTitle": "Uninstalling Components  PERSOS 200 and PERSONAS 300 using transaction SAINT (SAP Screen Personas 3.0)", "RefUrl": "/notes/2232809 "}, {"RefNumber": "2230429", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UIMDC001 100  from the Product Version SAP Fiori 1.0 for SAP Master Data Governance", "RefUrl": "/notes/2230429 "}, {"RefNumber": "2229796", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UIFM001 100  from the Product version SAP Fiori 1.0 for SAP Fashion Management", "RefUrl": "/notes/2229796 "}, {"RefNumber": "2227939", "RefComponent": "LOD-TEM-IMD", "RefTitle": "Deinstallation of SAP Cloud for Travel and Expense add-ons OTM_EXTR, ODTHCMER, ODTGEN, ODTFINCO, ODTFINCC, TEMODFI and DCFLPROC", "RefUrl": "/notes/2227939 "}, {"RefNumber": "2228009", "RefComponent": "LOD-TEM-IMD", "RefTitle": "Deinstallation of obsolete SAP Cloud for Travel and Expense Add-Ons NWSEXTFW, TEMEXFIN and TEMEXHCM", "RefUrl": "/notes/2228009 "}, {"RefNumber": "2229040", "RefComponent": "CRM-FIO-BP", "RefTitle": "Uninstalling SAP Fiori OData components GBX02CRM 700 using transaction SAINT", "RefUrl": "/notes/2229040 "}, {"RefNumber": "2160664", "RefComponent": "PP-FIO-SFC", "RefTitle": "Uninstalling SAP Fiori OData components GBX02SAP 600, GBX02EAP 600, GBX02SA4 604, GBX02RT4 604 using transaction SAINT", "RefUrl": "/notes/2160664 "}, {"RefNumber": "2209288", "RefComponent": "CA-MDG-UI5", "RefTitle": "Uninstalling SAP Fiori UI components UIMDG001 100  using transaction SAINT", "RefUrl": "/notes/2209288 "}, {"RefNumber": "2209387", "RefComponent": "CA-TDM-MOB", "RefTitle": "Uninstalling SAP Fiori UI components UICSLO01 100  using transaction SAINT", "RefUrl": "/notes/2209387 "}, {"RefNumber": "2209418", "RefComponent": "SCM-EM-MGR", "RefTitle": "Uninstalling SAP Fiori UI components UIEM001 100  using transaction SAINT", "RefUrl": "/notes/2209418 "}, {"RefNumber": "2203656", "RefComponent": "SCM-BAS-FIO", "RefTitle": "Uninstalling SAP Fiori UI components SCMB_UI 100  using transaction SAINT", "RefUrl": "/notes/2203656 "}, {"RefNumber": "2203681", "RefComponent": "SCM-BAS-FIO", "RefTitle": "Uninstalling SAP Fiori UI components UIEHSM01 100  using transaction SAINT", "RefUrl": "/notes/2203681 "}, {"RefNumber": "2206380", "RefComponent": "MOB-APP-TEA", "RefTitle": "Uninstalling LWMCR002 700 and LWMCCERP  600  with transaction SAINT", "RefUrl": "/notes/2206380 "}, {"RefNumber": "2217323", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UIFND001 100 from the Product  version SAP Fiori 1.0 for SAP Business Suite foundation component", "RefUrl": "/notes/2217323 "}, {"RefNumber": "2217321", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UMCUI501 100 from the Product  version SAP Multichannel Foundation for Utilities and Public Sector 1.0", "RefUrl": "/notes/2217321 "}, {"RefNumber": "2217279", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UICPD001 100 from the Product  version SAP Fiori 1.0 for SAP Commercial Project Management", "RefUrl": "/notes/2217279 "}, {"RefNumber": "2217260", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UIX01SRM 100 from the Product  version SAP Fiori principal apps 1.0 for SAP SRM", "RefUrl": "/notes/2217260 "}, {"RefNumber": "2217255", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UIX01CA1 100 from the Product  version SAP Fiori for request approvals 1.0", "RefUrl": "/notes/2217255 "}, {"RefNumber": "2217230", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UISCAR01 100 and UIRAP001 100  from the Product  version SAP Fiori 1.0 for SAP Customer Activity Repository retail applications bundle", "RefUrl": "/notes/2217230 "}, {"RefNumber": "2209914", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of  the Add-on Product  version SAP In-Store Product Lookup 1.0.0", "RefUrl": "/notes/2209914 "}, {"RefNumber": "2200172", "RefComponent": "GRC-SAC-ARA", "RefTitle": "Uninstalling SAP Fiori UI components UIHGRC01 100  using transaction SAINT", "RefUrl": "/notes/2200172 "}, {"RefNumber": "2200656", "RefComponent": "CEC-MKT-ITC", "RefTitle": "Uninstalling SAP Fiori UI components UICUAN 100/120  using transaction SAINT", "RefUrl": "/notes/2200656 "}, {"RefNumber": "2189023", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of  UIX02CRM and UICRM001 from the Product Version SAP Fiori transactional apps 1.0 for SAP CRM ( or Uninstallation of UICRM001 from SAP Fiori 1.0 for SAP CRM )", "RefUrl": "/notes/2189023 "}, {"RefNumber": "2195588", "RefComponent": "MOB-APP-TRC", "RefTitle": "Uninstalling MIVHRTRV", "RefUrl": "/notes/2195588 "}, {"RefNumber": "2195035", "RefComponent": "BC-ILM-WP", "RefTitle": "Uninstalling SAP Fiori UI components UIILM001 100  using transaction SAINT", "RefUrl": "/notes/2195035 "}, {"RefNumber": "2202923", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Software Component NWMQC01 100  from the Product  version SAP AR Warehouse Picker 1.0, QR code generator add-on", "RefUrl": "/notes/2202923 "}, {"RefNumber": "2201905", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UISFM001 100 from the Product  version SAP Smart Business 1.0 for SAP Fashion Management", "RefUrl": "/notes/2201905 "}, {"RefNumber": "2201856", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UISAFND1 100 from the Product  version SAP Smart Business 1.0 foundation component or from the Product version SAP HANA Live tools 1.0", "RefUrl": "/notes/2201856 "}, {"RefNumber": "2201852", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UISRTL01 100 from the Product  version SAP Smart Business 1.0 for retail promotion execution", "RefUrl": "/notes/2201852 "}, {"RefNumber": "2201850", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UISDTG01 100 from the Product  version SAP Smart Business 1.0 for SAP Information Lifecycle Management", "RefUrl": "/notes/2201850 "}, {"RefNumber": "2201846", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UIHSFIN1 100 from the Product  version SAP Smart Business for the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/2201846 "}, {"RefNumber": "2201843", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UIHPLM01 100 from the Product  version SAP Smart Business 1.0 for SAP PLM", "RefUrl": "/notes/2201843 "}, {"RefNumber": "2200413", "RefComponent": "CRM-BF-DCN", "RefTitle": "Uninstalling CRMGWS", "RefUrl": "/notes/2200413 "}, {"RefNumber": "2176792", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UISRM200 100  from the Product  version UI FOR EHP3 FOR SAP SRM 7.0", "RefUrl": "/notes/2176792 "}, {"RefNumber": "2176779", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UISKPI01 100 from the Product  version KPI MODELER 1.0", "RefUrl": "/notes/2176779 "}, {"RefNumber": "2176775", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UISERP01 100 from the Product  version   SAP SMART BUSINESS FOR ERP 1.0", "RefUrl": "/notes/2176775 "}, {"RefNumber": "2176772", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UISCRM01 100 from the Product  version SAP SMART BUSINESS FOR CRM 1.0", "RefUrl": "/notes/2176772 "}, {"RefNumber": "2176725", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UIPPM001 100 from the Product  version UI FOR SAP PORTF PROJ MGMT 6.0", "RefUrl": "/notes/2176725 "}, {"RefNumber": "2176724", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UIHSCM01 100 from the Product  versions  SAP ANALYTIC APPS FOR APO 1.0 and SAP SMART BUSINESS FOR APO 1.0", "RefUrl": "/notes/2176724 "}, {"RefNumber": "2176696", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  UIGRC001 100 from the Product  version SAP FIORI FOR SAP GRC 1.0", "RefUrl": "/notes/2176696 "}, {"RefNumber": "2176346", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component  SCMSNCE1 100 from the Product  version SAP Supply Network Collaboration, UI add-on for purchase order collaboration 1.0", "RefUrl": "/notes/2176346 "}, {"RefNumber": "2167334", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of FIORI UI Components UIX01EAP 100 and UIX01TRV 100  from the Product Version SAP Fiori principal apps 1.0 for SAP ERP", "RefUrl": "/notes/2167334 "}, {"RefNumber": "2134432", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of  Fiori UI Components from the Product Version SAP Fiori for SAP ERP 1.0", "RefUrl": "/notes/2134432 "}, {"RefNumber": "2167372", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UIHR001 100 from the Product  version SAP Fiori 1.0 for SAP ERP HCM", "RefUrl": "/notes/2167372 "}, {"RefNumber": "2162719", "RefComponent": "MOB-APP-TER", "RefTitle": "Uninstalling GBTRV001", "RefUrl": "/notes/2162719 "}, {"RefNumber": "2149065", "RefComponent": "LO-BM-GBT", "RefTitle": "Uninstalling Add-On GBTRINT", "RefUrl": "/notes/2149065 "}, {"RefNumber": "2125074", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of FIORI UI Components  from the Product Version SAP Fiori transactional apps for SAP ERP 1.0", "RefUrl": "/notes/2125074 "}, {"RefNumber": "2132758", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI Component UIHFND01 100 from the Product  versions SAP Fiori for SAP HANA Live for SAP Business Suite foundation component 1.0 and SAP Smart Business 1.0 for SAP Business Suite foundation component", "RefUrl": "/notes/2132758 "}, {"RefNumber": "2131580", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of the Fiori UI component UIHERP01 100 from the Product Versions  SAP Fiori for SAP HANA Live for SAP ERP 1.0 and SAP Smart Business 1.0 for SAP ERP", "RefUrl": "/notes/2131580 "}, {"RefNumber": "2144806", "RefComponent": "XX-SER-REL", "RefTitle": "Uninstallation of UIAPFI70 100 and UIFSCM70 100  from the Product Version SAP Fiori 1.0 for the SAP Simple Finance add-on for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/2144806 "}, {"RefNumber": "2131368", "RefComponent": "MM-FIO-PUR", "RefTitle": "Uninstalling SAP Fiori OData component GBAPP002 600 using transaction SAINT", "RefUrl": "/notes/2131368 "}, {"RefNumber": "2131383", "RefComponent": "PA-FIO", "RefTitle": "Uninstalling SAP Fiori OData component GBHCM003 600 using transaction SAINT", "RefUrl": "/notes/2131383 "}, {"RefNumber": "2131381", "RefComponent": "PA-FIO", "RefTitle": "Uninstalling SAP Fiori OData component GBHCM002 600 using transaction SAINT", "RefUrl": "/notes/2131381 "}, {"RefNumber": "2131310", "RefComponent": "MM-FIO-PUR", "RefTitle": "Using transaction SAINT to uninstall SAP Fiori OData component SRA013 1.0", "RefUrl": "/notes/2131310 "}, {"RefNumber": "2131364", "RefComponent": "SD-FIO-BIL", "RefTitle": "Uninstalling SAP Fiori OData component SRA021 1.0 using transaction SAINT", "RefUrl": "/notes/2131364 "}, {"RefNumber": "2131360", "RefComponent": "MM-FIO-PUR", "RefTitle": "Uninstalling SAP Fiori OData component SRA020 1.0 using transaction SAINT", "RefUrl": "/notes/2131360 "}, {"RefNumber": "2131358", "RefComponent": "LE-FIO", "RefTitle": "Uninstalling SAP Fiori OData component SRA019 1.0 with transaction SAINT", "RefUrl": "/notes/2131358 "}, {"RefNumber": "2131353", "RefComponent": "SD-FIO-SLS", "RefTitle": "Uninstalling SAP Fiori OData component SRA018 1.0 using transaction SAINT", "RefUrl": "/notes/2131353 "}, {"RefNumber": "2131352", "RefComponent": "SD-FIO-SLS", "RefTitle": "Uninstalling SAP Fiori OData component SRA017 1.0 using transaction SAINT", "RefUrl": "/notes/2131352 "}, {"RefNumber": "2131351", "RefComponent": "SD-FIO-SLS", "RefTitle": "Uninstalling SAP Fiori OData component SRA016 1.0 using transaction SAINT", "RefUrl": "/notes/2131351 "}, {"RefNumber": "2131303", "RefComponent": "CO-FIO", "RefTitle": "Uninstalling SAP Fiori OData component SRA012 1.0 using transaction SAINT", "RefUrl": "/notes/2131303 "}, {"RefNumber": "2131301", "RefComponent": "PA-FIO", "RefTitle": "Uninstalling SAP Fiori OData component SRA010 1.0 using transaction SAINT", "RefUrl": "/notes/2131301 "}, {"RefNumber": "2131278", "RefComponent": "FI-FIO-TV-ATR", "RefTitle": "Using transaction SAINT to uninstall SAP Fiori OData component SRA009 1.0", "RefUrl": "/notes/2131278 "}, {"RefNumber": "2131186", "RefComponent": "PA-FIO", "RefTitle": "Uninstalling SAP Fiori OData component SRA006 1.0 using transaction SAINT", "RefUrl": "/notes/2131186 "}, {"RefNumber": "2131147", "RefComponent": "PA-FIO", "RefTitle": "Uninstalling SAP Fiori OData component SRA002 1.0 using transaction SAINT", "RefUrl": "/notes/2131147 "}, {"RefNumber": "2128051", "RefComponent": "MM-FIO-PUR", "RefTitle": "Using transaction SAINT to uninstall SAP Fiori OData component SRA001 1.0", "RefUrl": "/notes/2128051 "}, {"RefNumber": "2131274", "RefComponent": "FI-FIO-TV-ATE", "RefTitle": "Using transaction SAINT to uninstall SAP Fiori OData component SRA008 1.0", "RefUrl": "/notes/2131274 "}, {"RefNumber": "2131187", "RefComponent": "PA-FIO", "RefTitle": "Uninstalling SAP Fiori OData component SRA007 1.0 using transaction SAINT", "RefUrl": "/notes/2131187 "}, {"RefNumber": "2131183", "RefComponent": "FI-FIO-TV-MTR", "RefTitle": "Using transaction SAINT to uninstall SAP Fiori OData component SRA004 1.0", "RefUrl": "/notes/2131183 "}, {"RefNumber": "2131814", "RefComponent": "SD-FIO-SLS", "RefTitle": "Uninstalling SAP Fiori OData component SRA003 1.0 using transaction SAINT", "RefUrl": "/notes/2131814 "}, {"RefNumber": "2124793", "RefComponent": "MOB-APP-TEA", "RefTitle": "Uninstalling GBTRV002", "RefUrl": "/notes/2124793 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "DEV", "To": "DEV", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}