{"Request": {"Number": "654800", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 263, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015712202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000654800?language=E&token=DBB6F9F3C834D0B635EDB7B84A575A7A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000654800", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000654800/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "654800"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 54}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB4"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 for AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 for AS/400", "value": "BC-DB-DB4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "654800 - FAQ: JDBC driver certified for SAP on IBM i"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note collects a series of current facts for using the JDBC drivers permitted by SAP for the database Db2 for i (DB2/400).</p>\r\n<ol><ol>\r\n<li>IBM i Toolbox JDBC driver (toolbox driver) or \"JTOpen\"</li>\r\n<li>IBM i Native JDBC driver (native driver) - <strong>at present, no further support</strong></li>\r\n</ol></ol>\r\n<p>You should read this SAP Note if one of the following cases applies to you:</p>\r\n<ol>\r\n<li>You are planning to install an SAP product on IBM i that requires the use of a JDBC driver. This applies to all SAP products that use a J2EE Engine 6.40 or higher.</li>\r\n<li>You are using an SAP product on any SAP platform and are planning a project that enables the connection of a Db2 for i using JDBC (multi-connect). In this case, the connectivity of SQL schemata that were not originally designed to be accessed by JDBC from an SAP application is subject to certain (partly obvious) restrictions. This also applies in particular to the R3&lt;SID&gt;DATA SQL schema reserved for the ABAP part of an SAP installation. Java multi-connect is described in <a target=\"_blank\" href=\"/notes/907733\">SAP Note 907733</a>.</li>\r\n<li>You want to keep up-to-date with current developments regarding the configuration, support or patch strategies for the JDBC drivers mentioned above in the context of an SAP application.</li>\r\n<li>Special scenario:&#x00A0;You are considering the connection of an IBM Db2 for i in an SAP product on an SAP platform that allows you to use JDBC in principle but neither requires it nor guarantees that it will work without any problems. In this case, it may be useful to refer to this note when converting the planned project. However, this is not a guarantee of success.</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>AS/400, AS400, DB4, DB2/400, JDBC driver, native driver, toolbox driver, J9, SAPJVM, IBM i, JTOpen<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note provides answers to the following questions in several sections:<br /><br /><strong>Basics</strong></p>\r\n<p style=\"padding-left: 30px;\">1. What is JDBC?</p>\r\n<p style=\"padding-left: 30px;\">2. What should I know about the configuration of JDBC in conjunction with SAP?</p>\r\n<p><strong>Two JDBC drivers for IBM i and their consequences:</strong></p>\r\n<ol>3. \"Toolbox driver\" - what is this?</ol><ol>4. \"Native driver\" - what is this?</ol><ol>5. Why are there two JDBC drivers?</ol><ol>6. When do I use the native driver and when do I use the toolbox driver?</ol><ol>7. Can I use both drivers in parallel in the same SAP installation?</ol><ol>8. Can I reconfigure the system driver of an existing SAP installation from toolbox to native driver or the other way around?</ol><ol>9. Which driver versions are compatible with which SAP, database, JVM, and operating system versions?</ol>\r\n<p><br /><strong>Configuration</strong></p>\r\n<ol>10. Which connection parameters are recommended for use with SAP?</ol><ol>11. Why does the URL provided by SAP for the native driver not correspond to  the URL documented by IBM?</ol><ol>12. How do I use JDBC when accessing external data sources?</ol><ol>13. Does SAP use IBM i exit programs for JDBC?</ol><ol>14. Will my iASP solution also work with J2EE?</ol>\r\n<p><br /><strong>Driver updates</strong></p>\r\n<ol>15. Where can I find the driver executables in my SAP installation?</ol><ol>16. Where can I order driver updates?</ol><ol>17. What is the relationship between the toolbox driver and JTOpen?</ol><ol>18. How can I find out which driver version I am using?</ol><ol>19. Where can I find out the current driver version?</ol><ol>20. How do I include a driver update in my SAP installation?</ol><ol>21. JDBC 3.0 compared with JDBC 4.0?</ol><ol>22. Special notes about individual driver versions</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>Basics</strong></p>\r\n<p><strong>1. What is JDBC?</strong><br />JDBC stands for \"Java Database Connectivity\". The \"JDBC interface\" was developed by SUN. It provides a number of programming interfaces for dynamic SQL database access using Java and is available in various versions. A \"JDBC driver\" is an actual implementation of this interface that is specially tailored to certain database systems (usually just one).</p>\r\n<p><strong>2. What should I know about the configuration of JDBC in conjunction with SAP?</strong><br />Each JDBC driver is identified or configured by two central characteristics:</p>\r\n<ol>\r\n<li>The central and explicitly identifying driver class (for example, \"com.ibm.as400.access.AS400JDBCDriver\")</li>\r\n<li>The JDBC URLs accepted by it (for example, \"************************;;&lt;properties&gt;\")</li>\r\n</ol>\r\n<p>JDBC enables a Java Virtual Machine (JVM) to load and use several JDBC drivers in parallel. To do this, the driver class must register with its JVM. The following queries are typically requests to set up a database connection whose properties are characterized by the JDBC URL (\":&lt;host&gt;;&lt;properties&gt;\"). In addition, the first two components of the URL (protocol and sub-protocol) explicitly determine which of the registered JDBC drivers is to control the connection. For example, all URLs starting with \"jdbc:as400\" are explicitly reserved for the IBM i toolbox JDBC driver.</p>\r\n<p>JDBC also provides the option of checking the structure of database connections using driver-specific data sources (for the toolbox driver, for example, \"com.ibm.as400.access.AS400JDBCDataSource\"), which, along with connect information, also encapsulates administration services such as connection pooling and can be called dynamically using JNDI.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Two JDBC drivers for SAP on IBM i and their consequences:</strong></span></p>\r\n<p><strong>3. \"Toolbox driver\" - what is this?</strong><br />The \"Toolbox JDBC driver\" or open source variant \"JTOpen\" is a type 4 JDBC driver for Db2 for IBM i. Type 4 means that the client parts of the driver are coded only in Java and can therefore be operated on every JVM that supports JDBC, regardless of the platform. Communication with the database back end occurs for the toolbox driver (in the same way as for the IBM i ODBC driver) by using direct TCP socket calls to the host server component (ZDA). The driver-supplier is IBM.</p>\r\n<p>Up to and including V6R1, the driver is part of the product \"IBM toolbox for Java\" (57xxJC1). As of IBM 7.1, it will be provided as part of product 57xxSS1, option 3.</p>\r\n<ul>\r\n<li>Driver class: com.ibm.as400.access.AS400JDBCDriver</li>\r\n</ul>\r\n<ul>\r\n<li>URL: ************************;;&lt;properties&gt;</li>\r\n</ul>\r\n<ul>\r\n<li>Data sources:<br /><br />com.ibm.as400.access.AS400JDBCDataSource<br />com.ibm.as400.access.AS400JDBCPooledConnectionDataSource<br />com.ibm.as400.access.AS400JDBCXADataSource</li>\r\n</ul>\r\n<p>For a number of years, IBM has also offered the largely code-identical driver as an open source variant called \"JTOpen\".</p>\r\n<p>For more information, see the <em>IBM i documentation:</em>&#x00A0;<a target=\"_blank\" href=\"https://www.ibm.com/docs/en/i\">https://www.ibm.com/docs/en/i</a>.</p>\r\n<p><strong>4. \"Native driver\" - what is this?</strong><br /><br /><strong>The native driver will not be supported by SAP in combination with SAP JVM until further notice, which has the following implications:</strong></p>\r\n<ul>\r\n<li><strong>No support for SAP products in Basis Release 7.10 and higher</strong></li>\r\n</ul>\r\n<ul>\r\n<li><strong>No support for EHPI installations with target Basis Release 7.01 and higher since the tool itself uses SAP JVM</strong></li>\r\n</ul>\r\n<ul>\r\n<li><strong>No support for products in Basis Release 6.40 and higher after changing to SAP JVM 1.4.2. (For detailed information about this, see <a target=\"_blank\" href=\"/notes/1495160\">SAP Note 1495160</a>.)</strong></li>\r\n</ul>\r\n<p><strong>You must therefore convert the JDBC driver before a relevant upgrade at the latest. The procedure is described in <a target=\"_blank\" href=\"/notes/654800\">SAP Note 654800</a>.</strong><br /><br />The \"Native JDBC driver\" is a type 2 JDBC driver for IBM DB2 for i. Type 2 means that the Java parts of the driver serve additional non-Java software on the client. The client installation is therefore generally platform-dependent. You can operate the native driver only on IBM i. Communication with the database back end occurs locally by using the Call Level Interface (CLI) or (remotely) under the additional use of DRDA.<br /><br />Although provision is made for accessing remote (IBM i) databases, the driver was developed and optimized primarily for local use on the database server. The driver is supplied by IBM Rochester. The driver is part of the product \"AS/400 Developer Kit for Java\" (57xxJV1).<br /><br /></p>\r\n<ul>\r\n<li>Driver class: com.ibm.db2.jdbc.app.DB2iSeriesDriver</li>\r\n</ul>\r\n<ul>\r\n<li>URL: *****************************************************;</li>\r\n</ul>\r\n<ul>\r\n<li>Data sources:<br /><br />com.ibm.db2.jdbc.app.UDBDataSource<br />com.ibm.db2.jdbc.app.UDBPooledConnectionDataSource<br />com.ibm.db2.jdbc.app.UDBXADataSource<br /><br />Note that instead of a host name, the native driver requires a relational database name (RDB name) for the connect because the RDB name can be determined with WRKRDBDIRE. The connection to a database in an iASP is also carried out in this way. However, you normally use the value \"*LOCAL\". \"*LOCAL\" opens a connection to the system ASP. For more information, see \"Will my iASP solution also work with J2EE?\".</li>\r\n</ul>\r\n<ul>\r\n<li>Example: URL for a connection to the system ASP (with additional properties):<br /><br />*************************** truncation=true;...</li>\r\n</ul>\r\n<ul>\r\n<li>Example: URL for the connection to the iASP group MY_IASP:<br />**************************** truncation=true;...<br /><br /></li>\r\n</ul>\r\n<p><strong>5. Why are there two JDBC drivers?</strong><br />Toolbox and native JDBC drivers were designed and optimized for different purposes:</p>\r\n<ul>\r\n<li>The toolbox driver is suitable for the straightforward use of application servers on any operating systems and therefore generally conforms to the Java philosophy of platform independence. Particular attention is devoted to efficient network communication.</li>\r\n</ul>\r\n<ul>\r\n<li>The native driver was developed primarily to optimize access to a local DB2 for IBM i (without network communication). This is achieved by the fact that central parts of the driver were not implemented in Java, but were optimized for IBM i.</li>\r\n</ul>\r\n<p><strong>6. When do I use the native driver and when do I use the toolbox driver?</strong><br />We generally recommend that you use the toolbox JDBC driver or the open source variant JTOpen.</p>\r\n<p>You can use the native JDBC as a system driver in an SAP installation as long as SAPJVM is not used and provided that all J2EE instances of an SAP SID are in the same IBM i partition as the SAP system database. We define a system JDBC driver as the driver that a J2EE server uses to communicate with its database.<strong><span style=\"text-decoration: underline;\"><br /></span></strong></p>\r\n<p><strong>7. Can I use both drivers in parallel in the same SAP installation?</strong><br />Only one JDBC driver can be configured as a system JDBC driver within an SAP installation. Accordingly, it is NOT possible to configure an installation comprising an IBM i J2EE instance on the database server and a J2EE instance on an additional application server so that only the remote instance uses the toolbox driver, while the local instance uses the native driver. If all J2EE instances of an SAP installation are on the database server and SAPJVM is not used, then you can use the native driver.</p>\r\n<p>Apart from the configuration as a system JDBC driver, in a local installation for which the native driver is already configured as a system JDBC driver, it is theoretically possible to use a toolbox driver as follows:</p>\r\n<ul>\r\n<li>As a secondary driver for the connect to another, possibly also remote, IBM i database (Java multiconnect)</li>\r\n</ul>\r\n<ul>\r\n<li>As a driver for operating the J2EE config tool (see <a target=\"_blank\" href=\"/notes/657117\">SAP Note 657117</a>)</li>\r\n</ul>\r\n<p><strong>8. Can I reconfigure the system driver of an existing SAP installation from toolbox to native driver or the other way around?</strong><br />Changing the JDBC driver is supported. This is necessary if, for example, you need to add a J2EE dialog instance on another application server to an SAP installation with a native driver as a system driver or if SAP JVM is to be used. In this case, you must configure toolbox JDBC drivers as system drivers. The procedure for the change is described in <a target=\"_blank\" href=\"/notes/826449\">SAP Note 826449</a>.</p>\r\n<p><strong>9. Which driver versions are compatible with which SAP, database, JVM, and operating system versions?</strong><br /><br />The <strong>Native JDBC driver</strong> is bound, by its native parts, to the IBM i on which it was installed (and therefore also to a particular operating system version). Since it was not released for remote database servers, the compatible Java Virtual Machine and database versions are derived automatically.</p>\r\n<p>On the other hand, the following rules of thumb apply to the compatibility of the <strong>Toolbox JDBC driver</strong>:</p>\r\n<ul>\r\n<li><strong>JDBC &lt;-&gt; Database:</strong><br /><br />The Toolbox JDBC driver of a certain database version is completely downward compatible for all database versions currently released by IBM, with their relevant restrictions. However, to use it properly with SAP (also in the context of multi-connect), you require at least database release V5R2M0.&#x00A0;<br /><br />The reverse is not the case: The JDBC driver of a certain database release is not upward compatible for future database releases. In particular, you should always update your JDBC driver, too, after you upgrade your operating system.<br /><br />The same applies to JTOpen; see section 17 (\"What is the relationship between the toolbox driver and JTOpen?\").<br /><br /></li>\r\n</ul>\r\n<ul>\r\n<li><strong>JDBC &lt;-&gt; JVM:</strong><br /><br />The toolbox JDBC driver of a particular operating system version is always fully compatible with all Java Virtual Machine versions that can be installed there when the relevant operating system version becomes generally available, and that were being maintained. The drivers then adjust to fit the JDBC specification used in each case. This applies not only to the IBM Classic JVM but also to IT4J (which is also called J9) and SAPJVM.<br /><br /></li>\r\n</ul>\r\n<ul>\r\n<li><strong>JDBC &lt;-&gt; Operating system:</strong><br /><br />The Toolbox JDBC driver can be used on all operating systems (including non-IBM i operating systems) for which a valid JVM version is available (see above).<br /><br />The SAP recommendation that you use the latest version of the Toolbox driver in the most current operating system for database releases that are still being maintained arises from this. IBM supports this procedure by offering an open source version (JTOpen) of the toolbox driver that can be downloaded from the Web (see below).<br /><br />The specific driver combinations that are released by SAP are listed in <a target=\"_blank\" href=\"/notes/1393104\">SAP Note 1393104</a>.<br /><br /><br /></li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong>Configuration</strong></span></p>\r\n<p><strong>10. Which connection parameters are recommended for use with SAP?</strong><br />To ensure correct operation with SAP, you must set the following connection parameters for URLs or when you configure data sources that point to the SAP Java database schema (\"SAP&lt;sid&gt;DB\"):</p>\r\n<ul>\r\n<li>transaction isolation=read uncommitted</li>\r\n</ul>\r\n<ul>\r\n<li>data truncation=true</li>\r\n</ul>\r\n<ul>\r\n<li>time format=jis</li>\r\n</ul>\r\n<ul>\r\n<li>date format=jis</li>\r\n</ul>\r\n<ul>\r\n<li>cursor hold=false (see SAP Note 699410)</li>\r\n</ul>\r\n<p><br />For the native driver, also set</p>\r\n<ul>\r\n<li>behavior override=2</li>\r\n</ul>\r\n<ul>\r\n<li>auto commit=true</li>\r\n</ul>\r\n<p><br />For the toolbox driver, also set:</p>\r\n<ul>\r\n<li>sort=hex</li>\r\n</ul>\r\n<ul>\r\n<li>hold input locators=true</li>\r\n</ul>\r\n<ul>\r\n<li>hold statements=true</li>\r\n</ul>\r\n<ul>\r\n<li>prompt=false</li>\r\n</ul>\r\n<ul>\r\n<li>true autocommit=true</li>\r\n</ul>\r\n<ul>\r\n<li>bidi string type=-1</li>\r\n</ul>\r\n<ul>\r\n<li>use block update=true (new for JTOpen 7.1)</li>\r\n</ul>\r\n<p><br />For more information about configuring connections to non-SAP data sources (for example, using the SAP PI JDBC adapter), see the \"How do I use JDBC when accessing external data sources?\" section.</p>\r\n<p><strong>11. Why does the driver class provided by SAP for the native driver and the URL differ from the values documented at IBM?</strong><br />For</p>\r\n<ol><ol>\"com.ibm.db2.jdbc.app.DB2Driver\" (\"jdbc:db2:\") and</ol></ol><ol><ol>\"com.ibm.db2.jdbc.app.DB2iSeriesDriver\" (\"jdbc:db2iSeries:\"),</ol></ol>\r\n<p>these are entry points into the same native driver functions documented by IBM. The second entry point for operation with SAP became necessary to ensure uninterrupted parallel use with \"Java Common Client\" (JCC), which also uses db2 as a sub protocol.</p>\r\n<p><strong>12. How do I use JDBC when accessing external data sources? </strong><br />As external data sources, we mean all that do not correspond to the SAP Java database schema SAP&lt;sid&gt;DB. In particular, we are referring only to external DB2 for IBM i data sources.</p>\r\n<p>From the SAP point of view, these types of connections typically arise from one of the following SAP applications:</p>\r\n<ul>\r\n<li>PI JDBC adapter</li>\r\n</ul>\r\n<ul>\r\n<li>Portal JDBC adapter</li>\r\n</ul>\r\n<ul>\r\n<li>BI universal data connector (UDC)</li>\r\n</ul>\r\n<p>For general information about configuring these types of connections, see <a target=\"_blank\" href=\"/notes/924753\">SAP Note 924753</a>.<br /><br />For information on accessing IBM i using the <strong>PI JDBC adapter</strong>, you must refer to <a target=\"_blank\" href=\"/notes/1385571\">SAP Note 1385571</a>, too.</p>\r\n<p><strong>13. Does SAP use IBM i exit programs for JDBC?</strong><br />To ensure the additional preconfiguration of the database access immediately after the connect, SAP occupies the IBM i starting points QIBM_QSQ_CLI_CONNECT and QIBM_QZDA_INIT with the main programs R3SYS/CLIEXIT and R3SYS/ZDAEXIT on the database server. These do not displace the exit programs that existed at the time of the SAP installation, but are automatically integrated, meaning their call is arranged by the respective SAP main program. For a detailed description of the mechanism, see <a target=\"_blank\" href=\"/notes/654794\">SAP Note 654794</a>.</p>\r\n<p><strong>14. Will my iASP solution also work with J2EE?</strong><br />J2EE can also be operated in iASP. Further details are provided in <a target=\"_blank\" href=\"/notes/568820\">SAP Note 568820</a>. The iASP group &lt;iasp&gt; is specified as part of the JDBC URL:</p>\r\n<ul>\r\n<li>Toolbox driver: ************************;;<strong>database name=&lt;iasp&gt;</strong>;&lt;properties&gt;;</li>\r\n</ul>\r\n<ul>\r\n<li>Native driver: jdbc:db2iSeries:<strong>&lt;iasp&gt;</strong>;&lt;properties&gt;;<br /><br /><br /></li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong>Driver updates</strong></span></p>\r\n<p><strong>15. Where can I find the driver executables in my SAP installation?</strong><br />A J2EE instance normally references the drivers configured in \"/usr/sap/&lt;SID&gt;/SYS/jdbc\":</p>\r\n<ul>\r\n<li>Toolbox driver: /usr/sap/&lt;SID&gt;/SYS/jdbc/tbx/jt400.jar</li>\r\n</ul>\r\n<ul>\r\n<li>Native driver: /usr/sap/&lt;SID&gt;/SYS/jdbc/ntv/db2_classes.jar</li>\r\n</ul>\r\n<p>Also consider the following:</p>\r\n<ul>\r\n<li>On IBM i instances, these should be links that lead to a physical file on the database server.<br /><br />For the toolbox driver, there should be one physical executable for each SAP SID in \"/QFileSvr.400/&lt;SAPGLOBALHOST&gt;/sapmnt/&lt;SID&gt;/SYS/jdbc/tbx/jt400.jar\". Otherwise, there are problems when operating a Linux application server.<br /><br />On the other hand, for the native driver, \"/sapmnt/&lt;SID&gt;/SYS/jdbc/ntv/db2_classes.jar\" must always be a link to \"/QIBM/ProdData/OS400/Java400/ext/db2_classes.jar\".</li>\r\n</ul>\r\n<ul>\r\n<li>Windows dialog instances:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In 6.40, the application server also references the driver using the path \"/usr/sap/&lt;SID&gt;/SYS/jdbc/tbx/jt400.jar\", although on Windows this is a local copy of the driver. This means that the Windows application servers need to be patched separately.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>As of 7.00, the application server uses the driver using the central \"sapmnt\" share \"\\\\&lt;SAPGLOBALHOST&gt;\\sapmnt\\&lt;SID&gt;\\SYS\\jdbc\".<br /><br />For more information about this topic, see <a target=\"_blank\" href=\"/notes/809693\">SAP Note 809693</a>.<br /><strong>We recommend that you only change the configuration described above in urgent cases</strong></li>\r\n</ul>\r\n</ul>\r\n<p><strong>16. Where can I order driver updates?</strong><br />All driver updates are provided by IBM.</p>\r\n<ul>\r\n<li><strong>Native driver:</strong> Driver updates are delivered exclusively as PTFs.<br /><br /></li>\r\n<li><strong>Toolbox driver:<br /><br /></strong></li>\r\n<ul>\r\n<li><strong>PTF process</strong> In principle, this driver is also always associated with the PTF process. PTFs update both its JDBC 3.0 API Version and, as of IBM i 7.1, its JDBC 4.0 API Version.<br /><br />JDBC 3.0: /QIBM/Proddata/HTTP/public/jt400/lib/jt400.jar<br /><br />JDBC 4.0: /QIBM/Proddata/HTTP/public/jt400/lib/<strong>java6</strong>/jt400.jar.<br /><br />(For more information about the JDBC version, see the section \"JDBC 3.0 versus JDBC 4.0\" in this note.)<br /><br />However, you must note that these automatically updated driver instances are not used directly by SAP at present; instead, the SAP system uses only a local copy in the directory \"/QFileSvr.400/&lt;SAPGLOBALHOST&gt;/sapmnt/&lt;SID&gt;/SYS/jdbc/tbx\". (For more information, see \"Where can I find the driver executables in my SAP installation?\".)<br /><br />Maintenance only via the PTF process is therefore insufficient. It also involves manual effort.<br /><br /></li>\r\n<li><strong>Internet (JTOpen):</strong> To acquire patches as quickly as possible, you can also order driver updates from IBM over the Internet with the name \"JTOpen\" JDBC drivers. The latest JTOpen patch corresponds in each case with a (future) toolbox PTF of the latest or a future operating system version and, therefore, has the same compatibility attributes as the toolbox driver.<br /><br />You can find JTOpen at:&#x00A0;<a target=\"_blank\" href=\"https://sourceforge.net/projects/jt400/\">https://sourceforge.net/projects/jt400/</a><br /><br />JTOpen is also provided in different variants for different JDBC API versions:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\"><span style=\"text-decoration: underline;\">Up to and including JTOpen 8.1</span>, a ZIP file is provided for download for each driver type. For the file that contains the driver that you require, see the naming structure of the file:</p>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<li>jtopen_x_y.zip&#x00A0; --- compatible with JDK 5.0 and below</li>\r\n<li>jtopen_x_y_jdbc40_jdk6.zip&#x00A0; --- compatible with JDK 6.0</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\">Once you have unpacked it, you will see the actual JDBC driver - \"/lib/jt400.jar\".</p>\r\n<p style=\"padding-left: 90px;\"><span style=\"text-decoration: underline;\">As of JTOpen 8.2</span>, all drivers are contained in a common ZIP file called \"jtopen_x_y.zip\". You can find the driver you require in the relevant subdirectory of this file as follows:</p>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<li>/Lib/jt400.jar     --- JDBC 3.0 version used at SAP with JSE 5.0 and below</li>\r\n<li>/lib/java6/jt400.jar --- JDBC 4.0 version used at SAP with Java 6 and Java 8</li>\r\n<li>/lib/java8/jt400.jar --- JDBC 4.2 version not currently used at SAP</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">We recommend that you obtain driver patches for toolbox drivers from the Internet.</p>\r\n<p><strong>17. What is the relationship between the toolbox driver and JTOpen?</strong><br />The JTOpen driver is the open source version of the toolbox driver. The source code corresponds to that of the toolbox driver for the latest or future operating system version. Although it is open source, IBM offers the same support as it does for the toolbox product. To ensure smooth operation with SAP, we urgently recommend that you use the latest JTOpen version.</p>\r\n<p>For more information, see the JTOpen home page at <a target=\"_blank\" href=\"http://jt400.sourceforge.net/\">http://jt400.sourceforge.net/</a>.</p>\r\n<p><strong>18. How can I find out which driver version I am using?</strong><br />Toolbox drivers and native drivers have different versioning mechanisms:</p>\r\n<ul>\r\n<li>Toolbox driver: The toolbox driver has a specification version and an implementation version. The decisive factor is the specification version, which is independent of the delivery method (JTOpen, toolbox). You can determine this, for example, by opening the manifest file (/META-INF/MANIFEST.MF) of \"jt400.jar\" and searching for entries for \"com/ibm/as400/access\". For JTOpen, for instance, you should find the following:<br /><br />Name: com/ibm/as400/access/<br />Specification title: IBM Toolbox for Java<br />Specification version: 5.3.0.1<br />Implementation title: com.ibm.as400.access<br />Implementation version: JTOpen 4.3<br /><br />The specification version states that this version of JTOpen correlates with V5R3 toolbox modification level 0.1, or toolbox Version 5.3.0.1 for short.<br /><br />As of V5R3 (or JTOpen 4.1), you can obtain the same information by performing the following call in the driver directory:<br /><br />java -cp jt400.jar utilities.AboutToolbox<br /><br /><strong>Result:</strong><br />IBM Toolbox for Java:<br /><br />Open Source Software, JTOpen 4.3, codebase 5722-JC1 V5R3M0.1<br /><br />On IBM i, you can execute the call from the PASE shell (QP2TERM), for example. You will find a suitable JVM in /QOpenSys/QIBM/ProdData/JavaVM/jdk60/64bit/bin, for example.</li>\r\n</ul>\r\n<ul>\r\n<li>Native driver: The version is determined by the current PTF level of different objects (for example, QJVAJDBC, QJVAJDBCXA, QJVAJDBS, and QJVASQL in the library QSYSDIR). You can use \"DSPOBJD OBJECT(QSYSDIR/&lt;obj&gt;) DETAIL(*SERVICE)\" to determine the PTF level of these objects.</li>\r\n</ul>\r\n<p><strong>19. Where can I find out the current driver version?</strong><br />For more information about the available patch level of toolbox and native driver as well as the patch strategy, see the <em>Preventative Maintenance for SAP solutions</em> section at <a target=\"_blank\" href=\"http://ibm.biz/ibmi-sap\">http://ibm.biz/ibmi-sap</a>.</p>\r\n<p>For JTOpen, we recommend that you always use the latest version from the World Wide Web.</p>\r\n<p>Section 22 of this SAP Note - \"Special notes about individual driver versions\" - contains a list of known driver problems.</p>\r\n<p><strong>20. How do I include a driver update in my SAP installation?</strong></p>\r\n<ul>\r\n<li>Native driver: Import the PTFs of the latest SAP Info APAR on the database server. You then automatically have the latest native driver version.</li>\r\n</ul>\r\n<ul>\r\n<li>Toolbox driver: We recommend that you download the current JTOpen version from the Web (see the \"Where can I order driver updates?\" section of this note) and copy these to the correct place(s) within your SAP installation. The place(s) are described in the \"Where can I find the driver executables in my SAP installation?\" section of this note. (As of Release 7.0, this is typically \"/QFileSvr.400/&lt;SAPGLOBALHOST&gt;/sapmnt/&lt;SID&gt;/SYS/jdbc/tbx/jt400.jar\".)<br /><br />However, under no circumstances should you replace the driver version delivered by the system in \"/QIBM/ProdData/HTTP/Public/jt400/jt400.jar\". Otherwise, the driver patch can be overwritten by an older toolbox version when you import Java PTFs.</li>\r\n</ul>\r\n<p><strong>21. JDBC 3.0 compared with JDBC 4.0?</strong><br />As mentioned previously, JDBC drivers implement the JDBC interface. This interface exists in different API versions, each of which is coupled to specific Java versions, whereby later Java versions are generally compatible with earlier JDBC interfaces. Below, the relationship between Java versions in the SAP environment and the used JDBC versions is depicted:</p>\r\n<ul>\r\n<li>Java 8: JDBC 4.0</li>\r\n<li>Java SE 6: JDBC 4.0</li>\r\n<li>JSE 5.0: JDBC 3.0</li>\r\n<li>JSE 1.4.2: JDBC 3.0</li>\r\n</ul>\r\n<p>At present, only JDBC 3.0 and JDBC 4.0 are of interest for SAP. IBM provides several versions of the toolbox driver as a licensed program. You can find these as follows:</p>\r\n<ul>\r\n<li>JDBC 3.0: /QIBM/Proddata/HTTP/public/jt400/lib/jt400.jar</li>\r\n</ul>\r\n<ul>\r\n<li>JDBC 4.0: /QIBM/Proddata/HTTP/public/jt400/lib/<strong>java6</strong>/jt400.jar</li>\r\n</ul>\r\n<p><br />For JTOpen, too, you can recognize the appropriate driver on the basis of naming conventions.</p>\r\n<p style=\"padding-left: 30px;\">Up to and including JTOpen Version 8.1, you can recognize the correct driver by the name of the download unit:</p>\r\n<ul>\r\n<ul>\r\n<li>JDBC 3.0: jtopen_x_y.zip</li>\r\n<li>JDBC 4.0: jtopen_x_y<strong>_jdbc40_jdk6</strong>.zip</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">As of Version JTOpen 8.2, you can find the driver versions for all JDBC versions in the subdirectories of a common ZIP file:</p>\r\n<ul>\r\n<ul>\r\n<li>/lib/jt400.jar</li>\r\n<li>/lib<strong>/java6</strong>/jt400.jar</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Once unpacked, both drivers are called \"jt400.jar\", which means that you cannot distinguish between them.<br /><br />You can and should use the JDBC 4.0 variant of the driver only for SAP products that are based on SAP Basis Release 7.20 and above. For all other products, you must use JDBC 3.0.<br /><br />You can use the following call in the driver directory to find out which JTOpen type you are using:<br /><br />java -cp jt400.jar utilities.AboutToolbox<br /><br /><strong>Result:</strong><br /><br />IBM Toolbox for Java:<br /><br />Open Source Software, JTOpen 6.6, codebase 5761-JC1 V6R1M0.10<br />Supports JDBC Version 3.0<br /><br />If the \"Supports JDBC version ...\" line does not exist at all, the driver also supports JDBC 3.0 and lower.<br /><br />If problems occur, read <a target=\"_blank\" href=\"/notes/1232613\">SAP Note 1232613</a>, too.</p>\r\n<p><span style=\"text-decoration: underline;\">Comment:</span> In this context, note that the native JDBC driver is not released for SAP products that are based on JDK 6.0 and above.</p>\r\n<p><strong>22. Special notes about individual driver versions</strong><br />The following driver versions may lead to errors when operating with SAP. Therefore, you should not use it or should only use it under certain circumstances.</p>\r\n<ul>\r\n<li><strong><strong>&lt; JTOpen 8.6: </strong></strong>During the modification of the configuration of the Java server as occurs during the deployment, for example, entries with errors arise in the table J2EE_CONFIGENTRY and the server no longer starts as a result. You can use the following SQL statement to determine whether this is the case: \"SELECT COUNT(*) FROM SAP&lt;sid&gt;DB/J2EE_CONFIGENTRY WHERE NAME = 'CFG_MODIFICATION_TS' and ISFILE &lt;&gt; 2\". The result should be \"0\". If not, repair the broken records as follows:&#x00A0;\"UPDATE SAP&lt;sid&gt;DB/J2EE_CONFIGENTRY SET ISFILE=2&#x00A0;WHERE NAME = 'CFG_MODIFICATION_TS' AND ISFILE &lt;&gt; 2\". Then commit the change (\"COMMIT\"). The problem is corrected with JTOpen 8.6.</li>\r\n<li><strong><strong>JTOpen 8.3 and 8.4: </strong></strong>The following error can occur during the installation of the Java server:<br />com.sap.inst.dbtools [Thread[main,5,main]] Fatal: DB Error during import of J2EE_CONFIGENTRY<br />com.sap.inst.dbtools [Thread[main,5,main]] Fatal: Database access error: <strong>[SQL0423] Locator *N not valid</strong>.&#x00A0;<br />OpenSQLExceptionCategories: [] (SQLState: 0F001, ErrorCode: -423)<br />Use JTOpen 8.2 as a workaround. We will let you know here once a solution is available.</li>\r\n<li><strong>JTOpen 7.4 or lower:</strong> Hanging HTTP processes in SAP Enterprise Portal 7.30 due to \"java.sql.BatchUpdateException\": The number of parameter values set or registered does not match the number of parameters.\" Affected SQL statements operate on tables with the prefix \"KMC_RIDID_\".<br />This is due to an incorrect implementation of the method PreparedStatement.clearBatch(). The problem is solved with JTOpen 7.51.</li>\r\n<li><strong>JTOpen 7.3 or lower:</strong> Deadlock in memory management. In the PI system, the cache refresh may not work if you access an IBM i using the JDBC adapter and JTOpen. Refer to APAR SE47785. The problem is solved with JTOpen 7.4.</li>\r\n<li><strong>JTOpen 7.2:</strong> Processes relating to SAP SLD, for example, the implementation of SLD CR content (CE 7.20), slow down the entire engine significantly over time. Timeouts sometimes occur during the logon.<br />The reason for this is a problem in list management of the JDBC driver. The problem is solved with JTOpen 7.3.</li>\r\n<li><strong>JTOpen 6.7 up to and including JTOpen 7.2:</strong> \"OutOfMemoryException\" possible, see SAP Note 1548414.</li>\r\n<li><strong>JTOpen 6.7:</strong> Errors when using database version DB2 for IBM i 7.1 or, generally, if you use bidirectional languages such as Hebrew or Arabic.</li>\r\n<li><strong>JTOpen 6.6:</strong> Errors when using database version DB2 for IBM i 7.1.</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D035492)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D035492)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000654800/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000654800/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000654800/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000654800/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000654800/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000654800/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000654800/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000654800/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000654800/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "934468", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i, FAQ: JDBC troubleshooting", "RefUrl": "/notes/934468"}, {"RefNumber": "924753", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: JAVA DataSources for external DB4 data sources", "RefUrl": "/notes/924753"}, {"RefNumber": "907733", "RefComponent": "BC-JAS-PER-DBI", "RefTitle": "Additional JDBC DataSources in SAP Java Application Server", "RefUrl": "/notes/907733"}, {"RefNumber": "83292", "RefComponent": "BC-INS-AS4", "RefTitle": "Release levels and PTFs for SAP on IBM i", "RefUrl": "/notes/83292"}, {"RefNumber": "826449", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: Change of JDBC driver", "RefUrl": "/notes/826449"}, {"RefNumber": "820325", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Use of QAQQINI with SAP", "RefUrl": "/notes/820325"}, {"RefNumber": "760362", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: JDDI and JDBI enhancements in the DB4 port", "RefUrl": "/notes/760362"}, {"RefNumber": "717376", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/717376"}, {"RefNumber": "699410", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries, J2EE: Table in use for DROP/ALTER", "RefUrl": "/notes/699410"}, {"RefNumber": "657117", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: JDBC configuration for Config and Deploy Tool", "RefUrl": "/notes/657117"}, {"RefNumber": "657115", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: Adding a remote J2EE instance", "RefUrl": "/notes/657115"}, {"RefNumber": "654794", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Exit programs for JDBC", "RefUrl": "/notes/654794"}, {"RefNumber": "568820", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Implementing an Independent ASP (iASP) System", "RefUrl": "/notes/568820"}, {"RefNumber": "1548414", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: OutOfMemory error due to JDBC driver", "RefUrl": "/notes/1548414"}, {"RefNumber": "1432783", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Known Issues with OS Release IBM i 7.1", "RefUrl": "/notes/1432783"}, {"RefNumber": "1393104", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: JDBC driver support matrix", "RefUrl": "/notes/1393104"}, {"RefNumber": "1385571", "RefComponent": "BC-DB-DB4", "RefTitle": "PI JDBC adapter: Connecting a DB2 for IBM i", "RefUrl": "/notes/1385571"}, {"RefNumber": "1234382", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1234382"}, {"RefNumber": "1232613", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Incompatible JDBC driver version in use", "RefUrl": "/notes/1232613"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2681874", "RefComponent": "BC-JVM", "RefTitle": "How to solve JDBC issues related to \"this version of the Java Runtime only recognizes class file versions up to <java compiler version>\"", "RefUrl": "/notes/2681874 "}, {"RefNumber": "3152440", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Known Issues with OS Release IBM i 7.5", "RefUrl": "/notes/3152440 "}, {"RefNumber": "2595196", "RefComponent": "BC-INS-RMP", "RefTitle": "Release Note for *70JDS*.SAR of Software Provisioning Manager 1.0 - Covers No Longer Supported Java and Dual-Stack Options and Access to Guides for Software Provisioning Manager 1.0 Java and Dual Stack", "RefUrl": "/notes/2595196 "}, {"RefNumber": "83292", "RefComponent": "BC-INS-AS4", "RefTitle": "Release levels and PTFs for SAP on IBM i", "RefUrl": "/notes/83292 "}, {"RefNumber": "1432783", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Known Issues with OS Release IBM i 7.1", "RefUrl": "/notes/1432783 "}, {"RefNumber": "654794", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Exit programs for JDBC", "RefUrl": "/notes/654794 "}, {"RefNumber": "820325", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Use of QAQQINI with SAP", "RefUrl": "/notes/820325 "}, {"RefNumber": "934468", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i, FAQ: JDBC troubleshooting", "RefUrl": "/notes/934468 "}, {"RefNumber": "924753", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: JAVA DataSources for external DB4 data sources", "RefUrl": "/notes/924753 "}, {"RefNumber": "1393104", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: JDBC driver support matrix", "RefUrl": "/notes/1393104 "}, {"RefNumber": "568820", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Implementing an Independent ASP (iASP) System", "RefUrl": "/notes/568820 "}, {"RefNumber": "1232613", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Incompatible JDBC driver version in use", "RefUrl": "/notes/1232613 "}, {"RefNumber": "1548414", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: OutOfMemory error due to JDBC driver", "RefUrl": "/notes/1548414 "}, {"RefNumber": "826449", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: Change of JDBC driver", "RefUrl": "/notes/826449 "}, {"RefNumber": "1385571", "RefComponent": "BC-DB-DB4", "RefTitle": "PI JDBC adapter: Connecting a DB2 for IBM i", "RefUrl": "/notes/1385571 "}, {"RefNumber": "657115", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: Adding a remote J2EE instance", "RefUrl": "/notes/657115 "}, {"RefNumber": "760362", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: JDDI and JDBI enhancements in the DB4 port", "RefUrl": "/notes/760362 "}, {"RefNumber": "907733", "RefComponent": "BC-JAS-PER-DBI", "RefTitle": "Additional JDBC DataSources in SAP Java Application Server", "RefUrl": "/notes/907733 "}, {"RefNumber": "842115", "RefComponent": "BC-INS-AS4", "RefTitle": "SAP NetWeaver 2004s Installation on IBM eServer iSeries", "RefUrl": "/notes/842115 "}, {"RefNumber": "657117", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: JDBC configuration for Config and Deploy Tool", "RefUrl": "/notes/657117 "}, {"RefNumber": "699410", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries, J2EE: Table in use for DROP/ALTER", "RefUrl": "/notes/699410 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}