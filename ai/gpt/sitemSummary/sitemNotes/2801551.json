{"Request": {"Number": "2801551", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 193, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=DA697F22EB7402EFFE440A5714071112"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2801551"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.08.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-NEO-SEC-IAM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authentication, Authorization(Cloud Platform Neo)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2801551 - BASIC authentication options for SAP BTP Neo subaccounts"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You would like to enable <a target=\"_blank\" href=\"https://en.wikipedia.org/wiki/Basic_access_authentication\"><strong>BASIC</strong> authentication</a> (user name and password)&#160;for the <a target=\"_blank\" href=\"https://help.sap.com/viewer/65de2977205c403bbc107264b8eccf4b/Cloud/en-US/0874895f1f78459f9517da55a11ffebd.html\">subaccount</a> on the SAP BTP&#160;Neo environment.</p>\r\n<p>By default, on a subaccount, the&#160;BASIC authentication is&#160;configured to work with&#160;users from the user store: SAP ID Services (accounts.sap.com or&#160;S-Users). No additional&#160;configuration is required. In case of&#160;applications/services provided by SAP, the&#160;authentication method is also devised by SAP.</p>\r\n<p>You now wish to use the&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/6d6d63354d1242d185ab4830fc04feb1/Cloud/en-US/d17a116432d24470930ebea41977a888.html\">SAP Identity Authentication Service</a>&#160;(IAS) for authentication instead of the SAP ID Service.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<p>SAP Business Technology Platform, Neo environment</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>Enable the BASIC authentication for BTP NEO subaccount per document&#160;<a target=\"_blank\" href=\"https://help.sap.com/docs/BTP/ea72206b834e4ace9cd834feed6c0e09/a2c696be81c14da189ccaeae9a2d687f.html\">Basic Authentication</a></p>\r\n<p>Important points to note:</p>\r\n<ul>\r\n<li>BASIC authentication is only triggered for the subaccount (the configuration does not affect the Global account or other subaccounts).&#160;</li>\r\n<li>BASIC authentication with a third-party corporate application identity provider is not supported.</li>\r\n<li>BASIC&#160;authentication where the Identity Authentication tenant acts as the proxy for another Identity Provider (for example: Azure AD) is not supported&#160;if the user resides on the external IDP. However if the&#160;user resides on the IAS&#160;tenant, then BASIC authentication can be configured.&#160;For one subaccount, only one IAS tenant can be configured for BASIC authentication. In addition, rules for Risk-Based Authentication&#160;(e.g. IP range restrictions) do not work with this method of authentication.</li>\r\n<li>BASIC authentication is only supported in Java applications.</li>\r\n<li>Custom Java applications can delegate BASIC authentication to an on-premise system. See&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/ea72206b834e4ace9cd834feed6c0e09/Cloud/en-US/04cbd0f30d524612aa438ed0b0eed217.html\">On-Premise User Store</a>.</li>\r\n<li>When assigning users that login via BASIC authentication to groups and roles in the subaccount, use the ID (S-user ID in case of SAP ID Service; P-user ID in case of IAS). Only direct assignment to groups and roles works. Dynamic group or attribute mapping does not work.</li>\r\n<li>Users created in the <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#techuser\">Technical Users</a> application cannot be used on SAP BTP. Reason: these users are not stored in the SAP ID Service. They are only used in SAP Solution Manager's Support Hub Connectivity.<strong><br /></strong></li>\r\n<li>SAP Universal ID does not work with Basic Authentication.&#160;Log on with the password associated to the S-user account.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<p>For more details on additional authentication methods,&#160;check:&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/65de2977205c403bbc107264b8eccf4b/Cloud/en-US/e637f62abb571014857cb0232adc43a7.html#loioe637f62abb571014857cb0232adc43a7\">SAP BTP Authentication</a>.</p>\r\n<p>For Cloud Foundry, see KBA&#160;<a target=\"_blank\" href=\"/notes/3015211\">3015211</a> - BASIC authentication options for SAP BTP Cloud Foundry applications</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>sso&#160;single-sign-on login.failed&#160;artifact&#160;JAVA&#160;Service Provider SP&#160;Identity Provider IDP&#160;Issue Instant is not valid SAP Production ABAP R/3 ERP SRM CRM ERP PPM SEM APO XI PI PORTAL Test development QA&#160;SAML 2.0&#160;SAML2Assertion&#160;Warning&#160;saml2.sp.ResponseValidationService&#160;SAML2Assertion Service Provider&#160;SAMLREQUEST scp Change basic authentication auth to IdP ID Services Identity Authentication Services IAS subaccount btp cloud platform business technology platform destination Neo</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I057222)"}, {"Key": "Processor                                                                                           ", "Value": "Hua Lv (I058778)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "", "RefComponent": "", "RefTitle": "On-Premise User Store", "RefUrl": "https://help.sap.com/viewer/ea72206b834e4ace9cd834feed6c0e09/Cloud/en-US/04cbd0f30d524612aa438ed0b0eed217.html"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP Cloud Platform Identity Authetication tenant service (IAS Tenant)", "RefUrl": "https://help.sap.com/viewer/6d6d63354d1242d185ab4830fc04feb1/Cloud/en-US/d17a116432d24470930ebea41977a888.html"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP Cloud Platform Authetication", "RefUrl": "https://help.sap.com/viewer/65de2977205c403bbc107264b8eccf4b/Cloud/en-US/e637f62abb571014857cb0232adc43a7.html#loioe637f62abb571014857cb0232adc43a7"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "Conditional Authetication", "RefUrl": "https://help.sap.com/viewer/6d6d63354d1242d185ab4830fc04feb1/Cloud/en-US/0143dce88a604533ab5ab17e639fec09.html"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "Navigate to Global Accounts and Subaccounts", "RefUrl": "https://help.sap.com/viewer/65de2977205c403bbc107264b8eccf4b/Cloud/en-US/0874895f1f78459f9517da55a11ffebd.html"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "Application Identity Provider", "RefUrl": "https://help.sap.com/viewer/65de2977205c403bbc107264b8eccf4b/Cloud/en-US/dc618538d97610148155d97dcd123c24.html#loiodc618538d97610148155d97dcd123c24"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP Cloud Platform NEO", "RefUrl": "https://help.sap.com/viewer/65de2977205c403bbc107264b8eccf4b/Cloud/en-US/ab512c3fbda248ab82c1c545bde19c78.html#loio1a8ee4e7b27d4293af175f021db8ad9c"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "BASIC authentication", "RefUrl": "https://en.wikipedia.org/wiki/Basic_access_authentication"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2174416", "RefComponent": "XX-SER-FORME", "RefTitle": "Creation and activation of Technical Communication Users - SAP for Me", "RefUrl": "/notes/2174416 "}, {"RefNumber": "2773752", "RefComponent": "BC-NEO-SEC-IAM", "RefTitle": "How to assign users to roles on SAP BTP Neo?", "RefUrl": "/notes/2773752 "}, {"RefNumber": "2524719", "RefComponent": "LOD-HCI-PI-OPS", "RefTitle": "FAQ: Cloud Integration/Integration Suite/CPI/HCI/IS simple questions", "RefUrl": "/notes/2524719 "}, {"RefNumber": "2946506", "RefComponent": "BC-NEO-SEC-IAM", "RefTitle": "BASIC Authentication is not working - SAP BTP Neo", "RefUrl": "/notes/2946506 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP Business Technology Platform all versions "}, {"Product": "SAP Cloud Identity Services all versions "}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "4 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 5.0, "Quality-Votes": 4, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 4}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}