SAP Note 2228220 addresses the issues faced by customers in the automotive sector using the Discrete Industries and Mill Products (DIMP) industry solution, specifically regarding the cross-system transit between two plants with scheduling agreements in an SAP ERP system when converting to SAP S/4HANA.

The note indicates that the functionality previously available in SAP ERP for managing this process is no longer available in SAP S/4HANA. Therefore, any custom code that interacts with certain SAP objects, particularly those mentioned in the piece list SI_AUT_04_XLO, will need to be adapted because these objects have changed in a way that is not compatible with the new S/4HANA environment.

The solution provided in this note is clear: customers must remove the usages of the specified SAP objects from their custom code, as highlighted by the custom code check process.

The note also references SAP Note 2190420, which gives comprehensive recommendations on adapting custom ABAP code for compatibility with SAP S/4HANA. Key points from that note include that most custom code should be compatible with minimal changes, recommendations for maintaining custom code on SAP ERP are still applicable, and specific tools and checks are available to aid in ensuring code compatibility and necessary adaptations for S/4HANA.

In summary, SAP Note 2228220 alerts customers in the automotive industry using DIMP who are transitioning to SAP S/4HANA about the need to adjust their custom code due to the lack of direct support for cross-system transit functionalities that existed in SAP ERP. It directs customers to perform code adaptations and remove dependencies on certain objects and serves as a signpost to further guidance provided in the referenced SAP Note 2190420.