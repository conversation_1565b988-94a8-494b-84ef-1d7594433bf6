{"Request": {"Number": "2806070", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 252, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001701172019"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002806070?language=E&token=EB0BDD9E30F4AD23C107A3716DB5EFB7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002806070", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002806070/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2806070"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.11.2020"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-EWM-UPG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Extended Warehouse Management", "value": "SCM-EWM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-EWM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade", "value": "SCM-EWM-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-EWM-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2806070 - SAP S/4HANA 1909: Release information and restrictions for EWM in SAP S/4HANA"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note contains release information and restrictions when using embedded Extended Warehouse Management (EWM) in SAP S/4HANA, on-premise edition 1909.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>SAP has recommendations for implementing EWM in SAP S/4HANA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Basic Warehouse Management and Extended Warehouse Management in SAP S/4HANA</strong></p>\r\n<p>Embedded EWM in S/4HANA is available in two versions:</p>\r\n<ul>\r\n<li>Basic Warehouse Management</li>\r\n<li>Extended&#160;Warehouse Management</li>\r\n</ul>\r\n<p>Refer to the Feature Scope Description of SAP S/4HANA 1909 for an overview of the features contained in each version.</p>\r\n<p>Both versions are available with the SAP S/4HANA installation. You set up the warehouse management version at warehouse number level. As long as no transaction data exists, you can change the version used by a warehouse but you must not change the configuration of whether EWM is embedded or decentralized once transaction data exists, because this would disrupt existing processes and documents.</p>\r\n<p><strong>Release Information</strong></p>\r\n<p>A document \"Basic Settings for EWM in SAP S/4HANA, On-Premise Edition\" for starting with EWM in SAP S/4HANA is available as attachment to this SAP note.</p>\r\n<p>Materials and batches that were created in SAP S/4HANA releases prior to 1610 have to be enabled for usage in EWM in SAP S/4HANA. To do so run report PRD_SCM_GUID_CONVERSION for materials and report RVB_BATCH_GUID_GENERATOR for batches. For more information see the report documentation.</p>\r\n<p><strong>Major differences between EWM in SAP S/4HANA 1909 and decentral SAP EWM 9.5</strong></p>\r\n<p>EWM in SAP S/4HANA and SAP EWM 9.5 share a common core of warehouse management functions. They support the same business scenarios and processes with only minor exceptions:</p>\r\n<ul>\r\n<li>Embedded EWM in SAP S/4HANA offers a simplified integration into other SAP S/4HANA applications</li>\r\n<li>EWM in SAP S/4HANA provides additional functionality</li>\r\n<li>Some restrictions apply in EWM in SAP S/4HANA</li>\r\n</ul>\r\n<p>These exceptions are listed in the document &#8220;EWM Deployment Differences&#8221; attached to this note.</p>\r\n<p><strong>Restrictions of embedded EWM in S/4HANA 1909 compared to functions that are generally available in SAP S/4HANA:</strong></p>\r\n<ul>\r\n<li>Batch derivation</li>\r\n<li>Batch-specific unit of measure and active ingredients - see also SAP note 2432414</li>\r\n<li>Interchangeable Manufacturer Parts</li>\r\n<li>International address versions for locations and supply chain units as well as delivery specific onetime addresses (document address)</li>\r\n<li>Special stock indicators other than E (Orders on hand), K (Vendor Consignment), M (Returnable Packaging from Vendor), Q (Project Stock)</li>\r\n<li>Valuated stock in transit (SiT) processes with EWM-managed storage locations (but STO processes without valuated SiT are supported) - see also SAP note 1822497</li>\r\n<li>VMS and JIT processes as described in SAP note 2825796</li>\r\n<li>Subcontracting for MRO processes - see also SAP note 2927661</li>\r\n<li>Restrictions related to QM integration:</li>\r\n<ul>\r\n<li>Inspection lot in the material inspection in Advanced Returns Management integrated with QM, see SAP Note 2259775 and 2481845</li>\r\n<li>First article inspection</li>\r\n<li>Monitor receipt of quality certificates in inbound during goods receipt posting</li>\r\n<li>Quality inspection in outbound (inspection type 10, 11, 12): open inspection lot results in hanging queue during goods issue posting</li>\r\n<li>Integration into quality notification, especially stock postings (for example, from blocked stock to unrestricted-use stock)</li>\r\n<li>Move quality inspection stock between MM-IM and EWM-managed storage location (transaction QAC2)</li>\r\n<li>Link between inspection lot and material document for stock postings with usage decision (technically: update of table QAMB at usage decision)&#160;</li>\r\n<li>Calculate samples on basis of number of handling units</li>\r\n<li>Inspection result recording&#160;for serial numbers&#160;(transaction QE51N)</li>\r\n<li>Visibility of assigned serial numbers on the ARM material inspection</li>\r\n</ul>\r\n</ul>\r\n<p>For restrictions and recommendations regarding the usage of user interfaces in embedded EWM in SAP S/4HANA, see SAP note 2348923.</p>\r\n<p>For restrictions and recommendations regarding the usage of decentralized EWM on SAP S/4HANA, see SAP note 2840129.</p>\r\n<p>For a list of BI data sources that can be used in the EWM context, see SAP note 2552797.</p>\r\n<p>For a list of BI data sources from the SCM Basis area that can be used in the EWM context, see SAP note 2382662.</p>\r\n<p><strong>Features in EWM in S/4HANA 1909 which are not using single-system integration qualities</strong></p>\r\n<p>S/4HANA EWM 1909 to S/4HANA TM 1909 system internal integration is done via A2A services in the same way as cross system communication. General scope description of TM EWM integration applies, see SAP note 1984252.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SCM-EWM-ANA (Analytics)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D051985)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028293)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002806070/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002806070/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002806070/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002806070/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002806070/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002806070/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002806070/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002806070/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002806070/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "BasicSettings_EWMinS4_V01.pdf", "FileSize": "274", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001418562019&iv_version=0018&iv_guid=00109B36BC6E1ED9B6C22F55801000D3"}, {"FileName": "RIN1909_EWM_Deployment_differences_V1_8.pdf", "FileSize": "934", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001418562019&iv_version=0018&iv_guid=00109B36BCAE1EDAA2AA87583FBD20DB"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2927661", "RefComponent": "SCM-EWM-DLP-SF", "RefTitle": "Subcontracting for MRO processes with EWM", "RefUrl": "/notes/2927661"}, {"RefNumber": "2825796", "RefComponent": "IS-A", "RefTitle": "SAP S/4HANA 1909, Automotive: Restriction Note", "RefUrl": "/notes/2825796"}, {"RefNumber": "2799003", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909: Restriction Note", "RefUrl": "/notes/2799003"}, {"RefNumber": "2782080", "RefComponent": "SCM-EWM-CNT", "RefTitle": "Overview of Guides or Best Practices Documents for Extended Warehouse Management", "RefUrl": "/notes/2782080"}, {"RefNumber": "2668150", "RefComponent": "SCM-EWM-UPG", "RefTitle": "SAP S/4HANA 1809: Release information and restrictions for EWM in SAP S/4HANA", "RefUrl": "/notes/2668150"}, {"RefNumber": "2552797", "RefComponent": "SCM-EWM-IF-BW", "RefTitle": "Embedded EWM in SAP S/4HANA: List of BI Data Sources used in EWM", "RefUrl": "/notes/2552797"}, {"RefNumber": "2348923", "RefComponent": "SCM-EWM-UPG", "RefTitle": "SAP S/4HANA: Usability Restrictions and Recommendations for EWM in SAP S/4HANA", "RefUrl": "/notes/2348923"}, {"RefNumber": "1984252", "RefComponent": "TM-INT-EWM", "RefTitle": "Supported functional scope of the direct TM EWM Integration for shippers", "RefUrl": "/notes/1984252"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2916853", "RefComponent": "QM-IM", "RefTitle": "Quality Info Record with Extended Warehouse Management.", "RefUrl": "/notes/2916853 "}, {"RefNumber": "2819209", "RefComponent": "SCM-EWM-DLP-BF-MDD", "RefTitle": "Using O002 and I002 process codes in EWM", "RefUrl": "/notes/2819209 "}, {"RefNumber": "2545012", "RefComponent": "CA-QIE", "RefTitle": "EWM-QM integration - Inspection lots of origin 17: Unsupported processes", "RefUrl": "/notes/2545012 "}, {"RefNumber": "2952651", "RefComponent": "TM-CF", "RefTitle": "SAP S/4HANA 2020 Supply Chain for Transportation Management - Release information", "RefUrl": "/notes/2952651 "}, {"RefNumber": "2927661", "RefComponent": "SCM-EWM-DLP-SF", "RefTitle": "Subcontracting for MRO processes with EWM", "RefUrl": "/notes/2927661 "}, {"RefNumber": "2840129", "RefComponent": "SCM-EWM-UPG", "RefTitle": "Release information and restrictions of Decentralized EWM on S/4HANA 1909", "RefUrl": "/notes/2840129 "}, {"RefNumber": "2799003", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909: Restriction Note", "RefUrl": "/notes/2799003 "}, {"RefNumber": "2813859", "RefComponent": "TM-CF", "RefTitle": "SAP S/4HANA 1909 Supply Chain for Transportation Management - Release information", "RefUrl": "/notes/2813859 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}