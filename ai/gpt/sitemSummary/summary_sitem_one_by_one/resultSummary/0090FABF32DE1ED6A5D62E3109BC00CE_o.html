<h3>SI43: Logistics_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2365665">2365665 - S4TWL- Retail Season Conversion ((SAP ERP to SAP S/4HANA 1610)</a></strong></p>
<p><strong>Description:</strong></p>
<p>In SAP S/4HANA certain functionality is not supported anymore, among others the Retail Season (e.g. customizing tables T6WSP, TWSAI). With the conversion, the following new season fields are introduced:</p>
<ul>
  <li>SEASON YEAR</li>
  <li>SEASON</li>
  <li>COLLECTION</li>
  <li>THEME</li>
</ul>
<p>The old season fields in the article master on the Basic Data (table MARA) are not used anymore.  The new season fields will be maintained on Basic Data 2 (table FSH_SEASONS_MAT).</p>
<p><strong>Business Process Impact:</strong></p>
<p>The season will not be defined in the customizing anymore, instead there is the Season Workbench (transaction FSH_SWB) available as a application transaction. Part of the conversion from SAP ERP to SAP S/4HANA 1610, articles need to be reassigned from "old" to "new" season. With the conversion, the following tables are filled automatically:</p>
<ul>
  <li>Assignment season to article
    <ul>
      <li>FSH_SEASONS_MAT </li>
    </ul>
  </li>
  <li>Season definition
    <ul>
      <li>FSH_SEASONS</li>
      <li>FSH_SEASONS_T</li>
      <li>FSH_SD_PERIODS</li>
      <li>FSH_MM_PERIODS</li>
      <li>FSH_COLLECTIONS</li>
      <li>FSH_COLLECTION_T </li>
    </ul>
  </li>
</ul>
<p>Note: The season fields are no longer maintained in table MARA. All relevant season information is now maintained in table FSH_SEASONS_MAT.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>During the conversion, the following reports are executed automatically:</p>
<ul>
  <li>R_FSH_S4_SEASONS_MD_MIG to convert the season Customizing data</li>
  <li>R_FSH_S4_SEASONS_MAT_MIG_XPRA (XPRA) to convert the season assignment to article</li>
</ul>
<p>In case of problems during the conversion, you can execute these reports manually. Note that R_FSH_S4_SEASONS_MAT_MIG_XPRA is a cross-client enabled report, whereas R_FSH_S4_SEASONS_MD_MIG has to be executed in each client required.</p>
<p><strong>Reference Notes:</strong>
  <a href="https://launchpad.support.sap.com/#/notes/2640859">2640859 - S4TWL - Conversion of Season Master Data to S/4HANA 1809</a>,
  <a href="https://launchpad.support.sap.com/#/notes/2481829">2481829 - S4TWL - Fashion Season Conversion (SAP ERP to SAP S/4HANA 1709)</a>
</p>