SAP Note 2950412 details functional restrictions within the Master Data Governance (MDG) Process Analytics framework on SAP S/4HANA 2020. This new framework allows key users to generate analytics CDS (Core Data Services) views based on their data model definitions and subsequently configure analytics applications for end users to analyze master data change requests.

The note lists limitations in the analytics CDS view generation, specifically for SAP delivered MDG standard data models (0G, BP, MM) and Custom Data Model with Flex option. The following data types are not supported by the CDS view generation:
- Hierarchy data, where parent-child relationships (like Cost Center within a Cost Center Group) are not supported.
- Amount and quantity fields tied to a currency or unit.
- Attributes whose data element in the data model differs from the active data (Reuse).
- Attributes with data elements of type LCHAR or LRAW.
- Entities with multiple changeable key fields.
- Classification data.
- Long text.
- Documents Data.

The note also mentions that for hierarchy data, only certain types of objects (type 1, e.g., Cost Center Group) are supported, while entities with one changeable key field are supported, but not those with multiple changeable key fields.

Regarding authorization control for custom extensions, a single value can be defined for the authorization type "Direct Value Processing" for data access control.

Finally, the note references a guide titled "How to Configure MDG Change Request Analytics using Master Data Attributes" for more detailed information on utilizing the function. The note does not provide any additional solution as the "Symptom" section contains all the necessary information regarding the functional restrictions.