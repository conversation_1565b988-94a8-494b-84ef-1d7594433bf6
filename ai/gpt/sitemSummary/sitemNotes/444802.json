{"Request": {"Number": "444802", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 427, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002101502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000444802?language=E&token=1D2F69A72C1105A33EF3AE2C77572481"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000444802", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000444802/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "444802"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 21}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.01.2007"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CZ-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-CZ"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Czech Republic", "value": "XX-CSC-CZ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-CZ-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-CZ", "value": "XX-CSC-CZ-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "444802 - Weighting Key and Budget Billing for Industr.Customers"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>IS-U/CCS local enhancement for country specific functionality for Czech and Slovak, description of installation, documentation.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Weighting key, Budget Billing Plan, Industrial Customers, WK, BBIC, /SAPCE/IU_WKBB, /SAPCE/IU_WKBBD, /SAPCE/IU_WKBBT, /SAPCE/IUWK, /SAPCE/IU_DPRQ5</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This functionality is optional.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3> <p>&#x00A0;&#x00A0;Note:<br />&#x00A0;&#x00A0;- These local enhancements are part of the Czech and Slovak country<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; version of IS-U/CCS<br />&#x00A0;&#x00A0; - There are no changes of the SAP standard objects<br />&#x00A0;&#x00A0; - The enhancements are using reserved name space /SAPCE/* which does<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; not overlap with customer name range or other project development<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; (according to note 72483)<br />&#x00A0;&#x00A0;- SAP CR, Prague is responsible for maintenance and hotline<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; support of these enhancements<br />&#x00A0;&#x00A0; - This enhancement should not interfere with any hot packages or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; any other add-on support packages for IS-U with the exception above<br /><br /><br />************************************************************************<br />Local enhancements for Czech and Slovak:<br />- Weighting key for bud billing plan (WK)<br />- Budget billing plan for industrial customers (BBIC)<br />************************************************************************<br /><br />Contents<br /><br />I.&#x00A0;&#x00A0; Overview of enhancements<br />II.&#x00A0;&#x00A0;Transport requests<br />III. Installation<br />IV.&#x00A0;&#x00A0;Additional notes, known problems and their solutions<br />************************************************************************<br /><br />I/ Overview of enhancements<br /><br />Weighting key for budget billing plan (WK)<br /><br />For several installations, where consumption varies during the year, there is a request to modify the amount of budget bills according to the consumption pattern by defining a weighting key procedure for budget billing plan.<br />This funcionality is delivered in function module /SAPCE/IU_EVENT_R994. The program reads the weights of weighting key, that is set on the third tab of contract, from table /SAPCE/IU_WKBBD. Than program calucalates the amount for each month and replaces the proposed constant amount in budget billing plan.<br /><br />Budget billing plan for industrial customers (BBIC)<br /><br />For the industrial customers there is a request for specific procedure for determination of the budget billing plan (BBP) with following basic characteristics:<br />- From (none) one to three (five) budget bills for one billing period,<br />- The \"fixed\" due dates (parameters) of budget bills (on the 5th,<br />&#x00A0;&#x00A0;15th, 25th day of the month, calendar of the work days is taken into<br />&#x00A0;&#x00A0;account) or individually negotiable dates,<br />- Generation of BBP for the billing period in advance,<br />- Static principle or dynamic principle of determination of BBP,<br />- The budget bills (BB) generated are statistical (statistical<br />&#x00A0;&#x00A0;procedure of BB) without tax,<br />- Billing period is 1 month.<br />This funcionality is delivered in following function modules<br />- /SAPCE/IU_EVENT_R401 (Selection of CA items for account maintainance),<br />- /SAPCE/IU_EVENT_R401 (Generation of BB requests).<br /><br />************************************************************************<br />II/&#x00A0;&#x00A0;Transport requests<br /><br />CZUK901885 - programs and dictionary objects for WK, BBIC<br />CZUK900705 - application forms for BBIC<br />CZUK900709 - customizing for setting events R401, R402, R994<br /><br />******************************************************************<br />III/ Installation<br /><br />III/a/ Location of the transport files and documentation<br /><br />Transport and document files are stored on sapserv3 in the directory specific/czsk/note_0444802:<br />- wk_bbic.zip&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- archive containing all transport files,<br />- wk_bbic_docu.zip - archive containing documentation in EN and CS/SK.<br /><br />III/b/ How to get the transport files and documentation<br /><br />The customer calls ftp as follows:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;set up link to sapserv3&gt;<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ftp sapserv3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Start file transfer program&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; */<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Enter 'ftp' as user and password&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;cd specific/czsk/note_0444802/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;bin&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Switch to binary mode&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;get wk_bbic.zip&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Fetch archive with R3trans data files */<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;get wk_bbic_docu.zip&#x00A0;&#x00A0;/* Fetch the documentation.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;bye&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* End ftp. */<br /><br /> Now extract archives under MS DOS or Windows,<br /> than call ftp to transfer the transport files to your R/3 server<br /> in the BINARY mode !<br /> Please see note 13719 for additional information.<br /><br />III/c/ Import of the transport files into R/3 system<br /><br />Import transports CZUK901885, CZUK900705 and CZUK900709 into your system.<br /><br />Remarks:<br />- CZUK900705 contains application forms and so has to be imported into<br />&#x00A0;&#x00A0;client 000,<br />- CZUK900709 contains customizing of events R401, R402 and R994 which<br />&#x00A0;&#x00A0;can be also done manualy (this is recommended in case of previous<br />&#x00A0;&#x00A0;usage of one of these events) and so its import is optional.<br /><br />III/d/ Activities after import<br /><br />If you have decided in previous step not to import CZUK900709, you have to assign function modules /SAPCE/IU_EVENT_R401, /SAPCE/IU_EVENT_R402 and /SAPCE/IU_EVENT_R994 to corresponding events R401, R402 and R994 (application R) in FI-CA customizing or directly in view cluster VC_TFKFB (transaction SM34).<br /><br />For IS/U release 4.63 there must be implemented below specified modification.<br /><br />For IS/U release 4.63 check whether there is the document source key 'RJ' defined in table TFK001 (by SM30). If it's not defined there, create new entry 'RJ' as copy of reason 'RA' (set description 'IS-U BBP Move-in').<br /><br />Optionally, you can insert the customizing activity /SAPCE/IU_WK_CUST into your IMG tree (billing master data). Otherwise you will have to start the customizing transaction by entering transaction code /SAPCE/IUWK.<br /><br />************************************************************************<br />IV./&#x00A0;&#x00A0;Additional notes, known troubleses and their solutions<br /><br />For usage of Weighting key for budget billing plan in release 4.62 see Note 0411836.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-U-IN (Invoicing)"}, {"Key": "Other Components", "Value": "XX-CSC-SK-IS-U (use FI-LOC-UT-SK)"}, {"Key": "Responsible                                                                                         ", "Value": "I024302"}, {"Key": "Processor                                                                                           ", "Value": "I024302"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444802/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444802/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444802/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444802/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444802/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444802/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444802/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444802/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000444802/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "554426", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech & Slovak specific functionality IS-U 4.64 (collective)", "RefUrl": "/notes/554426"}, {"RefNumber": "489912", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech specific functionality IS Utilities,collective note", "RefUrl": "/notes/489912"}, {"RefNumber": "1125443", "RefComponent": "IS-U-BI", "RefTitle": "INV: error AJ 064 in extrapolation for budget billing plan", "RefUrl": "/notes/1125443"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1125443", "RefComponent": "IS-U-BI", "RefTitle": "INV: error AJ 064 in extrapolation for budget billing plan", "RefUrl": "/notes/1125443 "}, {"RefNumber": "554426", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech & Slovak specific functionality IS-U 4.64 (collective)", "RefUrl": "/notes/554426 "}, {"RefNumber": "489912", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Czech specific functionality IS Utilities,collective note", "RefUrl": "/notes/489912 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-U/CCS", "From": "463", "To": "463", "Subsequent": ""}, {"SoftwareComponent": "IS-U/CCS", "From": "464", "To": "464", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-U/CCS", "NumberOfCorrin": 1, "URL": "/corrins/0000444802/16"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}