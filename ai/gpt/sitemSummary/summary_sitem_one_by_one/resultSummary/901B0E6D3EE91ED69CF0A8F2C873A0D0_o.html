<h3>SI7: PPM_DOC_HTTP_01</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2353984">2353984 - S4TWL - HTTP-Based Document Management in SAP Portfolio and Project Management for SAP S/4HANA</a></strong></p>
<p><strong>Description:</strong></p>
<p>In SAP Portfolio and Project Management for SAP S/4HANA, the HTTP-Based Document Management for the check-in/check-out of documents is no longer available. The HTTP-Based check-in/check-out feature had provided the possibility to upload and download documents including the possibility to checkout a document and open a document with the specified client application. The HTTP-based check-in/check-out required a Java installation and a valid certificate on client-side.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No effects on business processes are expected.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>The functionality to upload and download documents is still available and can be used. No further actions necessary.</p>
<p>Customizing / Configuration: In SAP Portfolio and Project Management for SAP S/4HANA, the HTTP-Based Document Management for the check-in/check-out of documents is no longer available. The HTTP-Based check-in/check-out feature had provided the possibility to upload and download documents including the possibility to checkout a document and open a document with the specified client application. The HTTP-based check-in/check-out required a Java installation and a valid certificate on client-side. The functionality to upload and download documents is still available and can be used. Please. see also SAP Note 2353984. Please check the Global Settings in Customizing: SAP Portfolio and Project Management -> Project Management -> Basic Settings -> Override Default Global Settings. If Master Switch 0007 (General Default Values) 0027 (Check-in/Check-out of Documents (' ': Normal, 'X': HTTP-Based)) is set to ‘X’, the Transition Worklist item is relevant.</p>
<p>User Training: remove orphaned client dependent data</p>
<p>Custom Code Adaption: deletion of unused orphaned objects</p>