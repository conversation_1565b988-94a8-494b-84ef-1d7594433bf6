{"guid": "0050569455E21ED5B3E17678390FA09E", "sitemId": "SI17: PROC_ADA", "sitemTitle": "S4TWL - Automatic Document Adjustment", "note": 2267742, "noteTitle": "2267742 - S4TWL - Automatic Document Adjustment", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA, the following differences to the process of automatic document adjustment apply:</p>\n<ol>\n<li>The direct entry function is activated per default for all document categories to simplify the creation of worklist entries.</li>\n<li>The field length of the Material Number data element (MATNR) is 40 characters. As a consequence, the field length of data element 'VAKEY' is enhanced and the database tables WIND and S111 have also been adjusted.</li>\n</ol>\n<p><strong>Business Process related information</strong></p>\n<ol>\n<li>In SAP S/4HANA, the indicator for direct entry is set now automatically during the update process of conditions. This means that:</li>\n</ol>\n<ul>\n<li>When a user saves a condition change - including the creation of a new condition - an entry is made in the worklist (table WIND) for all document categories with valid Customizing settings.</li>\n</ul>\n<p>For more information, please see SAP Note 519685 (Conversion of the procedure for Worklist creation).</p>\n<p>Note also that change pointers do not have to be updated for message type CONDBI if direct entries are activated. With the conversion to the 'direct entry' procedure, you can deactivate the <strong><span>update of change pointers</span></strong> if you do not use it for other purposes</p>\n<p>2. Rebuilding business data volume (tables WIND and S111)</p>\n<ul>\n<li>SAP recommends that you process all open worklist entries completely before conversion to SAP S/4HANA. In this case, no activities for automatic document adjustment are required after the system conversion process</li>\n<li>If the worklist cannot be completed in the SAP ERP system, you can proceed with the procedure described in section 'Required and Recommended Actions on Start Release.</li>\n</ul>\n<p><strong><br/>Required and Recommended Action(s)</strong></p>\n<p>If in the ERP system the worklist could not be completely processed, a rebuilding of the worklist for automatic document adjustment is required in SAP S/4HANA, on-premise edition 1511 -&gt; please follow in this case the following procedure:</p>\n<ul>\n<li>Delete the content for automatic document adjustment of table S111 ( Report RMEBEIN6 (transaction MEI6)) and</li>\n<li>Rebuild the table entries for S111 for the used document categories by using the appropriate setup reports (see below). Make sure that the filter selection for rebuilding the S111 entries is meaningful  by selecting the appropriate organizational criteria ( time, org data, docnr, ...) [Rebuild is in SAP S/4HANA]</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Doc. category </strong></p>\n</td>\n<td>\n<p><strong>Description</strong></p>\n</td>\n<td>\n<p><strong>Report for rebuilding  S111 entries</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>01</p>\n</td>\n<td>\n<p>Purchase Order</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>02</p>\n</td>\n<td>\n<p>Scheduling Agreement</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>10</p>\n</td>\n<td>\n<p>Sales Price Calculation</p>\n</td>\n<td>\n<p>RWVKP012</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>11</p>\n</td>\n<td>\n<p>Sales Price Calculation Index</p>\n</td>\n<td>\n<p>RWVKP012</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>20</p>\n</td>\n<td>\n<p>Customer billing document</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>30</p>\n</td>\n<td>\n<p>Vendor billing document</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>31</p>\n</td>\n<td>\n<p>Expense document</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>32</p>\n</td>\n<td>\n<p>Vendor settlement document</p>\n</td>\n<td>\n<p>RMEBEIV3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>35</p>\n</td>\n<td>\n<p>Remuneration list</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>40</p>\n</td>\n<td>\n<p>Vendor settlement request</p>\n</td>\n<td>\n<p>RMEBEIZ3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>50</p>\n</td>\n<td>\n<p>Assortment List</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>55</p>\n</td>\n<td>\n<p>POS-Outbound</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>60</p>\n</td>\n<td>\n<p>Customer settlement</p>\n</td>\n<td>\n<p>RMEBEIK3</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>If you want to process multiple applications in one step (subsequent settlement and automatic document adjustment), you can also use report RMCENEUA.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Notes: 2192984, 2215169</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 3, "refer_note": [{"note": "2215169", "noteTitle": "2215169 - S4TC Pre-Transition Checks for Automatic Document Adjustment", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the pre-transition checks it will be checked if the application 'Automatic Document Adjustment' is actively used in the customer system.</p>\n<p>The pre-transition checks identify whether condition types are maintained for automatic document adjustment in Customizing (table T6I1) and whether there are entries in table S111 with origin 'pricing' (S111-KVEWE = 'A'). But this is only a technical check and cannot be interpreted in that way that the customer is actually using this functionality. Therefore, please make sure that the customer is really using the process of automatic document adjustment by also asking in the purchase department. If automatic document adjustment is not used you can ignore the following information.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>WIND; S111; Direct Entry</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, the following differences to the process of automatic document adjustment apply:</p>\n<ol>\n<li>The direct entry function is activated for all document categories to simplify the creation of worklist entries, so transaction MEI4 cannot be used anymore.</li>\n<li>Because the field length  of the Material Number data element (MATNR)  is enhanced to 40 characters, the field length of data element 'VAKEY' had to be adjusted accordingly.  This has an impact for the index table S111 if the material number is part of the 'VAKEY'.</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p> 1)  In SAP S/4HANA, the indicator for direct entry is set now automatically during the update process of conditions. This means that:</p>\n<ul>\n<li>When a user saves a condition change - including the creation of a new condition - an entry is made in the worklist ( table WIND ) for all document categories with valid Customizing settings.</li>\n<li>For worklist creation, it is not required anymore to use the transaction MEI4 .</li>\n</ul>\n<p>(For more information, please see SAP Note 519685 (Conversion of the procedure for Worklist creation ).</p>\n<p>Note also that change pointers do not have to be updated for message type CONDBI if the 'direct entries' are activated. With the conversion to the 'direct entry' procedure, you can deactivate the <a href=\"sapevent:DOCU_LINK\\DS:SIMG.W_DF_LT_0820\" target=\"_blank\"><strong>update of change pointers</strong></a> if you do not use it for other purposes.)</p>\n<p>2.)  Impact for index table S111: As explained above, if the material number is part of the field S111-VAKEY then these records cannot be used anymore after migration from ERP to S4HANA for automatic document adjustement</p>\n<ul>\n<li>SAP recommends strongly  that you process all open worklist entries completely before the conversion to SAP S/4HANA. In this case, no activities for automatic document adjustment are required in S/4HANA.</li>\n<li>If the worklist cannot be completed in the SAP ERP system, you can proceed with the following procedure:</li>\n<ul>\n<li>Delete the relevant content for automatic document adjustment of table S111 ( Report RMEBEIN6 (transaction MEI6)) by selecting appropriate filter criteria ( time intervall--)  and</li>\n<li>Rebuild these table entries for S111 for the used document categories by using the appropriate setup reports (see below). Make sure that the filter selection for rebuilding the S111 entries is meaningful  by selecting the appropriate organizational criteria ( time, org data, docnr...) [Rebuild is done in SAP S/4HANA]</li>\n</ul>\n</ul>\n<p> </p>\n<div class=\"table-wrap\">\n<div class=\"table-responsive\"><table class=\"confluenceTable tablesorter\">\n<thead>\n<tr class=\"sortableHeader\"><th class=\"confluenceTh sortableHeader\">\n<div class=\"tablesorter-header-inner\"><strong>Doc. category </strong></div>\n</th><th class=\"confluenceTh sortableHeader\">\n<div class=\"tablesorter-header-inner\">Description</div>\n</th><th class=\"confluenceTh sortableHeader\">\n<div class=\"tablesorter-header-inner\"><strong>Report for rebuilding  S111 and WIND entries</strong></div>\n</th></tr>\n</thead>\n<tbody>\n<tr>\n<td class=\"confluenceTd\">01</td>\n<td class=\"confluenceTd\">Purchase Order  </td>\n<td class=\"confluenceTd\">RMEBEIN3</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">02</td>\n<td class=\"confluenceTd\">Scheduling Agreement </td>\n<td class=\"confluenceTd\">RMEBEIN3</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">10</td>\n<td class=\"confluenceTd\">Sales Price Calculation </td>\n<td class=\"confluenceTd\">RWVKP012</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">11</td>\n<td class=\"confluenceTd\">Sales Price Calculation Index</td>\n<td class=\"confluenceTd\">RWVKP012</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">20</td>\n<td class=\"confluenceTd\">Customer billing document</td>\n<td class=\"confluenceTd\">RMEBEIL3</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">30</td>\n<td class=\"confluenceTd\">Vendor billing document</td>\n<td class=\"confluenceTd\">RMEBEIL3</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">31</td>\n<td class=\"confluenceTd\">Expense document  </td>\n<td class=\"confluenceTd\">RMEBEIL3</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">32</td>\n<td class=\"confluenceTd\">Vendor settlement document </td>\n<td class=\"confluenceTd\">RMEBEIV3</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">35</td>\n<td class=\"confluenceTd\">Remuneration list    </td>\n<td class=\"confluenceTd\">RMEBEIL3</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">40</td>\n<td class=\"confluenceTd\">Vendor settlement request </td>\n<td class=\"confluenceTd\">RMEBEIZ3</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">50</td>\n<td class=\"confluenceTd\">Assortment List     </td>\n<td class=\"confluenceTd\">RMEBEIN3</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">55</td>\n<td class=\"confluenceTd\">POS-Outbound </td>\n<td class=\"confluenceTd\">RMEBEIN3</td>\n</tr>\n<tr>\n<td class=\"confluenceTd\">60</td>\n<td class=\"confluenceTd\">Customer settlement  </td>\n<td class=\"confluenceTd\">RMEBEIK3</td>\n</tr>\n</tbody>\n</table></div>\n</div>\n<p>If you want to process multiple applications in one step (subsequent settlement and automatic document adjustment), you can also use report RMCENEUA.</p>\n<p> Afterwards you can start automatic document adjustement with tra. MEI1 or MEI2.</p>", "noteVersion": 6}, {"note": "2192984", "noteTitle": "2192984 - S4TC SAP_APPL  Pre-Transition Checks for Automatic Document Adjustement", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span>Pre-Transition Checks for Automatic Document Adjustment have to be executed before the upgrade to S/4.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC; S111; WIND</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Installation of pre-transition check class for Automatic Document Adjustment.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Refer to the attached Correction Instructions</p>", "noteVersion": 11}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If you actively use the application 'Automatic Document Adjustment' in your system, adjust your impacted custom code accordingly."}, {"Activity": "Business Operations", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If in the ERP system the worklist could not be completely processed, a rebuilding of the worklist for automatic document adjustment is required in SAP S/4HANA. Please follow th e procedure outlined in SAP Note 2267742."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Users must be made aware of the differences to the process of automatic document adjustment in SAP S4/HANA as per SAP Note 2267742."}]}