SAP Note 1298788 outlines the prerequisites for using the BI 7.x Precalculation service with SAP GUI 7.30. Here's a summary:

**Symptom**: The note provides essential pre-requisite information for the BI 7.x Precalculation delivered with SAP GUI 7.30.

**Other Terms**: The note references standalone installation, .NET Framework, Excel 2007, Office 2007, Office 2010, and variant.

**Reason and Prerequisites**: 
- BI 7.x Precalculation patches are available for download from the Service Marketplace.
- The machine designated as the Precalculation Server must have the corresponding BI 7.X Frontend Patch installed.

**Solution**:
- Review the following SAP Notes before installing the precalculation service:
   - 1013201, which details Hardware & Software requirements
   - 1013139, which lists prerequisites for the BI 7.x frontend
   - 1013207, which is an installation guide
- Other SAP Notes that are recommended to understand general information, limitations, and installation methods for precalculation, as well as the precalculation service delivery schedule.

**Prerequisites**:
- The precalculation application depends on the Business Explorer patch level installed on your machine. There is a direct dependency between the precalculation version and the BEx version.
- The note offers an example showing the required frontend support package for a specific precalculation version and underscores the importance of matching these versions for successful precalculation service installation.

**Service Properties**: 
- Instructions are provided to configure the "SAP BW Precalculation Service Multi Instance" via the services management console (services.msc), detailing how to set the service to log on using a specified account.

The note highlights that further information on hardware, software requirements, and limitations can be found in SAP Note 1236773, and recommends checking SAP Note 1145589 for future release schedules of the precalculation service.