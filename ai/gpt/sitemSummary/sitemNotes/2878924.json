{"Request": {"Number": "2878924", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 351, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002878924?language=E&token=4F56E40A2977B84D354D22C719A3000B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002878924", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2878924"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "How To"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.03.2020"}, "SAPComponentKey": {"_label": "Component", "value": "FI-FIO-TV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Trip Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2878924 - Fiori My Travel Management (Version 2) - Adding Actions Using Annotations"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You can use annotations to add specific actions in Travel applications. Actions trigger an interaction with the back end, calling an OData service and trigger a navigation when necessary.</p>\r\n<p>You can use extensions to add custom actions to the List Page and the Object Page of Travel Fiori application.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<p>SAP Fiori app \"My Travel and Expenses (Version 2)\" or \"My Travel Requests (Version 2)\"</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<p>Standard application does not&#160;meet your requirements.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>Missing feature</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>Fiori Travel Applications only support &#8220;annotated generic actions&#8221; handle via OData Function Import. Based on response of OData Function Import, these actions can trigger a Navigation.</p>\r\n<p>Navigation is triggered when entity or sub-entity in response of OData Function Import call is different from the entity or sub-entity which is currently displayed.</p>\r\n<p>Since version 2.0.23 of Travel Reuse components (SAP Note 2887377), custom actions can open PDF documents or external links:</p>\r\n<ul>\r\n<li>To open a PDF Function Import must return an entity with a property named &#8220;PDF&#8221;. Source link of the PDF must be defined in a property named &#8220;URL&#8221;.</li>\r\n<li>To open external navigation, link must be defined in a property named &#8220;ExternalLink&#8221;.</li>\r\n</ul>\r\n<p>You can define custom actions for the following:</p>\r\n<ul>\r\n<li>List Page global action. This type of action refers to the whole application.</li>\r\n<li>Table action toolbar of the List Page.</li>\r\n<li>Header action of the Object Page.</li>\r\n<li>Table action toolbar of the Object Page.</li>\r\n<li>Form action in a section on the Object page.</li>\r\n<li>Footer action bar on the Object Page.</li>\r\n</ul>\r\n<p>These custom actions are displayed as buttons on the UI. When the user selects the action, UI calls OData Function Import.</p>\r\n<ul>\r\n<li>Table toolbar of the list page</li>\r\n</ul>\r\n<p>&lt;Annotations xmlns=<strong>\"<span style=\"text-decoration: underline;\">http://docs.oasis-open.org/odata/ns/edm</span>\"</strong> Target=<strong>\"Entity\"</strong>&gt;</p>\r\n<p><strong>&#160; </strong>&lt;Annotation Term=<strong>\"com.sap.vocabularies.UI.v1.LineItem\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.DataFieldForAction\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Action\"</strong> String=<strong>\"FunctionImport\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Label\"</strong> String=<strong>\"Function Import Label\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"IconUrl\"</strong> Path=<strong>\"sap-<span style=\"text-decoration: underline;\">icon://icon</span>\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Criticality\"</strong> EnumMember=<strong>\"UI.CriticalityType/Critical\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Record&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;/Collection&gt;</p>\r\n<p><strong>&#160; </strong>&lt;/Annotation&gt;</p>\r\n<p>&lt;/Annotations&gt;</p>\r\n<p>In this specific case only, you can define the generic action as &#8220;Critical&#8221; via &#8220;Criticality&#8221; property. Action button will be displayed on the right of the Table Toolbar of the List Page.</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Header of the object page</li>\r\n</ul>\r\n<p>&lt;Annotations xmlns=<strong>\"<span style=\"text-decoration: underline;\">http://docs.oasis-open.org/odata/ns/edm</span>\"</strong> Target=<strong>\"Entity\"</strong>&gt;</p>\r\n<p><strong>&#160; </strong>&lt;Annotation Term=<strong>\"com.sap.vocabularies.UI.v1.</strong><strong>Identification\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.DataFieldForAction\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Action\"</strong> String=<strong>\"FunctionImport\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Label\"</strong> String=<strong>\"Function Import Label\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"IconUrl\"</strong> Path=<strong>\"sap-<span style=\"text-decoration: underline;\">icon://icon</span>\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Determining\"</strong> Bool=<strong>\"false\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Record&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;/Collection&gt;</p>\r\n<p><strong>&#160; </strong>&lt;/Annotation&gt;</p>\r\n<p>&lt;/Annotations&gt;</p>\r\n<p>&#160;</p>\r\n<p>&lt;Annotations xmlns=<strong>\"<span style=\"text-decoration: underline;\">http://docs.oasis-open.org/odata/ns/edm</span>\"</strong> Target=<strong>\"Entity\"</strong>&gt;</p>\r\n<p><strong>&#160; </strong>&lt;Annotation Term=<strong>\"com.sap.vocabularies.UI.v1.LineItem\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.DataFieldForAction\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Action\"</strong> String=<strong>\"FunctionImport\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Label\"</strong> String=<strong>\"Function Import Label\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"IconUrl\"</strong> Path=<strong>\"sap-<span style=\"text-decoration: underline;\">icon://icon</span>\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Criticality\"</strong> EnumMember=<strong>\"UI.CriticalityType/Critical\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Record&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;/Collection&gt;</p>\r\n<p><strong>&#160; </strong>&lt;/Annotation&gt;</p>\r\n<p>&lt;/Annotations&gt;</p>\r\n<p>In this specific case only, you can define the generic action as &#8220;Critical&#8221; via &#8220;Criticality&#8221; property. Action button will be displayed on the right of the Table Toolbar of the List Page.</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Header of the object page</li>\r\n</ul>\r\n<p>&lt;Annotations xmlns=<strong>\"<span style=\"text-decoration: underline;\">http://docs.oasis-open.org/odata/ns/edm</span>\"</strong> Target=<strong>\"Entity\"</strong>&gt;</p>\r\n<p><strong>&#160; </strong>&lt;Annotation Term=<strong>\"com.sap.vocabularies.UI.v1.</strong><strong>Identification\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.DataFieldForAction\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Action\"</strong> String=<strong>\"FunctionImport\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Label\"</strong> String=<strong>\"Function Import Label\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"IconUrl\"</strong> Path=<strong>\"sap-<span style=\"text-decoration: underline;\">icon://icon</span>\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Determining\"</strong> Bool=<strong>\"false\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Record&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;/Collection&gt;</p>\r\n<p><strong>&#160; </strong>&lt;/Annotation&gt;</p>\r\n<p>&lt;/Annotations&gt;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Table toolbar for a specific table on the object page</li>\r\n</ul>\r\n<p>&lt;Annotations xmlns=<strong>\"<span style=\"text-decoration: underline;\">http://docs.oasis-open.org/odata/ns/edm</span>\"</strong> Target=<strong>\"Entity\"</strong>&gt;</p>\r\n<p><strong>&#160; </strong>&lt;Annotation Term=<strong>\"com.sap.vocabularies.UI.v1.LineItem\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.DataFieldForAction\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Action\"</strong> String=<strong>\"FunctionImport\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Label\"</strong> String=<strong>\"Function Import Label\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"IconUrl\"</strong> Path=<strong>\"sap-<span style=\"text-decoration: underline;\">icon://icon</span>\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Record&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;/Collection&gt;</p>\r\n<p><strong>&#160; </strong>&lt;/Annotation&gt;</p>\r\n<p>&lt;/Annotations&gt;</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Table inline for a specific table on the object page</li>\r\n</ul>\r\n<p>&lt;Annotations xmlns=<strong>\"<span style=\"text-decoration: underline;\">http://docs.oasis-open.org/odata/ns/edm</span>\"</strong> Target=<strong>\"Entity\"</strong>&gt;</p>\r\n<p><strong>&#160; </strong>&lt;Annotation Term=<strong>\"com.sap.vocabularies.UI.v1.LineItem\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.DataFieldForAction\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Action\"</strong> String=<strong>\"FunctionImport\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Label\"</strong> String=<strong>\"Function Import Label\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"IconUrl\"</strong> Path=<strong>\"sap-<span style=\"text-decoration: underline;\">icon://icon</span>\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Inline\"</strong> Bool=<strong>\"true\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Record&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;/Collection&gt;</p>\r\n<p><strong>&#160; </strong>&lt;/Annotation&gt;</p>\r\n<p>&lt;/Annotations&gt;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Table toolbar for a specific table on the object page</li>\r\n</ul>\r\n<p>&lt;Annotations xmlns=<strong>\"<span style=\"text-decoration: underline;\">http://docs.oasis-open.org/odata/ns/edm</span>\"</strong> Target=<strong>\"Entity\"</strong>&gt;</p>\r\n<p><strong>&#160; </strong>&lt;Annotation Term=<strong>\"com.sap.vocabularies.UI.v1.LineItem\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.DataFieldForAction\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Action\"</strong> String=<strong>\"FunctionImport\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Label\"</strong> String=<strong>\"Function Import Label\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"IconUrl\"</strong> Path=<strong>\"sap-<span style=\"text-decoration: underline;\">icon://icon</span>\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Record&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;/Collection&gt;</p>\r\n<p><strong>&#160; </strong>&lt;/Annotation&gt;</p>\r\n<p>&lt;/Annotations&gt;</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Table inline for a specific table on the object page</li>\r\n</ul>\r\n<p>&lt;Annotations xmlns=<strong>\"<span style=\"text-decoration: underline;\">http://docs.oasis-open.org/odata/ns/edm</span>\"</strong> Target=<strong>\"Entity\"</strong>&gt;</p>\r\n<p><strong>&#160; </strong>&lt;Annotation Term=<strong>\"com.sap.vocabularies.UI.v1.LineItem\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.DataFieldForAction\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Action\"</strong> String=<strong>\"FunctionImport\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Label\"</strong> String=<strong>\"Function Import Label\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"IconUrl\"</strong> Path=<strong>\"sap-<span style=\"text-decoration: underline;\">icon://icon</span>\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Inline\"</strong> Bool=<strong>\"true\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Record&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;/Collection&gt;</p>\r\n<p><strong>&#160; </strong>&lt;/Annotation&gt;</p>\r\n<p>&lt;/Annotations&gt;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Form in a section on the object page</li>\r\n</ul>\r\n<p>&lt;Annotations xmlns=<strong>\"<span style=\"text-decoration: underline;\">http://docs.oasis-open.org/odata/ns/edm</span>\"</strong> Target=<strong>\"Entity\"</strong>&gt;</p>\r\n<p><strong>&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.FieldGroupType\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Data\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.DataFieldForAction\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Action\"</strong> String=<strong>\"FunctionImport\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Label\"</strong> String=<strong>\"Function Import Label\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Record&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;/PropertyValue&gt;</p>\r\n<p><strong>&#160; </strong>&lt;/Annotation&gt;</p>\r\n<p>&lt;/Annotations&gt;</p>\r\n<p>&#160;</p>\r\n<ul>\r\n<li>Footer bar on the object page</li>\r\n</ul>\r\n<p>&lt;Annotations xmlns=<strong>\"<span style=\"text-decoration: underline;\">http://docs.oasis-open.org/odata/ns/edm</span>\"</strong> Target=<strong>\"Entity\"</strong>&gt;</p>\r\n<p><strong>&#160; </strong>&lt;Annotation Term=<strong>\"com.sap.vocabularies.UI.v1.LineItem\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;Collection&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;Record Type=<strong>\"com.sap.vocabularies.UI.v1.Identification\"</strong>&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Action\"</strong> String=<strong>\"FunctionImport\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Label\"</strong> String=<strong>\"Function Import Label\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong>&lt;PropertyValue Property=<strong>\"Determining\"</strong> Bool=<strong>\"true\"</strong>/&gt;</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160; </strong>&lt;/Record&gt;</p>\r\n<p><strong>&#160;&#160;&#160; </strong>&lt;/Collection&gt;</p>\r\n<p><strong>&#160; </strong>&lt;/Annotation&gt;</p>\r\n<p>&lt;/Annotations&gt;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<p>See also the document in attachment (2878924 - Fiori My Travel&#160;Management (Version 2) - Adding Actions Using Annotation.docx) which explains different extensibility cases and provides code examples.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: windowtext; line-height: 107%; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: SimSun; mso-fareast-theme-font: minor-fareast; mso-hansi-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-bidi-theme-font: minor-bidi;\">MTE V2, MTR V2, SAP Fiori, TRV 2.0, &#160;Extensibility.</span></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-FIO-TV-MTR (My Travel Requests)"}, {"Key": "Other Components", "Value": "FI-FIO-TV-MTE (My Travel and Expenses)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I028128)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I040414)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878924/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878924/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878924/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878924/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878924/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878924/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878924/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878924/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878924/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "2878924 - Fiori My Travel and Expenses (Version 2) - Adding Actions Using Annotations.docx", "FileSize": "33", "MimeType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900000047422020&iv_version=0001&iv_guid=00109B36DB361EDA998E9CC8245840D8"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2878933", "RefComponent": "FI-FIO-TV", "RefTitle": "Fiori My Travel Management (Version 2) – Other Extensions", "RefUrl": "/notes/2878933"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2693032", "RefComponent": "FI-FIO-TV-MTR", "RefTitle": "Fiori My Travel Requests (Version 2) - Extensibility concept", "RefUrl": "/notes/2693032 "}, {"RefNumber": "2775801", "RefComponent": "FI-FIO-TV", "RefTitle": "My Travel and Expenses (Version 2) - Extensibility concept", "RefUrl": "/notes/2775801 "}, {"RefNumber": "2878933", "RefComponent": "FI-FIO-TV", "RefTitle": "Fiori My Travel Management (Version 2) – Other Extensions", "RefUrl": "/notes/2878933 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}