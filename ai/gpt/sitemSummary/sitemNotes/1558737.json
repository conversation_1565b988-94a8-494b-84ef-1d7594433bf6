{"Request": {"Number": "1558737", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 358, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009241862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001558737?language=E&token=68762704AEC89ECB9E252AD893FA079F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001558737", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001558737/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1558737"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.12.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-GEN"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT."}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT.", "value": "BW-BCT-GEN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-GEN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1558737 - Data Sources released for ODP data replication API (obsolete - new SAP Note 2232584)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Attention: This note has been merged with others into the new SAP Note 2232584, which allows to release all tested Extractors at once. It is adviced to use the new note instead.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ETL Interface, Operational Delta Queue</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Please implement SAP Note 2232584 instead.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Please implement SAP Note 2232584 instead.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-CO-MD (BW only - Master Data Controlling)"}, {"Key": "Other Components", "Value": "BW-BCT-CO-PC (BW only - Product Cost Controlling)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-IM (BW only - Inventory Management)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-LIS (BW only - LIS)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-PUR (BW only - Purchase)"}, {"Key": "Other Components", "Value": "BW-BCT-CO-OM (BW only - Overhead Cost Controlling)"}, {"Key": "Other Components", "Value": "EIM-DS-ODP (SAP ERP Extractors)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-SLS (BW only - Sales)"}, {"Key": "Other Components", "Value": "BW-BCT-CO-PA (BW only - Profitability Analysis)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-BW (BW only - SD Content im BW System)"}, {"Key": "Other Components", "Value": "BW-BCT-EC (BW only - Enterprise Controlling)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D000153)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I041661)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001558737/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001558737/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001558737/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001558737/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001558737/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001558737/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001558737/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001558737/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001558737/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2232584", "RefComponent": "BC-BW-ODP", "RefTitle": "Release of SAP extractors for ODP replication (ODP SAPI)", "RefUrl": "/notes/2232584"}, {"RefNumber": "1806637", "RefComponent": "BW-BCT-GEN", "RefTitle": "Releasing ERP Extractors for ODP API (obsolete - new SAP Note 2232584)", "RefUrl": "/notes/1806637"}, {"RefNumber": "1585204", "RefComponent": "BC-EIM-ODP", "RefTitle": "ETL interface: Release of DataSources", "RefUrl": "/notes/1585204"}, {"RefNumber": "1560241", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1560241"}, {"RefNumber": "1541366", "RefComponent": "BW-BCT-SD-SLS", "RefTitle": "MC11V_0ITM, MC11V_0SCL: Complete ABR delta update", "RefUrl": "/notes/1541366"}, {"RefNumber": "1521883", "RefComponent": "BC-BW-ODP", "RefTitle": "ODP Data Replication API 1.0", "RefUrl": "/notes/1521883"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2799684", "RefComponent": "HAN-DP-SDI", "RefTitle": "How to test an ODP extractor with SAPI context on SAP HANA Smart Data Integration", "RefUrl": "/notes/2799684 "}, {"RefNumber": "2731192", "RefComponent": "EIM-DH-MD", "RefTitle": "SAP Data Hub - ABAP connection type for SAP Data Hub", "RefUrl": "/notes/2731192 "}, {"RefNumber": "1521883", "RefComponent": "BC-BW-ODP", "RefTitle": "ODP Data Replication API 1.0", "RefUrl": "/notes/1521883 "}, {"RefNumber": "1806637", "RefComponent": "BW-BCT-GEN", "RefTitle": "Releasing ERP Extractors for ODP API (obsolete - new SAP Note 2232584)", "RefUrl": "/notes/1806637 "}, {"RefNumber": "1585204", "RefComponent": "BC-EIM-ODP", "RefTitle": "ETL interface: Release of DataSources", "RefUrl": "/notes/1585204 "}, {"RefNumber": "1541366", "RefComponent": "BW-BCT-SD-SLS", "RefTitle": "MC11V_0ITM, MC11V_0SCL: Complete ABR delta update", "RefUrl": "/notes/1541366 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60020", "URL": "/supportpackage/SAPKH60020"}, {"SoftwareComponentVersion": "SAP_APPL 602", "SupportPackage": "SAPKH60210", "URL": "/supportpackage/SAPKH60210"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60309", "URL": "/supportpackage/SAPKH60309"}, {"SoftwareComponentVersion": "SAP_APPL 604", "SupportPackage": "SAPKH60410", "URL": "/supportpackage/SAPKH60410"}, {"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60505", "URL": "/supportpackage/SAPKH60505"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0001558737/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "605", "Number": "1558737 ", "URL": "/notes/1558737 ", "Title": "Data Sources released for ODP data replication API (obsolete - new SAP Note 2232584)", "Component": "BW-BCT-GEN"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}