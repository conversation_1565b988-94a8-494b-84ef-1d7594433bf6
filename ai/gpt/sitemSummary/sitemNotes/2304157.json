{"Request": {"Number": "2304157", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1272, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013614202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002304157?language=E&token=9A8E0A62A5AD3B5945423DDEA278DC61"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002304157", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2304157"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AA-LM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Leasing Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Accounting", "value": "FI-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Leasing Processing", "value": "FI-AA-LM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-LM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2304157 - Enhancement of \"Asset Accounting (new)\" for lessee integration using RE contract"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note contains functional enhancements for the integration of the lessee solution via the RE contract with Asset Accounting.</p>\r\n<p>You must implement this SAP Note</p>\r\n<ul>\r\n<li>if you want to use this function</li>\r\n<li>or if a follow-on correction references this SAP Note (even if you do <span style=\"text-decoration: underline;\">not</span> want to use the leasing functions).</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>CL_FAA_DOCUMENT_DELEGATION, CREATE_FAA_DOC_FROM_RLAMBU, FILL_POSTINGCONTROL_FROM_AMBU, FILL_POSTINGCONTROL_FROM_RAIFP, REACI, LEAS, leasing, RE contract</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Legal requirements</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the attached correction instructions.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D033895)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029438)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002304157/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002304157/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002304157/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002304157/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002304157/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002304157/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002304157/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002304157/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002304157/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2255555", "RefComponent": "RE-FX-LA", "RefTitle": "Valuation of leasing contracts (SAP Contract and Lease Management based on SAP RE-FX)", "RefUrl": "/notes/2255555 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10003INS4CORE", "URL": "/supportpackage/SAPK-10003INS4CORE"}, {"SoftwareComponentVersion": "EA-FIN 617", "SupportPackage": "SAPK-61713INEAFIN", "URL": "/supportpackage/SAPK-61713INEAFIN"}, {"SoftwareComponentVersion": "SAP_FIN 618", "SupportPackage": "SAPK-61804INSAPFIN", "URL": "/supportpackage/SAPK-61804INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 700", "SupportPackage": "SAPK-70010INSAPFIN", "URL": "/supportpackage/SAPK-70010INSAPFIN"}, {"SoftwareComponentVersion": "EA-FIN 700", "SupportPackage": "SAPK-70010INEAFIN", "URL": "/supportpackage/SAPK-70010INEAFIN"}, {"SoftwareComponentVersion": "SAP_FIN 720", "SupportPackage": "SAPK-72006INSAPFIN", "URL": "/supportpackage/SAPK-72006INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 730", "SupportPackage": "SAPK-73004INSAPFIN", "URL": "/supportpackage/SAPK-73004INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 4, "URL": "/corrins/0002304157/15841"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0002304157/19773"}, {"SoftwareComponent": "EA-FIN", "NumberOfCorrin": 2, "URL": "/corrins/0002304157/15842"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 7, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 48, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1943345 ", "URL": "/notes/1943345 ", "Title": "S2I: Error when posting cross-company-code documents with asset account assignment", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1953836 ", "URL": "/notes/1953836 ", "Title": "S2I: Error FAA_POST 011 for transferring a posting with asset account assignment from PSCD or Contract A/R & A/P", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1981871 ", "URL": "/notes/1981871 ", "Title": "S2I: Asset is locked when Parked document is being posted, error message AA003", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2012381 ", "URL": "/notes/2012381 ", "Title": "S2I: Fixed asset retirement Russia/Kazakhstan - dump with message AA484 for asset retirement posting with additional costs", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2014719 ", "URL": "/notes/2014719 ", "Title": "S2I: cannot correct errors on document line items", "Component": "FI-AA-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2015956 ", "URL": "/notes/2015956 ", "Title": "S2I: Error AAPO 007 for settlement with capitalization key", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2016114 ", "URL": "/notes/2016114 ", "Title": "Error AC-496 or short dump SFIN_FI-004 when transferring transactions during mid-year legacy data transfer", "Component": "FI-AA-AA-A"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2018270 ", "URL": "/notes/2018270 ", "Title": "S2I: Error for parking or posting parked documents", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2022923 ", "URL": "/notes/2022923 ", "Title": "S2I: Area selection is not filtered correctly which causes posting failed or wrongly", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2027693 ", "URL": "/notes/2027693 ", "Title": "S2I: <PERSON><PERSON> \"addtional asset account assignment\" does not work properly", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2028792 ", "URL": "/notes/2028792 ", "Title": "S2I: BAPI: Document reference fields are not filled", "Component": "FI-AA-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2029684 ", "URL": "/notes/2029684 ", "Title": "S2I: Dump MESSAGE_TYPE_X FAA_POST-042 when you post an integrated asset acquisition", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2052925 ", "URL": "/notes/2052925 ", "Title": "S2I: Error AU 133 on line item display or edit in ABxx Transaction", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2085665 ", "URL": "/notes/2085665 ", "Title": "S2I: Calculation of proportional values during settlement of asset under construction or full settlement from CO", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2101405 ", "URL": "/notes/2101405 ", "Title": "S2I: Error FAA_POST-042 when processing batch input sessions", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2107024 ", "URL": "/notes/2107024 ", "Title": "Settlement of an asset under construction: Error message AA 478", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2110884 ", "URL": "/notes/2110884 ", "Title": "S2I: Payment run generates incorrect posting to fixed asset", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2118603 ", "URL": "/notes/2118603 ", "Title": "Reference date not updated correctly when you settle an asset under construction", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2125464 ", "URL": "/notes/2125464 ", "Title": "Entering output for SPO relating to a fixed asset -> error FAA_POST011", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2147539 ", "URL": "/notes/2147539 ", "Title": "S2I: Balance in technical clearing account for document splitting with inheritance", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2153920 ", "URL": "/notes/2153920 ", "Title": "AB01: Non-permitted transaction type causes runtime error", "Component": "FI-AA-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2159608 ", "URL": "/notes/2159608 ", "Title": "Settlement of an asset under construction updates incorrect values in the parallel area", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2175328 ", "URL": "/notes/2175328 ", "Title": "S2I: Value sign is wrongly converted and posted after you maintained proportional values in AB01 Transactions", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2187647 ", "URL": "/notes/2187647 ", "Title": "Error FAA_POST 011 for reservation of goods issue for asset", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2225411 ", "URL": "/notes/2225411 ", "Title": "S2I: Multiple posting to asset via BAPI with disjunct ledger groups", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2237168 ", "URL": "/notes/2237168 ", "Title": "MIRO with fixed asset reference Error FAA_POST042 occurs as termination message", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2259597 ", "URL": "/notes/2259597 ", "Title": "Integrated fixed asset retirement with quantity results in incorrect error message AAPO169", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2272036 ", "URL": "/notes/2272036 ", "Title": "Negative settlement of CO element with capitalization differences", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2279640 ", "URL": "/notes/2279640 ", "Title": "AB08: Dump in CL_FAA_POSTING_LINE_ITEM_GEN", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "700", "Number": "1953836 ", "URL": "/notes/1953836 ", "Title": "S2I: Error FAA_POST 011 for transferring a posting with asset account assignment from PSCD or Contract A/R & A/P", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "700", "Number": "2021582 ", "URL": "/notes/2021582 ", "Title": "S2I: Updating the reference fields or customer fields in asset documents", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "1981871 ", "URL": "/notes/1981871 ", "Title": "S2I: Asset is locked when Parked document is being posted, error message AA003", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2012381 ", "URL": "/notes/2012381 ", "Title": "S2I: Fixed asset retirement Russia/Kazakhstan - dump with message AA484 for asset retirement posting with additional costs", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2014719 ", "URL": "/notes/2014719 ", "Title": "S2I: cannot correct errors on document line items", "Component": "FI-AA-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2015956 ", "URL": "/notes/2015956 ", "Title": "S2I: Error AAPO 007 for settlement with capitalization key", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2016114 ", "URL": "/notes/2016114 ", "Title": "Error AC-496 or short dump SFIN_FI-004 when transferring transactions during mid-year legacy data transfer", "Component": "FI-AA-AA-A"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2018270 ", "URL": "/notes/2018270 ", "Title": "S2I: Error for parking or posting parked documents", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2022923 ", "URL": "/notes/2022923 ", "Title": "S2I: Area selection is not filtered correctly which causes posting failed or wrongly", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2027693 ", "URL": "/notes/2027693 ", "Title": "S2I: <PERSON><PERSON> \"addtional asset account assignment\" does not work properly", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2028792 ", "URL": "/notes/2028792 ", "Title": "S2I: BAPI: Document reference fields are not filled", "Component": "FI-AA-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2029684 ", "URL": "/notes/2029684 ", "Title": "S2I: Dump MESSAGE_TYPE_X FAA_POST-042 when you post an integrated asset acquisition", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2052925 ", "URL": "/notes/2052925 ", "Title": "S2I: Error AU 133 on line item display or edit in ABxx Transaction", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2085665 ", "URL": "/notes/2085665 ", "Title": "S2I: Calculation of proportional values during settlement of asset under construction or full settlement from CO", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2101405 ", "URL": "/notes/2101405 ", "Title": "S2I: Error FAA_POST-042 when processing batch input sessions", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2107024 ", "URL": "/notes/2107024 ", "Title": "Settlement of an asset under construction: Error message AA 478", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2110884 ", "URL": "/notes/2110884 ", "Title": "S2I: Payment run generates incorrect posting to fixed asset", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2118603 ", "URL": "/notes/2118603 ", "Title": "Reference date not updated correctly when you settle an asset under construction", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2125464 ", "URL": "/notes/2125464 ", "Title": "Entering output for SPO relating to a fixed asset -> error FAA_POST011", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2147539 ", "URL": "/notes/2147539 ", "Title": "S2I: Balance in technical clearing account for document splitting with inheritance", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2153920 ", "URL": "/notes/2153920 ", "Title": "AB01: Non-permitted transaction type causes runtime error", "Component": "FI-AA-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2159608 ", "URL": "/notes/2159608 ", "Title": "Settlement of an asset under construction updates incorrect values in the parallel area", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2175328 ", "URL": "/notes/2175328 ", "Title": "S2I: Value sign is wrongly converted and posted after you maintained proportional values in AB01 Transactions", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2187647 ", "URL": "/notes/2187647 ", "Title": "Error FAA_POST 011 for reservation of goods issue for asset", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2225411 ", "URL": "/notes/2225411 ", "Title": "S2I: Multiple posting to asset via BAPI with disjunct ledger groups", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2237168 ", "URL": "/notes/2237168 ", "Title": "MIRO with fixed asset reference Error FAA_POST042 occurs as termination message", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2259597 ", "URL": "/notes/2259597 ", "Title": "Integrated fixed asset retirement with quantity results in incorrect error message AAPO169", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2272036 ", "URL": "/notes/2272036 ", "Title": "Negative settlement of CO element with capitalization differences", "Component": "FI-AA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2324381 ", "URL": "/notes/2324381 ", "Title": "Follow-on note for SAP Note 2013545", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2213744 ", "URL": "/notes/2213744 ", "Title": "No depreciations are posted for special reserves area", "Component": "FI-AA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2217275 ", "URL": "/notes/2217275 ", "Title": "Reversal of asset transactions: Incorrect reversal/line items missing from display", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2225411 ", "URL": "/notes/2225411 ", "Title": "S2I: Multiple posting to asset via BAPI with disjunct ledger groups", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2231678 ", "URL": "/notes/2231678 ", "Title": "Incorrect legacy data transfer via BAPI_FIXEDASSET_OVRTAKE_CREATE", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2233879 ", "URL": "/notes/2233879 ", "Title": "Duplicate year start values/problems with write-up or post-capitalization", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2235253 ", "URL": "/notes/2235253 ", "Title": "Missing documents during settlement with a zero amount", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2237168 ", "URL": "/notes/2237168 ", "Title": "MIRO with fixed asset reference Error FAA_POST042 occurs as termination message", "Component": "FI-AA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2252940 ", "URL": "/notes/2252940 ", "Title": "ABLDT/BAPI_FIXEDASSET_OVRTAKE_CREATE: Transfer of open items for assets under construction", "Component": "FI-AA-AA-A"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2259597 ", "URL": "/notes/2259597 ", "Title": "Integrated fixed asset retirement with quantity results in incorrect error message AAPO169", "Component": "FI-AA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2272036 ", "URL": "/notes/2272036 ", "Title": "Negative settlement of CO element with capitalization differences", "Component": "FI-AA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2285624 ", "URL": "/notes/2285624 ", "Title": "S4CORE: user specified currency type cannot be recognized in asset management", "Component": "FI-AA-AA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2294012 ", "URL": "/notes/2294012 ", "Title": "Legacy data transfer of open items of an AuC", "Component": "FI-AA-AA-A"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2305475 ", "URL": "/notes/2305475 ", "Title": "Short dump ASSERTION_FAILED for reversal of asset documents", "Component": "FI-AA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2324381 ", "URL": "/notes/2324381 ", "Title": "Follow-on note for SAP Note 2013545", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2225411 ", "URL": "/notes/2225411 ", "Title": "S2I: Multiple posting to asset via BAPI with disjunct ledger groups", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2237168 ", "URL": "/notes/2237168 ", "Title": "MIRO with fixed asset reference Error FAA_POST042 occurs as termination message", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "1966686 ", "URL": "/notes/1966686 ", "Title": "S2I: After Asset Transfer posted, parameter is set to receiving company code and sending asset", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2013545 ", "URL": "/notes/2013545 ", "Title": "SFIN: Wrong documents when posting with restriction on accounting principle", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2041891 ", "URL": "/notes/2041891 ", "Title": "SFIN: Accounting Principle is not correctly displayed in document simulation when using transaction ABXX", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2324381 ", "URL": "/notes/2324381 ", "Title": "Follow-on note for SAP Note 2013545", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2014219 ", "URL": "/notes/2014219 ", "Title": "Legacy data transfer with SAP_FIN 720", "Component": "FI-AA-AA-A"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2144868 ", "URL": "/notes/2144868 ", "Title": "Near-Zero-Downtime for \"Migration to SAP Accounting powered by SAP HANA\"", "Component": "FIN-MIG"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2147539 ", "URL": "/notes/2147539 ", "Title": "S2I: Balance in technical clearing account for document splitting with inheritance", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2153920 ", "URL": "/notes/2153920 ", "Title": "AB01: Non-permitted transaction type causes runtime error", "Component": "FI-AA-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2157195 ", "URL": "/notes/2157195 ", "Title": "Migration: FINS_RECON761 for fixed assets with legacy data transfer", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2175328 ", "URL": "/notes/2175328 ", "Title": "S2I: Value sign is wrongly converted and posted after you maintained proportional values in AB01 Transactions", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2178120 ", "URL": "/notes/2178120 ", "Title": "sFIN migration: Migration of AuCs legacy data transfer", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2187647 ", "URL": "/notes/2187647 ", "Title": "Error FAA_POST 011 for reservation of goods issue for asset", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2189962 ", "URL": "/notes/2189962 ", "Title": "Corrections for ensuring the document reference", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2213744 ", "URL": "/notes/2213744 ", "Title": "No depreciations are posted for special reserves area", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2217275 ", "URL": "/notes/2217275 ", "Title": "Reversal of asset transactions: Incorrect reversal/line items missing from display", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2225411 ", "URL": "/notes/2225411 ", "Title": "S2I: Multiple posting to asset via BAPI with disjunct ledger groups", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2231678 ", "URL": "/notes/2231678 ", "Title": "Incorrect legacy data transfer via BAPI_FIXEDASSET_OVRTAKE_CREATE", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2233879 ", "URL": "/notes/2233879 ", "Title": "Duplicate year start values/problems with write-up or post-capitalization", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2235253 ", "URL": "/notes/2235253 ", "Title": "Missing documents during settlement with a zero amount", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2237168 ", "URL": "/notes/2237168 ", "Title": "MIRO with fixed asset reference Error FAA_POST042 occurs as termination message", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2252940 ", "URL": "/notes/2252940 ", "Title": "ABLDT/BAPI_FIXEDASSET_OVRTAKE_CREATE: Transfer of open items for assets under construction", "Component": "FI-AA-AA-A"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2259597 ", "URL": "/notes/2259597 ", "Title": "Integrated fixed asset retirement with quantity results in incorrect error message AAPO169", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2272036 ", "URL": "/notes/2272036 ", "Title": "Negative settlement of CO element with capitalization differences", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2294012 ", "URL": "/notes/2294012 ", "Title": "Legacy data transfer of open items of an AuC", "Component": "FI-AA-AA-A"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2305475 ", "URL": "/notes/2305475 ", "Title": "Short dump ASSERTION_FAILED for reversal of asset documents", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2324381 ", "URL": "/notes/2324381 ", "Title": "Follow-on note for SAP Note 2013545", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2225411 ", "URL": "/notes/2225411 ", "Title": "S2I: Multiple posting to asset via BAPI with disjunct ledger groups", "Component": "FI-AA-AA-C"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2305475 ", "URL": "/notes/2305475 ", "Title": "Short dump ASSERTION_FAILED for reversal of asset documents", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2324381 ", "URL": "/notes/2324381 ", "Title": "Follow-on note for SAP Note 2013545", "Component": "FI-AA-AA-C"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2350250", "RefTitle": "Short dump ASSERTION_FAILED in BAPI_FIXEDASSET_OVRTAKE_CREATE after SAP Note 2304157", "RefUrl": "/notes/0002350250"}]}}}}}