{"Request": {"Number": "830982", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 250, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015870142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000830982?language=E&token=62AB6A808B9B4E7CAE699EA1D4CAB72A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000830982", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000830982/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "830982"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.05.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-RAC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Real Application Clusters (RAC)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Application Clusters (RAC)", "value": "BC-DB-ORA-RAC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-RAC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "830982 - SAP recommendations for Oracle RAC 9.2.0.x config."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SAP Recommendations </p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAP / RAC / Oracle9i Net Services</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note is intended to provide additional assistance on configuring SAP Systems and Oracle RAC.<br />For an introduction to the topic, refer to the white paper \"Configuring SAP R/3 4.6D for Use with Oracle Real Application Cluster\"&#x00A0;&#x00A0;(see SAP Service Marketplace).<br /><br /><br /><br />This covers in particular parameter settings, the user environment and configuration parameters on Unix.<br /><br />The note deals with three basic areas:<br />I. Oracle initialization parameters<br />II. SAP/Oracle environment variables<br />III. Oracle SQL*Net/Net8/Net9<br /><br /><B>I. Oracle instance parameters</B><br /><br />Oracle uses its parameter files to initialize and configure a database instance. Usually, these profiles are on the ORACLE_HOME/dbs directory. This note does not provide information about the differences between an ASCII profile (init&lt;SID&gt;.ora) and spfile (server parameter file) (see Note 601157). For RAC, an spfile.ora is mandatory for the BR tools.<br />The RAC option allows the Oracle database software to access a database from several database instances. These instances are called using instance-specific parameters. The parameters used in the standard, single-instance based implementation are still valid. In addition to these parameters, you must set further parameters in the RAC - environment<br />for the individual instances of a database.<br />We can differentiate between instance-specific and global RAC-specific parameters.<br />The global RAC-specific parameters must be defined on all instances in the same way as the above-mentioned standard parameters. The spfile used by all instances indicates this with * before the parameter name. Instance-specific parameters are displayed by the proposed instance name.<br /> Example:<br />*.cluster_database = true<br />C11001.thread = 001<br /><br /><B>Global RAC parameters to be defined:</B><br /><br />*.db_domain = WORLD<br />*.local_listener = LISTENER<br />*.cluster_database = true<br />*.max_commit_propagation_delay = 1<br /><br /><B>DB_DOMAIN</B><br /><br />This parameter specifies the second part of the database name. The first part is defined by db_name. Note that we are referring to the database, not the instance. To recap, RAC recognizes one database and several instances. Both parameters provide the unique name of the database.<br /><br />For example:<br />*.db_name = C11<br />*.db_domain = WORLD<br /><br />Therefore, the unique global database name is C11.WORLD.<br /><br />The term 'domain' is used by Oracle in just two contexts. In the first, it refers to the database name and in the second, entirely separate context, it is used for the network service names or SQL*NET/Net8/Net9 alias names.<br /><br />The standard domain WORLD originates from older Oracle releases. Network service names and their default domains are described later under point III.<br /><br /><B>LOCAL_LISTENER</B><br /><br />The parameter local_listener defines a network name. This name can be resolved as an address or address list of Oracle Net local listeners.<br />Oracle Net local listeners are listeners that run on the same machine as the Oracle instance. The address or address list is defined in the TNSNAMES.ORA file or in similar address repositories, provided these are configured for the system (for example, LDAB).<br /><br />For example:<br />*.local_listener = LISTENER<br /><br />The following default value is returned following resolution with the TNSNAMES.ORA (see point 3):&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ADDRESS = (PROTOCOL=TCP) (HOST=) (PORT=1521))<br /><br /><br /><B>CLUSTER_DATABASE</B><br /><br />The parameter determines whether Oracle 9i Real Application Clusters is activated or not. The parameter must be defined identically on all instances. If the parameter is set to false, the database can only be started with one instance.<br /><br />For example:<br />*.Cluster_database = true<br /><br />Possible values are true or false<br /><br /><br /><B>MAX_COMMIT_PROPAGATION_DELAY</B><br /><br />For detailed information about the parameter, refer to Note 794361. For R/3 applications, you must set this parameter to 1.<br /><br />For example:<br />*.Max_commit_propagation_delay = 1.<br /><br /><br /><br />Instance-specific parameters to be defined:<br /><br />C11001.instance_number = 1<br />C11001.thread = 1<br />C11001.instance_name = C11001<br />C11001.service_names = (C11, C11001)<br />C11001.undo_tablespace = PSAPUNDO_001<br />C11001.ifile = /oracle/C11/920_32/dbs/initcommon.ora<br /><br /><br /><B>INSTANCE_NUMBER</B><br /><br />The parameter corresponds to a number that assigns extents of data blocks to a specific instance. The instance number ensures that an instance uses precisely those extents assigned to it for inserts and updates following startup. This means that the instance works independently in its own allocated area and does not collide with other instances.<br /><br />For example:<br />C11001.instance_number = 001<br /><br /><br /><B>THREAD</B><br /><br />The parameter specifies the redo thread number. The number corresponds to the THREAD initializing parameter for the statement Alter DATABASE ADD LOGFILE.<br />(alter database add logfile &#x00B4;tread 1 group 11 (&#x00B4;/oracle/C11/origlogA/log_g11m1.dbf&#x00B4; )<br />Any unassigned number can be used, but the same number must not be used by two instances simultaneously. We recommend that you select a thread number that is identical to the instance number.<br />Leading zeros are not taken into account for the number, but the instance name can be more clearly identified if its length is constant and conforms with a clear syntax.<br /><br />For example:<br />C11001.thread = 001<br /><br /><br /><B>INSTANCE_NAME</B><br /><br /><br />The parameter represents the name of an instance. The instance name is set just like the Oracle System Identifier (SID). The instance name ensures that an instance is identified as unique. Usually, the name in a cluster consists of: db_name (C11) and thread_number (001).<br /><br />For example:<br />C11001.instance_name = C11001<br /><br /><br /><br /><B>SERVICE_NAMES</B><br /><br />As of Oracle 8.1, all instances are dynamically registered with their listener. By default, an instance registers itself to the listener with its global database name. This default value can be overridden with the SERVICE_NAMES parameter. If the parameter is defined in the Oracle parameter file, the instance logs on to the listener with the specified values. Bear in mind that an instance can register several service names.<br />From a client view, a service name is the same as the network service name. A connection setup using the network is performed as follows:<br />sqlplus &lt;user&gt;/&lt;password&gt;@&lt;network service name&gt;<br />The network service name is used to perform the mapping or addressing function. (See also point III).<br /><br />For example:<br />C11001.service_names = (C11, C11001)<br /><br /><br /><B>UNDO_TABLESPACE</B><br /><br />This parameter specifies the name of the tablespace that is to be used by the instance for Undo management. SAP and Oracle recommend a separate Undo tablespace for each instance. For the management function, the global parameter undo_management = auto must also be set. For the sake of clarity, the two parameters should be listed below each other. Undo_management must have the same setting on all instances.<br /><br />For example:<br />C11001.undo_tablespace = &lt;tablespacename&gt;<br />*.undo_management = auto<br /><br /><br /><br /><br /><br /><br /><br /><B>II. SAP/Oracle OS environment variables</B><br /><br /><br />This section deals with the environment variables set for the operating system users. SAP appoints exactly one user (&lt;SAPSID&gt;adm) for an SAP R/3 system to carry out administration tasks on R/3 and one user for the database (ora&lt;DBSID&gt;).<br /><br /><br /><br />Required parameters for the ora&lt;DBSID&gt; user with DB Version 9.2.x, using the C-shell:<br /><br />setenv THREAD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;001<br />setenv DB_SID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;C11<br />setenv ORACLE_SID&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;$DB_SID$THREAD<br />setenv ORACLE_HOME&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /oracle/&lt;DBSID&gt;/920_32<br />setenv ORACLE_BASE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /oracle<br />setenv ORA_NLS33&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /oracle/&lt;DBSID&gt;/920_32/ocommon/nls/admin/data<br />setenv NLS_LANG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AMERICAN_AMERICA.WE8DEC<br />setenv LD_LIBRARY_PATH /oracle/&lt;DBSID&gt;/920_32/lib:/usr/sap/&lt;SAPSID&gt;/exe/run<br /><br />In this case, &lt;DBSID&gt; corresponds to the value of $DB_SID.<br /><br /><B>THREAD</B><br /><br />The environment variable THREAD corresponds to the Oracle profile parameter THREAD. Although the parameter is not relevant for the connection setup, it is used to set ORACLE_SID. THREAD can be set to NOPS (NO OPS) for non-RAC databases.<br /><br />For example:<br />setenv THREAD 001<br /><br /><B>DB_SID</B><br /><br />SAP uses the environment variable DB_SID as a database system ID. The Oracle database does not recognize this parameter. The parameter is not relevant for the connection between client and database.&#x00A0;&#x00A0;However, parameters such as ORACLE_HOME are defined using DB_SID.<br /><br />For example:<br />setenv&#x00A0;&#x00A0;DB_SID&#x00A0;&#x00A0;C11<br /><br /><br /><B>ORACLE_SID</B><br /><br />The environment variable ORACLE_SID is available purely as an environment variable and is not saved anywhere in the database. This variable is required when you start the instance. It is used to define the parameter file init&lt;ORACLE_SID&gt;.ora. In addition, the allocated shared memory (SGA) is designated and addressed using ORACLE_SID. During the connect to the database without the use of network services, the system checks whether the SGA matches the environment variable. If they do not match, Oracle returns the message \"Connected to an idle instance\". (At startup, Oracle would try to start another instance with an SGA - identifier corresponding to ORACLE_SID)<br />Note 168243 includes a description of how to start a database from a remote location. This is relevant in the RAC environment if the BR-tools are used for backup and recovery. For the offline backup, you must ensure that all instances are shut down (this is carried out by the BR tools using a remote logon).<br /><br />For example:<br />setenv&#x00A0;&#x00A0;ORACLE_SID&#x00A0;&#x00A0;C11001<br /><br /><br /><B>ORACLE_HOME</B><br /><br />The environment variable ORACLE_HOME defines the root for the installation directory for an Oracle installation. Using this directory as a starting point, Oracle creates search paths for libraries, message files, network parameter files and so on.<br />Usually, the path is set to /oracle/$DB_SID/&lt;Version&gt;, with an underscore in the version differentiating between either a 32-bit or 64-bit architecture.<br /><br />For example:<br />setenv ORACLE_HOME&#x00A0;&#x00A0;/oracle/C11/920_32<br /><br /><B>ORACLE_BASE</B><br /><br />The environment variable ORACLE_BASE defines the root for all Oracle installations. Using this directory as a starting point, you can locate the different databases and their versions.<br /><br />For example:<br />setenv&#x00A0;&#x00A0;ORACLE_BASE&#x00A0;&#x00A0; /oracle<br /><br /><br /><B>ORA_NLS33</B><br /><br />The environment variable ORA_NLS33 defines the path of the directory where Oracle performs a search for National Language Support (NLS) files. This ensures that database utilities, error messages, sort, order, date, time, and so on<br />are automatically adjusted to the native language and locales.<br /><br />For example:<br />setenv&#x00A0;&#x00A0;ORA_NLS33&#x00A0;&#x00A0; /oracle/&lt;DBSID&gt;/920_32/ocommon/nls/admin/data<br /><br /><br /><B>NLS_LANG</B><br /><br />The environment variable NLS_LANG defines the character set of the client host.<br />The character set that Oracles uses is independent of the operating system on which the database runs. An Oracle database running on an English-only UNIX system can be addressed by a Russian client. The operating system on which the client is running is the only system that must support Cyrillic with a corresponding code page.<br />If a query is executed and the character set of the database is not the same as the character set of the client, Oracle converts the characters into suitable values. An incorrect entry can cause major display problems.<br /><br />For example:<br />Setenv NLS_LANG&#x00A0;&#x00A0;AMERICAN_AMERICA.WE8DEC<br /><br /><br /><B>LD_LIBRARY_PATH</B><br /><B>LIBPATH </B><br /><B>SHLIB_PATH</B><br /><B>DIR_LIBRARY</B><br /><br /><br />The environment variable LD_LIBRARY_PATH represents the environment variable that depends on the operating system and specifies the search path for libraries. LD_LIBRARY_PATH is used on Solaris. LIBPATH is the path for AIX 5.1, SHLIB_PATH the path for HP-UX, and DIR_LIBRARY the path for Linux.<br /><br />Example of Linux:<br />setenv DIR_LIBRARY&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /oracle/C11/920_32/lib<br /><br /><br /><br />Required parameters for the &lt;SAPSID&gt;adm user with DB Version 9.2.x using the C-shell:<br />This note only describes those environment variables required to access a database from a client server. It provides no further details about ORACLE_HOME, ORA_NLS33, NLS_LANG and LD_LIBRARY_PATH.<br /><br />setenv&#x00A0;&#x00A0; dbs_ora_tnsname&#x00A0;&#x00A0; C11<br />setenv&#x00A0;&#x00A0; TNS_ADMIN /oracle/C11/920_32/network/admin<br /><br /><br /><B>dbs_ora_tnsname</B><br /><br />The environment variable dbs_ora_tnsname is defined by SAP.<br />The variable should appear in lower case. It represents the network service name used by SAP to log onto the database instance.<br />A connection is set up via the network as follows:<br />sqlplus &lt;user&gt;/&lt;password&gt;@&lt;network service name&gt;<br />The network service name is described in detail under point III at a later stage.<br />It is essential to note where SAP obtains this network service name and to bear in mind that this name is not necessarily related to other parameters.<br /><br />For example:<br />setenv&#x00A0;&#x00A0; dbs_ora_tnsname&#x00A0;&#x00A0; C11<br /><br /><B>TNS_ADMIN</B><br /><br />The environment variable TNS_ADMIN defines the search path used by the Oracle client to find the file tnsnames.ora. Tnsnames.ora is the file that defines which connections can be established with which alias on a database instance. The variable is also used in mapping or addressing the network service name. For example, there must be a process listener listening on the sapdev host for the TCP protocol on port 1527 to accept incoming connection requests.<br /><br /><br /><B>III. Oracle SQL*Net / Net8 / Net9</B><br /><br /><br />SQL*Net is Oracle&#x00B4;s client/server middleware product. SQL*Net allows you to set up transparent connections between client tools and the database or from one database instance to another. SQL*Net can support different network protocols and operating system connections. This note covers the usual connections used between the SAP client and database instance.<br />A connection is set up via the network as follows:<br />Sqlplus &lt;user&gt;/&lt;password&gt;@&lt;networkservername&gt;<br /><br />The tnsnames.ora file contains the required information to resolve the network service.<br /><br />Excerpt from a tnsnames.ora:<br /><br /><B>P11</B>=<br />&#x00A0;&#x00A0;(DESCRIPTION =<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ADDRESS_LIST =<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ADDRESS = (PROTOCOL = TCP) (HOST = oracx1) (PORT = 1521))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ADDRESS = (PROTOCOL = TCP) (HOST = oracx2) (PORT = 1521))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(CONNECT_DATA =<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (SERVER = DEDICATED)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (SERVICE_NAME = P11.WORLD)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (FAILOVER_MODE=<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(TYPE = SELECT)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(METHOD = BASIC)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; )<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;)<br />&#x00A0;&#x00A0;)<br /><br />The network service name in our example is <B>P11</B>. This name should not be confused with the database name. You could also have entered informix or db2 as a network service name.<br /><br />The mapping or addressing function is performed using the network service name (P11). In the above example, this means the following: On the ORACX1 host, a listener process must listen for the TCP log on port 1521 and accept incoming connection requests. In the example provided, an address list (ADDRESS_LIST) is supplied to the network service name. This list retains its meaning when the failover mode is activated by means of CONNECT_DATA.<br />If the first address fails, the list is processed sequentially. In the RAC case, this means that the SAP client automatically arrives at the next instance.<br />The Transparent Application Failover (TAF) uses this list to switch to the next RAC node automatically and transparently in the event of an error with a select statement (the instance change is transparent for the client, its select statement is returned without errors).<br /><br />As of Oracle 8.1, all instances are dynamically registered with their listener. The instances use their global database names by default for registration. This default setting can be overridden. If the Init.ora parameter service_names is set for an instance, the instance logs on to the listener with its services. A client must request one of the registered service names for the connection. The client resolves this service name with the tnsnames.ora. In the above case, SERVICE_NAME = P11.WORLD.<br /><br />The listener output shows the following listing. Refer to the listing for information on the available services and their statuses.<br /><br />Connecting to (DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=)(PORT=1521)))<br />STATUS of the LISTENER<br />------------------------<br />Alias&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LISTENER<br />Version&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TNSLSNR for Linux: Version *******.0 - Production<br />Start Date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;13-APR-2005 16:53:55<br />Uptime&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0 days 0 hr. 8 min. 44 sec<br />Trace Level&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; off<br />Security&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OFF<br />SNMP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;OFF<br />Listener Parameter File&#x00A0;&#x00A0; /oracle/P11/920_32/network/admin/listener.ora<br />Listener Log File&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /oracle/P11/920_32/network/log/listener.log<br />Listening Endpoints Summary...<br />&#x00A0;&#x00A0;(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=oracx1)(PORT=1521)))<br />Services Summary...<br />Service \"<B>P11.WORLD</B>\" has 1 instance(s).<br />&#x00A0;&#x00A0;Instance \"P11001\", status READY, has 1 handler(s) for this service...<br />Service \"P11001\" has 1 instance(s).<br />&#x00A0;&#x00A0;Instance \"P11001\", status UNKNOWN, has 1 handler(s) for this service...<br />Service \"P11001.WORLD\" has 1 instance(s).<br />&#x00A0;&#x00A0;Instance \"P11001\", status READY, has 1 handler(s) for this service...<br />Service \"P11002\" has 1 instance(s).<br />&#x00A0;&#x00A0;Instance \"P11002\", status UNKNOWN, has 1 handler(s) for this service...<br />The command completed successfully<br />oracx1:orap11 27&gt;<br /><br />Excerpt from the related listener.ora:<br /><br />LISTENER =<br />&#x00A0;&#x00A0;(DESCRIPTION =<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ADDRESS_LIST =<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ADDRESS = (PROTOCOL = TCP)(HOST =)(PORT = 1521))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;)<br />&#x00A0;&#x00A0;)<br /><br />STARTUP_WAIT_TIME_LISTENER = 0<br />CONNECT_TIMEOUT_LISTENER = 10<br />TRACE_LEVEL_LISTENER = OFF<br />SID_LIST_LISTENER =<br />&#x00A0;&#x00A0;(SID_LIST =<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(SID_DESC =<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(SID_NAME = P11001)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ORACLE_HOME = /oracle/P11/920_32)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(SID_DESC =<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(SID_NAME = P11002)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ORACLE_HOME = /oracle/P11/920_32)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;)<br />&#x00A0;&#x00A0;)<br /><br /><br />The SAP client uses its network service name (dbs_ora_tnsname) and the SERVICE_NAME from its tnsnames.ora (TNS_ADMIN) to resolve the access in the RAC environment on the database instances. This access is independent of the instance name. You do not have to change the environment variables of the &lt;SAPSID&gt;adm user. Use the ADDRESS_LIST of the tnsnames.ora.to assign the SAP instance to the database instance.<br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA (Oracle)"}, {"Key": "Responsible                                                                                         ", "Value": "C2857311"}, {"Key": "Processor                                                                                           ", "Value": "D000674"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000830982/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000830982/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000830982/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000830982/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000830982/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000830982/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000830982/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000830982/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000830982/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "905359", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using BR*Tools for Oracle RAC databases", "RefUrl": "/notes/905359"}, {"RefNumber": "527843", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle RAC support in the SAP environment", "RefUrl": "/notes/527843"}, {"RefNumber": "1171095", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "Information about Oracle RAC 10.2", "RefUrl": "/notes/1171095"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "527843", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle RAC support in the SAP environment", "RefUrl": "/notes/527843 "}, {"RefNumber": "905359", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using BR*Tools for Oracle RAC databases", "RefUrl": "/notes/905359 "}, {"RefNumber": "1171095", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "Information about Oracle RAC 10.2", "RefUrl": "/notes/1171095 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}