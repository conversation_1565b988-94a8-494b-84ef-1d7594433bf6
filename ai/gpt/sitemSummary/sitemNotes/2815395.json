{"Request": {"Number": "2815395", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 406, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001366122019"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002815395?language=E&token=9370522C1EE875D53E447625B4442633"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002815395", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002815395/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2815395"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.09.2021"}, "SAPComponentKey": {"_label": "Component", "value": "IS-MP-NF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Enahancements Non-Ferrous Metal"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Mill Products", "value": "IS-MP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-MP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enahancements Non-Ferrous Metal", "value": "IS-MP-NF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-MP-NF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2815395 - Enhancement of transaction BP with Non Ferrous Metals (NFM) \"exchange key\" field"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>NFM field \"Exchange Key\" is not displayed in transaction BP. Sales Order and Purchase Order NFM items do not get the default value by customer/ vendor..</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Maintain Exchange Keys, KNVV-J_1NBOESL (Customer master), LFM1-J_1NBOESL (Vendor master)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>With S/4HANA, the customer and vendor master transactions are disabled. The Business Partner transaction is used as the primary data entry point. The Non-Ferrous Metal solution has a field &#8220;Exchange Key&#8221; which could be maintained in customer/vendor transactions (SAP ERP). Since Exchange Key is not present in the BP transaction and Business Partner is the primary source in S/4<del datetime=\"2019-07-26T16:19\"> </del>Hana, this field cannot be maintained. As a result, the end-user must maintain the exchange key manually in creation of each sales order/purchase order</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>You can enhance the transaction BP. Follow the steps mentioned in the cookbook of SAP Note 2309153 (Customer enhancements in CVI). It explains how to make customer enhancements to the customer/vendor integration to integrate additional customer/vendor fields in the business partner and to use CVI synchronization to update them in the customer/vendor. There are various scenarios mentioned in the cookbook. The one relevant to Exchange Key functionality is Scenario B.</p>\r\n<p>By following the steps mentioned in the cookbook, the Exchange Key can be maintained in BP transactions and passed on to the customer/vendor master.</p>\r\n<p>Instructions:</p>\r\n<ol>\r\n<li>Add the field to customer/vendor role in BP using BDT.</li>\r\n<li>Saving to database using XO and CVI.</li>\r\n</ol>\r\n<p><strong>Step 1: Add the field to customer/vendor role in BP using BDT</strong></p>\r\n<p>Add NFM exchange fields:</p>\r\n<p>Customer: KNVV-J_1NBOESL</p>\r\n<p>Vendor: LMF1-J_1NBOESL</p>\r\n<p>Follow the steps in the note 2309153 (BDT assignments) of creating a view and assigning it to proper section.</p>\r\n<p><strong>Step 2: Saving to database&#160;</strong></p>\r\n<ul>\r\n<li>Create validation object classes for customer and vendor as mentioned in the chapter \"Create Validation Object (VO) classes\" of the cookbook.</li>\r\n<li>Assign the created validation objects to the respective segmented object as mentioned in the chapter \"XO Customizing\" of the cookbook. E.g. Customer validation object is added under&#160;CUSTOMER_SALES and Vendor validation object under&#160;VENDOR_PURCHASE.</li>\r\n<li>Extend the structure CVIS_EI_EXTERN with the appended fields in the structure DATA and DATAX in the corresponding core table section as mentioned in chapter \"Scenario B &#8211; Integration of core table appends\" of the cookbook.</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "Manasa G B (I501325)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022207)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002815395/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002815395/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002815395/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002815395/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002815395/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002815395/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002815395/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002815395/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002815395/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3104342", "RefComponent": "IS-MP", "RefTitle": "SAP S/4HANA On Premise 2021, 2022 and 2023 release: Mill Products Restriction Note", "RefUrl": "/notes/3104342 "}, {"RefNumber": "2970611", "RefComponent": "IS-MP", "RefTitle": "SAP S/4HANA On Premise 2020 release: Mill Products Restriction Note", "RefUrl": "/notes/2970611 "}, {"RefNumber": "2829102", "RefComponent": "IS-MP", "RefTitle": "SAP S/4HANA On Premise 1909 release: Mill Products Restriction Note", "RefUrl": "/notes/2829102 "}, {"RefNumber": "2270403", "RefComponent": "IS-MP-NF", "RefTitle": "S4TWL - Non Ferrous Metal Processing", "RefUrl": "/notes/2270403 "}, {"RefNumber": "2698659", "RefComponent": "IS-MP", "RefTitle": "S/4HANA for Cable industry - Solution and Implementation facts", "RefUrl": "/notes/2698659 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}