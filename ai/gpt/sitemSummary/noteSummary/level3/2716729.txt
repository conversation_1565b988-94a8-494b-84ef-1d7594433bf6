This SAP Note, 2716729, addresses the configuration of a new communication channel to SAP Parcel Box for SAP Solution Manager systems with different support package levels. The RFC destination "SAP-SUPPORT_PARCELBOX" is required for this communication channel, which is not defined in the system by default.

For environments with SAP Solution Manager 7.2:
- With ST 720 SP08 or SP09, the task list SAP_SUPPORT_HUB_CONFIG should be re-executed to generate the destination automatically after an update from earlier support packages (SP05/SP06/SP07).
- For systems with ST-PI 740, or ST 720 SP07 or lower, the RFC destination can be created manually or by implementing SAP Note 2738426 and executing the task list 'SAP_BASIS_CONFIG_OSS_COMM'.

The manual steps include creating the RFC destination in SM59, configuring "HTTP Connections to External Server", technical settings (including host, port, path prefix), and logon & security (with Basic Authentication using the technical communication user).

Additional linked SAP Notes are:
- 2714210: New communication channel to SAP Backbone for Service Content Update
- 2174416: Creation and activation of users for the Support Hub Communication
- 2616023: ST-PI 2008_1_* SP19, ST-PI 740 SP09: Enhancements in functionality

Lastly, the note lists the relevant keywords such as parcelbox, SAP-SUPPORT_PARCELBOX, SDCCN, and SCU.