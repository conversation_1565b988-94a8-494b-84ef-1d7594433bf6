SAP Note 950136 addresses an issue in SAP BW where there is no available option to determine the status of a request without considering the timeout period. This condition could lead to terminations with error '2' in InfoPackages or InfoPackage groups waiting for request status during timeout scenarios.

The underlying cause of the issue is identified as a program error. To resolve this, the note provides specific instructions for importing Support Packages into the SAP BW system for different BW versions as follows:

- For BW 3.0B: Import Support Package 32 (BW 3.0B Patch32 or SAPKW30B32).
- For BW 3.10 Content: Import Support Package 26 (BW 3.10 Patch 26 or SAPKW31026).
- For BW 3.50: Import Support Package 18 (BW 3.50 Patch 18 or SAPKW35018).
- For BW 7.0: Import Support Package 09 (BW 7.0 Patch 09 or SAPKW70009).

Additionally, the note mentions a new optional import parameter 'I_NO_TIMEOUT' of type RS_BOOL with the default value RS_C_FALSE for the function module RSSM_GET_REQUEST_STATUS. This parameter needs to be added manually if the user does not wish to import the correction provided by the support package.

The note also refers to other related notes, which describe these Support Packages in more detail, with the caveat that these notes might be in a "preliminary version" if accessed before the release of the Support Packages.

To summarize, this SAP Note provides a solution for handling request status during timeouts for various SAP BW versions, detailing the necessary Support Packages to import and the introduction of a new function module parameter to manage timeouts.