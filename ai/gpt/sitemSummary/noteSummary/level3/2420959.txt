SAP Note 2420959 addresses an issue where generating Business Partner (BP) from a customer or vendor master record after turning on Customer-Vendor Integration (CVI) using the Synchronization Cockpit results in BP being created with new address GUIDs (Globally Unique Identifiers). This situation can lead to problems during data exchange with an integrated CRM system.

The issue occurs when an SAP ERP system is connected to a CRM system via Middleware, and there is a need to generate BPs from existing customer or vendor masters as a prerequisite to upgrading to S/4HANA.

The note provides a solution to this problem by suggesting the creation of a new enhancement implementation for the BADI (Business Add-In) `CVI_CUSTOM_MAPPER`. An example implementation named `PI_BP_GET_BP_ADDRGUID_FROM_CRM` is given as a reference. This proposed solution requires performing an RFC (Remote Function Call) into the CRM system for each BP being created in the ERP system. However, the note cautions that this approach may lead to performance issues when dealing with large numbers of customers or vendors. Consequently, this enhancement is presented only as an example and may need to be considered carefully before implementation due to potential performance impacts. 

In summary, SAP Note 2420959 outlines a solution to address the creation of new address GUIDs when synchronizing customer or vendor master data with Business Partners in CRM, while also highlighting the potential performance trade-offs of the proposed workaround.