{"Request": {"Number": "2027972", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 441, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017888752017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002027972?language=E&token=CEA7DC357077F29D7767D5F79F4C1738"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002027972", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2027972"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released Internally"}, "ReleasedOn": {"_label": "Released On", "value": "09.12.2014"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-FI-CA"}, "SAPComponentKeyText": {"_label": "Component", "value": "obsolete: Please use Component FI-CA instead"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Finance", "value": "XX-PROJ-FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "obsolete: Please use Component FI-CA instead", "value": "XX-PROJ-FI-CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-FI-CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2027972 - FI-CA EhP7, SP05 and SAP Simple Finance add-on for SAP Business Suite powered by SAP HANA 1.0 in one system"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to release your industry solution which is based on FI-CA. Precondition for this release is that FI-CA is released since FI-CA is part of your solution. The following applications do use FI-CA:</p>\r\n<p>- FI-CAX</p>\r\n<p>- IS-T (Telecommunications)</p>\r\n<p>- IS-U/CCS (Utilities, Waste)</p>\r\n<p>- FS-CD (Insurance)</p>\r\n<p>- PSCD (Public Sector)</p>\r\n<p>- IS-M (Media)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>\r\n<li><strong>FI-CA EhP7, SP05&#160;can run generally with SAP Simple Finance add-on in one system</strong></li>\r\n<li>FI-CA is tested with&#160;SAP Simple Finance add-on&#160;for the industry solution FI-CAX</li>\r\n<li>Since there is nearly no special development in FI-CA for the communication with&#160;SAP Simple Finance add-on for all the various industry solutions based on FI-CA, the statement 1. can be extended for industry solutions based on FI-CA in case they have no own direct interfaces to FI and only do use FI-CA.</li>\r\n<li><strong>FI-CA only works together with an industry</strong> (FI-CAX is technically an industry) each industry itself needs to give a release statement for SAP Simple Finance add-on.&#160; We can only give this release statement for FI-CA and only&#160;for those countries where there is no special development done by Globalisation Services. For those countries where there is special development, Globalisation Services has to give a statement on top of our statement. &#160;</li>\r\n<li>There are currently the following industry solution dependant restrictions: There are two industry solutions based on FI-CA which do use the special functionality Funds Management: Utilities and Public Sector, the integration of FI-CA and Funds Management is tested in a separate small validation test. The test showed that the&#160;integration for Funds Management in FI-CA generally&#160;runs together with&#160;SAP Simple Finance add-on 1.0 in one system. There is only one restriction left: it is not possible to do a post activation of Funds Management in an already productive system. This means transaction FPFMDY cannot be used for&#160;SAP Simple Finance add-on installations.</li>\r\n<li><strong>There is the general restriction for every solution using FI-CA that currently it is not possible to use cash management in installations with FI-CA and&#160;SAP Simple Finance add-on&#160;in one system.</strong></li>\r\n</ol>\r\n<p>This note gives only the status about the compatibility of FI-CA EhP7, SP05 and&#160;SAP Simple Finance add-on 1.0. It is not a release statement of any of the above mentioned (see symptom) industry solutions based on FI-CA.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021150)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D040496)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002027972/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002027972/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002027972/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002027972/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002027972/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002027972/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002027972/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002027972/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002027972/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2053487", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information for scenarios from Public Service Industries together with SAP Accounting powered by SAP HANA 1.0", "RefUrl": "/notes/2053487"}, {"RefNumber": "1968568", "RefComponent": "XX-SER-REL", "RefTitle": "Release Scope Information: SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/1968568"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2164777", "RefComponent": "FI-CAX", "RefTitle": "Release Information for component FI-CAX of SAP together with SAP Simple Finance add-on 2.0", "RefUrl": "/notes/2164777 "}, {"RefNumber": "2140797", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "FI-CA EhP7, SP08 and SAP Simple Finance 1503 (sFIN OP 1503) in one system", "RefUrl": "/notes/2140797 "}, {"RefNumber": "2106073", "RefComponent": "FI-CAX", "RefTitle": "Release Information for using the Industry Solution for FI-CAX with the Financials add-on for SAP Business Suite powered by SAP HANA1.0 SP01", "RefUrl": "/notes/2106073 "}, {"RefNumber": "2053487", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information for scenarios from Public Service Industries together with SAP Accounting powered by SAP HANA 1.0", "RefUrl": "/notes/2053487 "}, {"RefNumber": "2051279", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information for SAP for Retail and Fashion Management together with Financials add-on for SAP Business Suite powered by SAP HANA 1.0", "RefUrl": "/notes/2051279 "}, {"RefNumber": "2049162", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Media and Financials add-on for SAP Business Suite powered by SAP HANA 1.0", "RefUrl": "/notes/2049162 "}, {"RefNumber": "2025786", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information zur Nutzung von Versicherungskomponenten zusammen mit Financials add-on for SAP Business Suite powered by SAP HANA1.0 SP01", "RefUrl": "/notes/2025786 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "FI-CA", "From": "617", "To": "617", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}