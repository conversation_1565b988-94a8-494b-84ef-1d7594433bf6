SAP Note 612454 provides information regarding the status and lifetime of SAP GUI Scripting. The note addresses inquiries about the current state and expected future support for SAP GUI Scripting.

Symptom:
Users are requesting information about the status and longevity of SAP GUI Scripting.

Other Terms:
The note references terms including SAP GUI, Wingui (Windows GUI), Javagui (Java GUI), and Scripting.

Reason and Prerequisites:
This note is relevant for users who are utilizing either SAP GUI for Windows 6.20 or SAP GUI for Java 6.20.

Solution:
The note states that SAP GUI Scripting is used to automate user interactions with SAP applications based on Dynpro/SAP GUI technology. The note assures that SAP GUI Scripting is and will continue to be supported in both current and upcoming versions of SAP GUI for Windows and SAP GUI for Java.

Furthermore, the note points users seeking details about the support of individual versions of SAP GUI to another note, SAP Note 147519. This referenced note is likely to contain specific information about version compatibility and support for SAP GUI Scripting.