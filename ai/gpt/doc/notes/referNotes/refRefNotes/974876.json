{"Request": {"Number": "974876", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 372, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005761082017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000974876?language=E&token=D9276F49ECE13F3B6D046D3A21C768FD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000974876", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000974876/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "974876"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 58}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.11.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "974876 - Oracle Transparent Data Encryption (TDE)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>Using Oracle Transparent Data Encryption (TDE) with SAP </strong></p>\r\n<p>This SAP Note describes the encrypted storage of data from the SAP application in the Oracle database with Oracle Transparent Data Encryption (TDE).</p>\r\n<p>This SAP Note is valid for Oracle database Version 10g Release 2 (10.2) and 11g Release 2 (11.2). For Oracle Database 12c Release 1 or higher, see <a target=\"_blank\" href=\"/notes/2591575\">SAP Note 2591575</a>.</p>\r\n<p><strong>Change</strong></p>\r\n<ul>\r\n<li>November 3, 2023</li>\r\n<ul>\r\n<li>Document \"Oracle Advanced Security Transparent Data Encryption Best Practices\" (twp-transparent-data-encryption-bes-130696.pdf) attached (original link is no longer valid)</li>\r\n</ul>\r\n<li>November 27, 2019</li>\r\n<ul>\r\n<li>Syntax error in SQL command corrected: 'alter system set encryption&#x00A0;encryption' -&gt; 'alter system set encryption'</li>\r\n</ul>\r\n<li>June 15, 2018</li>\r\n<ul>\r\n<li>New SAP Note <a target=\"_blank\" href=\"/notes/2591575\">2591575</a> valid as of Oracle Database 12c Release 1</li>\r\n</ul>\r\n<li>February 20, 2018</li>\r\n<ul>\r\n<li>SAP Note completely revised</li>\r\n<li>Recommendations for TDE tablespace encryption as of 11.2 added</li>\r\n<li>TDE column encryption should no longer be used as of 11.2</li>\r\n</ul>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;BR0202I Saving init_ora+ora_wallet</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">BR1733I Database wallet will be saved:</span></p>\r\n<p>&#x00A0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note is valid for Oracle Database Version 10g Release 2 (10.2) and 11g Release 2 (11.2).</p>\r\n<p><strong>Protect sensitive data in the database through encryption with TDE</strong></p>\r\n<p>Oracle TDE protects sensitive data in the database against unauthorized reading if the normal database access protection mechanisms are bypassed and external operating system tools are used physically and directly to access the contents of the database files (for example, with a hex editor). Encryption with TDE protects sensitive data against unauthorized reading in the following cases:</p>\r\n<ul>\r\n<li>in database files (user tablespaces, undo tablespaces, TEMP tablespaces)</li>\r\n<li>in redo log files</li>\r\n<li>in export dump files (data pump)</li>\r\n<li>in database backups</li>\r\n<li>from insiders with file system authorizations</li>\r\n<li>when backup media with database backups are lost or stolen</li>\r\n<li>when hard disks or other storage media that contain database files are (improperly) disposed of</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">IMPORTANT:</span> TDE does not offer protection against unauthorized access to data in the database if access takes place via the SQL interface (for example, by highly-privileged database users or database administrators / DBAs). The \"Oracle Database Vault\" is the right solution to protect against this type of attack.</p>\r\n<p><strong>Responsibility for data protection</strong></p>\r\n<p>The responsibility for encrypting sensitive data in the database can be separated from the responsibility for normal database administration and assigned to a separate database security administrator.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong><a target=\"_blank\" name=\"A1_EINFUEHRUNG\"></a>&#xFEFF;1. Introduction</strong></p>\r\n<p><strong><a target=\"_blank\" name=\"A1.1_TDE\"></a>&#xFEFF;1.1 Oracle Transparent Data Encryption (TDE)</strong></p>\r\n<p>'Transparent Data Encryption' (TDE) is part of the Advanced Security Option of the Oracle Enterprise Edition. With TDE, sensitive application data can be saved in an encrypted form in the database. TDE is available in two forms:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Characteristic</span></td>\r\n<td><span style=\"text-decoration: underline;\">TDE Column Encryption</span></td>\r\n<td><span style=\"text-decoration: underline;\">TDE Tablespace Encryption</span></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Granularity</p>\r\n</td>\r\n<td>\r\n<p>Encryption of individual table columns</p>\r\n</td>\r\n<td>Encryption of entire tablespaces</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Availability for SAP</p>\r\n</td>\r\n<td>\r\n<p>Oracle Release 10.2 and higher</p>\r\n</td>\r\n<td>Oracle Release 11.2 and higher</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">What does \"transparent\" mean from the perspective of the SAP application?</span></p>\r\n<p>The use of TDE is completely transparent for the SAP application. Therefore:</p>\r\n<ul>\r\n<li>You do not have to change the SAP application. Encryption and decryption are not carried out by the application.</li>\r\n<li>The database encrypts and decrypts the data automatically.</li>\r\n<li>The administration of the key is performed automatically, internally in the database. The database administrator only needs a few additional administrative commands.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Other encryption procedures</span></p>\r\n<p>Other procedures from Oracle, such as the use of procedures of the PL/SQL package DBMS_CRYPTO or DBMS_OBFUSCATION_TOOLKIT, are not transparent for the SAP application and are not supported by SAP.</p>\r\n<p><span style=\"text-decoration: underline;\">General Recommendation</span></p>\r\n<p>For SAP, the use of TDE column encryption is no longer recommended, due to technical restrictions, denigrated performance, memory overhead, and last but not least, the increased administrative effort, particularly during the creation of candidates for the columns to encrypt. Instead, you should use TDE tablespace encryption only as of Oracle Release 11.2 TDE.</p>\r\n<ul>\r\n<li>Existing SAP installations with TDE column encryption should be converted to TDE tablespace encryption.</li>\r\n<li>New SAP installations should use TDE tablespace encryption only.</li>\r\n<li>Support for TDE tablespace encryption is planned in transaction SWPM.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">What does TDE not protect against?</span></p>\r\n<p>TDE encrypts sensitive data in database files. As a result, this sensitive data is protected against unauthorized access when it is accessed directly from outside the database, for example, when displaying data blocks of database files with a hex editor.</p>\r\n<p>To access encrypted data in the database, the TDE Encryption Wallet must be opened. When it is open, the encrypted data is decyrpted automatically. Due to this automatic decryption, TDE is transparent for applications such as the SAP application. For the same reason, TDE does not provide protection against unauthorized access to sensitive application data when database users with the appropriate privileges gain access through the SQL interface.</p>\r\n<p>Therefore, TDE does not provide any protection against unauthorized reading of sensitive application data by database administrators, for example, or other database users who have the corresponding privileges. To cover this case, as well, and protect sensitive application data, you can use Oracle Database Vault. <br /><br /><span style=\"text-decoration: underline;\">TDE keys and Encryption Wallets</span><br /><br />Table keys (TDE column encryption) are saved in the Oracle dictionary. Tablespace keys (TDE tablespace encryption) are saved in the tablespace header and in the headers of the related data files for performance optimization. Each of the TDE table keys and TDE tablespace keys are encrypted with the master key. The master key is saved outside the database in an external security module.</p>\r\n<p><span style=\"text-decoration: underline;\">External security modules</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Name</span></td>\r\n<td><span style=\"text-decoration: underline;\">Type&#x00A0;</span></td>\r\n<td><span style=\"text-decoration: underline;\">Description</span></td>\r\n</tr>\r\n<tr>\r\n<td>TDE Encryption Wallet</td>\r\n<td>Software</td>\r\n<td>Oracle</td>\r\n</tr>\r\n<tr>\r\n<td>Hardware Security Module (HSM)</td>\r\n<td>Hardware</td>\r\n<td>3rd party</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>As of Release 11.2, the same master key is used to encrypt the table keys and the tablespace keys.<br /><br />Depending on the security policy that is valid in each case, a new master key must be generated at regular intervals. This is then saved in addition to the previous master keys. As a result, the Encryption Wallet saves the history of all master keys of a database that were ever generated.<br /><br /><span style=\"text-decoration: underline;\">Encryption Wallet password</span><br /><br />The Encryption Wallet is protected by the \"Encryption Wallet Password\". The Encryption Wallet must be opened by the security administrator with the correct password before encrypted data can be accessed. <br /><br />The \"Encryption Wallet Password\" is independent of other Oracle system passwords, which is useful and necessary for separating the areas of responsibility of database administrators and security administrators. The Encryption Wallet password can be defined independently of passwords of the database users SYS and SYSTEM or of SYSDBA users.<br /><br />In addition to the Encryption Wallet, an \"Auto-open-Wallet\" (previously named \"Auto-login-Wallet\") can be set up that can be opened automatically with no password.</p>\r\n<p><span style=\"text-decoration: underline;\">Encryption and compression</span></p>\r\n<p>When you have encrypted tablespaces, the system encrypts data blocks when writing from the buffer cache; they are decrypted when loaded to the buffer cache. As a result, the data in the SGA buffer cache is unencrypted, and can therefore be processed with or without TDE. TDE Tablespace Encryption can therefore be combined with the Oracle compression techniques without problems: compression of tables (ACO, see SAP Note 1436352), compression of secure files (ACO, see SAP Note 1426979), compression of index keys (see SAP Note 1109743).</p>\r\n\r\n<p><strong><a target=\"_blank\" name=\"A1.2_NEUE_FUNKTIONALITAET\"></a>1.2 New TDE Functions in Release 11.2</strong></p>\r\n<ul>\r\n<li>Transparent encryption of data at the tablespace level (TDE Tablespace Encryption, Encrypted Tablespaces)</li>\r\n<li>Encrypting LOB data type (SECUREFILES) In an encrypted tablespace, SECUREFILE-LOBs do not have to be encrypted again.</li>\r\n<li>Changing the Encryption Wallet password using the orapki command line</li>\r\n<li>Support for the hardward security module (HSM)</li>\r\n<li>Released for the use with SAP as of Release ******** (see prerequisites)</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"A1.3_NEUE_FUNKTIONALITAET_102\"></a>1.3 New TDE Functions in Release 10.2</strong></p>\r\n<ul>\r\n<li>Transparent encrypting of data based on columns (TDE column encryption)</li>\r\n<li>Released for the use with SAP as of Release 10.2.0.4 (see prerequisites)</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"A1.4_MOS_HINWEISE\"></a>&#xFEFF;1.4 References - My Oracle Support (MOS) / Oracle Technology Network (OTN)</strong></p>\r\n<ul>\r\n<li>Master Note For Transparent Data Encryption (TDE) [ID 1228046.1]</li>\r\n<li>TDE Setup and FAQ [ID 1251597.1]</li>\r\n<li><a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/focus-areas/security/twp-transparent-data-encryption-bes-130696.pdf\">Oracle Advanced Security Transparent Data Encryption Best Practices</a><br />(<a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/focus-areas/security/twp-transparent-data-encryption-bes-130696.pdf\">http://www.oracle.com/technetwork/database/focus-areas/security/twp-transparent-data-encryption-bes-130696.pdf</a>)<br /><br />Caution: Sorry, this link is no longer valid. Therefore, the document \"Oracle Advanced Security Transparent Data Encryption Best Practices\" is now attached directly to this SAP Note in the version from July 2012 (file twp-transparent-data-encryption-bes-130696.pdf).<br /><br /></li>\r\n<li><a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/focus-areas/security/tde-faq-093689.html\">Transparent Data Encryption, Frequently Asked Questions, TDE FAQ</a>&#x00A0;<br />(<a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/focus-areas/security/tde-faq-093689.html\">http://www.oracle.com/technetwork/database/focus-areas/security/tde-faq-093689.html</a>)</li>\r\n</ul>\r\n\r\n<p>&#x00A0;</p>\r\n<p><strong><a target=\"_blank\" name=\"A2_VORAUSETZUNGEN\"></a>&#xFEFF;2.&#x00A0;&#x00A0;Prerequisites for using TDE with SAP</strong></p>\r\n<p>To use TDE, you require a license for the Advanced Security Option (ASO). For more information, see SAP Note <a target=\"_blank\" href=\"/notes/740897\">740897</a>.</p>\r\n<p>With TDE tablespace encryption, SAP application data of both the SAP ABAP Stack and the SAP JAVA Stack can be protected if the following prerequisites are met:</p>\r\n<p><strong><a target=\"_blank\" name=\"A2.2_VORRAUSETZUNGEN_TSE\"></a>2.2 Prerequisites for Using TDE Tablespace Encryption</strong><br /><br /></p>\r\n<ul>\r\n<li>Oracle license for Advanced Security Option, see SAP Note <a target=\"_blank\" href=\"/notes/740897\">740897</a></li>\r\n<li>Oracle Release 11.2 or higher</li>\r\n<li>SAP BR*Tools 7.20 or higher</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"A2.3_VORAUSSETZUNGEN_COLUM_ENCRYPTION\"></a>2.3 Prerequisites for Using TDE Column Encryption</strong></p>\r\n<ul>\r\n<li>Oracle license for Advanced Security Option, see SAP Note <a target=\"_blank\" href=\"/notes/740897\">740897</a></li>\r\n<li>Oracle Release 10.2.0.4 or higher</li>\r\n<li>SAP BR*Tools 7.00 patch level 24 or higher</li>\r\n<li>SAP kernel Release 6.40 or higher</li>\r\n<li>SAP Basis Support Package level: Basis Release 6.40 SP22 or higher, Basis Release 7.00 SP16 or higher.</li>\r\n</ul>\r\n<p><strong>Support for the SAP ABAP stack</strong><br /><br />TDE column encryption for tables of the ABAP stack is also supported for SAP release upgrades: Table conversions of tables of the ABAP stack with encrypted columns are supported for structural changes within a Support Package or an SAP release upgrade. If the SAP system is not yet at the specified Support Package level, you must implement the corrections attached to this note using transaction SNOTE.<br /><br /><strong>Support for the SAP Java stack</strong><br /><br />TDE column encryption for tables of the Java stack is also supported for SAP release upgrades: Table conversions of tables of the Java stack with encrypted columns are not supported if there are structural changes as a result of a Support Package or an SAP release upgrade, and therefore errors may occur during the SAP release upgrade.<br /><br />Prior to an SAP release upgrade, therefore, we recommend that you decrypt the tables of the Java stack that are encrypted with TDE column encryption and then perform the SAP release upgrade with the decrypted tables, since errors may occur otherwise during the SAP upgrade with TDE tables that were changed structurally.<br /><br />Alternatively, tables of the Java stack can be encrypted with TDE tablespace encryption as of Release 11.2. With TDE tablespace encryption, the previous decryption for SAP release upgrades no longer applies.</p>\r\n<p><strong><a target=\"_blank\" name=\"A3.1_KONFIGURATION_TDE_MIT_WALLET\"></a>3.1 Configuration of TDE with Encryption Wallet</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Parameter ENCRYPTION_WALLET_LOCATION</span><br /><br />The storage location for the TDE Encryption Wallet (ewallet.p12) or the Auto-Open-Wallet (cwallet.sso) is configured in the server-side configuration file sqlnet.ora by the parameter ENCRYPTION_WALLET_LOCATION. The related syntax reads:<br /><br />################################################<br /># Encryption Wallet Location<br />################################################<br />ENCRYPTION_WALLET_LOCATION =<br />(SOURCE = (METHOD = FILE)&#x00A0;(METHOD_DATA =&#x00A0;(DIRECTORY = &lt;wallet_location&gt; )))<br /><br />The specified path, &lt;wallet_location&gt;, must exist and the Oracle database instance must have authorization to write to this path. The file SQLNET.ORA is located under &lt;ORACLE_HOME&gt;/network/admin.</p>\r\n<p><strong>Encryption Wallet Location (Oracle Standard)</strong><br /><br />Without the sqlnet.ora entry ENCRYPTION_WALLET_LOCATION, Oracle saves the TDE Encryption Wallet under the following path by default:</p>\r\n<ul>\r\n<li>$ORACLE_BASE/admin/&lt;DBNAME&gt;/wallet (Unix)</li>\r\n<li>%ORACLE_BASE%\\admin\\&lt;DBNAME&gt;/wallet (Windows</li>\r\n</ul>\r\n<p><strong>Encryption Wallet Location (SAP BR*Tools Standard)</strong><br /><br />For the TDE Encryption Wallet to be saved automatically by the SAP BR*Tools, the parameter ENCRYPTION_WALLET_LOCATION must be configured as follows:</p>\r\n<ul>\r\n<li>$ORACLE_HOME/dbs (Unix / Linux)<br />%ORACLE_HOME%\\database (Windows)<br /><br />This is the default for SAP installations if the Oracle database software is installed with the classic user 'ora&lt;dbsid&gt;:dba' (single instance).</li>\r\n<li>$SAPDATA_HOME/orawallet&#x00A0;&#x00A0;(Unix / Linux)<br />%SAPDATA_HOME%\\orawallet (Windows)<br /><br />This is the default for SAP installations if the Oracle database software is installed with the user 'oracle:oinstall'. This default is supported as of SAP BR*Tools 7.20 Patch Level 32 and 7.40 Patch Level 2.<br />The directory $SAPDATA_HOME/orawallet is recognized and supported by the SAP BR*Tools only in connection with the directory $SAPDATA_HOME/sapprof.</li>\r\n</ul>\r\n<p><strong>Configuration of ENCRYPTION_WALLET_LOCATION with environment variables</strong><br /><br />The storage location &lt;wallet_location&gt; for the TDE Encryption Wallet can either be specified as a specific path without a variable (example 1) or as a generic path using environment variables (example 2).</p>\r\n<ul>\r\n<li>Example 1 for a specific path (= path without a variable) (Unix):<br />ENCRYPTION_WALLET_LOCATION =<br />(SOURCE = (METHOD = FILE) (METHOD_DATA =&#x00A0;(DIRECTORY = /oracle/C11/112_64/dbs )))<br /><br />The path specification without a variable is suitable only for dedicated Oracle Homes that are used only by one database.</li>\r\n<li>Example 2 for a generic path (= path with a variable) (Unix):<br />ENCRYPTION_WALLET_LOCATION =<br />(SOURCE = (METHOD = FILE) (METHOD_DATA = (DIRECTORY = $SAPDATA_HOME/orawallet )))<br /><br />The path specification with a variable in the path must be used if multiple databases jointly use an Oracle Home with TDE (Shared Oracle Home). In this way, each database saves its own Encryption Wallet in another location.</li>\r\n</ul>\r\n<p><strong>Configuration of ENCRYPTION_WALLET_LOCATION with SAPDATA_HOME environment variable</strong><br /><br />You must ensure that the environment variable SAPDATA_HOME is defined at the start of the database instance so that the path '$SAPDATA_HOME/orawallet' can be interpreted correctly by the Oracle instance.</p>\r\n<ul>\r\n<li>For installations with grid infrastructure (RAC, ASM), SAPDATA_HOME is saved via 'srvctl' in OCR (in the 'oracle' environment, obtained for the respective database). The database is restarted after the change.<br />Example for database C11:<br />OS&gt; srvctl setenv database -d C11 -T \"SAPDATA_HOME=/oracle/C11\"<br />OS&gt; srvctl getenv database -d C11<br /><br />OS&gt; srvctl stop&#x00A0;&#x00A0; database -d C11<br />OS&gt; srvctl start&#x00A0;&#x00A0;database -d C11</li>\r\n</ul>\r\n<ul>\r\n<li>For installations without grid infrastructure, SAPDATA_HOME must be defined rigidly in the environment of the Oracle user.<br />Example for database C11:<br />C shell: setenv SAPDATA_HOME /oracle/C11<br />Bourne shell: export SAPDATA_HOME=/oracle/C11</li>\r\n</ul>\r\n<p><strong>Recommendations</strong></p>\r\n<ul>\r\n<li>In principle, the current TDE Encryption Wallet ewallet.p12 must also be saved automatically by the SAP BR*Tools.<br />To ensure this, the storage location must be configured for the TDE Wallet in accordance with the above specifications.</li>\r\n<li>For installations on Unix/Linux with the user 'oracle:oinstall', the SAP BR*Tools are installed and executed under the user &lt;sapsid&gt;adm.&#x00A0;In this case, you must ensure that the same entry is available for ENCRYPTION_WALLET_LOCATION both in the server-side sqlnet.ora in the environment of the user 'oracle' and in the client-side sqlnet.ora in the environment of the user &lt;sapsid&gt;adm. Only then can you ensure consistent results of the wallet administration via BRSPACE from the environment of &lt;sapsid&gt;adm or via SQL*Plus from the environment of the 'oracle' or 'ora&lt;dbsid&gt;' user. If the server and the client have different entries for ENCRYPTION_WALLET_LOCATION, inconsistent behavior occurs in wallet administration (for example, display of incorrect wallet statuses).</li>\r\n<li>The directory &lt;SAPDATA_HOME&gt;/orawallet may be created only if the TDE Encryption Wallet is also located there.</li>\r\n<li>For an 11gR2 RAC installation, the Encryption Wallet should be in a shared file system (for example, in ACFS). In this case, there are &lt;SAPDATA_HOME&gt;/orawallet symbolic links from each RAC node to the shared directory.</li>\r\n<li>If multiple databases are encrypted with TDE and jointly use an Oracle Home (Shared Oracle Home), the generic entry '$SAPDATA_HOME/orawallet' is configured as a storage location for the TDE Encryption Wallet. In this way, each database saves its wallet in its dedicted storage location.<br /><br />For example:<br />SAPDATA_HOME=/oracle/C11 --&gt; /oracle/C11/orawallet<br />SAPDATA_HOME=/oracle/C12 --&gt; /oracle/C12/orawallet<br />SAPDATA_HOME=/oracle/C13 --&gt; /oracle/C13/orawallet</li>\r\n</ul>\r\n<p><strong>Procedure</strong></p>\r\n<ol>\r\n<li>Create a backup of sqlnet.ora.</li>\r\n<li>Supplement ENCRYPTION_WALLET_LOCATION in sqlnet.ora<br />################################################<br /># Encryption Wallet Location (SAP note 974876) #<br />################################################<br />ENCRYPTION_WALLET_LOCATION = (SOURCE =&#x00A0;(METHOD = FILE) (METHOD_DATA = (DIRECTORY = &lt;wallet_location&gt; )))</li>\r\n<li>Make sure that &lt;wallet_location&gt; exists and that the user under which the Oracle instance is running has write authorization for it.</li>\r\n<li>If &lt;wallet_location&gt; is defined with a variable, this must be defined and known in the environment of the database instance.<br />Example for &lt;DBNAME&gt; with grid infrastucture:<br />OS&gt; srvctl setenv database -d &lt;DBNAME&gt; -T \"SAPDATA_HOME=/oracle/&lt;DBNAME&gt;\"<br />OS&gt; srvctl getenv database -d &lt;DBNAME&gt;</li>\r\n<li>Restart the database.</li>\r\n<li>Check whether the specified &lt;wallet_location&gt; has been recognized correctly.<br />With BRSPACE:<br />OS&gt; brspace -u &lt;user&gt;/&lt;pwd&gt; -f mdencr -a show<br /><br />With SQL*Plus (single instance):<br />SQL&gt; SELECT WRL_PARAMETER, STATUS FROM V$ENCRYPTION_WALLET; <br /><br />Example:<br />WRL_PARAMETER&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;STATUS<br />------------------------ ------<br />$SAPDATA_HOME/orawallet&#x00A0;&#x00A0;OPEN<br /><br />With SQL*Plus (RAC)<br />SQL&gt; SELECT INST_ID, WRL_PARAMETER, STATUS FROM GV$ENCRYPTION_WALLET ORDER BY INST_ID;<br /><br />Example:<br />INST_ID WRL_PARAMETER&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;STATUS<br />------- ------------------------ ------<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 $SAPDATA_HOME/orawallet&#x00A0;&#x00A0;OPEN<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 $SAPDATA_HOME/orawallet&#x00A0;&#x00A0;OPEN</li>\r\n<li>You can now create the TDE Encryption Wallet<br />With BRSPACE:<br />OS&gt; brspace -u &lt;user&gt;/&lt;pwd&gt; -f mdencr -a create</li>\r\nOS&gt; brspace -u &lt;user&gt;/&lt;pwd&gt; -f mdencr -a show</ol>\r\n<p><strong><a target=\"_blank\" name=\"A3.2_KONFIGURATION_HSM\"></a>3.2 Configuration of TDE with HSM</strong></p>\r\n<p>If you use a hardware security module (HSM), install and test the HSM software according to the specifications of the manufacturer. Refer to the information and explanations about HSM in the Oracle documentation.</p>\r\n<p>For more information, see the Oracle white paper \"<a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/focus-areas/security/twp-transparent-data-encryption-bes-130696.pdf\">Oracle Advanced Security Transparent Data Encryption Best Practices</a>\".<br /><br /><span style=\"text-decoration: underline;\">HSM in sqlnet.ora</span><br />In the SQLNET.ORA file, insert the following line:<br />ENCRYPTION_WALLET_LOCATION= (SOURCE = (METHOD = HSM))<br /><br />SQL&gt; SELECT * FROM V$ENCRYPTION_WALLET;<br /><br />For example:<br />WRL_TYPE&#x00A0;&#x00A0;WRL_PARAMETER&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;STATUS<br />--------&#x00A0;&#x00A0;----------------------&#x00A0;&#x00A0;------<br />HSM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CLOSED<br /><br /><span style=\"text-decoration: underline;\">HSM support in SAP BR*Tools (as of BR*Tools Release 7.40 patch level 30)</span></p>\r\n<p>The backup or reloading of the Encryption Wallet with BRBACKUP/BRRESTORE is no longer required. This is part of the area of responsibility of the HSM administration.</p>\r\n<p><strong><a target=\"_blank\" name=\"A3.3_UNTERSTUETZUNG_BRTOOLS\"></a>3.3 Support for TDE in the SAP BR*Tools</strong></p>\r\n<p>SAP BR*Tools have integrated various functions for TDE (see SAP Notes 1279682, 1235952):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>BRSPACE</td>\r\n<td>\r\n<ul>\r\n<li>Administration of the Encryption Wallet: open, close, create, delete, save. Support for regenerating the master key (newkey)</li>\r\n<li>Re-encryption of table columns (rekey)</li>\r\n<li>Displaying the current usage status of TDE in the database</li>\r\n<li>Creating encrypted tablespaces (as of Release ********)</li>\r\n<li>Changing the Encryption Wallet password as of BRSPACE Release 7.20</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>BRARCHIVE<br />BRBACKUP</td>\r\n<td>\r\n<p>BRARCHIVE/BRBACKUP (as of BR*Tools Release 7.00 patch level 24):</p>\r\n<ul>\r\n<li>Automatic backup of the Encryption Wallet ewallet.p12<br /><br /><strong>BR0202I Saving init_ora+ora_wallet</strong><br /><strong>BR0203I to &lt;path&gt; ...</strong><br /><br />Test: Minor r online backup of the control file or the online redo logs:<br />'brbackup -u / -d disk -t online -m 0'<br />'brbackup -u / -d disk -t online -m 0</li>\r\n<li>As of Release 7.20 patch level 20, even in the query mode of BRBACKUP and BRARCHIVE, you can see that the Encryption Wallet is also saved:<br /><br /><strong>BR1733I Database wallet will be saved: /oracle/&lt;SID&gt;/102_64/dbs/ewallet.p12</strong><br /><br />Test: Backup in query mode: 'brbackup -u / -t online -m all -q'</li>\r\n<li>The Auto-Open Wallet cwallet.sso is never saved by the BR*Tools.</li>\r\n<li>The wallet is saved in the directory /oracle/&lt;DBSID&gt;/sapbackup/&lt;DBSID&gt; during a backup to a disk.</li>\r\n<li>Automatic opening of wallet after offline backups: See Note 1235952, point 12.</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>BRRECOVER</td>\r\n<td>\r\n<ul>\r\n<li>Disaster recovery of the Encryption Wallet: See the section 'Disaster recovery of the Encryption Wallet'.</li>\r\n<li>Automatic opening of the Encryption Wallet during recovery if the BRRECOVER option \"-pw|-pass_wallet|-PW|-PASS_WALLET\" is specified</li>\r\n<li>Also see the following SAP Notes: 1279682, 1235952.</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul></ul>\r\n<p>&#x00A0;</p>\r\n<p><strong><a target=\"_blank\" name=\"A3.4_EINSCHRAENKUNGEN_TSE\"></a>3.4 Restrictions of TDE Tablespace Encryption</strong></p>\r\n<ul>\r\n<li>When you use TDE tablespace encryption, you should open and shut down the database only with an open wallet.</li>\r\n<li>An existing tablespace cannot be converted to an encrypted tablespace with ALTER TABLESPACE.</li>\r\n<li>Restrictions regarding transportable tablespace (TTS): See \"Limitations on Transportable Tablespace Use\" in the Oracle documentation. See <a target=\"_blank\" href=\"http://docs.oracle.com/cd/E11882_01/server.112/e25494/tspaces.htm#ADMIN11396\">http://docs.oracle.com/cd/E11882_01/server.112/e25494/tspaces.htm#ADMIN11396</a></li>\r\n<li>If no Auto-Open Wallet exists, you must open the Encryption Wallet in MOUNT status for an instance recovery so that the recovery process can decrypt entcrypted data blocks, redo blocks and undo blocks.<br />sqlplus / as sysoper<br />SQL&gt; STARTUP MOUNT<br />SQL&gt; ALTER SYSTEM SET ENCRYPTION WALLET OPEN IDENTIFIED BY \"&lt;wallet_password&gt;\";<br />SQL&gt; ALTER DATABASE OPEN</li>\r\n<li>Partitioned tables can have partitions in both encrypted and unencrypted tablespaces.</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"A3.5_TRANSPORT_TSE\"></a>3.5 Transport of Encrypted Tablespaces (TDE + TTS)</strong></p>\r\n\r\n<p><br />References:</p>\r\n<ul>\r\n<li>MOS note [432776.1]</li>\r\n<li>Oracle documentation: '<a target=\"_blank\" href=\"https://docs.oracle.com/cd/E11882_01/server.112/e25494/tspaces.htm#ADMIN11396\">Limitations on Transportable Tablespace Use</a>' (<a target=\"_blank\" href=\"https://docs.oracle.com/cd/E11882_01/server.112/e25494/tspaces.htm#ADMIN11396\">https://docs.oracle.com/cd/E11882_01/server.112/e25494/tspaces.htm#ADMIN11396</a>)</li>\r\n</ul>\r\n<p>You can transport encrypted tablespaces to a target database if the endianness is the same. First, the Encryption Wallet must be copied from the source database to the target database (except for HSM). In the target database, you should change the password of the Encryption Wallet.<br /><br />You cannot transport an encrypted tablespace to a target database if there is already another Encryption Wallet. In this case, you must export the data with expdp with a password in an encrypted way and import it again in the target database to an encrypted tablespace.<br /><br />The platform endianness of the source and target database must match.</p>\r\n<p><br /><strong><a target=\"_blank\" name=\"A3.6_SCHUTZ_DES_WALLET\"></a>3.6 Wallet Protection</strong></p>\r\n<p>The following recommendations for wallet protection apply only to Unix/Linux platforms. We recommend that you restrict access authorizations for the wallet in the file system as much as possible. You should also protect the wallet against accidental deletion, provided your file system offers such protection.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Recommendations</span></td>\r\n<td><span style=\"text-decoration: underline;\">Command</span></td>\r\n</tr>\r\n<tr>\r\n<td>Only the Oracle software owner should have access to the wallet.</td>\r\n<td>OS&gt; chmod 600 ewallet.p12</td>\r\n</tr>\r\n<tr>\r\n<td>Protect the wallet against accidental deletion (\"immutable bit\").</td>\r\n<td>As root user: <br />OS&gt; chattr +i ewallet.p12<br />OS&gt; sudo -c \"chattr +i ewallet.p12\"</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>For more information, see the Oracle white paper \"<a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/focus-areas/security/twp-transparent-data-encryption-bes-130696.pdf\">Oracle Advanced Security Transparent Data Encryption Best Practices</a>\".</p>\r\n<p><strong><a target=\"_blank\" name=\"A4_EMPFEHLUNGEN\"></a>&#xFEFF;4. Recommendations</strong></p>\r\n<p><strong><a target=\"_blank\" name=\"A4.1_ALLGEMEIN\"></a>4.1 Recommendations for TDE in General</strong></p>\r\n<ul>\r\n<li>The Oracle White Paper \"Oracle Advanced Security Transparent Data Encryption Best Practices\" provides a very good technical overview of TDE and recommendations for using TDE (see below for the link).</li>\r\n<li>You should choose a very secure password for the TDE Encryption Wallet. On the other hand, the password must also be easy to remember or must be kept in a safe place because you <strong>cannot (!!)</strong> restore a password that is forgotten or lost.</li>\r\n<li>You should save the Encryption Wallet before and after each change. You can no longer access encrypted data without the correct, current Encryption Wallet. <br /><br /><strong>For this reason, we recommend that you use only BRSPACE for wallet administration because backup copies of the wallet are then created automatically if the wallet is changed (ewallet.cpy, ewallet.new, and so on).</strong><br /><br />Changes to the Encryption Wallet include the following:</li>\r\n<ul>\r\n<li>A new TDE master key was generated.</li>\r\n</ul>\r\n<ul>\r\n<li>The password of the Encryption Wallet &lt;wallet_password&gt; was changed.<br /></li>\r\n</ul>\r\n<li>After the first encryption of unencrypted data, you may need to deal with plaintext copies known as \"ghost copies\".</li>\r\n<li>For performance reasons, as well as security concerns, it makes no sense to encrypt data with TDE twice (exception: during migration from TDE column encryption to TDE tablespace encryption, but only temporarily, for the duration of the migration).</li>\r\n<li>It is best if you perform two different backups of the Encryption Wallet before encrypting data with TDE. </li>\r\n<li>The (local) Auto-Open Wallet cwallet.sso may never be saved together with the database.</li>\r\n<li>File access authorization for the Encryption Wallet (see Best Practices white paper):<br />OS&gt; chmod 600 ewallet.p12</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"A4.2_Empfehlungen_TSE\"></a>4.2 Recommendations for TDE Tablespace Encryption</strong></p>\r\n<ul>\r\n<li>Since indexes as well as tables may also contain sensitive data, indexes of tables that are in encrypted tablespaces should also be in an encrypted tablespace. </li>\r\n<li>Tables of the BR*Tools dictionary (see appendix), which are all the tables specified in the SAPDBA role (sapdba_role.sql), should not be in an encrypted tablespace if no Auto-Open Wallet is configured. It is easiest to move these tables and indexes to a separate tablespace.</li>\r\n<li>Advantages of TDE tablespace encryption compared to TDE column encryption:</li>\r\n<ul>\r\n<li>Fewer technical restrictions:<br />- No restriction for data types <br />- No restriction of data record length <br />- No memory overhead</li>\r\n</ul>\r\n<ul>\r\n<li>You no longer require the analysis of the SAP application to identify relevant tables and columns with sensitive data.</li>\r\n</ul>\r\n<ul>\r\n<li>Data in the PSAPTEMP tablespace is also encrypted.</li>\r\n</ul>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"A4.3_Empfehlungen_TDE_Column\"></a>4.3 Recommendations for TDE Column Encryption</strong></p>\r\n<p>For SAP, TDE column encryption should no longer be used as of Release 11.2. Instead, you should use TDE tablespace encryption.</p>\r\n<ul>\r\n<li>Oracle recommends that you encrypt columns with NOMAC (less memory overhead and CPU overhead).</li>\r\n<li>If more than 100 columns are to be encrypted, you should consider using TDE tablespace encryption.</li>\r\n<li>Columns of tables of the SAP Basis application should not be encrypted if possible so that all important Basis transactions of the SAP system also work after you restart the database if the Encryption Wallet is not yet open. You may need to configure an Auto-Open Wallet if SAP tables must be encrypted, which are necessary for the SAP core system to work.</li>\r\n<li>Tables of the BR*Tools dictionary should not be encrypted without a (local) Auto-Open Wallet so that SAP BR*Tools (brbackup, brarchive) can also make the relevant log entries after restarting the database (for example, after an offline backup) if the Encryption Wallet is still closed.</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"A4.4_Empfehlungen_Backup_Export\"></a>4.4 Recommendations for Using TDE for Backups and Export Dumps</strong></p>\r\n<p>If you use Oracle TDE to protect sensitive SAP application data from unauthorized access, then you should also consider using the following Oracle technologies:</p>\r\n<ul>\r\n<li>Encryption of backups with RMAN backup encryption: See Note 1324684.<br />For more information, see&#x00A0;<a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/security/index-095354.html\">http://www.oracle.com/technetwork/database/security/index-095354.html</a>.</li>\r\n<li>Encryption of EXPDP export files: See Note 1324930.<br />For more information, see <a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/security/index-092213.html\">http://www.oracle.com/technetwork/database/security/index-092213.html</a>.</li>\r\n<li>Encryption of the network traffic to Oracle database: See SAP Note 973450.</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"A5_EINSATZ\"></a>&#xFEFF;5. Using TDE</strong></p>\r\n<p><strong><a target=\"_blank\" name=\"A5.1_VORBEREITUNGEN\"></a>&#xFEFF;5.1 Preparing to Use TDE</strong><br /><br /><span style=\"text-decoration: underline;\">Scenario 1: First-time use of TDE</span><br />Prerequisites:</p>\r\n<ul>\r\n<li>TDE was not yet configured in this database.</li>\r\n<li>The storage location of the Encryption Wallet &lt;wallet_location&gt; is specified in sqlnet.ora (see above: ENCRYPTION_WALLET_LOCATION)</li>\r\n</ul>\r\n<p>Perform the following steps to prepare the database for TDE (tablespace encryption or column encryption). In this case, you can also use 'brspace -f mdencr'.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Check Encryption Wallet status and storage location</td>\r\n<td>\r\n<p>OS&gt; brspace -f mdencr -a show<br />SQL&gt; select STATUS, WRL_PARAMETER from v$encryption_wallet;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Generate TDE master key</td>\r\n<td>\r\n<p>OS&gt; brspace -f mdencr -a create<br />SQL&gt; alter system set encryption key&#x00A0;identified by \"&lt;wallet_password&gt;\";<br />When you first execute this command, the Encryption Wallet file ewallet.p12 is created. Choose a secure password.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Check Encryption Wallet status and storage location</td>\r\n<td>\r\n<p>OS&gt; brspace -f mdencr -a show<br />SQL&gt; select STATUS, WRL_PARAMETER from v$encryption_wallet;<br />The Encryption Wallet is now open (STATUS=OPEN).</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Save the Encryption Wallet</td>\r\n<td>OS&gt; brspace -f mdencr -a save</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Note:</p>\r\n<ul>\r\n<li>Choose a secure password if possible for the Encryption Wallet. Pay attention to uppercase and lowercase.</li>\r\n<li>The wallet password is independent of other passwords and the master key.</li>\r\n<li>Make sure that the Encryption Wallet is saved again as of now and for each change!!</li>\r\n<li>Save the Encryption Wallet before you encrypt data with TDE. The security of the data can be guaranteed only in this way.</li>\r\n<li>The size of the Encryption Wallet slowly increases with each new master key.</li>\r\n<li>If, for test purposes, you create a master key, test TDE, but then no longer use it, you should still not delete the existing Encryption Wallet. If it was deleted and you still want to use TDE for this database, apply patch 8682102 (Release ********) and perform log switches until all log files were rotated once. You can then create a new Oracle Wallet with a new master key.</li>\r\n</ul>\r\n<p><br /><span style=\"text-decoration: underline;\">Scenario 2: TDE use after the database upgrade from 10.2 to 11.2</span><br /><br />Prerequisites:</p>\r\n<ul>\r\n<li>&lt;wallet_location&gt; is specified in sqlnet.ora (see above: ENCRYPTION_WALLET_LOCATION)</li>\r\n<li>TDE column encryption was already used with Release 10.2.</li>\r\n<li>Data base upgrade from Release 10.2 to Release 11.2</li>\r\n<li>Further use of TDE column encryption or migration to tablespace encryption</li>\r\n</ul>\r\n<p>If you already use TDE column encryption with Oracle Release 10.2 and you perform an upgrade of the database to Release 11.2, also execute the following steps:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Copy the Encryption Wallet</td>\r\n<td>Copy the Encryption Wallet from Oracle Home (10.2) to the new location in the new Oracle Home (11.2).</td>\r\n</tr>\r\n<tr>\r\n<td>Configure the Encryption Wallet location</td>\r\n<td>Configure the parameter ENCRYPTION_WALLET_LOCATION in sqlnet.ora.</td>\r\n</tr>\r\n<tr>\r\n<td>Check the Encryption Wallet status</td>\r\n<td>\r\n<p>OS&gt; brspace -f mdencr -a show<br />SQL&gt; select status from v$encryption_wallet;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Open the Encryption Wallet</td>\r\n<td>\r\n<p>OS&gt; brspace -f mdencr -a open<br />SQL&gt; alter system set encryption wallet open identified by \"&lt;wallet_password&gt;\";</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Check the Encryption Wallet status</td>\r\n<td>\r\n<p>OS&gt; brspace -f mdencr -a show<br />SQL&gt; select status from v$encryption_wallet;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Generate a new TDE master key</td>\r\n<td>\r\n<p>Now generate a new TDE master key if you want to use tablespace encryption as of now or if you want to convert from column encryption to tablespace encryption.</p>\r\n<p>OS&gt; brspace -f mdencr -a newkey<br />SQL&gt; alter system set encryption key&#x00A0;identified by \"&lt;wallet_password&gt;\";</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Save the Encryption Wallet</td>\r\n<td>\r\n<p>Perform a backup of the Encryption Wallet:</p>\r\n<p>OS&gt; brspace -f mdencr -a save</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#x00A0;</p>\r\n<p><strong><a target=\"_blank\" name=\"A5.2_EINSATZ_TSE\"></a>5.2 Using TDE Tablespace Encryption</strong></p>\r\n<p>Prerequisites:</p>\r\n<ul>\r\n<li>Use of TDE is prepared (see above).</li>\r\n<li>Encryption Wallet with master key exists.</li>\r\n<li>You performed at least one backup of the Encryption Wallet.</li>\r\n</ul>\r\n<p>Carry out the following steps:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Start the database</td>\r\n<td>SQL&gt; startup mount</td>\r\n</tr>\r\n<tr>\r\n<td>Open the Encryption Wallet</td>\r\n<td>SQL&gt; alter system set encryption wallet open identified by \"&lt;wallet_password&gt;\";</td>\r\n</tr>\r\n<tr>\r\n<td>Open the database</td>\r\n<td>SQL&gt; alter database open;</td>\r\n</tr>\r\n<tr>\r\n<td>Create new, encrypted tablespaces</td>\r\n<td>\r\n<p>OS&gt; brspace -f tscreate -encryption yes</p>\r\n<p>OS&gt; brtools -&gt; Space Management -&gt; Create tablespace -&gt; Tablespace encryption (encrypt) [yes]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Transfer data from a different tablespace</td>\r\n<td>\r\n<p>Transfer all tables and indexes from the old, unencrypted tablespace to the new, encrypted tablespace.</p>\r\n<p>For example, with brtools -&gt; Segment Management -&gt; Reorganize Tables</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Delete blank tablespaces and data files securely</td>\r\n<td>\r\n<p>Once you have transferred all the data from a tablespace to the new, encrypted tablespace, you can delete the old tablespace. Make sure that the data files are deleted securely and that the blocks in the file system are overwritten securely with the appropriate tools, to ensure that the unencrypted data does not remain visible as ghost copies in the file system.</p>\r\n<p>See below: 'Handling ghost copies'</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<ul>\r\n<ul></ul>\r\n</ul>\r\n</ul>\r\n</ul>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>5.3 Using TDE Column Encryption</strong></p>\r\n<p>Note the following: The use of TDE column encryption for SAP is no longer recommended as of Release 11.2. Instead, use TDE tablespace encryption</p>\r\n<p>Prerequisites:</p>\r\n<ul>\r\n<li>Use of TDE is prepared (see above).</li>\r\n<li>Encryption Wallet with master key exists.</li>\r\n<li>You performed at least one backup of the Encryption Wallet.</li>\r\n</ul>\r\n<p>To use TDE column encryption, proceed as follows:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Determine the columns</td>\r\n<td>Together with the relevant departments, specify which data should be encrypted (that is, which tables and columns). Also refer to the recommendations for TDE column encryption.</td>\r\n</tr>\r\n<tr>\r\n<td>Encryption of the specified table columns </td>\r\n<td>Encrypt the columns (see below)</td>\r\n</tr>\r\n<tr>\r\n<td>Refreshing the table statistics</td>\r\n<td>\r\n<p>After encryption, you should update the table statistics of the relevant tables. The relevant brconnect command is:</p>\r\n<p>OS&gt; brconnect -u / -c -f stats -t &lt;table_name1&gt;,... -f collect</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Delete blank tablespaces and data files securely</td>\r\n<td>See below 'Handling ghost copies'</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Supported data types/encryption algorithms</span></p>\r\n<p>See the Oracle documentation.</p>\r\n<p><span style=\"text-decoration: underline;\">Procedure for the encryption of table columns</span></p>\r\n<p>The encryption of a column of an existing table can be executed in two ways:</p>\r\n<ol>\r\n<li><span style=\"text-decoration: underline;\">Using the SQL command 'ALTER TABLE'</span><br />The syntax for activating TDE using 'ALTER TABLE' is:<br />SQL&gt; ALTER TABLE &lt;table_name&gt; MODIFY (&lt;column name&gt; ENCRYPT&#x00A0;'NOMAC');<br />SQL&gt; ALTER TABLE &lt;table_name&gt; MODIFY (&lt;column name&gt; ENCRYPT&#x00A0;USING '&lt;alg&gt;' 'NOMAC');</li>\r\n<li><span style=\"text-decoration: underline;\">Using online redefinition (DBMS_REDEFINITION) with BRSPACE</span><br /><br />Under this method, the table remains open for other DML operations during the encryption process. In addition, no chained rows are created due to the TDE memory overhead.<br /><br />If the online redefinition is executed using the SAP BR* tools (BRSPACE), you have to manually edit the generated file ddl.sql (use the option \"-d first\"). For the CREATE TABLE command, the clause for the encryption must be specified in the columns that are to be encrypted in the target table.<br />For example:<br />CREATE TABLE &lt;tablename&gt; (<br />firstname VARCHAR2(128),<br />lastname VARCHAR2(128),<br />id1 NUMBER <span style=\"text-decoration: underline;\">ENCRYPT,</span><br />id2 NUMBER <span style=\"text-decoration: underline;\">ENCRYPT 'NOMAC',</span><br />id3 NUMBER <span style=\"text-decoration: underline;\">ENCRYPT USING &lt;encryption_algorithm&gt;</span><br />);</li>\r\n</ol>\r\n<p>&#x00A0;</p>\r\n<p><span style=\"text-decoration: underline;\">Encrypting with SALT or NO SALT</span></p>\r\n<p>Columns are encrypted with SALT by default. Encrypting with SALT involves a random value being added to the value to be encrypted before encryption. Twice the same plaintext then leads to two different values after encryption. Without SALT, the same plaintext also creates the same encrypted value with the same algorithm. Encryption with SALT is therefore more secure.<br /><br />SALT requires 16 bytes of additional memory for each encrypted piece of data. Information regarding which columns are encrypted with SALT and without SALT is contained in DBA_ENCRYPTED_COLUMNS.SALT.<br /><br /><span style=\"text-decoration: underline;\">Encrypted index columns</span></p>\r\n<p>If a column that you want to encrypt appears in an index, this column must be encrypted with the NO SALT option. Otherwise, you get the following error message:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ORA-28338: can not encrypt indexed column(s) with salt</span><br /><br />Example:<br />SQL&gt; CREATE TABLE &lt;table_name&gt; (&lt;column name&gt; ENCRYPT NO SALT&#x00A0;'NOMAC');<br />SQL&gt; ALTER&#x00A0;&#x00A0;TABLE &lt;table_name&gt; (&lt;column name&gt; NO SALT 'NOMAC');<br /><br />When encrypting index columns, note: An index in an encrypted column can be used only with an equality condition because the column values are also encrypted in the index. It may have a negative effect on performance if the index was used with inequality conditions (&lt;, &lt;=, &gt;, &gt;=, like): You can then no longer use the index.<br /><br />Alternatively, the existing indexes can be created again without the columns to be encrypted.  Whether this is enough to avoid long response times must be determined in each individual case.<br /><br />Is an index defined in a certain table column?<br />SQL&gt; SELECT INDEX_OWNER, INDEX_NAME FROM DBA_IND_COLUMNS WHERE TABLE_NAME = '&lt;table name&gt;' AND COLUMN_NAME = '&lt;column name&gt;';<br /><br />Which columns are encrypted?<br />SQL&gt; SELECT OWNER, TABLE_NAME, COLUMN_NAME, SALT, ENCRYPTION_ALG FROM DBA_ENCRYPTED_COLUMNS ORDER BY OWNER, TABLE_NAME, SALT;</p>\r\n<p><br /><strong>5.4 Terminating the Use of TDE</strong></p>\r\n<p>You can also reverse the TDE encryption of data in the database. You still should not delete the Encryption Wallet afterwards because you cannot reactivate TDE in this database otherwise (see ORA-28374: typed master key not found in wallet).<br /><br /><span style=\"text-decoration: underline;\">For TDE tablespace encryption:</span></p>\r\n<ol>\r\n<li>Page out tables and indexes from encrypted tablespaces <br />for example, via brspace (tablespace reorganization).</li>\r\n<li>Discard empty, encrypted tablespaces<br />for example, via brspace.</li>\r\n<li>Do encrypted tablespaces still exist?<br />SQL&gt; SELECT tablespace_name, ENCRYPTED from DBA_TABLESPACES WHERE ENCRYPTED = 'YES';</li>\r\n<li>Do not delete the Encryption Wallet<br />You should not delete the Encryption Wallet (immediately) because it may be required in the case of a media recovery if a backup is imported from a time when TDE was still activated. Otherwise, you must reload the Encryption Wallet via a 'disaster recovery'.</li>\r\n</ol>\r\n<p><span style=\"text-decoration: underline;\">For TDE column encryption:</span></p>\r\n<ol>\r\n<li>Decrypt table columns<br />SQL&gt; ALTER TABLE &lt;table&gt; MODIFY (&lt;col&gt; DECRYPT);<br />This command runs a full-table-update, and writes the unencrypted data back to the database. This can also be run as an online redefinition.</li>\r\n<li>Do encrypted columns still exist?<br />SQL&gt; SELECT count(*) from DBA_ENCRYPTED_COLUMNS;</li>\r\n<li>Do not delete the Encryption Wallet</li>\r\nYou should not delete the Encryption Wallet (immediately) because it may be required in the case of a media recovery if a backup is imported from a time when TDE was still activated. Otherwise, you must reload the Encryption Wallet via a 'disaster recovery'.</ol>\r\n<p><br /><br /><strong><a target=\"_blank\" name=\"A5.5_KLARTEXTKOPIEN\"></a>5.5 Handling Plaintext Copies of Encrypted Data</strong></p>\r\n<p>Over time, tables and indexes are relocated, copied, moved and sorted several times within a tablespace or from one tablespace to another tablespace. Such movements leave traces in the database file of what are known as 'ghost copies'. <span style=\"text-decoration: underline;\">Ghost copies</span> are free data blocks in a database file, former table blocks or index blocks that have not yet been assigned to a new segment and which still contain the old information provided they were not overwritten with new data.<br /><br />If you use transparent data encryption (TDE) and encrypt a table or selected columns of a table, only the data of the table copy that is currently valid is encrypted. Data in blocks of ghost copies still remain visible in plaintext for a while. Due to this phenomenon, you can still see old plaintext data after TDE encryption when accessing data blocks directly (for example, with a hex editor). This is possible until these data blocks are overwritten again with other data.<br /><br />To ensure that as little as possible old plaintext still exists in the file system when you use TDE, we recommend the following procedure:<br /><br /><span style=\"text-decoration: underline;\">Tablespace encryption:</span><br />Delete the old, empty tablespace safely.</p>\r\n<ol>\r\n<li>Note which data files belong to the old tablespace.<br />For example, with BR*Tools<br />SQL&gt; select file_name from dba_data_files where tablespace_name = '&lt;tablespace_name&gt;';</li>\r\n<li>Delete the old tablespace (without the files)<br />SQL&gt; DROP TABLESPACE &lt;tablespace_name&gt; INCLUDING CONTENTS KEEP DATAFILES;<br />Or via BR*Tools: BRSPACE option: -KDF (keep data files, as of BRSPACE 7.20)</li>\r\n<li>To securely delete the database files of the original tablespace, use operating system-specific methods such as 'shred' (Unix/Linux), 'eraser' (Freeware), or 'SDelete' (Windows) (see <a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/security/tde-faq-093689.html\">http://www.oracle.com/technetwork/database/security/tde-faq-093689.html</a>).</li>\r\n</ol>\r\n<p><span style=\"text-decoration: underline;\">TDE column encryption:</span></p>\r\n<ol>\r\n<li>Create a new tablespace.</li>\r\n<li>Execute TDE encryption for all affected tables in the original tablespace. In the new tablespace, the data that is to be encrypted should not exist unencrypted because they could still exist as ghost copies.</li>\r\n<li>After encryption in the original tablespace, move all tables and indexes from the original tablespace to the new tablespace.</li>\r\n<li>Now delete the empty original tablespace (DROP TABLESPACE) without using the clause 'AND DATAFILES'. Use the option ('KEEP DATAFILES') instead.</li>\r\n<li>To securely delete the database files of the original tablespace, use operating system-specific methods such as 'shred' (Unix/Linux), 'eraser' (Freeware), or 'SDelete' (Windows) (see <a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/security/tde-faq-093689.html\">http://www.oracle.com/technetwork/database/security/tde-faq-093689.html</a>).</li>\r\n</ol>\r\n<p>&#x00A0;</p>\r\n<p><strong><a target=\"_blank\" name=\"A6.2_Administration_mit_sqlplus\"></a>&#xFEFF;6.2 Encryption Wallet Administration with SQL*Plus</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Check the Encryption Wallet status</td>\r\n<td>V$ENCRYPTION_WALLET contains information about the current status (OPEN, CLOSED) and the storage location &lt;wallet_location&gt;:<br />SQL&gt; select * from v$encryption_wallet;</td>\r\n</tr>\r\n<tr>\r\n<td>Create a new Encryption Wallet</td>\r\n<td>The Encryption Wallet is created automatically by Oracle when it first generates the master key.<br />SQL&gt; alter system set encryption&#x00A0;key&#x00A0;identified by \"&lt;wallet_password&gt;\";</td>\r\n</tr>\r\n<tr>\r\n<td>Generate a new master key (REKEY)</td>\r\n<td>SQL&gt; alter system set encryption&#x00A0;key identified by \"&lt;wallet_password&gt;\";</td>\r\n</tr>\r\n<tr>\r\n<td>Open the Encryption Wallet</td>\r\n<td>SQL&gt; alter system set encryption wallet open identified by \"&lt;wallet_password&gt;\";</td>\r\n</tr>\r\n<tr>\r\n<td>Close the Encryption Wallet</td>\r\n<td>\r\n<p>To close the Encryption Wallet in Release 11.2, the wallet password must be entered:<br />SQL&gt; alter system set encryption wallet close&#x00A0;identified by &lt;wallet_password&gt;;</p>\r\n<p>In Release 10.2, you can close the Encryption Wallet without entering the wallet password:<br />SQL&gt; alter system set encryption wallet close;<br /><br />When the database is stopped, the Encryption Wallet is closed automatically.</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong><a target=\"_blank\" name=\"A6.3_orapki\"></a>6.3 Encryption Wallet Administration with orapki</strong></p>\r\n<p>To display the complete orapki syntax: OS&gt; orapki wallet help</p>\r\n<p>orapki is a command line tool and supports the following wallet operations (among others):</p>\r\n<ul>\r\n<li>Displaying Encryption Wallet contents<br />Changing Encryption Wallet password</li>\r\n<li>Configuring/deleting local Auto-Open Wallet</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Displaying orapki syntax </span></p>\r\n<p>OS&gt; orapki wallet help</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Displaying Encryption Wallet contents</span></p>\r\n<p>OS&gt; orapki wallet display -wallet &lt;wallet_location&gt;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Changing the Encryption Wallet password</span></p>\r\n<p>OS&gt; orapki wallet change_pwd -wallet &lt;wallet_location&gt;&#x00A0;[-oldpwd &lt;oldpwd&gt; -newpwd &lt;newpwd&gt;]<br /><br />Note:</p>\r\n<ul>\r\n<li>Before you assign a new Encryption Wallet password, you should save the Encryption Wallet. If you mistype or forget the new password, there is also a copy of the wallet that can be opened with the old, known password.  You can delete the old copy only if the new password was tested successfully.</li>\r\n<li>The new wallet password is entered once hidden. There is a risk here of an inadvertent incorrect entry. Therefore, it is better to change the wallet password via BRSPACE.</li>\r\n<li>After you change the wallet password with orapki, you should close the Encryption Wallet and open it again with the new password. To close it, you must use the old password again!<br />SQL&gt; alter system set encryption wallet close&#x00A0;identified by \"&lt;oldpwd&gt;\";<br />SQL&gt; alter system set encryption wallet open&#x00A0;identified by \"&lt;newpwd&gt;\";</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Configuring Auto-Open Encryption Wallet cwallet.sso</span></p>\r\n<p>Prerequisite: The Encryption Wallet ewallet.p12 exists.<br /><br />The orapki command is as follows:<br />OS&gt; orapki wallet create -wallet &lt;wallet_location&gt; -auto_login&#x00A0;[ -pwd &lt;wallet_password&gt; ]<br />OS&gt; orapki wallet create -wallet &lt;wallet_location&gt; -auto_login_local&#x00A0;[ -pwd &lt;wallet_password&gt; ]<br /><br />With orapki, the wallet password can be entered as visible in the command line or hidden interactively.<br />The Auto-Open Wallet is not saved by the BR*Tools.<br />A local Auto-Open Wallet is more secure because it can be opened only on the computer on which it was created:</p>\r\n\r\n<p>&#x00A0;</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><br /></p>\r\n<p><strong><a target=\"_blank\" name=\"A6.4_BRSPACE\"></a>6.4 Encryption Wallet Administration with BRSPACE</strong></p>\r\n<p>Managing the TDE Encryption Wallet via BRSPACE provides the following advantages as well as easier operation:</p>\r\n<ul>\r\n<li>Automatic backup of the Encryption Wallet before and after change operations</li>\r\n<li>Duplicate entry of the new wallet password for password changes</li>\r\n<li>Option of open and hidden password entry </li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">TDE administration access (brspace)</span><br />To access TDE administration with BRSPACE, you navigate via the BRTOOLS menu or directly via BRSPACE.<br /><br />OS&gt; brtools&#x00A0;-&gt; 1 = Instance management -&gt; 7 - Manage data encryption<br />OS&gt; brspace -f mdencr<br /><br /><span style=\"text-decoration: underline;\">Check Encryption Wallet status (brspace)</span><br />OS&gt; brspace -f mdencr -a show<br /><br /><span style=\"text-decoration: underline;\">Create a new Encryption Wallet (brspace)</span><br />OS&gt; brspace -f mdencr -a create<br />The Encryption Wallet ewallet.p12 is created. A copy of the Encryption Wallet ewallet.cpy is created.</p>\r\n<p><span style=\"text-decoration: underline;\">Save the Encryption Wallet (brspace)</span><br />OS&gt; brspace -f mdencr -a save<br />A copy of the Encryption Wallet ewallet.cpy is created. The Encryption Wallet is saved (ewallet.sve).<br /><br /><span style=\"text-decoration: underline;\">Generate new master key (REKEY) (brspace)</span><br />OS&gt; brspace -f mdencr -a newkey<br /><br /><span style=\"text-decoration: underline;\">Open Encryption Wallet (brspace)</span></p>\r\n<ul>\r\n<li>Enter the wallet password via the menu: OS&gt; brspace -f mdencr -a open</li>\r\n<li>Enter the wallet password as hidden interactively: OS&gt; brspace -f mdencr -a open -password</li>\r\n<li>Enter the wallet password as visible interactively: OS&gt; brspace -f mdencr -a open -PASSWORD</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Close Encryption Wallet (brspace)</span><br />OS&gt; brspace -f mdencr -a close<br /><br /><span style=\"text-decoration: underline;\">Configure auto-open Wallet cwallet.sso (brspace)</span><br />OS&gt; brspace -f mdencr<br />OS&gt; brspace -f mdencr -a enable<br />OS&gt; brspace -f mdencr -a disable<br />For more information, see SAP Note 1464091, point 2.<br /><br /><span style=\"text-decoration: underline;\">Change Encryption Wallet password (brspace)</span><br />OS&gt; brspace -f mdencr -a chpass<br />OS&gt; brspace -f mdencr -a chpass -password -newpass<br />OS&gt; brspace -f mdencr -a chpass -PASSWORD -NEWPASS</p>\r\n<ol>\r\n<li>The Encryption Wallet is saved (ewallet.old).</li>\r\n<li>The Encryption Wallet password is changed.</li>\r\n<li>The Encryption Wallet is closed and reopened.</li>\r\n<li>A copy of the Encryption Wallet (ewallet.cpy) is created.</li>\r\n<li>The Encryption Wallet is saved (ewallet.new).</li>\r\n</ol>\r\n<p><span style=\"text-decoration: underline;\">Display Encryption Wallet contents (brspace)</span><br />This function is not supported by BRSPACE. Use orapki or the Wallet Manager to display the wallet contents.<br /><br /><span style=\"text-decoration: underline;\">Create an encrypted tablespace (brspace)</span><br />OS&gt; brspace -f tscreate -encryption yes<br /><br /><span style=\"text-decoration: underline;\">Offline backup with TDE (brbackup)</span><br />BRBACKUP provides the option of automatically reopening the wallet after you start the database after an offline backup. For more information, see Note 1235952. In this case, you enter the password via the following option:<br />-pw -pass_wallet -PW -PASS_WALLET [&lt;wallet_password&gt;]<br /><br />For example:<br />OS&gt; brbackup -u / -d disk -t offline -m all -c -pass_wallet<br />OS&gt; brbackup -u / -d disk -t offline -m all -c -PASS_WALLET</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;BR1712I Database wallet /oracle/QO1/112_64/dbs/ewallet.p12 opened successfully&#xFEFF;</span></p>\r\n\r\n<p><br /><br /></p>\r\n<p><strong><a target=\"_blank\" name=\"A6.5_OWM\"></a>6.5 Encryption Wallet Administration with Oracle Wallet Manager</strong></p>\r\n\r\n<p>You can also use the graphical user interface of the Oracle Wallet Manager (OWM) to manage the TDE Encryption Wallet. The command to start the OWM&#x00A0;from the command line is \"owm\". The OWM features the same functions as the \"orapki\" command line tool.</p>\r\n\r\n<p><br /><strong><a target=\"_blank\" name=\"A6.6_autoopen_wallet\"></a>6.6 Encryption Wallet and Local Auto-Open Wallet</strong></p>\r\n<p>The use of an Auto-Open Wallet ensures that the database can access encrypted data after it is restarted without the Security Administrator manually opening the Encryption Wallet. You should never save an Auto-Open Wallet together with the database. BRBACKUP always saves only the Encryption Wallet, but never an Auto-Open Wallet. Oracle RMAN currently does not support any Encryption Wallet backup.<br /><br />A local Auto-Open Wallet (see below) is more secure than an Auto-Open Wallet. A local Auto-Open Wallet works only on the database server (same host name) on which it was generated and under the same operating system user.<br /><br />For the database operation, it is sufficient if only the Auto-Open Wallet exists. The Encryption Wallet must exist for the following wallet operations:</p>\r\n<ul>\r\n<li>Changing the wallet password<br />Otherwise: PKI-02003: Unable to load the wallet at: &lt;wallet_location&gt;</li>\r\n<li>Generating a new master key<br />Otherwise: ORA-28368: cannot auto-create wallet</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"A7.1_KONSEQUENZEN\"></a>&#xFEFF;7.1 Consequences of Using TDE for Administration</strong></p>\r\n<p><br /><span style=\"text-decoration: underline;\">Offline backup with BRBACKUP</span><br />If no Auto-Open Wallet is configured, you must specify the wallet password to open the Encryption Wallet automatically after an offline backup with BRBACKUP (see Note 1235952).<br />Examples:<br />OS&gt; brbackup -u / -d util_file -t offline -m all -c -pw<br />OS&gt; brbackup -u / -d util_file -t offline -m all -c -PW<br /><br />If the Encryption Wallet is not opened again by BRBACKUP and if the tables of the BR*Tools dictionary are saved in an encrypted tablespace, BRBACKUP cannot access these tables.<br /><br /><span style=\"text-decoration: underline;\">Starting the database</span><br />When you use TDE tablespace encryption, you must open the wallet for the instance recovery (after instance crash, shutdown abort) in mount status before you open the database. Without access to the master key in the open wallet, the instance recovery fails for encrypted data.</p>\r\n<ol>\r\n<li>OS&gt; sqlplus / as sysoper<br />SQL&gt; STARTUP MOUNT<br />OS&gt; brspace -u / -c -function dbstart -state mount</li>\r\n<li>OS&gt; sqlplus / as sysoper<br />SQL&gt; ALTER SYSTEM SET ENCRYPTION WALLET OPEN IDENTIFIED BY \"&lt;wallet_password&gt;\";<br />OS&gt; brspace -u / -c -function mdencr -action open -pw</li>\r\n<li>OS&gt; sqlplus / as sysoper<br />SQL&gt; ALTER DATABASE OPEN<br />OS&gt; brspace -u / -c -function dbstart -state open (not possible if the database has already been mounted)</li>\r\n</ol>\r\n<p>We recommend that you always open the database in this way if you are using tablespace encryption.<br /><br /><span style=\"text-decoration: underline;\">Stopping the database</span><br />To stop the database instance, the Encryption Wallet must be opened if encrypted data was accessed (internal cleanup operations).</p>\r\n<ol>\r\n<li>OS&gt; sqlplus / as sysoper<br />SQL&gt; SHUTDOWN IMMEDIATE<br />OS&gt; brspace -u / -c -function dbshut -mode immediate</li>\r\n</ol>\r\n<p><span style=\"text-decoration: underline;\">Flashback query</span><br />The flashback query only works as far back as the last change to the table key or master key.<br /><br /><span style=\"text-decoration: underline;\">Flashback Database</span><br />A flashback database operation does not reset the Oracle Wallet.<br /><br /><span style=\"text-decoration: underline;\">Real Application Clusters (RAC)</span><br />The Encryption Wallet is should be saved on a shared disk. As a result, all instances of an RAC cluster have access to the same central Encryption Wallet. As of Release 11.2, the system automatically forwards all wallet operations (open, close, master key rekey) to all RAC instances.<br /><br /><span style=\"text-decoration: underline;\">Instance/database recovery</span><br />To restore a database with TDE, the Encryption Wallet must be opened.<br /><br /><span style=\"text-decoration: underline;\">Data guard/physical standby database</span><br />On the standby side, the current Encryption Wallet must be open to recover the database in mount status. You may want to consider configuring an Auto-open Wallet for the standby side. If a new master key was generated in the primary database, you must copy a new Auto-Open Wallet with the new master key to the standby database.</p>\r\n<p><strong>Appendix</strong></p>\r\n<p><strong><a target=\"_blank\" name=\"ANHANG_A\"></a>Appendix A: Disaster recovery of the Encryption Wallet</strong></p>\r\n<p>In contrast to a database file, the Encryption Wallet cannot be restored by 'RECOVER DATABASE'. If the current Encryption Wallet was deleted or overwritten (and only then!!), it must be reloaded from a backup (disaster recovery).<br /><br />With a disaster recovery, you can reload the Encryption Wallet ewallet.p12 together with the other parameter files initSID.sap, initSID.ora and spfileSID.ora. The command is:<br /><br />OS&gt; brrecover -t disaster<br />-&gt; Restore profiles and log files from BRBACKUP backup<br />-&gt; &lt;Device type&gt;<br /><br />Select 'Oracle Wallet' in the menu point \"Restore of profiles and log files of BRBACKUP backup\".<br /><br />If a new master key was generated after the backup of the Encryption Wallet, the database can be restored only up to this point! All changes to encrypted data after that cannot be restored!<br /><br />Since the Encryption Wallet contains not only the current master key, but all earlier master keys, data that was encrypted with TDE using earlier master keys can still be decrypted. A current wallet file should therefore never be overwritten by an earlier version from a backup!</p>\r\n<p><strong>Appendix C.2: CPU Overhead</strong></p>\r\n<p>Also see the FAQ document for TDE.<br /><br /><span style=\"text-decoration: underline;\">CPU overhead for column encryption</span></p>\r\n<ul>\r\n<li>The CPU overhead occurs only when accessing (reading and writing to) encrypted columns. No overhead when accessing (reading or writing to) unencrypted columns.</li>\r\n<li>On average, the CPU overhead is approximately 5%. For an individual statement, the % CPU overhead with TDE column encryption may also be significantly higher than 5%.</li>\r\n<li>The actual overhead depends on the number of encrypted columns and the frequency of the access to encrypted columns.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">CPU overhead for tablespace encryption</span></p>\r\n<ul>\r\n<li>The CPU overhead occurs when reading (decrypting) or writing (encrypting) blocks of encrypted tablespaces.</li>\r\n<li>On average, the CPU overhead is approximately 5%. If special hardware support is available for the encryption (INTEL XEON chips), the CPU overhead for tablespace encryption is reduced by up to 80%.</li>\r\n</ul>\r\n<p><strong>Appendix C.3: Memory Overhead</strong></p>\r\n<p>Also see the FAQ document for TDE.<br /><br /><span style=\"text-decoration: underline;\">Memory overhead for column encryption</span></p>\r\n<p>At a maximum, +52 bytes of additional space will be needed for each encrypted column and record.</p>\r\n<p>Per encrypted date, that is, per encrypted column per data record, a maximum of 52 bytes of additional memory space is required. These consist of the following:</p>\r\n<ul>\r\n<li>Additional 20 bytes for MAC (integrity check)<br />You can save this overhead by using NOMAC. See NOMAC.</li>\r\n<li>Additional 16 bytes for SALT</li>\r\n<li>Up to 16 additional bytes to expand the column by a multiple of 16 bytes when using the AES algorithm</li>\r\n<li>Up to 8 additional bytes to expand the column by a multiple of 8 bytes when using the 3DES168 algorithm</li>\r\n</ul>\r\n<p>Due to the required additional memory space in the case of column encryption, chained rows and the resulting further deterioration in performance may occur.</p>\r\n<p><span style=\"text-decoration: underline;\">Memory overhead for tablespace encryption</span></p>\r\n<ul>\r\n<li>Tablespace encryption does not require additional memory space.<br />The encryption is carried out block-by-block when writing a block. A block in the SGA corresponds to a block in the storage medium.</li>\r\n<li>Tablespace encryption does not result in chained rows.</li>\r\n</ul>\r\n<p><strong>Appendix D: Glossary</strong><br /><br /></p>\r\n<p>For an explanation of the terms, also see the technical Oracle White Paper (OTN): \"Oracle Advanced Security Transparent Data Encryption Best Practices\"</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Auto-login Wallet</td>\r\n<td>\r\n<p>Obsolete term for the Auto-Open Wallet</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>(Local) Auto-open Wallet</td>\r\n<td>\r\n<p>Wallet file 'cwallet.sso';<br /><br />The Auto-Open (Encryption) Wallet is an Encryption Wallet that is automatically open without a password when you open the database. <br /><br />The local version of the Auto-Open Wallet works only under the same operating system user who generated the local Auto-Open Wallet and only on the database server on which the Auto-Open Wallet was created.<br /><br />You cannot change the Auto-Open Wallet without the related Encryption Wallet.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Encryption Wallet</td>\r\n<td>\r\n<p>File 'ewallet.p12' is outside the database.<br />The TDE master key is saved in the Encryption Wallet for TDE column encryption and/or TDE tablespace encryption.<br /><br />You require the Encryption Wallet password to open the Encryption Wallet.<br /><br />The Encryption Wallet must exist for wallet operations that make changes.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Ghost <br />copy</td>\r\n<td>\r\n<p>Non-encrypted plaintext data in blocks, which previously belonged to a table, but is now encrypted</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&lt;wallet_location&gt;</td>\r\n<td>\r\n<p>The storage location for the Encryption Wallet is specified via the parameter ENCRYPTION_WALLET_LOCATION in the sqlnet.ora.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Hardware Security Module (HSM)</td>\r\n<td>\r\n<p>This is a hardware module for the secure administration of TDE master keys. It can be used instead of a software Encryption Wallet.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>NOMAC</td>\r\n<td>\r\n<p>DDL option for TDE column encryption<br />This is used to deactivate the integrity check at column level to reduce the memory expenditure by 20 bytes for each piece of encrypted data.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SAP BR*Tools dictionary</td>\r\n<td>This includes all tables that are specified in the SAPDBA role (sapdba_role.sql).</td>\r\n</tr>\r\n<tr>\r\n<td>sapwallet</td>\r\n<td>\r\n<p>This is a directory in SAPDATA_HOME in which BRSPACE creates backups of the wallet (ewallet.new, ewallet.old) if the wallet is changed.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Security policy</td>\r\n<td>\r\n<p>(Customer-specific) policy for database security</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>TDE</td>\r\n<td>\r\n<p>This is the abbreviation for \"Transparent Data Encryption\".</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>(TDE) Master Key</td>\r\n<td>\r\n<p>The (TDE) master key with which both table keys and tablespace keys are encrypted</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>(TDE) Table key <br />(TDE) Table key</td>\r\n<td>\r\n<p>This is the key for encrypting and decrypting sensitive data in table columns. For each table, there is only a maximum of one table key, which is used for all columns in this table.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>(TDE) Tablespace key<br />(TDE) Tablespace key</td>\r\n<td>\r\n<p>This is the key for encrypting and decrypting all data in tablespaces.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>TSE</td>\r\n<td>\r\n<p>TSE = \"Tablespace Encryption\"<br />This is usually the abbreviation for \"TDE Encrypted Tablespaces\".</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#x00A0;</p>\r\n<p><strong><a target=\"_blank\" name=\"ANHANG_E\"></a>Appendix E: Information on Oracle Technology Network (OTN)</strong><br /><br /></p>\r\n<p>More information about Oracle Transparent Data Encryption (TDE) can be found on OTN using the following links:</p>\r\n<ul>\r\n<li>Database security: <a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/security/index.html\">http://www.oracle.com/technetwork/database/security/index.html</a></li>\r\n<li>Data encryption/transparent data encryption: <a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/options/advanced-security/index-099011.html\">http://www.oracle.com/technetwork/database/options/advanced-security/index-099011.html</a></li>\r\n<li>TDE best practices: <a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/security/twp-transparent-data-encryption-bes-130696.pdf\">http://www.oracle.com/technetwork/database/security/twp-transparent-data-encryption-bes-130696.pdf</a></li>\r\n<li>TDE FAQ: <a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/security/tde-faq-093689.html\">http://www.oracle.com/technetwork/database/security/tde-faq-093689.html</a></li>\r\n<li>Backup encryption: <a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/security/index-095354.html\">http://www.oracle.com/technetwork/database/security/index-095354.html</a></li>\r\n<li>Export file encryption: <a target=\"_blank\" href=\"http://www.oracle.com/technetwork/database/security/index-092213.html\">http://www.oracle.com/technetwork/database/security/index-092213.html</a></li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p><strong><strong><strong><a target=\"_blank\" name=\"ANHANG_F\"></a>Appendix F: Solutions for known problems</strong></strong></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Problem</span></td>\r\n<td><span style=\"text-decoration: underline;\">Problem Description</span></td>\r\n</tr>\r\n<tr>\r\n<td>ORA-28374: typed master key not found in wallet</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Valid for:</span></p>\r\n<p>Release ********</p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>SQL&gt; alter system set encryption key identified by \"&lt;wallet_password&gt;\";<br />alter system set encryption key identified by \"&lt;wallet_password&gt;\"<br />*<br />ERROR at line 1:<br />ORA-28374: typed master key not found in wallet</p>\r\n<p><span style=\"text-decoration: underline;\">Reason:</span></p>\r\n<p>TDE was previously configured in the database for test purposes. However, the Encryption Wallet was deleted after the tests were completed.<br />If TDE is no longer used in a database, the existing Encryption Wallet must still not be deleted because you cannot subsequently create a new Encryption Wallet with a new master key otherwise.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Reload the original Encryption Wallet or apply patch 8682102 and perform log switches until all log files were rotated once. You can then create a new Oracle Wallet with a new master key.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Wallet Manager cannot open wallet</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Valid for:</span></p>\r\n<p>Release 11.2.0.X</p>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>If an Encryption Wallet is created with a simple password such as \"test\", the wallet cannot be opened after that by the Oracle Wallet Manager (owm) (\"Password is incorrect\").</p>\r\n<p>SQL&gt; ALTER SYSTEM SET ENCRYPTION KEY IDENTIFIED BY \"test\";</p>\r\n<p><span style=\"text-decoration: underline;\">Cause:</span></p>\r\n<p>Oracle bug 9353748</p>\r\n<p>owm has implemented password check:<br />- Minimum length of 8 characters<br />- Letters and numbers<br />- Special characters</p>\r\n<p><span style=\"text-decoration: underline;\">Solution/Workaround:</span></p>\r\n<p>1) Use a secure password for Encryption Wallet, or<br />2) Use orapki instead of the Oracle Wallet Manager owm.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>ORA-28365: wallet is not open</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>SQL&gt; shutdown immediate;<br />ORA-28365: wallet is not open<br />SQL&gt;</p>\r\n<p>Alert log:<br />ORA-01595: error freeing extent (5) of rollback segment (22))<br />ORA-28365: wallet is not open<br />ORA-00604: error occurred at recursive SQL level 1<br />ORA-28365: wallet is not open</p>\r\n<p><span style=\"text-decoration: underline;\">Reason:</span></p>\r\n<p>Oracle bug 8369636<br />Internal cleanup operations during the shutdown of the database that affect TSE data (in redo, undo, temp) require an open wallet.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>When you use tablespace encryption, you must always open the Encryption Wallet.</p>\r\n<p><span style=\"text-decoration: underline;\">Reference:</span></p>\r\n<p>My Oracle Support Note 1073237.1</p>\r\n<p>My Oracle Support Note 432776.1</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>startsap r3 fails</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Symptom:</span></p>\r\n<p>'Database is not available via R3trans' when calling 'startsap r3' or 'stopsap r3'</p>\r\n<p><span style=\"text-decoration: underline;\">Reason:</span></p>\r\n<p>The wallet is not opened and SAP tables in an encrypted tablespace cannot be accessed as a result.</p>\r\n<p><span style=\"text-decoration: underline;\">Solution:</span></p>\r\n<p>Open the TDE Encryption Wallet in advance.</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><br /><strong><a target=\"_blank\" name=\"ANHANG_G\"></a>Appendix G: Oracle Dictionary Views for TDE</strong></p>\r\n<p>DBA_ENCRYPTED_COLUMNS, DBA_TABLESPACES.ENCRYPT, V$ENCRYPTION_WALLET, V$ENCRYPTED_TABLESPACES</p>\r\n<p><strong><a target=\"_blank\" name=\"ANHANG_H\"></a>Appendix H: Tables of the BR*Tools dictionary</strong></p>\r\n<p>The SAP BR*Tools dictionary contains all tables that are named specifically in the SAPDBA role (script sapdba_role.sql).<br /><br />This BRSPACE command, in which all BR*Tools dictionary tables are specified by name, lets you move these BR*Tools dictionary tables to another, separate tablespace:<br />OS&gt; brspace -f tbreorg -t \"TAORA,SVERS,SDBAH,SDBAD,SAPLIKEY,RSPSPACE,RSNSPACE,MLICHECK,DDNTT,<br />DDART,DBSTATTORA,DBSTATIORA,DBSTATHORA,DBSTATC,DBSTAIHORA,DBMSGORA,DBCHK,DBCHECKORA,<br />DBATRIAL,DBATL,DBASPAL,DBAREOL,DBARCL,DBAPHAL,DBAOPTL,DBAOBJL,DBAML,DBAGRP,DBAFID,DBAEXTL,<br />DBAERR,DBADFL,DBABL,DBABD,DBABARL,DARTT,CVERS,TSORA,TGORA,IGORA,IAORA,DD09L,DD02L,DBDIFF\"</p>\r\n<p><strong><a target=\"_blank\" name=\"ANHANG_I\"></a>Appendix I: Overview of related SAP notes</strong></p>\r\n<p><a target=\"_blank\" href=\"/notes/1324930\">1324930</a> - Creating encrypted EXPDP exports with BRSPACE<br /><a target=\"_blank\" href=\"/notes/1279682\">1279682</a> - Support for Oracle data encryption in BR*Tools<br /><a target=\"_blank\" href=\"/notes/1324684\">1324684</a> - Creating encrypted RMAN backups using BR*Tools<br /><a target=\"_blank\" href=\"/notes/1235952\">1235952</a> - Minor functional enhancements in BR*Tools (2)<br /><a target=\"_blank\" href=\"/notes/1355140\">1355140</a> - Using Oracle Database Vault in an SAP environment<br /><a target=\"_blank\" href=\"/notes/973450\">973450</a> - Oracle Advanced Security: Network encryption<br /><br /></p>\r\n<p>&#x00A0;</p>\r\n<p>&#x00A0;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-SEC (Security Messages)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Database System", "Value": "Oracle 11.2"}, {"Key": "Database System", "Value": "Oracle 10.2"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5074214)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000974876/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000974876/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000974876/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000974876/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000974876/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000974876/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000974876/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000974876/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000974876/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "tde_demo.txt", "FileSize": "2", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000454832006&iv_version=0058&iv_guid=30AB4BE0A4607D42B7AD305494773E16"}, {"FileName": "TDE_Whitepaper_10gR2.pdf", "FileSize": "567", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000454832006&iv_version=0058&iv_guid=67D46E6A4CE78D409BF59ECFD3AF21AC"}, {"FileName": "sqlnet_ora.txt", "FileSize": "1", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000454832006&iv_version=0058&iv_guid=6A0E1BA5A505C3468AC1B432BB5DDECD"}, {"FileName": "twp_TDE_bestpractices.pdf", "FileSize": "461", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000454832006&iv_version=0058&iv_guid=6FD9B7C8EAA80541BF9CEF0A11E3E2BD"}, {"FileName": "OracleAdvancedSecurity_10gR2_DS.pdf", "FileSize": "92", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000454832006&iv_version=0058&iv_guid=80DCBCD60DCC5A4B96D1D7FF069416A3"}, {"FileName": "twp-transparent-data-encryption-bes-130696.pdf", "FileSize": "573", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000454832006&iv_version=0058&iv_guid=00109B36D66A1EEE9EC74AFADCDFB704"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "973450", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database network encryption and data integrity", "RefUrl": "/notes/973450"}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268"}, {"RefNumber": "2591575", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Transparent Data Encryption (TDE) with SAP NetWeaver", "RefUrl": "/notes/2591575"}, {"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094"}, {"RefNumber": "1586304", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP System Copies with Oracle Transparent Data Encryption", "RefUrl": "/notes/1586304"}, {"RefNumber": "1464091", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (3)", "RefUrl": "/notes/1464091"}, {"RefNumber": "1436352", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 11g Advanced Compression for SAP Systems", "RefUrl": "/notes/1436352"}, {"RefNumber": "1431800", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Central Technical Note", "RefUrl": "/notes/1431800"}, {"RefNumber": "1426979", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g: SecureFiles - The new way to store LOB data", "RefUrl": "/notes/1426979"}, {"RefNumber": "1324930", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Creating encrypted EXPDP exports with BRSPACE", "RefUrl": "/notes/1324930"}, {"RefNumber": "1324684", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Creating encrypted RMAN backups using BR*Tools", "RefUrl": "/notes/1324684"}, {"RefNumber": "1279682", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for Oracle data encryption in BR*Tools", "RefUrl": "/notes/1279682"}, {"RefNumber": "1269911", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Chained Rows", "RefUrl": "/notes/1269911"}, {"RefNumber": "1109743", "RefComponent": "BC-DB-ORA", "RefTitle": "Use of Index Key (Prefix) Compression for Oracle Databases", "RefUrl": "/notes/1109743"}, {"RefNumber": "105047", "RefComponent": "BC-DB-ORA", "RefTitle": "Support for Oracle functions in the SAP environment", "RefUrl": "/notes/105047"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2606965", "RefComponent": "BI-BIP-INS", "RefTitle": "Is Oracle TDE supported on SAP BI Platform?", "RefUrl": "/notes/2606965 "}, {"RefNumber": "2591575", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Transparent Data Encryption (TDE) with SAP NetWeaver", "RefUrl": "/notes/2591575 "}, {"RefNumber": "1436352", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 11g Advanced Compression for SAP Systems", "RefUrl": "/notes/1436352 "}, {"RefNumber": "105047", "RefComponent": "BC-DB-ORA", "RefTitle": "Support for Oracle functions in the SAP environment", "RefUrl": "/notes/105047 "}, {"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094 "}, {"RefNumber": "1431800", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Central Technical Note", "RefUrl": "/notes/1431800 "}, {"RefNumber": "1324684", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Creating encrypted RMAN backups using BR*Tools", "RefUrl": "/notes/1324684 "}, {"RefNumber": "1586304", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP System Copies with Oracle Transparent Data Encryption", "RefUrl": "/notes/1586304 "}, {"RefNumber": "1109743", "RefComponent": "BC-DB-ORA", "RefTitle": "Use of Index Key (Prefix) Compression for Oracle Databases", "RefUrl": "/notes/1109743 "}, {"RefNumber": "1464091", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (3)", "RefUrl": "/notes/1464091 "}, {"RefNumber": "1426979", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g: SecureFiles - The new way to store LOB data", "RefUrl": "/notes/1426979 "}, {"RefNumber": "1269911", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Chained Rows", "RefUrl": "/notes/1269911 "}, {"RefNumber": "1279682", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for Oracle data encryption in BR*Tools", "RefUrl": "/notes/1279682 "}, {"RefNumber": "1324930", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Creating encrypted EXPDP exports with BRSPACE", "RefUrl": "/notes/1324930 "}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268 "}, {"RefNumber": "973450", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database network encryption and data integrity", "RefUrl": "/notes/973450 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "701", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 2, "URL": "/corrins/0000974876/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}