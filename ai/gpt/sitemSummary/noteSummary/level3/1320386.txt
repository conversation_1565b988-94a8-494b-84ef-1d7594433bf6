SAP Note 1320386 addresses frequently asked questions concerning the BW Trade Foundation and related trade service classes. It is structured to provide information on the objects created within the BW Trade Foundation, specific functionalities, key figure service classes, integration with retail cubes, and data modeling adherence to the Layered Scalable Architecture (LSA) concept.

Key points from this SAP Note:

1. Objects Delivered: The Trade Business Content includes objects for areas such as Goods Movements, Stock Revaluation, Retail Revaluation, Retail Sales, and POS Markdown DSO. These are connected to respective DataSources.

2. Late Sales and Customer Returns: A connection between sales data and stock data to revaluation DataStore objects (0RT_DS53 and 0RT_DS63) addresses "Late Sales" and "Customer Returns" scenarios. Transformations can generate markups/markdowns to balance these instances. If not required, certain transformations can be removed.

3. Article Explosion: Structured articles are quantitatively split via a BAdI. This increases the number of data records, and precautions must be taken to prevent duplicates. If article explosion is not desired, the corresponding BAdI should be inactivated, and transformations adjusted.

4. Trade Foundation Key Figure Service Classes: Separate service classes exist for applications like sales, purchasing, inventory management, and revaluation at retail, to calculate corresponding key figures. These classes encapsulate update logics and are utilized in transformations to calculate key figures.

5. Integration with Retail Cubes: New transformations must be set up to connect the Trade Foundation DataStore objects with retail cubes. This enables the use of pre-prepared logic in Trade Foundation service classes for key figure updates.

6. Corporate Memory Considerations: The delivered objects should not be seen as Corporate Memory in the EDW model as they do not align with LSA concept characteristics such as saving all source fields without aggregation. However, a custom Corporate Memory Layer can be introduced in the customer namespace, outside of time-critical loading processes. This can offer flexibility for long-term granular data storage and meet varying analysis requirements.

Overall, SAP Note 1320386 provides clarification on the function and integration of the BW Trade Foundation elements and guidance on how to tailor them according to specific business needs and data warehousing principles.