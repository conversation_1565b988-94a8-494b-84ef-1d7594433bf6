{"Request": {"Number": "1731206", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 854, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010286262017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001731206?language=E&token=C53943401D52309F9D198A5F1FADD74B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001731206", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001731206/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1731206"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.07.2012"}, "SAPComponentKey": {"_label": "Component", "value": "PY-FR"}, "SAPComponentKeyText": {"_label": "Component", "value": "France"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "France", "value": "PY-FR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-FR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1731206 - DNAC-AED: AED statement viewer"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In order to check the content of an AED declaration, only the report RPUTMS9S (Temse Viewer) was available to the payroll administrator.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>N4DS, DNAC, AED</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note introduces a new report to display the AED content in a more user friendly way, to ease the check of the declaration that is going to be sent to the administration.<br /><br />This new report is called \"RPLAEDF0\", and can be also reached by using the transaction \"PC00_M06_AED\".<br /><br />The parameters, requested so the report can be launched, are:<br />-<B></B><B><B>temse file number</B></B><B>: </B>this is the temse file in which the AED declaration(s) you want to display/print/download are stored. This parameter is a mandatory one.<br />-<B></B><B><B>declaration number</B></B><B>: </B>this is the number of the declaration(s) you want to display/print/download are stored. This parameter is an optional one.<br /><br />Three options are then available:<br />-<B><B> Display .pdf file: </B></B>this option enables to make a direct display of the AED declaration in Adobe Reader. For this option, a declaration number (only one) is mandatory.<br />-<B><B> Print: </B></B>this option enables to print one or more AED declaration(s). To use this option, please ensure that the user that is using it has in his user parameter (tabstrip \"Defaults\", Section \"Spool Control\", field \"Output device\") a printer entered (this printer must be suitable for ADS document).<br />-<B> </B><B><B>Desktop download</B></B><B>: </B>this option enables to download AED .pdf file(s) in a given folder of the user's desktop. For this option the target folder must be written in the \"Target directory\" field.<br /><br />For all these options, an <B>authority check </B>will be done to check if the user has the right to get the AED declaration; in standard no implementation is being done, you will have to created your own check based on:<br />- For ECC6.04 and ECC6.0: the method \"AED_AUTHORITY_CHECK\" related to the BAdI \"HRPAYFR_N4DS\" (available within the enhancement spot \"HRPAYFR_N4DS\").<br />- For the other releases: the method \"AED_AUTHORITY_CHECK\" related to the BAdI \"HRPAYFR_N4DS_CUST\"</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The correction enclosed within this note will be included in HR Support Packages referenced below. If you need the correction before the HRSP is available, you may apply the manual and automatic correction instructions.<br /><br /><B><B>Install the following files</B></B>. Install first the file related to the workbench DDIC object, then the one related to the customizing entries corresponding to your release (the files mentioned below are already properly ordered).<br /><br /><B><B>ECC 6.04 (Enhancement Pack 4)</B></B>: R133420.L4H, K133420.L4H, R133421.L4H, K133421.L4H (Attachment SAPCAR_604.zip)<br /><br /><B><B>ECC 6.0</B></B>: R203678.L7D, K203678.L7D, K203679.L7D,<br />R203679.L7D (Attachment SAPCAR_600.zip)<br /><br /><B><B>ECC 5.0</B></B>: R178447.L6D, K178447.L6D, K178448.L6D,<br />R178448.L6D (Attachment SAPCAR_500.zip)<br /><br /><B><B>4.7</B></B>: R245324.L6B, K245324.L6B, K245325.L6B,<br />R245325.L6B (Attachment SAPCAR_500.zip)<br /><br /><B>How to proceed for reporting errors to SAP:</B><br />Create a customer message with a title starting with '[AED stat.]'.<br /><br /><B>Risk and Restrictions inherent in Transport Files</B><br />If you use a Transport (SAR) file please note the following:<br />1) Read carefully SAP Note 1318389, where conditions and risks of using<br />Transport files are explained in detail.<br />2) There are no updates to Transport files when any object in them are<br />modified. Objects contained in Transport files may become obsolete<br />without warning.<br />3) Transport files are not valid once their content is available via<br />Support Packages or CLC Packages. The changes may then be installed only<br />via the Packages.<br />4) Text objects are provided in the language in which they were created.<br />Translation is available only via the Support Packages.<br />5) Changes to the SAP Easy Access menu and Implementation Guide (IMG)<br />are provided only via the Packages.<br /><br /><B>Caution: Handling Transport Files</B><br />When you apply more than one Transport file, the order of implementation must be followed as indicated. A wrong sequence will cause transports to fail. In the case of this note, the order is not relevant since there is only one workbench transport and one customizing transport, and since the tables for the customizing entries are already existing in your system.<br />Once a Transport file has been installed, future installations of<br />Support Packages (or CLC Packages for the HR components modified by the<br />Transport file) must include the Packages that delivered the changes<br />contained in the Transport file. Otherwise objects may be replaced by<br />older versions.<br /><br />It should be emphasized however,that correction instructions are only a short-term solution, not a substitute for the Support Package.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D057886)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D054247)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731206/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731206/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731206/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731206/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731206/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731206/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731206/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731206/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001731206/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "RPLAEDF0_Draft.pdf", "FileSize": "699", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332872012&iv_version=0005&iv_guid=90F603EF73765143ABA83D5A6C6187B4"}, {"FileName": "sfstyle-hr_fr_dnac_aed_statement.zip", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332872012&iv_version=0005&iv_guid=9C0FC02280C4674EA787E05594FD84E2"}, {"FileName": "hr_fr_dnac_aed_statement_470.zip", "FileSize": "15", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332872012&iv_version=0005&iv_guid=3414CC9F6E0C0F44968CCA0D2378CD7F"}, {"FileName": "SAPCAR_600.zip", "FileSize": "33", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332872012&iv_version=0005&iv_guid=3E89A75CDFBAE94E9A0870FAD5865638"}, {"FileName": "SAPCAR_470.zip", "FileSize": "28", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332872012&iv_version=0005&iv_guid=483F2654C7155D4E96BCB99FF5FAAF7C"}, {"FileName": "SAPCAR_500.zip", "FileSize": "29", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332872012&iv_version=0005&iv_guid=42A08CBF7B0EFA44B737F9DDEAE80FAF"}, {"FileName": "SAPCAR_604.zip", "FileSize": "53", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332872012&iv_version=0005&iv_guid=B7A8CCFCD27F464F8770E3EB687CB762"}, {"FileName": "SFPF_HR_FR_DNAC_AED_STATEMENT.zip", "FileSize": "42", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000332872012&iv_version=0005&iv_guid=AF2CE78B87C3BA40A0A51CB5D16FBCEB"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1731593", "RefComponent": "PY-FR", "RefTitle": "DN-AC AED notes to be installed/re-installed after CLC/HR-SP", "RefUrl": "/notes/1731593"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1318389", "RefComponent": "BC-CTS", "RefTitle": "How to Use .SAR/.CAR files and Risks Involved", "RefUrl": "/notes/1318389"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1731593", "RefComponent": "PY-FR", "RefTitle": "DN-AC AED notes to be installed/re-installed after CLC/HR-SP", "RefUrl": "/notes/1731593 "}, {"RefNumber": "1318389", "RefComponent": "BC-CTS", "RefTitle": "How to Use .SAR/.CAR files and Risks Involved", "RefUrl": "/notes/1318389 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCFR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCFR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCFR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCFR", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CJ1", "URL": "/supportpackage/SAPKE46CJ1"}, {"SoftwareComponentVersion": "SAP_HRCFR 470", "SupportPackage": "SAPK-470D6INSAPHRCFR", "URL": "/supportpackage/SAPK-470D6INSAPHRCFR"}, {"SoftwareComponentVersion": "SAP_HRCFR 500", "SupportPackage": "SAPK-500A2INSAPHRCFR", "URL": "/supportpackage/SAPK-500A2INSAPHRCFR"}, {"SoftwareComponentVersion": "SAP_HRCFR 600", "SupportPackage": "SAPK-60085INSAPHRCFR", "URL": "/supportpackage/SAPK-60085INSAPHRCFR"}, {"SoftwareComponentVersion": "SAP_HRCFR 604", "SupportPackage": "SAPK-60451INSAPHRCFR", "URL": "/supportpackage/SAPK-60451INSAPHRCFR"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HRCFR", "NumberOfCorrin": 4, "URL": "/corrins/0001731206/6484"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_HRCFR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 470&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-470D3INSAPHRCFR - SAPK-470D5INSAPHRCFR&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 500&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-50099INSAPHRCFR - SAPK-500A1INSAPHRCFR&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60082INSAPHRCFR - SAPK-60084INSAPHRCFR&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60448INSAPHRCFR - SAPK-60450INSAPHRCFR&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/><B>Adobe form import (releases ECC5.0, 6.0 and 6.04)</B></P> <OL>1. Interface creation</OL> <OL><OL>a) Login in your dev. system with the language \"FR\" enabled</OL></OL> <OL><OL>b) Launch the \"SFP\" transaction</OL></OL> <OL><OL>c) Press the radio button \"Interface\" and enter  \"HR_FR_DNAC_AED_STATEMENT\" in the text field next to it, then press the \"Créer\" button</OL></OL> <OL><OL>d) Enter \"Interface pour état de contrôle DNAC-AED\" in the description  field, choose \"PC06\" as a package and \"Interface basée sur le dictionnaire ABAP\" as interface type; save your data</OL></OL> <OL><OL>e) From the \"Interface\" tabstrip, click on the \"Importation\" item  linked to the \"Interface de formulaire\" tabstrip on the left of the screen</OL></OL> <OL><OL>f) Add new import parameter \"IS_AED_STATEMENT\" with \"TYPE\" value as  \"Typage\" and \"P06AED_S_CHECK_STATEMENT\" as \"Désignation de type\"</OL></OL> <OL><OL>g) Save and activate the interface</OL></OL> <OL>2. Form creation</OL> <OL><OL>a) Unzip the file \"SFPF_HR_FR_DNAC_AED_STATEMENT.zip\" attached to this note; you should then have an .xml file named  \"SFPF_HR_FR_DNAC_AED_STATEMENT.xml\"</OL></OL> <OL><OL>b) Launch the \"SFP\" transaction again</OL></OL> <OL><OL>c) Press the radio button \"Formulaire\" and enter  \"HR_FR_DNAC_AED_STATEMENT\" in the text field next to it, then press the \"Créer\" button</OL></OL> <OL><OL>d) Set \"Formulaire Etat de Contrôle DNAC-AED\" as the form description,  then \"HR_FR_DNAC_AED_STATEMENT\" as the related interface and \"PC06\" as a package; save your data</OL></OL> <OL><OL>e) Then in the menu bar, click on the \"Utilitaires-&gt;Télécharger  formulaire\" entry and upload the \"SFPF_HR_FR_DNAC_AED_STATEMENT.xml\"  file (available within the .zip \"SFPF_HR_FR_DNAC_AED_STATEMENT.zip\"  file attached to this note)</OL></OL> <OL><OL>f) Save the form and activate it</OL></OL> <P><br/><br/><B>Smartform import (release 4.7 only)</B></P> <OL>1. Login in your dev. system with the language \"FR\" enabled</OL> <OL><OL>a) Launch the \"SMARTFORMS\" transaction</OL></OL> <OL><OL>b) Press the radio button \"Feuille de style\" and enter  \"HR_FR_DNAC_AED_STATEMENT\" in the text field next to it, then press the \"Créer\" button</OL></OL> <OL><OL>c) Enter \"F.style AED\" in the description field, choose \"PC06\" as a package; save your data</OL></OL> <OL><OL>d) Then in the menu bar, click on the \"Utilitaires-&gt;Télécharger\" entry  and upload the and upload the \"sfstyle-hr_fr_dnac_aed_statement.xml\"  file (available within the .zip \"sfstyle-hr_fr_dnac_aed_statement.zip\" file attached to this note)</OL></OL> <OL><OL>e) Save and activate the stylesheet</OL></OL> <OL><OL>f) Launch the \"SMARTFORMS\" transaction again</OL></OL> <OL><OL>g) Press the radio button \"Formulaire\" and enter  \"HR_FR_DNAC_AED_STATEMENT\" in he text field next to it, then press the \"Créer\" button</OL></OL> <OL><OL>h) Set \"Formulaire Etat de Contrôle DNAC-AED\" as the form description and \"PC06\" as a package; save your data</OL></OL> <OL><OL>i) Then in the menu bar, click on the \"Utilitaires-&gt;Télécharger\" entry  and upload the \"hr_fr_dnac_aed_statement_470.xml\" file (available within  the .zip \"hr_fr_dnac_aed_statement_470.zip\"&nbsp;&nbsp;file attached to this note)</OL></OL> <OL><OL>j) Save the smartform and activate it</OL></OL> <P><br/><br/><B>Message class creation (all releases)</B></P> <OL>1. Login in your dev. system with the language \"FR\" enabled</OL> <OL>2. Launch the SE91 transaction and enter \"HRPAYFR_N4DS_REPORT\" in the  \"Classe de messages\" field then press the create button</OL> <OL>3. Enter \"Classe de messages pour report N4DS\" in the \"Désignation\" field and choose PC06 as a package; save your entry</OL> <OL>4. Enter following messages</OL> <OL><OL>a) Message \"000\" with the description \"&amp;1 l'affichage n'est possible que si un numéro d'ordre est sélectionné\"</OL></OL> <OL><OL>b) Message \"001\" with the description \"&amp;1 sauvegarde sur le poste de travail impossible, répertoire manquant\"</OL></OL> <OL><OL>c) Message \"002\" with the description \"&amp;1 seule l'impression est possible en mode arrière-plan\"</OL></OL> <OL><OL>d) Message \"003\" with the description \"Employé &amp;1 : fichier &amp;2 téléchargé dans le répertoire &amp;3\"</OL></OL> <OL><OL>e) Message \"004\" with the description \"Affichage impossible, une erreur technique est survenue\"</OL></OL> <OL><OL>f) Message \"005\" with the description \"Traitement impossible, aucune donnée AED n'est disponible\"</OL></OL> <OL><OL>g) Message \"006\" with the description \"Employé &amp;1 : fichier &amp;2 traité en arrière-plan\"</OL></OL> <OL><OL>h) Message \"007\" with the description \"Employé &amp;1, déclaration &amp;2 :  traitement impossible, pas d'autorisation\"</OL></OL> <OL><OL>i) Message \"008\" with the description \"Traitement impossible, déclaration &amp;1 inconnue dans fichier temse &amp;2\"</OL></OL> <OL><OL>j) Message \"009\" with the description \"Traitement impossible, le fichier &amp;1 n'existe pas ou n'a pu être lu\"</OL></OL> <P><B>Report RPLAEDF0 text elements (all releases)</B></P> <OL>1. Login in your dev. system with the language \"FR\" enabled</OL> <OL>2. Launch the SE38 transaction, enter \"RPLAEDF0\", select the \"Eléments  de texte\" radio button then press the \"Modifier\" button</OL> <OL>3. Enter following text elements in the \"Symboles de texte\" tabstrip</OL> <OL><OL>a) Symbol \"000\" with the text \"Liste de messages\"</OL></OL> <OL><OL>b) Symbol \"001\" with the text \"Sélection des données\"</OL></OL> <OL><OL>c) Symbol \"002\" with the text \"Choix du mode de rendu\"</OL></OL> <OL><OL>d) Symbol \"003\" with the text \"Compte-rendu d'édition des fichiers AED\"</OL></OL> <OL><OL>e) Symbol \"004\" with the text \"Traitement interrompu :\"</OL></OL> <OL>4. Enter following text elements in the \"Textes de sélection\" tabstrip</OL> <OL><OL>a) Nom \"P_DEORD\" with the text \"N° ordre de la déclaration\"</OL></OL> <OL><OL>b) Nom \"P_DIR\" with the text \"Répertoire cible\"</OL></OL> <OL><OL>c) Nom \"P_PDF\" with the text \"Affichage fichier .pdf\"</OL></OL> <OL><OL>d) Nom \"P_PRINT\" with the text \"Impression\"</OL></OL> <OL><OL>e) Nom \"SAVE\" with the text \"Sauvegarde poste de travail\"</OL></OL> <OL><OL>f) Nom \"P_TEMSE\" with the text \"Fichier temse\"</OL></OL> <OL>5. Launch again the SE38 transaction, select the \"Propriétés\" radio  button then press the \"Modifier\" button; in the \"Titre\" field, enter \"Prévisualisation de déclarations AED\"</OL> <OL>6. Save and activate your changes</OL> <P><br/><B>Transaction creation (all releases)</B><br/>Login in your dev. system with the language \"FR\" enabled</P> <OL>1. Launch the SE93 transaction, enter \"PC00_M06_AED\" in the \"Code transaction\" field then press the \"Créer\" button</OL> <OL>2. Enter \"Prévisualisation de déclarations AED\" in the designation  field, select the \"Programme et écran de sélection (transaction de programme)\" radio button</OL> <OL>3. In the next screen, enter \"RPLAEDF0\" in the programm field, then mark the \"SAP Gui pour windows\" checkbox</OL> <OL>4. Save the transaction and linked it to the \"PC06\" package</OL> <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_HRCFR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 470&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-470D3INSAPHRCFR - SAPK-470D5INSAPHRCFR&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 500&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-50099INSAPHRCFR - SAPK-500A1INSAPHRCFR&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60082INSAPHRCFR - SAPK-60084INSAPHRCFR&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60448INSAPHRCFR - SAPK-60450INSAPHRCFR&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>As stated in the note, please install the pre-requisite files according to your ECC release.<br/><br/><U>Note</U>: these files will enable you to get all new DDIC objects as  well as the customizing entries requested to run the new report.<br/><br/>Following additional step will have to be performed if your HR-SP level is lower than:<br/>- HR-SP50 for the release ECC6.04<br/>- HR-SP84 for the release ECC6.00<br/>- HR-SPA2 for the ECC5.00 release<br/>- HR-SPD5 for the 4.7 release</P> <OL>1. Structure P06_DECL modification</OL> <OL><OL>a) Login in your dev. system with the language \"FR\" enabled</OL></OL> <OL><OL>b) Launch the SE11 transaction and enter \"P06_DECL\" in the \"Type de données\" field, then press the \"Modifier\" button</OL></OL> <OL><OL>c) Add in the end of the structure (after the field \"DEPER\") a new  field named \"DEPNR\" with the component type \"PERNR_D\".</OL></OL> <OL><OL>d) Save and activate this change.</OL></OL> <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_HRCFR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 470&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-470D3INSAPHRCFR - SAPK-470D5INSAPHRCFR&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 500&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-50099INSAPHRCFR - SAPK-500A1INSAPHRCFR&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/><B>Report RPLAEDF0 creation (releases ECC5.0 and 4.7 only)</B></P> <OL>1. Login in your dev. system with the language \"FR\" enabled</OL> <OL>2. Launch the SE38 transaction and enter \"RPLAEDF0\" in the \"Programme\" field then press the \"Créer\" button</OL> <OL>3. In the \"Propriétés\" popup window, enter \"Prévisualisation de  déclarations AED\" in the \"Titre\" field, choose \"Programme exécutable\" in  the \"Type\" listbox and \"PC06\" as a package then click on the \"sauvegarder\" button</OL> <OL>4. Create afterwards within this report following includes:</OL> <OL><OL>a) RPLAEDF0_DATA</OL></OL> <OL><OL>b) RPLAEDF0_SEL</OL></OL> <OL><OL>c) RPLAEDF0_FORM</OL></OL> <OL>5. Save and activate those changes</OL> <P><br/><br/><B>Interface IF_HRPAYFR_AED_VIEWER_MGR creation (releases ECC5.0 and 4.7 only)</B></P> <OL>1. <B>Launch the SE24 transaction and enter \"</B>IF_HRPAYFR_AED_VIEWER_MGR\" in the \"Type d'objet\" field then press the \"Créer\" button</OL> <OL>2. In the popup window, enter \"Interface class for AED viewer\" as a description, and \"PC06\" as a package</OL> <OL>3. Click then on the \"Méthodes\" tabstrip</OL> <OL>4. Create a first entry with \"SET_AED_STATEMENT\" in the \"Méthode\"  field, \"Instance Method\" in the \"Type\" field, \"Public\" as \"Visibility\" and \"Set AED statement\" as a description\"</OL> <OL>5. Press then the \"Paramètre\" button and add following parameters:</OL> <OL><OL>a) \"IV_DEORD\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"P06_DEORD\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"IT_DEORD\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"STANDARD TABLE\" as \"Type réf.\"</OL></OL> <OL><OL>c) \"IV_SAVE\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"XFELD\" as \"Type réf.\"</OL></OL> <OL><OL>d) \"IV_DISPLAY\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"XFELD\" as \"Type réf.\"</OL></OL> <OL><OL>e) \"IV_DIRECTORY\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"ANY\" as \"Type réf.\"</OL></OL> <OL><OL>f) \"IV_PRINT\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"XFELD\" as \"Type réf.\"</OL></OL> <OL><OL>g) \"ET_MSG\" as Paramètre, \"Exporting\" as Catégorie, \"Type\" as \"Categ. typage\", \"HRPADFR_SYMSG\" as \"Type réf.\"</OL></OL> <OL>6. Press then the \"Méthodes\" button and add a second entry with  \"PRINT_AED_STATEMENT\" in the \"Méthode\" field, \"Instance Method\" in the  \"Type\" field, \"Public\" as \"Visibility\" and \"Print AED statement\" as a description\"</OL> <OL>7. Press then the \"Paramètre\" button and add following parameters:</OL> <OL><OL>a) Version ECC5.0: \"IV_FORMNAME\" as Paramètre, \"Importing\" as  Catégorie, \"Type\" as \"Categ. typage\", \"FPWBFORMNAME\" as \"Type réf.\", \"'HR_FR_DNAC_AED_STATEMENT'\" as \"Valeur par défaut\"</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Version 4.7: \"IV_FORMNAME\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"TDSFNAME\" as \"Type réf.\", \"'HR_FR_DNAC_AED_STATEMENT'\" as \"Valeur par défaut\" <OL><OL>a) \"IV_LANGU\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ.  typage\", \"SY-LANGU\" as \"Type réf.\", \"SY-LANGU\" as \"Valeur par défaut\"</OL></OL> <OL><OL>b) Version ECC5.0: \"IS_OUTPUTPARAMS\" as Paramètre, \"Importing\" as  Catégorie, check the \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"SFPOUTPUTPARAMS\" as \"Type réf.\"</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Version 4.7: \"IS_OUTPUTPARAMS\" as Paramètre, \"Importing\" as Catégorie,  check the \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"SSFCOMPOP\" as \"Type réf.\" <OL><OL>a) \"IV_DISPLAY\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"XFELD\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"IV_DIRECTORY\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"ANY\" as \"Type réf.\"</OL></OL> <OL><OL>c) \"IV_DEORD\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"P06_DEORD\" as \"Type réf.\"</OL></OL> <OL><OL>d) \"IV_PRINT\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"XFELD\" as \"Type réf.\"</OL></OL> <OL><OL>e) \"CT_MSG\" as Paramètre, \"Changing\" as Catégorie, \"Type\" as \"Categ. typage\", \"HRPADFR_SYMSG\" as \"Type réf.\"</OL></OL> <OL>1. Save and activate those changes</OL> <P><br/><B>Class CL_HRPAYFR_AED_VIEWER_MGR creation (releases ECC5.0 and 4.7 only)</B></P> <OL>1. Launch the SE24 transaction and enter <B>\"</B>CL_HRPAYFR_AED_VIEWER_MGR\" in the \"Type d'objet\" field then press the \"Créer\" button</OL> <OL>2. In the popup window, enter \"Manager class for AED viewer\" as a description, and \"PC06\" as a package</OL> <OL>3. Click then on the \"Interfaces\" tabstrip and enter \"IF_HRPAYFR_AED_VIEWER_MGR\" in the \"Interface\" field</OL> <OL>4. Click on the \"Propriétés\" tabstrip and add \"PFRDS\" in the \"Groupe type/type object\" field</OL> <OL>5. Click afterwards on the \"Attributs\" tabstrip and add following attributes (in the text fields from left to right):</OL> <OL><OL>a) AV_TEMSE_NAME, Instance Attribute, Protected , Type, RPTSTYPE-TSOBJ</OL></OL> <OL><OL>b) AO_N4DS_CONTROL, Instance Attribute, Protected, Type Ref To, CL_HRPAYFR_N4DS_CONTROL</OL></OL> <OL><OL>c) AO_N4DS_RENDERER, Instance Attribute, Protected , Type Ref To, CL_HRPAYFR_N4DS_RENDERER</OL></OL> <OL><OL>d) AV_XML_XSTRING, Instance Attribute, Protected, Type, XSTRING</OL></OL> <OL><OL>e) AV_FILEPATH, Instance Attribute, Protected, Type, STRING</OL></OL> <OL><OL>f) AV_PARAMETER, Instance Attribute, Protected, Type, STRING</OL></OL> <OL><OL>g) AV_DIRECTORY, Instance Attribute, Protected, Type, STRING</OL></OL> <OL><OL>h) AS_AED_STATEMENT, Instance Attribute, Protected , Type, P06AED_S_CHECK_STATEMENT</OL></OL> <OL><OL>i) AV_APPL, Instance Attribute, Protected, Type, T5F99F0-APPL</OL></OL> <OL><OL>j) AV_DATE, Instance Attribute, Protected, Type, DATUM</OL></OL> <OL><OL>k) AV_MOLGA, Instance Attribute, Protected, Type, MOLGA</OL></OL> <OL><OL>l) AT_T5F99FCDT, Instance Attribute, Protected, Type, HR99S_TAB_T5F99FCDT</OL></OL> <OL><OL>m) AV_SPRSL, Instance Attribute, Protected, Type, SPRAS</OL></OL> <OL><OL>n) AB_N4DS_BADI, Instance Attribute, Protected, Type Ref To, HRPAYFR_N4DS_CUST</OL></OL> <OL>6. Click afterwards on the \"Méthodes\" tabstrip</OL> <OL>7. Create a first entry with \"CONSTRUCTOR\" in the \"Méthode\" field,  \"Instance Method\" in the \"Type\" field, \"Public\" as \"Visibility\" and \"Set AED statement\" as a description\"</OL> <OL>8. Press then the \"Paramètre\" button and add following parameters:</OL> <OL><OL>a) \"IV_TEMSE_NAME\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"RPTSTYPE-TSOBJ\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"IO_TEMSE\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type Ref To\" as \"Categ. typage\", \"CL_HR99S00_TEMSE \" as \"Type réf.\"</OL></OL> <OL><OL>c) \"IV_APPL\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"T5F99F0-APPL\" as \"Type réf.\", \"SY-LANGU\" as \"Valeur par défaut\"</OL></OL> <OL><OL>d) \"IV_DATE\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"DATUM\" as \"Type réf.\", \"SY-DATUM\" as \"Valeur par défaut\"</OL></OL> <OL><OL>e) \"IV_MOLGA\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"MOLGA\" as \"Type réf.\", \"'06'\" as \"Valeur par défaut\"</OL></OL> <OL><OL>f) \"IV_SPRSL\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"SPRAS\" as \"Type réf.\", \"SY-LANGU\" as \"Valeur par défaut\"</OL></OL> <OL><OL>g) Press then the \"Méthodes\" button and add a second entry with  \"PRINT_AED_STATEMENT\" in the \"Méthode\" field, \"Instance Method\" in the  \"Type\" field, \"Public\" as \"Visibility\" and \"Print AED statement\" as a description\"</OL></OL> <OL>9. Press then the \"Méthodes\" button and add a second entry with  \"PRELIMINARY_CHECK\" in the \"Méthode\" field, \"Instance Method\" in the  \"Type\" field, \"Protected\" as \"Visibility\" and \"Contrôles préliminaires\" as a description\"</OL> <OL>10. Press then the \"Paramètre\" button and add following parameters:</OL> <OL><OL>a) \"IT_DEORD\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"STANDARD TABLE\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"EV_STOP_PROCESS\" as Paramètre, \"Exporting\" as Catégorie, \"XFELD\" as \"Categ. typage\"</OL></OL> <OL><OL>c) \"CT_MSG\" as Paramètre, \"Changing\" as Catégorie, \"Type\" as \"Categ. typage\", \"HRPADFR_SYMSG\" as \"Type réf.\"</OL></OL> <OL>11. Press then the \"Méthodes\" button and add a second entry with  \"AUTHORITY_CHECK\" in the \"Méthode\" field, \"Instance Method\" in the  \"Type\" field, \"Protected\" as \"Visibility\" and \"Contrôle d'autorisation\" as a description</OL> <OL>12. Press then the \"Paramètre\" button and add following parameters:</OL> <OL><OL>a) \"IS_DECLARATION_DATA\" as Paramètre, \"Importing\" as Catégorie, \"Type\"  as \"Categ. typage\", \"P06_T_DECL\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"CV_CHECK_OK\" as Paramètre, \"Changing\" as Catégorie, \"XFELD\" as \"Categ. typage\"</OL></OL> <OL><OL>c) \"CT_MSG\" as Paramètre, \"Changing\" as Catégorie, \"Type\" as \"Categ. typage\", \"HRPADFR_SYMSG\" as \"Type réf.\"</OL></OL> <OL>13. Press then the \"Méthodes\" button and add a new entry with  \"SET_EMPLOYER_DEC_DATA\" in the \"Méthode\" field, \"Instance Method\" in the  \"Type\" field, \"Protected\" as \"Visibility\" and \"Set employer/declaration data\" as a description\"</OL> <OL>14. Press then the \"Paramètre\" button and add following parameter:</OL> <OL><OL>a) \"IS_DECLARATION\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as  \"Categ. typage\", \"PFRDS_RENDER_DECLARATION\" as \"Type réf.\"</OL></OL> <OL>15. Press then the \"Méthodes\" button and add a new entry with  \"SET_ETAB_DATA\" in the \"Méthode\" field, \"Instance Method\" in the \"Type\"  field, \"Protected\" as \"Visibility\" and \"Set \"établissement\" data\" as a description</OL> <OL>16. Press then the \"Paramètre\" button and add following parameters:</OL> <OL><OL>a) \"IT_ETABLISSEMENT\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as  \"Categ. typage\", \"PFRDS_T_RENDER_ETABLISSMENT\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"IV_CHECK_DATA\" as Paramètre, \"Importing\" as Catégorie, \"ANY\" as \"Categ. typage\"</OL></OL> <OL>17. Press then the \"Méthodes\" button and add a new entry with  \"SET_EMPLOYEE_DATA\" in the \"Méthode\" field, \"Instance Method\" in the  \"Type\" field, \"Protected\" as \"Visibility\" and \"Set employee data\" as a description\"</OL> <OL>18. Press then the \"Paramètre\" button and add following parameter:</OL> <OL><OL>a) \"IT_EMPLOYEE\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as  \"Categ. typage\", \"PFRDS_T_RENDER_SALARIE\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"IV_SIMAT\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"PERNR_D\" as \"Type réf.\"</OL></OL> <OL>19. Press then the \"Méthodes\" button and add a new entry with  \"SET_NOTICE_PERIOD_DATA\" in the \"Méthode\" field, \"Instance Method\" in  the \"Type\" field, \"Protected\" as \"Visibility\" and \"Set \"préavis\" data\" as a description</OL> <OL>20. Press then the \"Paramètre\" button and add following parameters:</OL> <OL><OL>a) \"IT_SITUATION\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as  \"Categ. typage\", \"PFRDS_T_RENDER_SITUATION\" as \"Type réf.\"</OL></OL> <OL>21. Press then the \"Méthodes\" button and add a new entry with  \"SET_SALARY_DATA\" in the \"Méthode\" field, \"Instance Method\" in the  \"Type\" field, \"Protected\" as \"Visibility\" and \"Set salary data\" as a description\"</OL> <OL>22. Press then the \"Paramètre\" button and add following parameter:</OL> <OL><OL>a) \"IT_SITUATION\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as  \"Categ. typage\", \"PFRDS_T_RENDER_SITUATION\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"IV_SIMAT\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"PERNR_D\" as \"Type réf.\"</OL></OL> <OL><OL>c) \"IT_EMPLOYEE\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as  \"Categ. typage\", \"PFRDS_T_RENDER_SALARIE\" as \"Type réf.\"</OL></OL> <OL>23. Press then the \"Méthodes\" button and add a new entry with  \"SET_DESCRIPTION\" in the \"Méthode\" field, \"Instance Method\" in the  \"Type\" field, \"Protected\" as \"Visibility\" and \"Set description related to a given field\" as a description</OL> <OL>24. Press then the \"Paramètre\" button and add following parameters:</OL> <OL><OL>a) \"IV_SECTN\" as Paramètre, \"Importing\" as Catégorie, check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"P_99S_SECTN\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"IV_FIELD\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"P_99S_FIELD\" as \"Type réf.\"</OL></OL> <OL><OL>c) \"IV_SECTN\" as Paramètre, \"Importing\" as Catégorie,&nbsp;&nbsp;check the \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"P_99S_SECTN\" as \"Type réf.\"</OL></OL> <OL><OL>d) \"IV_VALUE\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"ANY\" as \"Type réf.\"</OL></OL> <OL><OL>e) \"IV_CDTYP\" as Paramètre, \"Importing\" as Catégorie,&nbsp;&nbsp;check the  \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"P_99S_CODE_TYPE\" as \"Type réf.\", \"'01'\" as \"Valeur par défaut\"</OL></OL> <OL><OL>f) \"IV_SET_CODE_AND_DESC\" as Paramètre, \"Importing\" as Catégorie, check  the \"Facultatif\" checkbox, \"Type\" as \"Categ. typage\", \"XFELD\" as \"Type réf.\", \"ABAP_TRUE\" as \"Valeur par défaut\"</OL></OL> <OL><OL>g) \"EV_TEXT\" as Paramètre, \"Exporting\" as Catégorie, \"Type\" as \"Categ. typage\", \"TEXT100\" as \"Type réf.\"</OL></OL> <OL>25. Save and activate those changes</OL> <P><br/><br/><B>Class CL_HR_T5F99FCDT creation (releases ECC5.0 and 4.7 only)</B></P> <OL>1. Launch the SE24 transaction and enter \"CL_HR_T5F99FCDT\" in the \"Type d'objet\" field then press the \"Créer\" button</OL> <OL>2. In the popup window, enter \"T5F99FCDT - Reading class\" as a description, and \"PC06\" as a package</OL> <OL>3. Click afterwards on the \"Méthodes\" tabstrip</OL> <OL>4. Create a first entry with \"GET_TEXT_BY_KEY\" in the \"Méthode\" field,  \"Static Method\" in the \"Type\" field, \"Public\" as \"Visibility\" and \"Get Code Value Text\" as a description</OL> <OL>5. Press then the \"Paramètre\" button and add following parameters:</OL> <OL><OL>a) \"IV_DATE\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"DATUM\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"IV_MOLGA\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"MOLGA\" as \"Type réf.\"</OL></OL> <OL><OL>c) \"IV_APPL\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"P_APPL\" as \"Type réf.\"</OL></OL> <OL><OL>d) \"IV_SECTN\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"P_99S_SECTN\" as \"Type réf.\"</OL></OL> <OL><OL>e) \"IV_FIELD\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"P_99S_FIELD\" as \"Type réf.\"</OL></OL> <OL><OL>f) \"IV_CDTYP\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"P_99S_CODE_TYPE\" as \"Type réf.\"</OL></OL> <OL><OL>g) \"IV_CDVAL\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"P_99S_CODE_VALUE\" as \"Type réf.\"</OL></OL> <OL><OL>h) \"EV_STEXT\" as Paramètre, \"Exporting\" as Catégorie, \"Type\" as \"Categ. typage\", \"TEXT100\" as \"Type réf.\"</OL></OL> <OL><OL>i) \"EV_LTEXT\" as Paramètre, \"Exporting\" as Catégorie, \"Type\" as \"Categ. typage\", \"TEXT200\" as \"Type réf.\"</OL></OL> <OL><OL>j) \"ES_T5F99FCDT\" as Paramètre, \"Exporting\" as Catégorie, \"Type\" as \"Categ. typage\", \"T5F99FCDT\" as \"Type réf.\"</OL></OL> <OL><OL>k) \"ET_T5F99FCDT \" as Paramètre, \"Exporting\" as Catégorie, \"Type\" as  \"Categ. typage\", \"HR99S_TAB_T5F99FCDT\" as \"Type réf.\"</OL></OL> <OL>6. Press then the \"Méthodes\" button and add a new entry with  \"GET_TEXT_ALL\" in the \"Méthode\" field, \"Instance Method\" in the \"Type\"  field, \"Protected\" as \"Visibility\" and \"Get all text\" as a description</OL> <OL>7. Press then the \"Paramètre\" button and add following parameters:</OL> <OL><OL>a) \"IV_DATE\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"DATUM\" as \"Type réf.\"</OL></OL> <OL><OL>b) \"IV_MOLGA\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"MOLGA\" as \"Type réf.\"</OL></OL> <OL><OL>c) \"IV_APPL\" as Paramètre, \"Importing\" as Catégorie, \"Type\" as \"Categ. typage\", \"P_APPL\" as \"Type réf.\"</OL></OL> <OL><OL>d) \"ET_T5F99FCDT \" as Paramètre, \"Exporting\" as Catégorie, \"Type\" as  \"Categ. typage\", \"HR99S_TAB_T5F99FCDT\" as \"Type réf.\"</OL></OL> <OL>8. Save and activate those changes</OL> <P><B>Structure P06AED_S_CHECK_SALAIRES_FLAT creation (release 4.7 only)</B></P> <OL>1. Login in your dev. system with the language \"FR\" enabled</OL> <OL>2. Launch the SE11 transaction and enter \" P06AED_S_CHECK_SALAIRES_FLAT\" in the \"Type de données\" field, then press  the \"Créer\" button</OL> <OL>3. Enter \"Salaires \"flat\" structure\" in the \"Descr. Synthét.\" field</OL> <OL>4. Add following items within this new structure in the fields \"Composante\"/\"Type composante\":</OL> <OL><OL>a) \"ACBEG\" as \"Composante\", \"P06DAD_PER_DEB_SIT\" as \"Type composante\"</OL></OL> <OL><OL>b) \"ACEND\" as \"Composante\", \"P06DAD_PER_FIN_SIT\" as \"Type composante\"</OL></OL> <OL><OL>c) \"SIDAT\" as \"Composante\", \"P06_SIDAT\" as \"Type composante\"</OL></OL> <OL><OL>d) \"DTCUT\" as \"Composante\", \"P06_DTCUT\" as \"Type composante\"</OL></OL> <OL><OL>e) \"DTCUT_TXT\" as \"Composante\", \"TEXT100\" as \"Type composante\"</OL></OL> <OL><OL>f) \"DTTTP\" as \"Composante\", \"P06_DTTTP\" as \"Type composante\"</OL></OL> <OL><OL>g) \"DACAR\" as \"Composante\", \"P06_DACAR\" as \"Type composante\"</OL></OL> <OL><OL>h) \"DACAR_TXT\" as \"Composante\", \"TEXT100\" as \"Type composante\"</OL></OL> <OL><OL>i) \"DANAR\" as \"Composante\", \"P06_DANAR\" as \"Type composante\"</OL></OL> <OL><OL>j) \"IACMO\" as \"Composante\", \"P06_MOT_DEB\" as \"Type composante\"</OL></OL> <OL><OL>k) \"IACMO_TXT\" as \"Composante\", \"TEXT100\" as \"Type composante\"</OL></OL> <OL><OL>l) \"CHSAL\" as \"Composante\", \"P06_CHSAL\" as \"Type composante\"</OL></OL> <OL><OL>m) \"CHBRU\" as \"Composante\", \"P06_CHBRU\" as \"Type composante\"</OL></OL> <OL><OL>n) \"RPAMT\" as \"Composante\", \"P06_RPAMT\" as \"Type composante\"</OL></OL> <OL>5. Save this structure, assign it to the package \"PC06\" and activate it<B>BAdI HRPAYFR_N4DS_CUST update (release ECC5.0 and 4.7 only)</B></OL> <OL>1. Login in your dev. system with the language \"FR\" enabled</OL> <OL>2. Launch the transaction SE18, enter \"HRPAYFR_N4DS_CUST\" in the \"Nom  de définition\" field, then press the \"Modifier\" button</OL> <OL>3. Click on the \"Interface\" tabstrip, then double-click on the \"Nom  d'interface\" field (which contains the value \"IF_EX_HRPAYFR_N4DS_CUST\")</OL> <OL>4. Click on the \"Modify\" button, then add a new method  \"AED_AUTHORITY_CHECK\" with \" Instance Method\" as a \"Type\" and \" Contrôle d'autorisation pour déclaration AED\" as a description</OL><OL>5. Click then on the \"Parameters\" button and add the two the two following parameters:</OL> <OL><OL>a) \"IS_DECLARATION_DATA\" as \"Paramètre\", \"Importing\" as \"Type\", \"Type\"  as \"Catégorie typage\", P06_T_DECL as \"Type réf.\"</OL></OL> <OL><OL>b) \"CV_CHECK_OK\" as \"Paramètre\", \"Changing\" as \"Type\", \"Type\" as  \"Catégorie typage\", \"XFELD\" as \"Type réf.\", \"ABAP_TRUE\" as \"Val. Par défaut\"</OL></OL> <OL>6. Save and activate this change</OL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 3, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HRCFR", "ValidFrom": "470", "ValidTo": "470", "Number": "1731206 ", "URL": "/notes/1731206 ", "Title": "DNAC-AED: AED statement viewer", "Component": "PY-FR"}, {"SoftwareComponent": "SAP_HRCFR", "ValidFrom": "500", "ValidTo": "500", "Number": "1731206 ", "URL": "/notes/1731206 ", "Title": "DNAC-AED: AED statement viewer", "Component": "PY-FR"}, {"SoftwareComponent": "SAP_HRCFR", "ValidFrom": "600", "ValidTo": "600", "Number": "1731206 ", "URL": "/notes/1731206 ", "Title": "DNAC-AED: AED statement viewer", "Component": "PY-FR"}, {"SoftwareComponent": "SAP_HRCFR", "ValidFrom": "604", "ValidTo": "604", "Number": "1731206 ", "URL": "/notes/1731206 ", "Title": "DNAC-AED: AED statement viewer", "Component": "PY-FR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}