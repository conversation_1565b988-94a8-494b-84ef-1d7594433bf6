SAP Note 3294684 addresses an important consideration in the data migration process to SAP S/4HANA using the SAP S/4HANA Migration Cockpit. It outlines a limitation regarding the parallel processing of certain migration objects.

**Summary of SAP Note 3294684:**

- **Issue:** When using transaction LTMC or the Fiori app "Migrate Your Data" for data migration, errors may occur stating that an instance is being processed or is locked by a certain user if more than one data transfer job is initiated for certain migration objects.
  
- **Environment:** The note applies to both the on-premise version of SAP S/4HANA and SAP S/4HANA Cloud.

- **Cause:** The root cause of these errors is that certain APIs used during the migration are not designed to run in parallel. This leads to lock issues when multiple data transfer jobs are executed simultaneously for the same migration object.

- **Resolution:** A list of migration objects that do not support parallel processing is provided. It is clearly stated that these objects must be processed using only one job.
  
  - For adjusting the number of jobs to one, the note refers to SAP Note 2878945, which provides instructions on using the migration cockpit's Job Management to manage the number of jobs.
  
- **Specific Objects Impacted:** Examples of objects that cannot be processed in parallel include "CO - Cost Center," "CO - Profit Center," "FI - Historical Balances," and "MM - Source List." For some of these objects, such as "FI - Historical Balances" and "MM - Source List," additional details are provided in separate SAP Notes, specifically 3111166 and 3294580 respectively.
  
  - It is highlighted that for "FI - Historical balances," the issue is fixed in SAP S/4HANA 2022 and SAP S/4HANA Cloud release.

- **Keywords:** The note identifies keywords related to this issue, such as parallelization, locks, parallel jobs, and LTMC.

This SAP Note serves as a warning and guide to ensure the proper configuration during data migration to SAP S/4HANA to avoid potential data lock issues, by limiting the processing of certain migration objects to a single data transfer job. It also links to other SAP Notes that further detail specific migration objects and how to handle them.