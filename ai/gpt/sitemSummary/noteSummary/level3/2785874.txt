SAP Note 2785874 addresses the replacement of the report RPFIAAPT_MAPAS_FISCAIS (transaction FIAAPT_MAPAS) with a new Advanced Compliance Reports (ACR) report named PT_AA_FISCAL_MAPS in the context of SAP S/4HANA OP1909 (with SW component S4CORE 104), where the old report can no longer be used.

Reason for the change:
The new ACR report has been introduced as part of the system's modernization efforts. It offers performance optimization by leveraging CDS views for data selection instead of the older logical database ADA. Additionally, the report can be accessed via the FIORI launchpad, reflecting a more modern user interface and user experience.

Solution provided:
The new report's configuration is available in the IMG path: Financial Accounting(New)/Asset Accounting/Information System/Country-Specific Functions/Portugal. Users need to define PT Fiscal Maps (ACR) Settings as well as PT Fiscal Maps (ACR) Current Settings.

For users upgrading from an older version of SAP that used RPFIAAPT_MAPAS_FISCAIS, there is a required migration step:
- Migrate the settings of validation keys using program RFIAAPT_MIG_VAL_KEY_CUST (transaction FIAAPT_MIG_VALKEY_C), for which program documentation should be consulted.

Fiori Configuration:
To run the new report from the FIORI launchpad, users need to assign the pre-defined role SAP_BR_AA_ACCOUNTANT_PT to their user profile. This role includes the tile "Run Compliance Report, Portugal AA Reporting" which leads to the "Run Advanced Compliance Report" FIORI application where report name PT_AA_FISCAL_MAPS is to be entered.

ACR Configuration:
ACR configuration should be maintained in the IMG under the path Financial Accounting(New)/Advanced Compliance Reporting/Setting Up Your Compliance Reporting. Detailed settings related to the country PT and the report RPFIAAPT_MAPAS_FISCAIS are described within the IMG documentation.

The note also mentions the possibility of extending the new ACR report with implementations of BAdI definition FIAAPT_BADI_MAPAS. If an implementation of this BAdI definition was previously created for the SAPGUI report RPFIAAPT_MAPAS_FISCAIS, it can still be utilized with the new ACR report.

Additional Information:
The note mentions the creation of new asset master record fields (such as National Classification Code) in the table GLOFAAASSETDATA in the OP1909 release, although these fields are not officially supported. These include fields in the structure GLO_S_PT_FSCL_MAPS (like 'Vehicle Type' GLO_PT_VEHICLETYPE), which may be of interest to some users.