SAP Note 1678758 addresses an issue specific to the Japanese localization in the Real Estate Flexible Management (RE-FX) module of SAP. The problem occurs when users download the output of the payment charge report into a local TXT file using the built-in download service. The symptom is that the payment date fields, namely PymentYear, PymentMonth, and PymentDay, lose their leading zeros in the TXT file. This is an issue because, for legal reasons in Japan, these fields are required to be two characters long.

The note provides guidance on resolving the issue. Users affected by this problem should implement the correction provided by the note or the corresponding support package referenced within the note to ensure that the payment date fields retain their leading zeros when downloaded to a TXT file. This will align with the Japanese legal requirements and ensure data accuracy.