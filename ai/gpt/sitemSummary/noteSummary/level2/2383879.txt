SAP Note 2383879 addresses the availability and support of Employee Self Service (ESS) and Manager Self Service (MSS) technologies within SAP S/4HANA 2022 or higher, specifically for those customers who are doing a system conversion or upgrade to SAP S/4HANA and want to use HCM in the Compatibility Pack or SAP HCM for SAP S/4HANA. 

The note highlights that since SAP S/4HANA does not contain the NetWeaver Application Server for Java (AS Java), Java-based ESS and MSS scenarios are not supported in the Compatibility Pack or within SAP HCM for SAP S/4HANA. 

Some key takeaways from the note are:

- ESS on SAP Internet Transaction Server (ITS) is an outdated technology, and ITS-based services are not supported anymore. SAP HCM for SAP S/4HANA technically blocks all ITS-based ESS services.
- Manager Self Service (MSS) based on HTMLB is obsolete and has been replaced by other technologies, and therefore, are not available within the aforementioned environments.
- Similarly, ESS/MSS on WebDynpro Java (WD Java) is also obsolete as AS Java is not included in SAP S/4HANA.
- MSS List reports based on Operational Data Provisioning (ODP) are based on old technology and are unavailable with Compatibility Pack or within SAP HCM for SAP S/4HANA.
- UI5-based ESS/MSS from HR Renewal are also not supported in SAP S/4HANA as they do not conform to SAP Fiori technology.

For providing ESS/MSS functionalities, SAP recommends using Fiori applications or WebDynpro ABAP based services. It's suggested that customers check if these former ESS/MSS technologies are being used before upgrading to SAP HCM for SAP S/4HANA 2022 or higher. If they are used and required, customers need to implement the ESS/MSS functionalities based on Fiori and WebDynpro ABAP.

The note also provides references to the following:

- SAP Note 2560753 discusses the incompatibility of SAP Kernel version 7.73 with NetWeaver AS Java.
- SAP Note 2273108 outlines the general HCM approach within SAP S/4HANA, including plans for migration.
- SAP Note 2269324 provides information on the Compatibility Scope Matrix for SAP S/4HANA.

To summarize, SAP Note 2383879 serves as an important advisory for organizations planning to transition to SAP S/4HANA 2022 or higher that rely on ESS/MSS technologies, outlining which older technologies are not supported and recommending replacement options available in the new system environment.