SAP Note 2920110 addresses an issue encountered during the upgrade process from SAP version 1709 (or lower) to either 1809 or 1909. The issue specifically relates to closed action tasks that were migrated as part of the upgrade but did not generate planned task instances when program R_EHFND_WFF_RESTART_PROCESSES was run with "Migrate Planned Task Instances" option selected.

The symptom of the error is the failure of the migration program to process closed action tasks, keeping their original workflow templates intact. This prevents the generation of planned task instances for migrated closed action tasks.

To resolve this issue, the SAP Note provides correction instructions. Once the corrections are applied, users can rerun the program R_EHFND_WFF_RESTART_PROCESSES with "Migrate Planned Task Instances" selected to complete the migration of closed action tasks.

The note also advises that for custom workflow templates with custom steps, users must manually add the custom steps to MT_EXEC_STEP_TEMPLATE_RANGE and/or MT_APPR_STEP_TEMPLATE_RANGE attributes of the class CL_EHFND_TASK_PLAN_MIGRATOR. This can be achieved by using the post-exit method for the constructor of the class.

The reason for the issue is deemed to be a program error. To implement the solution, users must apply the correction instructions provided in the SAP Note or, alternatively, install the Support Packages that contain the necessary corrections.

In summary, SAP Note 2920110 outlines a fix for a task migration issue when upgrading to SAP releases 1809 or 1909, making it possible to successfully migrate closed action tasks and generate the appropriate planned task instances as expected.