import re

from auth import get_jsession_cookie
import requests
from bs4 import BeautifulSoup
import logging
from summarize_util import get_completion_with_gpt_4o_mini
from prompts import GET_NOTE_PROMPT_V2

logging.basicConfig(
        format='%(asctime)s %(levelname)-8s %(message)s',
        level=logging.INFO,
        datefmt='%Y-%m-%d %H:%M:%S')


cookies = None
cache_notes = {}
notes_differs = {}
def get_note_json(note, retry = 0):
    global cookies
    global cache_notes
    if note in cache_notes:
        logging.info(f"get note {note} from cache")
        return cache_notes[note]
    if cookies is None:
        cookies = get_jsession_cookie()
    result = requests.get(f"https://me.sap.com/backend/raw/sapnotes/Detail?q={note}", cookies=cookies)
    if result.headers.get("Content-Type", "") != 'application/json':
        retry += 1
        logging.warning(f"unexpected response, {result.content}")
        print(f"session timeout, refresh cookie: time {retry}")
        if retry > 3:
            logging.error(f"Get note {note} failed")
            return None
        cookies = get_jsession_cookie()
        return get_note_json(note, retry)
    if (result.json().get('Response') is not None
            and result.json().get('Response').get('Error').get('Code') == 'NOT_ENTITLED'):
        logging.warning(f"Note {note} is not entitled, skip")
        return None
    if result.json().get('Response') is None or result.json().get('Response').get('Error').get('Code') is not None:
        retry += 1
        logging.warning(f"get note {note} failed, retry {retry}, response: {result.content}")
        if retry > 3:
            logging.warning(f"Get note {note} failed")
            return None
        return get_note_json(note, retry)
    else:
        logging.info(f"get note {note} success")
        cache_notes[note] = result.json()
        return result.json()


def get_note_result(note):
    note_json = get_note_json(note)
    if note_json is None or note_json.get('Response') is None or note_json.get('Response').get('Error').get(
            'Code') is not None:
        logging.warning(f"get note {note} failed")
        return None
    return {
        "note": note,
        "noteTitle": note_json['Response']['SAPNote']['Title']['value'],
        "noteText": remove_img(note_json['Response']['SAPNote']['LongText']['value']),
        "noteVersion": note_json['Response']['SAPNote']['Header']['Version']['value']
    }
    
def get_refer_notes(note_json, level=1, note_set=None):
    if note_set is None:
        note_set = set()
    result = []
    refer_items = note_json['Response']['SAPNote']['References']['RefTo']['Items']
    refer_notes = sorted({item['RefNumber'] for item in refer_items if item['RefNumber']}, key=int)
    # First pass: Construct the first layer of ref notes
    for note in refer_notes:
        if note in note_set or note == "":
            continue
        note_set.add(note)
        note_result = get_note_result(note)
        if note_result is not None:
            result.append(note_result)

    # Second pass: Construct the deeper layers of ref notes if level > 1
    if level > 1:
        for note_result in result:
            note = note_result["note"]
            ref_note_json = get_note_json(note)
            if ref_note_json is None or ref_note_json.get('Response') is None:
                continue

            ref_ref_notes = get_refer_notes(ref_note_json, level - 1, note_set)
            if ref_ref_notes:
                note_result["refer_note"] = ref_ref_notes
    return result

def remove_img(html_string):
    # Parse the HTML
    soup = BeautifulSoup(html_string, "html.parser")

    # Find all img tags
    for img in soup.find_all('img'):
        # Remove them
        img.decompose()

    # Print the result
    return str(soup)

def extract_note_numbers(html_content):
    # Pattern to match SAP Note numbers in various contexts
    pattern = r'(?:(?:SAP\s+Note|SAP\s+Notes?|[Nn]otes?)\s+(?:<a[^>]*>)?(\d{5,7})(?:</a>)?|/notes/(\d{5,7}))'

    # Find all matches in the HTML content
    matches = re.findall(pattern, html_content)

    # Flatten the list of tuples and remove empty strings
    all_numbers = [num for tuple_match in matches for num in tuple_match if num]

    # Remove duplicates and sort the list
    unique_matches = sorted(set(all_numbers))

    return unique_matches

def get_direct_mentioned_note(note_num:str, note_text: str, guid:str = ""):
    notes = get_completion_with_gpt_4o_mini(GET_NOTE_PROMPT_V2.render({"content": note_text}))
    re_notes = extract_note_numbers(note_text)
    if re_notes:
        logging.info(f"Regex extracted note numbers: {re_notes}")
    result = []
    if notes == "" or notes == "NO_NOTE_FOUND":
        notes = set()
    else:
        notes = set(note.strip() for note in notes.split(","))
        logging.info(f"GPT-4o-mini extracted note numbers: {notes}")
    if notes != set(re_notes):
        logging.warning(f"!!!!!Note numbers extracted by GPT-4o-mini are different from the regex extracted note numbers for guid {guid} in note {note_num}.")
        logging.warning(f"GPT-4o-mini: {sorted(notes)}")
        logging.warning(f"Regex: {re_notes}")
        notes_differs[guid] = {
            "note_num": note_num,
            "gpt_4o_mini": sorted(notes),
            "regex": re_notes,
            "ai-re": sorted(notes - set(re_notes)),
            "re-ai": sorted(set(re_notes) - notes)
        }
        notes = set(notes) | set(re_notes)
    for note in sorted(notes):
        note = note.strip()
        # check if the note is a valid note number, exclude the current note number
        if note != "" and note.isnumeric() and note != note_num:
            result.append(get_note_result(note))
    return result
    