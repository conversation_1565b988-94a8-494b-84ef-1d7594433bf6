{"Request": {"Number": "1454353", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 3819, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008580462017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001454353?language=E&token=38607D871D689A5F3B4E0558A8788CA6"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001454353", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001454353/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1454353"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Pilot Release"}, "ReleasedOn": {"_label": "Released On", "value": "19.04.2010"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-CDP-TEST-059"}, "SAPComponentKeyText": {"_label": "Component", "value": "CDP - Acceptance Test"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project Solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP internal ONLY for CDP projects (see SAP Note 689050)", "value": "XX-PROJ-CDP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-CDP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CDPs in Test Phase", "value": "XX-PROJ-CDP-TEST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-CDP-TEST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CDP - Acceptance Test", "value": "XX-PROJ-CDP-TEST-059", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-CDP-TEST-059*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1454353 - SETI RE-FX"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1454353&TargetLanguage=EN&Component=XX-PROJ-CDP-TEST-059&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1454353/D\" target=\"_blank\">/notes/1454353/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Real Estate is not supported by SETI<br /></p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>New Development<br /></p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Before implementing the code corrections attached to this note, the following steps have to be performed.<br /><br />The new developments has been added to this note as an attachment a<br />Correction in the system and can be implemented as an advance<br />correction in the system.<br /><br />Download the following files and import the relevant files in<br />client 000 of your system.<br /> -  TRANS4895.zip<br />For more information about preliminary transports to the<br />customer, see Note 13719.<br /><br />Now implement the corrections using SNOTE. There will be a screen where you need to confirm the changes. On this screen, you will see some &quot;yellow lights&quot; because of objects which will be overwritten. All these lines have to be marked as to be implemented.<br /><br />All changes in correction instruction 854776 wants to modify RE-FX.<br /><br /><B>The following modifications have been made to RE-FX</B><br />Method CL_RECD_CASHFLOW_MNGR   IF_RECD_CASHFLOW_MNGR~GET_LIST_PAY_X<br />Modification was necessary for the temporary cash flow generation in contract maintenance (RECN).<br /><br />Method CL_RECD_CONDITION   IF_RECD_CONDITION~CALC_TAX_AND_ROUND<br />Modification was necessary for the condition specific tax calculation in contract maintenance (RECN).<br /><br />Method CL_RERA_DOC   IF_RERA_DOC~REVERSE<br />Modification was necessary to store the FI document number for tax document.<br /><br />Method CL_RESR_SB_RULE_CALC   IF_RESR_SB_TERM_CALC~CALC_PART<br />Modification was necessary needed to calculate the tax amounts in sales-based settlement.<br /><br />Method CL_RESR_RECEIVABLE_CALC  IF_RESR_RECEIVABLE_CALC~CALC<br />Modification was necessary needed to calculate the tax amounts for sales-based settlement correspondence.<br /><br /><br /><B>The following BADI-Implementations have been created:</B><br />&quot; /TXINTF/RE_CHG_CF for BAdI definition RECD_CASHFLOW-CHANGE_CASHFLOW_PAY<br />&quot; /TXINTF/RE_TX_CLC_RR for BAdI definition RERA_DOC-BEFORE_POST<br />&quot; /TXINTF/RE_TX_CLC_RR for BAdI definition RERA_DOC-BEFORE_REVERSE<br />&quot; /TXINTF/RE_TX_DET_RR for BAdI definition REEX_FI_BAPI-BAPIDOC_MODIFY<br /></p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "D022102"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (D024859)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001454353/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "TRANS4895.zip", "FileSize": "54", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000091482010&iv_version=0004&iv_guid=8D8F89238EAAAF4485F0B6BA14EAAFE5"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1468241", "RefComponent": "XX-PROJ-CDP-090", "RefTitle": "Input XML Element(RE)-FULLY INCLUSIVE set incorrectly", "RefUrl": "/notes/1468241"}, {"RefNumber": "1460956", "RefComponent": "XX-PROJ-CDP-TEST-059", "RefTitle": "Optimization SETI RE-FX", "RefUrl": "/notes/1460956"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1468241", "RefComponent": "XX-PROJ-CDP-090", "RefTitle": "Input XML Element(RE)-FULLY INCLUSIVE set incorrectly", "RefUrl": "/notes/1468241 "}, {"RefNumber": "1460956", "RefComponent": "XX-PROJ-CDP-TEST-059", "RefTitle": "Optimization SETI RE-FX", "RefUrl": "/notes/1460956 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "TXINTF", "From": "473", "To": "473", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "200", "To": "200", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "TXINTF", "NumberOfCorrin": 1, "URL": "/corrins/0001454353/1581"}]}, "ManualActions": {"_label": "Manual Activities", "value": "\r\n<p><code><br />\r\n<br />\r\n------------------------------------------------------------------------<br />\r\n|Manual post-implementation steps                                                   |<br />\r\n------------------------------------------------------------------------<br />\r\n|VALID FOR                                                            |<br />\r\n|Software Component   TXINTF                        external tax in...|<br />\r\n| Release 473          All Support Package Levels                     |<br />\r\n------------------------------------------------------------------------<br />\r\n<br />\r\nPlease deactivate the following BADI-Implementations<br />\r\n<br />\r\nTransaction SE19<br />\r\nImplementation name /TXINTF/RE_TAX_CALC<br />\r\nDeactivate<br />\r\n<br />\r\n<br />\r\nTransaction SE19<br />\r\nImplementation name /TXINTF/RE_TAX_DET<br />\r\nDeactivate<br />\r\n<br />\r\nTransaction SE19<br />\r\nImplementation name /TXINTF/RE_SETCFBUFF<br />\r\nDeactivate<br />\r\n<br />\r\nCheck in an additional function for the display of tax documents in the document relation browser (DRB) for  RE contracts:<br />\r\nInsert an entry /TXINTF/DRB_RE_RELATIONS in table ASHGETS using transaction SE16<br />\r\n<br />\r\n<br />\r\n<br /></code></p>\r\n<code><br /></code>"}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisite", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1454353&TargetLanguage=EN&Component=XX-PROJ-CDP-TEST-059&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1454353/D\" target=\"_blank\">/notes/1454353/D</a>."}}}}