SAP Note 724536 addresses an issue where changing the base unit of measure on the material master's basic data view or on the accounting view does not update the unit in the material ledger, particularly affecting material price analysis.

Symptom:
- The unchanged base unit of measure is still displayed in the material ledger reports like CKM3 after the base unit of measure is changed in the material master.

Other Terms:
- Keywords associated with this issue: ACTMASTER, MM01 (Create Material), CKM3 (Material Price Analysis), actual BOM, valuated quantity structure, CKMLPP.

Reason:
- The issue occurs when the base unit of measure for a material is changed only in the basic data view, not calling the accounting view, or if changed in the accounting view, it updates the material ledger only for the specific plant and not across all plants where the material number is used.

Prerequisites:
- The inconsistency is observed in systems where the material ledger is activated.

Solution:
- Apply the program corrections as indicated in Note 724535 and Note 364368 (ML Helpdesk).
- Execute the attached program ZML_ADJUST_MEINS for relevant materials or, as of Release 4.7, use the program MLHELP_ADJUST_MEINS from Note 364368.
- After implementing the corrections, the supplied program or MLHELP_ADJUST_MEINS must be executed every time there is a change in the base unit of measure.
- Implement the corrections in Note 1580829 which eliminates the need to run ZML_ADJUST_MEINS or MLHELP_ADJUST_MEINS in the future.
- Do not use ZML_ADJUST_MEINS or MLHELP_ADJUST_MEINS with Catch Weight Management.

Instructions are provided to create the program ZML_ADJUST_MEINS in transaction SE38, including text symbols and selection texts definitions. The note also recommends creating a corresponding GUI status.

The note is essentially guiding users to apply specific program corrections and to run a provided program to ensure the base unit of measure changes are reflected across the material ledger, thereby preventing and correcting inconsistencies.