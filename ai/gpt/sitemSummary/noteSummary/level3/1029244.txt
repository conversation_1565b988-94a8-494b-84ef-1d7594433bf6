SAP Note 1029244 addresses an issue encountered during the service charge settlement process in the context of a migration from ER Classic to RE-FX (Real Estate Flexible Management). The problem is that advance payments from legacy data, which were made prior to the current settlement period and before the date of the first debit position, are not being correctly processed. These erroneous records result in unsettled special general ledger items.

The cause of the issue is identified as a program error. To resolve this problem, SAP provides correction instructions that need to be implemented. The note's symptom section clarifies the issue, while the solution section suggests that by following the correction instructions, the data records will be processed correctly and the open items will be settled as expected. The note does also reference the term "VIRAADVPAYEXT," but doesn't provide additional context for it within the given excerpt. To fully understand and resolve the issue, one must implement the correction instructions as indicated in the note.