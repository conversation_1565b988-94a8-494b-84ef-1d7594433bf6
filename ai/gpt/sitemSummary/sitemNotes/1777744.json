{"Request": {"Number": "1777744", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 359, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001777744?language=E&token=481A757FCB5C5FA8A6442B9884A00DE2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001777744", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001777744/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1777744"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "How To"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.03.2015"}, "SAPComponentKey": {"_label": "Component", "value": "FI-TV-PL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Travel Planning"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "1777744 - Travel Planning Third Party Integration (How to)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><h2 data-toc-skip style=\"text-align: left;\"><strong><strong>Business</strong> Background</strong></h2>\r\n<p>The travel management business process demands an integrated solution for the management of the full trip life cycle; pre-trip approval, online booking, and expense reporting. <br />In 2009, SAP&#8217;s strategy for SAP Travel Management made a significant change. SAP partnered with the two global market leaders in online booking; GetThere by Sabre and e-Travel Management by Amadeus. <br /><br />There are many advantages to work with these two partners, which are specialists in Travel Planning.&#160; The new travel rules will be available faster for the SAP end-user and changes regarding fares and providers are immediately taken into account without double maintenance in the SAP IMG. Development of new features (new processes during the online booking) will be transparent for SAP customers. As the business is outsourced outside SAP, there is no need to install a note or a Support Package to integrate a new feature. The partners (GetThere and eTravel) &#160;handle this themselves. Only the communication and the integration of data from these partners into SAP is maintained (per correction and/or new services).</p><h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3><ul>\r\n<li>Travel Management (FI-TV)</li>\r\n<li>Travel Planning with Third Party Partner</li>\r\n<li class=\"longtext\" style=\"font-size: 100.01%;\">Enhancement Package&#160;5 and Higher</li>\r\n</ul><h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3><p>The maintenance of the classical, previous Travel Planning solution will not include possible new features. <br />By consequence, new specific <strong>travel planning</strong> features will not be available in the \"old\" SAP GUI UI, Java WebDynpro or ABAP WebDynpro UI. <br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3><p>We recommend to use this new solution. For more information about our strategy please refer to SAP note <a href=\"https://websmp130.sap-ag.de/sap(bD1lbiZjPTAwMQ==)/bc/bsp/spn/sapnotes/index2.htm?numm=1255358\" target=\"_blank\">1255358</a>.</p>\r\n<p class=\"header5 header3\">Business Value:</p>\r\n<ul>\r\n<li>Usage of best-in-breed online booking tools integrated in SAP environment</li>\r\n<li>Combined end-to-end travel management solution</li>\r\n<li>SAP reporting on GetThere / Amadeus eTravel data</li>\r\n</ul>\r\n<p class=\"header3 header5\">Functional Details:</p>\r\n<ul>\r\n<li>As a general statement, the goal is that the integration of the Third-Party Travel Planning tools is similar to SAP&#8217;s Travel Planning solution.</li>\r\n<li>\r\n<p>This includes topics like integration of SAP employee HR master data, traveler profile, pre-trip approval (Travel Request), integration to expense report, All My Trips, and SSO enablement</p>\r\n</li>\r\n</ul>\r\n<h4 data-toc-skip style=\"padding-left: 30px;\">Connection to the Web Service (LP_CONFIG vs SOAMANAGER):</h4>\r\n<p style=\"padding-left: 30px;\">The third-party integration is based on the use of Web Services (WS). These WSs are provided by our partners. The connection was defined in the older documentation in several steps. We had to define a RFC destination and then link the travel user group and this RFC destination via LP configuration. <br />This process was possible via the Transaction LP_CONFIG or the existing report LP_EDIT with a special variant. All this is described in the documentation. <br />Now SAP offers a new way to access an external system via the SAOMANAGER. More and more customers asked us to use the SOAMANAGER. <br />In the standard we didn't propose SOA for Travel but it is very easy to launch SOAMANAGER by defining a new logical port for a proxy consumer and then in the definition accessing the same URL <br />(rather than defining in the RFC destination in the original configuration). <br /><br />For that purpose, launch the Transaction SOAMANAGER. Then in the tab \"Service Administration\" link -&gt; \"Web Service Configuration\". Here, for each Consumer Proxy starting with CO_TS* (see the exhausted list underneath), you have to create a new LP (tab Configuration in the dependent tray). Here, on the configuration of the logical port, you have to report the same information that is defined in the transaction SM59 (Set Up RFC Destinations for Interfaces to External Systems). You still have to maintain some IMG activities in Travel to assign your Sales Office to the correct Logical Port (Determine Sales Offices and RFC Destinations). <br />The component BC-ESI-WS-ABA-CFG can be used for any issues concerning the creation of the connection.</p>\r\n<h4 data-toc-skip style=\"padding-left: 30px;\">List of Proxies for GetThere</h4>\r\n<ul>\r\n<li>CO_GT_PROFILE_UPLOAD_PORT_TYPE</li>\r\n<li>CO_GT_TRAVEL_ITINERARY_CANCEL</li>\r\n<li>CO_GT_TRAVEL_ITINERARY_READ</li>\r\n<li>CO_GT_TRAVEL_ITIN_LOCATOR_READ</li>\r\n<li>CO_GT_TRAVEL_ITIN_PURCHASE</li>\r\n<li>CO_TSQACCESS_PORT_TYPE</li>\r\n<li>CO_TSQCOUNT_PORT_TYPE</li>\r\n<li>CO_TSQPLACE_PORT_TYPE</li>\r\n<li>CO_TSSESSION_CLOSE_PORT_TYPE</li>\r\n<li>CO_TSSESSION_CREATE_PORT_TYPE</li>\r\n</ul>\r\n<h4 data-toc-skip style=\"padding-left: 30px;\">List of Proxies for eTravel</h4>\r\n<ul>\r\n<li>CO_AMI_PROFILE</li>\r\n<li>CO_AMI_TRIP_PLAN</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Remark: We have less Proxies for eTravel as we have several methods by proxy as compared with GetThere where one Proxy corresponds exactly to one method.</p>\r\n<p class=\"header5\">Related subjects:</p>\r\n<h4 data-toc-skip style=\"padding-left: 30px;\">SAP Help Portal</h4>\r\n<p style=\"padding-left: 30px;\">This is the general documentation that all customers can find on the SAP help portal concerning Travel Management in general (first link) and more specifically, the integration with third parties (second link).</p>\r\n<ul>\r\n<li>\r\n<div style=\"padding-left: 30px;\"><a href=\"http://help.sap.com/erp2005_ehp_05/helpdata/en/49/d61162099d61eae10000000a42189c/content.htm\" target=\"_blank\">Travel Management</a></div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\"><a href=\"http://help.sap.com/erp2005_ehp_05/helpdata/en/c8/2c7b80cb2f4eaab0a519b9336ebf3b/content.htm?frameset=/en/1e/9467498ee0444f9d06070d3e267364/frameset.htm\" target=\"_blank\">Travel Management, Third-Party Travel Planning 2</a></div>\r\n</li>\r\n</ul>\r\n<h4 data-toc-skip style=\"padding-left: 30px;\">Note for the Profile synchronization</h4>\r\n<p style=\"padding-left: 30px;\">Before using the third-party integration, a synchronization of the profile between SAP and the third party is mandatory. <br />Pertinent information included in the SAP info types needs to be transferred to the corresponding structures of the partners. See the following SAP notes for further details:</p>\r\n<ul>\r\n<li>GetThere Profile Synchronization: <a href=\"https://websmp130.sap-ag.de/sap/support/notes/1665959\" target=\"_blank\">1665959</a></li>\r\n<li>eTravel Profile Synchronization: <a href=\"https://websmp130.sap-ag.de/sap/support/notes/1695902\" target=\"_blank\">1695902</a></li>\r\n<li>Enablement of Travel Profile Deletion for eTravel: <a href=\"https://websmp130.sap-ag.de/sap/support/notes/1807943\" target=\"_blank\">1807943</a></li>\r\n<li>\r\n<p>Enablement of Travel Profile Deletion for GetThere : <a href=\"https://css.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6E756D6265723D3231343039373026\">2140970</a></p>\r\n</li>\r\n</ul>\r\n<h4 data-toc-skip style=\"padding-left: 30px;\">Note for Workflow with external party</h4>\r\n<p style=\"padding-left: 30px;\">With our partners and as described in the presentations, a new status has been introduced in the complete workflow process. A trip can be created in GetThere or eTravel and retain the status \"on Hold\" until it is approved by the manager. This new status is handled by the BADI PTRM_EXTERNAL_TP_BADI_DEF, which provides several methods during the life cycle of the trip (for example the synchronization in POWL).</p>\r\n<ul>\r\n<li>SAP Note <a href=\"https://websmp130.sap-ag.de/sap/support/notes/1805622\" target=\"_blank\">1805622</a>: Travel Approval issue: integration with 3rd Party (GetThere)</li>\r\n<li>SAP Note <a href=\"https://websmp130.sap-ag.de/sap/support/notes/1805970\" target=\"_blank\">1805970</a>: GET_ETRAVEL_PNR, issue with approver name deleted (eTravel).</li>\r\n</ul>\r\n<h4 data-toc-skip style=\"padding-left: 30px;\">Booking \"On Behalf of\"</h4>\r\n<p style=\"padding-left: 30px;\">The Travel Assistant is an HCM feature which is integrated in Travel by activating a switch framework (see documentation and customizing). <br />This feature is available with eTravel since EHP6 and was downgraded to EHP5. It is not available this time with GetThere (a work-around is possible); some improvements in the near future are expected.</p>\r\n<h4 data-toc-skip style=\"padding-left: 30px;\">Presentation</h4>\r\n<p style=\"padding-left: 30px;\">What's new in EHP6? Please have a look at the attached file (RKT_FIN_TRAVEL_Overview_Travel_planning.pdf) which contains all new enhancements shipped with EHP6 (ERP2005 ECC 6.06). This document includes: Travel Assistant, batch processes, Workflow integration (approval) and synchronization of lowest airfare (flight bookings).</p>\r\n<p style=\"padding-left: 30px;\">&#160;</p>\r\n<h4 data-toc-skip style=\"padding-left: 30px;\"><strong>Note for the &#171;&#160;Other service&#160;&#187; implementation</strong></h4>\r\n<p style=\"padding-left: 30px;\">The &#8220;Other Service&#8221; feature allows customers to synchronize services other than the traditional Flight, Hotel, Car and Rail. External third-parties and customers are able to send flexible sets of data to the SAP system to represent Ferry, Helicopter - or any other service. Although &#8220;other services&#8221; are highly customer-dependent &#8211; they depend on customer agreements with the third party, SAP provides a default integration into SAP tables. Customers can then use customizing through BADI PTRM_EXTERNAL_TP_INTERFACE&#126;CHANGE_OTHER_SERVICE_DATA to integrate data differently.</p>\r\n<p style=\"padding-left: 30px;\">The feature &#8220;Other Service&#8221; is available for Amadeus eTravel as of release EHP5. It is currently not available for Sabre GetThere.</p>\r\n<p style=\"padding-left: 30px;\">Please check note <a href=\"https://css.wdf.sap.corp/sap(bD1kZSZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?iv_language=D&amp;param=69765F6D6F64653D3030312669765F7361706E6F7465735F6B65793D303132303036313533323030303030303835343732303135267361702D6C616E67756167653D442669765F6C616E67756167653D44\" target=\"_blank\">2116682 </a>on how to implement this feature in your system.</p>\r\n<p style=\"padding-left: 30px;\">&#160;</p>\r\n<p style=\"padding-left: 30px;\">&#160;</p><h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3><p><a target=\"_blank\" href=\"http://help.sap.com/erp2005_ehp_05/helpdata/en/c8/2c7b80cb2f4eaab0a519b9336ebf3b/content.htm?frameset=/en/1e/9467498ee0444f9d06070d3e267364/frameset.htm\">Travel Management, Third-Party Travel Planning 2</a></p><h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3><p>GetThere, eTravel, Third Party Partner, External Booking, SOAMANAGER, Proxies, Profile Synchronization, Workflow, Travel Assistant</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-TV-PL-SAB (API Interface SABRE GetThere)"}, {"Key": "Other Components", "Value": "FI-TV-PL-AET (Interface Amadeus e-Travel Management)"}, {"Key": "Responsible                                                                                         ", "Value": "Francois<PERSON><PERSON> (I023407)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I031918)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001777744/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001777744/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001777744/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001777744/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001777744/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001777744/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001777744/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001777744/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001777744/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "RKT_FIN_TRAVEL_Overview_Travel_Planning.pdf", "FileSize": "703", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000653732012&iv_version=0003&iv_guid=005056A877AD1ED28EDA62A8BC45431A"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2892303", "RefComponent": "FI-TV-PL", "RefTitle": "Travel Planning External content", "RefUrl": "/notes/2892303 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP enhancement package 5 for SAP ERP 6.0"}, {"Product": "SAP enhancement package 6 for SAP ERP 6.0"}, {"Product": "SAP enhancement package 7 for SAP ERP 6.0"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "1 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 4.0, "Quality-Votes": 1, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 1, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}