{"Request": {"Number": "3090910", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 519, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001365992021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003090910?language=E&token=DBBC488C8B842ECA50F2E5E1A5AB2540"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003090910", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003090910/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3090910"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.05.2023"}, "SAPComponentKey": {"_label": "Component", "value": "FI-CF-APR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Central Payment"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Central Finance", "value": "FI-CF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-CF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Central Payment", "value": "FI-CF-APR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-CF-APR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3090910 - Check If Special GL Transaction Exist When Creating Invoice in Source System"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You had checked the flag of '<strong>Commitments Warning</strong>' in properties of special G/L indicator.</p>\r\n<p>When you create an invoice, a message will be raised for your check if special G/L transactions exist in system.</p>\r\n<p>But no message is raised once the company code is activated for Central Payment.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p id=\"\">Central Finance, FB60, FB70, Fiori app 'Create Supplier Invoice', Special G/L transaction</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This is a missing functionality.</p>\r\n<p>Because special G/L transactions will be posted in the Central Finance system for Central Payment case, the system should remotley check special G/L transactions in the Central Finance system.</p>\r\n<p>You have implemented the following SAP Notes in the given order in source system.</p>\r\n<p><a target=\"_blank\" href=\"/notes/3090908\">3090908</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/3087555\">3087555</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/3094144\">3094144</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2871310\">2871310</a></p>\r\n<p>And you have maintained RFC connection from source system to Central Finance system by steps described in <a target=\"_blank\" href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/26c2d5e366bc44c1a98f2a9212a0c49d/b5268985efc348ecae9d0029e233416c.html?version=2021.latest\">online help</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p id=\"\"><strong><em>This correction applies to the source system(s).<br /></em></strong><em>Implement the attached correction instructions or the corresponding Support Package.</em></p>\r\n<p>If you want to use the functionality, you also have to implement SAP Note&#160;<a target=\"_blank\" href=\"/notes/3090911\">3090911</a> in the Central Finance system.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I017703)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I310190)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003090910/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003090910/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003090910/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003090910/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003090910/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003090910/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003090910/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003090910/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003090910/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3090911", "RefComponent": "FI-CF-APR", "RefTitle": "Get Special GL Transaction in Central Finance system for Central Payment Case", "RefUrl": "/notes/3090911"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2827364", "RefComponent": "FI-CF-APR", "RefTitle": "Central Payment for SAP Central Finance", "RefUrl": "/notes/2827364 "}, {"RefNumber": "2346233", "RefComponent": "FI-CF-APR", "RefTitle": "Switch on Central Payment for Central Finance", "RefUrl": "/notes/2346233 "}, {"RefNumber": "3090911", "RefComponent": "FI-CF-APR", "RefTitle": "Get Special GL Transaction in Central Finance system for Central Payment Case", "RefUrl": "/notes/3090911 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10210INS4CORE", "URL": "/supportpackage/SAPK-10210INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10308INS4CORE", "URL": "/supportpackage/SAPK-10308INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10406INS4CORE", "URL": "/supportpackage/SAPK-10406INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 105", "SupportPackage": "SAPK-10503INS4CORE", "URL": "/supportpackage/SAPK-10503INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 106", "SupportPackage": "SAPK-10601INS4CORE", "URL": "/supportpackage/SAPK-10601INS4CORE"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60034", "URL": "/supportpackage/SAPKH60034"}, {"SoftwareComponentVersion": "SAP_APPL 602", "SupportPackage": "SAPKH60224", "URL": "/supportpackage/SAPKH60224"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60323", "URL": "/supportpackage/SAPKH60323"}, {"SoftwareComponentVersion": "SAP_APPL 604", "SupportPackage": "SAPKH60424", "URL": "/supportpackage/SAPKH60424"}, {"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60521", "URL": "/supportpackage/SAPKH60521"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60629", "URL": "/supportpackage/SAPKH60629"}, {"SoftwareComponentVersion": "SAP_FIN 617", "SupportPackage": "SAPK-61724INSAPFIN", "URL": "/supportpackage/SAPK-61724INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 618", "SupportPackage": "SAPK-61818INSAPFIN", "URL": "/supportpackage/SAPK-61818INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 720", "SupportPackage": "SAPK-72017INSAPFIN", "URL": "/supportpackage/SAPK-72017INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 730", "SupportPackage": "SAPK-73019INSAPFIN", "URL": "/supportpackage/SAPK-73019INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 4, "URL": "/corrins/0003090910/15841"}, {"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 6, "URL": "/corrins/0003090910/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 6, "URL": "/corrins/0003090910/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 16, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}