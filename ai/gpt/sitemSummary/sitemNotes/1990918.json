{"Request": {"Number": "1990918", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2593, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001990918?language=E&token=C24EE30ABD57CADE2E0D6A0CC0B85F6C"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001990918", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001990918/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1990918"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Troubleshooting Help"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1990918 - Description of RFC connections to SAPNet R/3 front end (OSS)"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1990918&TargetLanguage=EN&Component=SV-SMG-SDD&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1990918/D\" target=\"_blank\">/notes/1990918/D</a>.</span></div></div><div><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>As of the 1st July 2014, no longer allow RFC accesses to the SAPNET R/3 front end (OSS) if the &quot;Server Selection&quot; setting is selected in an SAP system in transaction SM59 within the RFC connection to SAP. The correct setting must be provided with &quot;Load Balancing&quot; and a corresponding logon group (for details, see this SAP Note).</strong></p>\r\n<p>This change affects all existing SAP systems that have incorrect settings in the RFC connection to SAPNet R/3 Frontent (OSS).</p>\r\n<p>This SAP Note contains a description such as the correct setting or Adjust the RFC connection in an SAP system (SAP customer system; Solution Manager system) to the SAPNet R/3 front end (OSS).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3>\r\n<p>Logon Groups SAPNet R/3 Frontend (OSS), SMLG, EWA, SNOTE, Solution Manager, SDCC, SDCCN, RTCCTOOL, RSECSH_NOTE, MOPZ, Maintenance Optimizer, Solution Manager Basic Functionality, RNOTIFUPDATE, RNOTIFUPDATE01, RNOTIFSH_POATE7, OSS1, Service Provider, VAR, Service Desk, Partner, Incident Management, RFC Connections to SAP</p>\r\n<p> </p>\r\n<h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\r\n<p><strong>Incorrect technical setting in an RFC connection (transaction SM59) of an SAP system to the SAPNET R/3 front end (OSS)</strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3>\r\n<p>There are various application-specific scenarios in an SAP system that require an RFC connection to the SAPNet R/3 front end (OSS) at SAP.<br /><br />Some examples:<br /><br />You can use transaction SNOTE to download SAP Notes into your SAP system so that you can then implement such a SAP Note in the SAP system.<br /><br />It is possible to send data packages with statistical information to SAP by RFC so that an EarlyWatch Alert report (so-called HealthCheck) can be created for an SAP system. This can, for example, be automatically and periodically scheduled and sent by a system administrator from an SAP Solution Manager system. This scenario can also be set up in any other SAP production system.</p>\r\n<p>In transaction SE38 or SA38, you can use the report RTCCOOL to display the latest recommendations for an SAP service delivery.<br /><br />You can use the Maintenance Optimizer application to determine download packages for pending support package maintenance or release upgrades.</p>\r\n<p><strong>SAP Solution Manager has various interfaces to the SAPNet R/3 front end (OSS):</strong> <br /><br /></p>\r\n<p>Examples:<br />- The Support Desk application can exchange error messages, known as incidents, with the SAP backbone (SAPNet R/3 front end).<br />- Maintenance certificates can be loaded from the SAPNet R/3 front end (OSS) into a customer system and compared<br />- EarlyWatchAlert data can be exchanged with SAP<br />- System data can be exchanged with SAPNet R/3 Froentend (OSS)<br />- Enterprise Support reports can be created<br />- etc.</p>\r\n<p>The support team responsible for SAPNet R/3 Frontend (OSS) at SAP repeatedly finds that technical settings of the RFC connections to the  SAPNet R/3 Frontend (OSS) are incorrectly maintained because the corresponding technical settings in the RFC connection (transaction SM59) to the SAPNet R/3 Frontend (OSS) are not activated using &quot;Load Balancing&quot; for logon groups, but &quot;Server Selection&quot; has been selected in the RFC connection.<br />This is an incorrect configuration of the RFC connection in transaction SM59 of the SAP system.</p>\r\n<p><strong>It is very important that these incorrect technical settings are corrected in transaction SM59 of the SAP system for the SAPNet R/3 front end (OSS).</strong></p>\r\n<p> </p>\r\n<p><strong>SAP becomes the first July 2014, activate the following change in the SAPNet R/3 front end (OSS).</strong><br /><br />RFC accesses from an SAP system to the SAPNet R/3 front end (OSS) are no longer permitted if the technical setting &quot;Server Selection&quot; in transaction SM59 is set to the central instance of OSS. As a result, only accesses by group logon are allowed.</p>\r\n<p>Every RFC access that takes place directly to the central instance of the SAPNet R/3 front end (OSS) will terminate as of this date.<br /><br /><br />Only RFC accesses via &quot;Load Balancing&quot; to the SAPNet R/3 front end (OSS) will be allowed. The possible load balancing settings are listed below in this SAP Note.</p>\r\n<p>Remember that you usually have more than one RFC connection in your SAP system to the SAPNet R/3 front end (OSS).<br />This means that you must check all RFC connections to the SAPNet R/3 front end and adjust them if necessary.<br /><br />See below the recommendations for the correct setting of the corresponding RFC connections to the SAPNet R/3 front end (OSS). <br />In an SAP system, you can use transaction SM59 to check and adjust the technical connections to the SAPNet R/3 front end (OSS).<br /><br /><br />The list of RFC connections proposed and therefore used by SAP at the current time are as follows.<br /><br />Note: If you use your own RFC connections to SAP that differ from the SAP proposal, you can identify these using transaction SE16 in the table RFCDES.</p>\r\n<p> </p>\r\n<p>The RFC connections to the SAPNet R/3 front end (OSS) proposed by SAP are usually as follows:</p>\r\n<p style=\"padding-left: 30px;\"><br /><span style=\"text-decoration: underline;\"><strong>RFC connections                          Logon group:</strong></span>                       <br />SAPOSS                                           EWA<br />SAPNET_RFC                                    EWA<br />SAPNET_RTCC                                  EWA<br />SDCC_OSS                                       EWA<br />CUPOSS                                           EWA<br /><br /><br />OSSNOTE                                         1_PUBLIC<br />SAP-OSS                                          1_PUBLIC<br />SAP-OSS-LIST                                  1_PUBLIC</p>\r\n<p style=\"padding-left: 30px;\">SM_SP_&lt;customer number>              1_PUBLIC<br /><br /><br /><br /><span style=\"text-decoration: underline;\"><strong>Logon group &quot;2_JAPANESE&quot;:</strong></span><br />SAP customers with Japanese language should use logon group “2_JAPANESE”.</p>\r\n<p> </p>\r\n<p>As a result, the following load balancing groups are available for a logon to the SAPNet R/3 front end (OSS)  :</p>\r\n<p style=\"padding-left: 30px;\"><br /><strong>1_PUBLIC</strong><br /><strong>2_JAPANESE</strong><br /><strong>EWA</strong></p>\r\n<p>SAP ensures that ONLY these RFC logon groups (1_PUBLIC, 2_JAPANESE, EWA) work when connecting to the SAPNet R/3 front end (OSS).</p>\r\n<p>The user with password already assigned in an existing RFC connection to the SAPNet R/3 front end (OSS) does not have to be changed.<br />Of course, the existing user must work in the RFC connection - including the password - before the adjustment of the RFC connection and continue to work after the adjustment of the RFC connection.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">For the technical settings in transaction SM59 for these ABAP connections:</span></strong><br /><br />Use the target system &quot;OSS&quot;<br />Please use the message server oss001.wdf.sap.corp.<br />Select YES for &quot;Load Balancing&quot;.<br />Use a logon group as already specified above in this SAP Note.</p>\r\n<p><br />Target host: /H/X1/S/S1/H/X2/S/3299/H/oss001.wdf.sap.corp<br />with<br />X1 = IP address of customer SAProuter<br />X2 = IP address of SapservX<br />S1 = Customer SAProuter port<br /><br /><br /><strong>Possible Entries for SapservX</strong><br />Sapserv1a (***************) Internet VPN connection<br />Sapserv2a (*************) Internet SNC connection<br />Sapserv3 (***********) for customers connected to Germany<br />Sapserv4 (************) for customers in America<br />Sapserv5 (************) for customers connected to Japan<br />Sapserv7 (*************) for customers in APJ incl. New Zealand and Australia<br />SAPserv9a (***************) for customers in APJ incl. New Zealand and Australia<br />SAPServ10a (*************) for customers in China</p>\r\n<p><strong>Caution:</strong><br /><strong>Note that these changes are activated in the SAPNet R/3 front end (OSS) on the date specified above. These changes will have no effect on your SAP systems if your RFC connection(s) to the SAPNet R/3 front end (OSS) are already configured correctly.</strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-INS (Solution Manager Installation, Configuration and Upgrade)"}, {"Key": "Other Components", "Value": "SV-ES-SEC (SAP Secure Support Services)"}, {"Key": "Other Components", "Value": "XX-SER-NET (network connection)"}, {"Key": "Other Components", "Value": "XX-SER-NET-HTL-CON (2nd level for RCSC - internal use only!)"}, {"Key": "Other Components", "Value": "SV-SMG-OP (Solution Directory)"}, {"Key": "Other Components", "Value": "SV-BO (Backoffice Service Delivery)"}, {"Key": "Other Components", "Value": "SV-SMG-SER-EWA (EarlyWatch Alert)"}, {"Key": "Other Components", "Value": "SV-SMG-SER (SAP Support Services)"}, {"Key": "Other Components", "Value": "SV-SMG-UMP (OBSOLETE and INACTIVE: Usage Measurement of Products)"}, {"Key": "Other Components", "Value": "BC-UPG-NA (Note Assistant)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (D001690)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (D001690)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001990918/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "812386", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "RFC Connections to SAPNet R/3 Frontend", "RefUrl": "/notes/812386"}, {"RefNumber": "766505", "RefComponent": "XX-SER-NET", "RefTitle": "OSS1: Changes to RFC Connection SAPOSS", "RefUrl": "/notes/766505"}, {"RefNumber": "216952", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCC) - FAQ", "RefUrl": "/notes/216952"}, {"RefNumber": "182308", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/182308"}, {"RefNumber": "169924", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/169924"}, {"RefNumber": "1234500", "RefComponent": "SV-SMG-UMP", "RefTitle": "Composite SAP Note: Information about AutoUpdate", "RefUrl": "/notes/1234500"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1990918&TargetLanguage=EN&Component=SV-SMG-SDD&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1990918/D\" target=\"_blank\">/notes/1990918/D</a>."}}}}