{"Request": {"Number": "179373", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 595, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014734622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000179373?language=E&token=32452E6C59ABF8DF2FD2FAB43379955E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000179373", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000179373/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "179373"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 83}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.10.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "179373 - Additional info for upgrade to Rel.4.6B"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>An error occurs in the upgrade procedure or in the upgrade guide.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Update, migration, release, upgrade release, maintenance level, R3up, Prepare</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>*</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><strong>IMPORTANT: This SAP Note is updated continually.</strong><br /><strong>You must therefore read it again immediately before the upgrade. </strong><br /><br /><br /><strong>What can you expect of this SAP Note? </strong></p>\r\n<ul>\r\n<li>It describes problems that may occur when you upgrade the R/3 system and provides you with information about how to solve them. In most cases you are referred to other SAP Notes.</li>\r\n</ul>\r\n<ul>\r\n<li>The main aim of this SAP Note is to prevent data loss, upgrade standstill and long runtimes.</li>\r\n</ul>\r\n<ul>\r\n<li>This SAP Note only deals with database-independent problems.</li>\r\n</ul>\r\n<p><strong>What can you not expect of this SAP Note? </strong><br />Problems after the upgrade are only dealt with if they are caused directly by the upgrade tools.<br /><br /><br /><strong>Which other SAP Notes do I need in preparation for the upgrade?</strong><br /><br />That depends on the functions you are using. You need one or more of the following SAP Notes:<br /><br /><br />Short text.......................................... SAP Note number<br />----------------------------------------------------------------------<br />Additions to upgrade to 4.6B SAP DB 180456<br />Additional Information: Upgrade to 4.6B - DB2/400 178823<br />DB6: Enhancements to upgrade to 4.6B 178759<br />DB2/390: Additions Upgrade to 4.6B 178168<br />Additions upgrade to 4.6B INFORMIX 179675<br />Additions upgrade on 4.6B MSSQL Server 179263<br />Additions Upgrade to 4.6B ORACLE 179376<br />----------------------------------------------------------------------<br />Current note on language import 4.6B 179580<br />Storage space required for 4.6B language import 179582<br />----------------------------------------------------------------------<br />Additions upgrade to 4.6B online documentation 179982<br />----------------------------------------------------------------------<br />Ready-to-Run R/3: Information on the upgrade 111601<br />----------------------------------------------------------------------<br />Problems during upgrade with Support Packages 119738<br />----------------------------------------------------------------------<br />For HR customers:<br />References to upgrade notes regarding HR 156387<br /><br />For HR customers of the Japan country version:<br />T512W backup&amp;restore procedure on Upgrade&amp;HR SP 310790<br /><br />For HR customers of the South Africa country version:<br />Phase TABIM_46B terminated on table T5W1Y  312909<br />----------------------------------------------------------------------<br /><br /><br /><br /><br /><strong>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Contents<br /></strong><br /><br />I/ ...... R3up keyword<br />II/ ..... Important general information<br />III/ .... Corrections to the guides<br />IV/ ..... Errors on the CD-ROM<br />V/ ...... Preventing loss of data, upgrade shutdowns and long<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;runtimes<br />VI/ ..... Problems with the Upgrade Assistant<br />VII/ .... Problems in the PREPARE and upgrade phases<br />VIII/.....Problems after the upgrade<br />IX/.......Contents in chronological order<br /><br /><br /><br /><br /><strong>I/&#x00A0;&#x00A0;&#x00A0;&#x00A0; R3up keyword<br /><br /></strong><br />------------------------&lt; D0001330 20/OCT/99 &gt;-------------------------<br />* R3up keyword (enter the first time you call R3up): 143665<br />----------------------------------------------------------------------<br /><br /><br /><br /><br /><strong>II/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Important general information<br /><br /><br />------------------------&lt; D022030 22/DEC/00 &gt;-------------------------</strong><br /><strong>Exchanging the SAP kernel</strong><br />When you exchange the SAP kernel, the contents of the kernel<br />directory will be deleted. For safety reasons, we therefore recommend creating<br />a backup of the directory before you start to carry out the upgrade.<br />For further information, refer to the PREPARE log CHECKS.LOG.<br /><br /><br />------------------------&lt;D019132 06/NOV/00 &gt;-------------------------<br />Use of the downward-compatible kernel 4.6D (DCK/46D)<br />The DCK/46D cannot be used to carry out the upgrade.<br />Therefore, do not exchange the kernel on the subdirectory &quot;exe&quot;<br />of the upgrade directory with a DCK/46D but only carry out<br />the kernel update after you have completed the upgrade.<br /><br /><br />------------------------&lt; D022030 28/SEP/00 &gt;-------------------------<br />If your DB-OS combination is not supported during the upgrade,<br />refer to the information in<br /><strong>SAP Note 335690</strong>.<br /><br /><br />------------------------&lt; D001330 29/MAY/00 &gt;-------------------------<br /><strong> Basis Support Packages in the upgrade</strong><br />If you want to include Basis Support Packages in the upgrade, note the release in <strong>SAP Note 119738</strong>.<br /><br /><br />------------------------&lt; D020768 27/JUL/99 &gt;-------------------------<br />Special features for <strong>SAP Retail </strong>customers<br /><br />The following cases must be distinguished:</p>\r\n<ul>\r\n<li>UPGRADE FROM R/3 RETAIL RELEASE 1.2Bx TO R/3 RELEASE 4.6B:<br />Caution: This only affects customers who are using the R/3 Retail Releases 1.2Bx.<br /><br />You have R/3 Retail Releases 1.2Bx (1.2B, 1.2B1, or 1.2B2) and you want to carry out an upgrade to R/3 Release 4.6B.<br />Proceed as follows:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Step 1: Upgrade from R/3 Retail Release 1.2Bx to R/3 Release 4.0B.<br />In this step, your system is automatically characterized as SAP Retail (XPRA XPRASETRETAILSYSTEM).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Step 2: Upgrade from R/3 Release 4.0B to R/3 Release 4.6B.</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CAUTION:<br />A direct upgrade of R/3 Retail Releases 1.2Bx to R/3 Release 4.6B is not supported.</p>\r\n<ul>\r\n<li>The following applies to all other cases:<br />If you are an SAP Retail customer already or you want to become one for 4.6B, additional actions as described in <strong>SAP Note 157755</strong> are necessary.<br /><br /></li>\r\n</ul>\r\n<p>------------------------&lt; D025323 09/MAY/2000 &gt;-----------------------<br />If you want to use the incremental table conversion,refer to SAP 193480.<br />You must not execute ICNV unless the Support Packages <br />described in the SAP Note are imported.<br /><br /><br />------------------------&lt; D030559 19/MAY/00 &gt;--------------------------<br />Before the upgrade, refer to SAP Note 16897 for the table S005.\r\n<br /><br /><br />------------------------&lt; D001330 16/JUN/00 &gt;--------------------------<br />HR customers with the Japan country version can find information regarding saving and reproducing table T512W during the upgrade in SAP Note 310790.<br /><br />-----------------------------------------------------------------------<br /><br /><br /><br /><strong>III/&#x00A0;&#x00A0; Corrections to the guides</strong><br /><br />------------------------&lt; D001330 20/OCT/04 &gt;-------------------------<br />Section: Phase JOB_RSVBCHCK2<br />The text should read:<br />&quot;If errors are reported in this Phase, and you are <strong>still</strong> in production operation, you can bypass this error with &#39;ignore&#39; without a password&quot;.<br /><br />------------------------&lt; D022030 12/OCT/00 &gt;-------------------------<br />Changed SAP Note number for the SAP Internet solution<br />(Section in guide: Prerequisites for the SAP Internet solution)<br />The SAP Note for information on new versions of SAP@Web <br />Studio and ITS has changed. The new SAP Note number is 197746.<br /><br />------------------------&lt; D022030 19/JAN/2000 &gt;-----------------------<br />UNIX shells (Section: New for upgrade; Page 9)<br />Corrected text: As of Release 4.6B, SAP no longer supports the Korn <br />shell. If you have used the Korn shell up to now, change the login shell of the user &lt;sapsid&gt;adm and of the database user to <br />C shell (csh) or Bourne shell (sh) and reset your <br />environment settings accordingly.<br /><strong>Caution:</strong> This only applies to the login shell and the scripts delivered by SAP (startsap, stopsap, and so on). The scripts you have developed remain unaffected by these changes.<br /><br /><br /><br /><strong>IV/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Errors on the CD-ROM<br /><br /><br /></strong><br /><strong>V/&#x00A0;&#x00A0;&#x00A0;&#x00A0; Avoiding data loss, upgrade standstill, and long run times<br /></strong><br />------------------------&lt; D031049 14/OCT/04 &gt;-------------------------<br /><strong>Source Release 4.0B: Project-related incoming order</strong><br />If you apply a published advance correction to a project-related incoming order using SAP Note 118780, you have to modify data elements before you upgrade.<br />For more information, see <strong>SAP Note 369542</strong>.<br /><br />------------------------&lt; D020839 14/OCT/04 &gt;-------------------------<br /><strong>Component PS-ST on SAP DB/MS SQL: Table MLST</strong><br />If you have changed table indexes as described in SAP Note 82252, you must undo these changes before you upgrade.<br />For more information, see <strong>SAP Note 191270</strong>.<br /><br />------------------------&lt; D001330 14/OCT/04 &gt;-------------------------<br /><strong>Measures for optimizing the upgrade runtime</strong><br />For more information about optimizing the upgrade runtime see <strong>composite SAP Note 76431</strong>.<br /><br />------------------------&lt; D030022 14/OCT/04 &gt;-------------------------<br /><strong>Source Release 3.1I: Loss of Address Data</strong><br />If you install component &quot;Maintenance&quot; (PM-WOC-MN) and convert addresses using report RSXADR05 before the upgrade from Source Release 3.1I, see the information in <strong>SAP Note 360929</strong>.<br />Otherwise address data may be lost during the upgrade.<br /><br />----------------------&lt; D030326 17/JUL/02 &gt;---------------------------<br /><strong>Source Releases 4.0B and 4.5B: Termination in various phases during the creation of a job variant using R3up</strong><br />If you have imported Support Packages 74, 75, 76, or 77 for Release 4.0B or 53, 54, 55 for Release 4.5B, terminations can occur in various upgrade and PREPARE phases. The related logs state that no variant can be created by R3up. In this case, refer to <strong>SAP Note 533777.</strong><br />This error no longer occurs as of Support Package 78 for Release 4.0B and Support Package 56 for Release 4.5B.<br /><br />------------------------&lt; D028597 29/APR/02 &gt;-------------------------<br /><strong>Do not import R/3 Support Package 40</strong><br />When importing Support Package SAPKH46B40, activation errors occur in the phase ACT_&lt;REL&gt;.<br />For more information, see <strong>SAP Note 515957.</strong><br /><br />------------------------&lt; D022803 15/APR/02 &gt;-------------------------<br /><strong>Do not include Basis Support Package 38 as the highest Support Package</strong><br />If you include Basis Support Package 38 in the upgrade, you cannot use transaction SPDD to compare tables. In this case, a syntax error occurs in the program SAPLTMSC.<br />For more information, see <strong>SAP Note 512241</strong>. Under &quot;References to Support Packages&quot;, you can find the corresponding Support Package that contains the solution to the problem.<br /><br />------------------------&lt; D024084 27/JUL/01 &gt;-------------------------<br /><strong>For MM customers (inventory management) and source release 3.0x/3.1x only:</strong><br />During the upgrade, specific customizing settings of movement types are lost. The settings are reset to the SAP default settings.<br />For more information, see <strong>SAP Note 86627</strong>.<br /><br />------------------------&lt; D025323 23/FEB/01 &gt;-------------------------<br />To prevent loss of table fields and data, you must integrate a minimum<br />number of Support Packages in the upgrade in the case of certain start<br />releases. This applies to the following source releases:<br />&#x00A0;&#x00A0; 31I   as of package SAPKH31I68<br />&#x00A0;&#x00A0;40B&#x00A0;&#x00A0;as of package SAPKH40B58<br />&#x00A0;&#x00A0; 45B&#x00A0;&#x00A0; as of package SAPKH45B35<br />&#x00A0;&#x00A0; 46B<br />For more information, see <strong>SAP SAP Note 383126</strong>.<br /><br />------------------------&lt; D029407 02/FEB/01 &gt;------------------------<br /><strong>Only for HR customers with payroll for construction industry </strong><br />All entries in Table T5DB4 are deleted for an upgrade with <br />source release 3.1I.  Before you upgrade, save the data in the database and <br />reload again after upgrading.<br />For more information, see <strong>SAP SAP Note 156568</strong>.<br /><br />-------------------&lt; D025323 31/JAN/01 &gt;------------------------------<br /><strong>Alternative name for the upgrade directory</strong><br />Note that the &lt;DIR_PUT&gt; upgrade directory may not contain \r\n<br />more than 18 characters. <br />Longer directory names lead to terminations in the <br />RUN_PRELDIST phase.<br /><br />-------------------&lt; D024777 20/NOV/00 &gt;------------------------------<br /><strong>Incorrect conversion of cost center assignments</strong><br />If you are using the organizational management, you have to include<br />at least R/3 HR Support Package 8 in the upgrade or to implement the<br />source code corrections of SAP Note 214976 before running<br />the XPRA RHU40C01.<br />For more information, see <strong>SAP Note 307803</strong>.<br /><br />-------------------&lt; D019307 25/AUG/00 &gt;------------------------------<br /><strong>For source releases smaller than 4.5: Advance conversion of addresses</strong><br />If the addresses make up a large data volume, we recommend<br />that you start report RSXADR21 for the address conversion before the upgrade.<br />For more information, see <strong>SAP SAP Note 97032</strong>.<br /><br />-------------------&lt; D000706 22/AUG/00 &gt;------------------------------<br /><strong>Only for source Release 3.1x: R3trans termination in PREPARE</strong><br />On some operating systems R3trans terminates in the PREPARE phase\r\n<br />PATCH_CMDIMP. You need a corrected R3trans version <br />from sapservX.<br />For more information, see <strong>SAP SAP Note 327290</strong>.<br /><br />-------------------&lt; D001330 24/JUN/00 &gt;------------------------------<br />Control of table T510Q prior to the upgrade, refer to SAP Note 83728.<br /><br /><br />-------------------&lt; D019132 15/JUN/00 &gt;------------------------------<br /><strong>Loss of customer secondary indexes</strong><br />Customer secondary indexes that you created for the tables<br />EKBO<br />MBEW<br />OBEW<br />EBEW<br />QBEW<br />are lost during the upgrade and cause the upgrade to stop in the PCON_46B phase.  To avoid this loss, see SAP Note <strong>310681</strong>.<br /><br />-------------------&lt; D025323 04/MAY/00 &gt;------------------------------<br /><strong>Import error in phase XTERN_CNV in Rel 46B</strong><br />The upgrade terminates in XTERN_CNV with an error.  In the file &lt;DIR_PUT&gt;/bin/XTERN_CNV.LST you find the error message:<br />2EETW000 Value of field PAT08-CNT truncated to &#39;&#39;.<br />To avoid this error, see SAP Note 217526.<br /><br />-------------------&lt; D019512 18/APR/00 &gt;------------------------------<br /><strong>Archiving work items to reduce conversion time</strong><br />When updating to Release 4.6A, table SWWWIHEAD was cleaned up<br />in an effort to increase performance.  This cleaning-up <br />requires an (automatic) table conversion on the database.<br />To minimize the conversion time, we strongly recommend that you either archive or delete entries that are no longer required<br />for this table (work items in an end status). <br />This should be done prior to the upgrade. <br />The following SAP Notes contain information on archiving/deleting data:<br />49545, 145291, 153205, 159065.<br /><br />-------------------&lt; D023536 11/APR/00 &gt;------------------------------<br /><strong>Loss of customer-specific logical databases</strong><br />Due to an incorrect selection customer-specific logical databases are lost during the upgrade with source Release 4.0B. In RDDIT006 the system erroneously determines that LDBA does not exist, and it is therefore not entered in the SAPKCCC46B piece list. This affects all customer-specific LDBAs which were created in 4.0.<br />To avoid this error you should import Support Package 42 before the upgrade, or implement the correction in accordance with SAP Note 213477.<br /><br />-------------------&lt; D022942 22/MAR/00 &gt;------------------------------<br /><strong>For HR customers who include LCP 3 in the upgrade only</strong><br />When you include the LCP 3 in the upgrade, terminations occur in phase TABIM_46B in the tables T5ITGC, T5ITGE, and T5ITGG. To avoid this, download the corresponding allow files from the sapserv prior to starting the upgrade, as described in SAP Note 208526 (and 183407).<br /><br />-------------------&lt; D019132 03/MAR/00 &gt;------------------------------<br /><strong>Data loss for table A518</strong><br />When using the application component SD, the generated table A518 is lost in the upgrade. To avoid this loss, see SAP Note <strong>204143</strong>.<br /><br />-------------------&lt; D001330 17/FEB/00 &gt;------------------------------<br /><strong>Upgrade phase SHADOW_IMPORT</strong><br />If Support Packages or add-ons (or a comparison transport by SPDD/SPAU from earlier systems) are applied in an upgrade, then you should replace the R3trans in the directory /usr/sap/put/exe by a corrected version before you start the upgrade. For more information, see SAP Note 197714.<br /><br /><br />-------------------&lt; D019141 24/JAN/00 &gt;------------------------------<br /><strong>Avoiding data loss during INVC</strong><br />To avoid data loss, make sure you read subsections &quot;ICNV in PREPARE&quot; in section VII (SAP Notes 139514 and 193480) and &quot;Incremntal convrsn of tables EDIDOC and CDCLS after the upgrade&quot; in section VIII (SAP Note 173330).<br /><br />-------------------&lt; D001330 01/SEP/00 &gt;------------------------------<br /><strong>Change documents are lost after the upgrade</strong><br />Before upgrading, see SAP Note 199037.<br /><br /><br /><br /><br /><br /><strong>VI/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problems with the Upgrade Assistant<br /></strong><br /><br /><br />Short text.......................................... SAP Note number<br />-----------------------------------------------------------------------<br /><strong>Only for MS Internet Explorer:</strong><br />Starting Upgr. Ass. from within MS Intnt Expl. 4.0 137590<br />-----------------------------------------------------------------------<br /><strong>Only if the Upgrade Assistant Server runs on Windows NT:</strong><br />R/3 upgrade Assistant: PREPARE stops 137587<br />-----------------------------------------------------------------------<br /><strong>Only for AIX:</strong><br />R/3 Upgrade Assistant: no connection to server .............137594<br />-----------------------------------------------------------------------<br /><strong>Only for Digital UNIX:</strong><br />NO R/3 Upgrade Assistant on Digital UNIX 3.2 ....................137599<br />-----------------------------------------------------------------------<br /><strong>Only for HP-UX:</strong><br />Upgrade Assistant: stack overflow exception&#x00A0;&#x00A0;...................137607<br />----------------------------------------------------------------------<br /><br /><br /><br /><br /><strong>VII/&#x00A0;&#x00A0; Problems in the PREPARE and upgrade phases<br /><br /></strong><br />In this column you can find known problems that you cannot avoid with preventive measures. These problems can only occur under certain conditions or prerequisites.<br /><br /><br />---------------------&lt; D001330 01/SEP/00 &gt;----------------------------<br /><strong>SPDD: modification of data elements does not work</strong><br />To avoid problems with SPDD, see SAP Note 184192 and include (at least) Basis Support Package 1.\r\n<br /><br /><br />---------------------&lt; C1605804 19/MAY/00 &gt;----------------------------<br /><strong>Only for RELIANT UNIX:</strong><br />SAPCAR does not start in the PREPARE if the RELIANT UNIX version is lower than 5.44 and the version of the C++ runtime environment CDS++RTS is lower than 2.0. For more information, see SAP Note 304809.<br /><br /><br />----------------------&lt; D023536 28/FEB/00 &gt;----------------------------<br /><strong>Checking prior to PREPARE</strong><br />If problems occurred in phase GENERATE_BACKUP when you applied Support Packages using SPAM and if these have been corrected with Note 189938, it is possible that these generated transport requests are identified as modification during upgrade. Execute the report RSSPAM15 to avoid this. If it does not exist in the system, import the current SPAM update for the source release PRIOR TO PREPARE.<br /><br /><br />----------------------&lt; D028773 28/FEB/00 &gt;----------------------------<br /><strong>Authorization errors for new transactions after the upgrade</strong><br />Authorization errors occur after the upgrade to Release 4.6B when executing new transactions. This is caused by a not current status of tables USOBX and USOBT. To avoid this problem, include the Support Packages 1 and 2 in the upgrade. For more information, see SAP Note 199921.<br /><br /><br />----------------------&lt; D028597 22/MAR/00 &gt;----------------------------<br /><strong> PREPARE phase BIND_PATCH</strong><br />To ensure that Support Packages are imported correctly, see the information in SAP Note 208509.<br /><br /><br />----------------------&lt; D019141 16/FEB/00 &gt;----------------------------<br /><strong>PREPARE phase TOOLIMPD2</strong><br />In source Release 4.0B, a termination in SAPLSDB6 might occur during the PREPARE in phase TOOLIMPD2. To prevent this problem, see the information in SAP Note <strong>201440</strong>.<br /><br />-------------------&lt; changed D024828 12/Oct/00 &gt;----------------------<br />----------------------&lt; D019141 24/JAN/00 &gt;----------------------------<br /><strong>ICNV in PREPARE</strong><br />The incremental conversion of table clusters before the upgrade works only from a certain kernel patch level. For more information, see SAP Notes <strong>139514</strong> and <strong>193480</strong>.<br /><br /><br />------------------------&lt; D024371 20/APRIL/00 &gt;------------------------<br /><strong>PREPARE module CD read</strong><br />If you have already installed the following add-ons, then you must read about including add-ons in the R/3 upgrade in the SAP Notes specified below. If the upgrade note has not yet been released, then find the prospective release date in the release strategy note.<br /><br /><strong>Add-on&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade note   &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release strategy note</strong><br /><br />BANKING ..................... 197273 .................. 102247<br />INSURANCE ................... 199892 .................. 100104<br />HR-PS ....................... 201355 .................. 133954<br />CA-JVA ...................... 216122, 214555 .......... 112809<br />CIN ......................... 214135 .................. 078432<br />WP-PI ....................... 201044<br />CEM (P3G) ................... 209533 .................. 208859<br />PI .......................... 185836 .................. 181248<br />PI-A ........................ 185836 .................. 181248<br /><br /><br />------------------------&lt; D019419 16/May/00 &gt;------------------------<br />PDB-ADD-ON(&quot;D&amp;B for SAP R/3&quot;) 302101&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;138213<br />BW-BCT, APO-CIF , CRM-R3A , B2B-PRO-PI&#x00A0;&#x00A0;, SEM-PI&#x00A0;&#x00A0;:<br />Installation of PI or PI-A according to SAP Notes in 181248, section III<br />and the subsequent upgrade to Release 4.6B according to SAP Note 185836.<br />------------------------------------------------------------------------<br /><br /><br /><strong>PREPARE module import </strong><br />-----------------------------------------------------------------------<br />Phase: TOOLIMPD2<br />SAP Note: 98198<br />Description: Error in upgrade phase TOOLIMPD1, TOOLIMPD2<br />-----------------------------------------------------------------------<br /><br /><strong>PREPARE module CD read </strong><br />-----------------------------------------------------------------------<br />Phase: IS_MERGE<br />SAP Note: 98673<br />Description: Add-on installation after upgrade PREPARE 4.x<br />---------------------------------------------------------------------<br /><br /><strong>PREPARE module activation checks </strong><br />-----------------------------------------------------------------------<br />Phase: JOB_RADDRCHK<br />SAP Note: 140280<br />Description: TG063: Name conflict for name &amp; will ...<br />-----------------------------------------------------------------------<br />Phase: JOB_RADDRCHK<br />SAP Note: 133017<br />Description: Namespace conflicts w. upgrade to Release 4.x<br />-----------------------------------------------------------------------<br />Phase: ACTREF_CHK<br />SAP Note: 140313<br />Description: Customer reference to deleted SAP data element<br />-----------------------------------------------------------------------<br /><br /><br /><br /><strong>Upgrade phases </strong><br />-----------------------------------------------------------------------<br />Phase: PATCH_STATUS<br />SAP Note: 208509<br />Description: Error in upgrade phase PATCH_STATUS<br />-----------------------------------------------------------------------<br />Phase: TABIM_46B<br />SAP Note: 208526<br />Description: Only for HR customers who include LCP 3 in the upgrade.<br />Termination at tables T5ITGC, T5ITGE, T5ITGG<br />-----------------------------------------------------------------------<br />Phase: SHADOW_IMPORT<br />SAP Note: 197714<br />Description: Problems with R3trans in SHADOW_IMPORT<br />-----------------------------------------------------------------------<br />Phase: All phases<br />SAP Note: 138284<br />Description: Spool: invalid entry for output device<br />-----------------------------------------------------------------------<br />Phase: ACT_46B<br />SAP Note: 515957<br />Description: After the inclusion of R/3 Support Package 40, activation errors with return code 8 appear in the log from SAPKH46B40.&lt;SID&gt;.<br />-----------------------------------------------------------------------<br />Phase: ACT_46B/SE11<br />SAP Note: 186773<br />Description: Customer ABAP Dictionary objects cannot be edited in SE11<br />-----------------------------------------------------------------------<br />Phase: ACT_46B/SPDD<br />SAP Note: 74831<br />Description: Transports QE1K900002, TCVK900029, EWSK900052<br />-----------------------------------------------------------------------<br />Phase: ACT_46B/SPDD<br />SAP Note: 147610<br />Description: Table T5UT4 is displayed in SPDD for adjustment<br />-----------------------------------------------------------------------<br />Phase: ACT_46B<br />SAP Note: 191270<br />Description: Only ADABAS D and MSSQLSRV:<br />SAP indexes for MLST cannot be activated.<br />-----------------------------------------------------------------------<br />Phase: PCON_46B<br />SAP Note: 73999<br />Description: Upgrade phase PCON_46B: TG450 to TG453<br />-----------------------------------------------------------------------<br />Phase: PCON_46B<br />SAP Note: 140071<br />Description: PCON_46B: R3up reports terminations in the R/3 System<br />-----------------------------------------------------------------------<br />Phase: JOB_RDDNTPUR<br />SAP Note: 394331<br />Description: <strong>Only if the Note Assistant is used</strong><br />Carry out the new installation of the Note Assistant in phase SPAUINFO before the modification adjustment.<br />---------------------------------------------------------------------<br />Phase: XPRAS_46B<br />SAP Note: 145794<br />Description: 990302: LONGPOST.LOG: Error filling tbl &quot;STACUST&quot;<br />---------------------&lt; D D019716 21/FEB/00 &gt;--------------------------<br />Phase: XPRAS_46B<br />Description: LONGPOST.LOG: You can ignore the following XPRA RMCSXP04 (SAPK46AXP5) entries:<br /> 3PEM2579 Client &quot;xxx&quot;: Problem adapting &quot; &quot;/&quot; &quot;<br /> to updating IS &quot;S216&quot; (TMC2F)<br />where xxx is a client in your system. The same applies to &quot;S217&quot; and &quot;S218&quot;.<br />-----------------------------------------------------------------------<br />Phase: XPRAS_46B<br />SAP Note: 193822<br />Description: With the logon language French, an ABAP runtime error occurs: PERFORM_CONFLICT_TYPE in the report RHUMST_HRPADNN_HRPADUZ.<br />-----------------------------------------------------------------------<br />Phase: XPRAS_46B<br />SAP Note: 203094<br />Description: Runtime of XPRA AGR_XPRA_REGENERATE_SAP_NEW 193631 is too long.<br />-----------------------------------------------------------------------<br /><br /><br /><br /><br /><strong>VIII/&#x00A0;&#x00A0;Problems after the upgrade<br /><br /></strong><br />In this section, you find known problems that are caused by the upgrade tools and that you cannot avoid using preventive measures. These problems can only occur under certain conditions or prerequisites.<br /><br /><br />-----------------------------------------------------------------------<br /><strong>Only if FI-FM is used:</strong><br />In the report tree of funds management, the<br />standard reports listed under the &quot;drilldown<br />reporting&quot; node cannot be called after the upgrade.<br />For more information, see <strong>SAP Note 69035</strong>.<br /><br />------------------------&lt; D022860 09/JAN/01 &gt;-------------------------<br /><strong>Incomplete path in .sapenv_&lt;hostname&gt;.sh</strong><br />Only for UNIX: If you use the Born or Korn shell as login<br />shell for &lt;sapsid&gt;adm and cannot stop/start the system<br />any longer, the cause may be an incompletely specified<br />path in in $HOME/.sapenv_&lt;hostname&gt;.sh.<br />Change the line:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;for d in /usr/sap/&lt;SAPSID&gt;/SYS/exe/run; do<br />to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;for d in /usr/sap/&lt;SAPSID&gt;/SYS/exe/run $HOME .; do\nin the file $HOME/.sapenv_&lt;hostname&gt;.sh.<br /><br />---------------------------------------------------------------------<br /><strong>Linux: Importing the new saposcol version</strong><br />For more information, see <strong>SAP Note 19227</strong>.<br /><br />---------------------------------------------------------------------<br /><strong>Contents of the tables EWUWAERTP and EWUWAERTPT are missing</strong><br />Check whether the tables have been filled in your system after the upgrade. If not, download the files from sapserv, and import the request.<br />For more information, see <strong>SAP Note 315568</strong>.<br /><br />-----------------------------------------------------------------------<br /><strong>ReliantUNIX: saposcol version 32 or 64-bit</strong><br />For more information, see <strong>SAP Note 148926</strong>.<br /><br />-----------------------------------------------------------------------<br /><strong>Solaris: saposcol version 32 or 64-bit</strong><br />For more information, see <strong>SAP Note 162980</strong>.<br /><br />--------------------&lt; D019141 07/DEC/99 &gt;------------------------------<br /><strong>Incremental conversion of tables EDIDOC + CDCLS after the upgrade</strong><br />The incremental conversion of tables EDIDOC and CDCLS after the upgrade works only as of a certain kernel patch level due to a kernel error. Do not start the data transfer after the upgrade until the corresponding kernel patch level has been implemented.<br />For more information, see <strong>SAP Note 173330</strong>.<br /><br />--------------------&lt; D025223 23/DEC/99 &gt;------------------------------<br /><strong>Local currency changeover to euro directly after the upgrade</strong><br />Call transaction SE38. In the attributes of report REUCLDOC, activate the fixed point arithmetic since otherwise, the change of all FI documents will be incorrect. The problem is corrected with Hot Package 1.<br />For more information, see <strong>SAP Note 189672</strong>.<br /><br />---------------------&lt; D023648 &gt;---------------------------------------<br /><strong>Missing release notes for releases &lt;= 4.0B </strong><br />For more information, see <strong>SAP Note 174073</strong>.<br /><br />---------------------&lt; D017206 29/DEC/99 &gt;-----------------------------<br /><strong>Release note for ALE in FI</strong><br />For more information, see <strong>SAP Note 187034</strong>.<br /><br />---------------------&lt; D020330 23/FEB/00 &gt;-----------------------------<br /><strong>Possible data inconsistency for work with Customizing projects</strong><br />Before you work with Customizing projects, you must apply the correction described in SAP Note 195033.<br />For more information, see <strong>SAP Note 195033</strong>.<br /><br />---------------------&lt; D028773 28/FEB/00 &gt;-----------------------------<br /><strong>Authorization errors</strong><br />Authorization errors occur after the upgrade to Release 4.6B when you execute new transactions. This is caused by a not current status of tables USOBX and USOBT. Read SAP Note 199921 and apply Hot Package 2 (Release 4.6B).<br />For more information, see <strong>SAP Note 199921</strong>.<br /><br /><br />----------------------&lt; D028953 13/MAR/00 &gt;----------------------------<br /><strong> Only for original Release 4.0B as of Hot Package 29 on Informix or Oracle Database: Problems with deletion of table EWUCLU after upgrade</strong><br />We are working on a solution by Hot Package to solve the problem.<br />For more information, see <strong>SAP Note 204338</strong>.<br /><br />-----------------------------------------------------------------------<br /><br /><br /><br /><br /><strong>IX/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Contents in chronological order<br /><br /></strong><br />Date.......Topic.....Short description<br />-----------------------------------------------------------------------<br />OCT/20/04..III.......Section: Phase JOB_RSVBCHCK2<br />14/OCT/04....V..Source Release 4.0B: Project-related incoming order<br />14/OCT/04....V..Component PS-ST on SAP DB/MS SQL: Table MLST<br />14/OCT/04....V..Source Release 3.1I: Loss of Address Data<br />14/OCT/04....V..Measures for optimizing the upgrade runtime<br />17/JUL/02....V..Termination when creating a job variant<br />29/APR/02..VII..Activation errors in phase ACT_46B<br />29/APR/02....V..Do not import R/3 Support Package 40<br />15/APR/02....V..Do not include Basis Support Package 38<br />27/JUL/01....V..MM + source release 3.x only: Loss of movement types<br />27/JUN/01.VIII..Only for FI-FM: Standard reports cannot be called<br />18/JUN/01..VII..Error in JOB_RDDNTPUR when using Note Assistant<br />23/FEB/01....V..Data loss in some source releases<br />02/JAN/01....V..HR construction industry: Loss of data in Table T5DB4<br />31/JAN/01....V..Alternative name for the upgrade directory<br />09/JAN/01.VIII..Incomplete path in .sapenv_&lt;hostname&gt;.sh<br />22/DEC/00...II..Exchanging the SAP kernel<br />20/NOV/00....V..Incorrect conversion of cost center assignments<br />06/NOV/00...II..Use of the downward-compatible kernel 4.6D<br />19/OCT/00.VIII..Linux:&#x00A0;&#x00A0;Importing the new saposcol version<br />Changed SAP Note number for SAP Internet solution<br />28/SEP/00...II..DB-OS-DB-OS combination during upgrade not supported<br />01/SEP/00 ..... SPDD: modification of data elements does not work<br />01/SEP/00....V..Change documents are lost after upgrading to 46A/B<br />25/AUG/00....V..Source releases &lt; 4.5: Advance conversion addresses<br />22/AUG/00....V..R3trans termination in PREPARE<br />24/JUL/00....V..Control of table T510Q prior to the upgrade<br />07/JUL/00.VIII..Table EWUWAERTP delivered empty<br />27/JUN/00 ..... Phase TABIM_46B terminated on table T5W1Y<br />16/JUN/00 ..... Saving and recovering table T512W<br />15/JUN/00....V..Loss of customer secondary indexes<br />19/MAY/00....V..SAPCAR does not start for RELIANT &lt; 5.44<br />19/MAY/00...II..Problems with S005 in the upgrade<br />09/MAY/00...II..Error in R3up for ICNV with add-ons in upgrade<br />04/MAY/00....V..Import error in phase XTERN_CNV in Rel 46B<br />18/APR/00....V..Archiving of work items<br />11/APR/00....V..Loss of logical databases<br />22/MAR/00..VII..Error in upgrade phase PATCH_STATUS 22/MAR/00....V &amp; VII..TABIM_46B Termination in tables T5ITGC, T5ITGE, T5ITGG\n13/MAR/00.VIII..Problems with table EWUCLU after upgrade<br />03/MAR/00....V..When using SD: See SAP Note 204143<br />28/FEB/00..VII..Check before PREPARE -&gt; RSSPAM15 28/FEB/00..VII &amp; VIII&#x00A0;&#x00A0;Authorization errors after upgrade\n28/FEB/00..VII..Runtime of XPRA AGR_XPRA_REGENERATE_SAP_NEW<br />23/FEB/00.VIII..Possible inconsistency for work with customer projects \n17/FEB/00....V &amp; VII..Problem in upgrade phase SHADOW_IMPORT<br /> 16/FEB/00....V &amp; VII......V &amp; VII..Problem in phase TOOLIMPD2\n10/FEB/00..VII..Namespace conflicts w. upgrade to Release 4.6B \n24/JAN/00....V &amp; VII..ICNV in PREPARE\n12/JAN/00..VII..RABAX in RHUMST_HRPADNN_HRPADUZ for language=FR<br />29/DEC/99.VIII..Release note for ALE in FI<br />23/DEC/99.VIII..Missing release notes for releases &lt;= 4.0B<br />23/DEC/99.VIII..Local currency changeover to euro directly after the upgrade<br />23/DEC/99..VII..SAP indexes of the MLST cannot be activated\n07/DEC/99.VIII..Incremental conversion of tables EDIDOC &amp; CDCLS\n29/NOV/99..VII..ACT_46B: ABAP Dictionary objects cannot be edited in SE11<br />25/NOV/99..VII..XPRAS_46B: LONGPOST.LOG: XPRA RMCSXP04 (SAPK46AXP5)<br />20/OCT/99.VIII..saposcol Version 32-bit or 64-bit on Solaris<br />20/OCT/99.VIII..saposcol Version 32-bit or 64-bit on ReliantUNIX<br />20/OCT/99..VII..Table T5UT4 is displayed in SPDD for adjustment<br />20/OCT/99..VII..XPRAS_46B: LONGPOST.LOG: Error filling table &quot;STACUST&quot;<br />20/OCT/99..VII..PREPARE/IS_MERGE: Add-on installation for source release<br />20/OCT/99..VII..ACT_46B/SPDD: QE1K900002, TCVK900029, EWSK900052<br />20/OCT/99..VII..PREPARE/JOB_RADDRCHK: For start with 3.0x/3.1x: TG063<br />20/OCT/99..VII..PREPARE/ACTREF_CHK customer ref. to deleted SAP data element<br />20/OCT/99..VII..PCON_46B: R3up reports terminations in the R/3 System<br />20/OCT/99...VI..Starting Upgr. Ass. from within MS Intnt Expl. 4.0<br />20/OCT/99...VI..R/3 Upgrade Assistant: PREPARE stops<br />20/OCT/99...VI..R/3 Upgrade Assistant: no connection to server<br />20/OCT/99...VI..NO R/3 Upgrade Assistant on Digital UNIX 3.2<br />20/OCT/99...VI..R/3 Upgrade Assistant: stack overflow exception<br />20/OCT/99..VII..Syslog/spool: invalid entry for output device<br />20/OCT/99..VII..PREPARE/TOOLIMPD2: Termination with distribution error<br />20/OCT/99..VII..PCON_46B: Error messages TG450 to TG453<br />20/OCT/99...II..SAP Retail: Special features during the upgrade<br />20/OCT/99....I..R3up keyword<br />----------------------------------------------------------------------</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D001330"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D049603)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000179373/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179373/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179373/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179373/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179373/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179373/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179373/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179373/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179373/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98673", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98673"}, {"RefNumber": "98198", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98198"}, {"RefNumber": "97032", "RefComponent": "SD-MD", "RefTitle": "Addresses: Preliminary conversion in Release 4.5/4.6/4.7", "RefUrl": "/notes/97032"}, {"RefNumber": "86627", "RefComponent": "MM-IM", "RefTitle": "Movement types: Customizing during release upgrade", "RefUrl": "/notes/86627"}, {"RefNumber": "83728", "RefComponent": "PY-DE", "RefTitle": "Check table T510Q before the upgrade", "RefUrl": "/notes/83728"}, {"RefNumber": "78432", "RefComponent": "XX-CSC-IN", "RefTitle": "Release strategy of country version India (CIN)", "RefUrl": "/notes/78432"}, {"RefNumber": "76431", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/76431"}, {"RefNumber": "74831", "RefComponent": "BC-CCM-MON", "RefTitle": "SPDD:Transports QE1K900002, TCVK900029, EWSK900052", "RefUrl": "/notes/74831"}, {"RefNumber": "73999", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/73999"}, {"RefNumber": "69035", "RefComponent": "FI-FM-IS", "RefTitle": "Drilldown reporting: Reports cannot be executed", "RefUrl": "/notes/69035"}, {"RefNumber": "546736", "RefComponent": "PP-REM-ORD", "RefTitle": "PPREM_XPRA_NO_RSH: Very long runtime", "RefUrl": "/notes/546736"}, {"RefNumber": "534826", "RefComponent": "LO-LIS-DC", "RefTitle": "LIS: Current update programs after upgrade", "RefUrl": "/notes/534826"}, {"RefNumber": "533777", "RefComponent": "BC-ABA-TO", "RefTitle": "Upgrade problem after implementing note 508520", "RefUrl": "/notes/533777"}, {"RefNumber": "515957", "RefComponent": "BC-UPG-OCS", "RefTitle": "Activation errors for the RFPDO1 table (AD857)", "RefUrl": "/notes/515957"}, {"RefNumber": "512241", "RefComponent": "BC-CTS-TMS", "RefTitle": "Syntax error in the SAPLTMSC program while upgrading", "RefUrl": "/notes/512241"}, {"RefNumber": "49545", "RefComponent": "BC-BMT-WFM", "RefTitle": "Deletion of work items that are no longer required", "RefUrl": "/notes/49545"}, {"RefNumber": "394331", "RefComponent": "BC-UPG-NA", "RefTitle": "Upgrading to 4.X: Error message with CWB* tables", "RefUrl": "/notes/394331"}, {"RefNumber": "383126", "RefComponent": "BC-SRV-BSF-CUR", "RefTitle": "Additions upgrade: expiring currencies", "RefUrl": "/notes/383126"}, {"RefNumber": "369542", "RefComponent": "PS-REV-IO", "RefTitle": "CJA1: Upgrade of Release 4.0 preliminary solution to >= 4.5", "RefUrl": "/notes/369542"}, {"RefNumber": "360929", "RefComponent": "PM-WOC-MN", "RefTitle": "Loss of address data on execution of RSXADR05", "RefUrl": "/notes/360929"}, {"RefNumber": "332889", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-Oil: Upgrade from 6.1.0 (4.0B) to 4.6B", "RefUrl": "/notes/332889"}, {"RefNumber": "327290", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade 46B/46C:R3trans termintn phase PATCH_CMDIMP", "RefUrl": "/notes/327290"}, {"RefNumber": "325196", "RefComponent": "XX-PI", "RefTitle": "R/3 Plug-in under Release 4.6A", "RefUrl": "/notes/325196"}, {"RefNumber": "315568", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/315568"}, {"RefNumber": "312909", "RefComponent": "BC-UPG", "RefTitle": "Phase TABIM_46B terminated on table T5W1Y", "RefUrl": "/notes/312909"}, {"RefNumber": "311448", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/311448"}, {"RefNumber": "310790", "RefComponent": "PY-JP", "RefTitle": "T512W backup&restore procedure on Upgrade&HR SP", "RefUrl": "/notes/310790"}, {"RefNumber": "307803", "RefComponent": "PA-OS", "RefTitle": "XPRA RHU40C01 cost distribution during upgrade", "RefUrl": "/notes/307803"}, {"RefNumber": "304809", "RefComponent": "BC-OP-FTS-REL", "RefTitle": "SAPCAR does not start", "RefUrl": "/notes/304809"}, {"RefNumber": "217526", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/217526"}, {"RefNumber": "216122", "RefComponent": "CA-JVA", "RefTitle": "Upgrade to R/3 4.6B with CA-JVA 4.61 and DEL-IS-OIL", "RefUrl": "/notes/216122"}, {"RefNumber": "214976", "RefComponent": "PA-OS", "RefTitle": "XPRA RHU40C01 sets time constraint K 1001 B011 incorrectly", "RefUrl": "/notes/214976"}, {"RefNumber": "214555", "RefComponent": "CA-JVA", "RefTitle": "Upgrade R/3 46B + CA-JVA 461 (not deinst. IS-OIL)", "RefUrl": "/notes/214555"}, {"RefNumber": "213477", "RefComponent": "BC-UPG-TLS", "RefTitle": "Logical databases lost after upgrade", "RefUrl": "/notes/213477"}, {"RefNumber": "209533", "RefComponent": "IS-A", "RefTitle": "Upgrade to R/3 4.6B with DI 46B", "RefUrl": "/notes/209533"}, {"RefNumber": "208859", "RefComponent": "IS-A", "RefTitle": "Release strategy note for the Add-on DI", "RefUrl": "/notes/208859"}, {"RefNumber": "208526", "RefComponent": "BC-UPG", "RefTitle": "Phase TABIM_46B terminated on T5ITGC(E,G) tables", "RefUrl": "/notes/208526"}, {"RefNumber": "207404", "RefComponent": "XX-CSC-ABPL", "RefTitle": "CSC-ABPL: Asian Best Practice Library 5.0.0", "RefUrl": "/notes/207404"}, {"RefNumber": "204338", "RefComponent": "CA-EUR-CNV", "RefTitle": "Problems with table EWUCLU after upgrade/euro changeover", "RefUrl": "/notes/204338"}, {"RefNumber": "204143", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/204143"}, {"RefNumber": "203094", "RefComponent": "BC-DWB-SEM", "RefTitle": "XPRA runtime for menu generation is too long", "RefUrl": "/notes/203094"}, {"RefNumber": "201440", "RefComponent": "BC-UPG-TLS", "RefTitle": "Termination in PREPARE phase TOOLIMP2", "RefUrl": "/notes/201440"}, {"RefNumber": "201355", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Additional info about the HR-PS add-on upgrade 461A to 4.6B", "RefUrl": "/notes/201355"}, {"RefNumber": "201044", "RefComponent": "WP-PLI", "RefTitle": "Upgrading WP component systems with WP-PI 2.00", "RefUrl": "/notes/201044"}, {"RefNumber": "199921", "RefComponent": "BC-SEC-USR", "RefTitle": "Missing authorizations after upgrade to 4.6A/B", "RefUrl": "/notes/199921"}, {"RefNumber": "199892", "RefComponent": "FS-CD", "RefTitle": "Additional info about R/3 4.6B upgrade with INSURANCE 4.61", "RefUrl": "/notes/199892"}, {"RefNumber": "197746", "RefComponent": "BC-FES-ITS", "RefTitle": "Maint. strategy: Internet Transaction Server (ITS)", "RefUrl": "/notes/197746"}, {"RefNumber": "197714", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgr. 4.6B: Termination phase SHADOWIMP or DIFFEXP*", "RefUrl": "/notes/197714"}, {"RefNumber": "197273", "RefComponent": "IS-B", "RefTitle": "Enhancements to upgrade BANKING 4.61", "RefUrl": "/notes/197273"}, {"RefNumber": "195033", "RefComponent": "BC-CUS-TOL-IMG", "RefTitle": "Migration of projects", "RefUrl": "/notes/195033"}, {"RefNumber": "193822", "RefComponent": "PT-SP", "RefTitle": "Dump: RHUMST_HRPADUZ, RHUMST_HRPADNN_HRPADUZ", "RefUrl": "/notes/193822"}, {"RefNumber": "193631", "RefComponent": "BC-DWB-SEM", "RefTitle": "XPRA runtime for menu generation is too long", "RefUrl": "/notes/193631"}, {"RefNumber": "192665", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/192665"}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227"}, {"RefNumber": "191270", "RefComponent": "PS-ST", "RefTitle": "Activating the indexes for table MLST", "RefUrl": "/notes/191270"}, {"RefNumber": "189938", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: SPAM termination in the GENERATE_BACKUP step", "RefUrl": "/notes/189938"}, {"RefNumber": "189672", "RefComponent": "CA-EUR-CNV", "RefTitle": "Cluster: REUCLDOC fixed-pt arithmetic not activated", "RefUrl": "/notes/189672"}, {"RefNumber": "189229", "RefComponent": "BC-SRV-ARL-INT", "RefTitle": "Report for upgrade 4.5ff with open WIs for TS7869", "RefUrl": "/notes/189229"}, {"RefNumber": "188473", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Simp. Chinese NT", "RefUrl": "/notes/188473"}, {"RefNumber": "188472", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Trad. Chinese NT", "RefUrl": "/notes/188472"}, {"RefNumber": "188470", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Korean NT", "RefUrl": "/notes/188470"}, {"RefNumber": "188469", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Japanese NT", "RefUrl": "/notes/188469"}, {"RefNumber": "188139", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Thai", "RefUrl": "/notes/188139"}, {"RefNumber": "187917", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Simp. Chinese UNIX", "RefUrl": "/notes/187917"}, {"RefNumber": "187916", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Trad. Chinese UNIX", "RefUrl": "/notes/187916"}, {"RefNumber": "187910", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Korean UNIX", "RefUrl": "/notes/187910"}, {"RefNumber": "187907", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Japanese UNIX", "RefUrl": "/notes/187907"}, {"RefNumber": "187034", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCC1/2: Sent documents are cleared", "RefUrl": "/notes/187034"}, {"RefNumber": "186773", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "DDIC objects cannot be displayed in the upgrade", "RefUrl": "/notes/186773"}, {"RefNumber": "185836", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/185836"}, {"RefNumber": "183407", "RefComponent": "BC-UPG-OCS", "RefTitle": "Termtn HR Supp.Pack.(HR LCP) in SPAM queue / upgrade", "RefUrl": "/notes/183407"}, {"RefNumber": "181248", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181248"}, {"RefNumber": "180456", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6B SAP DB", "RefUrl": "/notes/180456"}, {"RefNumber": "179982", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.6B online documentation", "RefUrl": "/notes/179982"}, {"RefNumber": "179675", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.6B      INFORMIX", "RefUrl": "/notes/179675"}, {"RefNumber": "179582", "RefComponent": "BC-CTS-LAN", "RefTitle": "Storage space required for 4.6B language import", "RefUrl": "/notes/179582"}, {"RefNumber": "179580", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note on language import 4.6B", "RefUrl": "/notes/179580"}, {"RefNumber": "179532", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/179532"}, {"RefNumber": "179376", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions Upgrade to 4.6B  ORACLE", "RefUrl": "/notes/179376"}, {"RefNumber": "179263", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/179263"}, {"RefNumber": "178823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to 4.6B - DB2/400", "RefUrl": "/notes/178823"}, {"RefNumber": "178759", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB6: Enhancements to upgrade to 4.6B", "RefUrl": "/notes/178759"}, {"RefNumber": "178725", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 4.5 to 4.6", "RefUrl": "/notes/178725"}, {"RefNumber": "178482", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 3.0 to 4.0", "RefUrl": "/notes/178482"}, {"RefNumber": "178452", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 4.0 to 4.5", "RefUrl": "/notes/178452"}, {"RefNumber": "178168", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/178168"}, {"RefNumber": "174073", "RefComponent": "BC-CUS", "RefTitle": "Missing release notes after upgrade", "RefUrl": "/notes/174073"}, {"RefNumber": "173330", "RefComponent": "BC-UPG-TLS", "RefTitle": "Conversion of tables EDIDOC & CDCLS Release 4.6", "RefUrl": "/notes/173330"}, {"RefNumber": "162980", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol for 64-bit Solaris", "RefUrl": "/notes/162980"}, {"RefNumber": "159065", "RefComponent": "BC-BMT-WFM", "RefTitle": "Data selection for work item archiving", "RefUrl": "/notes/159065"}, {"RefNumber": "157755", "RefComponent": "IS-R", "RefTitle": "Using R/3 as SAP Retail (Release 4.6A/B 4.6C 4.70 5.00)", "RefUrl": "/notes/157755"}, {"RefNumber": "156568", "RefComponent": "PY-DE-CI", "RefTitle": "Data loss of T5DB4 after upgrade to 4.0", "RefUrl": "/notes/156568"}, {"RefNumber": "153205", "RefComponent": "BC-BMT-WFM", "RefTitle": "Direct deletion of type C work items", "RefUrl": "/notes/153205"}, {"RefNumber": "150644", "RefComponent": "SD-MD-CM", "RefTitle": "Condition tables A518 and A777 were delivered", "RefUrl": "/notes/150644"}, {"RefNumber": "148926", "RefComponent": "BC-OP-FTS", "RefTitle": "saposcol version 32 or 64 bit on ReliantUNIX", "RefUrl": "/notes/148926"}, {"RefNumber": "147610", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "Table T5UT4 is displayed in SPDD for adjustment", "RefUrl": "/notes/147610"}, {"RefNumber": "145794", "RefComponent": "BC-MID-ALE", "RefTitle": "LONGPOST.LOG:  Error filling table \"STACUST\"", "RefUrl": "/notes/145794"}, {"RefNumber": "145291", "RefComponent": "BC-BMT-WFM", "RefTitle": "Performance of deletion from archive file", "RefUrl": "/notes/145291"}, {"RefNumber": "140313", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Phase ACTREF_CHK in PREPARE module act.checks", "RefUrl": "/notes/140313"}, {"RefNumber": "140280", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Phase JOB_RADDRCHK in PREPARE module act.checks", "RefUrl": "/notes/140280"}, {"RefNumber": "140071", "RefComponent": "BC-UPG", "RefTitle": "PCON_<rel>: R3up reports termins. in R/3 System", "RefUrl": "/notes/140071"}, {"RefNumber": "139514", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "ICNV prior to the upgrade to Release 4.6A/B", "RefUrl": "/notes/139514"}, {"RefNumber": "138284", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spool: invalid entry for output device", "RefUrl": "/notes/138284"}, {"RefNumber": "137607", "RefComponent": "BC-UPG-TLS", "RefTitle": "R/3 Upgrade Assistant: stack overflow exception", "RefUrl": "/notes/137607"}, {"RefNumber": "137599", "RefComponent": "BC-UPG-TLS", "RefTitle": "NO R/3 Upgrade Assistant on Digital UNIX 3.2", "RefUrl": "/notes/137599"}, {"RefNumber": "137594", "RefComponent": "BC-UPG-TLS", "RefTitle": "R/3 Upgrade Assistant: no connection to server", "RefUrl": "/notes/137594"}, {"RefNumber": "137590", "RefComponent": "BC-UPG-TLS", "RefTitle": "Starting Upgr. Ass. from within MS Intnt Expl. 4.0", "RefUrl": "/notes/137590"}, {"RefNumber": "137587", "RefComponent": "BC-UPG-TLS", "RefTitle": "R/3 upgrade Assistant: PR<PERSON><PERSON><PERSON> stops", "RefUrl": "/notes/137587"}, {"RefNumber": "133954", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the R/3 HR-PS add-on", "RefUrl": "/notes/133954"}, {"RefNumber": "133017", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/133017"}, {"RefNumber": "119738", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/119738"}, {"RefNumber": "111601", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Ready-to-Run R/3: Information on the upgrade", "RefUrl": "/notes/111601"}, {"RefNumber": "102247", "RefComponent": "IS-B", "RefTitle": "Release strategy for the SAP R/3 BANKING Add-On", "RefUrl": "/notes/102247"}, {"RefNumber": "100104", "RefComponent": "FS-CD", "RefTitle": "Release strategy for SAP Insurance", "RefUrl": "/notes/100104"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "178823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to 4.6B - DB2/400", "RefUrl": "/notes/178823 "}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227 "}, {"RefNumber": "197746", "RefComponent": "BC-FES-ITS", "RefTitle": "Maint. strategy: Internet Transaction Server (ITS)", "RefUrl": "/notes/197746 "}, {"RefNumber": "189229", "RefComponent": "BC-SRV-ARL-INT", "RefTitle": "Report for upgrade 4.5ff with open WIs for TS7869", "RefUrl": "/notes/189229 "}, {"RefNumber": "97032", "RefComponent": "SD-MD", "RefTitle": "Addresses: Preliminary conversion in Release 4.5/4.6/4.7", "RefUrl": "/notes/97032 "}, {"RefNumber": "207404", "RefComponent": "XX-CSC-ABPL", "RefTitle": "CSC-ABPL: Asian Best Practice Library 5.0.0", "RefUrl": "/notes/207404 "}, {"RefNumber": "138284", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spool: invalid entry for output device", "RefUrl": "/notes/138284 "}, {"RefNumber": "140071", "RefComponent": "BC-UPG", "RefTitle": "PCON_<rel>: R3up reports termins. in R/3 System", "RefUrl": "/notes/140071 "}, {"RefNumber": "304809", "RefComponent": "BC-OP-FTS-REL", "RefTitle": "SAPCAR does not start", "RefUrl": "/notes/304809 "}, {"RefNumber": "512241", "RefComponent": "BC-CTS-TMS", "RefTitle": "Syntax error in the SAPLTMSC program while upgrading", "RefUrl": "/notes/512241 "}, {"RefNumber": "369542", "RefComponent": "PS-REV-IO", "RefTitle": "CJA1: Upgrade of Release 4.0 preliminary solution to >= 4.5", "RefUrl": "/notes/369542 "}, {"RefNumber": "394331", "RefComponent": "BC-UPG-NA", "RefTitle": "Upgrading to 4.X: Error message with CWB* tables", "RefUrl": "/notes/394331 "}, {"RefNumber": "178725", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 4.5 to 4.6", "RefUrl": "/notes/178725 "}, {"RefNumber": "173330", "RefComponent": "BC-UPG-TLS", "RefTitle": "Conversion of tables EDIDOC & CDCLS Release 4.6", "RefUrl": "/notes/173330 "}, {"RefNumber": "137590", "RefComponent": "BC-UPG-TLS", "RefTitle": "Starting Upgr. Ass. from within MS Intnt Expl. 4.0", "RefUrl": "/notes/137590 "}, {"RefNumber": "179675", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.6B      INFORMIX", "RefUrl": "/notes/179675 "}, {"RefNumber": "86627", "RefComponent": "MM-IM", "RefTitle": "Movement types: Customizing during release upgrade", "RefUrl": "/notes/86627 "}, {"RefNumber": "201355", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Additional info about the HR-PS add-on upgrade 461A to 4.6B", "RefUrl": "/notes/201355 "}, {"RefNumber": "216122", "RefComponent": "CA-JVA", "RefTitle": "Upgrade to R/3 4.6B with CA-JVA 4.61 and DEL-IS-OIL", "RefUrl": "/notes/216122 "}, {"RefNumber": "214555", "RefComponent": "CA-JVA", "RefTitle": "Upgrade R/3 46B + CA-JVA 461 (not deinst. IS-OIL)", "RefUrl": "/notes/214555 "}, {"RefNumber": "195033", "RefComponent": "BC-CUS-TOL-IMG", "RefTitle": "Migration of projects", "RefUrl": "/notes/195033 "}, {"RefNumber": "78432", "RefComponent": "XX-CSC-IN", "RefTitle": "Release strategy of country version India (CIN)", "RefUrl": "/notes/78432 "}, {"RefNumber": "199892", "RefComponent": "FS-CD", "RefTitle": "Additional info about R/3 4.6B upgrade with INSURANCE 4.61", "RefUrl": "/notes/199892 "}, {"RefNumber": "100104", "RefComponent": "FS-CD", "RefTitle": "Release strategy for SAP Insurance", "RefUrl": "/notes/100104 "}, {"RefNumber": "197273", "RefComponent": "IS-B", "RefTitle": "Enhancements to upgrade BANKING 4.61", "RefUrl": "/notes/197273 "}, {"RefNumber": "102247", "RefComponent": "IS-B", "RefTitle": "Release strategy for the SAP R/3 BANKING Add-On", "RefUrl": "/notes/102247 "}, {"RefNumber": "153205", "RefComponent": "BC-BMT-WFM", "RefTitle": "Direct deletion of type C work items", "RefUrl": "/notes/153205 "}, {"RefNumber": "179580", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note on language import 4.6B", "RefUrl": "/notes/179580 "}, {"RefNumber": "201044", "RefComponent": "WP-PLI", "RefTitle": "Upgrading WP component systems with WP-PI 2.00", "RefUrl": "/notes/201044 "}, {"RefNumber": "208859", "RefComponent": "IS-A", "RefTitle": "Release strategy note for the Add-on DI", "RefUrl": "/notes/208859 "}, {"RefNumber": "534826", "RefComponent": "LO-LIS-DC", "RefTitle": "LIS: Current update programs after upgrade", "RefUrl": "/notes/534826 "}, {"RefNumber": "157755", "RefComponent": "IS-R", "RefTitle": "Using R/3 as SAP Retail (Release 4.6A/B 4.6C 4.70 5.00)", "RefUrl": "/notes/157755 "}, {"RefNumber": "186773", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "DDIC objects cannot be displayed in the upgrade", "RefUrl": "/notes/186773 "}, {"RefNumber": "183407", "RefComponent": "BC-UPG-OCS", "RefTitle": "Termtn HR Supp.Pack.(HR LCP) in SPAM queue / upgrade", "RefUrl": "/notes/183407 "}, {"RefNumber": "204338", "RefComponent": "CA-EUR-CNV", "RefTitle": "Problems with table EWUCLU after upgrade/euro changeover", "RefUrl": "/notes/204338 "}, {"RefNumber": "213477", "RefComponent": "BC-UPG-TLS", "RefTitle": "Logical databases lost after upgrade", "RefUrl": "/notes/213477 "}, {"RefNumber": "360929", "RefComponent": "PM-WOC-MN", "RefTitle": "Loss of address data on execution of RSXADR05", "RefUrl": "/notes/360929 "}, {"RefNumber": "546736", "RefComponent": "PP-REM-ORD", "RefTitle": "PPREM_XPRA_NO_RSH: Very long runtime", "RefUrl": "/notes/546736 "}, {"RefNumber": "191270", "RefComponent": "PS-ST", "RefTitle": "Activating the indexes for table MLST", "RefUrl": "/notes/191270 "}, {"RefNumber": "179582", "RefComponent": "BC-CTS-LAN", "RefTitle": "Storage space required for 4.6B language import", "RefUrl": "/notes/179582 "}, {"RefNumber": "209533", "RefComponent": "IS-A", "RefTitle": "Upgrade to R/3 4.6B with DI 46B", "RefUrl": "/notes/209533 "}, {"RefNumber": "150644", "RefComponent": "SD-MD-CM", "RefTitle": "Condition tables A518 and A777 were delivered", "RefUrl": "/notes/150644 "}, {"RefNumber": "188139", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Thai", "RefUrl": "/notes/188139 "}, {"RefNumber": "188469", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Japanese NT", "RefUrl": "/notes/188469 "}, {"RefNumber": "188470", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Korean NT", "RefUrl": "/notes/188470 "}, {"RefNumber": "188472", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Trad. Chinese NT", "RefUrl": "/notes/188472 "}, {"RefNumber": "188473", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Simp. Chinese NT", "RefUrl": "/notes/188473 "}, {"RefNumber": "187917", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Simp. Chinese UNIX", "RefUrl": "/notes/187917 "}, {"RefNumber": "187916", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Trad. Chinese UNIX", "RefUrl": "/notes/187916 "}, {"RefNumber": "187910", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Korean UNIX", "RefUrl": "/notes/187910 "}, {"RefNumber": "187907", "RefComponent": "BC-INS", "RefTitle": "R/3 4.6B Installation/Upgrade Japanese UNIX", "RefUrl": "/notes/187907 "}, {"RefNumber": "133954", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for the R/3 HR-PS add-on", "RefUrl": "/notes/133954 "}, {"RefNumber": "178759", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB6: Enhancements to upgrade to 4.6B", "RefUrl": "/notes/178759 "}, {"RefNumber": "383126", "RefComponent": "BC-SRV-BSF-CUR", "RefTitle": "Additions upgrade: expiring currencies", "RefUrl": "/notes/383126 "}, {"RefNumber": "49545", "RefComponent": "BC-BMT-WFM", "RefTitle": "Deletion of work items that are no longer required", "RefUrl": "/notes/49545 "}, {"RefNumber": "332889", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-Oil: Upgrade from 6.1.0 (4.0B) to 4.6B", "RefUrl": "/notes/332889 "}, {"RefNumber": "178452", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 4.0 to 4.5", "RefUrl": "/notes/178452 "}, {"RefNumber": "178482", "RefComponent": "BC-ABA-LA", "RefTitle": "Incompatible ABAP changes from Release 3.0 to 4.0", "RefUrl": "/notes/178482 "}, {"RefNumber": "533777", "RefComponent": "BC-ABA-TO", "RefTitle": "Upgrade problem after implementing note 508520", "RefUrl": "/notes/533777 "}, {"RefNumber": "515957", "RefComponent": "BC-UPG-OCS", "RefTitle": "Activation errors for the RFPDO1 table (AD857)", "RefUrl": "/notes/515957 "}, {"RefNumber": "145794", "RefComponent": "BC-MID-ALE", "RefTitle": "LONGPOST.LOG:  Error filling table \"STACUST\"", "RefUrl": "/notes/145794 "}, {"RefNumber": "187034", "RefComponent": "FI-GL-GL-M", "RefTitle": "ALE FIDCC1/2: Sent documents are cleared", "RefUrl": "/notes/187034 "}, {"RefNumber": "69035", "RefComponent": "FI-FM-IS", "RefTitle": "Drilldown reporting: Reports cannot be executed", "RefUrl": "/notes/69035 "}, {"RefNumber": "180456", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6B SAP DB", "RefUrl": "/notes/180456 "}, {"RefNumber": "162980", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol for 64-bit Solaris", "RefUrl": "/notes/162980 "}, {"RefNumber": "148926", "RefComponent": "BC-OP-FTS", "RefTitle": "saposcol version 32 or 64 bit on ReliantUNIX", "RefUrl": "/notes/148926 "}, {"RefNumber": "83728", "RefComponent": "PY-DE", "RefTitle": "Check table T510Q before the upgrade", "RefUrl": "/notes/83728 "}, {"RefNumber": "310790", "RefComponent": "PY-JP", "RefTitle": "T512W backup&restore procedure on Upgrade&HR SP", "RefUrl": "/notes/310790 "}, {"RefNumber": "199921", "RefComponent": "BC-SEC-USR", "RefTitle": "Missing authorizations after upgrade to 4.6A/B", "RefUrl": "/notes/199921 "}, {"RefNumber": "214976", "RefComponent": "PA-OS", "RefTitle": "XPRA RHU40C01 sets time constraint K 1001 B011 incorrectly", "RefUrl": "/notes/214976 "}, {"RefNumber": "311448", "RefComponent": "BC-UPG-RDM", "RefTitle": "Supplement to upgrading 3.1G to 4.6B/4.6C", "RefUrl": "/notes/311448 "}, {"RefNumber": "325196", "RefComponent": "XX-PI", "RefTitle": "R/3 Plug-in under Release 4.6A", "RefUrl": "/notes/325196 "}, {"RefNumber": "145291", "RefComponent": "BC-BMT-WFM", "RefTitle": "Performance of deletion from archive file", "RefUrl": "/notes/145291 "}, {"RefNumber": "111601", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Ready-to-Run R/3: Information on the upgrade", "RefUrl": "/notes/111601 "}, {"RefNumber": "203094", "RefComponent": "BC-DWB-SEM", "RefTitle": "XPRA runtime for menu generation is too long", "RefUrl": "/notes/203094 "}, {"RefNumber": "139514", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "ICNV prior to the upgrade to Release 4.6A/B", "RefUrl": "/notes/139514 "}, {"RefNumber": "197714", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgr. 4.6B: Termination phase SHADOWIMP or DIFFEXP*", "RefUrl": "/notes/197714 "}, {"RefNumber": "208526", "RefComponent": "BC-UPG", "RefTitle": "Phase TABIM_46B terminated on T5ITGC(E,G) tables", "RefUrl": "/notes/208526 "}, {"RefNumber": "137587", "RefComponent": "BC-UPG-TLS", "RefTitle": "R/3 upgrade Assistant: PR<PERSON><PERSON><PERSON> stops", "RefUrl": "/notes/137587 "}, {"RefNumber": "137594", "RefComponent": "BC-UPG-TLS", "RefTitle": "R/3 Upgrade Assistant: no connection to server", "RefUrl": "/notes/137594 "}, {"RefNumber": "137599", "RefComponent": "BC-UPG-TLS", "RefTitle": "NO R/3 Upgrade Assistant on Digital UNIX 3.2", "RefUrl": "/notes/137599 "}, {"RefNumber": "137607", "RefComponent": "BC-UPG-TLS", "RefTitle": "R/3 Upgrade Assistant: stack overflow exception", "RefUrl": "/notes/137607 "}, {"RefNumber": "327290", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade 46B/46C:R3trans termintn phase PATCH_CMDIMP", "RefUrl": "/notes/327290 "}, {"RefNumber": "201440", "RefComponent": "BC-UPG-TLS", "RefTitle": "Termination in PREPARE phase TOOLIMP2", "RefUrl": "/notes/201440 "}, {"RefNumber": "174073", "RefComponent": "BC-CUS", "RefTitle": "Missing release notes after upgrade", "RefUrl": "/notes/174073 "}, {"RefNumber": "179376", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions Upgrade to 4.6B  ORACLE", "RefUrl": "/notes/179376 "}, {"RefNumber": "312909", "RefComponent": "BC-UPG", "RefTitle": "Phase TABIM_46B terminated on table T5W1Y", "RefUrl": "/notes/312909 "}, {"RefNumber": "307803", "RefComponent": "PA-OS", "RefTitle": "XPRA RHU40C01 cost distribution during upgrade", "RefUrl": "/notes/307803 "}, {"RefNumber": "179982", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.6B online documentation", "RefUrl": "/notes/179982 "}, {"RefNumber": "189938", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: SPAM termination in the GENERATE_BACKUP step", "RefUrl": "/notes/189938 "}, {"RefNumber": "189672", "RefComponent": "CA-EUR-CNV", "RefTitle": "Cluster: REUCLDOC fixed-pt arithmetic not activated", "RefUrl": "/notes/189672 "}, {"RefNumber": "193822", "RefComponent": "PT-SP", "RefTitle": "Dump: RHUMST_HRPADUZ, RHUMST_HRPADNN_HRPADUZ", "RefUrl": "/notes/193822 "}, {"RefNumber": "193631", "RefComponent": "BC-DWB-SEM", "RefTitle": "XPRA runtime for menu generation is too long", "RefUrl": "/notes/193631 "}, {"RefNumber": "74831", "RefComponent": "BC-CCM-MON", "RefTitle": "SPDD:Transports QE1K900002, TCVK900029, EWSK900052", "RefUrl": "/notes/74831 "}, {"RefNumber": "156568", "RefComponent": "PY-DE-CI", "RefTitle": "Data loss of T5DB4 after upgrade to 4.0", "RefUrl": "/notes/156568 "}, {"RefNumber": "159065", "RefComponent": "BC-BMT-WFM", "RefTitle": "Data selection for work item archiving", "RefUrl": "/notes/159065 "}, {"RefNumber": "147610", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "Table T5UT4 is displayed in SPDD for adjustment", "RefUrl": "/notes/147610 "}, {"RefNumber": "140313", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Phase ACTREF_CHK in PREPARE module act.checks", "RefUrl": "/notes/140313 "}, {"RefNumber": "140280", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Phase JOB_RADDRCHK in PREPARE module act.checks", "RefUrl": "/notes/140280 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}