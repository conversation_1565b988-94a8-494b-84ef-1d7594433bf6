{"guid": "901B0E6D3EED1ED78CFA20C4590EA0CB", "sitemId": "SI01: TM_CUSTOMIZING", "sitemTitle": "S4TWL - Simplified customizing of Transportation Manangement (TM) integration", "note": 2465978, "noteTitle": "2465978 - S4TWL : Impact on TM process with conversion of SAP ERP or upgrade of S/4HANA 1511/1610 to S/4HANA 1709", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use SAP ERP Enhancement Package 4, 5, 6, 7 or 8 or S/4HANA 1511 or S/4HANA 1610 for freight settlement process integration with Materials Management(MM) with SAP TM in a side-by-side mode.</p>\n<p>As a part of integration, there are mappings maintained for freight settlement document (FSD) type to purchase order (PO) Type, material group, plant in the view TCM_V_PUR_MAP.</p>\n<p>There are entries for the same FSD type mapped to different PO type / material group / plant OR there are entries where FSD type is blank.</p>\n<p>The system conversion is planned to S/4HANA 1709 or system upgrade is planned from S/4HANA 1511 or S/4HANA 1610 to S/4HANA 1709.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>System conversion, S/4HANA, SAP TM, Freight settlement document transfer, FSD transfer, Freight settlement document posting, FSD posting, TM process, TM - ERP integration, TM Internal in S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value :</strong></p>\n<p>Since Transportation Management (TM) is available internally as part of S/4HANA 1709, customers can now have the TM integration in a single system also.  Also this has impact to the side-by-side integration with S/4HANA 1709 onwards.</p>\n<p>The mapping requirement for the posting of the freight settlement document in S/4HANA 1709 is now changed.  From S/4HANA 1709 release onwards, the usage of the view TCM_V_PUR_MAP and the underlying customizing table TCM_C_PUR_MAP have been discontinued.</p>\n<p><strong>Description :</strong></p>\n<p>In a side by side scenario, with S/4HANA 1709, there are the following changes required.</p>\n<p>1. The mapping of purchase organization unit from TM org data to the MM Purchase organization is no longer required in S/4HANA.  SAP TM sends this mapping information with the message type: TransportationOrderSUITEInvoicingPreparationRequest used in the A2A service interface: TransportationOrderSUITEInvoicingPreparationRequest_In on S/4HANA.</p>\n<p>2. The other information for posting of the freight settlement document such as PO type, plant and material group are now maintained in the maintanence view based on the /SCMTMS/C_SFIR_T table using the customizing activity <strong><em>Assign Purchasing Information for Posting </em></strong>under <strong>Transportation Management &gt; Settlement &gt; Freight Settlement &gt; Integration for Settlement Posting</strong>.  You now maintain the PO type, plant and material group at the freight settlement document type level.</p>\n<p>System conversion or upgrade shall take care of copying the entries from the source table to the target table which is /SCMTMS/C_SFIR_T table. For the copy to happen successfully, there should not be conflicting entries for the same FSD type or entries with blank FSD types in the source table i.e. in table TCM_C_PUR_MAP.</p>\n<p><strong>Business process related information :</strong></p>\n<p>Freight settlement document from stand alone SAP TM is transferred to S/4HANA 1709 for posting using A2A service interface : TransportationOrderSUITEInvoicingPreparationRequest_In.</p>\n<p><strong>Required and Recommended action(s) :</strong></p>\n<p>In order to ensure that the data available in TCM_C_PUR_MAP table is consistent enough to be copied into the target table /SCMTMS/C_SFIR_T via an XPRA which will be executed during the upgrade / system conversion process, it is required that the source table doesn't contain :</p>\n<p>1. Entries where FSD type is blank</p>\n<p>2. Entries for the same FSD type to different PO type / plant / material group.</p>\n<p>If you have any of the above combinations of data in the source table, Review these entries to adjust the right settings either by</p>\n<p>1. Deleting the duplicate FSD type records</p>\n<p>2. Deleting the records with blank FSD type or specifying the FSD type in such entries.  To add the FSD type where FSD type is blank, you will need to copy such records in the source table and enter the FSD type before saving and then followed by deletion of records which are with blank FSD types.</p>\n<p><strong>NOTE:</strong>  Before conversion please also check if there are FSDs in the source SAP TM system where these combination in conflict are required for posting of the settlement document. In case, kindly ensure that the above mentioned corrections are made keeping these yet to be processed FSDs for posting.</p>\n<p><strong>How to determine relevancy :</strong></p>\n<p>In order to ensure that the data available in TCM_C_PUR_MAP table is consistent enough to be copied into the target table /SCMTMS/C_SFIR_T via an XPRA which will be executed during the upgrade / system conversion process, it is required that the source table doesn't contain :</p>\n<p>1. Entries where FSD type is blank</p>\n<p>2. Entries for the same FSD type but mapped to different PO type / plant / material group.</p>\n<p> </p>\n<p> </p>", "noteVersion": 5, "refer_note": [{"note": "2583557", "noteTitle": "2583557 - Change the value of Plant during the accruals posting of freight settlement/Credit memo/Charge Correction Advice documents to S4 ERP system", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are Posting/Transferring freight settlement document, Credit Memo, Charge correction advice for accruals and ERP system is S4 HANA 1709 and above.</p>\n<p>while posting the Freight settlement, Credit Memo, or Charge correction advice document for accruals. The standard SAPTM system allows only one plant per settlement document type which is maintained at(ERP S4):</p>\n<p>In the IMG activity <strong>Define Freight settlement document types</strong> under Transportation Management -&gt; Settlement -&gt; Freight Settlement -&gt; Integration for Settlement Posting -&gt;  Assign Purchasing Information for Posting. Here we assign the Plant to FSD Type</p>\n<p>The same plant is passed in the  Service Purchase order Item.</p>\n<p>If your business requirement needs you to have different plants for the same Freight settlement document type. Carry out the following activities to change it to some other value of plant during execution.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Freight settlement document type, Credit memo, Charge correction advice, Plant, Service Purchase order, Service Purchase order item</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Consulting Note describing the Settings and enhancements necessary to realize this requirement.</p>\n<p>You are using SAP TM S4 1709 and above system for Freight settlement functionality.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Customers having Embedded posting scenario (TM and ECC are in one system  can follow below solution.</p>\n<p>-Customers using S4 HANA 1809 and above can implement BADI /SCMTMS/BADI_MODIFY_PO_DATA to overwrite the Purchase order data while posting the FSD for accruals.</p>\n<p>-Customers with S4 HANA 1709 needs to follow below steps to modify the plant information while posting FSD for accruals</p>\n<p>Step1: Implement the <a href=\"/notes/2580123\" target=\"_blank\">SAP Note 2580123</a>  if not already applied in the system.</p>\n<p>Step2: Create Post exit for the method get_sfir_type of class  /SCMTMS/CL_SFIR_CUST and overwrite the value of plant using it</p>\n<p>You can refer the enhancement guide for doing the same</p>\n<p><a href=\"https://help.sap.com/doc/PRODUCTION/c7dab057b713442294901f1f7cbe069d/9.5.0/en-US/TM9xEnhancementGuide_2017.pdf\" target=\"_blank\">https://help.sap.com/doc/PRODUCTION/c7dab057b713442294901f1f7cbe069d/9.5.0/en-US/TM9xEnhancementGuide_2017.pdf</a></p>\n<p>or follow the following steps:</p>\n<p>a. Go to Se24-&gt; give class name  /SCMTMS/CL_SFIR_CUST -&gt;Display</p>\n<p>b. Put cursor on the method GET_SFIR_TYPE and navigate to Class -&gt;Enhance</p>\n<p>c. Create Enhancement implementation.</p>\n<p>d. Go to Edit-&gt;Enhancement Operations -&gt;Insert Post-Method-&gt;Access to Private/Protected Components of Class<br/>   /SCMTMS/CL_SFIR_CUST?-&gt;yes</p>\n<p>e. There will be a post exit created for the method open it. and write the desired logic to replace the value of the field PLANT in changing parameter ES_SFIRTYPE with the new value.</p>\n<p>Customers who are having two separate systems for TM and ECC process  can use the already known BADI <strong>TCM_SE_TORINVPREPREQ_ASYN </strong> for modifying posting information.</p>\n<p> </p>\n<p> </p>", "noteVersion": 5, "refer_note": [{"note": "2580123", "noteTitle": "2580123 - Provide a single access for reading the Customizing for freight settlement document type and remove the database selects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>There are multiple select statements on Frieght settlement document type customizing making it dificult to have a single access point for customizing</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Freight settlement document type, SFIR, Customizing</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Maintainance</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the note or upgrade to relvant SP.</p>", "noteVersion": 1}]}], "activities": [{"Activity": "Data correction", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Run conversion pre-check and correct any found inconsistencies"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}