{"Request": {"Number": "1642677", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 214, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017326892017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001642677?language=E&token=1689B9595932198D3DE82DDB43A559AF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001642677", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001642677/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1642677"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.04.2017"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Flexible Real Estate Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1642677 - Useful control parameters in RE-FX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note provides information about useful settings in RE-FX. It is updated continuously.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SET/GET parameter, customizable messages, message control, user interface control, tips, tricks, FAQ</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>BAdIs: </strong></p>\r\n<p><strong>Date Until Which the Cash Flow Is Locked:</strong> By setting the &#x201C;Date Until Which the Cash Flow Is Locked\" in the runtime data of the real estate contract or the rental object, you can prevent planned records from originating for past periods during cash flow generation.&#x00A0;To display the field, proceed as follows:</p>\r\n<ul>\r\n<li>Use transaction REBDRO0002 to add the following additional field for field group 15 (Start Date of Cash Flow): REBDROFLDS - CFLOCKEDTO. Set the \"Input field\" indicator.</li>\r\n<li>By default, the field is hidden. To display the field, you have to implement the BAdI BADI_RECN_CONTRACT (for the real estate contract) or the BAdI BADI_REBD_RENTAL_OBJECT (for the rental object). To do this, set the parameter CF_CFLOCKEDTO_DISABLED to ' ' (initial) in the method GET_BEHAVIOR_CONTEXT.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p><strong>SET/GET parameters (User Profile -&gt; Own Data -&gt; Parameters):</strong></p>\r\n<ul>\r\n<li>REBDCOLLAPSEFIXTURE Fixtures and fittings characteristics: Collapse (X) or expand (SPACE) characteristic tree.</li>\r\n</ul>\r\n<ul>\r\n<li>RECNMSDETAILOFF Expand (SPACE) or collapse (X) differing measurements in contract.</li>\r\n</ul>\r\n<ul>\r\n<li>RECAMSGLISTBYSAVE Execute checks again when saving RE objects: All checks have to be executed again to ensure the information messages and warning messages are also displayed in the error list when saving. This may cause performance problems. If RECAMSGLISTBYSAVE is set to ' ' (SPACE), the system does not execute the check again when saving. If you also want to display the error list when saving, set the user parameter RECAMSGLISTBYSAVE to 'X'.</li>\r\n</ul>\r\n<ul>\r\n<li>RECAWBCROSSSUFORBE: RE Navigator: also display the cross-business-entity settlement units in the navigation tree for subordinate objects of a business entity.</li>\r\n</ul>\r\n<ul>\r\n<li>RECARGIGNOREFORMSG: Hide message RECABT 025 (see SAP Note 1776358 for details).</li>\r\n</ul>\r\n<p><br /><strong>Configurable messages that change the system response:</strong></p>\r\n<ul>\r\n<li>Accounting:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Message REEXFI 152: Check BSEG-VERTN against customer account (SAP Notes 1612480, 1625832).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Adjustment:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Message REAJME 099: Permissibility of adjustments after end date of object (SAP Note 1584252).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Messages REAJME035 and REAJME036: Create adjustment record, even if no adjustment is made (SAP Note 1326819).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Conditions:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Message RECD277: Prevent use of obsolete conditions for automatic processes (SAP Note 1709132).</li>\r\n</ul>\r\n</ul>\r\n<p><strong>RE80 control</strong></p>\r\n<ul>\r\n<li>Hide finished objects using the layout (SAP Note 1784653).</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D002072)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D002072)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001642677/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001642677/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001642677/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001642677/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001642677/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001642677/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001642677/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001642677/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001642677/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "1856430", "RefComponent": "RE-FX-BD", "RefTitle": "Set default for registration dialog box", "RefUrl": "/notes/1856430"}, {"RefNumber": "1784653", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Objects with validity in the past", "RefUrl": "/notes/1784653"}, {"RefNumber": "1776358", "RefComponent": "RE-FX", "RefTitle": "Registrations: Controlling message RECABT 025", "RefUrl": "/notes/1776358"}, {"RefNumber": "1709132", "RefComponent": "RE-FX-CF", "RefTitle": "Marking condition type as obsolete", "RefUrl": "/notes/1709132"}, {"RefNumber": "1708101", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Cross-business entity SU in navigation tree", "RefUrl": "/notes/1708101"}, {"RefNumber": "1663018", "RefComponent": "RE-FX", "RefTitle": "Composite SAP Note for Customer Connection Real Estate 2011/2012", "RefUrl": "/notes/1663018"}, {"RefNumber": "1659901", "RefComponent": "RE-FX-AJ", "RefTitle": "Customizable messages for the object selection", "RefUrl": "/notes/1659901"}, {"RefNumber": "1604196", "RefComponent": "RE-FX-CN", "RefTitle": "RECN: Detailed display for differing measurements", "RefUrl": "/notes/1604196"}, {"RefNumber": "1525735", "RefComponent": "RE-FX-BD", "RefTitle": "Correction report vacancy management: Message display", "RefUrl": "/notes/1525735"}, {"RefNumber": "1326819", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment even if only reduction/increase allowed", "RefUrl": "/notes/1326819"}, {"RefNumber": "1315287", "RefComponent": "RE-FX-BD", "RefTitle": "Search option in tree when assigning fixtures+fittings char", "RefUrl": "/notes/1315287"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1980983", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Performance problems with large business entity", "RefUrl": "/notes/1980983 "}, {"RefNumber": "1920843", "RefComponent": "RE-FX-BD", "RefTitle": "Object assignment: Editing of existing assignments", "RefUrl": "/notes/1920843 "}, {"RefNumber": "1856430", "RefComponent": "RE-FX-BD", "RefTitle": "Set default for registration dialog box", "RefUrl": "/notes/1856430 "}, {"RefNumber": "1784653", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Objects with validity in the past", "RefUrl": "/notes/1784653 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "1326819", "RefComponent": "RE-FX-AJ", "RefTitle": "Adjustment even if only reduction/increase allowed", "RefUrl": "/notes/1326819 "}, {"RefNumber": "1776358", "RefComponent": "RE-FX", "RefTitle": "Registrations: Controlling message RECABT 025", "RefUrl": "/notes/1776358 "}, {"RefNumber": "1709132", "RefComponent": "RE-FX-CF", "RefTitle": "Marking condition type as obsolete", "RefUrl": "/notes/1709132 "}, {"RefNumber": "1659901", "RefComponent": "RE-FX-AJ", "RefTitle": "Customizable messages for the object selection", "RefUrl": "/notes/1659901 "}, {"RefNumber": "1663018", "RefComponent": "RE-FX", "RefTitle": "Composite SAP Note for Customer Connection Real Estate 2011/2012", "RefUrl": "/notes/1663018 "}, {"RefNumber": "1708101", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Cross-business entity SU in navigation tree", "RefUrl": "/notes/1708101 "}, {"RefNumber": "1604196", "RefComponent": "RE-FX-CN", "RefTitle": "RECN: Detailed display for differing measurements", "RefUrl": "/notes/1604196 "}, {"RefNumber": "1315287", "RefComponent": "RE-FX-BD", "RefTitle": "Search option in tree when assigning fixtures+fittings char", "RefUrl": "/notes/1315287 "}, {"RefNumber": "1525735", "RefComponent": "RE-FX-BD", "RefTitle": "Correction report vacancy management: Message display", "RefUrl": "/notes/1525735 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}