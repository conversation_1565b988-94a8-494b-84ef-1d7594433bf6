{"Request": {"Number": "2307907", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 304, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018317502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002307907?language=E&token=B006A1A400F1D8370C494A62DADA71B4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002307907", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2307907"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.09.2016"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-SAF-GLM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Global Label Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Safety", "value": "EHS-SAF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-SAF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Global Label Management", "value": "EHS-SAF-GLM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-SAF-GLM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2307907 - Uninstalling Genifix 2.2"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The uninstallation of the add-on <em>EHS Genifix 2.2</em>, using&#160;transaction SAINT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAINT, add-on, EHS&#160;Genifix 2.2, software component TDAG_GF 220_600, /TDAG/GF*</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You need to uninstall the add-on&#160;<em>EHS&#160;Genifix 2.2</em> from&#160;your system.</p>\r\n<p>See&#160;the prerequisites below.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This SAP Note is updated regularly. Make sure you have the current version of this SAP Note before you start the uninstall process.<br /><br />Contents<br />&#160;&#160;1. Change history<br />&#160;&#160;2. Prerequisites for uninstalling&#160;the add-on<br />&#160;&#160;3. Manual preparation steps before uninstalling<br />&#160;&#160;4. Uninstalling&#160;the add-on with SAINT</p>\r\n<p>1. Change history<br /><br />Date&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Short Description<br />01.09.2016&#160;&#160; SAP Note released</p>\r\n<p>2. Prerequisites for uninstalling the add-on</p>\r\n<p>Read and implement&#160;the following SAP Notes before you begin the installation:</p>\r\n<ul>\r\n<li>SAP Note&#160;<a target=\"_blank\" href=\"/notes/70228\">70228</a>: Add-ons: Conditions and upgrade planning</li>\r\n<li>SAP Note&#160;<a target=\"_blank\" href=\"/notes/2011192\">2011192</a>:&#160;Uninstalling ABAP add-ons</li>\r\n<li>SAP Note <a target=\"_blank\" href=\"/notes/2313869\">2313869</a>: Genifix: Support for the add-on uninstallation</li>\r\n</ul>\r\n<p>Release your transports for the&#160;SAP Note&#160;implementation to avoid locks during the uninstallation.</p>\r\n<p>Import the latest update of SPAM/SAINT (at least SPAM version 63).</p>\r\n<p>Make sure that the latest available&#160;<em>Attribute Change Package (ACP)</em>&#160;of the software component version TDAG_GF 220_600&#160;is installed in the system.</p>\r\n<p>3. Manual preparation steps before uninstalling</p>\r\n<p>Most of the Genifix functions have been transferred to the standard enhancements for the Global Label Management (GLM) in EHS-GLM (in SAP ERP).<br />In case you want to continue using the enhanced GLM functions, a migration to standard GLM is mandatory before uninstalling Genifix. For more information, refer to SAP Note <a target=\"_blank\" href=\"/notes/1934253\">1934253</a>.</p>\r\n<p>Perform the following steps on each client and repeat them after each client copy:</p>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>With the deinstallation of the add-on the following application and transactional data is deleted. Data in deleted database tables is lost as long as you do not save it before the add-on uninstallation. You can export the data into spreadsheets using transaction SE16n (General Table Display) or SQVI (QuickViewer) for data that needs to be joined to be easily human readable.<br />- Genifix print requests (tables /TDAG/GFM_LBLORH (header), /TDAG/GFM_LBLORD (parameter), /TDAG/GFM_LBLORO (order), /TDAG/GFM_LBLORS (serialization))<br />- Genifix components for WWI templates (tables /TDAG/GFWWIM_CMP,&#160;/TDAG/GFWWIM_CGP,&#160;/TDAG/GFWWIM_CDT)<br /><br /></li>\r\n<li>Genifix background jobs<br />Unschedule active jobs with the ABAP program name /TDAG/GF* in the job overview (SM37).<br />Genifix background jobs are checked for their usage during the uninstallation. If released jobs are found that are still referencing /TDAG/GF objects, the uninstallation will stop with an according message (\"Background jobs check failed\"). You need to adapt or delete the remaining jobs manually.<br /><br /></li>\r\n<li>Genifix report symbols on WWI templates and reports<br />WWI reporting must not use any /TDAG/GF development objects, such as table structures or function modules, to avoid runtime errors.<br />The Customizing activities listed below can be found under Environment, Health and Safety -&gt; Basic Data and Tools -&gt; Report Definition -&gt; Report Symbols.<br /><br />Ensure that there are no report symbols with table name starting with /TDAG/GF (the ones delivered by the add-on start with EGF* or SGF*) in the IMG activity Check and Specify Report Symbols or in the database table TCGA6.<br />If there are no reports using these symbols, you can delete the report symbols completely.<br />If reports using these symbols exist, copy the used table in the customer namespace and replace the table name.<br />Ensure that there are no report symbol groups with function modules starting with /TDAG/GF* (the ones delivered by the add-on start with GF*) in the IMG activity Specify Report Symbol Groups or in the database table TCGA1.<br />If there are no reports using these symbols, you can delete the report symbol groups completely.<br />If reports using these symbols exist, copy the used function module in the customer namespace and replace the function module for the field Foreign key - function module.<br />Genifix report symbols are checked for their usage on reports and templates during the uninstallation (tables ESTLS, TCGA1, TCGA6). If symbols are found, that are still based on /TDAG/GF objects, the uninstallation will stop with an according message (\"Report symbols check failed\" / \"Report symbol groups check failed\"). You need to adapt or delete the remaining symbols and groups manually.<br /><br /></li>\r\n<li>Genifix report symbols as GLM user entries<br />Ensure that Genifix report symbols are not used as GLM user entries at scenario or template level. Verify that tables CCGLT_OEDL, CCGLC_MPDP, CCGLC_MPDP_NEW don't contain any entries with Genifix report symbols that are based on /TDAG/GF objects (LSYID starting with EGF* or SGF*).<br />GLM user entries are checked for Genifix report symbols during the uninstallation (tables CCGLT_OEDL, CCGLC_MPDP, CCGLC_MPDP_NEW). If used symbols are found, that are still based on /TDAG/GF objects, the uninstallation will stop with an according message&#160;(\"Report symbols check failed\" / \"Report symbol groups check failed\").&#160;You need to adapt or delete the remaining user entries manually.<br />Maintain user entries at scenario level in the IMG activity <em>Environment, Health and Safety -&gt; Global Label Management -&gt; Specify Labeling Scenarios for Label Printing -&gt; Define Print Scenarios -&gt; </em>select scenario<em> -&gt; User Entries</em>.<br />Maintain user entries at template level in transaction CG42 (<em>Edit Report Template) -&gt; </em>select template<em> -&gt; Goto -&gt; User Entry</em>.<br /><br /></li>\r\n<li>Genifix print scenarios<br />Ensure that no GLM scenarios use objects from the /TDAG/GF namespace in the IMG activity <em>Environment, Health and Safety -&gt; Global Label Management -&gt; Specify Labeling Scenarios for Label Printing -&gt; Define Print Scenarios</em>.<br />All Genifix scenarios should have been migrated to standard GLM scenarios during Genifix to GLM migration. Delete the remaining scenarios if they are not in use anymore.<br />Genifix scenarios are checked for their usage during the uninstallation (tables CCGLC_SCEN, CCGLC_MPD, CCGLC_MPD_NEW). If scenarios are found, that are still based on /TDAG/GF objects, the uninstallation will stop with an according message (\"Print scenario check failed\"). You will need to adapt or delete the remaining scenarios manually.<br /><br /></li>\r\n<li>Genifix report application types<br />Ensure that the Genifix specific report application PAPERTYPE is not used in report symbol groups in the IMG activity <em>Environment, Health and Safety -&gt; Basic Data and Tools -&gt; Report Definition -&gt; Report Symbols -&gt; Specify Report Symbol Groups</em> or in the database table TCGA1.<br />Change all appearances of report application PAPERTYPE to MATMASTER.<br />Ensure that the Genifix specific report application PAPERTYPE is not used in generation variants in transaction CG2B -&gt; select generation variant -&gt; <em>Goto -&gt; Application Objects</em> or in the database table ESTDO.<br />Change all appearances of PAPERTYPE to LABELSTOCK and enter a matching object key.<br />Genifix report application PAPERTYPE is checked for its usage during the uninstallation (tables TCGA1, ESTDO, ESTLS). If usages are found, the uninstallation will stop with an according message (\"Report applications check failed\"). You will need to adapt or delete the remaining entries manually.<br /><br /></li>\r\n<li>Genifix trigger implementations<br />Ensure that the Genifix triggers for automatic print are deactivated (Process Order and Delivery Note) or don't use any /TDAG/GF objects.<br />- Default implementation for Process Order trigger:<br />Transaction CMOS -&gt; Project ZSAVEPP -&gt; <em>Components -&gt; Function exit</em> EXIT_SAPLCOBT_001. Deactivate or delete the enhancement.<br />- Default implementation for Delivery Note trigger:<br />IMG activity <em>Environment, Health and Safety -&gt; Product Safety -&gt; Report Shipping -&gt; Basic Settings for Shipping from SD Documents -&gt; Settings for the SD Interface: Shipping Documents -&gt; Output Determination</em><br />&#160; -&gt; <em>Check Output Types</em> -&gt; Output Type ZGFD. Delete the output type.<br />&#160; -&gt; <em>Check Output Determination Procedures</em> -&gt; select Procedure V10000 -&gt; Control. Delete the row with Condition Type ZGFD.<br />Transaction VV22 -&gt; Output Type ZGFD -&gt; Enter -&gt; Execute -&gt; Delete all entries.<br />Revise these steps accordingly in case of a custom implementation of these triggers.<br />In case further custom triggers are implemented (e.g. Handling Unit), ensure they are also deactived or don't use any /TDAG/GF objects.<br /><br /></li>\r\n<li>Genifix labeling workbench functions<br />Ensure that the GLM labeling workbench (transaction CBGLWB) does not include any business processes that use /TDAG/GF objects in the IMG activity <em>Environment, Health and Safety -&gt; Global Label Management -&gt; Define Layout of Function Workbench</em>.<br />Genifix business processes that are to be reused in GLM must be copied in the customer namespace and /TDAG/GF objects must be replaced.<br />GLM labeling workbench business functions are checked for usage of /TDAG/GF objects during the uninstallation (table CCGLC_WBC_BP). If usages are found, the uninstallation will stop with an according message (\"Labeling workbench functions check failed\"). You will have to adapt or delete the remaining entries manually.<br /><br /></li>\r\n<li>Genifix component database<br />Genifix components for WWI templates will be deleted during the uninstallation.<br />Genifix components for CG42 layouting are not reusable in GLM. Ensure that all components are migrated to the GLM building block database and/or deleted from the Genifix component database.<br />Genifix components can be exported in transaction CG42 -&gt; select template -&gt; Goto Document -&gt; Component tab -&gt; select component -&gt; Export.<br />Adapt the export XML file from Genifix format to GLM format:<br />Genifix format:<br />&lt;GF_GENIFIX_WWI VERSION=\"1.00\" SAPSystemType=\"Unicode\" SAPCodePage=\"U2L\" DOC_NAME=\"GFWWIS_COMPONENT\"&gt;<br />&#160; &lt;ROW NUMBER=\"1 \"&gt;<br />&#160; &#160; &lt;COL NAME=\"MANDT\"&gt;001&lt;/COL&gt;<br />&#160; &#160; &lt;COL NAME=\"COMPID\"&gt;block-id-key&lt;/COL&gt;<br />&#160; &#160; &lt;COL NAME=\"GROUPID\"&gt;group-id&lt;/COL&gt;<br />&#160; &#160; &lt;COL NAME=\"COMPNAME\"&gt;block-id&lt;/COL&gt;<br />&#160; &#160; &lt;COL NAME=\"COMPCOMMENT\"&gt;block-desc&lt;/COL&gt;<br />&#160; &#160; &lt;COL NAME=\"LANGU\"&gt;EN&lt;/COL&gt;<br />&#160; &#160; &lt;COL NAME=\"DOKAR\"/&gt;<br />&#160; &#160; &lt;COL NAME=\"DOKNR\"/&gt;<br />&#160; &#160; &lt;COL NAME=\"DOKVR\"/&gt;<br />&#160; &lt;/ROW&gt;<br />&lt;/GF_GENIFIX_WWI&gt;<br /><br />GLM format:<br />&lt;building_block&gt;<br />&#160; &lt;desc&gt;<br />&#160; &#160; &lt;group_id&gt;group-id&lt;/group_id&gt; &#160; &#160; &#160; &#160; &#160;&lt;=&gt; &lt;COL NAME=\"GROUPID\"&gt;<br />&#160; &#160; &lt;block_id&gt;block-id&lt;/block_id&gt; &#160; &#160; &#160; &#160; &#160; &#160; &lt;=&gt; &lt;COL NAME=\"COMPNAME\"&gt;<br />&#160; &#160; &lt;langu_iso&gt;EN&lt;/langu_iso&gt; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &lt;=&gt; &lt;COL NAME=\"LANGU\"&gt;<br />&#160; &#160; &lt;description&gt;block-desc&lt;/description&gt; &lt;=&gt; &lt;COL NAME=\"COMPCOMMENT\"&gt;<br />&#160; &lt;/desc&gt;<br />&lt;/building_block&gt;<br /><br />GLM building blocks can be imported in transaction CG42 -&gt; select template -&gt; Goto Document -&gt; Building Block Catalog tab -&gt; Import Block.<br />Genifix components are checked during the uninstallation (tables /TDAG/GFWWIM_CGP, /TDAG/GFWWIM_CMP, /TDAG/GFWWIM_CDT). If components are found, the uninstallation will stop with an according message (\"WWI layout components check failed\"). You will have to delete the remaining entries manually.<br /><br /></li>\r\n<li>Ensure that no development objects from the /TDAG/GF namespace are used for user exits in the IMG activity <em>Environment, Health and Safety -&gt; Basic Data and Tools -&gt; Basic Settings -&gt; Manage User Exits</em> or in the database table TCGUEFU.<br />This would lead to runtime errors when the functionality is used. If you want to keep user exits that are delivered with function modules in the /TDAG/GF namespace, copy the function modules in the customer namespace, ensure that there are no references to development objects in the /TDAG/GF namespace and adapt the customizing.<br /><br /></li>\r\n<li>In case a new role was created in the customer namespace from the SAP roles starting with /TDAG*, unassign the role from users and then delete the role via transaction PFCG.<br />Ensure that no authorization objects use /TDAG/GF authorization fields in transaction SU21 or in database table TOBJ.<br />Genifix authorization fields are checked during the uninstallation. If used fields are found, the uninstallation will stop with an according message (\"Authorization fields check failed\"). You will have to adapt or delete the affected authorization objects manually.<br /><br /></li>\r\n<li>The usage of coding from software component TDAG_GF in local coding will stop the add-on uninstallation and might lead to runtime errors in other applications if dynamic calls (that might not be found by the add-on uninstallation) are not cleaned up. The software component contains packages starting with /TDAG/GF. Use the add-on piece list to see in detail which development objects will be deleted. To do so you can open the Installation and Support Package Directory via Goto from the Add-On Installation Tool (transaction SAINT). Select the OCS package TDAGGF and open the object lists for the piece lists below.<br />You can use report RS_ABAP_SOURCE_SCAN to search for the usage of string /TDAG/ in local objects (package $TMP). Check if found results are related to this add-on.</li>\r\n</ol>\r\n<p><span style=\"font-size: 14px;\">&#160;</span>4. Uninstalling&#160;the add-on with SAINT</p>\r\n<p>You can use the Add-On Installation Tool to uninstall add-ons. Deletion of some add-ons is only possible if certain prerequisites apply, however.</p>\r\n<p>Prerequisites</p>\r\n<ol>\r\n<li>You are logged on to client 000.</li>\r\n<li>You have entered transaction code SAINT to start the <em>Add-On Installation Tool</em>.</li>\r\n<li>The add-on you want to delete is displayed in the tab page of components that can be uninstalled.</li>\r\n<li>Your system is configured correctly and you have imported the latest update of SPAM/SAINT.</li>\r\n<li>You have read this SAP Note carefully and have followed the instructions.</li>\r\n</ol>\r\n<p>Procedure</p>\r\n<ol>\r\n<li>Choose the tab page for components that can be uninstalled.</li>\r\n<li>From the list, select the add-on that you want to uninstall.</li>\r\n<li>Choose Start to start the uninstallation process.</li>\r\n<li>A confirmation prompt describes potential dangers of uninstallation and refers to an SAP Note with additional important information. Be sure to read this SAP Note and follow its instructions.</li>\r\n<li>If you have read the SAP Note and followed its instructions, choose \"Yes\". To display the SAP Note, choose \"Display\". To cancel the uninstallation process, choose \"Cancel\".</li>\r\n<li>After you start the uninstallation process, the Add-On Installation Tool runs a predefined sequence of phases. Should an error occur in any of these phases, the uninstallation process is stopped and the error is described to the extent possible. Once you have corrected the problem, you can choose \"Continue\" to continue the uninstallation process.</li>\r\n<li>At first, the Add-On Installation Tool performs preparation and check steps. If the errors cannot be corrected during these phases, you can choose \"Back\" to stop and reset the uninstallation process. In later phases, when the system has already made changes and deletions, a reset is no longer possible and the system issues an appropriate error message. In this case, you have to correct any errors and then complete the uninstallation process.</li>\r\n<li>Once the add-on has been uninstalled successfully, you can choose \"Logs\" to see the import logs or choose \"Exit\" to complete the uninstallation process.</li>\r\n</ol>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "EHS-SAF (Product Safety)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D054830)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D054757)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002307907/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002307907/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002307907/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002307907/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002307907/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002307907/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002307907/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002307907/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002307907/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2313869", "RefComponent": "EHS-SAF-GLM", "RefTitle": "Genifix: Support for add-on uninstallation", "RefUrl": "/notes/2313869"}, {"RefNumber": "2011192", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Uninstallation of ABAP add-ons", "RefUrl": "/notes/2011192"}, {"RefNumber": "1934253", "RefComponent": "EHS-SAF-GLM", "RefTitle": "Enhanced functions for EHS Global Label Management (GLM) - release information note (RIN)", "RefUrl": "/notes/1934253"}, {"RefNumber": "1620452", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1620452"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3076978", "RefComponent": "EHS-SAF-GLM", "RefTitle": "Genifix: <PERSON><PERSON><PERSON><PERSON> von Entwicklungsobjekten", "RefUrl": "/notes/3076978 "}, {"RefNumber": "2313869", "RefComponent": "EHS-SAF-GLM", "RefTitle": "Genifix: Support for add-on uninstallation", "RefUrl": "/notes/2313869 "}, {"RefNumber": "2267427", "RefComponent": "EHS-SAF", "RefTitle": "S4TWL - Add-On: Genifix (Labeling solution)", "RefUrl": "/notes/2267427 "}, {"RefNumber": "2221779", "RefComponent": "EHS-SAF", "RefTitle": "SAP S/4HANA Simplification Item: EHS Genifix Add-On (Labeling Solution)", "RefUrl": "/notes/2221779 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}