{"Request": {"Number": "1028068", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 400, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016315922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=A317EEC1B2B1D7BE6FE7AD0CD82361E8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1028068"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "HotNews"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.04.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1028068 - Required Oracle options for the DBA Cockpit"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>For the ABAP Application Server Release 7.00 (Netweaver 2004s), the new monitor and CCMS tool \"DBA Cockpit\" (transaction \"dbacockpit\") are delivered with Basis Support Package 12. As of the future Release 7.10 and higher, the \"DBA Cockpit\" will be included by default. This tool has a mandatory requirement for another, previously optional Oracle package. This note provides further information about this requirement. In addition, this note describes how you can activate optional functions in the DBA Cockpit for which other Oracle Packages are required.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>License, Oracle DBA Cockpit, dbacockpit, diagnostic pack, tuning pack</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note is relevant only if all of the following conditions are satisfied:</p> <OL>1. You use an SAP product on the Basis of ABAP Application Server Release 7.00 (Netweaver 2004s) or higher with an Oracle database.</OL> <OL>2. You intend importing Basis Support 7.00 Support Package 12 or you have already imported it.</OL> <OL>3. You have not purchased the Oracle DB license from SAP, but have purchased it directly from Oracle.</OL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The Oracle \"Diagnostic Package\" option is required for the new \"DBA Cockpit\". The following functions, which are contained in the package, are used in the \"DBA Cockpit\":<br /></p> <UL><LI>Active Session History (ASH):</LI></UL> <UL><LI>Automatic Workload Repository (AWR):</LI></UL> <UL><LI>Automatic Database Diagnostic Monitor (ADDM):</LI></UL> <p><br />This package is contained by default in the Oracle DB license obtained by SAP, and you do not need to do anything from a licensing perspective in this case.<br />In contrast, if you have obtained your Oracle license directly from Oracle, check whether the \"Diagnostic Package\" option is&#x00A0;&#x00A0;also included in the license.<br />If this is not the case, you must subsequently license the \"Diagnostic Package\" if you want to import Basis Support Package 7.00 Support Package 12 or you have already imported it. Contact Oracle directly regarding the license.<br />You do not have to implement or activate this new function. As of the SAP releases listed above, the new functions are automatically available.<br /><br />The Oracle Diagnostic Package is not absolutely necessary for remote systems that are connected to the DBA Cockpit with Oracle as the database.&#x00A0;&#x00A0;The following functions that require the diagnostic package may be used for the relevant remote system only if the diagnostic package is licensed for the related Oracle database:<br /></p> <UL><LI>Workload Reporting</LI></UL> <UL><LI>Active Session History</LI></UL> <UL><LI>System Metrics</LI></UL> <UL><LI>System Summary Metrics</LI></UL> <UL><LI>File Metrics</LI></UL> <UL><LI>Wait Class Metrics</LI></UL> <p><br />As of Basis Support Package 7.00 18, these functions are automatically hidden for remote systems that are connected and are only visible again once you have activated the checkbox \"ORACLE Diagnostic Package is licensed\" in the system administration of the DBA Cockpit for the relevant remote system. (See also Note 1250596). For the local system, that is, the system on which the DBA Cockpit runs, this checkbox is set automatically in the system administration of the DBA Cockpit, since SAP licensing is required as of Basis Release 7.00 Support Package 12 (see above).<br /><br />As of Enhancement Package 1 for Basis Release 7.00 (that is, Basis Release 7.01), the function \"SQL Tuning Advisor\" is contained in the DBA Cockpit. This optional function requires the Oracle Tuning Pack to be licensed. (If you have purchased the Oracle license from SAP, the tuning package is included in the license.)<br />The function \"SQL Tuning Advisor\" is hidden by default, even for the local system. If you require this function and you have a licensed tuning pack, the following procedure is necessary to activate the function.<br /><br />In the system on which the DBA Cockpit runs, enter<br />the following data in the table ORA_FEAT_USED (for example, using transaction SE16):<br /><br />REMSYS = &lt;For each system, enter here the system ID from the system administration of the DBA Cockpit for which an Oracle Tuning Pack license exists&gt;.<br /><br />FEATURE = TUNINGPACK<br />IS_USED = X<br /><br />You can deactivate the function again by setting the indicator IS_USED to ' ' (blank) or by deleting the entry for the relevant system.<br /><br />As of Oracle Version 11g, the following new database parameter is available:<br />CONTROL_MANAGEMENT_PACK_ACCESS. If the diagnostic package is not licensed, the parameter can be set to \"none\". As a result, all functions of the diagnostic package and tuning package are deactivated in the database so that an unintentional violation of the<br />license obligations is prevented.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019926)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019926)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "740897", "RefComponent": "BC-DB-ORA", "RefTitle": "Info about the scope of the Oracle license; required Oracle options", "RefUrl": "/notes/740897"}, {"RefNumber": "1314689", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Dynamic loadable monitors (RSORASTT)", "RefUrl": "/notes/1314689"}, {"RefNumber": "1281999", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: SQL Command Editor extended check", "RefUrl": "/notes/1281999"}, {"RefNumber": "1256322", "RefComponent": "BC-DB-ORA", "RefTitle": "Establishing a remote database connection in DBACOCKPIT", "RefUrl": "/notes/1256322"}, {"RefNumber": "1136582", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBACockpit: Used Oracle features", "RefUrl": "/notes/1136582"}, {"RefNumber": "1080813", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Changing history update (Oracle database)", "RefUrl": "/notes/1080813"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2921786", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "Checklist: Remote Oracle Monitoring in SOLMAN", "RefUrl": "/notes/2921786 "}, {"RefNumber": "740897", "RefComponent": "BC-DB-ORA", "RefTitle": "Info about the scope of the Oracle license; required Oracle options", "RefUrl": "/notes/740897 "}, {"RefNumber": "1314689", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Dynamic loadable monitors (RSORASTT)", "RefUrl": "/notes/1314689 "}, {"RefNumber": "1256322", "RefComponent": "BC-DB-ORA", "RefTitle": "Establishing a remote database connection in DBACOCKPIT", "RefUrl": "/notes/1256322 "}, {"RefNumber": "1080813", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Changing history update (Oracle database)", "RefUrl": "/notes/1080813 "}, {"RefNumber": "1281999", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: SQL Command Editor extended check", "RefUrl": "/notes/1281999 "}, {"RefNumber": "1136582", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBACockpit: Used Oracle features", "RefUrl": "/notes/1136582 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}