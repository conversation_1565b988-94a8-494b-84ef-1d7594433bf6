Setting Up Your Statutory Reporting | SAP Help PortalHomeSAP S/4HANA CloudStatutory ReportingGetting StartedSetting Up Your Statutory ReportingStatutory Reporting2402.1Available Versions: 2402.1  2308.4 EnglishAvailable Languages: English  Chinese Simplified (简体中文)  French (Français)  German (Deutsch)  Japanese (日本語)  Portuguese for Brazil (Português do Brasil)  Russian (Русский)  Spanish (Español) This documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteTo mark this page as a favorite, you need to log in with your SAP ID.If you do not have an SAP ID, you can create one for free from the login page.Log onDownload PDFThe following PDF options are available for this document:Create Custom PDFShareTable of Contents Statutory Reporting  Getting Started  Setting Up Your Statutory Reporting  Create Statutory Reporting Entities  Assign Organizational Unit to a Reporting Entity  Assign Report Categories to a Reporting Entity  Set Periodicity of Report Category  Set Properties of Reporting Activity  Enter Parameters Specific to the Report Category  Set Validity of Organizational Unit  Enter Parameters Specific to Reporting Entities  Integration for Submission of Reports  Run Statutory Reports  Define Statutory Reports  Extensibility and Reuse in Statutory Reporting Utilities and Other Items Video Library  Glossary  Setting Up Your Statutory ReportingOn this pageExample
          Related Information
         

In statutory reporting, to fulfill the requirements in the countries/regions where your business operates, you can choose to use predefined standard report definitions, report categories, and reporting activities, or you create your own. In the Setting Up Your Statutory Reporting  configuration activity, you connect the reporting entities for which you should submit statutory reports. You also connect the report definitions, report categories and reporting activities relevant to the statutory reporting scenario.
In your configuration environment, use the search function to open the following activity: Setting Up Your Statutory Reporting. To access statutory reporting specific configuration activity, you need the Statutory Reporting – Configuration (SAP_CA_BC_IC_LND_FIN_SRF_PC) business catalog.
The SSCUI leads you through the process needed to set up your statutory reporting. We recommend filling in the settings in this sequence:


Create Statutory Reporting Entities

Assign Organizational Unit to a Reporting Entity

Assign Report Categories to a Reporting Entity

Set Periodicity of Report Category

Set Properties of Reporting Activity

Enter Parameters Specific to the Report Category

Set Validity of Organizational Unit

Enter Parameters Specific to Reporting Entities



Example
Your organization operates in the United Kingdom and is required to submit both VAT returns and EC Sales Lists to the government of the United Kingdom. You create these reports with the organizational unit as the company code. The company code values can be GB01, GB02, and GB03, with GB03 as the legal entity that submits the reports.

           In this SSCUI, you make the following settings:
           

You create a reporting entity GBRPT (GB Company Codes).

You enter the required valid-from date for the GBRPT reporting entity according to your business needs for company code assignments.

You assign three company codes to GBRPT: GB01, GB02, and GB03. Since GB03 is the legal entity for which you submit the report, you mark GB03 as the leading company code.

You assign the relevant report categories to GBRPT. Since you want to submit both VAT returns and EC Sales Lists, you select both of these categories from the available entries.

For each of the assigned report categories, you set the periodicity.
For example, for the VAT return, you enter the date that you created the GBRPT reporting entity as the active-from date, that is, you should be able to create report runs of this type starting on this date. You want to make sure that you submit your VAT returns no later than 10 days after the period end. You enter 10 as the period offset. You need to submit your VAT return every three months, so you select the appropriate fiscal year variant. To help your users create and submit reports on time, you want the system to notify them that the due date is approaching 7 days before the due date. You enter 7 as the notification period. For more information on how you can customize the periodicity of the report based on your requirements, see Set Periodicity of Report Category.








          Related Information
         

Configuration Environment of SAP S/4HANA Cloud



On this pageExample
          Related Information
         Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

