{"guid": "0090FABF32DE1ED6A5D41D1B661420CE", "sitemId": "SI20: Logistics_General", "sitemTitle": "S4TWL - Logistical Variant", "note": 2368655, "noteTitle": "2368655 - S4TWL - Logistical Variants", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>For articles with several units of measures it was possible via logistical variants to a) manage the stocks of an article separately for different units of measure and b) plan requirements according to the unit of measure</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA logistical variants are not available anymore. Thus also program MMLOGMETRANSFORM \"Transformation of Single Material to Logistics UoM Variant\" is not available anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>There is no equivalent functionality to logistical variants available in SAP S/4HANA prior to SAP S/4HANA 1809. Starting with SAP S/4HANA 1809 FPS01 there is the so called Logisical Product which offeres functionality that addreesses a similar business requirement, for functional scope see referenced note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if logistical variants are used. This can be checked via transaction SE16N. Enter table MARA and check whether there are entries with field BFLME (Logistical Variant) not equal blank.</p>", "noteVersion": 5, "refer_note": [{"note": "3481008", "noteTitle": "3481008 - Program MMLOGMETRANSFORM for Logistical Variants not available in S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Program MMLOGMETRANSFORM \"Transformation of Single Material to Logistics UoM Variant\" can be executed altough Logistical Variants are not available in S/4HANA anymore</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\"> MMLOGMETRANSFORM, Logistical Variants</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\"> Program MMLOGMETRANSFORM was accidentally not blocklisted.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p id=\"\"> The error will be fixed in future releases and future Support Package stacks.</p>", "noteVersion": 1, "refer_note": [{"note": "2368655", "noteTitle": "2368655 - S4TWL - Logistical Variants", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>For articles with several units of measures it was possible via logistical variants to a) manage the stocks of an article separately for different units of measure and b) plan requirements according to the unit of measure</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA logistical variants are not available anymore. Thus also program MMLOGMETRANSFORM \"Transformation of Single Material to Logistics UoM Variant\" is not available anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>There is no equivalent functionality to logistical variants available in SAP S/4HANA prior to SAP S/4HANA 1809. Starting with SAP S/4HANA 1809 FPS01 there is the so called Logisical Product which offeres functionality that addreesses a similar business requirement, for functional scope see referenced note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if logistical variants are used. This can be checked via transaction SE16N. Enter table MARA and check whether there are entries with field BFLME (Logistical Variant) not equal blank.</p>", "noteVersion": 5}]}, {"note": "2310522", "noteTitle": "2310522 - S4TC SAP_APPL Checks for LO-RFM-MD-ART", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<ul>\n<li>The check returns a message saying 'CLIENT: XXX, Not all generic articles have been migrated' and/or 'CLIENT: XXX, Not all generic article variants have been migrated' because conversion to SAP S/4HANA requires migration of generic articles and their variants including variant-creating characteristics.</li>\n<li>The check returns a message saying 'CLIENT: XXX,  has customizing entries for splitting logistical variants, This is not supported' and/or 'CLIENT: XXX,  has logistical variants. Their usage is not supported'.</li>\n<li>The check returns a message saying  'CLIENT: all, No servergroup found, see SAP note 2501201. Please create servergroup (grouptype S).' </li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span>S4TC; GENERIC_ARTICLE_MIGRATION</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ul>\n<li>\n<p>In SAP ERP for Retail/Fashion, the variant-creating characteristics for a generic article are derived from the merchandise category hierarchy. In SAP S/4HANA, a generic article is assigned to a configuration class and the variant-creating characteristics for a generic article are derived from this configuration class.</p>\n<p>For this reason, generic articles, variant-creating characteristics, and variants that were created in SAP ERP need to be converted to the iBase storage format before conversion to SAP S/4HANA.</p>\n</li>\n<li>SAP S/4HANA does not support logistical variants; however, logistical variants are migrated from SAP ERP for Retail/Fashion, to avoid data loss but are not split into sales variants when receiving a goods receipt. All other processes remain unchanged. It is not possible to create new logistical variants or to change existing ones in SAP S/4HANA. This means in detail:</li>\n<ul>\n<li>If there are Customizing entries for the splitting of logistical variants, the conversion pre-check returns an error. These entries must be changed or deleted before conversion. After conversion, it is not possible to maintain the settings for splitting logistical variants.</li>\n<li>If logistical variants exist in the system, the conversion pre-check returns a warning.</li>\n<li>Logistical variants are migrated like normal generic articles.</li>\n<li>It is not possible to change migrated logistical variants.</li>\n<li>It is not possible to create new logistical variants.</li>\n<li>It is not possible to use a material group that has logistical characteristics directly assigned.</li>\n<li>When posting a goods receipt, there is no automatic rebooking from a logistical variant to a sales variant.</li>\n</ul>\n<li>As a prerequisite for the message about missing servergroup, note 2501201 was applied, which extends the prechecks by a  servergroup check. This message does not refer to a specific client as the server group database table is client-independent.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This SAP Note refers to check IDs GENERIC_ARTICLE_MIGRATION and GA_LOGISTIC_VARIANTS.</p>\n<p>To solve the error message 'CLIENT: XXX,  Total: YYY generic articles are needed to be migrated - Please see SAP Note - 2310522.', execute the report R_PREMIG_VAR2CONFCLASS in all the clients where non-migrated generic articles exist. If the generic articles that need to be migrated are known, you can enter them on the selection screen for fast execution of the report.</p>\n<p>To solve the error message 'CLIENT: XXX,  Total: YYY generic article variants are needed to be migrated - Please see SAP Note - 2310522.', execute the report R_VARMIG2IBASE in all the clients where non-migrated generic article variants exist. If the generic article variants that need to be migrated are known, you can enter them on the selection screen for fast execution of the report.</p>\n<p>The warning message 'CLIENT: XXX, Characteristic value restrictions for Informative characteristics (if used) will get lost' is a warning message that checks whether there are any characteristic value restrictions defined for informative characteristics and prompts you that these restrictions will not be migrated. The detailed information about which restrictions are used in such a way is stored in the application log during the pre-migration run using report program R_PREMIG_VAR2CONFCLASS. Please select the 'Write Application Log' checkbox during execution of the program.</p>\n<p>To solve the error message 'CLIENT: all, No servergroup found, see SAP note 2501201. Please create servergroup (grouptype S).' : use tx RZ12 to maintain server groups.</p>\n<p>After implementation of note 2501201, the message 'CLIENT: all, No servergroup found' is sometimes shown, although the server group exists. To solve this issue, please implement first note 3000393 and then note 3001682 in your system</p>\n<p>Please implement SAP Note 2331707 to implement the pre-migration reports.</p>\n<p>The complete data migration process is divided into three phases: manual pre-migration activities, automated processes during downtime, and manual post-migration activities.</p>\n<p>This SAP Note describes the manual pre-migration activities that are needed to be performed.</p>\n<p>Please check the SAP Note 2350650 for performing the Post-migration activities.</p>\n<p>In SAP ERP for Retail/Fashion, the variant-creating characteristics for a generic article are derived from the merchandise category hierarchy. But in SAP S/4HANA, variant-creating characteristics are derived from the configuration class of ithe Base storage. Therefore, the variant-creating characteristics of the generic articles that were created in an SAP ERP for Retail/Fashion system also need to be assigned/converted to the configuration class.</p>\n<p>This can be done using the report R_PREMIG_VAR2CONFCLASS. The program has to be executed for each client individually and provides the following selection fields.</p>\n<p><strong>Material</strong> – Is used to enter the material number of the generic articles that need to be migrated. It is possible to migrate a single generic article or a range of generic articles or the complete list of generic articles present in the whole system (if no material number is entered).</p>\n<p><strong>Class Type</strong> – Is used to enter the new class type to which the variant-creating characteristics are to be migrated.</p>\n<p><strong>Test Mode</strong> – Can be used to run the report in test mode without any DB updates. This option can be used to check whether the conversion process can be successfully executed.</p>\n<p><strong>Package Size</strong> – Refers to the number of records/articles to be processed in one single package. Ideally, the size also refers to the number of records prepared for DB insertion in 1 INSERT statement. After this, the data is cleared from the buffer and next package is processed. In test mode, no real DB Inserts happen, but data preparation and so on is still the same as during the productive run.</p>\n<p><strong>Write Application Log</strong> – Is used to write the processing logs to the application log. It is only possible to select this checkbox during the productive run. You can check the application log by using transaction SLG1. Enter the following:</p>\n<p><strong>       Object</strong>: ‘MATU’</p>\n<p><strong>       External ID</strong>: ‘VARCONV_CORR’</p>\n<p><strong>Including Delta Update</strong> – This flag is used to include the generic articles (that have already been migrated) for migration again (probably because of changes in the generic article after last pre-migration run) with delta values. If this flag is not selected, the generic articles that have already been migrated are ignored for this migration run.</p>\n<p>Report R_VARMIG2IBASE migrates the generic article variants and their variant-creating characteristics &amp; values to the iBase storage. The program must be executed for each client individually and provides the following selection fields.</p>\n<p><strong>Material</strong> – Is used to enter the material number of the generic articles for which the variants, characteristics and values are needed to be migrated. It is possible to migrate the variants of a single generic article or of a range of generic articles or the complete list of generic articles that exist in the whole system (if no material number is entered).</p>\n<p><strong>Class Type</strong> – Is used to enter the new configuration class type to which the variants would be migrated in the iBase storage.</p>\n<p><strong>Variants per process block</strong> – Refers to the number of variants to be processed in one single package. Ideally, the size also refers to the number of records to be prepared for DB insertion in 1 INSERT statement. After this, the data is cleared from the buffer and next package is processed. In test mode, no real DB Inserts happen but, data preparation and so on is still the same as during the productive run.</p>\n<p><strong>Test Mode</strong> – Can be used to run the report program in test mode without any DB updates. This option can be used to check whether the conversion process can be successfully executed.</p>\n<p><strong>Write Application Log (SLG1)</strong> – Is used to write the processing logs to the application log. It is only possible to select this checkbox during the productive run. You can check the application log by using transaction SLG1. Enter the following:</p>\n<p><strong>       Object</strong>: ‘MATU’</p>\n<p><strong>       External ID</strong>: ‘VARCONV_CORR’</p>\n<p>In a first step, select the data to be migrated to the IBase from the tables INOB, IBINOWN, AUSP, and KSML in one selection step (variants that have already been migrated are automatically excluded by this selection). This can result in a large amount of data to be migrated. For each variant, the report builds up the data to be migrated to IBase and collects them in corresponding buffer tables for the data base tables IBIN, IBIB, IBIBT, IBINOWN, IBSYMBOL, IBINVALST, IBINVALUES, IBEXTINST, IBST, and IBINST_OBJ. In a final step, mass inserts and updates are executed. All messages, success or error messages, are written to the application log.</p>\n<p>Because the number of variants can be very high, the report R_VARMIG2IBASE can run a long time so we recommend that you either run a batch job or run the report several times to limit the number of generic articles selected each time.</p>\n<p>The error message 'CLIENT: XXX,   has customizing entries for splitting logistical variants, This is not supported in SAP S/4HANA - Please see SAP Note - 2310522.'  can be solved in the following way: In Customizing for Logistics - General under Material Master -&gt; Retail-Specific Settings- &gt; Settings for Structured Materials -&gt; Structured Material in Logistics Process, choose transaction key '200' and set the splitting indicator for all entries for material category '02' to '0'.</p>\n<p>The warning message 'CLIENT: XXX, has Logistical variants. Their usage is not supported in S4HANA - Please see SAP Note - 2310522' indicates that logistical variants exist in the system. Logistical variants are migrated like generic articles, but cannot be changed in SAP S/4HANA.</p>\n<p> </p>", "noteVersion": 8, "refer_note": [{"note": "2350650", "noteTitle": "2350650 - Post-migration cleanup of Retail generic articles and variants data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are in the process of converting your SAP ERP for Retail/Fashion system to an SAP S/4HANA on-premise system and you want to use existing Retail Generic Articles in SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, S4TC; GENERIC_ARTICLE_MIGRATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP ERP for Retail/Fashion, the variant-creating characteristics for a generic article are derived from the merchandise category hierarchy. In SAP S/4HANA, a generic article is assigned to a configuration class and the variant-creating characteristics for a generic article are derived from this configuration class.</p>\n<p>For this reason, generic articles, variant-creating characteristics, and variants that were created in SAP ERP for Retail/Fashion need to be converted to the iBase storage format before conversion to SAP S/4HANA.</p>\n<p>Once the conversion has happened, there is still old data (generic article classes, variant creating characteristics assigned to old class type 026) in the system which is not used/needed anymore and should be cleaned up.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please execute the Report program R_POSTMIG_RETAIL_CLEANUP to cleanup the non required data in the S/4HANA system. Please take into account that this cleanup step is an optional step. The operational usage of migrated generic articles and their variants won't be affected when the cleanup will be done later on. You should do the cleanup only if it is ensured that there are no inconsistencies in place for the converted generic articles and variants. If the cleanup has been executed it is no possible anymore to correct any remaining inconsistencies. The cleanup report does a rough check whether all generic articles and all variants has been converted but this check cannot cover all inconsistency situations.</p>\n<p>This cleanup is the 3rd/last phase of the whole conversion process (after the execution of pre migration reports (first phase) and execution of downtime report through SUM (second phase)). This report is needed to be executed manually for each client. The report has following parameters:</p>\n<p><strong>Test Mode</strong> – Can be used to run the report in test mode without any DB updates. This option can be used to check whether the cleanup process can be successfully executed.</p>\n<p><strong>Write Application Log</strong> – Is used to write the processing logs to the application log. It is only possible to select this checkbox during the productive run. You can check the application log by using transaction SLG1. Enter the following:</p>\n<p><strong>       Object</strong>: ‘MATU’</p>\n<p><strong>       External ID</strong>: ‘VARCONV_CORR’</p>\n<p><strong>Class Type</strong> – Is used to enter the class type to which the variant-creating characteristics were migrated in the first phase.</p>\n<p>This report deletes following kind of old data :</p>\n<p>The old restrictions with class type 026 are no longer used, so they could be deleted from the DB table AUSP.</p>\n<p>The Generic Article Class data with wwskz = 3 in DB table KLAH is no longer needed, so these could be deleted from table KLAH.</p>\n<p>Old variant creating characteristics in table AUSP are no longer used, so they could be deleted from the table AUSP.</p>\n<p> </p>\n<p> </p>", "noteVersion": 2}, {"note": "2501201", "noteTitle": "2501201 - S4SIC SAP_APPL: Retail Generic Articles - Pre-check for Servergroup", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As per note 2182725 checks have to be done before starting data migrations in SAP S/4HANA target system.</p>\n<p>This note refers to the migration of SAP Retail generic articles and extends the checks by adding a check for a server group being available in the target system.</p>\n<p>In some cases no server group might be setup and only the standard logon group is available for system operative mode. A servergroup is required, because during target system downtime some data migrations are running in parallelization mode due to high Retail data volume and small time window for the migration to take place during downtime.</p>\n<p>If no server group is being setup, migration of Retail generic articles will not be started giving error message E KMAT_MIGR 050 (\"Cannot migrate generic articles; RFC server group is missing\") in the migration protocol.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, R_MIGOBJCLASSREL_PARA, S4PC, Conversion, S/4HANA, Pre-Check, Simplification Item, TCI, SIC, SI-Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note applies in the first place if you plan to perform the S/4HANA system conversion to S/4HANA 1709 or above, see note 2418800.</p>\n<p>If your target release is S/4HANA 1511 or S/4HANA 1610 (see note 2182725) please see correction instructions</p>\n<ul>\n<li>0020751258 11412</li>\n<li>0020751258 24858</li>\n<li>0020751258 225561</li>\n<li>0020751258 98038</li>\n</ul>\n<p>of note 2326345.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new servergroup check coding for S/4HANA system conversion to S/4HANA 1709 or above is delivered via TCI. No corrections instructions are provided for this functionality within this note.</p>\n<p>The new servergroup check coding for S/4HANA system conversion to S/4HANA 1511 or 1610 is provided via note 2326345.</p>\n<p><strong>How to create a server group assignment:</strong></p>\n<ol>\n<li><span>Go to transaction RZ12</span></li>\n<li>Press Create button</li>\n<li>Enter a name for the server group, e.g. 390</li>\n<li>Enter the instance from system connection properties as described in the follwoing example: Application server = pvx189, System ID = XYZ, Instance number  = 00; Instance of the server group is then is then pvx189_XYZ_00.</li>\n<li>If a pop up appears, where the current quota can be applied, press yes.</li>\n<li>If  the server group assignment is created, adapt the properties of the assignment. Contact a system administrator to maintain proper values.</li>\n</ol>", "noteVersion": 5}, {"note": "2326345", "noteTitle": "2326345 - S4TC SAP_APPL conversion pre-checks for LO-RFM-MD-ART", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your SAP ERP for Retail/Fashion system to an SAP S/4HANA and you want to use existing Retail generic articles in SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span>S4TC; GENERIC_ARTICLE_MIGRATION; </span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP ERP for Retail/Fashion system, the variant-creating characteristics for a generic article are derived from the merchandise category hierarchy. In SAP S/4HANA, a generic article is assigned to a configuration class and the variant-creating characteristics for a generic article are derived from this configuration class.</p>\n<p>For this reason, generic articles, variant-creating characteristics, and variants that were created in SAP ERP for Retail/Fashion system, need to be converted to the iBase storage format before conversion to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please apply the correction instructions to implement the pre-check class.</p>", "noteVersion": 10}, {"note": "2331707", "noteTitle": "2331707 - Pre-migration of Retail generic articles and variants to configurable materials", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your SAP ERP for Retail/Fashion system to an SAP S/4HANA, and you want to use existing retail generic articles in SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4HANA, S4TC; GENERIC_ARTICLE_MIGRATION; S/4</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP ERP for Retail/Fashion, the variant-creating characteristics for a generic article are derived from the merchandise category hierarchy. Whereas, in SAP S/4HANA, a generic article is assigned to a configuration class, and the variant-creating characteristics for a generic article are derived from that configuration class.</p>\n<p>For this reason, generic articles, variant-creating characteristics, and variants, which were all created in SAP ERP for Retail/Fashion, need to be converted to the iBase storage format before conversion to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please apply the correction instructions in this SAP Note to implement the pre-migration reports. This note is only to be applied by Retail/Fashion customers or by customers who are using generic articles. Regarding the pre-migration and the final migration phases, please have a look at SAP Note 2625496 to get more background information.</p>\n<p>Some hints about the major note versions:</p>\n<ul>\n<li>Version 12:</li>\n<ul>\n<li>Contains the correction mentioned in Note 2453010 (valid only for SAP S/4HANA).</li>\n</ul>\n<li>Version 14:</li>\n<ul>\n<li>It's not needed anymore to apply Note 2383883 to your system because the syntax error has been removed within this version. Up to version 13, to remove the syntax error which prevented the activation of the pre-migration reports and classes, it was needed to apply Note 2383883 directly after applying this note.</li>\n</ul>\n<li>Version 15:</li>\n<ul>\n<li>Method SELECT_VARIANT_LIST of class CL_VARMIG2IBASE displays the client in the message text when no generic article has been found for pre-migration (excerpt of Note 2472251, which is valid only for SAP S/4HANA).</li>\n<li>Method COLLECT_ALL_CHARACTERISTICS of class CL_ACCESS_MC_HIERARCHY now supports variant-creating characteristics which have been assigned several times along a merchandise category hierarchy path (e.g. at the merchandise category level and again on the characteristics profile level), even if this is not a proper characteristic master data setup (correction according to Note 2602079, which is valid only for SAP S/4HANA).</li>\n</ul>\n<li>Version 16-22:</li>\n<ul>\n<li>Method SELECT_VARIANT_LIST of class CL_VARMIG2IBASE: The result table of the database select for the relevant variants is now sorted in a way that all adjacent duplicates get deleted correctly (correction according to Note 2641666, which is valid only for SAP S/4HANA).</li>\n<li>Method BUILD_LIST_OF_GA_NUMBERS of class CL_VARMIG2IBASE: Adaptation according to the new sort order of the variants result list</li>\n<li>Type group MDMIG: Switch from sorted table to standard table due to different sorting behavior of the variants result list.</li>\n</ul>\n<li>Version 23-24:</li>\n<ul>\n<li>Contains the corrections mentioned in Note 2643881 (valid only for SAP S/4HANA).</li>\n<li>Method SELECT_VARIANT_LIST of class CL_VARMIG2IBASE: The selection of the relevant data produces multiple entries for characteristics which are assigned to more than one class. Therefore, the join on table KSML was isolated in a separate method (SELECT_KSML).</li>\n<li>Method SELECT_KSML of class CL_VARMIG2IBASE: Selects all characteristics with relevancy indicator (RELEV) = 2 (variant-creating).</li>\n<li>Method BUILD_PACKAGES of class CL_VARMIG2IBASE: Selects all generic articles from table MARA and build packages. The package size can be controlled by an input parameter that comes from the selection screen of report R_VARMIG2IBASE. This was necessary because of performance reasons.</li>\n<li>Report R_VARMIG2IBASE has a new selection option – the number of generic articles for one package. Additionally, the report calls the methods SELECT_KSML and BUILD_PACKAGES of class CL_VARMIG2IBASE, and, in a loop over the packages with generic articles, calls the methods SELECT_VARIANT_LIST and PROCESS.</li>\n<li>Method CHECK_MULTIPLE_CHAR_VALUES of class CL_VARMIG2IBASE: Performance optimizations</li>\n</ul>\n</ul>\n<ul>\n<li>Version 25: Contains a correction instruction of version 23-24 which, by mistake, was not included in the note.</li>\n<li>Version 26-29:</li>\n<ul>\n<li>Contains the corrections mentioned in Note 2752442 (valid only for SAP S/4HANA).</li>\n<li>Method ENQUEUE_MARA of class CL_VARMIG2IBASE: Switch from shared lock mode to an exclusive lock mode to prevent multiple IBase instance entries for variants.</li>\n<li>Method DEQUEUE_MARA of class CL_VARMIG2IBASE: Switch from shared lock mode to an exclusive lock mode to prevent multiple IBase instance entries for variants.</li>\n</ul>\n<li>Version 30: Manual post-processing added to verify whether Note 2798896 is in place, and customizing maintenance has been done for a better parallelization setup.</li>\n<li>Version 31:</li>\n<ul>\n<li>Generation of entries in table SWOR for the new configuration classes has been improved to support not only the logon language but also other languages (contains the correction mentioned in SAP Note 3090263, which is valid only for SAP S/4HANA).</li>\n<li>Due to technical issues which prevented a proper update of the old correction instruction, a new correction instruction was created for the main coding, and the old correction instruction was deleted.</li>\n<li>New correction instruction created for UDO report NOTE_2330294 due to technical improvements regarding the handling of correction instructions.</li>\n<li>To explain how to use the UDO report, a correction instruction for manual post-processing was added. </li>\n<li>The wording of the correction instruction for the existing manual post-processing was improved.</li>\n</ul>\n<li>Version 32: Minor technical change in UDO report NOTE_2330294.</li>\n<li>Version 33: Method CL_VARMIG2IBASE of class CL_VARMIG2IBASE: If select-options were used and multiple generic articles were entered, the package was built with a between operator for the first found article and the last found article. This led to the effect that also all generic articles between the first and the last generic article were processed. With version 33 the select-options are passed through so that no additional generic articles processed when specifying generic articles on the selection screen.</li>\n</ul>", "noteVersion": 33}]}, {"note": "2844326", "noteTitle": "2844326 - Functional Scope of Logistical Products in SAP S/4HANA Retail for Merchandise Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using SAP S/4HANA Retail for merchandise management or SAP S/4HANA for fashion and vertical business, and you want to make use of Logistical Products.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span>Logistical Product, Sales Product, Procurement Product</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Logistical Products are available in specific processes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Logistical Products are available in the following processes:</p>\n<ul>\n<li>Article Maintenance via Transactions MM4x</li>\n<li>MRP for distribution centers</li>\n<li>Rapid Replenishment (transaction WRP1R) for stores</li>\n<li>Purchase requisitions</li>\n<li>Purchase orders</li>\n<li>Stock transfer orders</li>\n<li>Collective purchase orders</li>\n<li>Merchandise distribution</li>\n<li>Invoice verification</li>\n<li>Goods movements</li>\n<li>Retail stock overview (transaction RWBE)</li>\n<li>Physical inventory</li>\n<li>Allocation table: only those stores are considered which have the corresponding source list entry (if a procurement product has been entered in the allocation table) or no source list entry (if the sales product has been entered in the allocation table)</li>\n<li>POS inbound</li>\n<li>POS outbound</li>\n<li>Interoperability with SAP Extended Warehouse Management (EWM) and SAP Transportation Management (TM)</li>\n<li>Outbound integration scenarios through IDoc ARTMAS</li>\n<li>Inbound integration scenarios through IDoc ARTMAS for the maintenance of Logistical Products</li>\n<li>Maintenance of Logistical Products through BAPI MATERIAL_MAINTAINDATA_RT</li>\n</ul>\n<p>Currently, Logistical Products are not available in the following processes:</p>\n<ul>\n<li>Article maintenance via product Fiori app</li>\n<li>Structured articles (displays, sales sets, prepacks) cannot be modelled as Logistical Products</li>\n<li>Generic articles and variants cannot be modelled as Logistical Products</li>\n<li>Discontinuation of Sales Articles at site level</li>\n<li>Copying of Procurement Products via transaction WRFMATCOPY</li>\n<li>GDS (Global Data Synchronization)</li>\n<li>Automatic replacement of the Sales Product by the relevant Procurement Product in purchase orders works only if source list entries for the Procurement Product have been created. The automatic replacement does not use  listing information.</li>\n<li>Procurement Products in the follow-up or replacement product process</li>\n<li>Sales price calculation for Sales Product: take into account different purchasing prices of different Purchasing Products</li>\n<li>Condition contract</li>\n<li>Supplier quota at level distribution center</li>\n<li>Purchasing contracts</li>\n<li>Sales orders</li>\n<li>Sales contracts</li>\n<li>Order optimization: online planning (transaction WWP1), load building, investment buy</li>\n<li>Allocation table: other scenarios than mentioned above</li>\n<li>MRP for stores</li>\n<li>Replenishment (transaction WRP1R) for distribution centers</li>\n<li>Automatically considering of all relevent Procurement Products in purchase order and stock/requirements lists and ATP related processes</li>\n<li>Promotion</li>\n<li>In-store Fiori apps</li>\n<li>Object page for articles: not possible to list Procurement Products of a Sales Product</li>\n<li>An internal unified API for product master maintenance has been developed. This does not support Logistical Products, see note <a href=\"https://me.sap.com/notes/3408057\" target=\"_blank\">3408057</a>, and <a href=\"https://me.sap.com/notes/3426439\" target=\"_blank\">3426439</a>. As a consequence the following do not support Logistical Products:</li>\n<ul>\n<li>Fiori app <em>Maintain Product Master</em> (F1602)</li>\n<li>Migrate your data Fiori app (for products)</li>\n<li>Inbound integration scenario through SOAP Service ProductMDMBulkReplicateRequest_In</li>\n<li>Outbound integration scenario through SOAP Service ProductMDMBulkReplicateRequest_Out via Data Replication Framework (DRF), see note <a href=\"https://me.sap.com/notes/3407965\" target=\"_blank\">3407965</a></li>\n<li>OData API API_PRODUCT_SRV</li>\n<li>Product maintenance via MDG: mass processing/consolidation Fiori app/change requests</li>\n</ul>\n<li>ALE Integration via IDoc ARTMAS from S/4HANA to ERP\r\n<ul></ul>\n</li>\n<li>Migration from Logistical Variants in ERP to Logistical Products in S/4HANA</li>\n<li>Integration with SAP Forecasting &amp; Replenishment</li>\n<li>Integration with SAP CAR (Customer Activity Repository), and consuming solutions of CARAB</li>\n<ul>\n<li>SAP Promotion Management Retail</li>\n<li>SAP Merchandise Planning</li>\n<li>SAP Assortment Planning</li>\n<li>SAP Allocation Management Retail</li>\n</ul>\n</ul>", "noteVersion": 9}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Business needs to evaluate if this missing functionlity is critical to the business."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If customer specific coding re-used development objects in ERP which are no longer available in SAP S4/HANA, the coding needs to be adjusted accordingly."}]}