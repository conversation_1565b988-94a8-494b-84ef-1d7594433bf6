SAP Note 1773094 addresses an issue where the generation of receivables and payables terminates with a short dump 'ITAB_DUPLICATE_KEY' in the method G_GROUP_ITEMS_P. This problem is related to profit center and segment reorganization (referred to as 'Reorg') and grouping (referenced as 'GRP').

The cause of the issue is identified as a program error during the generation process. To fix this problem, the note recommends implementing the correction instructions provided within the note. The note does not go into detail about the exact correction instructions within the summary given, but it implies that these instructions are available within the full contents of the note to resolve the error.