SAP Note 892613 addresses an issue encountered in the master data transactions within the Real Estate Management (RE-FX) module, specifically when using transaction RE80 (the RE navigator) to navigate through these transactions. The note outlines the observed symptom, provides the technical context and reason behind the issue, and advises on a solution.

**Symptom:** Users are experiencing discrepancies in the tabs displayed in the master data dialogs when accessing them directly through master data transactions versus through the RE navigator (transaction RE80).

**Other Terms:** Certain SAP terms like 'Transaction variant,' 'screen variant,' SHD0 (transaction for maintaining transaction and screen variants), and related technical function modules (SAPLRECA_BDT_APPL_TOOL, SAPLBUSS, SAPLRECA_BDT_APPL_INITIAL) are referenced for context.

**Reason and Prerequisites:** The issue arises from the creation and use of transaction and screen variants for master data transactions in RE-FX. The core reason the problem occurs is that the initial screens of transactions such as REBDBE, BEBDBU, REBDPR, REBDRO, RECN, etc., are specifically designed for transaction RE80 (RE navigator) and do not behave like classical transaction screens. Adjusting screen elements like tabs via variants on one of these special initial screens can unintentionally affect the display of tabs on master data dialogs for other objects within the RE-FX module.

**Solution:** The recommendation provided to resolve this issue is to delete the transaction and screen variants that were created (using transaction SHD0). Instead of using transaction and screen variants to modify the dialogs, the note suggests utilizing the Business Data Toolset (BDT) provided by the RE-FX module to customize the dialog layouts in a way that is supported and will not result in the unintended consequences mentioned. Guidance on how to achieve this can be found in the Implementation Guide for Flexible Real Estate Management (RE-FX), under the relevant menu item for the master data.

In summary, SAP Note 892613 advises against the use of transaction and screen variants for altering the master data transaction screens in RE-FX due to their special design related to transaction RE80. It provides instructions for reverting such changes and recommends using BDT to customize the dialog screens instead.