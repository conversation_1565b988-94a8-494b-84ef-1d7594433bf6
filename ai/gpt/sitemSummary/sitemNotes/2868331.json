{"Request": {"Number": "2868331", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 522, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000134062020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002868331?language=E&token=326F8E5E1343DBE7A49B201E73957131"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002868331", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002868331/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2868331"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.02.2020"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2868331 - SAP S/4HANA Cloud 2002: Release Restriction Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are using&#160;<em>SAP S/4HANA Cloud 2002.&#160;</em>This note&#160;informs you about any restrictions&#160;in this release. This SAP Note is subject to change, please check it for regular updates.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li><strong>General:</strong></li>\r\n<ul>\r\n<li>General Release Information Note for&#160;<em>SAP S/4HANA Cloud 2002</em>&#160;can be seen here: SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2868297\">2868297</a></strong>.</li>\r\n<li>Restrictions&#160;valid for SAP Fiori visual theme for classic applications are described in the SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2428021\">2428021</a>.</strong></li>\r\n<li><em>SAP S/4HANA Cloud for Contract Accounting and Invoicing</em>&#160;is released for Germany/ US only.</li>\r\n<li>For restrictions relevant to Enterprise Search functionality, please see SAP Note:&#160;SAP Note:&#160;<strong><a target=\"_blank\" href=\"/notes/2768916\">2768916</a></strong>.</li>\r\n<li>For deprecated Scope Items in R&amp;D/ Engineering, please&#160;see SAP Note:&#160;<strong><a target=\"_blank\" href=\"/notes/2792363\">2792363</a></strong>.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Configuration &amp; SSCUIs:</strong></li>\r\n<ul>\r\n<li>Configurations are done via available Self-Service Configuration UIs (SSCUIs) and pre-approved expert configurations (templates).</li>\r\n<li>Maintenance of scope (scope items, languages) needs to be done before content activation and other configurations happen. Scope extensions can be done after initial activation.</li>\r\n<li>Adjustment of pre-configured organizational data in the \"Manage Your Solution\" app needs to be done prior to content activation. Extension of organizational structure after content activation will be still possible.</li>\r\n<li>If you need to adapt the IDs of SAP Best Practice G/L accounts (Operative Chart of Accounts (YCOA), Group Chart of Accounts (YGR1) and Local Chat of Accounts), use the \"Renumber G/L Account\" configuration step in the Manage Your Solution app and the Convert Renumbered G/L Accounts app after activation of SAP S/4HANA Cloud Q-system (ideally in Realize phase). Renumbering of a G/L Account needs to be executed before first posting is entered for that G/L account in productive system. To keep the SAP standard CoA, confirm the Chart of Accounts settings in the \"Renumber G/L Accounts\" configuration step in the Manage Your Solution app.</li>\r\n<li>Deletion of SAP-owned/ shipped customizing entries in SSCUIs is not allowed. Customer entries can only be deleted in certain enabled SSCUIs.</li>\r\n<li>Currently, only one change of a project is allowed at a time.</li>\r\n<li>Currently, only one country solution can be changed at a time across the entire customer project. Simultaneous changes to different countries are not allowed, even if conducted by different users.</li>\r\n<li>Configure Your Solution app:</li>\r\n<ul>\r\n<li>Configuration step (SSCUI) \"Define Tax Codes for Sales and Purchases\" works for USA, Germany, Hong Kong, Italy, China, New Zealand, Slovakia, South Korea only. Please use accelerators from <strong><a target=\"_blank\" href=\"https://go.support.sap.com/roadmapviewer/#/group/BE47098A-617A-43EF-A27E-DFD801D70483/roadmap/IMPS4HANACLDENMGMT:001999B7BD851ED68D97F853D2C762CE/node/901B0E6D3F441ED78DE8F084DE1F8E7B\">Configure Tax Solution</a></strong>&#160;if you need to add or change any tax rates in other countries.</li>\r\n<li>While using the SSCUI \"Set Up Payment Methods per Country for Payment Transactions\" (ID: 101972), please consider that only 9 new payment method IDs can be created. The following IDs are specifically reserved for customers: 6, 7, 8, 9, U, V, W, Y and Z.</li>\r\n<li>There are 5 payment method IDs marked as automatic payment (no approval) in the Bank Communication Management app. Currently, the approval process is not supported for these 5 payment methods and there is also no SSCUI available to change it.</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Extensibility:</strong></li>\r\n<ul>\r\n<li>Custom analytical queries as well as the preview functionality for custom CDS views are not enabled for read access logging.</li>\r\n<li>Data protection: the management of data in an extension scenario deviates from the management of data in the standard scenarios. You are responsible for ensuring that the data used in an extension scenario is managed in accordance with any applicable legal requirements or business needs, such as data protection legislation or data life cycle requirements. Please note that the extensibility framework is currently not integrated in the privacy-by-default functionality of SAP S/4HANA. Therefore, the extensibility framework should not be used for the processing of personal data if this processing falls under any applicable data protection legislation.</li>\r\n<li>Extensibility Cockpit supports English only, irrespective of logon language.</li>\r\n<li>Released ABAP Artifacts support the logon language for Technical Content of ABAP artifacts. In case content is not available in the logon language, this content will be displayed in the language it was created.</li>\r\n<li>Due to possible changes in the underlying Virtual Data Model content, you might have to adapt your Custom CDS content after the upgrade. For further information see SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2540701\">2540701</a></strong>.</li>\r\n<li>A custom CDS view inherits its protection exclusively from its primary data source.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Financial-specific:</strong></li>\r\n<ul>\r\n<li>For&#160;printing of lists please&#160;use&#160;SAP Fiori app \"Display G/L Account Line Items\" and perform the \"Download to Excel\" and&#160;the \"Print from Excel\".</li>\r\n<li>If you want to create an E-Balance sheet for Germany:</li>\r\n<ol>\r\n<li>Upload the taxonomy into the system</li>\r\n<li>Enter the mapping of the accounts to the taxonomy positions</li>\r\n<li>You can now either:</li>\r\n<ul>\r\n<li>download and install the ERP standalone client for&#160;E-Balance&#160;and enter the data into this excel sheet manually, validate and send off to the authority or</li>\r\n<li>transfer the mapped data to your tax consultant</li>\r\n</ul>\r\n</ol>\r\n<li>Taxes:</li>\r\n<ul>\r\n<li>Currently, it is not possible to restrict the auditor's access by time frame. Auditors can access all tax-relevant data, regardless of date.</li>\r\n</ul>\r\n<li>Tax calculation in the US:</li>\r\n<ul>\r\n<li>The internal US tax calculation in&#160;<em>SAP S/4HANA Cloud 2002&#160;</em>and any reporting based on this calculation may not comply with all reporting requirements in your jurisdiction due to the specifics of the tax law system in the United States. You must check with your accounting or tax experts&#160;to make sure that the results generated by this report are fully compliant with your relevant jurisdictions&#8217; specific sales and use tax reporting requirements. For further information on these restrictions, please refer to the SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/**********\">2440096</a></strong>.</li>\r\n</ul>\r\n<li>Asset Accounting: as previously announced, the app Total Depreciation has become obsolete with the 1911 release of <em>SAP</em>&#160;<em>S/4HANA Cloud</em>. Fiori app Asset Balances or the app Asset Balances (Accessible) should be used as successor apps instead. However, any forecast of calculated depreciation for future fiscal years that have not yet been opened is currently not supported by these apps. This functionality is currently in planning and should be made available within the next 1-2 releases. Until this functionality is shipped, please extract the data to a spreadsheet and perform a manual calculation of predicted forecast depreciation values.</li>\r\n<li>Asset Accounting (US): Luxury Automobile Limitations and 100% Bonus Depreciation:&#160;<em>SAP S/4HANA Cloud 2002&#160;</em>provides a MANUAL default Depreciation Key (MANU) for the US Tax Books in asset class 3100 (Vehicles). Please enter depreciation for the affected vehicle assets for each individual year. This may be done by selecting the Fiori app \"Post Depreciation Manually\", entering asset and affected depreciation areas. The transaction type is 610 for a current year asset or 600 for a prior year asset. The affected US Tax Books are:</li>\r\n<ul>\r\n<li>90 ACRS/MACRS Federal Tax</li>\r\n<li>91 ALT MIN Alternative Minimum Tax</li>\r\n<li>92 ACE Adjusted Current Earnings</li>\r\n</ul>\r\n<li>It is only possible to do validation &amp; substitution on journal entries for the fields, which are delivered under Validation &amp; Substitution Cloud BADIs.</li>\r\n<li>Account Determination app:</li>\r\n<ul>\r\n<li>It is not allowed to change the posting keys or rules for a transaction key</li>\r\n<li>It is not allowed to delete any existing records</li>\r\n</ul>\r\n<li>Data Aging: with the following apps, you cannot select journal entries that have been moved to the historical part of the database:</li>\r\n<ul>\r\n<li>Display G/L Account Balances</li>\r\n<li>Display Line Items in General Ledger</li>\r\n<li>Display Line Item Entry</li>\r\n</ul>\r\n<li>Revenue Recognition (Event-based) &#8211; Sales Order: only standard Sales Order-based Sell from Stock processes and Delivery-based Billings will trigger Revenue Recognition postings (Sales Document Item Category TAN). Variants of Sell from Stock processes such as the following will not trigger any Revenue Recognition postings:</li>\r\n<ol>\r\n<li>returns</li>\r\n<li>intercompany sales</li>\r\n<li>third-party sales</li>\r\n</ol>\r\n<li>For restrictions relevant for \"Product Costing: Real-Time Work in Process\", please refer to the SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2737228\">2737228</a></strong>.</li>\r\n<li>Based on the third currency enablement, Work in Process (WIP) values under BEI scope item are calculated in company code currency and group currency only. WIP values in the third currency are converted during settlement. This can lead to differences in the WIP account. Values for variances&#160;are&#160;also converted to&#160;the third currency&#160;and not calculated for all currencies in parallel.</li>\r\n<li>Management Accounting and Margin Analysis - Predictive Accounting:</li>\r\n<ul>\r\n<li>If you use predictive accounting with the following business scenarios, please consider that there is no simulation of goods issue and predicted costs cannot be calculated:</li>\r\n<ul>\r\n<li>Third-party direct shipment</li>\r\n<li>Intercompany sales within selling company</li>\r\n</ul>\r\n<li>Predictive accounting only processes the following SD document categories:&#160;C - Order;&#160;H - Returns; I - Order Without Charge; K - Credit Memo Request; L - Debit Memo Request</li>\r\n</ul>\r\n<li>Group Ledger:</li>\r\n<ul>\r\n<li>It is not possible to activate the Scope Items for Group Leger &#8211; IFRS together with the Scope Items for Group Ledger &#8211; US GAAP. You can only activate one of them.</li>\r\n</ul>\r\n<li>General Ledger, Asset Accounting, Contract and Lease Management, Single ledger/ Parallel ledger:</li>\r\n<ul>\r\n<li>Ledger bundles are possible for three following options only:</li>\r\n<ol>\r\n<li>Local ledger only</li>\r\n<li>Local ledger + IFRS (free choice of which should be leading)</li>\r\n<li>Local ledger + US GAAP (free choice of which should be leading)</li>\r\n</ol>\r\n<li>Ledger setup cannot be reverted after initial activation</li>\r\n</ul>\r\n<li>Group Reporting:</li>\r\n<ul>\r\n<li>Consolidation of investments (rule-based): consolidation adjustments required for the consolidation of units according to at-equity method are not automatically posted.</li>\r\n<li>Restrictions and additional information on Consolidation of Investments (activity-based):&#160;are collected in the SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2812499\">2812499</a></strong>.</li>\r\n<li>Restrictions valid for Fiscal Year Variant in Group Reporting are described in the SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2844616\">2844616</a></strong>.</li>\r\n<li>Interunit Reconciliation &#8211; Group View app is not supported for customers with the initial release&#160;<em>SAP S/4HANA Cloud 1902</em>&#160;or higher. Please use the Intercompany Matching and Reconciliation (ICMR) solution instead.</li>\r\n<li>Intercompany Matching and Reconciliation (ICMR): it is possible to use a data source that has the accounting document data stored as data entry view (in the BKPF and BSEG tables). If there are discrepancies for matched documents, currently you&#160;can&#8217;t trigger automatic document posting back to the BKPF and BSEG tables of the accounting side. Please enter the values manually.&#160;</li>\r\n<li>It is not possible to assign different ledgers to different consolidation groups within one single version. When multiple group currencies are required for consolidation groups, the corresponding ledgers must be assigned to consolidation groups in different versions.</li>\r\n<li>The deletion of versions is currently not supported.</li>\r\n<li>It is possible to add new values for the delivered attributes of the Financial Statement item. Creating new attributes is currently not supported.</li>\r\n<li>In the reports available in the tile Group Reports, which are based on the old queries for consolidated data (&lt;&#160;<em>SAP S/4HANA Cloud 1902</em>&#160;release), the drill-through to the consolidation reports shows a technical document generated by the integration task&#160;and not the document of origin.</li>\r\n<li>Only one unit of measure can be assigned to a breakdown category. Data cannot be reported with a different unit of measure for the FS (Financial Statement) items using this breakdown category.</li>\r\n<li>Extensibility: if you also want to see custom fields from accounting in Group Reporting, please use the business context \"Accounting: Coding Block\".&#160;</li>\r\n</ul>\r\n<li>Third Currency:</li>\r\n<ul>\r\n<li>It is possible to post currency adjustments in the Freely Defined Currency in GL but not in Asset Accounting, where you can only maintain adjustments to the asset value in the company code currency; this value will then be converted to the Freely Defined Currency. The Freely Defined Currency is not yet available in all reports and dash boards.</li>\r\n</ul>\r\n<li>Reprocess Bank Statement Items:</li>\r\n<ul>\r\n<li>When creating a reprocessing rule directly out of the app Reprocess Bank Statement Items you will experience issues if you pressed on the \"GO\" button upfront. In that case, only the first rule created will display also the \"Post to G/L Account\" Tab. The second rule you want to create afterwards will display the rule without the \"Post to G/L Account\" Tab. Please change the Action Type via the dropdown list manually to see the \"Post to G/L Account\" tab.</li>\r\n</ul>\r\n<li>Treasury and Risk Management:</li>\r\n<ul>\r\n<li>With <em>SAP S/4HANA Cloud 2002</em>, the termination functionality is available for FX transactions (FX forwards, FX options, non-deliverable forwards, FX swaps and FX collars). After a financial transaction has been terminated, it is not visible in the Cash Flow Analyzer app in Cash Management. The termination flow is also not displayed.</li>\r\n</ul>\r\n<li>Product Cost Controlling:</li>\r\n<ul>\r\n<li>Scrap and variance split per variance categories are not supported under solution Period-End Closing - Plant (&#8207;BEI&#8207;) in S/4 HANA Cloud.</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Human Resources:</strong></li>\r\n<ul>\r\n<li>Integration between SAP SuccessFactors Employee Central and&#160;<em>SAP S/4HANA Cloud 2002</em>&#160;does not support employees with multiple employments (Concurrent Employment, Global Assignment) and employees going through an inter-company transfer.</li>\r\n<li>Employee central time-off API is restricted and does not support delta handling (when called with lastModified). It is recommended to replicate no more than 120 days at a time during initial load. This can be done by setting the parameters RETRO_PERIOD and FIXED_NO_OF_DAYS to give a sum of no more than 120 (e.g. RETO_PERIOD = 60 (90), FIXED_NO_OF_DAYS = 60 (30). In addition, the Employee Central time-off API does not support the replication of Availability data of contingent workers being hired as regular employees.</li>\r\n<li>Contingent Worker Replication:&#160;\"Change start date of work order &amp; extend end date of work order\" scenario is possible within&#160;<em>SAP SuccessFactors Employee Central</em>, but replication is not supported.</li>\r\n</ul>\r\n<ul>\r\n<li>Handling of freelancers is currently not supported. Although, creation of freelancers is possible through import employee/ public API and Success Factors Employee Central, there are no standard&#160;<em>SAP S/4HANA Cloud</em>&#160;processes defined. Please use the role Service Performer instead.</li>\r\n<li>Any HR-related data must go via source HR system only and not via a direct modification in Business Partner Maintenance transaction.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Integration:</strong></li>\r\n<ul>\r\n<li>The following interfaces between SAP Ariba Guided Buying capability and&#160;<em>SAP S/4HANA Cloud 2002</em>&#160;(scope items 2NV, 3EN) are currently not supported. The solution is planned to be delivered in the upcoming releases. A Guided Buying Requisition can be edited only until it is approved in SAP Ariba Guided Buying. The following are yet to be supported:</li>\r\n<ul>\r\n<li>Editing a PR (Purchase Requisition) and in the process adding a new line item.</li>\r\n<li>Editing a PR and in the process deleting an existing line item.</li>\r\n<li>Attachments added during PR creation or when a PR is edited.</li>\r\n</ul>\r\n<li>The following features between&#160;<em>SAP Ariba Contracts</em>&#160;and&#160;<em>SAP S/4HANA Cloud 2002</em>&#160;(scope item 4AZ) are currently not supported. The solution is planned to be delivered in the upcoming releases.</li>\r\n<ul>\r\n<li>Attachments are not yet supported in&#160;<em>SAP Ariba Contracts</em>.</li>\r\n<li>If the document type or a supplier is changed in an existing contract in&#160;<em>SAP Ariba Contracts</em>, these changes will not be reflected in&#160;<em>SAP S/4HANA Cloud</em>.</li>\r\n<li>Purchase contracts transferred from&#160;<em>SAP Ariba Contracts</em>&#160;can only be approved&#160;automatically.</li>\r\n<li>Only quantity contracts are currently supported. No value contracts are supported.</li>\r\n<li>The contracts cannot contain volume scales, value scales, graduated scales and percentage conditions.</li>\r\n<li>Only lean services can be created in&#160;<em>SAP S/4HANA Cloud.</em></li>\r\n<li>Multi-currency contracts currently not supported.</li>\r\n<li>Integration with central purchase contracts in&#160;<em>SAP S/4HANA Cloud</em>&#160;is not yet supported.</li>\r\n<li>Extensibility of contracts is not yet supported.</li>\r\n</ul>\r\n<li>The following restrictions apply in the context of SD/ FI-CA integration (Scope Item 43Y). Currently, it is not possible to:</li>\r\n<ul>\r\n<li>Use the new Fiori app for sales order creation.</li>\r\n<li>Create a milestone billing plan in a sales order with VA01. E.g. by using the order type OR and item category CTAD.</li>\r\n<li>Create a scheduling agreement with VA31.</li>\r\n<li>Create a contract (value or quantity) with VA41.</li>\r\n<li>Create a project (scope item J11 and J14).</li>\r\n<li>Use Official Document Numbering.</li>\r\n<li>Use Tax-Abroad. Tax information from BSET is not read and printed on the bill.</li>\r\n<li>Use EDOC-Framework. With release to accounting a message to the government authority cannot be created.</li>\r\n<li>Use Extensibility. The extensibility flow from billing item to journal entry item is not supported.</li>\r\n<li>Use some special logisitic processes in delivery. BOM, Batch and Variant Configuration are not supported.</li>\r\n<li>Use some special processes in Professional Service. E.g. Account-Payment and RevRec are not supported.</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Localization:</strong></li>\r\n<ul>\r\n<li>For localization-specific restrictions, please see the Country Versions: Release Information &amp; Restriction Note (SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2785449\">2785449</a></strong>).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Master Data:</strong></li>\r\n<ul>\r\n<li>SAP note&#160;<strong><a target=\"_blank\" href=\"/notes/2690843\">2690843</a></strong>&#160;contains information about the searches that are currently not supported for the CDS based search connector &#8220;Products&#8221;.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Procurement:</strong></li>\r\n<ul>\r\n<li>Procurement Overview Page: List Apps are not sorted and filtered in the same way as the cards. When navigating from the card headers, the corresponding app might be filtered and sorted in a different way than the card is filtered and sorted in the Overview Page. This restriction only applies to the transactional cards \"Monitor Purchase Order Items\" and \"Monitor RFQs\".</li>\r\n<li>Currently, customer and vendor consignment processes and consignment stocks with a border crossing are not supported. Only domestic consignment processes are supported.</li>\r\n<li>The value &#8220;empty&#8221; is not supported by the Define Conditions tab in the value help dialog of the filters; when applying a filter in the table settings. This restriction is applicable for the following apps:</li>\r\n<ul>\r\n<li>Mass Changes to Purchase Orders</li>\r\n<li>Mass Changes to Purchase Contracts</li>\r\n<li>Mass Changes to Purchase Scheduling Agreements</li>\r\n<li>Mass Changes to Purchasing Info Records</li>\r\n<li>Mass Changes to Central Purchase Contracts</li>\r\n</ul>\r\n<li>Tasklist doesn't load into My Inbox when a work item is received for approval for a Purchase Requisition/ Purchase Order and the approver navigates from a Fiori push notification. Please press the refresh button on the task list. The solution is planned to be delivered via a HotFix.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Produce:</strong></li>\r\n<ul>\r\n<li>Backorder processing (BOP):</li>\r\n<ul>\r\n<li>Requested schedule line is not displayed correctly in the BOP monitor. Please display item details to see all confirmed schedule lines and ignore the aggregation line.</li>\r\n<li>Stock transfer requisitions are not supported for selection in BOP segments. Use stock transport orders as BOP-relevant requirements instead.</li>\r\n<li>It is not possible to use characteristic ATPRELEVANTDOCUMENTCATEGORY to differentiate document types when defining BOP Segment anymore.</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Product Lifecycle Management (PLM):</strong></li>\r\n<ul>\r\n<li>Restrictions on PLM &#8211; Advanced Variant Configuration can be found under the SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2872090\">2872090</a></strong>.<strong><br /></strong></li>\r\n<li>Fiori UI for Structures:</li>\r\n<ul>\r\n<li>You can only include individual elements or sub-structures between projects of the same profile (e.g. Enterprise Project - Internal).</li>\r\n<li>When you copy revenue projects with profile Enterprise Project - Internal assigned, the settlement rules will not be copied to the new project.&#160;When you copy a project with a profile Enterprise Project &#8211; Internal to a new project, the assigned settlement profile of the source project is copied for projects with the profile Overhead Project or Investment Project. It is recommended to copy projects that have the same project profile assigned. If required, you can change the settlement profile in the new project.</li>\r\n</ul>\r\n<li>It is possible to upload files no larger than 100MB from&#160;<em>SAP Fiori for SAP S/4HANA</em>&#160;applications.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Professional Services:</strong></li>\r\n<ul>\r\n<li>Material determination configuration within the DIP&#160;(Dynamic Item Processor)&#160;is not possible now. Therefore, additional material/ activity&#160;types T* should&#160;not be created.&#160;Please take the existing 20 T* materials and rename them.</li>\r\n<li>Project Forecast Month in Customer Project uses the universal time zone. The project forecast in the Customer Project Review application is set to the correct month only if a Project manager waits until the local time and the universal time fall on the same month.</li>\r\n<li>It is not possible to assign a central purchasing organization to a service organization, while purchasing itself is possible. Please create a local purchasing organization to assign it to service organizations.</li>\r\n<li>A purchase order item with multiple account assignment objects such as WBS element and cost center is not supported. Please use only one account assignment object.</li>\r\n<li>If a purchase order with Account Assignment Category P (Project) is used for Service Procurement, the note field is restricted to 40 characters.</li>\r\n<li>If a purchase order with Account Assignment Category P (Project) or U (Unknown) is used for Service Procurement, the blocking functionality on work package level is not supported.</li>\r\n<li>Concurrent employments are not supported during maintenance of project roles that are not delivered by default. The first valid employment of a Business Partner gets forwarded to the Sales Order.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Project System / Enterprise Portfolio and Project Management:</strong></li>\r\n<ul>\r\n<li>Note text related to Billing Items (Fixed Price, Time and Expense Items) of your Project:</li>\r\n<ul>\r\n<li>Fixed Price Items: if the note text is changed via the \"Plan Customer Project\" app, this change will presently not be reflected in the corresponding billing proposal item entry in the \"Release Billing Proposals\" app.</li>\r\n<li>Time and Expense Items: if the note text for an actual cost posting in the \"Release Billing Proposals\" app is modified by a project manager, a modification in \"My Timesheet\" app by an employee will overwrite the note text that the project manager previously created. Upon finding a different note text in the \"Release Billing Proposals\" app, the project manager can manually edit/ update the note text before releasing the billing proposal item(s).</li>\r\n<li>Restrictions of SAP CoPilot while using \"Release Billing Proposals\" app: it is not possible to navigate to a screenshot of Billing Proposal Item Details through collection using SAP CoPilot&#160;in a new session of the SAP Fiori Launchpad. You will be navigated to the screen containing the list of billing proposals instead. \"Go to Linked Screen\" navigates to the last saved URL of the current browser user session. Project manager can access the \"Release Billing Proposals\" app, select the relevant billing proposal(s) and manually navigate to the billing proposal items screen, if needed.</li>\r\n</ul>\r\n<li>Grouped billing proposal items: when you perform a defer after date function, please expand all the grouped entries to make sure that billing proposal items display the correct total amount and correct quantities for each individual group.</li>\r\n<li>Project Builder app: it is currently not possible to simultaneously change the Responsible Cost Center and Profit Center belonging to a different company code. Please use the Project Planning app to make this change.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Public Sector Management (PSM):</strong></li>\r\n<ul>\r\n<li>following functionalities cannot be used by&#160;Public Sector customers and are not integrated by PSM:</li>\r\n<ul>\r\n<li>Settlements</li>\r\n<li>Create Single Payment Direct</li>\r\n<li>Free Form Payment Request</li>\r\n<li>CO Planning</li>\r\n</ul>\r\n<li>Financial statement version is currently not reflecting the format of US Public Sector organizations. The functionality to support public sector-specific versions and layouts is planned to be available with a later release.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Responsibility Management:</strong></li>\r\n<ul>\r\n<li>You, as data controller, are responsible for ensuring that the data used in responsibility definitions in teams is managed in accordance with any applicable legal requirements or business needs, such as data protection legislation or data life cycle requirements. Responsibility definitions in the Fiori app \"Manage Teams and Responsibilities\" are currently not integrated in privacy by default functionality. Therefore, the responsibility definitions in teams should not be used for the processing of personal data if this processing falls under any applicable data protection legislation.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Sales &amp; Distribution:</strong></li>\r\n<ul>\r\n<li>Controls used in the&#160;apps \"Business Process Activities\" and \"Aggregated Business Process Activities for Order-to-Cash Process Performance Monitoring\" cannot&#160;currently display backwards connections. So, some information referring to previous process steps will not be reflected on the diagrams.</li>\r\n<li>Batch split: freight costs entered in the delivery cannot be billed for delivery items with batch splits. The batch split main item has zero quantity, weight and volume, but is relevant for billing. The quantities, weight and volume are distributed among the batch split sub-items which are not relevant for pricing and billing. See also note&#160;<strong><a target=\"_blank\" href=\"/notes/1921287\">1921287</a></strong>.</li>\r\n<li>Currently, customer and vendor consignment processes and consignment stocks with a border crossing are not supported. Only domestic consignment processes are supported.</li>\r\n<li>Sales Billing:</li>\r\n<ul>\r\n<li>When using the process &#8216;Quantity contract-based down payments&#8217; (scope item I9I), rounding differences with regards to the deducted down payments may occur in the partial and final invoices. Even though, the complete invoice amount is covered by available down payments, a small amount e.g. 1 cent may appear as open for payment. In FI the postings for the down payment settlement would nevertheless appear correct.</li>\r\n</ul>\r\n<li>Existing Smart KPI apps: Check Open Sales, Profit Margin, Credit Memo, Confirmed as Requested, Backorders and Demand Fulfillment Smart Business apps show an additional dimension &#8222;By Product Hierarchy&#8220;, which is not related to any values. Unfortunately, it can&#180;t be removed, please ignore it.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>SAP Analytics Cloud (SAC):</strong></li>\r\n<ul>\r\n<li><em>SAP Analytics Cloud</em> stories can be created in the app Manage KPIs and Reports. Please make sure to use another browser other than Internet Explorer, as it is not supported for story creation.</li>\r\n<li><em>SAP Analytics Cloud</em> does not support right to left languages at the moment.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>SAP S/4HANA Cloud for invoice processing by OpenText:</strong></li>\r\n<ul>\r\n<li>This solution supports a subset of all countries supported by&#160;<em>SAP S/4HANA Cloud</em>. Please check the&#160;<strong><a target=\"_blank\" href=\"https://support.sap.com/content/dam/SAAP/Sol_Pack/Library/Setup/1LE_Set-Up_EN_XX.pdf\">Customer Set-Up Guide</a></strong>&#160;for scope item \"Invoice Processing by OpenText\" (1LE) for further details.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Service Management:</strong></li>\r\n<ul>\r\n<li>Inter-company cost postings are supported for service orders and for repair orders, but Inter-company billing is not supported for the same. If a service order item or a repair order item is confirmed by an employee belonging to a different company than the service order or repair order company code, then the following issues occur:</li>\r\n<ul>\r\n<li>By executing a service confirmation or a repair confirmation, the user does not get notified that an intercompany posting will be triggered.</li>\r\n<li>Based on this intercompany confirmation there will be intercompany accounting documents posted in both companies. In the sending company code, the employee cost center will be credited and in the receiving company code the service order or repair order item will be debited. To fulfill consolidation and tax requirements an additional manual posting needs to be done by the accountant (the intercompany cost accounting postings are not yet considered in periodic intercompany billing).</li>\r\n<li>For activity allocation, the content supports only the G/L accounts ******** and ********. If there are additional activity types created and assigned to the service order material or repair order material, it needs to be ensured that one of these G/L accounts are assigned. If a different one is used, the time confirmation will be created, but cannot be transferred to Financials.</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Transportation Management:</strong></li>\r\n<ul>\r\n<li>One-time addresses from SD (Sales and Distribution)&#160;cannot be used as source or destination locations in freight unit and freight order documents.</li>\r\n<li>As of today, it is not possible to connect&#160;<em>SAP Transportation Management On Premise</em>&#160;planning system to&#160;<em>SAP&#160;S/4HANA Cloud</em>&#160;due to unavailability of interfaces in the OP system.</li>\r\n<li>Evaluated Receipt Settlement process is not supported for setting freight cost with the carrier.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Two Tier:</strong></li>\r\n<ul>\r\n<li>For Two Tier-relevant restrictions please see SAP Note&#160;<strong><a target=\"_blank\" href=\"/notes/2697118\">2697118</a></strong>.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>View Browser Application:</strong></li>\r\n<ul>\r\n<li>If the table variant is modified in View Browser, there could be a possibility to have an outdated app state.&#160;It is, therefore, not recommended to bookmark the page, but rather to re-launch the application.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Warehouse Management:</strong></li>\r\n<ul>\r\n<li>In some catalogs for business roles SAP_BR_WAREHOUSE_OPERATIVE_EWM and SAP_BR_WAREHOUSE_CLERK_EWM, the restriction of authorizations to a warehouse number and the restriction of read/ write access is not fully supported. You can find details in the documentation of the affected business catalogs.</li>\r\n</ul>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D068020"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036530)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002868331/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002868331/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002868331/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002868331/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002868331/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002868331/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002868331/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002868331/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002868331/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2872090", "RefComponent": "LO-VCH", "RefTitle": "Restrictions for Advanced Variant Configuration SAP S/4HANA 2002 CE", "RefUrl": "/notes/2872090"}, {"RefNumber": "2868297", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 2002: Release Information Note", "RefUrl": "/notes/2868297"}, {"RefNumber": "2844616", "RefComponent": "FIN-CS", "RefTitle": "Fiscal year variant: Informations and restrictions", "RefUrl": "/notes/2844616"}, {"RefNumber": "2812499", "RefComponent": "FIN-CS-COR", "RefTitle": "FAQ Activity-based consolidation of investments in SAP S/4HANA Cloud for group reporting", "RefUrl": "/notes/2812499"}, {"RefNumber": "2792363", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2792363"}, {"RefNumber": "2785449", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 2002, Local Versions: Release Information & Restriction Note", "RefUrl": "/notes/2785449"}, {"RefNumber": "2768916", "RefComponent": "CA-GTF-HSB", "RefTitle": "Potential unauthorized access to data when peforming search in the Fiori Launchpad Search UI", "RefUrl": "/notes/2768916"}, {"RefNumber": "2737228", "RefComponent": "CO-PC-OBJ-ORD-2CL", "RefTitle": "SAP S/4HANA Cloud: Event-Based Work in Process (WIP) Posting for Product Cost by Order", "RefUrl": "/notes/2737228"}, {"RefNumber": "2697118", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2697118"}, {"RefNumber": "2690843", "RefComponent": "LO-MD-MM", "RefTitle": "CDS based search connector \"Products\"", "RefUrl": "/notes/2690843"}, {"RefNumber": "2540701", "RefComponent": "BC-SRV-APS-GKE", "RefTitle": "Custom CDS Views – Necessary Adoptions after Upgrade", "RefUrl": "/notes/2540701"}, {"RefNumber": "2440096", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 1705 (and all later releases) Restrictions Internal US Tax Calculation", "RefUrl": "/notes/2440096"}, {"RefNumber": "2428021", "RefComponent": "XX-SER-REL", "RefTitle": "S/4HANA Cloud 1711 (and later releases): Restrictions on WebGUI/HTML GUI", "RefUrl": "/notes/2428021"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2901554", "RefComponent": "FIN-CS-COR", "RefTitle": "Consulting note to correct Version / Ledger combinations", "RefUrl": "/notes/2901554 "}, {"RefNumber": "2868297", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 2002: Release Information Note", "RefUrl": "/notes/2868297 "}, {"RefNumber": "2785449", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Cloud 2002, Local Versions: Release Information & Restriction Note", "RefUrl": "/notes/2785449 "}, {"RefNumber": "2868852", "RefComponent": "FI-GL-GL-F", "RefTitle": "Enhancement of Scheduling Agreement by the tax rate valid-from date for time-dependent tax calculation", "RefUrl": "/notes/2868852 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}