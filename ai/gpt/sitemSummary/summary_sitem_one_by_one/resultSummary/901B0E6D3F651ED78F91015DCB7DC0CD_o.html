<h3>SI06: OGSD_COLLECTIVE_ORDERS</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2490624">2490624 - S4TWL - OGSD - Collective Orders</a></strong></p>
<p><strong>Description:</strong></p>
<p>Collective Orders is an OGSD application that involves customers ordering materials for themselves and for other customers in the surrounding area, for example, to obtain better conditions from the supplier. The business advantages of collective orders include more efficient use of means of transport and more options for pricing, due to lower shipment costs and the fostering of relationships between the customer and the company. The ordering parties for a collective order are clearly mapped in a customer hierarchy.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Please note:
1. OGSD application "Collective Orders" is discontinued when converting to SAP S/4HANA and using S4SCSD Release 1.0. In this case all its program- and DDIC-objects are lost after a conversion to SAP S/4HANA.
2. But this application is again available when converting to SAP S/4HANA and using S4SCSD Release 2.0. In this case all its program- and DDIC-objects are kept and usable after a conversion to SAP S/4HANA.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p><strong>A) After conversion to S/4HANA and S4SCSD 1.0:</strong></p>
<ol>
<li>There is no such function in S4SCSD 1.0. If you want to continue using a functionality in SAP S/4HANA that is provided at present by Collective Orders in OGSD for the Business Suite, you need to switch to other SAP- or third-party-software.</li>
<li>You need to consider this as a project. All possible adaptations need to be executed manually.</li>
<li>In case of switching to a new software you need to organize knowledge transfer to all users working with Collective Orders as their pattern of work will change when working with new software. Users will have to use new applications for creating, changing, displaying and processing new documents.</li>
</ol>
<p><strong>B) After conversion to S/4HANA and S4SCSD 2.0:</strong></p>
<ol>
<li>No action is required, as this application does exist in S4SCSD 2.0.</li>
</ol>
