<h3>SI28: Logistics_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2368785">2368785 - S4TWL - Goods Receipt Capacity Check</a></strong></p>
<p><strong>Description:</strong></p>
<p>Goods receipt capacity was used for checking the schedule and the delivery dates of a vendor, allowing the distribution of the load of the goods receipt evenly, avoiding bottlenecks during inbound delivery. In SAP S/4HANA, goods receipt capacity check is not available anymore.</p>
<p><strong>Business Process Impact:</strong></p>
<p>This Simplification Item is relevant if goods receipt capacity check is used. This is the case if transactions WGRC1, WGRC2, WGRC3, WGRC4, WGRC5, WGRC6 are used. This also can be checked via transaction SE16N. Enter tables WGRC_ACT, WGRC_ACT_PO and check whether there are any entries.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>There is no equivalent functionality to goods receipt capacity check available in SAP S/4HANA. If you reuse ABAP objects of packet WGRC in your custom code, please see the attached note. Custom Code Adaption is necessary during the conversion project if ABAP objects of packet WGRC are reused in custom code.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2383533">2383533 - S4TWL - Retail Deprecated Applications Relevance for Custom Code</a></p>