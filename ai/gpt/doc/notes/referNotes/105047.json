{"Request": {"Number": "105047", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 284, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014567682017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000105047?language=E&token=B2EB1EEC753D47079F85C72FA99B64E6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000105047", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000105047/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "105047"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 241}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.07.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "105047 - Support for Oracle functions in the SAP environment"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to use an Oracle feature that is not used in the standard SAP system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle Data Guard</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note provides answers to questions concerning the possible uses of certain Oracle products and database options that are not used in the standard SAP system.</p>\r\n<p>This SAP Note relates to the SAP products that are based on the SAP Web Application Server and SAP NetWeaver (including all SAP R/3 versions). Other rules might apply to other products (for example, those included in the SAP product portfolio due to acquisitions by SAP (such as SAP Business Objects products)). If you have questions about these SAP products, you must clarify these on a case-by-case basis by creating a customer message.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The section below provides information about Oracle features and tools that are not used at all in the SAP standard system or whose use is restricted there. If you want to use a feature that does not appear in this list, log a customer message with SAP and refer to this SAP Note.</p>\r\n<p><strong>Support for problems</strong></p>\r\n<p>If a feature is tolerated by SAP, but is not officially supported by SAP Support, you can obtain support if problems occur as follows:</p>\r\n<ul>\r\n<li>If you a have a support contract directly with Oracle, then contact Oracle about the problem. If SAP does not provide support for the Oracle functions listed below, Oracle will support you directly if you have obtained your Oracle database license directly from Oracle, that is, if it is not an embedded license or an application-specific full-use (ASFU) license.</li>\r\n<li>If you bought the Oracle database from SAP, open a customer message to SAP. This is forwarded internally to Oracle Development Support if necessary, who will support you in solving the problem.</li>\r\n</ul>\r\n<p><strong>Licensing of Oracle features</strong></p>\r\n<p>In addition to ensuring that SAP supports or tolerates the use of features, you must also make sure that the feature or procedure is covered by the Oracle license. For more information, see SAP Notes <a target=\"_blank\" href=\"/notes/740897\">740897</a> and <a target=\"_blank\" href=\"/notes/581312\">581312</a>.</p>\r\n<p><strong>Usage of Oracle features</strong></p>\r\n<p>SAP Note <a target=\"_blank\" href=\"/notes/598678\">598678</a> describes new Oracle 9i features and their support in the SAP environment.<br />Support for Oracle 10g features is described in SAP Note <a target=\"_blank\" href=\"/notes/828268\">828268</a>.<br />For Oracle 11g features, see SAP Note <a target=\"_blank\" href=\"/notes/1431800\">1431800</a>.<br /><br />As of Oracle 10g, you can use the following database query to determine which database features have already been used:<br /><br />SELECT<br /> NAME, DETECTED_USAGES, CURRENTLY_USED, FIRST_USAGE_DATE<br />FROM<br /> DBA_FEATURE_USAGE_STATISTICS<br />WHERE<br /> VERSION = (SELECT VERSION FROM V$INSTANCE) AND (DETECTED_USAGES &gt; 0 OR CURRENTLY_USED != 'FALSE');</p>\r\n<p>If you specify features that, according to the information below, are only restricted or are no longer supported at all, you should determine in which context the feature is used and to what extent you actually require this usage.</p><p><strong>Installed Oracle database options</strong><br /><br />SQL*Plus displays installed and activated database options when a call takes place. The display of certain database options when a call of SQL*Plus takes place does not yet have an immediate effect on their licensing. As long as you do not actually use an option, it does not have to be licensed. As standard, SAP installs many options even if you never use these in concrete situations.</p>\r\n<p>Example (11g):</p>\r\n<p>$ sqlplus<br />SQL*Plus: Release ********.0 Production on Fri Jul 18 12:01:17 2014<br />Copyright (c) 1982, 2011, Oracle. All rights reserved.<br />Connected to:<br />Oracle Database 11g Enterprise Edition Release ********.0 - 64bit Production<br />With the Partitioning, OLAP, Data Mining and Real Application Testing options<br />SQL&gt;</p><p><strong>Overview of Oracle features and their use with SAP</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Feature/option/tool/product/procedure</span></td>\r\n<td><span style=\"text-decoration: underline;\">Use and support</span></td>\r\n</tr>\r\n<tr>\r\n<td>Advanced Replication</td>\r\n<td>Can be used.</td>\r\n</tr>\r\n<tr>\r\n<td>Auditing</td>\r\n<td>\r\n<p>Use of normal (database) auditing allowed</p>\r\n<p>Oracle Audit Vault and Database Firewall<br />You are permitted to use this only with explicit approval from SAP and only with SAP Kernel 7.10 or higher.</p>\r\n<p>You are not permitted to use Database Firewall. Contact: <a target=\"_blank\" href=\"mailto:<EMAIL>\"><EMAIL></a></p>\r\n<p>Unified auditing can be used.<span style=\"color: windowtext;\">&#x00A0;Further important details about unified auditing are described in SAP Note 2379567.</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Automatic Shared Memory Management (ASMM)</td>\r\n<td>You can use ASMM, but SAP does not recommend it.<br />Note the restrictions stated in SAP Note <a target=\"_blank\" href=\"/notes/789011\">789011</a>. <br />(Define sufficiently high lower limits for DB_CACHE_SIZE and SHARED_POOL_SIZE.)</td>\r\n</tr>\r\n<tr>\r\n<td>Automatic Storage Management (ASM)</td>\r\n<td>\r\n<p>Usage with Oracle 10g is not allowed.<br />Usage of ASM and ACFS as of Oracle 11.2.0.2 is supported (SAP Note <a target=\"_blank\" href=\"/notes/1550133\">1550133</a>).</p>\r\n<p>Using Oracle Flex ASM is not permitted (SAP Note <a target=\"_blank\" href=\"/notes/1550133\">1550133</a>).</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>BIGFILE tablespaces</td>\r\n<td>Use permitted and supported (SAP Notes <a target=\"_blank\" href=\"/notes/1644762\">1644762</a> and <a target=\"_blank\" href=\"/notes/1647271\">1647271</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Block Change Tracking (10g or higher)</td>\r\n<td>Use permitted (SAP Note <a target=\"_blank\" href=\"/notes/964619\">964619</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Change Data Capture (CDC)</td>\r\n<td>Use in the context of SAP Business Objects Data Services is supported.<br />Otherwise, it cannot be used</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Configuration Manager (OCM)</td>\r\n<td>Use not supported (SAP Note <a target=\"_blank\" href=\"/notes/1227404\">1227404</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Connection Manager</td>\r\n<td>Cannot be used.</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Database Administration Assistant for Windows</td>\r\n<td>Use not recommended.&#x00A0;No SAP support. Depricated as of Oracle Version 12.2&#x00A0;</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Database Appliance (ODA)</td>\r\n<td>Use permitted (SAP Note 1760737)</td>\r\n</tr>\r\n<tr>\r\n<td>In-Memory database</td>\r\n<td>Use permitted&#x00A0;(SAP Note 2178980)</td>\r\n</tr>\r\n<tr>\r\n<td>Database Configuration Assistant (DBCA)</td>\r\n<td>The creation of an SAP Oracle database using DBCA is not permitted.</td>\r\n</tr>\r\n<tr>\r\n<td>Database File System (DBFS)</td>\r\n<td>Use permitted, but not for SAP binaries, SAP files, SAP transports, and SAP file systems. No SAP support.</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Database Migration Assistant for Unicode (DMU)</td>\r\n<td>Use not allowed</td>\r\n</tr>\r\n<tr>\r\n<td>Database Vault</td>\r\n<td>Use permitted (SAP Note <a target=\"_blank\" href=\"/notes/1355140\">1355140</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>-</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Data Guard</td>\r\n<td>\r\n<p>You can use \"Physical Standby\".<br />You cannot use \"Logical Standby\".<br />You are allowed to use Fast Start Failover (FSFO) but SAP Support is not provided.<br />You can use Data Guard Broker.</p>\r\n<p>You can use Maximum Performance Mode, Maximum Availability Mode and Maximum Protection Mode.<br />In the case of Maximum Availability and Maximum Protection, you must pay particular attention to a fast network connection in order to avoid performance problems.<br />Maximum Protection causes the primary database to terminate if problems occur in the standby database.</p>\r\n<p>If you are interested in Active Data Guard, send an e-mail to <a target=\"_blank\" href=\"mailto:christian.graf@sap\"><EMAIL></a>.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Data Guard Fast Sync (SYNC NOAFFIRM)</td>\r\n<td>Use permitted.</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Active Data Guard Far Sync</td>\r\n<td>Use permitted.&#x00A0;To use it, the Oracle Active DataGuard&#x00A0;Option must be licensed.</td>\r\n</tr>\r\n<tr>\r\n<td>Data masking</td>\r\n<td>If you are interested in this, e-mail <a target=\"_blank\" href=\"mailto:<EMAIL>\"><EMAIL></a>.</td>\r\n</tr>\r\n<tr>\r\n<td>Data Mining</td>\r\n<td>Use permitted, but no SAP support is provided.</td>\r\n</tr>\r\n<tr>\r\n<td>Data Pump (expdp, impdp)</td>\r\n<td>Use supported as of BRSPACE 7.00 (17) (SAP Note <a target=\"_blank\" href=\"/notes/976435\">976435</a>)<br />You can use EXPDP/IMPDP directly, but there is no SAP support.</td>\r\n</tr>\r\n<tr>\r\n<td>Data redaction</td>\r\n<td>Use not allowed</td>\r\n</tr>\r\n<tr>\r\n<td>DB_BLOCK_SIZE different from 8 KB</td>\r\n<td>Block sizes smaller than 8 KB are not permitted.<br />An Oracle block size greater than 8 KB is allowed; the disadvantages of using larger block sizes include an increased demand for buffer pool memory and an increased volume of I/O data.<br />Multiple block sizes (equal to or greater than 8 KB) are allowed.</td>\r\n</tr>\r\n<tr>\r\n<td>DBMS_OBFUSCATION_TOOLKIT</td>\r\n<td>Use not permitted</td>\r\n</tr>\r\n<tr>\r\n<td>DBMS_SPACE_ADMIN.TABLESPACE_MIGRATE_TO_LOCAL</td>\r\n<td>Use not permitted</td>\r\n</tr>\r\n<tr>\r\n<td>DBMS_STATS</td>\r\n<td>You cannot use DBMS_STATS to create statistics directly on a regular basis.<br />You can use DBMS_STATS to create statistics via BRCONNECT (SAP Note 408532).</td>\r\n</tr>\r\n<tr>\r\n<td>DBNEWID (NID)</td>\r\n<td>Use permitted, but no SAP support is provided.</td>\r\n</tr>\r\n<tr>\r\n<td>Diagnostics Pack (OEM)</td>\r\n<td>Activation mandatory in SAP environment so that relevant ST04 functions can be used (SAP Note <a target=\"_blank\" href=\"/notes/1028068\">1028068</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Direct NFS (DNFS)</td>\r\n<td>Use permitted. (SAP Note 1416773)</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Disk Manager (ODM)</td>\r\n<td>Use permitted in conjunction with the VERITAS implementation of ODM (for example, in conjunction with \"VERITAS Storage Foundation for Oracle\", \"VERITAS Storage Foundation for Oracle RAC\" (previously DBE/AC), or \"HP Serviceguard Storage Management Suite for Oracle\").<br />Otherwise, it cannot be used.</td>\r\n</tr>\r\n<tr>\r\n<td>Distributed Transactions</td>\r\n<td>Use permitted, but no SAP support is provided.</td>\r\n</tr>\r\n<tr>\r\n<td>Domain Indexes</td>\r\n<td>You can use this feature in connection with BugsEye / Requisite.<br />There is no further SAP support (see SAP Note <a target=\"_blank\" href=\"/notes/740897\">740897</a>).</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Enterprise Manager (OEM)</td>\r\n<td>\r\n<p>Applies to Oracle Enterprise Manager, OEM Grid Control, OEM Database Control, and OEM Cloud Control</p>\r\n<p>Use permitted, but no SAP support is provided.<br />You can also use optional OEM packages, but no SAP support is provided.<br />You can also use the Management Server, but no SAP support is provided.<br />You can use Enterprise Manager Grid Control for monitoring purposes, but Oracle provides support (not SAP).<br />You cannot use Enterprise Manager Grid Control for other purposes (SAP Note <a target=\"_blank\" href=\"/notes/828268\">828268</a>).<br />The use of OEM to create CBO statistics is not supported.<br />For more information, see SAP Note <a target=\"_blank\" href=\"/notes/386413\">386413</a>.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Enterprise Manager Fleet Maintenance</td>\r\n<td>\r\n<p>Use not allowed.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Encryption</td>\r\n<td>Network Encryption: Use supported (SAP Note <a target=\"_blank\" href=\"/notes/973450\">973450</a>)<br />Transparent Data Encryption (TDE): Use supported (SAP Note <a target=\"_blank\" href=\"/notes/974876\">974876</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Exadata</td>\r\n<td>Use permitted (SAP Note <a target=\"_blank\" href=\"/notes/1590515\">1590515</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Hybrid Columnar Compression <br />(EHCC, HCC, compress for query, compress for archive)</td>\r\n<td>Use is only supported as part of ADO policies (see SAP Note 2254866), not for individual tables or partitions. Only HCC with row level locking.<br />As an alternative, you can use the OLTP tables compression. This is fully supported in the SAP environment (SAP Note <a target=\"_blank\" href=\"/notes/1436352\">1436352</a>).</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Exalogic</td>\r\n<td>Can be used (SAP Note 1617188).</td>\r\n</tr>\r\n<tr>\r\n<td>Export/import with EXP/IMP</td>\r\n<td>Automatically supported if you use BRSPACE (SAP Note 646681).<br />You can use them but no SAP support is provided if problems and follow-on errors occur (SAP Note 541538).<br />As of Oracle 11g, you may only use this in exceptional cases (refer to SAP Note 1712612).</td>\r\n</tr>\r\n<tr>\r\n<td>External data in the SAP database</td>\r\n<td>\r\n<ul>\r\n<li>Must be covered by an acquired database license (SAP Note 581312).</li>\r\n<li>Permitted for administration tools and monitoring tools.</li>\r\n<li>In addition, we do not recommend to use an SAP database with non-SAP software, since this constellation has considerable disadvantages:</li>\r\n<ul>\r\n<li>A database upgrade required for the system can only be performed if all software components that use the database can handle the new release.</li>\r\n<li>Required changes to database parameters for a system result in a downtime of several systems.</li>\r\n<li>Required import of database patches for a system result in a downtime of several systems.</li>\r\n<li>Special SAP database parameter settings may be unsuitable for external software access.</li>\r\n<li>Conflicts with CPU resources, memory resources, I/O resources and Oracle internal resources.</li>\r\n<li>Conflicts with BR*TOOLS</li>\r\n<li>Dependencies and unnecessary downtimes for restore scenarios and recovery scenarios</li>\r\n<li>The code page must be the same for all systems.</li>\r\n<li>Critical Oracle bugs, such as the problem described in SAP Note 1107700, are possible</li>\r\n</ul>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Flashback</td>\r\n<td>\r\n<ul>\r\n<li>You can use the flashback table to restore data in urgent cases if the consistency of the application can be ensured (see SAP Note 937492, if in doubt, contact SAP).</li>\r\n<li>The use of the flashback database (Oracle &gt;= 10g) is permitted (SAP Note 966117)</li>\r\n<li>The use of the flashback archive or total recall (Oracle 11g or higher) is permitted but there is no SAP support.</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Smart Flash cache (Oracle 11g or higher)</td>\r\n<td>Using the Exadata Smart Flash cache is permitted.<br />You can use Database Smart Flash Cache but SAP Support is not provided.</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Flex ASM (Oracle &gt; = 12.1)</td>\r\n<td>\r\n<p>Operating an ASM instance on a different physical server than the database server is not supported.&#x00A0;(SAP Note <a target=\"_blank\" href=\"/notes/1550133\">1550133</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Function-based indexes</td>\r\n<td>Can be used, but no SAP support is provided.<br />No support is provided by SAP-DDIC  or by monitoring transactions.</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Oracle Gateway</p>\r\n</td>\r\n<td>Can be used, but no SAP support is provided. Support must be requested directly from Oracle.</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Global Data Services (GDS)</p>\r\n</td>\r\n<td>Use not allowed.</td>\r\n</tr>\r\n<tr>\r\n<td>Golden Gate</td>\r\n<td>Use is permitted in connection with SAP Note 1508271.<br />Otherwise, it cannot be used.</td>\r\n</tr>\r\n<tr>\r\n<td>Hardware Security Module (HSM) keystore</td>\r\n<td>\r\n<p>HSM keystores are third-party physical devices for storing TDE encryption keys.</p>\r\n<p>Use is allowed, but there is no SAP support.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Health Monitor (&gt;= 11g)</td>\r\n<td>Use is allowed, but there is no SAP support.<br />Individual tests are useful to avoid negative side effects.</td>\r\n</tr>\r\n<tr>\r\n<td>ILM/ADO</td>\r\n<td>Use permitted (SAP Note 2254866)</td>\r\n</tr>\r\n<tr>\r\n<td>Index-monitoring</td>\r\n<td>Use permitted, but no SAP support</td>\r\n</tr>\r\n<tr>\r\n<td>Index Organized Tables (IOTs)</td>\r\n<td>Use permitted<br />For more information and details on restrictions, see SAP Note 641435.</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle software installation media</td>\r\n<td>Only SAP Oracle installation media is permitted<br />Customer-defined builds and Oracle standard installation media is not permitted</td>\r\n</tr>\r\n<tr>\r\n<td>Invisible indexes</td>\r\n<td>The usage for a temporary analysis of performance problems and a punctual usage are permitted.</td>\r\n</tr>\r\n<tr>\r\n<td>Java Database Connectivity (JDBC)</td>\r\n<td>Not permitted for ABAP work process connections<br />Default for J2EE connections</td>\r\n</tr>\r\n<tr>\r\n<td>Java Virtual Machine (JVM)</td>\r\n<td>You may use this only after receiving explicit permission from SAP.<br />Contact: <a target=\"_blank\" href=\"mailto:<EMAIL>\"><EMAIL></a></td>\r\n</tr>\r\n<tr>\r\n<td>Cloning Oracle software</td>\r\n<td>\r\n<p>Copying or cloning Oracle software in accordance with the metalink document 300062.1 is permitted in the SAP environment.<br />Otherwise, copying or cloning the Oracle software is not permitted.</p>\r\n<p>More information: SAP Notes 1983457, 1614823, and 1696869</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Compression</td>\r\n<td>\r\n<p>See SAP Notes 1289494 and 2138262.</p>\r\n<ul>\r\n<li>BASIC table compression: Permitted only as part of SAP Note 701235 and SAP Note 1231895.</li>\r\n<li>OLTP table compression and Securefile compression (Oracle 11g or higher, Advanced Compression Option) are supported (SAP Note 1436352).</li>\r\n<li>The use of index compression in accordance with SAP Note 1109743 is supported.</li>\r\n<li>12c/18c/19c: Advanced index compression 'low' is supported and recommended. The option 'high' is supported but not recommended, since it results in poor throughput (up to 20% worse query performance, up to 50% worse insert/update/delete performance, and slower index creation/rebuild by a factor of 2-3).</li>\r\n<li>12c/18c/19c: Client-server network compression is supported.</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>LDAP</td>\r\n<td>Use permitted, but no SAP support is provided.</td>\r\n</tr>\r\n<tr>\r\n<td>Materialized Views</td>\r\n<td>Use permitted<br />For more information, see SAP Note 741478.</td>\r\n</tr>\r\n<tr>\r\n<td>Monitoring Tools</td>\r\n<td>You can use external monitoring tools</td>\r\n</tr>\r\n<tr>\r\n<td>ALTER TABLE MOVE</td>\r\n<td>This is supported by BRSPACE as of Release 7.00 (28) or 7.10 (4) (see SAP Note 1080376).<br />You can use ALTER TABLE MOVE, but no SAP support is provided if problems and follow-on errors occur (SAP Note 541538).</td>\r\n</tr>\r\n<tr>\r\n<td>Multitenant</td>\r\n<td>\r\n<p>Use permitted (SAP Note 2336881)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Objects Option</td>\r\n<td>Cannot be used (SAP Note 740897).</td>\r\n</tr>\r\n<tr>\r\n<td>OLAP</td>\r\n<td>Use not permitted (SAP Note 740897)</td>\r\n</tr>\r\n<tr>\r\n<td>OCFS (Oracle Cluster File System)</td>\r\n<td>Can be used in the RAC environment as described in SAP Note 527843.</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Key Vault (OKV)</td>\r\n<td>\r\n<p>OKV is a software appliance for centralizing the management of encryption keys.</p>\r\n<p>Use is allowed, but there is no SAP support.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Online backups without backup mode (BEGIN BACKUP)</td>\r\n<td>Only permitted in connection with RMAN or with tools that satisfy the requirements specified in metalink document 604683.1</td>\r\n</tr>\r\n<tr>\r\n<td>Optimal Flexible Architecture (OFA)</td>\r\n<td>\r\n<p>No SAP support<br />Problems are likely during installation, migration, and when using SAP tools.</p></td>\r\n</tr>\r\n<tr>\r\n<td>Shared Oracle Homes/Oracle Home Sharing</td>\r\n<td>\r\n<p>\"Oracle Home Sharing\" denotes a situation where multiple database instances (&gt;=2) share use of an Oracle Home. This means that there is no need to install a separate Oracle Home for each database instance. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/1778431\">1778431</a>.</p>\r\n<ul>\r\n<li>SAP installations with an Oracle RAC database (custom RAC installation) on UNIX/Linux</li>\r\n<ul>\r\n<li>The use of an Oracle Home in a shared file system as a shared Oracle Home for all RAC instances of a <span style=\"text-decoration: underline;\">single RAC database</span> is mandatory.</li>\r\n<li>The use of an Oracle Home in a shared file system as a shared Oracle Home for RAC instances of <span style=\"text-decoration: underline;\">different RAC databases</span> is supported.</li>\r\n</ul>\r\n<li>SAP installations with an Oracle Single Instance database on UNIX/Linux: Use permitted; for details, see SAP Note <a target=\"_blank\" href=\"/notes/1778431\">1778431</a></li>\r\n<li>SAP installations with an Oracle Single Instance database on Windows: Use permitted</li>\r\n</ul>\r\n<p>Caution: There are dependencies for patching; extensive tests are therefore useful.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Linux (OL)</td>\r\n<td>Use permitted (SAP Note <a target=\"_blank\" href=\"/notes/1565179\">1565179</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Outlines/Stored Outlines</td>\r\n<td>\r\n<p>Use permitted<br />Not generally recommended because performance problems can usually be solved by other means.<br />Notification of end of support by Oracle in Oracle 11. Deprecated with Oracle 12.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Parallel Execution</td>\r\n<td>Use permitted<br />For more information, see SAP Note <a target=\"_blank\" href=\"/notes/651060\">651060</a>.</td>\r\n</tr>\r\n<tr>\r\n<td>Partitioning</td>\r\n<td>\r\n<ul>\r\n<li>The use of partitioning is supported within the BR*TOOLS functions (Release 6.20 or higher) and the SAP Data Dictionary function (Release 4.6C or higher).</li>\r\n<li>Sub-partitioning permitted (SAP DDIC support with SAP NetWeaver 7.00 or higher, Oracle 11g and higher, and support package levels from SAP Note 742243)</li>\r\n<li>For more information, see SAP Notes <a target=\"_blank\" href=\"/notes/722188\">722188</a> and <a target=\"_blank\" href=\"/notes/742243\">742243</a>.</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>RDBMS/Grid Oracle Home JDK und Perl Components</td>\r\n<td>\r\n<p>The JDK component and Perl component of an RDBMS home or a grid home must be used only to run the Oracle-provided tools of these homes. It is not supported to use non-Oracle tools or applications with the JDK component and Perl component of an RDBMS home or a grid home.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Read-only mode</td>\r\n<td>You can use a read-only database for administrative purposes only (for example, consistency check on standby side).<br />You cannot start an SAP instance on a read-only database.<br />You cannot use read-only tablespaces<br />For more information, see SAP Note <a target=\"_blank\" href=\"/notes/817253\">817253</a>.</td>\r\n</tr>\r\n<tr>\r\n<td>Real Application Clusters (RAC)</td>\r\n<td>You can use this feature in accordance with SAP Note 527843.<br />RAC One Node (Oracle 11g or higher) is not allowed.</td>\r\n</tr>\r\n<tr>\r\n<td>Real Application Testing (RAT) / Capture / Replay / SQL Performance Analyzer</td>\r\n<td>Use permitted (SAP Note <a target=\"_blank\" href=\"/notes/1426980\">1426980</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Recovery Manager (RMAN)</td>\r\n<td>\r\n<ul>\r\n<li>Use of RMAN for backup, restore, and recovery allowed</li>\r\n<li>As of Oracle Version 12c: Cross platform backup/restore permitted</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Recycle bin</td>\r\n<td>Only pilot customers are allowed to use this (contact: <a target=\"_blank\" href=\"mailto:<EMAIL>\"><EMAIL></a>).<br />It is a good idea to PURGE the recycle bin on a regular basis to prevent increasing space requirements.</td>\r\n</tr>\r\n<tr>\r\n<td>Remote Diagnostic Agent (RDA)</td>\r\n<td>Use permitted (SAP Note <a target=\"_blank\" href=\"/notes/1005931\">1005931</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Resource Manager</td>\r\n<td>Use permitted (SAP Note <a target=\"_blank\" href=\"/notes/1589924\">1589924</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Restart:</td>\r\n<td>Use permitted</td>\r\n</tr>\r\n<tr>\r\n<td>RESUMABLE</td>\r\n<td>Use permitted (SAP Note <a target=\"_blank\" href=\"/notes/1327890\">1327890</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Result Cache</td>\r\n<td>Server Result Cache: Can be used, but no SAP support is provided.<br />Client Result Cache: Cannot be used.</td>\r\n</tr>\r\n<tr>\r\n<td>Reverse key indexes</td>\r\n<td>Use permitted<br />For more information, see SAP Note <a target=\"_blank\" href=\"/notes/915242\">915242</a>.</td>\r\n</tr>\r\n<tr>\r\n<td>Sequences</td>\r\n<td>In certain cases, this is used in the standard SAP system (for example, in the table DDLOG).<br />Otherwise, use is not permitted.</td>\r\n</tr>\r\n<tr>\r\n<td>Software Configuration Manager (SCM)</td>\r\n<td>Use not permitted (SAP Note <a target=\"_blank\" href=\"/notes/1227404\">1227404</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>Sparse Clones on Exadata</td>\r\n<td>Use permitted. Sparse clones provide poorer performance. An SAP message due to performance problems is processed only if the performance problem can be reproduced to non-sparse disks.</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Spatial Option</td>\r\n<td>Use not permitted</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SQLcl (new command line interface for the Oracle database)</p>\r\n</td>\r\n<td>Use permitted, but no SAP support</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle SQL Developer</td>\r\n<td>Use permitted, but no SAP support</td>\r\n</tr>\r\n<tr>\r\n<td>SQL Loader (SQLLDR)</td>\r\n<td>Use not permitted</td>\r\n</tr>\r\n<tr>\r\n<td>SQL Plan Management (SPM, Oracle 11g or higher)</td>\r\n<td>Use permitted</td>\r\n</tr>\r\n<tr>\r\n<td>SQL Profiles</td>\r\n<td>Use permitted<br />Not generally recommended because performance problems can usually be solved by other means.</td>\r\n</tr>\r\n<tr>\r\n<td>Standard Edition</td>\r\n<td>SAP products always require Oracle Enterprise Edition (EE); use with Oracle Standard Edition (SE) is not permitted.</td>\r\n</tr>\r\n<tr>\r\n<td>Statspack</td>\r\n<td>Use permitted<br />For more information, see SAP Note <a target=\"_blank\" href=\"/notes/717484\">717484</a>.</td>\r\n</tr>\r\n<tr>\r\n<td>Streams</td>\r\n<td>Use not permitted</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle SuperCluster</td>\r\n<td>Use permitted (SAP Note 1693680)</td>\r\n</tr>\r\n<tr>\r\n<td>Supplemental Logging</td>\r\n<td>\r\n<p>Use permitted<br />Increased redo log volume must be considered.</p>\r\n<p>No support in ABAP Dictionary</p>\r\n<p>&#x00A0;For more information, see SAP Note 2380799.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>TCPS/TCP via SSL</p>\r\n</td>\r\n<td>\r\n<p>Use allowed for the SAP NetWeaver ABAP stack, but no SAP support.</p>\r\n<p>Use allowed for the SAP NetWeaver Java stack if both the Oracle client and the database server have at least Version 19c. No SAP support.</p>\r\n<p>For Oracle versions lower than 19c, the use with the SAP NetWeaver Java stack is technically impossible.</p>\r\n<p>For more information, see SAP Note 973450.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Temporary Tablespace Group</td>\r\n<td>Use permitted, but no SAP support is provided.<br />Caution: The reliability of the temp-tablespace checks of BRCONNECT/RZ20/Solution Manager is no longer guaranteed.</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Text Option (previously: Intermedia)</td>\r\n<td>You can use this feature in connection with BugsEye/Requisite.<br />There is no further SAP support (see SAP Note 740897).</td>\r\n</tr>\r\n<tr>\r\n<td>TimesTen</td>\r\n<td>Use not permitted</td>\r\n</tr>\r\n<tr>\r\n<td>Transparent Gateway (DB2)</td>\r\n<td>Use permitted, but no SAP support is provided.</td>\r\n</tr>\r\n<tr>\r\n<td>Transportable Tablespaces</td>\r\n<td>Implicit use supported as part of the BRSPACE function \"-f dbcreate\" (SAP Note <a target=\"_blank\" href=\"/notes/748434\">748434</a>) and the \"Tablespace Point in Time Recovery\" function of BRRECOVER.<br />Explicit use as part of system copying is tolerated</td>\r\n</tr>\r\n<tr>\r\n<td>Transportable database</td>\r\n<td>Can be used (SAP Note 1367451).</td>\r\n</tr>\r\n<tr>\r\n<td>Trigger</td>\r\n<td>\r\n<p>Use permitted as part of the SAP standard system (for example, BW trigger /BI0/05* in accordance with SAP Note <a target=\"_blank\" href=\"/notes/449891\">449891</a>, incremental conversion ICNV)<br />Use of Logon Trigger permitted in accordance with SAP Note <a target=\"_blank\" href=\"/notes/712777\">712777</a><br />Implicit use as part of Oracle features permitted (for example, online reorganisation, materialized views, GridControl/Enterprise Manager)<br />Using trigger-based real-time replication is permitted in connection with an SAP HANA implementation only.</p>\r\n<p>Use in connection with materialized views in an SAP BW system is permitted as long as no flat cubes are available as an alternative. There is no SAP Integration and SAP does not offer support for this.</p>\r\n<p>Otherwise, use is not permitted.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Tuning Pack (OEM)</td>\r\n<td>Use permitted</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle VCA</td>\r\n<td>Use permitted (SAP Note 2052912)</td>\r\n</tr>\r\n<tr>\r\n<td>Virtual environments (Oracle VM, XEN, VMWare)</td>\r\n<td>With regard to VMWare, see SAP Note <a target=\"_blank\" href=\"/notes/1173954\">1173954</a>.<br />With regard to XEN, see SAP Note <a target=\"_blank\" href=\"/notes/1426182\">1426182</a>.<br />The use of Oracle VM is supported (SAP Note <a target=\"_blank\" href=\"/notes/1808268\">1808268</a>).</td>\r\n</tr>\r\n<tr>\r\n<td>Virtual Column Partitioning</td>\r\n<td>Use not allowed</td>\r\n</tr>\r\n<tr>\r\n<td>Virtual Private Database (VPD)</td>\r\n<td>Use not allowed.</td>\r\n</tr>\r\n<tr>\r\n<td>Oracle Zero Data Loss Recovery Appliance (ZDLRA)</td>\r\n<td>Use is allowed, but there is no SAP support. There is no integration with SAP (BR* tools, calendars, and so on).</td>\r\n</tr>\r\n</tbody>\r\n</table></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019926)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019926)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000105047/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000105047/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000105047/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000105047/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000105047/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000105047/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000105047/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000105047/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000105047/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "997990", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "Oracle Linux / Oracle Unbreakable Enterprise Kernel", "RefUrl": "/notes/997990"}, {"RefNumber": "976435", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for Oracle Data Pump in BRSPACE", "RefUrl": "/notes/976435"}, {"RefNumber": "974876", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Transparent Data Encryption (TDE)", "RefUrl": "/notes/974876"}, {"RefNumber": "973450", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database network encryption and data integrity", "RefUrl": "/notes/973450"}, {"RefNumber": "966117", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Flashback Database technology", "RefUrl": "/notes/966117"}, {"RefNumber": "964619", "RefComponent": "BC-DB-ORA", "RefTitle": "RMAN: Incremental backups with block change tracking", "RefUrl": "/notes/964619"}, {"RefNumber": "937492", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Flashback", "RefUrl": "/notes/937492"}, {"RefNumber": "915242", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Reverse key indexes", "RefUrl": "/notes/915242"}, {"RefNumber": "912620", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle indexes", "RefUrl": "/notes/912620"}, {"RefNumber": "817253", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Open a Database Read only", "RefUrl": "/notes/817253"}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011"}, {"RefNumber": "742243", "RefComponent": "BC-DB-ORA", "RefTitle": "General table partitioning as of SAP_BASIS 46C", "RefUrl": "/notes/742243"}, {"RefNumber": "741478", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Materialized views", "RefUrl": "/notes/741478"}, {"RefNumber": "740897", "RefComponent": "BC-DB-ORA", "RefTitle": "Info about the scope of the Oracle license; required Oracle options", "RefUrl": "/notes/740897"}, {"RefNumber": "722188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle partitioning", "RefUrl": "/notes/722188"}, {"RefNumber": "717484", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQs: Oracle statspack", "RefUrl": "/notes/717484"}, {"RefNumber": "712777", "RefComponent": "BC-DB-ORA", "RefTitle": "MCOD operation of R3 and BW in a database", "RefUrl": "/notes/712777"}, {"RefNumber": "701235", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Usage of Oracle compression and BW", "RefUrl": "/notes/701235"}, {"RefNumber": "651060", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Parallel Execution", "RefUrl": "/notes/651060"}, {"RefNumber": "641435", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle index-organized tables (IOTs)", "RefUrl": "/notes/641435"}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668"}, {"RefNumber": "581312", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle database: Licensing restrictions", "RefUrl": "/notes/581312"}, {"RefNumber": "527843", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle RAC support in the SAP environment", "RefUrl": "/notes/527843"}, {"RefNumber": "408532", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using the DBMS_STATS package for collecting statistics", "RefUrl": "/notes/408532"}, {"RefNumber": "386413", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Oracle Enterprise Manager (OEM) in the SAP environment", "RefUrl": "/notes/386413"}, {"RefNumber": "355770", "RefComponent": "BC-DB-ORA", "RefTitle": "Use of Oracle Enterprise Manager in SAP environments", "RefUrl": "/notes/355770"}, {"RefNumber": "309439", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/309439"}, {"RefNumber": "2254866", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Database Automatic Data Optimization with SAP NetWeaver", "RefUrl": "/notes/2254866"}, {"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094"}, {"RefNumber": "1818320", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g Advanced Compression for LONG data restriction", "RefUrl": "/notes/1818320"}, {"RefNumber": "1808268", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "SAP on Oracle VM - Oracle VM Support", "RefUrl": "/notes/1808268"}, {"RefNumber": "1712612", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g: Usage of exp with 11.2 / 11g", "RefUrl": "/notes/1712612"}, {"RefNumber": "1647271", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for bigfile tablespaces in BR*Tools", "RefUrl": "/notes/1647271"}, {"RefNumber": "1644762", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle bigfile tablespaces", "RefUrl": "/notes/1644762"}, {"RefNumber": "1589924", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "SAP Workload Management via Oracle Resource Manager", "RefUrl": "/notes/1589924"}, {"RefNumber": "1565179", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "SAP software and Oracle Linux", "RefUrl": "/notes/1565179"}, {"RefNumber": "1551504", "RefComponent": "BC-DB-ORA", "RefTitle": "Cleaning up Oracle audit files", "RefUrl": "/notes/1551504"}, {"RefNumber": "1436352", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 11g Advanced Compression for SAP Systems", "RefUrl": "/notes/1436352"}, {"RefNumber": "1426182", "RefComponent": "BC-DB-ORA", "RefTitle": "Support of Oracle Database for XEN and KVM", "RefUrl": "/notes/1426182"}, {"RefNumber": "1327890", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle resumable space allocation feature", "RefUrl": "/notes/1327890"}, {"RefNumber": "1289494", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle compression", "RefUrl": "/notes/1289494"}, {"RefNumber": "1227404", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Configuration Manager (OCM)", "RefUrl": "/notes/1227404"}, {"RefNumber": "1178190", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP on Oracle VM Server for SPARC domains", "RefUrl": "/notes/1178190"}, {"RefNumber": "1109743", "RefComponent": "BC-DB-ORA", "RefTitle": "Use of Index Key (Prefix) Compression for Oracle Databases", "RefUrl": "/notes/1109743"}, {"RefNumber": "1080376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements for reorganization and rebuild", "RefUrl": "/notes/1080376"}, {"RefNumber": "1032636", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle configuration manager", "RefUrl": "/notes/1032636"}, {"RefNumber": "1013049", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Data Pump", "RefUrl": "/notes/1013049"}, {"RefNumber": "1005931", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Remote Diagnostic Agent (RDA)", "RefUrl": "/notes/1005931"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2989058", "RefComponent": "BC-DB-ORA", "RefTitle": "How to check Triggers", "RefUrl": "/notes/2989058 "}, {"RefNumber": "2889465", "RefComponent": "SV-PERF-DB-ORA", "RefTitle": "Long runtime of TMW_RC_BPA_DATA_COLL in SAP Readiness Check 2.0", "RefUrl": "/notes/2889465 "}, {"RefNumber": "2380799", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Database Feature 'Supplemental Logging' with SAP NetWeaver", "RefUrl": "/notes/2380799 "}, {"RefNumber": "1848918", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP System Copy on Oracle Solaris with Oracle Database", "RefUrl": "/notes/1848918 "}, {"RefNumber": "527843", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle RAC support in the SAP environment", "RefUrl": "/notes/527843 "}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "1436352", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 11g Advanced Compression for SAP Systems", "RefUrl": "/notes/1436352 "}, {"RefNumber": "742243", "RefComponent": "BC-DB-ORA", "RefTitle": "General table partitioning as of SAP_BASIS 46C", "RefUrl": "/notes/742243 "}, {"RefNumber": "641435", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle index-organized tables (IOTs)", "RefUrl": "/notes/641435 "}, {"RefNumber": "1808268", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "SAP on Oracle VM - Oracle VM Support", "RefUrl": "/notes/1808268 "}, {"RefNumber": "1005931", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Remote Diagnostic Agent (RDA)", "RefUrl": "/notes/1005931 "}, {"RefNumber": "974876", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Transparent Data Encryption (TDE)", "RefUrl": "/notes/974876 "}, {"RefNumber": "1565179", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "SAP software and Oracle Linux", "RefUrl": "/notes/1565179 "}, {"RefNumber": "1289494", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle compression", "RefUrl": "/notes/1289494 "}, {"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094 "}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011 "}, {"RefNumber": "1647271", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for bigfile tablespaces in BR*Tools", "RefUrl": "/notes/1647271 "}, {"RefNumber": "1818320", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g Advanced Compression for LONG data restriction", "RefUrl": "/notes/1818320 "}, {"RefNumber": "581312", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle database: Licensing restrictions", "RefUrl": "/notes/581312 "}, {"RefNumber": "355770", "RefComponent": "BC-DB-ORA", "RefTitle": "Use of Oracle Enterprise Manager in SAP environments", "RefUrl": "/notes/355770 "}, {"RefNumber": "1178190", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP on Oracle VM Server for SPARC domains", "RefUrl": "/notes/1178190 "}, {"RefNumber": "740897", "RefComponent": "BC-DB-ORA", "RefTitle": "Info about the scope of the Oracle license; required Oracle options", "RefUrl": "/notes/740897 "}, {"RefNumber": "997990", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "Oracle Linux / Oracle Unbreakable Enterprise Kernel", "RefUrl": "/notes/997990 "}, {"RefNumber": "1712612", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g: Usage of exp with 11.2 / 11g", "RefUrl": "/notes/1712612 "}, {"RefNumber": "1109743", "RefComponent": "BC-DB-ORA", "RefTitle": "Use of Index Key (Prefix) Compression for Oracle Databases", "RefUrl": "/notes/1109743 "}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668 "}, {"RefNumber": "976435", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for Oracle Data Pump in BRSPACE", "RefUrl": "/notes/976435 "}, {"RefNumber": "1644762", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle bigfile tablespaces", "RefUrl": "/notes/1644762 "}, {"RefNumber": "964619", "RefComponent": "BC-DB-ORA", "RefTitle": "RMAN: Incremental backups with block change tracking", "RefUrl": "/notes/964619 "}, {"RefNumber": "722188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle partitioning", "RefUrl": "/notes/722188 "}, {"RefNumber": "966117", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Flashback Database technology", "RefUrl": "/notes/966117 "}, {"RefNumber": "1589924", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "SAP Workload Management via Oracle Resource Manager", "RefUrl": "/notes/1589924 "}, {"RefNumber": "1551504", "RefComponent": "BC-DB-ORA", "RefTitle": "Cleaning up Oracle audit files", "RefUrl": "/notes/1551504 "}, {"RefNumber": "741478", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Materialized views", "RefUrl": "/notes/741478 "}, {"RefNumber": "1426182", "RefComponent": "BC-DB-ORA", "RefTitle": "Support of Oracle Database for XEN and KVM", "RefUrl": "/notes/1426182 "}, {"RefNumber": "1080376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements for reorganization and rebuild", "RefUrl": "/notes/1080376 "}, {"RefNumber": "1327890", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle resumable space allocation feature", "RefUrl": "/notes/1327890 "}, {"RefNumber": "937492", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Flashback", "RefUrl": "/notes/937492 "}, {"RefNumber": "912620", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle indexes", "RefUrl": "/notes/912620 "}, {"RefNumber": "651060", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Parallel Execution", "RefUrl": "/notes/651060 "}, {"RefNumber": "973450", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database network encryption and data integrity", "RefUrl": "/notes/973450 "}, {"RefNumber": "1227404", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Configuration Manager (OCM)", "RefUrl": "/notes/1227404 "}, {"RefNumber": "408532", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using the DBMS_STATS package for collecting statistics", "RefUrl": "/notes/408532 "}, {"RefNumber": "1013049", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Data Pump", "RefUrl": "/notes/1013049 "}, {"RefNumber": "913862", "RefComponent": "SCM-BAS-TDL", "RefTitle": "TDL: Partitioning transaction data tables", "RefUrl": "/notes/913862 "}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678 "}, {"RefNumber": "386413", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Oracle Enterprise Manager (OEM) in the SAP environment", "RefUrl": "/notes/386413 "}, {"RefNumber": "1032636", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle configuration manager", "RefUrl": "/notes/1032636 "}, {"RefNumber": "701235", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Usage of Oracle compression and BW", "RefUrl": "/notes/701235 "}, {"RefNumber": "915242", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Reverse key indexes", "RefUrl": "/notes/915242 "}, {"RefNumber": "717484", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQs: Oracle statspack", "RefUrl": "/notes/717484 "}, {"RefNumber": "817253", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Open a Database Read only", "RefUrl": "/notes/817253 "}, {"RefNumber": "712777", "RefComponent": "BC-DB-ORA", "RefTitle": "MCOD operation of R3 and BW in a database", "RefUrl": "/notes/712777 "}, {"RefNumber": "572060", "RefComponent": "BC-DB-ORA", "RefTitle": "Options in the Oracle database during archiving", "RefUrl": "/notes/572060 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}