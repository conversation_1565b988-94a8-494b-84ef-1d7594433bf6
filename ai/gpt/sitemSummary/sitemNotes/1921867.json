{"Request": {"Number": "1921867", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 630, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017732472017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001921867?language=E&token=3BFB5DC3EB781A46DEFEEF4006CEB035"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001921867", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001921867/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1921867"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.02.2014"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-MGM-PRC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Product Compliance Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP EHS Management", "value": "EHS-MGM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Compliance Management", "value": "EHS-MGM-PRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM-PRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1921867 - EHSM 3.0 SP05 PRC: Release Information Product Compliance"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n\r\n<p>You want to install Support Package 05 for component extension 3.0 for SAP EHS Management(EHSM300 SP05). For the topics of product compliance and IMDS compliance, this note gives an overview of important configuration changes or post installation steps that have to be executed after installing the Support Package.</p>\r\n<p><strong></strong><br /><strong>Customizing Changes</strong></p>\r\n<p>The following Customizing changes are shipped with EHSM300, Support Package 05:</p>\r\n<ul>\r\n<li>IMDS Check</li>\r\n</ul>\r\n<ul>\r\n<li>BOMBOS Transfer</li>\r\n</ul>\r\n<p><br />To get further information about the Customizing changes, refer to the corresponding notes.</p>\r\n<p><strong>Post Installation Steps</strong></p>\r\n<ol>1. Activation of BC Sets</ol>\r\n<p>After the installation of EHSM300, Support Package 05 activate the following BC sets:</p>\r\n<ul>\r\n<li>EHPRC_DI_BOM_BOS</li>\r\n</ul>\r\n<ul>\r\n<li>EHPRC_DI_IMDS</li>\r\n</ul>\r\n<ul>\r\n<li>EHPRC_SPECIAL</li>\r\n</ul>\r\n<p><br />Before you activate any BC set, ensure the consistency of your Customizing data. Therefore start transaction SCPR20 and compare the existing Customizing tables with each BC Set.<br />This step prevents to overwrite existing data that you want to keep.</p>\r\n<ol>2. Adaption of Field Control Framework</ol>\r\n<p>In Customizing start activity \"Specify Field Control\" under SAP EHS Management-&gt;Foundation for EHS Management-&gt;General Configuration.<br />Choose EHPRC_COMPLIANCE_DATA under \"Business Objects\", now choose DECL_SUBSTANCE under \"BO Nodes\", and delete EXEMPTION under \"Profiles for BO Nodes\". This profile is no longer used and has to be deleted in EHSM300, Support Package 05.</p>\r\n<p><strong>Miscellaneous</strong></p>\r\n<ol>1. Refresh of Caches for Personal Object Worklists (POWL)</ol>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">Use transaction SA38 to run the following reports in the given order to clear the cache for personal object worklists (POWLs):</p>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">a) POWL_D04 (Delete Cached Selection Criteria for Admin Queries)</p>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">Caution: Run this report only with&#160;&#160;the following entries:</p>\r\n<ul>\r\n<ul>\r\n<li>\"Query ID\"= EH*</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>\"POWL Type ID\": EH*.</li>\r\n</ul>\r\n</ul>\r\n<p>b) POWL_D01 (Delete Queries from Database)</p>\r\n<p>Caution: Run this report only with the following entries:</p>\r\n<ul>\r\n<ul>\r\n<li>\"APPLID\"= EH*</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>\"USER\": list here all users.</li>\r\n</ul>\r\n</ul>\r\n<ol>2. Change of Product Compliance Easy Search Parameter</ol>\r\n<p>With EHSM300, Support Package 05 the search paramter \"Show only salable products\" is replaced by the search parameter \"Include non-salable assemblies\".<br />Be aware: by default the easy search does not include non-salable assemblies. To change this default, press the link \"Change Query\" on the top right in the EASY SEARCH tab. Select the \"Include non-salable assemblies\" checkbox.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p></p>\r\n<p>EHSM300SP05PRC<br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Configuration and/or program error</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The corrections and Customizing changes are shipped with EHSM300, Support Package 05.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D054905)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D054646)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001921867/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001921867/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001921867/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001921867/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001921867/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001921867/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001921867/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001921867/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001921867/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1916079", "RefComponent": "EHS-MGM-PRC", "RefTitle": "EHFNDS_TM_SEARCHFIELDS-max_rows not existing", "RefUrl": "/notes/1916079"}, {"RefNumber": "1912753", "RefComponent": "EHS-MGM-PRC", "RefTitle": "Texts for IMG and UI", "RefUrl": "/notes/1912753"}, {"RefNumber": "1910514", "RefComponent": "EHS-MGM-PRC", "RefTitle": "BOMBOS transfer of a recursive BOM does never finish", "RefUrl": "/notes/1910514"}, {"RefNumber": "1888381", "RefComponent": "EHS-MGM-PRC", "RefTitle": "New IMDS Check implementation", "RefUrl": "/notes/1888381"}, {"RefNumber": "1733614", "RefComponent": "XX-SER-REL", "RefTitle": "Component Extension 3.0 for SAP EHS Management: RIN", "RefUrl": "/notes/1733614"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1733614", "RefComponent": "XX-SER-REL", "RefTitle": "Component Extension 3.0 for SAP EHS Management: RIN", "RefUrl": "/notes/1733614 "}, {"RefNumber": "1912753", "RefComponent": "EHS-MGM-PRC", "RefTitle": "Texts for IMG and UI", "RefUrl": "/notes/1912753 "}, {"RefNumber": "1888381", "RefComponent": "EHS-MGM-PRC", "RefTitle": "New IMDS Check implementation", "RefUrl": "/notes/1888381 "}, {"RefNumber": "1916079", "RefComponent": "EHS-MGM-PRC", "RefTitle": "EHFNDS_TM_SEARCHFIELDS-max_rows not existing", "RefUrl": "/notes/1916079 "}, {"RefNumber": "1910514", "RefComponent": "EHS-MGM-PRC", "RefTitle": "BOMBOS transfer of a recursive BOM does never finish", "RefUrl": "/notes/1910514 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EHSM", "From": "300", "To": "300", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}