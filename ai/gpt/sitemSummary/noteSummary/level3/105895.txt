SAP Note 105895 addresses an error that occurs in transaction MEU2, which is part of program SAPMNB01, specifically on screen 0300. The error message "SG105: Please enter rate __ rate type M for & in the system settings" appears when users engage in business volume comparison and agreement for vendor conditions based on a percentage. This situation applies specifically when the rebate arrangement currency and the condition currency are different (e.g., DM and DM3), and the settlement is processed on the debit side at the plant level.

The error is triggered when there's a change in the scale basis during the process, which leads to an attempted read of the exchange rate table with an empty field for the currency pair DM3/blank, thereby not finding any relevant entry. This occurs due to the FORM routine XWMB01_UPDATE in SAPMNB01 calling function module MM_ARRANG_CURRENCY_CONVERSION with a parameter for currency (T001-WAERS) that has not been set.

Additionally, company code data is not read and table T001 is not filled, because when starting the business volume comparison and agreement process, the FORM routine COMPANY_CODE_READ is called. However, due to both T001-BURKS and LE_BUKRS being blank, the necessary read is not performed.

The prerequisite for this error to occur is that the settlement must be on the debit side at the plant level, and the root cause is identified as a program error.

The solution is to apply an advance correction to resolve the issue. This typically means that users would need to implement a correction instruction provided by SAP or update their system with the relevant support package that contains the fix for this error.