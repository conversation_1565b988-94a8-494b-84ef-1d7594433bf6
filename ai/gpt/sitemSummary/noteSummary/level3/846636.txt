SAP Note 846636 addresses an enhancement to the SAP ERP HCM Payroll for Canada that allows handling Multiple Period Start Days (MPSD) in a Concurrent Employment (CE) scenario. This enhancement permits running payroll for contracts across different payroll areas, which previously could only be combined within the same payroll area.

Key points from the note:

- The new feature allows organizations to process payroll for employees with contracts in different payroll areas.
- There are certain conditions and restrictions that must be adhered to when using this feature, such as:
  - The switch MPSDS in the CCURE group of view V_T77S0 must remain active once payroll for MPSD has been executed.
  - The grouping rule XXMP for grouping XXPY in view V_T7CCE_GPASG must not be changed.
  - Payroll areas included in a combined payroll run must belong to a single payroll area grouping (view V_T549AGA).
  - Payroll area groupings used for MPSD must have consistent payment dates, time units, and may have different period start dates for their period modifiers (PERMO).
  - Additional payroll areas can only be added to a payroll area grouping if no payroll results exist for them in the first payroll area of the grouping.
  - Payroll area deletions from a grouping are prohibited in the live system.
  - Splitting of payroll results as per table T530 is unsupported.
  - Organizational reassignments or switches to another area of grouping require a new employment contract or the use of an existing one already associated with the new payroll area or grouping.

The note provides detailed steps to activate the MPSD function:
- Refer to Note 520965 regarding the release of the concurrent employment function.
- Set the MPSDS switch to 'X' (yes) in the V_T77S0 view for the CCURE group and ensure it remains activated.
- Enter the grouping rule XXMP with grouping reason XXPY in view V_T7CCE_GPASG without changing the rule thereafter.
- Maintain payroll area groupings in view V_T549AG.
- Assign payroll areas to their respective payroll area groupings using view V_T549AGA.

The note specifies that the functionality is currently only available for the Canadian payroll and advises on contacting the respective product manager for enhancements needed for other countries. Moreover, this note represents a special development and is delivered with an HR Support Package, not including correction instructions since it is not an error correction. The implementation of these configurations can also be found within the SAP system's IMG under the payroll for Canada, within the Concurrent Employment - Multiple Period Start Days option.