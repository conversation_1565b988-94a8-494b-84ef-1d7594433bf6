{"Request": {"Number": "1984997", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 377, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000011719792017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001984997?language=E&token=7A0BB93106CC97DDC0C8DA14CF229B82"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001984997", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1984997"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.02.2014"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-EWM-RF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Radio Frequency Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Extended Warehouse Management", "value": "SCM-EWM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-EWM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Radio Frequency Processing", "value": "SCM-EWM-RF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-EWM-RF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1984997 - RF performance measurement tool does not store the warehouse info"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are using the RF performance measurement tool in different warehouses. The tool is not able to store the warehouse information so later in the analysis you cannot group the times per warehouse.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RF perf tool, performance measure</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>WH was not stored.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the enhancement.</p>\r\n<p>!!!!!!!!!!!!!!!!!!!!!!!!!<br />!!!!IMPORTANT!!!!!!<br />!!!!!!!!!!!!!!!!!!!!!!!!!<br />This note just add the warehouse info to the logs, and to use this info you must have the relevant service package of ST/A- PI.<br />Currently it is in development phase, as soon it is ready to ship, this note will be updated.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "/scwm/rfui"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I034552)"}, {"Key": "Processor                                                                                           ", "Value": "I043697"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001984997/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984997/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984997/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984997/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984997/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984997/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984997/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984997/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001984997/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2115913", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "RF performance measurement tool does not display the warehouse info", "RefUrl": "/notes/2115913 "}, {"RefNumber": "1595305", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Measuring runtimes for RF devices in SAP EWM", "RefUrl": "/notes/1595305 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SCMEWM", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "702", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "712", "To": "712", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "900", "To": "900", "Subsequent": ""}, {"SoftwareComponent": "SCMEWM", "From": "910", "To": "910", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SCMEWM 701", "SupportPackage": "SAPK-70113INSCMEWM", "URL": "/supportpackage/SAPK-70113INSCMEWM"}, {"SoftwareComponentVersion": "SCMEWM 702", "SupportPackage": "SAPK-70212INSCMEWM", "URL": "/supportpackage/SAPK-70212INSCMEWM"}, {"SoftwareComponentVersion": "SCMEWM 712", "SupportPackage": "SAPK-71207INSCMEWM", "URL": "/supportpackage/SAPK-71207INSCMEWM"}, {"SoftwareComponentVersion": "SCMEWM 900", "SupportPackage": "SAPK-90010INSCMEWM", "URL": "/supportpackage/SAPK-90010INSCMEWM"}, {"SoftwareComponentVersion": "SCMEWM 910", "SupportPackage": "SAPK-91002INSCMEWM", "URL": "/supportpackage/SAPK-91002INSCMEWM"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SCMEWM", "NumberOfCorrin": 4, "URL": "/corrins/0001984997/4620"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SCMEWM", "ValidFrom": "701", "ValidTo": "701", "Number": "1690850 ", "URL": "/notes/1690850 ", "Title": "Performance measurement in RFUI", "Component": "SCM-EWM-RF"}, {"SoftwareComponent": "SCMEWM", "ValidFrom": "701", "ValidTo": "701", "Number": "1785323 ", "URL": "/notes/1785323 ", "Title": "The performance measurement trace is incomplete", "Component": "SCM-EWM-RF-FW"}, {"SoftwareComponent": "SCMEWM", "ValidFrom": "702", "ValidTo": "702", "Number": "1690850 ", "URL": "/notes/1690850 ", "Title": "Performance measurement in RFUI", "Component": "SCM-EWM-RF"}, {"SoftwareComponent": "SCMEWM", "ValidFrom": "702", "ValidTo": "702", "Number": "1785323 ", "URL": "/notes/1785323 ", "Title": "The performance measurement trace is incomplete", "Component": "SCM-EWM-RF-FW"}, {"SoftwareComponent": "SCMEWM", "ValidFrom": "900", "ValidTo": "900", "Number": "1785323 ", "URL": "/notes/1785323 ", "Title": "The performance measurement trace is incomplete", "Component": "SCM-EWM-RF-FW"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}