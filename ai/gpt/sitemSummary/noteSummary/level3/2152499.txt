SAP Note 2152499 serves as the central note for the Patch 02 of Support Package 05 (SP05) for SAP Business Planning & Consolidation (BPC) 10.1, version for SAP NetWeaver.

**Symptom**:
The note addresses issues and provides information on SAP BPC 10.1 NW SP05 Patch02.

**Other Terms**:
Terminology used in this context includes BPC 10.1, installation, and central note.

**Reason and Prerequisites**:
- The note specifies that a system must have SAP Business Planning and Consolidation 10.1 on NetWeaver 740 (SAP_BW740) SP10.
- BPC 10.1 NW SP05 Patch01 must already be applied (central note 2095747 mentioned for reference).
- SAP UI5 version 1.26.1 (corresponding to SAP_UI5 SP11) is identified as compatible with this patch.

**Solution**:
- BPC 10.1 SP05 Patch01 contains bug fixes with reference notes for more details.
- EPM add-in SP21 is recommended for compatibility.
- It directs the application of note 2158614, which is BPC 10.1 NW SP05 Patch 02, but warns that the patch may not be reversible, and suggests a system backup before implementation.
- There are two warning objects when implementing the note 2158614 that need to be selected.
- Clearing the server cache is necessary after note implementation, with consulting note 1968214 available for further guidance.

The note acknowledges two issues with this patch that only function correctly in debug mode and explains how to enable debug mode in the URL for troubleshooting performance.

**For BPC 10.1 Embedded (Component SAP_BW)**:
- Several mandatory and recommended ABAP notes should be applied together with Patch 02.

**For BPC 10.1 Standard (Component CPMBPC)**:
- A list of mandatory ABAP notes must be installed with this patch to correct issues, such as work status checks, migration problems, report and form settings, and others detailed in the given list.

In summary, this SAP Note is a comprehensive guideline for applying Patch 02 to BPC 10.1 NW SP05 and includes prerequisites, detailed instructions on the update process, and a list of additional mandatory and recommended notes to resolve specific issues.