{"Request": {"Number": "106047", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 393, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014570142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000106047?language=E&token=4627D8454D0FB8CB326FB5479F4AE663"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000106047", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000106047/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "106047"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 23}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.06.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "106047 - DB21: Customizing the DBSTATC"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The parameters that BRCONNECT selected by default for the CBO statistics (accuracy, sample size, histograms, and so on) are not optimal for individual objects. As a result, some SQL statements use unsuitable access paths, which results in poor performance.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>If you are still using SAPDBA to create statistics, see Note 403704 for changing to BRCONNECT.<br /><br />BRCONNECT determines how the statistics are created (for example, sample size, histogram statistics, and refresh times) in accordance with internal rules. See Note 588668 for further information. This procedure is in most cases sufficient to create useful statistics. In some cases, however, you must configure exceptions to achieve optimal statistics; for example:</p>\r\n<ul>\r\n<li>Histogram statistics must be created.</li>\r\n</ul>\r\n<ul>\r\n<li>You no longer want the system to overwrite the state of the table statistics with new statistics.</li>\r\n</ul>\r\n<ul>\r\n<li>You do not want to create any statistics on a table at all.</li>\r\n</ul>\r\n<ul>\r\n<li>You require statistics that are based on a larger (or smaller) sample size.</li>\r\n</ul>\r\n<p>To configure these modifications of the standard system behavior, make the corresponding entries in the exception table DBSTATC.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Call transaction DB21. The entries of the DBSTATC table are displayed in a sorted manner. Search for the table containing the values you want to change to determine whether it already contains an entry. If you find an entry, you can switch to change mode by double clicking on the existing entry. Otherwise, you can create a new entry using the \"New Entries\" button.<br /><br />Important:</p>\r\n<ul>\r\n<li>Do not implement any duplicate records. This can result in errors such as ORA-00001 (Note 458872). Tables may appear twice only if the entries refer to different databases (for example, ORACLE as opposed to DB6) or if individual statistics have been maintained in accordance with SAP Note1374807.</li>\r\n</ul>\r\n<ul>\r\n<li>Where necessary, ignore the warning \"Only maintenance by SAP allowed\" (Note 128486).</li>\r\n</ul>\r\n<ul>\r\n<li>As well as manually implemented entries, the DBSTATC table also contains numerous other entries for tables with special processing (note 122718) by default. Historically, the table was also used as an interface between \"sapdba -checkopt \" and \"sapdba -analyze \".</li>\r\n</ul>\r\n<ul>\r\n<li>You can only sometimes perform the fixing of statistics (according to the method described in Note 1374807) using transaction DB21. In individual cases, you must implement the changes manually. </li>\r\n</ul>\r\n<ul>\r\n<li>When you use Note 1374807 to fix statistics, the original meaning of columns sometimes changes (for example, ACTIV=R, DOTYP, OBJOW, OBJECT).</li>\r\n</ul>\r\n<p>You can now enter the following:</p>\r\n<ol>1. Database object: &lt;table name&gt;</ol><ol>2. Object type: 01  (for table)</ol><ol>3. Object owner: Release, or enter the name of the database user (SAPR3 or SAP&lt;sid&gt;).</ol><ol>4. Database: ORACLE</ol><ol>5. Usage type: O (for optimizer)</ol><ol>6. active:</ol><ol><ol>a) A -&gt; Statistics created if necessary.</ol></ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BRCONNECT determines whether statistics are necessary, taking into account the number of table entries compared to the number of the table entries during the last statistics creation. If the variance exceeds a threshold value (default: stats_change_threshold = 50%), new statistics are created.</p>\r\n<ol><ol>b) U -&gt; Unconditional creation of statistics.</ol></ol><ol><ol>c) N -&gt; No creation of statistics</ol></ol><ol><ol>d) R -&gt; Only temporary creation of statistics (similar to \"N\") or new statistics fixing in accordance with Note 1374807</ol></ol><ol><ol>e) I -&gt; Ignore the table (that is, no change to the current statistics status)</ol></ol><ol>7. Analysis method (or option):</ol><ol><ol>a) C  -&gt; COMPUTE</ol></ol><ol><ol>b) CH -&gt; COMPUTE + histograms</ol></ol><ol><ol>c) E  -&gt; ESTIMATE</ol></ol><ol><ol>d) EH -&gt; ESTIMATE + histograms</ol></ol><ol>8. Sample size: Only necessary with the \"E\" or \"EH\" analysis method.</ol><ol><ol>a) P&lt;percent&gt;: Sample size in percent; for example, \"P10\" means that ten percent of the table is used for the analysis.</ol></ol><ol><ol>b) R&lt;rows&gt;: Sample size in units of 1,000 rows; for example, \"R6\" means that 6,000 rows of the table are used for the analysis.</ol></ol><ol>9. Customer flag: X (useful to mark customer-specific DBSTATC entries; necessary to create statistics for pool tables and cluster tables in Oracle 9i or lower).</ol><ol>10. EDM cluster (or UDM cluster):</ol><ol><ol>a) THLD:&lt;value_in_%&gt;</ol></ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Table-specific change threshold value (STATS_CHANGE_THRESHOLD), evaluated as of BRCONNECT 7.10 (27)</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 11. DURAT:</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAP Note 892296 (8) describes how you can use the field DURAT of the table DBSTATC to configure histogram configurations and Oracle Parallel Execution. It is not possible to maintain transaction DB20 for this purpose; instead, DBSTATC must be adjusted directly.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 12.&#x00A0;Select the update check box if you definitely want to use the desired settings to generate statistics during the next analysis run.</p>\r\n<p>You do not have to make any other settings.<br /><br />The R/3 online documentation contains comprehensive information on the creation of statistics and on entries in DBSTATC.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA (Oracle)"}, {"Key": "Other Components", "Value": "BC-DB-ORA-CCM ((CCMS) Database Monitors)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030484)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000106047/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000106047/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000106047/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000106047/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000106047/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000106047/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000106047/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000106047/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000106047/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "972013", "RefComponent": "SLL-LEG-CON", "RefTitle": "Performance Blocked Documents Report: Database settings", "RefUrl": "/notes/972013"}, {"RefNumber": "932975", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle statistics for RFC tables", "RefUrl": "/notes/932975"}, {"RefNumber": "93098", "RefComponent": "BC-DB-ORA", "RefTitle": "CBO: Changes in upgrade to 4.0", "RefUrl": "/notes/93098"}, {"RefNumber": "902675", "RefComponent": "MM-IM-GF-REP", "RefTitle": "MB51: Database hints to improve runtime / 4.70", "RefUrl": "/notes/902675"}, {"RefNumber": "902157", "RefComponent": "MM-IM-GF-REP", "RefTitle": "MB5B: Improving the runtime using database hints/4.70", "RefUrl": "/notes/902157"}, {"RefNumber": "869006", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-04031", "RefUrl": "/notes/869006"}, {"RefNumber": "724545", "RefComponent": "BC-DB-ORA", "RefTitle": "Adjusting the CBO statistics manually using DBMS_STATS", "RefUrl": "/notes/724545"}, {"RefNumber": "458872", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "ORA-00001 with sapdba or BR tools", "RefUrl": "/notes/458872"}, {"RefNumber": "371068", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/371068"}, {"RefNumber": "335415", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/335415"}, {"RefNumber": "151847", "RefComponent": "CA-DMS", "RefTitle": "Index on table DRAW is not used optimally", "RefUrl": "/notes/151847"}, {"RefNumber": "1374807", "RefComponent": "BC-DB-ORA", "RefTitle": "Freezing single kinds of statistics", "RefUrl": "/notes/1374807"}, {"RefNumber": "131372", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/131372"}, {"RefNumber": "1312060", "RefComponent": "BW-BCT-CO-OM", "RefTitle": "BW-BCT-CO-OM: COSP/COSS Extractor performance", "RefUrl": "/notes/1312060"}, {"RefNumber": "128486", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB21: Customizing the DBSTATC", "RefUrl": "/notes/128486"}, {"RefNumber": "122718", "RefComponent": "BC-DB-ORA", "RefTitle": "CBO: Tables with special treatment", "RefUrl": "/notes/122718"}, {"RefNumber": "109034", "RefComponent": "BC-DB-ORA", "RefTitle": "Collective note: SAPDBA - performance/CBO", "RefUrl": "/notes/109034"}, {"RefNumber": "104964", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/104964"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2957955", "RefComponent": "BC-DB-ORA", "RefTitle": "Wrong index used for table INOB - NetWeaver", "RefUrl": "/notes/2957955 "}, {"RefNumber": "1842251", "RefComponent": "BC-DB-ORA", "RefTitle": "Missing or incorrect database statistic for a SAP table", "RefUrl": "/notes/1842251 "}, {"RefNumber": "2250334", "RefComponent": "BC-DB-ORA", "RefTitle": "12c : ORA-06502 PL/SQL: numeric or value error: character string buffer overflow creating statistcs", "RefUrl": "/notes/2250334 "}, {"RefNumber": "724545", "RefComponent": "BC-DB-ORA", "RefTitle": "Adjusting the CBO statistics manually using DBMS_STATS", "RefUrl": "/notes/724545 "}, {"RefNumber": "176754", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with CBO and RBO", "RefUrl": "/notes/176754 "}, {"RefNumber": "869006", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-04031", "RefUrl": "/notes/869006 "}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668 "}, {"RefNumber": "122718", "RefComponent": "BC-DB-ORA", "RefTitle": "CBO: Tables with special treatment", "RefUrl": "/notes/122718 "}, {"RefNumber": "902675", "RefComponent": "MM-IM-GF-REP", "RefTitle": "MB51: Database hints to improve runtime / 4.70", "RefUrl": "/notes/902675 "}, {"RefNumber": "902157", "RefComponent": "MM-IM-GF-REP", "RefTitle": "MB5B: Improving the runtime using database hints/4.70", "RefUrl": "/notes/902157 "}, {"RefNumber": "1374807", "RefComponent": "BC-DB-ORA", "RefTitle": "Freezing single kinds of statistics", "RefUrl": "/notes/1374807 "}, {"RefNumber": "1312060", "RefComponent": "BW-BCT-CO-OM", "RefTitle": "BW-BCT-CO-OM: COSP/COSS Extractor performance", "RefUrl": "/notes/1312060 "}, {"RefNumber": "93098", "RefComponent": "BC-DB-ORA", "RefTitle": "CBO: Changes in upgrade to 4.0", "RefUrl": "/notes/93098 "}, {"RefNumber": "932975", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle statistics for RFC tables", "RefUrl": "/notes/932975 "}, {"RefNumber": "972013", "RefComponent": "SLL-LEG-CON", "RefTitle": "Performance Blocked Documents Report: Database settings", "RefUrl": "/notes/972013 "}, {"RefNumber": "151847", "RefComponent": "CA-DMS", "RefTitle": "Index on table DRAW is not used optimally", "RefUrl": "/notes/151847 "}, {"RefNumber": "109034", "RefComponent": "BC-DB-ORA", "RefTitle": "Collective note: SAPDBA - performance/CBO", "RefUrl": "/notes/109034 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}