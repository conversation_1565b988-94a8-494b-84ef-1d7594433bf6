SAP Note 1121318 introduces a new analysis tool for diagnosing performance issues in variant configuration (VC), specifically focusing on the dependency knowledge components such as constraints, procedures, actions, selection conditions, and preconditions. If users are experiencing slow response times while entering or working with a sales document that contains configurable items, or within the configuration interface itself, this tool can help identify where the system is spending the most time during these processes.

The tool operates in two modes: Simple and Advanced. 

- **Simple Mode**: This mode is designed to measure dependency processing times during the entry of a sales order configuration or configuration simulation. It allows users to choose which transaction to analyze, set the proper clock type for measurement, perform a dependency trace to avoid non-representative times, and then perform an ABAP-trace.

- **Advanced Mode**: This mode allows the upload of existing trace files of dependency knowledge and ABAP runtime to perform a thorough analysis. It requires users to ensure that both traces match by performing identical steps during their capture.

The tool provides an Evaluation section with several tabs displaying various pieces of information such as the matching process results and the time each dependency has consumed. The analysis tool gives users a detailed breakdown of processing time across different dependency types and how often certain dependencies are executed.

In the Interpretation part, the note guides users on how to use the data collected to identify the most time-consuming dependencies and actions, and points out potential model inefficiencies that might be causing excess constraint executions or unnecessary BOM explosions. It also references other notable problems such as potential short dumps during trace collection due to buffer limitations, issues with dependency trace import, and invalid trace files when uploaded to different systems.

The note recommends applying the latest ST-A/PI package, referencing SAP Note 69455 and 1023995 for additional context and potential fixes for related issues.

References are made to other SAP Notes including SAP Note 917987 which discusses general performance within variant configuration, SAP Note 69455 for details on the ST-A/PI, SAP Note 1730246 regarding a wrong constraint count in ST13 - VARCONF - CUP, and SAP Note 1667903 & 1023995 concerning errors and issues within the trace tool and analysis process.