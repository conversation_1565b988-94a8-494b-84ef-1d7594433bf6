SAP Note 1854940 addresses an issue where incorrect data might be returned when using the StrToMember function in an MDX query under SAP NetWeaver Business Warehouse (BW) in very specific circumstances.

The note identifies this behavior as a program error and offers a solution in the form of Support Packages for different versions of SAP NetWeaver BW:

- For BW 7.01, import Support Package 14 (SAPKW70114), with detailed information available in SAP Note 1794836.
- For BW 7.02, import Support Package 14 (SAPKW70214), detailed in SAP Note 1800952.
- For BW 7.11, import Support Package 12 (SAPKW71112), detailed in SAP Note 1797080.
- For BW 7.30, import Support Package 10 (SAPKW73010), detailed in SAP Note 1810084.
- For BW 7.31, import Support Package 09 (SAPKW73109), detailed in SAP Note 1847231.
- For BW 7.40, import Support Package 4 (SAPKW74004), detailed in SAP Note 1853730.

In situations where a resolution is required urgently, the note advises the implementation of correction instructions in advance. However, to proceed with this, users must first familiarize themselves with transaction SNOTE by reading SAP Note 1668882.

Additionally, it's mentioned that the SAP Notes containing further details about the Support Packages may already be available even before the Support Package release. If this is the case, the SAP Notes will indicate that they are a "Preliminary version."

In summary, the SAP Note provides guidance on resolving a specific error with the StrToMember function, directs users to the appropriate Support Packages or advance corrections as a workaround, and references other SAP Notes for detailed information on the Support Packages and use of transaction SNOTE.