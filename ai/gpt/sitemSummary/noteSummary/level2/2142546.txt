SAP Note 2142546 addresses an issue with the performance of the transaction WRP1R, which is used for rapid replenishment. Customers are experiencing slower than expected runtimes when using this transaction.

The root cause of the slow performance is identified as an incorrect filter factor applied on the table WRRP_DOC_TYPE. The database statistics aren't accurately reflecting the situation at runtime, causing DB2 to assume that no rows will qualify when executing queries. Consequently, this incorrect assumption by DB2 contributes to the poor performance observed.

To resolve this issue, the SAP Note prescribes a solution involving the deletion of catalog statistics for the 'PROCESS_ID' column in the 'WRRP_DOC_TYPE' table. Users are instructed to execute an SQL statement that updates the relevant system tables within the DB2 database to remove these statistics. Specifically, the note gives the following SQL command for users to execute:

```sql
update sysibm.syscolumns set colcardf = -1 where name = 'PROCESS_ID' and tbname = 'WRRP_DOC_TYPE';
```

Executing this SQL statement should help alleviate the performance issue by altering how DB2 optimizes and executes queries for the rapid replenishment process. The note does not reference any other notes or documents. This suggests that the provided solution is self-contained and does not require further context from additional SAP Notes.