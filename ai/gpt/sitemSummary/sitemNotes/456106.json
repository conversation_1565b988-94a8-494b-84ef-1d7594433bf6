{"Request": {"Number": "456106", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 338, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015136962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000456106?language=E&token=6CF132A27B01A666282F6EA2ECCF11D3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000456106", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000456106/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "456106"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.01.2002"}, "SAPComponentKey": {"_label": "Component", "value": "MM-IS-IC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Inventory Controlling"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Information System", "value": "MM-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Inventory Controlling", "value": "MM-IS-IC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-IS-IC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "456106 - Standard analyses: Display of values"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The note describes the display of the value key figures for separately valuated materials in the INVCO standard analyses and the display of the value key figures after price changes and stock transfers.<br /><br />Represented here is the valuation procedure that is preset up to and including Release 4.5B and which can as of Release 4.6 be selected on the selection screen as valuation procedure \"Standard\".<br /></p> <OL>1. Price change for separate valuated materials:</OL> <p><br />Assumption: material 4711 is stored for the first time in month 10.2001 in storage location 0001 of plant 0001. For example, this receipt is reflected in the standard analyses as follows:<br /><br />Plant 0001&#x00A0;&#x00A0; Material 4711&#x00A0;&#x00A0; Month 10.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br />In the INVCO info structures (combination of stock and movement structure) the following values are stored for this:<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 100&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br /><br />In the following month 5 pieces are added:<br /><br />Standard analysis:<br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 11.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br />Info structure:<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br /><br />In the same month the price is changed from 10 to 11<br /><br />Standard analysis:<br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 11.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;65&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;165<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br />Info structure:<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br /><br />The price change is assigned to the storage location &lt;blank&gt;, because there is no reference to a certain storage location in the accounting document for the price change.<br /><br />Afterwards 3 pieces of this material are transferred from storage location 0001 to storage location 0002.<br /><br />Standard analysis:<br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 11.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 65&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;33&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;12&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;132<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;33&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 33<br /><br />Info structure:<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 15<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;12&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br /><br />During the stock transfer the system does not generate any accounting document, that is, only quantities but not values are updated into the INVCO info structures. Therefore, the declarations of value for transferred quantities are missing.<br /><br /><br />Finally 1 piece of the material is taken from the stock of storage location 0002.<br /><br />Standard analysis:<br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 11.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;65&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;33&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 12&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;132<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;33&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;11&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 22<br /><br /><br />Info structure:<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;12&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;11&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-11<br /><br /></p> <OL>2. Price change due to invoice receipt for valuation with moving price</OL> <p><br />If the moving average price is used for the valuation of a material, invoice receipts with changed price might require surcharges for the value of the receipts or the value of the outward movements. This is explained by means of the following example:<br /><br />Assumption: From material 4711 there is a stock of 10 piece in month 10.2001. This then might result in the following display in the standard analysis:<br /><br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 10.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100<br /><br />In the INVCO info structures (combination of stock and movement structure) the following values stored for this:<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;100<br /><br /><br />In the following month 11.2001 a goods receipt of 5 pieces for the price of 10 is posted first:<br /><br />Standard analysis:<br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 11.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150<br /><br /><br />Info structure:<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150<br /><br /><br />Then a goods issue follows of 4 pieces for the price of 10:<br /><br />Standard analysis:<br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 11.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;40&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;150<br /><br />Info structure:<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;40&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;11&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;110<br /><br /><br />Finally, an invoice receipt occurs (also in the month 11.2001), in which 5 pieces of material 4711 with a price of 21 are updated, that is, the price of material 4711 with which the goods receipt was updated deviates from the price in the invoice:<br /><br />Standard analysis:<br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 11.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;125&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;60&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;11&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;165<br /><br />Info structure:<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;55&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 55<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;50&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;40&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;11&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;110<br /><br /><br />As in the first example, the price change is assigned to the storage location &lt;blank&gt; because the accounting document does not have any reference to the storage location. The amount that is surcharged to the storage location &lt;blank&gt; results from the difference between the price at the time of the goods receipt and the price specified in the invoice: 5 x ( 21 - 10 ) = 55. As a result, the invoice receipt has a change of the average price from 10 to 15. Therefore, the following clearing invoice is necessary:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Value of overall stock in previous month<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;+ Value of all receipts in current month<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Value of all outward movements in current month<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;+ Clearing amount for value change<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;= Value of total stock in current month<br /><br />In the above listed example this would be:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;100 + 75 - 60 + clearing amount = 165<br /><br />This results in the value of the clearing amount of 50. The amount is positive, therefore it is surcharged to the receipts. Basically, the following applies: Price increases result in a surcharge for the receipt value, and price reductions result in a surcharge for the asset retirement value. The clearing invoice is necessary to achieve the understanding of the stock back calculation (\"Stock value + receipt value - issue value results in the value of the previous month\") and at the same time the correspondence of the total stock value with the values specified in the overviews of the materials management.<br /><br />For the comparison of the valuations in the Inventory Controlling with the valuations of the MM evaluations applies that the values of the INVCO must agree with the MM values at the respective month's end at plant level. The value of the receipts and the issues can deviate from the value that results from the product of quantity and average price (as described in the above example), and thus also from the values that are specified in the material documents. The example shows that the variance can be considerable if the price change is high and concerns a considerable part of the total stock.<br /></p> <OL>3. Goods movement for separately valuated materials:</OL> <p><br />Assumption: for material 4711 there are two different valuation types. The price of the material according to valuation type 1 would be 9, the price according to valuation type 2 would be 11.<br /><br />The material is placed into stock for the first time in month 10.2001 in storage location 0001 of plant 0001. The valuation would correspond to valuation type 2. For example, this receipt is reflected in the standard analyses as follows:<br /><br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 10.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;110&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;110<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br />In the INVCO info structures (combination of stock and movement structure) the following values stored for this:<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;110&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;10&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;110<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0<br /><br /><br />In the same month 10 pieces are added which are valuated according to valuation type 1:<br /><br />Standard analysis:<br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 10.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;200&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;200<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br />Info structure:<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;200&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;200<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br /><br />The valuation type is not contained as an actual characteristic (that is, as record key) in the standard analyses of the INVCO. The character key figure \"valuation type\" only contains the key of the last posting. Therefore, the two receipts are summarized in a data record and valuated with the average price.<br /><br />In the following month the price of valuation type 1 is changed from 9 to 0.<br /><br />Standard analysis:<br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 11.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;90&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;110<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br />Info structure:<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;90&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-90<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 20&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;200<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br />The price change is assigned to the storage location &lt;blank&gt; because in the accounting document for the price change there is no reference to a certain storage location.<br /><br />Finally 1 piece of the material is taken from the stock of storage location 0001. From the corresponding material document follows that the withdrawal must be valuated according to valuation type 1 (current price after price change 0).<br /><br /><br />Standard analysis:<br />Plant 0001&#x00A0;&#x00A0;Material 4711&#x00A0;&#x00A0;Month 11.2001<br /><br />Stor.loc.&#x00A0;&#x00A0; qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;val.&#x00A0;&#x00A0;&#x00A0;&#x00A0;qty&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; val.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0;receipt&#x00A0;&#x00A0; issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;issue&#x00A0;&#x00A0;&#x00A0;&#x00A0;stock&#x00A0;&#x00A0;&#x00A0;&#x00A0; stock<br /><br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5.5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5.5&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;19&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;110<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br />Info structure:<br /><br />&lt;blank&gt;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;90&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-90<br />0001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;19&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;200<br />0002&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><br />In the info structure the document for the outward movement was updated with value 0. The withdrawal of a material with valuation price 0 changes the average price of the remaining stock. At the time of the withdrawal the average price is 110 / 20 = 5.5. That is, the average value of the withdrawal is also 5.5. However, due to valuation 0, the value of the total stock must not change. Therefore, a receipt value of the same amount without quantity is issued for the clearing of the withdrawal value.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Price change; split valuation</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>n/a</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>n/a<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027201)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D027201)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000456106/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000456106/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000456106/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000456106/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000456106/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000456106/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000456106/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000456106/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000456106/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "201327", "RefComponent": "MM-IS-IC-RPT", "RefTitle": "Values in Inventory Controlling", "RefUrl": "/notes/201327"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "201327", "RefComponent": "MM-IS-IC-RPT", "RefTitle": "Values in Inventory Controlling", "RefUrl": "/notes/201327 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}