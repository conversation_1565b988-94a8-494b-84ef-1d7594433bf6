SAP Note 495859 addresses an issue with the where-used list functionality for dictionary objects, such as tables and data elements. Users have experienced that this list is not displaying all possible instances where the specified type is utilized.

Key points about this SAP Note include:

- Symptom: The where-used list does not show all locations of usage for dictionary types.
- Other Terms: The issue relates to dictionary utilization within transactions like SE11 and SE38, as well as the program editor and program index.
- Cause: The root of the problem is identified as a program error.
- Solution: To resolve the issue, SAP advises customers to import the relevant Support Package or apply an advanced correction. Subsequently, it is necessary to create an index by scheduling a job for the program SAPRSEUJ with the variant EU_INIT. For further details on this process, the note references SAP Note 18023.

Overall, the note indicates a known defect within a specific SAP functionality and provides guidance on how to correct it to ensure complete and accurate where-used lists for dictionary types in SAP systems.