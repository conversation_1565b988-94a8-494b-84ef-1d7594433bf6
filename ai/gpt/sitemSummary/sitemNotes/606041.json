{"Request": {"Number": "606041", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 564, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015420812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000606041?language=E&token=564A0D0AA9168DCF50FEE50D7A832D99"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000606041", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000606041/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "606041"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.07.2005"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "606041 - Upgrade with ST-PI 003C (Solution Tools Plug-In)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You plan to upgrade your SAP system or are already in the prepare stage, during which you require an additional CD to update add-on ST-PI 003C.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Upgrade, add-on, ST-PI, Solution Tool Plug-in, 003C_45B, 003C_46B, 003C_46C, 003C_46D, 003C_610, 003C_620, 003C_640</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have installed add-on ST-PI 003C_[SAP-Release].</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>This note is continuously updated.</B><B> </B><B>Obtain the latest version before you start the upgrade.</B><br /></p> <b>1. General information</b><br /> <UL><LI>When the new release ST-PI 2005_1 is available in Q1/05, the upgrade packages are no longer available on SAP Service Marketplace. Use the upgrade packages for ST-PI 2005_1 as described in Note 769519</LI></UL> <UL><LI>The upgrades of all SAP products with ST-PI 003C, which are based on SAP_APPL 40B/45B or SAP_BASIS 46B - 620, are supported.<br />For this, you require an additional add-on-specific CD ('supplement CD'), which updates the add-on during the upgrade to the corresponding status for</LI></UL> <UL><UL><LI>SAP_APPL 40B/45B to SAP_BASIS 46C - 620</LI></UL></UL> <UL><UL><LI>SAP_BASIS 46B - 610 to SAP_BASIS 46C - 620</LI></UL></UL> <UL><UL><LI>SAP_BASIS 610/620 to SAP_BASIS 640</LI></UL></UL> <UL><LI>You do not require an add-on-specific CD for the upgrade to WebAS 640-based products (ERP 2004, SCM 4.1, and so on), since it the add-on specific CD is contained in the export.</LI></UL> <UL><LI>This CD is only available electronically on the SAP Service Marketplace and cannot be ordered from SAP as a physical CD.</LI></UL> <UL><LI><B>If you carry out the upgrade without this additional CD,</B><B>the status of your</B><B> SAP System will be inconsistent and the add-on will no longer work.</B></LI></UL> <UL><LI>The upgrade with ST-PI is released with all other add-ons; you may require further supplement CDs for the other add-ons.</LI></UL> <UL><LI>Required components Support Packages in the target release for</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>ST-PI 003C_45B</TH></TR> <TR><TD>SAP_APPL</TD><TD> 45B</TD><TD> SAPKH45B03</TD></TR> <TR><TH>ST-PI 003C_46B</TH></TR> <TR><TD>SAP_ABA</TD><TD> 46B</TD><TD> SAPKA46B04</TD></TR> <TR><TD>SAP_BASIS</TD><TD> 46B</TD><TD> SAPKB46B04</TD></TR> <TR><TH>ST-PI 003C_46C</TH></TR> <TR><TD>SAP_ABA</TD><TD> 46C</TD><TD> SAPKA46C03</TD></TR> <TR><TD>SAP_BASIS</TD><TD> 46C</TD><TD> SAPKB46C03</TD></TR> <TR><TH>ST-PI 003C_46D</TH></TR> <TR><TD>SAP_ABA</TD><TD> 46D</TD><TD> SAPKA46D02</TD></TR> <TR><TD>SAP_BASIS</TD><TD> 46D</TD><TD> SAPKB46D02</TD></TR> <TR><TH>ST-PI 003C_610</TH></TR> <TR><TD>SAP_ABA</TD><TD> 50A</TD><TD> SAPKA50A05</TD></TR> <TR><TD>SAP_BASIS</TD><TD> 610</TD><TD> SAPKB61005</TD></TR> <TR><TH>ST-PI 003C_620</TH></TR> <TR><TD>SAP_ABA</TD><TD> 620</TD><TD> None</TD></TR> <TR><TD>SAP_BASIS</TD><TD> 620</TD><TD> None</TD></TR> <TR><TH>ST-PI 003C_640</TH></TR> <TR><TD>SAP_ABA</TD><TD> 640</TD><TD> None</TD></TR> <TR><TD>SAP_BASIS</TD><TD> 640</TD><TD> None</TD></TR> </TABLE></UL> <p></p> <UL><LI>Space requirements</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>Space required in database</TD><TD> </TD><TD> 20 MB</TD></TR> <TR><TD>Space required in the transport directory</TD><TD> 4 MB</TD></TR> <TR><TD></TD></TR> </TABLE></UL> <b>2. Preparations</b><br /> <UL><LI>Download the CD archive<br />You can find the CD archive for the relevant SAP target release on the SAP Service Marketplace at<br />http://service.sap.com/INSTALLATIONS -&gt; SAP Solution Tools Plugin<br />-&gt; ST-PI -&gt; ST-PI 003C_&lt;REL&gt; -&gt; Supplementary upgrade</LI></UL> <UL><UL><LI>The archives are:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>003C_45B</TD><TD> 01200615322000000558.ZIP</TD></TR> <TR><TD>003C_46B</TD><TD> 01200615322000000557.ZIP</TD></TR> <TR><TD>003C_46C</TD><TD> 01200615322000000556.ZIP</TD></TR> <TR><TD>003C_46D</TD><TD> 01200615322000000555.ZIP</TD></TR> <TR><TD>003C_610</TD><TD> 01200615322000000554.ZIP</TD></TR> <TR><TD>003C_620</TD><TD> SAPKITLPRB</TD><TD> KITLPRB.CAR</TD></TR> <TR><TD>003C_640</TD><TD> SAPKITLPSB</TD><TD> KITLPSB.CAR</TD></TR> <TR><TD></TD></TR> </TABLE></UL></UL> <p></p> <UL><LI>Download the required component Support Packages<br />You can find the Support Packages, which are required for the relevant SAP target release, on the SAP Service Marketplace at:<br />http://service.sap.com/PATCHES<br />-&gt; SAP [Product] -&gt; SAP &lt;REL&gt; -&gt; Support Packages<br /></LI></UL> <b>3. Additional information about the upgrade</b><br /> <UL><LI>Additional information about PREPARE</LI></UL> <UL><UL><LI>Phase IS_READ (target release up to and including SAP_BASIS 610)<br />At the latest, you must install the additional CD now. To do this, unpack the archive into a temporary directory &lt;CD_DIR&gt; and specify &lt;CD_DIR&gt;/&lt;REL&gt; as the mount point. Make sure that the file names consist only of upper case letters.<br />If you use WinZip, choose 'Options -&gt; Configuration' and activate the 'Allow all upper case file names' checkbox on the 'View' tab page under 'General'.</LI></UL></UL> <UL><UL><LI>Phase IS_SELECT (Module Extension, as of target release SAP_BASIS 620)<br /><B>Before </B>this module, you must place all required Support Packages and SAINT packages into the EPS inbox (this is usually directory &lt;DIR_TRANS&gt;/EPS/in or directory &lt;DIR_TRANS&gt;\\EPS\\in).<br />You must now decide how to handle ST-PI in the upgrade. Select 'Upgrade with SAINT package'.<br />However, if you carry out a component upgrade, in other words if the underlying SAP_BASIS release does not change - for example, when you upgrade from R/3 Enterprise 4.70 Extension Set 1.10 to Extension Set 2.00 - select 'KEEP VERSION' or 'KEEP YOURS' for ST-PI. You may also have to enter a keyword. The key words are:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>SAP_BASIS</TH><TH ALIGN=LEFT> Key</TH></TR> <TR><TD>620</TD><TD> 258345</TD></TR> <TR><TD>640</TD><TD> 276135</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Phase BIND_PATCH<br />Include all the necessary component Support Packages of the target release in the upgrade.</LI></UL></UL> <UL><LI>Additional information about R3up</LI></UL> <UL><UL><LI>Phase KEY_CHK<br />A key is requested for ST-PI with a reference to Note 86985. This note contains a general explanation on the release of SAP upgrades with add-ons. This note is referred to for ST-PI.<br />This key is also valid for component upgrades.</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Target release</TH><TH ALIGN=LEFT> Key</TH></TR> <TR><TD>003C_46C</TD><TD> 316630</TD></TR> <TR><TD>003C_46D</TD><TD> 354930</TD></TR> <TR><TD>003C_610</TD><TD> 394800</TD></TR> <TR><TD>003C_620</TD><TD> 390975</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI>Phase CHK_POSTUP (only upgrade from release 4xx to SAP_BASIS 640)<br />In the LONGPOST.LOG log, you may find rows in the following form:<br />Table and runtime object \"...\" exist without DDIC reference<br />If this concerns objects<br />/SQLR/RELE, /SQLR/TAB, /SQLR/TEXT1 and /SQLR/TEXT2, use the database utility (Transaction SE14, or call SE11 and choose Utilities -&gt; Database Utility) to delete them manually.</LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D020457)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D035944)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000606041/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000606041/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000606041/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000606041/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000606041/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000606041/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000606041/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000606041/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000606041/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "KITLPA2.car", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000239582003&iv_version=0009&iv_guid=8D47F273C6629041B8C815AF7549F722"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "86985", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release of R/3 releases for SAP R/3 add-ons from 4.0A", "RefUrl": "/notes/86985"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "733796", "RefComponent": "BC-UPG-OCS", "RefTitle": "SAP ERP Central Component 5.0: Software architect./mainten.", "RefUrl": "/notes/733796"}, {"RefNumber": "689574", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/689574"}, {"RefNumber": "597673", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/597673"}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977 "}, {"RefNumber": "86985", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release of R/3 releases for SAP R/3 add-ons from 4.0A", "RefUrl": "/notes/86985 "}, {"RefNumber": "733796", "RefComponent": "BC-UPG-OCS", "RefTitle": "SAP ERP Central Component 5.0: Software architect./mainten.", "RefUrl": "/notes/733796 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-PI", "From": "003C_45B", "To": "003C_45B", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "003C_46B", "To": "003C_46B", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "003C_46C", "To": "003C_46C", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "003C_46D", "To": "003C_46D", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "003C_610", "To": "003C_610", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "003C_620", "To": "003C_620", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "003C_640", "To": "003C_640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}