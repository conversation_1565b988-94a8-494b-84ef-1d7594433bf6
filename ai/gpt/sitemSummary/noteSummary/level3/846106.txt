SAP Note 846106 addresses an issue with data records being deleted from data packages in the start routines of the InfoSources 2LIS_02_SCL, 2LIS_03_BX, and 2LIS_03_BF. These start routines are implemented in mySAP ERP 2005 to handle the 'explosion' of structured articles which is the separation of a document record into its individual components.

The note explains the purpose of the start routines, which is to prevent the extracted transaction data in SAP Business Warehouse (BW) from being updated multiple times, leading to incorrect values. To enable the explosion of structured articles, the WRF_BWEXT_STRUKTART implementation of the RS05_SAPI_BADI Business Add-In must be activated.

To ensure the accuracy of data in the connected data targets after activating the Business Add-In, start routines separate exploded and unexploded data records using the 'RSBCT_RFASH_MATERIAL_EXPLO' function module. Parameters E_T_DATA_NOR and E_T_DATA_EXP within this function module are used to control and return unexploded and exploded structured article records, respectively.

Two scenarios are outlined in the note:

1. If only the unexploded structured article records need to be analyzed in SAP BW, the standard Content delivers this functionality without further adjustments.

2. If both unexploded and exploded records are to be analyzed, separate data targets should be created, and specific source code (as provided in the Business Content's start routine of the transfer rule) applied in the start routine of the data target updates for these respective records. After implementing this, the start routine from the transfer rules can be deleted, ensuring correct data segregation in all connected data targets to prevent the creation of incorrect data. 

The note is intended to guide users on how to correctly handle structured articles in SAP BW, ensuring accurate data analysis and preventing potential errors due to multiple updates of transaction data.