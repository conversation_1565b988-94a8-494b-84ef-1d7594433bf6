{"Request": {"Number": "689035", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 583, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015577612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000689035?language=E&token=F93A97D564E7D2C1A7954CF8C1E72ACA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000689035", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000689035/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "689035"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.05.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-TAN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Transaction TAANA for Table Analysis"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transaction TAANA for Table Analysis", "value": "BC-CCM-TAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-TAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "689035 - Table analysis: Partial & virtual fields - advance delivery"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><UL><LI>You cannot merely analyze a section of a table field when analyzing a table. Example: The table contains a date field. You want to analyze the data distribution by the year.</LI></UL> <UL><LI>You cannot include a field in an analysis variant that is not contained in the table to be analyzed, whose values, however, can be determined from the existing table fields or from other tables. Example: The table to be analyzed does not contain any field that contains the archiving object used to archive a data record.</LI></UL> <UL><LI>You cannot create an analysis variant as local object. The system always prompts you for a transport request.</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Transactions: TAANA, ANA_VAR, TAANA_AV, TAANA_VF<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The problem is due to missing development.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As of Basis Release 640, the table analysis was enhanced. With the new TAANA_VF transaction, you can now create \"virtual\" fields that simulate a subfield of an existing table field or a new table field. You can include these fields in an analysis variant as usual in transaction TAANA_AV.<br />In transaction TAANA, you can also create an \"ad-hoc\" variant while performing an analysis. An ad-hoc variant does not require any transport request and is not stored in the database. It is only available for the analysis that you are currently scheduling.<br />Documentation about the available functions are available on the initial screen of the transactions mentioned as well as in the F1 field help.<br />The Support Package assigned to this note contains the new version of the table analysis as advance delivery for earlier releases. The new version can only be delivered by means of a Support Package, not by correction instructions.<br />Importing this version will not invalidate the analyses and analysis variants that already exist in your system.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D022872)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D035465)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000689035/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000689035/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000689035/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000689035/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000689035/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000689035/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000689035/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000689035/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000689035/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "807482", "RefComponent": "BC-DWB-CEX", "RefTitle": "XPRA error in upgrade due to structure ARDB_TABLE_VIRT_FIELD", "RefUrl": "/notes/807482"}, {"RefNumber": "689036", "RefComponent": "BC-CCM-TAN", "RefTitle": "Table analysis: Archive analysis variants - advance delivery", "RefUrl": "/notes/689036"}, {"RefNumber": "1282667", "RefComponent": "BC-CCM-TAN", "RefTitle": "TAANA: Virtual fields for a UTC reference field", "RefUrl": "/notes/1282667"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1282667", "RefComponent": "BC-CCM-TAN", "RefTitle": "TAANA: Virtual fields for a UTC reference field", "RefUrl": "/notes/1282667 "}, {"RefNumber": "689036", "RefComponent": "BC-CCM-TAN", "RefTitle": "Table analysis: Archive analysis variants - advance delivery", "RefUrl": "/notes/689036 "}, {"RefNumber": "807482", "RefComponent": "BC-DWB-CEX", "RefTitle": "XPRA error in upgrade due to structure ARDB_TABLE_VIRT_FIELD", "RefUrl": "/notes/807482 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C48", "URL": "/supportpackage/SAPKB46C48"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D36", "URL": "/supportpackage/SAPKB46D36"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61039", "URL": "/supportpackage/SAPKB61039"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62036", "URL": "/supportpackage/SAPKB62036"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64001", "URL": "/supportpackage/SAPKB64001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}