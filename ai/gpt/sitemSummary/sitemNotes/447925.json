{"Request": {"Number": "447925", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 555, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015120222017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000447925?language=E&token=EC20E8937D7AD7A3BCA61974762519BB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000447925", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000447925/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "447925"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 67}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.11.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-OCS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)", "value": "BC-UPG-OCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-OCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "447925 - OCS: Known problems with Support Packages in Basis Rel. 6.20"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes problems that may occur when you import support<br />packages.<br /><br /><B>Caution:Since the internal structure of this note is irreparably</B><br /><B>corrupted, we can no longer maintain or enhance it. For this reason, we</B><br /><B>have created Note 782140 as a copy of Note 447925 (this note). Only Note</B><br /><B>782140 will be maintained in future, therefore, use only Note 782140!</B><br /><br /><br />Occasionally, you may have to perform a SPAM/SAINT update before you can<br />import a specific Support Package. However, a SPAM/SAINT update may<br />require that a certain R3trans or tp version is installed in your system<br />This note also informs you when a new SPAM/SAINT update, R3trans or tp<br />is required.<br /><br />Note also that due to the new Support Package Stack concept,<br />dependencies can be defined between the different Support Package types.<br />For example, the R/3 Support Package SAPKH47017 requires Basis Support<br />Package SAPBK62034 and ABA Support Package SAPKA62034. You can find<br />existing support package stacks, as well as further information about<br />the Support Package Stack concept on the SAP Service Marketplace at<br />http://service.sap.com/sp-stacks .<br />Basis Support Packages may also require SAP kernels with a minimum patch<br />number. The following interdependencies currently exist:<br />SAPKB62029: Kernel 6.20, patch number 1027, see Note 644640<br />SAPKB62038: Kernel 6.20, patch number 1269, see Note 720083<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>OCS, SPAM, SPAM/SAINT UPATE, CONFLICT RESOLUTION TRANSPORT, SUPPORT<br />PACKAGE, CRT, LOAD_PROGRAM_LOST, SAPSQL_SELECT_WA_TOO_SMALL,<br />SAPSQL_WA_TOO_SMALL, XPRA_EXECUTION, TP_FAILURE, TEST IMPORT,<br />DDIC ACTIVATION<br /><br /><B>Change history</B><br /><B>Nov/29/2004: Note 447925 will not be maintained anymore!</B><br /><B>Use Note 782140 instead!</B><br />- Oct/06/2004: SAPKGPABO8: Error in DDIC activation<br />- Oct/06/2004: Database error ORA-02168 on Oracle systems with<br />'Automatic Segment Space Management (ASSM)' active.<br />- Oct/06/2004: SAPKB62043: Test plan generation in the Solution Manager<br />fails<br />- Oct/06/2004: SAPKB62027 and higher in systems on MS SQL servers:<br />Library dbmssslib.dll should have patch level lib_dbsl_1467<br />or higher (Note 731265)<br />- Oct/06/2004: SAPKGPFB02, SAPKGPFB02 - test import error in SAPKGPFB06<br />- Jul/14/2004: Termination of the \"Move Nametab\" step (Batch-Job<br />RDDMNTAB) using kernels with patch levels of 1532-1542. <B>Do not use</B><br /><B>these kernels to import Support Packages</B><br />- Jul/14/2004: SAPKU40001, SAPKU40004: Test import error with SAPKU40004<br />- Jul/14/2004: SAPKU40003: Error in DDIC activation<br />- Jul/14/2004: Importing Support Packages and of TMS transports hangs<br />after you update the kernel to a patch level &gt;=1509<br />- Jul/14/2004: Ignorable error during DDIC activation of Table T8JBBSEG<br />- Jun/09/2004: SAPKB62040: Syntax error in the \"Records Management<br />Public Sector\"<br />- MAY/20/2004: SAPKB62036 - SAPKB62040: Error on MS SQL Server<br />(Note 738950)<br />- MAY/14/2004: SAPKB62018, SAPKB62019, SAPKB62021, SAPKB62026&#x00A0;&#x00A0;-<br />Interactions with Note 551809 caused serious inconsistencies in the<br />batch system.<br />- APR/01/04: SAPKH47020: \"duplicate key errors\" during the import<br />(IMPORT_PROPER Phase)<br /> MAR/31/04: SAPKB62038: Error during release and export of transport<br />requests<br />- MAR/26/04: SAPKB62038: Import on Informix terminates with rc = 0012;<br />corrected Support Package is available<br />- MAR/09/04: SAPKB62021, SAPKB62035 - error when you create the<br />UMG_TEST_P table on the database<br />- MAR/09/04: SAPKB62036, SAPKB62037 - test import error with SAPKB62037<br />- MAR/09/04: SAPKA62019, SAPKA62036 - test import error with SAPKA62036<br />- JAN/07/04: DDIC activation error due to sequencing problems with the<br />dependency calculation<br />- JAN/07/04: Problems with lock objects after you import SAPKB62032<br />(generated ENQUEUE_ and DEQUEUE_ function modules are syntactically<br />incorrect)<br />- JAN/07/04: SAPKGPPA11 - SAPKGPPA16: Errors when you execute the<br />after-import methods (XPRA_EXECUTION phase)<br />- NOV/25/03: Error due to missing codepage segments after an upgrade<br />(including runtime error IMPORT_INIT_CONVERSION_FAILED)<br />- NOV/25/03: Error in the Support Package Manager after an upgrade from<br />R3E 4.70 Ext.1.10 to R3E 4.70 Ext.2.00<br />- NOV/25/03: Non-correctable activation errors when you restart DDIC<br />activation<br />- NOV/25/03: SAPKB62018: Error during DDIC activation (STPRSOKEY table<br />type)<br />- NOV/25/03: CONNE_IMPORT_WRONG_COMP_TYPE runtime error in the<br />AUTO_MOD_SPAU phase or when you call the modification adjustment (SPAU)<br />- SEP/24/03: SAPKU40001, SAPKU40002: Error during DDIC activation<br />- SEP/24/03: Problems with supposedly obsolete notes in the modification<br />adjustment (Note 640609)<br />- SEP/24/03: SAPKE47019: Import with SPAM/SAINT - Version 0014<br />- JUL/25/03: SAPKB62026: Problems when you import in a queue with other<br />Basis Support Packages<br />- JUL/25/03: SAPKB62026: Error while distributing customizing between<br />UNICODE systems (note 639970)<br />- JUL/25/03: SAPKE47015: Potential DDIC activation errors with<br />P05_SIM_PAR structure<br />- JUL/25/03: SAPKH47011: Potential generation errors during the import<br />- JUN/02/03: SAPKA62019: Error during DDIC activation when importing<br />into an EBP/CRM 4.00 System<br />- MAY/28/03: SAPKH47005, SAPKGPPA07: DDIC activation error for the FM01D<br />structure<br />- MAY/19/03: Install PI_BASIS 2002_2_620 or higher BEFORE importing the<br />EBP/CRM Support Packages SAPKU31004<br />- APR/25/03: SAPKB62020: Changed system response of an R/3 Enterprise<br />system<br />- APR/22/03: SAPKB62014: Termination of the Support Package Manager in<br />the SPDD_SPAU_CHECK phase (after the upgrade to R/3 Enterprise 4.70)<br />- APR/17/03: SAPKH47007: Error during DDIC activation (H_TMFK, V_TB2BU<br />and V_TB2BT views)<br />- APR/17/03: SAPKB62022: Deleting temporary variants during periodic<br />background jobs<br />- APR/17/03: Incorrect display in SPAU and SPDD when using Oracle 8.1.7<br />(Note 604377)<br />- MAR/21/03: Database system DB6: Import error with \"Activation of<br />inactive runtime objects\" (tp step \"6\") after importing Basis Support<br />Package SAPKB62019 (Note 605752)<br />- MAR/21/03: Texts deleted after you import Support Packages Note 596982<br />- MAR/13/03: SAPKA62013, SAPKA62019 - Test import error with SAPKA62019<br />- MAR/13/03: SAPKB62019: Incorrect delivery - Activation error during<br />the DDIC_ACTIVATION phase<br />- FEB/14/03: SAPKH47006: Update termination and incorrect statistics<br />after change in sales order (Notes 578797, 621605)<br />- FEB/14/03: Import error with SAPKA62002<br />- FEB/14/03: SAPKB62009, SAPKB62018 - Test import error with SAPKB62018.<br />- JAN/22/03: SAPKH47002, SAPKH47005 - Test import error with SAPKH47005<br />- JAN/22/03: SAPKGPHA05, SAPKGPHA09 - Test import error with SAPKGPHA09<br />- JAN/21/03: SAPKB62016, SAPKB62017 - Import error with SAPKB62016<br />- JAN/03/03: SAPKB62016: Necessary postprocessing if the SAP DB or<br />liveCache is used (Notes 545030, 588515)<br />- JAN/02/03: SAPKH47003: <B>Important:</B> Refer to note 578964 if using<br />the Material Ledger (CO Module)<br />- NOV/19/02: Serious error in DDIC mass activation program - <B>it is</B><br /><B>essential that you refer to note 568454.</B><br />- NOV/15/02: DDIC_TYPE_INCONSISTENCY runtime error after you import<br />Basis Support Package SAPKB62012 into a CRM 3.1 system or an EBP 3.5 sys<br />system<br />- NOV/15/02: Problems when continuing the import after return code 0006<br />in the XPRA_EXECUTION step<br />- NOV/06/02: SPAM/SAINT Version 0003 - Version 0008: potential problems<br />with implemented user exits<br />-&#x00A0;&#x00A0;OCT/18/02: Serious error in the DDIC activation when you import<br />EA-APPL Support Packages SAPKGPAA01 and SAPKGPAA02 in the same queue<br />- OCT/18/02: Application error when you use the Enterprise Extension<br />EA-FINSERV at Support Package level 0000<br />- OCT/18/02: Problems with interest rate instruments (product type 550)<br />with interest capitalization (without EA-FINSERV Support Package<br />SAPKGPF04)<br />- OCT/04/02: SAPKB62006, SAPKB62010 - Test import error with SAPKB62010<br />- SEP/18/02: Error in the attributes of HR Support Package SAPKE47002<br />- JUL/30/02: Termination in the XPRA_EXECUTION step (TP_FAILURE)<br />- APR/01/02: New note created<br /><br /><br /><B>SAP recommendation</B><br />We strongly recommend that you always use the latest version of the SPAM<br />/SAINT update before importing any other support packages.<br />We also recommend that you download the latest versions of the tp and<br />R3trans from the SAP Service Marketplace (see Note 19466).<br /><br /><B>V3 update entries:</B> Process your V3 update entries before<br />importing Support Packages. If support packages make changes in the<br />interface structure of the V3-Update module, you might not be able to<br />process your V3 update entries.<br /><br />If you are working with several application servers, make sure that the<br /><B>rdisp/bufrefmode</B> profile parameter is set to<br /><B>sendon,exeauto</B>. If you are working with only one application<br />server, set it to <B>sendoff,exeauto</B>. The TPPARAM Parameter<br /><B>buffreset</B> should be set to <B>TRUE</B>. Only execute the \"/$sync\"<br />command to put the support package changes into effect.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is generally caused by program errors or by the Support<br />Package type.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. SPAM/SAINT updates</OL> <UL><LI>Symptom: Different runtime errors may occur when you import the<br />SPAM/SAINT update:</LI></UL> <UL><UL><LI>LOAD_PROGRAM_LOST</LI></UL></UL> <UL><UL><LI>LOAD_TEXTPOOL_LOST</LI></UL></UL> <UL><UL><LI>SAPSQL_SELECT_WA_TOO_SMALL</LI></UL></UL> <UL><UL><LI>SAPSQL_SELECT_TAB_TOO_SMALL</LI></UL></UL> <UL><UL><LI>SAPSQL_WA_TOO_SMALL</LI></UL></UL> <UL><UL><LI>DDIC_STRUCLENG_INCONSISTENT</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These errors are caused when the import changes the source code of the<br />Support Package Manager at runtime.<br />LOAD_PROGRAM_LOST and LOAD_TEXTPOOL_LOST occur if the ABAP Load or text<br />elements should be reloaded to the local buffer, but another version is<br />stored in the database.<br />On the other hand, SAPSQL_SELECT_WA_TOO_SMALL,<br />SAPSQUL_SELECT_TAB_TOO_SMALL, SAPSQL_WA_TOO_SMALL and<br />DDIC_STRUCLENG_INCONSISTENT occur if changes are made to SPAM/SAINT<br />data structures during the SPAM/SAINT update.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: These errors do not recur if you restart transaction SPAM and<br />continue importing the SPAM/SAINT update.</p> <UL><LI> <B>SPAM/SAINT - Version 0003 - Version 0008</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import an installation queue, user exits are<br />deactivated. Only user exits contained in inactive CMOD enhancement<br />projects are affected.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: With SPAM/SAINT Version 0003, an automatic import postprocess<br />was implemented that may automatically reactivate any user exits that<br />were deactivated when imported (see Note 40972).<br />Since SAP does not support active user exits in inactive CMOD<br />enhancement projects, this situation was not taken into account in the<br />first version of this postprocess. This meant that all user exits in<br />inactive CMOD enhancement projects were deactivated when customer<br />enhancements were restored.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: If the scenario described above applies in your system (active<br />user exits in inactive CMOD enhancement projects), check if the<br />implementation of the user exits can be returned to the SAP standard<br />(active user exits only in active CMOD enhancement projects and inactive<br />user exits only in inactive CMOD enhancement projects).<br />The import postprocess was enhanced in SPAM/SAINT - Version 0009 so that<br />all combinations of user exit status and enhancement project are handled<br />properly.</p> <UL><LI> <B>SPAM/SAINT - Version 0009</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If an error with return code 0006 occurs while the after-import<br />methods are processed in the XPRA_EXECUTION step, you cannot continue<br />with the import, even when the error has been dealt with.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: The check for the return code in the XPRA_EXECUTION step is too<br />strict.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: This problem is eliminated in SPAM/SAINT Version 0010 (and<br />higher). Use the solution described in Note 572266 until Version 0010 is<br />available.<br /><B>Caution:</B> Return code 0006 represents a serious warning. Therefore<br />always look for a solution to the problem, instead of simply continuing<br />the import. You can find specific error messages in the After-import<br />methods execution log, and use these messages to search for relevant<br />notes or to obtain the help of SAP Support.</p> <UL><LI> <B>SPAM/SAINT - any version</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After an upgrade from R/3 Enterprise 470 Extension Set 1.10 to<br />Extension Set 2.00, when you attempt to import support packages, you<br />you receive conflict messages between installed add-ons and the support<br />packages to be installed. These messages are incorrect.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: The already-installed version of SPAM/SAINT was recovered during<br />the upgrade from R/3 Enterprise 470 Extension Set 1.10 to Extension<br />Set 2.00. There is an error in the upgrade procedure, such that the<br />content of several configuration tables is copied incorrectly. This<br />causes the system to report nonexistent conflicts.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Check whether a new SPAM/SAINT version already exists in the<br />SAP Service Marketplace. If so, import the new version; if not, you need<br />to re-import your current SPAM/SAINT version. Note 684564 contains<br />further information this procedure.<br /><br /></p> <OL>2. <B>All Support Packages</B></OL> <UL><LI> <B>Error in the DDIC mass activation program</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: Importing queues with <B>several</B> Support Packages results<br />in activation errors that can only be corrected with the help of SAP<br />Support.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Implement the correction described in note 568454 as soon as<br />possible. Always do this before you import more Support Packages!<br />It is also possible to import Basis Support Package SAPKB62011 to fix<br />the problem. Note that importing a queue of <B>Several</B> Basis support<br />packages (including SAPKB62011) could cause problems. This is because<br />the DDIC Activation Program correction in SAPKB62011 is not imported<br />until after the DDIC activation Step.</p> <UL><LI> <B>Activation error when you restart DDIC activation</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import a queue of several Support Packages you receive<br />invalid error messages while repeating DDIC activation. These error<br />messages cannot be eliminated.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Implement the correction described in note 668163 as soon as<br />possible. You must do this before you import more support package<br />queues!<br />It is also possible to import Basis Support Package SAPKB62031 to fix<br />the problem. Note that importing a queue of <B>several</B> Basis support<br />packages (including SAPKB62031) could lead to further problems. This is<br />because the corrections to the DDIC Activation Program contained in<br />SAPKB62031 are not imported until after the DDIC activation Step.</p> <UL><LI> <B>DDIC activation errors because of sequence problems</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import a queue with several Support Packages error<br />messages similar to the following appear during DDIC activation:<br />&#x00A0;&#x00A0;Activate table &lt;tab name&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Field &lt;field name&gt;: Table type &lt;type name&gt; is not active<br />&#x00A0;&#x00A0; Table &lt;tab name&gt; was not activated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A typical feature of this error is that the error messages do not<br />represent actual inconsistencies, and can be eliminated by repeating the<br />DDIC activation phase, allowing you to successfully complete DDIC<br />activation.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To avoid this type of error, follow the instructions in<br />Note 684750, or import Basis Support Package SAPKB62035.<br />If you have already encountered this error, DDIC activation will be<br />successful when you repeat it several times. If repeated execution of<br />the DDIC activation does not work, then you need to investigate the<br />reported errors more carefully, as it is likely that a genuine<br />inconsistency is the problem.</p> <UL><LI> <B>Termination in the XPRA_EXECUTION step (TP_FAILURE)</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: A termination occurs when you import one or more support<br />packages. The following dialog box is displayed:<br /><br />Error in step: XPRA_EXECUTION<br />Cause of error: TP_FAILURE<br />Return code: 0204<br />Error message: parameter is missing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: If you encounter this error, proceed as described in<br />Note 537881.</p> <UL><LI> <B>Texts deleted by Support Package</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: Texts disappear due to support package imports. After you<br />import support packages, texts are missing or are displayed in the<br />original language (usually German or English). This affects texts that<br />were translated by the customer, or that were filled in with another<br />language (Language Supplementation).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause and Solution: Note 596982 describes the causes of this phenomenon<br />and provides information on how to alleviate or solve the problem.</p> <UL><LI> <B>Incorrect display in transactions SPAU and SPDD</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: In transactions SPAU or SPDD objects that were not deleted are<br />incorrectly displayed under deleted objects. This problem only occurs<br />when the SAP system is running on an Oracle 8.1.7 database.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Proceed as described in Note 604377 and import the Oracle<br />patch specified there.</p> <UL><LI> <B>SPAU incorrectly displays note corrections as obsolete</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: You import Support Packages and execute the modification<br />adjustment in transaction SPAU. Under the section: \"Note Corrections\"<br />notes are displayed as obsolete (gray traffic light symbol), even though<br />they remain valid for your system and still need to be implemented. When<br />you click upon the gray traffic light symbol, the note corrections are<br />returned to the default condition and are no longer found in your<br />system.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Proceed as described in Note 640609.</p> <UL><LI> <B>CONNE_IMPORT_WRONG_COMP_TYPE runtime error in SPAU/SPDD</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: The CONNE_IMPORT_WRONG_COMP_TYPE runtime error occurs in the<br />AUTO_MOD_SPAU import phase or even when you call the modification<br />adjustment.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Proceed as described in Note 603173.</p> <UL><LI><B>IMPORT_INIT_CONVERSION_FAILED runtime error during queue import</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: Importing a support package queue terminates with an error<br />(error code 0012) in the after-import method execution. In the error log<br />it states that Background Job RDDEXECL terminated because of the runtime<br />error IMPORT_INIT_CONVERSION_FAILED.<br />This symptom has been observed with Basis Support Packages SAPKB62030<br />and SAPKB62031.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause and Solution: Note 676135 describes the cause and solution of this<br />problem.</p> <UL><LI><B>Support Package import hangs with kernel-patch-level &gt;=1509</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you update the kernel to a patch level &gt;=1509, support<br />package and TMS imports of user-defined requests hang. Transport steps<br />that should run as background jobs (DDIC Activation, for example) do not<br />start, instead error message EH1 is recorded in the system log.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Note 745468 describes the solution of the problem. Load the<br />newest version of the SAPEVT program (at least Version 3) from the SAP<br />Service Marketplace, replacing the older version. Imports will then work<br />normally.</p> <UL><LI><B>Termination of the \"Move Nametab\" step (Batch-Job RDDMNTAB)</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: The import steps \"Move Nametab\" or \"Activation of Inactive<br />Runtime Object\" terminate (in the IMPORT_PROPER) phase with Returncode<br />0012 when you import support packages if you have a kernel with a<br />support package level of 1532-1542. This is a fatal error when importing<br />Basis support packages, because at this point the system is in an<br />inconsistent state.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: <B>Do not use kernels with patch levels of 1532-1542 to</B><br /><B>import support packages. Install a kernel with a patch level of 1559 or</B><br /><B>higher!</B><br />If the corrected kernel is not available, you can avoid the error by<br />setting the parameter <B>MAB=0</B> in the transport tool configuration<br />of the Transport Management System (Transaction STMS) (see also<br />Note 753627)<br />If you encountered this error while importing a Basis support package,<br />then the problem can only be resolved with the assistance of SAP Support<br />Open a problem message on the BC-UPG-OCS component.<br />If encountered while importing other support packages, the problem is<br />less serious. You can continue the import in the Support Package<br />Manager without further difficulties.</p> <UL><LI><B>ORA-02168 on Oracle systems with 'Automatic Segment Space Management</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When importing Support Packages, the process terminates at the<br />This problem only occurs on Oracle systems on which 'Automatic Segment S<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To avoid this error, implement the corrections described in No<br />If the error has already occurred, you must also correct the incorrect t Sections 2.a) and 2.b) of this note.<br /></p> <OL>3. <B>Basis Support Packages (SAP BASIS components)</B></OL> <UL><LI> <B>SAPKB62006 - SAPKB62010</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If Basis Support Packages SAPKB62006 and SAPKB62010 are<br />imported together in a queue, an error occurs with Support Package<br />SAPKB62010 in the TEST_IMPORT step. The SAPKB62010 test import log<br />records the following error message:<br /> Function UMG_READ_LANG_INFO (SPUMG 18) does not fit into the existing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;function group ((SUMG 03))<br /> Transport the entire function group.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by selecting \"Extras\"-&gt;\"Ignore test<br />Import Errors\". The error will not return during later imports.</p> <UL><LI> <B>SAPKB62012</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB62012 into a<br />CRM 3.1 system or an EBP 3.5 system, DDIC_TYPE_INCONSISTENCY runtime<br />errors occur. The following error messages appear:<br />Inconsistency in the dictionary for the BAD_ADDRESS structure.<br />Inconsistency in the dictionary for the REQADDRESS structure.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: The Basis support package extends a DDIC structure used in the<br />BAD_ADDRESS and REQADDRESS tables. As a result, these tables become to<br />long and cannot be completely activated.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Proceed as described in Notes 570660, 570683 and 572041.</p> <UL><LI> <B>SAPKB62014</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB62014 into an R/3<br />Enterprise 4.70 system, the Support Package Manager terminates in the<br />SPDD_SPAU_CHECK phase with the following error message:<br />Runtime error CALL_FUNCTION_NOT_FOUND<br />&#x00A0;&#x00A0; Function module \"UPG_NAMETAB_GET\" not found.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: This problem only occurs after an upgrade to R/3 Enterprise 4.70,<br />when the upgrade includes the correction FIX_R3E470.SAR but not the<br />Basis Support Package SAPKB62014.<br />Importing Support Package SAPKB62014 after the upgrade partially resets<br />the upgrade correction and so causes these inconsistencies in the<br />Version Management.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: There are two ways to avoid the problem:</p> <UL><UL><LI>During the upgrade: Import Basis Support Packages up to at least<br />SAPKB62014 (or use the R/3 Enterprise Source Release 1 upgrade kit),</LI></UL></UL> <UL><UL><LI>After the upgrade: Import Basis Support Packages SAPKB62014 and<br />SAPKB62022 together in a queue. Basis Support Package SAPKB62022<br />contains the complete Version Management correction.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the error has already occurred, follow the workaround described in<br />Note 522711 and import the Y6AK029165 transport. Import Basis Support<br />Package SAPKB62022 as soon as possible.</p> <UL><LI> <B>SAPKB62016</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you are using an SAP DB or the liveCache, you need to perform some<br />rework in the CCMS Alert Monitoring (RZ20) and Database Management Tools<br />(DB13, DB50) after importing Basis Support Package SAPKB62016. Refer to<br />Notes 545030 and 588515 for instructions on how to proceed.</p> <UL><LI> <B>SAPKB62016 - SAPKB62017</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import a queue with Basis Support Packages SAPKB62016<br />and SAPKB62017, the import terminates with an error during the import of<br />SAPKB62016. The following error messages are displayed in the Import Log<br />for SAPKB62016:<br />&#x00A0;&#x00A0;key field SSPIPD_TA-LANGU missing, key is truncated.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: A version of Support Package SAPKB62017 (EPS filename:<br />CSN0120061532_0014260.PAT) was briefly available, which deleted the key<br />field LANGU of the SSPIPD_TA table structure. When importing SAPKB62016<br />and SAPKB62017 in a single queue, errors occur during the import of<br />table entries from Support Package SAPKB62016, since they require this<br />key field.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: A new version of the Support Package SAPKB62017 is available<br />(File name EPS: CSN0120061532_0014274.PAT) that no longer contains the<br />deletion of the key field. It is also possible to import Support Package<br />SAPKB62016 in the same queue as this version. If possible, use only this<br />version of Support Package SAPKB62017.<br />If you already imported the old version of SAPKB62017 without any<br />problems (SAPKB62016 was not in the same queue), no further action is<br />needed.<br />If you imported the old version of SAPKB62017 in the same queue as<br />SAPKB62016 and came across this problem, contact SAP Support to resolve<br />the problem.</p> <UL><LI> <B>SAPKB62009 - SAPKB62018</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you import Basis Support Packages SAPKB62009 and SAPKB62018<br />in the same queue, an error occurs in the TEST_IMPORT step of Support<br />Package SAPKB62018. The test import log of SAPKB62018 records the<br />following error message:<br /> Function SRM_VIS_COPY_SPS (SRMREG_SPS 11)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;existing function group ((SRMREGEDIT 06))<br /> Transport the entire function group.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by selecting \"Extras\"-&gt;\"Ignore test<br />import errors.\" The error will not recur during later imports.</p> <UL><LI> <B>SAPKB62001 - SAPKB62018</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: An error occurs in the DDIC_ACTIVATION phase when you import a<br />queue including Basis Support Packages SAPKB62001-SAPKB62018.<br />The following errors appear in the DDIC activation log:<br />&#x00A0;&#x00A0; EDO802XActivation of table type \"STPRSOKEY\"<br />&#x00A0;&#x00A0;EDO837 Table type \"STPRSOKEY\" is a generic type<br />&#x00A0;&#x00A0;EEDO839 Generic table type not allowed (\" \" uses)<br />&#x00A0;&#x00A0;EDO804 Table type \"STPRSOKEY\" was not activated<br />A similar error was also observed for the TTABSTRIPITEM table type when<br />importing a queue of Support Packages SAPKB62002 - SAPKB62027.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: Errors in the DDIC activation program.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Note 640462 describes the cause and solution of the problem.<br />To avoid the problem, Basis Support Packages SAPKB62001 and SAPKB62002<br />should not be imported together with SAPKB62018 and higher in a queue<br />(see Recommended Queues).</p> <UL><LI> <B>SAPKB62019</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problem: There are serious errors in the first version of Basis Support<br />Package SAPKB62019 (EPS filename: CSN0120061532_0014715.PAT), which was<br />available for a brief period.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: The Support Package contains objects from ABA Support Packages<br />that cause DDIC activation errors when you import it without first<br />having imported ABA Support Packages SAPKA62001 - SAPKA62019.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: <B>Do not use the incorrect version of the Basis Support</B><br /><B>Package.</B> You should always use the corrected Basis Support Package<br />SAPKB62019 (EPS filename: CSN0120061532_0014761.PAT). This support<br />package has been available since March 13, 2003.<br />If you have already imported the incorrect Basis Support Package and<br />have encountered DDIC activation errors, contact SAP Support,<br />(Component: BC-UPG-OCS).</p> <UL><LI> <B>SAPKB62019</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB62019, errors occur<br />in the database during the tp step \"Activating inactive runtime objects\"<br />(tp step \"6\") while importing transports (customer transports and<br />support packages). The import log P &lt; YY &gt;&#x00A0;&#x00A0;&lt; MM &gt; &lt; DD &gt;. &lt; SID &gt;<br />records the following error messages.<br />Error -104-dsql_db6_exec_immediate( SQLExecDirect ):<br />[IBM][CLI Driver][DB2/AIX64] SQL0104N An unexpected token \"ALLOW REVERSE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SCANS\" was found following ...<br />Expected tokens may include: \"&lt;space&gt;\". SQLSTATE=42601<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Support Package SAPKB62020 corrects the error. <B>To avoid</B><br /><B>the problem, import the Basis Support Packages SAPKB62019 and SAPKB62020</B><br /><B>in the same queue.&lt;Z/&gt;</B><br /><B>If the error described has already occurred, implement the solution</B><br /><B>described in Note 605752. Then import Basis Support Package SAPKB62020</B><br /><B>as quickly as possible.</B></p> <UL><LI> <B>SAPKB62020</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB62020, you may<br />experience a change in the system response at different points in an R/3<br />Enterprise System. This problem can be seen in the sudden appearance<br />of screen elements, for example, or in changes in the process flow of<br />transactions. In extreme cases, it can also cause data corruption.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: Support Package SAPKB62020 included an incomplete correction to<br />BAdI processing. This caused the execution of BAdI implementation of<br />deactivated Enterprise Extensions.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: See Note 601742. Import Basis Support Package SAPKB62023<br />as soon as possible (planned release date is May 06, 2003) or implement<br />the additional correction instructions from Note 601742.</p> <UL><LI> <B>SAPKB62022</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB62022, periodic<br />jobs, which use a temporary variant that is created for the first run of<br />the job, terminate.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Implement the corrections described in Note 615168 or import<br />Basis Support Package SAPKB62023 as soon as possible.</p> <UL><LI> <B>SAPKB62026</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Basis Support Package SAPKB62026 in a queue<br />with other Basis support packages the import terminates in the<br />IMPORT_PROPER phase in the \"Activation of inactive runtime objects\"<br />step. This error has been frequently observed in the past. <br />&#x00A0;&#x00A0;Error message in the queue status Window:<br />&#x00A0;&#x00A0; OCS Package ALL, tp-Step \"6\", Return-Code 0008<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Afterwards, the background processing system is in an inconsistent state<br />such that it is not possible to continue the import.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To avoid this problem, we recommend that you import the Basis<br />Support Package SAPKB62026 individually.<br />If you have already encountered the import error and restarting the<br />import does not work, open a problem message on the BC-UPG-OCS<br />component. The problem can only be dealt with by SAP Support.</p> <UL><LI> <B>SAPKB62026</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB62026, errors occur<br />on UNICODE systems during customizing comparison and distribution<br />between two systems (see Note 639970).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Import Basis Support Package SAPKB62027 or implement the<br />correction contained in note 639970.</p> <UL><LI><B>SAPKB62018, SAPKB62019, SAPKB62021, SAPKB62026</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you import Support Packages SAPKB62018, SAPKB62019,<br />SAPKB62021, SAPKB62026 into a system where the correction from Note<br />551809 has been implemented, the batch system is in an inconsistent<br />state after the main import (IMPORT_PROPER Phase). You can no longer<br />plan background jobs, and the import process itself is affected by the<br />problem.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: The correction from Note 551809 concerns the batch system. When<br />you import any of the support packages mentioned above this correction<br />is partially overwritten. As a result, the function group BTCH becomes<br />syntactically incorrect.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: If you installed Note 551809, remove the correction before you<br />import Support Packages SAPKB62018, SAPKB62019, SAPBK62021, SAPBK62026 A<br />is still valid. (Note 551809 is valid up to SAPKB62028).</p> <UL><LI><B>SAPKB62027 and higher</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: In systems on the MS SQL server, the main import may terminate<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Use a DbSL (dbmssslib. dll) with patch level lib_dbsl_1467 or h</p> <UL><LI><B>SAPKB62032</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: Problems occur with lock management during normal operation<br />after you import Basis Support Package SAPKB62032. Syntax errors are<br />reported when requesting and releasing locks.<br />SYNTAX_ERROR: Syntax error in the \"/1BCDWBEN/SAPLSENxxxx\" program<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This potentially affects all lock objects (R3TR ENQU objects in the<br />support package object lists) that were imported with the support<br />package queue that included SAPKB62032.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Apply the solution described in note 692155 for the problem.<br />To minimize the effects of the problem, you should import Basis Support<br />Package SAPKB62032 individually. Since it contains only one lock object<br />(R3TR ENQU E_DXMAP), you can avoid problems by activating E_DXMAP in<br />transaction SE11 immediately after importing the support package.</p> <UL><LI> <B>SAPKB62021 - SAPKB62035</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you import the Basis Support Packages SAPKB62021 and<br />SAPKB62035 together in a queue, an error occurs in the Table UMG_TEST_P<br />in the database during the request-independent transport step<br />\"Activation of inactive runtime objects\". The log records the following:<br />2EEGT236 The SQL statement was not executed<br />2EEGT074 Statements for \"SQLT table\" \"UMG_TEST_P\" could not be executed<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The UMG_TEST_C table and UMG_TEST_F table are also affected by the<br />error.<br />You will find details on this problem in Note 603575.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Do not import the two Support Packages SAPKB62021 and<br />SAPKB62035 together in a queue. Refer to the split points below.<br />If you have already encountered the problem, proceed as described in the<br />Solution part of Note 603575.</p> <UL><LI> <B>SAPKB62036 - SAPKB62037</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When Basis Support Packages SAPKB62036 and SAPKB62037 are<br />imported together in a queue, an error occurs in the TEST_IMPORT step of<br />SAPKB62037. The test import log for SAPKB62037 records the following<br />error message:<br /> Function TWB_F4_OUTLINE (S_TWB_H 14) does not fit into the existing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;function group ((STWB 05))<br /> Function TWB_SEPARATE_TESTCASE_NAME (S_TWB_A 10) does not fit into the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;existing function group ((STWB08))<br /> Transport the entire function group.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by selecting \"Extras\"-&gt;\"Ignore test<br />import errors\", and the error will not appear in subsequent imports.</p> <UL><LI> <B>SAPKB62038</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Basis Support Package SAPKB62038, the import<br />terminates in the IMPORT_PROPER phase with return code 0012:<br />sap_dext called with msgnr 1:<br />db call info<br />function:&#x00A0;&#x00A0;db_dynpro_interface<br />fcode:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DI_UPDATE<br />tabname:&#x00A0;&#x00A0;&#x00A0;&#x00A0;SHEADER<br />len:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;44<br />key:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RSDBBUFF 0140<br />retcode:&#x00A0;&#x00A0;&#x00A0;&#x00A0;1<br />SQL-Fehler -391 accessing : INF-391: Cannot insert a null into column<br />(d020s.cuan)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This error is based on an incorrect delivery of the Support Package and<br />has to date only been observed on the INFORMIX database.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: The incorrect Support Package (EPS filename:<br />CSN0120061532_0018003.PAT) was withdrawn on March 26, 2004 and a<br />corrected version, Support Package SAPKB62038 (EPS filename:<br />CSN0120061532_0018125.PAT) was made available. If you loaded Support<br />Package SAPKB62038 from the SAP Service Marketplace before March 26,<br />2004, replace it with the corrected version before importing.<br />If you have already encountered the error, contact SAP Support<br />(Component BC-UPG-OCS).</p> <UL><LI> <B>SAPKB62038</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Support Package SAPKB62038, transport requests<br />can no longer be released. The export terminates with the following<br />message in the export log:<br />===&gt; HALT: GetDBMigrateCodePagesLangs fails with rc=128<br /> (twconv.c:888) Please contact the SAP support.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Proceed as described in Note 722638.</p> <UL><LI><B>SAPKB62036 - SAPKB62040</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If Basis Support Packages SAPKB62036 and SAPKB62040 are<br />imported in the same queue, an error arises in the \"Method execution\"<br />phase in Microsoft SQL Server only:<br />SQL script sap_mon_sqlsetup&#x00A0;&#x00A0; not found<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Apply the correction from Note 738950, and repeat the import.<br />You can apply this correction preventively, to ensure that this error<br />does not occur at all.</p> <UL><LI><B>SAPKB62040</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Basis Support Package SAPKB62040, syntax<br />errors can occur in \"Records management public sector\" applications:<br /> Syntax error in CL_IM_RMPS_VISUALIZATION.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Formal parameter IM_FCODE does not exist.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Note 740325 describes this problem. Apply the associated<br />corrections or import Basis Support Package SAPKB62041.</p> <UL><LI><B>SAPKB62043</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After importing the Basis Support Packages SAPKB62043, test pla triggered or you cannot select the 'System role' and 'Transfer tr fields.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Implement the corrections in Note 769465 or the Basis Support<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br />Based on the previous remarks, we recommend the following for Basis<br />Support Package queues:<br />SAPKB62001 - SAPKB62013<br />SAPKB62014 - SAPKB62025<br />SAPKB62026<br />SAPKB62027 - SAPKB62031<br />SAPKB62032<br />SAPKB62033 - SAPKB620.. (highest available Basis Support Package)<br /><br />You can define shorter queues but you must pay attention to the split<br />points.<br />These recommended queues only apply if you have not installed any<br />add-ons or do not need to include an CRTs in the queue. Otherwise, you<br />will need advice from the producer of the add-ons or CRTs as to optimal<br />or permitted queues.<br /> <OL>4. <B>ABA Support Packages (SAP_ABA component)</B></OL> <UL><LI> <B>SAPKA62002</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import ABA Support Package SAPKA62002, the main import<br />(the IMPORT_PROPER import phase) terminates with return code 0008. You<br />find the following error message in the import log:<br />&#x00A0;&#x00A0; Table SWD_PROFILE does not exist in Nametab<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: The SWD_PROFILE table is only delivered with Basis Support<br />Package SAPKB62001. Therefore, the error only occurs if you import ABA<br />support packages without first importing Basis support packages.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Import at least Basis Support Package SAPKB62001 before you<br />import the ABA support packages. This prevents the described error from<br />occuring.<br />If you have already encountered the error, contact SAP Support<br />(Component BC-UPG-OCS).</p> <UL><LI> <B>SAPKA62013 - SAPKA62019</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you import ABA Support Packages SAPKA62013 and SAPKA62019<br />together in a queue, an error occurs with Support Package SAPKB62019 in<br />the TEST_IMPORT step. The SAPKA62019 test import log records the<br />following error message:<br /> Function EFG_FORM_AFTER_IMPORT_METHOD (EFG_FORM_TR_AND_CC 02) does not<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;fit into the existing function group.<br /> Transport the entire function group.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by selecting \"Extras\"-&gt;\"Ignore test<br />import errors\". The error will not recur during subsequent imports.</p> <UL><LI> <B>SAPKA62019</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import ABA Support Package SAPKA62019 into an<br />EBP/CRM 4.00 system, an error occurs in the DDIC_Activation phase. The<br />activation log records the following:<br />&#x00A0;&#x00A0;Activate dependent table type MDM_BUS_EI_EXTERN_FS_TAB<br />&#x00A0;&#x00A0;User type MDM_BUS_EI_EXTERN_FS is a generated proxy type<br />&#x00A0;&#x00A0;Table type MDM_BUS_EI_EXTERN_FS_TAB was not activated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: Due to a delivery error in the EBP/CRM 4.00, the proxy indicator<br />is missing for some table types, which can lead to activation errors.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: The delivery error is eliminated with ABA Support Package<br />SAPKA62024. Import ABA Support Packages SAPKA62019 and SAPKA62024<br />together in a queue.<br />If you have already encountered the activation error, contact SAP<br />Support (BC-DWB-DIC-AC component) to solve the problem.</p> <UL><LI> <B>SAPKA62019 - SAPKA62036</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If the ABA Support Packages SAPKA62019 and SAPKA62036 are<br />imported together in a queue, an error occurs with Support Package<br />SAPKA62036 in the TEST_IMPORT step. You find the following message in<br />the test import log of SAPKA62036:<br /> Function BUPA_CENTRAL_CI_CHANGE (BUBA_4 87) does not fit into the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;existing function group ((BUBA_5 05))<br /> Transport the entire function group.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by selecting \"Extras\" -&gt; \"Ignore tes imports.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br />Based on the previous remarks, we recommend the following for ABA<br />support package queues:<br />SAPKA62001 - SAPKA620.. (highest available ABA Support Package).<br /><br />You can define shorter queues but you must pay attention to the split<br />points. <B>Also note that you must import at least Basis Support</B><br /><B>Package SAPKB62001 before you import any ABA support packages.</B><br />These recommended queues only apply if you have not installed any<br />add-ons or do not need to include any CRTs in the queue. Otherwise, you<br />will need advice from the producer of the add-ons or CRTs as to optimal<br />or permitted queues.<br /> <OL>5. <B>EBP/CRM 3.10 Support Packages (component BBPCRM 310)</B></OL> <UL><LI> <B>SAPKU31004</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: To function correctly, the EBP/CRM Support Package SAPKU31004<br />requires at least the PI_BASIS 2002_2_620 plug-in. If this plug-in is<br />not present, this will be detected during the queue calculation.<br />Due to an error in the Support Package Manager up to Version 0011, the<br />upgrade package for PI_BASIS is placed in the support package queue<br />instead of an error message being generated. This queue is imported<br />without errors, but the release of PI_BASIS is not adjusted because the<br />relevant control tables for importing a support package queue have not<br />been adjusted.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: This error is eliminated in the Support Package Manager<br />Version 0012. Before you import Support Package SAPKU31004, make sure<br />that you use Version 0012 or higher, of the Support Package Manager,<br />or make sure that the PI_BASIS plug in has been installed with release<br />2002_2_620 or higher.<br />If you have already encountered the error described here and you have<br />imported the upgrade package for PI_BASIS together with the support<br />package, open a report on the BC-UPG-OCS component.</p> <OL>6. <B>EBP/CRM 4.00 Support Packages (BBPCRM 400 component)</B></OL> <UL><LI> <B>SAPKU40001 - SAPKU40002</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import the EBP/CRM Support Packages SAPKU40001 and<br />SAPKU40002 in a queue, an error occurs in the DDIC_ACTIVATION phase. The<br />action log records that:<br />Table CRMT_BSP_LAM_ACI_3000 could not be activated<br />Table CRMT_BSP_LAM_ACI_4000 could not be activated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: The table structures are delivered in Support Package SAPKU40001,<br />and deleted in Support Package SAPKU40002. The DDIC Activation Program<br />cannot always handle this situation correctly.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To avoid the error, both Support Packages should be imported<br />separately.<br />If you have already encountered this error, contact SAP Support.</p> <UL><LI><B>SAPKU40001 - SAPKU40004</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import support packages SAPKU40001 and SAPKU40004, the<br />TEST_IMPORT step returns an error for Support Package SAPKU40004.<br />The Test Import Log for SAPKU40004 records the following error message:<br />\"The Function COM_PRODUCT_MAP_EAN_TO_GTIN (COM_PRODUCT_MAT_R3_ADAPTER)<br />does not belong in the available function group (COM_COMM_PR_GTIN)<br />transport the entire function group.\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by selecting \"Extras\" -&gt; \"Ignore<br />test import errors\". The error will not recur with subsequent imports.</p> <UL><LI><B>SAPKU40003</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Support Package SAPKU40003, the DDIC Activation<br />phase hangs. You receive an error message stating that the data element<br />CRM_ACE_PROC_STATUS cannot be activated, because the domain CRMCHAR1 is<br />not available.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: See the solution from Note 704586.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br />From the preceding remarks we can derive the following recommendations for EBP/CRM 4.00 Support Package queues:<br />SAPKU40001<br />SAPKU40002 - SAPKU400.. (highest available EBP/CRM Support Package)<br /><br />You can define shorter queues but you must pay attention to the split<br />points.<br />These recommended queues only apply if you have not installed any<br />add-ons or do not need to include any CRTs in the queue. Otherwise, you<br />will need advice from the producer of the add-ons or CRTs as to optimal<br />or permitted queues.<br /> <OL>7. <B>BW</B><B> Support Packages (Component </B><B>SAP_BW</B><B>)</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problems that occur when you import BW support packages are described in<br />specific notes. Refer to the following composite SAP Notes and overview:<br />SAP_BW 3.0B: 510838, 532586<br />SAP_BW 3.10: 539867, 545747<br /></p> <OL>8. <B>SCM</B><B> Support Packages (components</B><B>SCM, SCM_BASIS,</B><br /><B>SCM_EXT</B>)</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problems that occur when you import SCM support packages are described<br />in specific notes. The information page of the support package download<br />area for SCM 4.00 in the Service Marketplace contains a list of the<br />specific notes needed for any given support package level.</p> <OL>9. <B>R/3 Support Packages (SAP_APPL component)</B></OL> <UL><LI> <B>SAPKH47003</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you are using the Material Ledger (CO module), it is essential that<br />you read Note 578964, or import R/3 Support Package SAPKH47005. Ideally,<br />you should import SAPKH47003 and SAPKH47005 in a queue together.</p> <UL><LI> <B>SAPKH47002 - SAPKH47005</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you import R/3 Support Packages SAPKH47002 and SAPKH47005<br />together in a queue, this results in an error in the TEST_IMPORT step<br />with Support Package SAPKH47005. The test import log for SAPKH47005<br />records the following error.<br /> Function OPEN_FI_PERFORM_00000930_E (BFFM5|03) does not fit into the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;existing function group ((BFFM3|01))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by selecting \"Extras\"-&gt;\"Ignore test<br />import errors\". The error will not recur with subsequent imports.</p> <UL><LI> <B>SAPKH47005</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: An error occurs in the DDIC_ACTIVATION phase when you import<br />R/3 Support Package SAPKH47005. The activation log records:<br />&#x00A0;&#x00A0;The IFM01D table could not be activated. Field FLG_AK_PAYM_FICA in the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IFM01D table is duplicated.<br />&#x00A0;&#x00A0;The IFM01D table could not be activated. Field FLG_AK_PAYM_FIAR in the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IFM01D table is duplicated.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Prerequisite: You import R/3 Support Package SAPKH47005 after EA-PS<br />Support Package SAPKGPPA07, but R/3 Support Package SAPKH47007 is not<br />contained in the same queue.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To avoid the problem, you should import EA-PS Support<br />Package SAPKGPPA07 after R/3 Support Package SAPKH47007. Alternatively,<br />import Support Packages SAPKH47005 and SAPKH47007 together in a queue.<br />If the error has already occurred, follow the solution directions from<br />Note 625098.</p> <UL><LI> <B>SAPKH47006</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: After you import Support Package SAPKH47006, duplicate updating<br />of information structures occurs when sales orders are changed in<br />Credit Management (see Notes 578797 and 621605).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Import Support Package SAPKH47007 or the correction<br />instructions specified in note 578797.</p> <UL><LI> <B>SAPKH47007</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: An error occurs in the DDIC_ACTIVATION phase when you import<br />Support Package SAPKH47007. The DDIC activation log records the<br />following error messages:<br />&#x00A0;&#x00A0;Relationship TMFK-TDID for table TTXS does not exist<br />&#x00A0;&#x00A0;Relationship TB2BT-TDID for table TTXS does not exist<br />&#x00A0;&#x00A0; Relationship TB2BU-TDID for table TTXS does not exist<br />&#x00A0;&#x00A0; View H_TMFK could not be activated<br />&#x00A0;&#x00A0; View V_TB2BT could not be activated<br />&#x00A0;&#x00A0; View V_TB2BU could not be activated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: A general solution to the problem is available with R/3<br />Support Package SAPKH47010. To avoid the error, import Support Packages<br />SAPKH47007 and SAPKH47010 together in a queue.<br />If you have already encountered this error, the queue must be reset and<br />extended to include Support Package SAPKH47010. Since the queue cannot<br />be reset from the DDIC_ACTIVATION phase using normal means, this must be<br />resolved by SAP Support.</p> <UL><LI> <B>SAPKH47011</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you activated the ABAP/screen generation when you imported<br />Support Packages, this may result in generation errors when you import<br />Support Package SAPKH47011. The generation log records the following<br />error messages:<br />Program RFFOUS_CTX_FG, Include RFFORI06: Syntax error in row 000750<br />Program RFFOUS_FW_FG, Include RFFORI06: Syntax error in row 000750<br />Program RFFOUS_TC_FG, Include RFFORI06: Syntax error in row 000750<br />Program RFFOUS_TC_FG_PLUS, Include RFFORI06: Syntax error in row 000750<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: The programs in question are from the environment of the<br />Enterprise Extension EA-PS. They are no longer used there, but are only<br />deleted when you import EA-PS Support Package SAPKGPPA08. These<br />generation errors only appear when SAPKGPPA08 has not yet been imported.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Since the programs are not used, you can ignore these<br />generation errors (Menu \"Extras\"-&gt;\"Ignore generation errors\"). You can<br />then continue importing the support package queue.</p> <UL><LI> <B>SAPKH47020</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import Support Package SAPKH47020, it terminates with<br />return code 0008 in the IMPORT_PROPER phase. The import log records the<br />following error message:<br />duplicate key errors during insert into table TABDIRDEVC occurred<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: The cause of this problem is described in Section 29 of<br />Note 88656.<br />Continue with the import of the Support Package. The error no longer<br />occurs during the second attempt and all table entries are imported<br />correctly. There is no loss of data.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br /> Based on the previous remarks, we recommend the following for R/3 Support Package queues:<br />SAPKH47001 - SAPKH470.. (highest available R/3 Support Package)<br /><br />You can define shorter queues but you must pay attention to the split<br />ponts. <B>However, note that SAPKH47007 and SAPKH47010 must be</B><br /><B>imported in a queue together.</B><br />These recommended queues only apply if you have not installed any<br />add-ons or do not need to include any CRTs in the queue. Otherwise, you<br />will need advice from the producer of the add-ons or CRTs as to optimal<br />or permitted queues. <OL>10. <B>R/3 HR Support Packages (SAP_HR component)</B></OL> <UL><LI> <B>SAPKE47002</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: You import HR Support Package SAPKE47002 and, depending on the<br />import method, observe the following symptoms:</p> <UL><UL><LI>You want to include the HR Support Package in an upgrade to R/3<br />Enterprise 4.70. The support package cannot be found in the BIND_PATCH<br />phase of PREPARE, and cannot be included, even though it is present in<br />the EPS Inbox.</LI></UL></UL> <UL><UL><LI>You have R/3 Enterprise Release 4.70 and are using the Support Package<br />Manager to import the Support Package. After the import, in System<br />Status, the Package Level of software components installed for SAP_HR is<br />displayed as level 0001 (instead of level 0002).</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: The first version of Support Package SAPKE47002 was incorrectly<br />attributed. You can identify this version from its EPS file name:<br />CSN0120061532_0013052.PAT.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: There are two ways to solve this problem, depending upon the<br />status of the system:</p> <UL><UL><LI>You have not yet imported Support Package SAPKE47002. Make sure that you<br />downloaded the corrected support package into your system. The corrected<br />EPS file has the following file name: CSN0120061532_0013121.PAT. Delete<br />the package with errors from your EPS Inbox (this is normally found in<br />the $DIR_TRANS/EPS/in Directory). If you are in PREPARE for the upgrade,<br />after exchanging the EPS file, repeat the BIND_PATCH phase.</LI></UL></UL> <UL><UL><LI>You have already imported the Support Package successfully. In this<br />case, no further action is required as the incorrectly displayed package<br />level has no effect on system functionality. All the information needed<br />by the Note Assistant is correct, so there will be no problems including<br />later notes.</LI></UL></UL> <UL><LI> <B>SAPKE47015</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import HR Support Package SAPKE47015, errors may occur<br />in the DDIC_ACTIVATION phase (see Note 620723). The activation log<br />reports:<br />&#x00A0;&#x00A0;Table P05_SIM_PAR could not be activated<br />&#x00A0;&#x00A0;(E- Field ... in table P05_SIM_PAR is specified twice)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To avoid the error, import HR Support Package SAPKE47015 and<br />SAPKE47016 in the same queue.<br />If you have already encountered the error, proceed as described in<br />Note 620723, then continue importing the support package queue.</p> <UL><LI> <B>SAPKE47019</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For importing HR Support Package SAPKE47019, use at least SPAM/SAINT -<br />Version 0014. If you use a lower version you may be warned of false<br />conflicts with Enterprise Extension Add-on EA-HR 200.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br /> Based on the previous remarks, we recommend the following for HR Support Package queues:<br />SAPKE47001 - SAPKE470.. (highest available HR Support Package)<br /><br />You can define shorter queues but you must pay attention to the split<br />points.<br />These recommended queues only apply if you have not installed any<br />add-ons or do not need to include any CRTs in the queue. Otherwise, you<br />will need advice from the producer of add-ons or CRTs as to optimal or<br />permitted queues.<br /> <OL>11. <B>Support Packages for Enterprise Extension EA-APPL 110</B></OL> <UL><LI> <B>SAPKGPAA01 - SAPKGPAA02</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If Support Packages SAPKGPAA01 and SAPKGPAA02 are imported<br />together in a queue, an error occurs in the DDIC_ACTIVATION step. The<br />following error message appears in the queue activation log:<br /> APPEND structure CRWB_PSB_APO_DISORDER_KEY was reassigned from table<br />OBJECT_KEYFIELDS to USROBJECTS.<br />You receive similar error messages for the CRWB_PSB_APO_DISPACK_KEY and<br />CRWB_PSB_APO_RECIPIENT_KEY.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Do not import the two Support Packages SAPKGPAA01 and<br />SAPKGAA02 together in one queue!<br />If you have already encountered the activation error, correct the<br />incorrect append assignment as described in Note 567268. If you have<br />further questions contact SAP Support.</p> <UL><LI><B>SAPKGPAA18</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: Support package SAPKGPAA18 can occasionally terminate in the<br />DDIC_ACTIVATION phase. The activation log records the following:<br />&#x00A0;&#x00A0;Table T8JBBSEG (active version) was deleted.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; (Please check references)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: The DDIC Activation Program carries out unnecessary tests when<br />deleting DDIC objects (see Note 742219). You can avoid this problem in<br />future by importing Basis Support Package SAPKB62041.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore these errors, as they will not arise next time<br />the DDIC Activation Program runs. Continue with the import of the<br />support package queue.<br />To solve the problem in the long term, import Basis Support Package<br />SAPKB62041.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br />Based on previous remarks, we recommend the following for EA-APPL<br />support package queues:<br />SAPKGPAA01<br />SAPKGPAA02 - SAPKGPAA.. (highest available EA_APPL support package)<br /><br />You can define shorter queues but you must pay attention to the split<br />points.<br />These recommended queues only apply if you have not installed any add-on<br />add-ons or do not need to include any CRTs in the queue. Otherwise, you<br />will need advice from the producer of the add-ons or CRTs as to optimal<br />or permitted queues.<br /> <OL>12. <B>Support Packages for Enterprise Extension EA-APPL 200</B></OL> <UL><LI><B>SAPKGPAB08</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import the Support Packages SAPKGPAB08 into a queue wi messages appear:<br />&#x00A0;&#x00A0;LANG DTEL *: Object information could not be imported<br />&#x00A0;&#x00A0;LANG TABL *: Object information could not be imported<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Cause: A faulty version of SAPKGPAB08 was available for a short time the archive.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: <B>Do not use the faulty version of Support Package SAPKGP id</B><br /><B>If you have already encountered the activation error, contact SAP Suppor</B><br /><B></B><br /><B></B></p> <OL>13. <B>Support Packages for Enterprise Extension EA-DFPS 200</B><br /><br /></OL> <OL>14. <B>Support Packages for Enterprise Extension EA-FINSERV 110</B></OL> <UL><LI>Problem: If you have not yet imported any Support Packages for<br />Enterprise Extension EA-FINSERV, the errors described in Note 524372 may<br />occur when you use Corporate Finance Management functions.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: It is essential that you import at least EA-FINSERV Support<br />Package SAPKGPFA01 before commencing productive use of Enterprise<br />Extension EA-FINSERV.</p> <UL><LI> <B>SAPKGPFA04</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problem: If interest rate instruments (product type 550) with interest<br />capitalization are already registered before you import Support Package<br />SAPKGPFA04, then the errors described in Note 563437 may occur.<br />Note 563437 is also relevant if you implement an upgrade with either:<br /> - BANK/CFM 462_10 and a support package lower than 14 or<br /> - BANK/CFM 463_20 and a support package lower than 15.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: Import Support Package SAPKGPFA04 as soon as possible or<br />follow the instructions in Note 563437. The note also describes the<br />post-processing that is necessary after you import Support Package<br />SAPKGPFA04.<br /></p> <OL>15. <B>Support Packages for Enterprise Extension EA-FINSERV 200</B></OL> <UL><LI><B>SAPKGPFB02 - SAPKGPFB06</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If Support Packages SAPKGPFB02 and SAPKGPFB06 are imported together in a queue, an error occurs for Support Package SAPKGPFB06 in the TEST_IMPORT step. In the Test Import log for SAPKGPFB06 the following error message appears:<br /> Function BCA_BUPA_ALIAS_GET_DESCRIPTION (FBB1_ABA|01) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((FBB1|04))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by selecting 'Extras'-&gt; 'Ignore test import error'. The error will not occur again for subsequent imports.<br /></p> <OL>16. <B>Support Packages for Enterprise Extension EA-GLTRADE 110</B><br /><br /></OL> <OL>17. <B>Support Packages for Enterprise Extension EA-GLTRADE 200</B><br /><br /></OL> <OL>18. <B>Support Packages for Enterprise Extension EA-HR 110</B></OL> <UL><LI> <B>SAPKGPHA05 - SAPKGPHA09</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: If you import Support Packages SAPKGPHA05 and SAPKGPHA09 in a<br />queue, this results in an error in the TEST_IMPORT step with Support<br />Package SAPKGPHA09. In the SAPKGPHA09 test import log the following<br />error messages are recorded:<br /> Function HRFPM_AWB_GET_F4_SELOPTS (HRFPM_AWB_F4_INTF|03) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((HRFPM_AWB_FRAME|03))<br /> Function HRFPM_AWB_SET_F4_SELOPTS (HRFPM_AWB_F4_INTF|04) does not fit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;into the existing function group ((HRFPM_AWB_FRAME|04))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can ignore this error by selecting \"Extras\"-&gt;\"Ignore test<br />import errors\". The error will not recur with subsequent imports.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br />Based on previous remarks, we recommend the following for EA-HR support<br />support package queues.<br />SAPKGPHA01 - SAPKGPHA.. (highest available EA_HR Support Package)<br /><br />You can define shorter queues but you must pay attention to the split<br />points.<br />These recommended queues only apply if you have not installed any<br />add-ons or do not need to include any CRTs in the queue. Otherwise, you<br />will need advice from the producer of the add-ons or CRTs as to optimal<br />or permitted queues.<br /> <OL>19. <B>Support Packages for Enterprise Extension EA-HR 200</B><br /><br /></OL> <OL>20. <B>Support Packages for Enterprise Extension EA-IPPE 110</B><br /><br /></OL> <OL>21. <B>Support Packages for Enterprise Extension EA-IPPE 200</B><br /><br /></OL> <OL>22. <B>Support Packages for Enterprise Extension EA-PS 110</B></OL> <UL><LI> <B>SAPKGPPA07</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: An error occurs in the DDIC_ACTIVATION phase when you import<br />EA-PS Support Package SAPKGPPA07. The activation log contains the<br />following error messages:<br />&#x00A0;&#x00A0;The IFM01D table could not be activated. Field FLG_AK_PAYM_FICA in the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;IFM01D table is duplicated.<br />&#x00A0;&#x00A0;The IFM01D table could not be activated. Field FLG_AK_PAYM_FIAR in the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; IFM01D table is duplicated.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Prerequisite: You import EA-PS Support Package SAPKGPPA07 after R/3<br />Support Package SAPKH47005 and before R/3 Support Package SAPKH47007.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: To avoid the problem, you should only import EA-PS Support<br />package SAPKGPPA07 after R/3 Support Package SAPKH47007.<br />If the error has already occurred, follow the solution directions from<br />Note 625098.</p> <UL><LI> <B>SAPKGPPA11 - SAPKGPPA16</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Symptom: When you import a queue with EA-PS Support Packages SAPKGPPA11<br />- SAPKGPPA16, an error arises in the after-import methods execution<br />(XPRA_EXECUTION phase). The after-import methods log records that the<br />after-import procedure G_RW_AFTER_IMPORT was completed with errors.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution: You can solve this problem by executing the RGSROLNM report<br />(see the post-processing information in Point 2 in the Solution section<br />of Note 670380).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To avoid the error, you should not import EA-PS Support Packages<br />SAPKGPPA11 and SAPKGPPA16 into the same queue.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Recommended queues:</B><br />Based on previous remarks, we recommend the following for EA-PS support<br />package queues:<br />SAPKGPPA01 - SAPKGPPA15<br />SAPKGPPA16 - SAPKGPHA.. (highest available EA_PS Support Package)<br /><br />You can define shorter queues but you must pay attention to the split<br />points.<br />These recommended queues only apply if you have not installed any<br />add-ons or do not need to include any CRTs in the queue. Otherwise you<br />will need advice from the producer of the add-ons or CRTs as to optimal<br />or permitted queues.<br /> <OL>23. <B>Support Packages for Enterprise Extension EA-PS 200</B><br /><br /></OL> <OL>24. <B>Support Packages for Enterprise Extension EA-RETAIL 110</B><br /><br /></OL> <OL>25. <B>Support Packages for Enterprise Extension EA-RETAIL 200</B><br /><br /></OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028597)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019419)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000447925/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000447925/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447925/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447925/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447925/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447925/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447925/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447925/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000447925/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "97621", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS Info: Online Correction Support (OCS)", "RefUrl": "/notes/97621"}, {"RefNumber": "97620", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Overview of important OCS SAP Notes", "RefUrl": "/notes/97620"}, {"RefNumber": "850738", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL/IS-MINE/IS-CWM 4.72: CRT APPL SP 23-24", "RefUrl": "/notes/850738"}, {"RefNumber": "789075", "RefComponent": "SCM-EM-MGR", "RefTitle": "Release note for SAP EM 1.1 Support Package 08", "RefUrl": "/notes/789075"}, {"RefNumber": "782140", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel. 6.20", "RefUrl": "/notes/782140"}, {"RefNumber": "779357", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL/IS-MINE/IS-CWM 4.72: CRT APPL Supp.Pack. 22", "RefUrl": "/notes/779357"}, {"RefNumber": "769465", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "Test plan generation fails", "RefUrl": "/notes/769465"}, {"RefNumber": "766751", "RefComponent": "FIN-CGV", "RefTitle": "SAP CM SOA 1.0: Status Unrestricted Shipment", "RefUrl": "/notes/766751"}, {"RefNumber": "754294", "RefComponent": "FIN-CGV-MIC", "RefTitle": "CGVMIC 100: Support Package 08", "RefUrl": "/notes/754294"}, {"RefNumber": "753627", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/753627"}, {"RefNumber": "745468", "RefComponent": "BC-CCM-BTC", "RefTitle": "Problems with sapevt program in Releases 6.20 and 6.40", "RefUrl": "/notes/745468"}, {"RefNumber": "744859", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL/IS-MINE/IS-CWM 4.72: CRT APPL Supp.Pack. 19-20", "RefUrl": "/notes/744859"}, {"RefNumber": "739111", "RefComponent": "SCM-EM-MGR", "RefTitle": "Release note for SAP EM 1.1 Support Package 07", "RefUrl": "/notes/739111"}, {"RefNumber": "738950", "RefComponent": "BC-DB-MSS", "RefTitle": "SPAM error in \"Method Execution\" on Microsoft SQL Server", "RefUrl": "/notes/738950"}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "731265", "RefComponent": "BC-DB-MSS", "RefTitle": "BindInputParams: the parameter is incorrect", "RefUrl": "/notes/731265"}, {"RefNumber": "722638", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/722638"}, {"RefNumber": "720083", "RefComponent": "BC", "RefTitle": "6.20 Basis Support Package 38 requires SAP kernel patch", "RefUrl": "/notes/720083"}, {"RefNumber": "712740", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL/IS-MINE/IS-CWM 4.72: CRT APPL Supp.Pack. 14-18", "RefUrl": "/notes/712740"}, {"RefNumber": "704586", "RefComponent": "CRM-MW-ADP", "RefTitle": "Activation error in CRM 4.0 / EBP 4.0", "RefUrl": "/notes/704586"}, {"RefNumber": "696859", "RefComponent": "SCM-TEC", "RefTitle": "SAP SCM 4.0 SP stack 02/2004: Release & information note", "RefUrl": "/notes/696859"}, {"RefNumber": "684564", "RefComponent": "BC-UPG-OCS", "RefTitle": "Re-importing a SPAM/SAINT update", "RefUrl": "/notes/684564"}, {"RefNumber": "676135", "RefComponent": "BC-UPG-OCS", "RefTitle": "Short dump when you import Support Packages", "RefUrl": "/notes/676135"}, {"RefNumber": "671047", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/671047"}, {"RefNumber": "668163", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Mass activation: Processed objects are not marked", "RefUrl": "/notes/668163"}, {"RefNumber": "644640", "RefComponent": "BC", "RefTitle": "Basis Support Package and SAP Kernel interdependencies", "RefUrl": "/notes/644640"}, {"RefNumber": "640609", "RefComponent": "BC-UPG-NA", "RefTitle": "SPAU incorrectly displays note corrections as obsolete", "RefUrl": "/notes/640609"}, {"RefNumber": "640462", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "SPAM 'DDIC_ACTIVATION' STPRSOKEY import phase", "RefUrl": "/notes/640462"}, {"RefNumber": "639970", "RefComponent": "BC-CTS-CCO", "RefTitle": "UNICODE error in SAPKB62026", "RefUrl": "/notes/639970"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "627246", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Error during creation of objects: ORA-02168", "RefUrl": "/notes/627246"}, {"RefNumber": "625098", "RefComponent": "BC-UPG-OCS", "RefTitle": "Error with DDIC activation", "RefUrl": "/notes/625098"}, {"RefNumber": "623934", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/623934"}, {"RefNumber": "620723", "RefComponent": "PY-NL", "RefTitle": "DDIC_ACTIVATION aborted in P05_SIM_PAR", "RefUrl": "/notes/620723"}, {"RefNumber": "615187", "RefComponent": "BC-UPG-OCS", "RefTitle": "Activation error when importing Support Package 7", "RefUrl": "/notes/615187"}, {"RefNumber": "615168", "RefComponent": "BC-CCM-BTC", "RefTitle": "Jobs with temporary variant terminate after Support Package", "RefUrl": "/notes/615168"}, {"RefNumber": "605752", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Unexpected token ALLOW REVERSE SCANS", "RefUrl": "/notes/605752"}, {"RefNumber": "604377", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU/SPDD: Deleted objects are determined incorrectly", "RefUrl": "/notes/604377"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "603575", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Termination when creating a new pool w/ VARDATA length 0000", "RefUrl": "/notes/603575"}, {"RefNumber": "603173", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: CONNE_IMPORT_WRONG_COMP_TYPE dump during adjustment", "RefUrl": "/notes/603173"}, {"RefNumber": "601742", "RefComponent": "BC-DWB-CEX", "RefTitle": "Multiple activation of singular BAdIs possible", "RefUrl": "/notes/601742"}, {"RefNumber": "596982", "RefComponent": "BC-CTS-LAN", "RefTitle": "Texts are deleted by Support Package", "RefUrl": "/notes/596982"}, {"RefNumber": "588515", "RefComponent": "BC-DB-SDB-CCM", "RefTitle": "Authorizations for database access in the MaxDB/liveCache CCMS", "RefUrl": "/notes/588515"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "578964", "RefComponent": "MM-IM-GF-VAL", "RefTitle": "Subcontractor order: Incorrect component posting", "RefUrl": "/notes/578964"}, {"RefNumber": "572266", "RefComponent": "BC-UPG-OCS", "RefTitle": "XPRA execution return code 0006 with Support Package import", "RefUrl": "/notes/572266"}, {"RefNumber": "572041", "RefComponent": "BC-SRV-ADR", "RefTitle": "DDIC_TYPE_INCONSISTENCY as a result of extending BAPIADDR3", "RefUrl": "/notes/572041"}, {"RefNumber": "570683", "RefComponent": "SRM-EBP-SHP", "RefTitle": "DDIC_TYPE_INCONSISTENCY for table REQADDRESS", "RefUrl": "/notes/570683"}, {"RefNumber": "570660", "RefComponent": "CRM-BTX-BF-IF", "RefTitle": "Structure BAD_ADDRESS cannot be activated", "RefUrl": "/notes/570660"}, {"RefNumber": "568454", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Problems during merge activation", "RefUrl": "/notes/568454"}, {"RefNumber": "567268", "RefComponent": "PLM-PPM-RPL", "RefTitle": "Correct DDIC activation errors (EA-APPL-SP 1/2)", "RefUrl": "/notes/567268"}, {"RefNumber": "563437", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "Interest rate instrument: +/- sign interest capitaliztn flow", "RefUrl": "/notes/563437"}, {"RefNumber": "551688", "RefComponent": "PA", "RefTitle": "Contents and applying HR LCPs 4.70, 1.10, 2.00", "RefUrl": "/notes/551688"}, {"RefNumber": "545030", "RefComponent": "BC-DB-LVC-CCM", "RefTitle": "Revised alert monitor for SAP DB/liveCache", "RefUrl": "/notes/545030"}, {"RefNumber": "537881", "RefComponent": "BC-CTS-TLS", "RefTitle": "TP stops during execution of SPAM phase XPRA_EXECUTION", "RefUrl": "/notes/537881"}, {"RefNumber": "524372", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "OTC: Accrual/deferral flows deleted", "RefUrl": "/notes/524372"}, {"RefNumber": "522711", "RefComponent": "BC-UPG", "RefTitle": "Corrections for upgrade to Basis 620", "RefUrl": "/notes/522711"}, {"RefNumber": "484219", "RefComponent": "BC-UPG-OCS", "RefTitle": "Known problems in transaction SAINT in Basis Release 6.20", "RefUrl": "/notes/484219"}, {"RefNumber": "432027", "RefComponent": "BC-UPG-OCS", "RefTitle": "Strategy for using SAP Support Packages", "RefUrl": "/notes/432027"}, {"RefNumber": "211077", "RefComponent": "BC-UPG-PRP", "RefTitle": "Replacement of target release kernel for upgrade/EHPI", "RefUrl": "/notes/211077"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "211077", "RefComponent": "BC-UPG-PRP", "RefTitle": "Replacement of target release kernel for upgrade/EHPI", "RefUrl": "/notes/211077 "}, {"RefNumber": "639970", "RefComponent": "BC-CTS-CCO", "RefTitle": "UNICODE error in SAPKB62026", "RefUrl": "/notes/639970 "}, {"RefNumber": "572041", "RefComponent": "BC-SRV-ADR", "RefTitle": "DDIC_TYPE_INCONSISTENCY as a result of extending BAPIADDR3", "RefUrl": "/notes/572041 "}, {"RefNumber": "782140", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS: Known problems with Support Packages in Basis Rel. 6.20", "RefUrl": "/notes/782140 "}, {"RefNumber": "613608", "RefComponent": "XX-PROJ-JP-CCS", "RefTitle": "Installation KJCCSJ 4.71 on R/3 Enterprise 47x110", "RefUrl": "/notes/613608 "}, {"RefNumber": "551688", "RefComponent": "PA", "RefTitle": "Contents and applying HR LCPs 4.70, 1.10, 2.00", "RefUrl": "/notes/551688 "}, {"RefNumber": "938155", "RefComponent": "IS-REA", "RefTitle": "IS-REA 4.71: Installing Support Package 03", "RefUrl": "/notes/938155 "}, {"RefNumber": "615168", "RefComponent": "BC-CCM-BTC", "RefTitle": "Jobs with temporary variant terminate after Support Package", "RefUrl": "/notes/615168 "}, {"RefNumber": "610368", "RefComponent": "XX-PROJ-JP-PHM", "RefTitle": "Installation KJCPH 2.0A on R/3 Enterprise 47x110", "RefUrl": "/notes/610368 "}, {"RefNumber": "572266", "RefComponent": "BC-UPG-OCS", "RefTitle": "XPRA execution return code 0006 with Support Package import", "RefUrl": "/notes/572266 "}, {"RefNumber": "545030", "RefComponent": "BC-DB-LVC-CCM", "RefTitle": "Revised alert monitor for SAP DB/liveCache", "RefUrl": "/notes/545030 "}, {"RefNumber": "627246", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Error during creation of objects: ORA-02168", "RefUrl": "/notes/627246 "}, {"RefNumber": "588515", "RefComponent": "BC-DB-SDB-CCM", "RefTitle": "Authorizations for database access in the MaxDB/liveCache CCMS", "RefUrl": "/notes/588515 "}, {"RefNumber": "684564", "RefComponent": "BC-UPG-OCS", "RefTitle": "Re-importing a SPAM/SAINT update", "RefUrl": "/notes/684564 "}, {"RefNumber": "634219", "RefComponent": "XX-PROJ-JP-EIM", "RefTitle": "Add. info on R/3 Enterprise 4.70 upgrade with KEPS-MM", "RefUrl": "/notes/634219 "}, {"RefNumber": "633772", "RefComponent": "XX-PROJ-JP-CJT", "RefTitle": "Information for BPCCSJP 10A (CCS Japan Template)", "RefUrl": "/notes/633772 "}, {"RefNumber": "744859", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL/IS-MINE/IS-CWM 4.72: CRT APPL Supp.Pack. 19-20", "RefUrl": "/notes/744859 "}, {"RefNumber": "779357", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL/IS-MINE/IS-CWM 4.72: CRT APPL Supp.Pack. 22", "RefUrl": "/notes/779357 "}, {"RefNumber": "850738", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL/IS-MINE/IS-CWM 4.72: CRT APPL SP 23-24", "RefUrl": "/notes/850738 "}, {"RefNumber": "484219", "RefComponent": "BC-UPG-OCS", "RefTitle": "Known problems in transaction SAINT in Basis Release 6.20", "RefUrl": "/notes/484219 "}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800 "}, {"RefNumber": "676135", "RefComponent": "BC-UPG-OCS", "RefTitle": "Short dump when you import Support Packages", "RefUrl": "/notes/676135 "}, {"RefNumber": "739609", "RefComponent": "IS-REA", "RefTitle": "IS-REA 4.71: Installation of Support Package 01", "RefUrl": "/notes/739609 "}, {"RefNumber": "806475", "RefComponent": "IS-REA", "RefTitle": "IS-REA 4.71: Installing Support Package 02", "RefUrl": "/notes/806475 "}, {"RefNumber": "814242", "RefComponent": "IS-REA", "RefTitle": "IS-REA 4.71 SR1: Installation note", "RefUrl": "/notes/814242 "}, {"RefNumber": "769465", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "Test plan generation fails", "RefUrl": "/notes/769465 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "712740", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL/IS-MINE/IS-CWM 4.72: CRT APPL Supp.Pack. 14-18", "RefUrl": "/notes/712740 "}, {"RefNumber": "97620", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Overview of important OCS SAP Notes", "RefUrl": "/notes/97620 "}, {"RefNumber": "605752", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Unexpected token ALLOW REVERSE SCANS", "RefUrl": "/notes/605752 "}, {"RefNumber": "644640", "RefComponent": "BC", "RefTitle": "Basis Support Package and SAP Kernel interdependencies", "RefUrl": "/notes/644640 "}, {"RefNumber": "615187", "RefComponent": "BC-UPG-OCS", "RefTitle": "Activation error when importing Support Package 7", "RefUrl": "/notes/615187 "}, {"RefNumber": "522711", "RefComponent": "BC-UPG", "RefTitle": "Corrections for upgrade to Basis 620", "RefUrl": "/notes/522711 "}, {"RefNumber": "789075", "RefComponent": "SCM-EM-MGR", "RefTitle": "Release note for SAP EM 1.1 Support Package 08", "RefUrl": "/notes/789075 "}, {"RefNumber": "720083", "RefComponent": "BC", "RefTitle": "6.20 Basis Support Package 38 requires SAP kernel patch", "RefUrl": "/notes/720083 "}, {"RefNumber": "97621", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS Info: Online Correction Support (OCS)", "RefUrl": "/notes/97621 "}, {"RefNumber": "745468", "RefComponent": "BC-CCM-BTC", "RefTitle": "Problems with sapevt program in Releases 6.20 and 6.40", "RefUrl": "/notes/745468 "}, {"RefNumber": "754294", "RefComponent": "FIN-CGV-MIC", "RefTitle": "CGVMIC 100: Support Package 08", "RefUrl": "/notes/754294 "}, {"RefNumber": "766751", "RefComponent": "FIN-CGV", "RefTitle": "SAP CM SOA 1.0: Status Unrestricted Shipment", "RefUrl": "/notes/766751 "}, {"RefNumber": "739111", "RefComponent": "SCM-EM-MGR", "RefTitle": "Release note for SAP EM 1.1 Support Package 07", "RefUrl": "/notes/739111 "}, {"RefNumber": "704586", "RefComponent": "CRM-MW-ADP", "RefTitle": "Activation error in CRM 4.0 / EBP 4.0", "RefUrl": "/notes/704586 "}, {"RefNumber": "731265", "RefComponent": "BC-DB-MSS", "RefTitle": "BindInputParams: the parameter is incorrect", "RefUrl": "/notes/731265 "}, {"RefNumber": "738950", "RefComponent": "BC-DB-MSS", "RefTitle": "SPAM error in \"Method Execution\" on Microsoft SQL Server", "RefUrl": "/notes/738950 "}, {"RefNumber": "432027", "RefComponent": "BC-UPG-OCS", "RefTitle": "Strategy for using SAP Support Packages", "RefUrl": "/notes/432027 "}, {"RefNumber": "696859", "RefComponent": "SCM-TEC", "RefTitle": "SAP SCM 4.0 SP stack 02/2004: Release & information note", "RefUrl": "/notes/696859 "}, {"RefNumber": "640462", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "SPAM 'DDIC_ACTIVATION' STPRSOKEY import phase", "RefUrl": "/notes/640462 "}, {"RefNumber": "603575", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Termination when creating a new pool w/ VARDATA length 0000", "RefUrl": "/notes/603575 "}, {"RefNumber": "668163", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Mass activation: Processed objects are not marked", "RefUrl": "/notes/668163 "}, {"RefNumber": "640609", "RefComponent": "BC-UPG-NA", "RefTitle": "SPAU incorrectly displays note corrections as obsolete", "RefUrl": "/notes/640609 "}, {"RefNumber": "567268", "RefComponent": "PLM-PPM-RPL", "RefTitle": "Correct DDIC activation errors (EA-APPL-SP 1/2)", "RefUrl": "/notes/567268 "}, {"RefNumber": "578964", "RefComponent": "MM-IM-GF-VAL", "RefTitle": "Subcontractor order: Incorrect component posting", "RefUrl": "/notes/578964 "}, {"RefNumber": "625098", "RefComponent": "BC-UPG-OCS", "RefTitle": "Error with DDIC activation", "RefUrl": "/notes/625098 "}, {"RefNumber": "601742", "RefComponent": "BC-DWB-CEX", "RefTitle": "Multiple activation of singular BAdIs possible", "RefUrl": "/notes/601742 "}, {"RefNumber": "620723", "RefComponent": "PY-NL", "RefTitle": "DDIC_ACTIVATION aborted in P05_SIM_PAR", "RefUrl": "/notes/620723 "}, {"RefNumber": "604377", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU/SPDD: Deleted objects are determined incorrectly", "RefUrl": "/notes/604377 "}, {"RefNumber": "570660", "RefComponent": "CRM-BTX-BF-IF", "RefTitle": "Structure BAD_ADDRESS cannot be activated", "RefUrl": "/notes/570660 "}, {"RefNumber": "603173", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: CONNE_IMPORT_WRONG_COMP_TYPE dump during adjustment", "RefUrl": "/notes/603173 "}, {"RefNumber": "570683", "RefComponent": "SRM-EBP-SHP", "RefTitle": "DDIC_TYPE_INCONSISTENCY for table REQADDRESS", "RefUrl": "/notes/570683 "}, {"RefNumber": "568454", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Problems during merge activation", "RefUrl": "/notes/568454 "}, {"RefNumber": "563437", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "Interest rate instrument: +/- sign interest capitaliztn flow", "RefUrl": "/notes/563437 "}, {"RefNumber": "537881", "RefComponent": "BC-CTS-TLS", "RefTitle": "TP stops during execution of SPAM phase XPRA_EXECUTION", "RefUrl": "/notes/537881 "}, {"RefNumber": "524372", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "OTC: Accrual/deferral flows deleted", "RefUrl": "/notes/524372 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "EA-HR", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-PS", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-GLTRADE", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "EA-IPPE", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "620", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "620", "To": "620", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}