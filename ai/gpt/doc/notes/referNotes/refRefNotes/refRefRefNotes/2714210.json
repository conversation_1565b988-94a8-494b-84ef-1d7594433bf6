{"Request": {"Number": "2714210", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 363, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002341462018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002714210?language=E&token=94E3DCFE098023E033B32FC0B2615918"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002714210", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002714210/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2714210"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.12.2019"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2714210 - New communication channel to SAP Backbone for Service Content Update."}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Service Content Update (SCU), is tool used to execute updates for SAP services in SAP Solution Manager without importing ST-SER Support Packages or implementing SAP Notes. On the customer side, SAP Solution Manager connects to the Support Backbone to exchange data required for service content update.</p>\r\n<p>SAP always ensures that this critical infrastructure is up to date and secure. Currently, the Support Backbone infrastructure is updated and hence the communication channel used by SCU application is upgraded to use https protocol. Please refer to the link for more information&#160;<a target=\"_blank\" href=\"https://support.sap.com/en/alm/solution-manager/sap-support-backbone-update.html\">https://support.sap.com/en/alm/solution-manager/sap-support-backbone-update.html</a>.</p>\r\n<p>This SAP Note includes an introduction about the new communication channel and the code changes delivered as a part of the update. The correction instructions attached to this Note are valid for&#160;SAP Solution Manager 7.2&#160;SP05, SP06&#160;and SP07.</p>\r\n<p>If you have newly installed or upgraded to SAP Solution Manager 7.2 SP08, the code changes in this SAP Note are already part of the standard delivery, so you don't need to implement any correction instructions of this SAP Note. But you can read the descriptions of this Note for understanding the changes that are part of this update in SCU application.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Prerequisite:</strong></span></p>\r\n<p>To connect to SAP Support backend, SCU process will now use the new communication channels to SAP Support Portal and SAP Parcel Box destinations instead of the RFC destination SAPOSS.&#160;You must make sure that following items are taken care of,</p>\r\n<ul>\r\n<li>The HTTP destinations SAP-SUPPORT_PARCELBOX (type G) and SAP-SUPPORT_PORTAL (type H) exist and work.\r\n<p>You can use the task list 'SAP_BASIS_CONFIG_OSS_COMM' - New OSS Communication to configure and check the connection to SAP new Support Backbone.<br />The required task list and tasks are provided via Transport based Correction Instruction (TCI).<br />TCI is available for releases: 7.40 &gt;=SP08 // 7.50, 7.51, 752 &gt;=SP00 // 7.53 &gt;= SP02. Please refer to the SAP Note&#160;<a target=\"_blank\" href=\"/notes/2738426\">2738426</a>&#160;for more information on how to implement this TCI.</p>\r\n</li>\r\n<li>If the destination SAP-SUPPORT_PORTAL is not available in the system, then please run the solman_setup activity System Preparation-&gt;Setup Connections to SAP-&gt;Support Hub connectivity and make sure this step is green.</li>\r\n<li>The S-user you requested through standard way has enough authorizations and can be used in these two destinations.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong>Troubleshooting information:</strong></span></p>\r\n<ul>\r\n<li>For Step 1: 'Check for New Updates', SCU now uses the SAP Support Portal to check for new service content updates. The HTTP destination that is used is&#160;SAP-SUPPORT_PORTAL. In case this HTTP destination is not configured correctly, the RFC SAPOSS will be used for checking and downloading updates as a fallback/troubleshooting option.&#160;<span style=\"text-decoration: underline;\"><em>Please be aware that SAP suggests you to configure and use the new communication channel. This fallback option is only for exceptional cases and troubleshooting purpose.</em><br /></span></li>\r\n<li>For Step 2: 'Download Service content Updates', SCU now uses the SAP Parcel Box to download newer service content. The HTTP destination that is used is&#160;SAP-SUPPORT_PARCELBOX.</li>\r\n<li>Symptom: Error Message \"<em>Communication with Parcel box failed.</em>\" occurs in step 2&#160; (message class AGS_DSWP_INFRA&#160;, message number&#160; 036).<br />This error message can either be found in the job log of&#160;job <em>SM:SERVICE CONTENT UPDATE </em>or when using transaction AGS_UPDATE in dialog. Possible reasons:</li>\r\n<ul>\r\n<li>Reason&#160;Destination SAP-SUPPORT_PARCELBOX does not exist. At the same time destination SAP-SUPPORT_PORTAL is functional.</li>\r\n<ul>\r\n<li>Solution: see KBA&#160;<a target=\"_blank\" href=\"/notes/2716729\">2716729</a>&#160;:&#160;&#160;the system is updated from SP05/SP06/SP07 to SP08 and higher, the task list SAP_SUPPORT_HUB_CONFIG needs to be re-executed to generate the&#160;destination.</li>\r\n</ul>\r\n<li>Reason&#160;Destination SAP-SUPPORT_PARCELBOX&#160;is not functional. At the same time destination SAP-SUPPORT_PORTAL is functional.</li>\r\n<ul>\r\n<li>Repair the destination SAP-SUPPORT_PARCELBOX. For background infomation see&#160;KBA&#160;<a target=\"_blank\" href=\"/notes/2716729\">2716729</a>&#160;and <a target=\"_blank\" href=\"https://ga.support.sap.com/dtp/viewer/index.html#/tree/1423/actions/17822\">Guided Answer on Support Hub Connectivity</a>.</li>\r\n</ul>\r\n<li>The&#160;<a target=\"_blank\" href=\"https://ga.support.sap.com/dtp/viewer/index.html#/tree/1423/actions/17822\">Guided Answer on Support Hub Connectivity</a>&#160;shows information upon the different network configurations possible&#160;and known&#160;error situations (partially valid on Solution Manager only). In case you are using a configuration with SAProuter, you need to make sure to use a&#160;<strong>SAProuter not below SAProuter 7.42 SP210</strong>. Otherwise communication will fail with the error described in&#160;<a target=\"_blank\" href=\"/notes/2173578\">SAP Note&#160;&#160;2173578</a>. When updating SAProuter, refer to&#160;<a target=\"_blank\" href=\"/notes/1897597\">SAP Note 1897597 - SAProuter: Release rollout</a>&#160;- it recommends you&#160;to update to the latest release and patch.</li>\r\n</ul>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Service content update, AGS_UPDATE, OSS, SCU,&#160;SAP Support Portal, SAP Parcel Box.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The&#160;infrastructure of the SAP Support Backbone is updated and SAPOSS can't be used for Service content update.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the correction instructions attached with this SAP Note.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I077373"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I059677)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002714210/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002714210/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002714210/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002714210/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002714210/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002714210/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002714210/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002714210/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002714210/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2722875", "RefComponent": "SV-SMG-SER", "RefTitle": "Recommended corrections to resolve issues with the new communication channel in Service content update.", "RefUrl": "/notes/2722875"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3360544", "RefComponent": "SV-SMG-SVD-SCU", "RefTitle": "SM:SERVICE CONTENT UPDATE: Problem with SAP destination SAPOSS: Internal error.", "RefUrl": "/notes/3360544 "}, {"RefNumber": "1835575", "RefComponent": "SV-SMG-SER", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC dump in AGS_UPDATE when applying updates", "RefUrl": "/notes/1835575 "}, {"RefNumber": "2285891", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "How to resolve Short Dumps for SYNTAX_ERROR in the /1AGS* namespace of SAP Solution Manager", "RefUrl": "/notes/2285891 "}, {"RefNumber": "2917960", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Silent Deactivation of Service Content Update Causes Regression to EarlyWatch Alert Content", "RefUrl": "/notes/2917960 "}, {"RefNumber": "2668288", "RefComponent": "SV-SMG-SVC", "RefTitle": "Differences between personalized S-user ID and Technical Communication User", "RefUrl": "/notes/2668288 "}, {"RefNumber": "2737826", "RefComponent": "XX-SER-NET", "RefTitle": "SAP is going to close its proprietary RFC communication for automated data exchange between Customers & SAP Support Backbone (SAPOSS) by July 2020.", "RefUrl": "/notes/2737826 "}, {"RefNumber": "2716729", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP backbone connectivity - SAP Parcel Box configuration", "RefUrl": "/notes/2716729 "}, {"RefNumber": "2125970", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Correction available with SAP Service Content Update", "RefUrl": "/notes/2125970 "}, {"RefNumber": "2767779", "RefComponent": "SV-SMG-SER", "RefTitle": "SCU: AGS_UPDATE dumps with 'Exception condition \"ZIP_PARSE_ERROR\" triggered'.", "RefUrl": "/notes/2767779 "}, {"RefNumber": "2740667", "RefComponent": "XX-SER-NET", "RefTitle": "SAP is going to close its proprietary RFC communication for automated data exchange between Customers & SAP Support Backbone (SAPOSS) by July 2020.", "RefUrl": "/notes/2740667 "}, {"RefNumber": "1143775", "RefComponent": "SV-SMG-SVD-SCU", "RefTitle": "SAP Service Content Update", "RefUrl": "/notes/1143775 "}, {"RefNumber": "2722875", "RefComponent": "SV-SMG-SER", "RefTitle": "Recommended corrections to resolve issues with the new communication channel in Service content update.", "RefUrl": "/notes/2722875 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "720", "To": "720", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST", "NumberOfCorrin": 5, "URL": "/corrins/0002714210/162"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "ST", "ValidFrom": "720", "ValidTo": "720", "Number": "2714210 ", "URL": "/notes/2714210 ", "Title": "New communication channel to SAP Backbone for Service Content Update.", "Component": "SV-SMG-SER"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}