SAP Note 2608006 addresses a new authorization concept within the Core Data Services (CDS) based Virtual Data Model (VDM), specifically for accessing Universal Journal data. The note introduces a new authorization object, F_ACDOCA_C, which is responsible for determining the different authorization levels for roles attempting to access the same data.

The Universal Journal combines various accounting components such as General Ledger Accounting (FI-GL), Asset Accounting (FI-AA), Controlling (CO), and Profitability Analysis (CO-PA) into a single chart of accounts. This integration ensures constant reconciliation as all financial data is drawn from the same line items, thereby eliminating the need for reconciliation between financial and management accounting. The note clarifies that while the universal journal is integrated with account-based profitability analysis, it can still run in parallel with costing-based profitability analysis, and mentions the Material Ledger (CO-PC-ACT) as an additional component.

Due to the data being stored in a single universal journal entry (table ACDOCA), yet necessitating various user access with differing authorizations, the note emphasizes the importance of the new authorization object to manage these authorizations effectively.

While the note summarizes the concept, it also refers to an attached document for further details on "Universal Journal Authorizations." The note suggests checking the access controls for the latest list of authorization objects for each context and mentions that official documentation is currently being prepared and will eventually be available on help.sap.com.

There are no references to other SAP Notes within this particular note.