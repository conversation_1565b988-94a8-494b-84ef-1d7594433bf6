SAP Note 873466 addresses a program termination error occurring in CO (Controlling) transactions, identified by the runtime error MESSAGE_TYPE_X. This note pertains to SAP systems where either the Funds Management or Grants Management is activated and that have been upgraded to or are already running on ECC Release 5.0 (ECC500).

The cause of the error is tied to two new fields, SEGMEN<PERSON> and PSEGMENT, added to the COKEY2 table during the upgrade to ECC500. If these fields are not initialized correctly post-upgrade, duplicate entries in the COKEY2 table may occur, resulting in data inconsistencies and subsequent transaction failures.

The solution proposed involves different actions depending on the upgrade scenario:

1. For systems upgraded to ECC5.0, it is recommended to run the report Z_873466_REPAIR_AFTER_UPGRADE directly after the upgrade to convert and correct the COKEY2 table in all clients, thus preventing further errors.

2. If new postings have been created after upgrading to ECC5.0, manual analysis and correction of the data are necessary. This involves implementing and executing three custom reports (Z_873466_CHECK, Z_873466_REPAIR_COKEY2, Z_873466_REPAIR_POSTINGS) in all clients. Inconsistencies detected by Z_873466_CHECK should be reported to SAP Support for resolution, and further postings should be avoided until corrected.

3. Upgrades from a release lower than ECC5.0 to ECC6.0 do not require any additional actions.

4. For systems on Release ECC5.0 that are upgrading to ECC6.0, table COKEY2 must be checked in each client. If entries exist, corrections using the custom reports should be performed before the upgrade.

In summary, SAP Note 873466 offers clear guidance on how to handle data inconsistencies in the COKEY2 table induced by upgrades to ECC5.0. It provides specific instructions for correction based on the upgrade path and includes custom report names for data analysis and repair. It also stresses the importance of involving SAP Support for certain errors and temporarily halting postings until issues are resolved.