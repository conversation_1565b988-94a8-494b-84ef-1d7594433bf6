{"guid": "00109B1315FA1ED9AE8A3467481D20DC", "sitemId": "SI32: PPM_INTGR_CRM_01", "sitemTitle": "S4TWL - Object links to marketing campains in SAP CRM", "note": 2823375, "noteTitle": "2823375 - S4TWL - Object links to marketing campains in SAP Customer Relationship Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your existing system landscape from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. The following SAP Portfolio and Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CRM, Customer Relationship Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span #333333;=\"\" arial',sans-serif;=\"\" color:=\"\" en-us;\"=\"\" lang=\"EN-US\" mso-ansi-language:=\"\">Description</span></strong></p>\n<p>The integration with marketing campaigns in SAP Customer Relationship Management (SAP CRM) is not available in SAP Portfolio and Project Management for SAP S/4HANA.</p>\n<p><strong><span #333333;=\"\" arial',sans-serif;=\"\" color:=\"\" en-us;\"=\"\" lang=\"EN-US\" mso-ansi-language:=\"\">Business Process related information</span></strong></p>\n<p>It is no longer possible to link from Project Management to marketing campains in SAP CRM.</p>\n<p><strong><span #333333;=\"\" arial',sans-serif;=\"\" color:=\"\" en-us;\"=\"\" lang=\"EN-US\" mso-ansi-language:=\"\">Required and Recommended Action(s)</span></strong></p>\n<p>Data related to the integration with marketing campains cannot be migrated to SAP Portfolio and Project Management for SAP S/4HANA, therefore it is required to clean up the data before the system conversion in order to prevent any data loss. Archive the corresponding projects or delete not needed integration related data before you continue with the system conversion.</p>", "noteVersion": 2, "refer_note": [{"note": "2692798", "noteTitle": "2692798 - Marketing functionality not supported in S/4HANA Service", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are considering or planning a migration of a CRM 7.0 or CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. The following information on changed or deprecated functionality is relevant in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4CRMTWL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Marketing functionality is not available in S/4HANA (with the exception of leads which are sometimes assigned to the Marketing area). Thus, you cannot create campaigns, target groups, or trade promotions. For Marketing functionality in the S/4HANA realm, SAP Marketing Cloud shall be used.<br/>As a consequence, all references that point to Marketing functionality, such as the campaign field in transactions, and all related functions, such as campaign determination, have been removed.<br/>Lead templates and mass lead generation are not supported.</p>", "noteVersion": 3}], "activities": [{"Activity": "Data cleanup / archiving", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "The CRM Integration for marketing campaigns is not available in SAP Portfolio and Project Management for SAP S/4HANA. Please delete existing object links of type 0CRMCAMPAIGN (“Marketing Campaign (CRM)”)."}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}]}