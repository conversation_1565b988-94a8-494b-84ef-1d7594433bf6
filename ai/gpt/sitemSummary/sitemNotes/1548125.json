{"Request": {"Number": "1548125", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 394, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017161142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001548125?language=E&token=400CEAA004BAFB47DAD94E914962D334"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001548125", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001548125/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1548125"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 41}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.04.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-NC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Non cumulative value"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Non cumulative value", "value": "BW-BEX-OT-NC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-NC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1548125 - Interesting Facts about Non-Cumulatives"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>A query with non-cumulative key figures does not display the expected values. You want to find out whether the query causes the issue or whether the data in the InfoProvider(InfoCube, DataStore objects (advanced)/ADSO) is already wrong by using the transaction LISTCUBE. Since non-cumulative key figures are calculated during query runtime (they are not physically stored in the fact tables), you don't know how to derive the query result from the data in the InfoProvider.<br />In the first chapters we mainly refer to classic InfoCubes, regarding 'flat' Hana InfoCubes and ADSOs please check chapters&#160;[VIII] and&#160;[IX].<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>inventory cubes, ncum, 0IC_C03, 2LIS_03_BX, 2LIS_03_BF, 2LIS_03_UM</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In order to be able to interpret query results correctly, it is very important to understand the basic concept of InfoCubes and ADSOs with non-cumulative key figures. This note should help you to aquire this knowledge by providing sufficient technical background information.<br /><br /><br /><strong>CONTENTS</strong><br /><br /><strong>[I]&#160;&#160;&#160; Definitions and Metadata (</strong>Classic InfoCube<strong>)</strong><br /><strong>[II]&#160;&#160; Process Flow (</strong>Classic InfoCube<strong>)</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;[II.a] Example</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;[II.b] Loading</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;[II.c] Compression &amp; Updating Reference Points</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;[II.d] Calculation of Non-Cumulatives in a Query (Reporting)</strong><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;[II.e] Validity Table<br /></strong><strong>&#160; &#160; &#160; [II.f] Working without Stock Initialization<br /></strong><strong><strong>[III] LIS and CDS Extractors for Non-Cumulatives<br /></strong></strong><strong>[IV]&#160; Stock Initialization &amp; DTP<br /></strong><strong>[V]&#160; &#160;Data Mart<br /></strong><strong>[VI]&#160;&#160;&#160;&#160; Performance &amp; Memory Consumption<br /></strong><strong>[VII]&#160;&#160; &#160;Analysis of Problems<br /></strong><strong>[VIII]&#160; HANA Optimized Inventory Cubes<br /></strong><strong>[IX] Non-Cumulatives in Advanced DataStores<br /></strong><strong>[X]&#160; &#160;External Hana Views</strong></p>\r\n<p>&#160;</p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[I] Definitions and Metadata</strong><span style=\"font-size: 14px;\"> (Classic InfoCube, sample key figure 0TOTALSTCK)</span></p>\r\n<p><br /><strong>(A)</strong>Values of non-cumulative(ncum) key figures cannot be cumulated (summed up) in a meaningful way over time. The 'result' is caculated during query runtime, in the majority of cases the exception aggregation LAST is used.<br /><br /><strong>(B)</strong>Key Figure Definition<br />In RSD1 you can see that 'normal' cumulative key figures are assigned to such a non-cumulative key figure which describe the inflow and outflow (or the value change (delta)). In the query the ncum key figure is then calculated (for the requested days) by taking the data given by the key figures 'Inflow' and 'Outflow'.<br />Example:<br />non-cumulative key figure 0TOTALSTCK:<br />outflow 0ISSTOTSTCK (cumulative key figure)<br />inflow&#160;&#160;0RECTOTSTCK (cumulative key figure)<br /><br /><strong>(C)Time Reference Characteristic</strong><br />It's the most detailed time characteristic of the cube (often 0calday) and is used to determine the values of all other time characteristics.<br /><br /><strong>(D)Fact Tables</strong><br />If you use the key figure 0TOTALSTCK in an InfoCube, the actual fact tables will only contain colums for the cumulative inflow and outflow key figures 0ISSTOTSTCK and 0RECTOTSTCK. You will have no column for the ncum key figure 0TOTALSTCK.<br />The values are only calculated during query runtime and not stored in the fact tables! Thats why you can't find this key figure in the transaction LISTCUBE either.<br /><br /><strong>(E)Reference Points</strong><br />Technically, only non-cumulative changes (movements) and the stock refering to a certain point in time (so called reference points or markers) are stored in the fact tables. In order to distinguish between this two kind of values the technical infoobject <strong>0RECORDTP </strong>(part of the package dimension) was introduced.<br />For movements we have 0RECORDTP=0, for the reference points 0RECORDTP is always equal to 1 and the time reference characteristic is set to 'infinity' (e.g 0CALDAY = 31.12.9999).<br />In case the cube is totally compressed and you are only interested in the current stock values of a certain material, the query can only read the marker and hasn't to carry out calculations using the inflow and outflow.<br />By default the transaction LISTCUBE only reads data records where 0RECORDTP=0! Hence, if you want to get the reference points displayed you need to restrict this infoobject explicitly to 1 (or interval [0,1]).<br /><br /><strong>(F)Validity</strong><br />As the values of non-cumulatives are computed by reading the reference points and the non-cumulative changes, it is in principle possible to compute a value for every point in time. To be able to do this calculation you need to know for which time interval you can calculate the non-cumulative. The minimum and maximum loaded values of the time reference characteristic are used as standard as the boundaries for the validity area.<br /><br /><br /><strong>(G)Documentation</strong></p>\r\n<ul>\r\n<li>Please search for the term 'Non-Cumulatives' in the online help or directly use the following link: <a target=\"_blank\" href=\"https://help.sap.com/docs/SAP_NETWEAVER_750/0ecf5244825c4742a7b062a89d11c2ac/4a245d6fb1160456e10000000a421937.html\">Non-Cumulatives</a> (BW75).</li>\r\n<li>SAP First Guidance SAP NetWeaver BW 7.40 / 7.50 powered by SAP HANA:&#160;<a target=\"_blank\" href=\"http://go.sap.com/docs/download/2016/02/7656180b-617c-0010-82c7-eda71af511fa.pdf\">Inventory Handling and Non-Cumulative Key Figures</a></li>\r\n<li>Check out also the WIKI page: <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/BIFdEg\">Non-Cumulative Key Figures</a></li>\r\n<li>SAP First Guidance SAP BW/4HANA:&#160;<a target=\"_blank\" href=\"https://www.sap.com/documents/2019/04/2cd3175e-497d-0010-87a3-c30de2ffd8ff.html\">Inventory Handling and Non-Cumulative Key Figure</a></li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[II]Process Flow (Classic InfoCube)</strong></p>\r\n<p><br />In this chapter we always refer to a very simple example which should help to understand the process steps and definitions faster.</p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[II.a] Example</strong></p>\r\n<p><br /><strong>STEP 1:</strong><br />A stock initialization is performed on 06.05.2010 for the material A. The current stock value is 100 PC.<br /><strong>STEP 2:</strong><br />For this material there are also movements for calday &lt; 06.05.2010. Only the movements from the last 2 days before the initialization are extracted.<br /><strong>STEP 3:</strong><br />A day after the stock initialization a new movement is posted and loaded into the cube. The current stock is then 90.<br /><strong>STEP 4:</strong> Compression of stock initialization<br /><strong>STEP 5:</strong> Compression of historical data<br /><strong>STEP 6:</strong> Compression of new movements<br /><strong>STEP 7:</strong> Running queries<br /><br />relevant stock values in ERP(transaction MB5B):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"4\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>03.05.2010</td>\r\n<td>40 PC</td>\r\n</tr>\r\n<tr>\r\n<td>04.05.2010&#160;&#160;&#160;&#160;</td>\r\n<td>60 PC</td>\r\n</tr>\r\n<tr>\r\n<td>05.05.2010</td>\r\n<td>100 PC&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>06.05.2010</td>\r\n<td>100 PC</td>\r\n</tr>\r\n<tr>\r\n<td>07.05.2010</td>\r\n<td>90 PC</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Please note that the stock for a certain day always includes already all changes of this day. Hence, by looking at the table above, we know that e.g. on the 05.05. 40 PC must have been added and that 'nothing' happened on the 06.05..<br /><br /></p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[II.b] Loading</strong></p>\r\n<p><br />For Inventory InfoCubes there is a special loading process called stock initialization(regarding ADSOs see also chapter&#160;[IX]). It transfers the current non-cumulatives once from the source system to the BW system. With the initialization you are only able to post one initial value for each combination of characteristics of the data source. Afterwards you can start loading 'historical data' and new movements (changes) using a different data source:<br /><br />With 2LIS_03_BX the initialization of the current stock is carried out.<br />With 2LIS_03_BF you can load the 'historical data' and the new data (movements)<br /><br />For all data records of the stock initialization we have 0RECORDTP=1. They are called reference points (marker) and play an important role when the non-cumulative values are calculated during query runtime.<br />Since the stock initialization refers to a certain point in time we must distinguish between movements which happened before and after this moment. The former are called 'historical data', the latter are new movements. When the delta (not stock!) initialization is carried out, the historical data (back to a required date) can be loaded into the cube. (Its not allowed to have historical data and movements in one request..)<br /><br />Its not mandatory to carry out a stock initialization (but it is e.g. necessary in case not all movements exist any more in the source system or if you don't want to load all movements because of the amount of data). If you skip this and load only movements the system assumes that the inital stock is zero for all materials. New markers are then created whenever requests are compressed with 'new' materials.<br />Please review also point [III]Stock Initialization &amp; DTP.<br />The standard extractor for 2LIS_03_BX can also deliver records from the source system with stockvalues = 0. In order to activate this option you have to mark the flag \"Zero stock to be transferred\" in the transaction MCNB. Please be aware that this may lead to a considerable runtime increase.</p>\r\n<p>Inventory cubes need a consistent time dimension, if there are inconsistencies they must be repaired. The loading process terminates in case the time dimension&#160;contains already inconsistent data records, please see note <a target=\"_blank\" href=\"/notes/1772036 \">1772036</a> for further details. In order to&#160;minimize the probability of&#160;such inconsistencies, the system derives the values for all time characteristics of the provider&#160;from the values of the reference characteristic(e.g. 0calday)&#160;during the loading process. Hence, in the transformation only the time reference characteristic is mapped, all other time characteristics are not available in the target.<br /><br /><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;EXAMPLE</strong><br /><br /><strong>STEP 1: </strong>Loading of stock initialization<br />RequestID 1000<br />initialization: using 2LIS_03_BX<br />record:<br />material A -&gt; 100 PC (on 06.05.2010)<br /><br /><strong>STEP 2:</strong> Loading of historical data<br />RequestID 2000<br />historical data: using 2LIS_03_BF<br />records:<br />material A -&gt; 40 PC added on 05.05.2010<br />material A -&gt; 20 PC added on 04.05.2010<br /><br /><strong>STEP 3:</strong> Loading of new movements(deltas)<br />RequestID 3000<br />delta (new movement): using 2LIS_03_BF<br />record:<br />material A -&gt; 10 PC removed on 07.05.2010<br /><br /><br />F-table:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"4\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>requestid</strong></td>\r\n<td><strong>recordtp&#160;&#160;</strong></td>\r\n<td><strong>calday&#160;&#160;&#160;&#160;&#160;&#160;</strong></td>\r\n<td><strong>0ISSTOTSTCK&#160;&#160;</strong></td>\r\n<td><strong>0RECTOTSTCK</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2000</td>\r\n<td>0</td>\r\n<td>04.05.2010</td>\r\n<td>&nbsp;</td>\r\n<td>20 PC</td>\r\n</tr>\r\n<tr>\r\n<td>2000</td>\r\n<td>0</td>\r\n<td>05.05.2010</td>\r\n<td>&nbsp;</td>\r\n<td>40 PC</td>\r\n</tr>\r\n<tr>\r\n<td>3000</td>\r\n<td>0</td>\r\n<td>07.05.2010</td>\r\n<td>10 PC</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1000</td>\r\n<td>1</td>\r\n<td>31.12.9999</td>\r\n<td>&nbsp;</td>\r\n<td>100 PC</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Please note that the marker refers to the calender day 31.12.9999 and that recordtp is equal to 1&#8203;!<br /><br />E-table: still empty (we have not compressed yet)<br /><br /></p>\r\n<p>&#160;</p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[II.c] Compression &amp; Updating Reference Points</strong></p>\r\n<p><br />After the initialization the reference point represents the stock value for a certain material at the time of the initialization. If movements (not historical data) are loaded, the reference points get updated during compression of the requests if the flag <strong>'No Marker Update'</strong> (RSA1 -&gt; Manage-&gt;tab Collapse) is NOT set (this is the default setting). Hence, for a fully compressed cube, the reference point tells you the current stock value for a certain material. Regarding ADSOs see also chapter&#160;[IX].</p>\r\n<p>*******************&#160;&#160;&#160;&#160;&#160;&#160;<strong>Important&#160;&#160;&#160;&#160; </strong>*******************</p>\r\n<p>There is one very important exception regarding the update of the marker: if you compress historical data the reference points must not be adjusted !! Otherwise you get wrong data in the cube (since these movements are already included in the marker of the stock initialization)! Such inconsistencies cannot be repaired automatically, once the marker is wrong a reload of the data into the cube is necessary.<br /><br />Please also note that the request with the historical data must be compressed since the query 'can't know' that these records shouldn't get added to the marker (see Example: STEP 7 point(A))!<br /><br />******************************************************</p>\r\n<p><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;EXAMPLE</strong><br /><br /><strong>STEP 4:</strong> Compression of request 1000 (with marker update)<br /><br />F-table after compression:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>requestid</strong></td>\r\n<td><strong>recordtp</strong></td>\r\n<td><strong>calday</strong></td>\r\n<td><strong>0ISSTOTSTCK</strong></td>\r\n<td><strong>0RECTOTSTCK</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2000</td>\r\n<td>0</td>\r\n<td>04.05.2010</td>\r\n<td>&nbsp;</td>\r\n<td>20 PC</td>\r\n</tr>\r\n<tr>\r\n<td>2000</td>\r\n<td>0</td>\r\n<td>05.05.2010</td>\r\n<td>&nbsp;</td>\r\n<td>40 PC</td>\r\n</tr>\r\n<tr>\r\n<td>3000</td>\r\n<td>0</td>\r\n<td>07.05.2010</td>\r\n<td>10 PC</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>E-table after compression:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>requestid</strong></td>\r\n<td><strong>recordtp</strong></td>\r\n<td><strong>calday</strong></td>\r\n<td><strong>0ISSTOTSTCK</strong></td>\r\n<td><strong>0RECTOTSTCK</strong></td>\r\n</tr>\r\n<tr>\r\n<td>0</td>\r\n<td>1</td>\r\n<td>31.12.9999</td>\r\n<td>&nbsp;</td>\r\n<td>100 PC</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>STEP 5:</strong> Compression of request 2000 Historical Data (WITHOUT! marker update)<br /><br />F-table after compression:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>requestid</strong></td>\r\n<td><strong>recordtp</strong></td>\r\n<td><strong>calday</strong></td>\r\n<td><strong>0ISSTOTSTCK</strong></td>\r\n<td><strong>0RECTOTSTCK</strong></td>\r\n</tr>\r\n<tr>\r\n<td>3000</td>\r\n<td>0</td>\r\n<td>07.05.2010</td>\r\n<td>10 PC</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />E-table after compression:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>requestid</strong></td>\r\n<td><strong>recordtp</strong></td>\r\n<td><strong>calday</strong></td>\r\n<td><strong>0ISSTOTSTCK</strong></td>\r\n<td><strong>0RECTOTSTCK</strong></td>\r\n</tr>\r\n<tr>\r\n<td>0</td>\r\n<td>0</td>\r\n<td>04.05.2010</td>\r\n<td>&nbsp;</td>\r\n<td>20 PC</td>\r\n</tr>\r\n<tr>\r\n<td>0</td>\r\n<td>0</td>\r\n<td>05.05.2010</td>\r\n<td>&nbsp;</td>\r\n<td>40 PC</td>\r\n</tr>\r\n<tr>\r\n<td>0</td>\r\n<td>1</td>\r\n<td>31.12.9999</td>\r\n<td>&nbsp;</td>\r\n<td>100 PC</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong>STEP 6:</strong> Compression of request 3000 new deltas (with marker update)<br /><br />F-table after compression: empty<br /><br />E-table after compression:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>requestid</strong></td>\r\n<td><strong>recordtp</strong></td>\r\n<td><strong>calday</strong></td>\r\n<td><strong>0ISSTOTSTCK</strong></td>\r\n<td><strong>0RECTOTSTCK</strong></td>\r\n</tr>\r\n<tr>\r\n<td>0</td>\r\n<td>0</td>\r\n<td>04.05.2010</td>\r\n<td>&nbsp;</td>\r\n<td>20 PC</td>\r\n</tr>\r\n<tr>\r\n<td>0</td>\r\n<td>0</td>\r\n<td>05.05.2010</td>\r\n<td>&nbsp;</td>\r\n<td>40 PC</td>\r\n</tr>\r\n<tr>\r\n<td>0</td>\r\n<td>0</td>\r\n<td>07.05.2010</td>\r\n<td>10 PC</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>0</td>\r\n<td>1</td>\r\n<td>31.12.9999</td>\r\n<td>&nbsp;</td>\r\n<td>90 PC</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />As we already know, the marker is defined in such a way that it refers to the date 31.12.9999. For inventory cubes this date is exclusively reserved for the marker, its not allowed to use this date for movements!<br /><br />Even if you use 'No Marker Update = X' it may happen that the number of records with recordtp = 1 increase during compression. The reason is that the compression is adding marker records (its assumed that the stock value was zero before the change) for movements where no corresponding marker records exist (see also note 1795551)<br /><br /></p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[II.d]Calculation of Non-Cumulatives in a Query (Reporting)</strong></p>\r\n<p><br />The query calculates the stock for the requested points in time(t) by using the movements(inflow and outflow) and the marker.<br />It starts with the calculation of the current stock value (CSV): [Marker + (total delta (change,movement) of all uncompressed requests)]:<br />CSV = [M + SUM(deltas with requestid&gt;0)]<br />Then it calculates 'backwards' the stock value for the requested day(t):<br />Value(t) = CSV - SUM(deltas with time&gt;t)<br /><br /><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;EXAMPLE</strong><br /><br /><strong>STEP 7: </strong>Running Queries<br /><br /><strong>(A)</strong>query executed after <strong>STEP 4</strong> was carried out:<br /><br />0TOTALSTCK(on 04.05)&#160;&#160;= [100 (marker in E-table) + 50(uncompressed requests)] + 10(movement 07.05) - 40(movement 05.05) = 120 PC<br /><br />120 is wrong! The calculation of the marker is by 60 PCs too large since the query took into account the historical movements. Thats why you must always compress the request with the historical data records!<br /><br /><strong>(B)</strong>query executed after <strong>STEP 5</strong> was carried out:<br /><br />0TOTALSTCK(on 04.05) = [100 (marker in E-table) - 10(uncompressed request)] + 10(movement 07.05) - 40(movement 05.05) = 60 PC<br /><br /><strong>(C)</strong>query executed after <strong>STEP 6</strong> was carried out:<br /><br />0TOTALSTCK(on 07.05) = [90 (marker in E-table)]&#160;&#160;= 90 PC<br />Please note that in this case (fully compressed, current stock requested) the calculation was very simple ( and fast). The query had only to read the marker.<br /><br /></p>\r\n<p>&#160;</p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[II.e] Validity Table</strong></p>\r\n<p><br />By default the so called validity table contains only the time reference characteristic and the interval for which non-cumulative values are defined (and can be displayed in a query). The validity interval is automatically adjusted when new data is loaded into the cube (by default the minimal and the maximal value of the time reference characteristic of all entries which have been loaded into the infocube).<br />Please also see note 1501907 and the online help (see above).<br />It might be necessary to add an additional characteristic to the validity table, e.g if the validity interval should depend on the plant(or source system). Please only add such validity-determining characteristics if its really necessary since too many of them ( or a validity-determining characteristic with a lot of characteristic values) decreases the performance considerably.<br /><br />The validity table has the following form /BIC/L\"ICUBE\"(in case of the delivered objects, like the cube 0IC_C03,&#160;which&#160;have the prefix&#160;'0' the name is /BI0/L0IC_C03; (regarding ADSOs see also chapter&#160;[IX]). The query only takes into account data records where SID_0REQUID is equal to -1 (or -2). These records contain the relevant validity interval for the entire cube (which gets calculated by taking into account all request). You can change the validity independently of the data loaded into the cube, this can be done by using the transaction RSDV (see note <a target=\"_blank\" href=\"/notes/2062029\">2062029</a>). In such a case the corresponding record in the validity table has then SID_0REQUID=-2.<br /><br />If the cube is already filled then adaptation(remove/add validity objects) of the validity table is only possible with the report <strong>RSDG_CUBE_VALT_MODIFY</strong>.<br /><br />If the validity table is inconsistent, then rebuilding is possible using the function module <strong>RSDV_VALID_RECREATE</strong>. For the parameter I_REBUILD_FROM_FACT you can choose between two values:<br /><br />= space : recomputation of the values for request -1 by aggregation of all reporting relevant requests (by only reading of validity table itself)<br />= X&#160;&#160;: recomputation, complete facttables must be read<br /><br /><br /><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;EXAMPLE</strong><br /><br />In our example we would have the following entry in the L-table</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SID_0REQUID</strong></td>\r\n<td><strong>SID_0CALDAY_F</strong></td>\r\n<td><strong>SID_0CALDAY_T</strong></td>\r\n<td><strong>MODEFROM</strong></td>\r\n<td><strong>MODETO</strong></td>\r\n</tr>\r\n<tr>\r\n<td>-1</td>\r\n<td>20100504</td>\r\n<td>20100507</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />The record with SID_0REQUID=-1 is the relvant one for the OLAP engine. If we change this interval manually in the transaction RSDV to [04.05.2010 - 10.05.2010] the L-table gets an additional entry (with SID_0REQUID=-2):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SID_0REQUID</strong></td>\r\n<td><strong>SID_0CALDAY_F</strong></td>\r\n<td><strong>SID_0CALDAY_T</strong></td>\r\n<td><strong>MODEFROM</strong></td>\r\n<td><strong>MODETO</strong></td>\r\n</tr>\r\n<tr>\r\n<td>-1</td>\r\n<td>20100504</td>\r\n<td>20100507</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>-2</td>\r\n<td>0</td>\r\n<td>20100510</td>\r\n<td>&nbsp;</td>\r\n<td>F</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />A query (without any restrictions) where the infoobject 0calday is drilled down displays now the following result:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>calday</strong></td>\r\n<td><strong>0TOTALSTCK</strong></td>\r\n</tr>\r\n<tr>\r\n<td>04.05.2010</td>\r\n<td>60 PC</td>\r\n</tr>\r\n<tr>\r\n<td>05.05.2010</td>\r\n<td>100 PC</td>\r\n</tr>\r\n<tr>\r\n<td>06.05.2010</td>\r\n<td>100 PC</td>\r\n</tr>\r\n<tr>\r\n<td>07.05.2010</td>\r\n<td>90 PC</td>\r\n</tr>\r\n<tr>\r\n<td>08.05.2010</td>\r\n<td>90 PC&#160;&#160; &lt;--</td>\r\n</tr>\r\n<tr>\r\n<td>09.05.2010</td>\r\n<td>90 PC&#160;&#160; &lt;--</td>\r\n</tr>\r\n<tr>\r\n<td>10.05.2010</td>\r\n<td>90 PC&#160;&#160; &lt;--</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Please note that for the days 08-10 (extended part) the value from the day 07 was taken.<br />In case you have in addition to 0calday e.g. the infoobject 0plant as an validity object, the processing of the query gets more complicated. Lets assume we have the following validities:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>SID_0REQUID</strong></td>\r\n<td><strong>SID_0PLANT</strong></td>\r\n<td><strong>SID_0CALDAY_F</strong></td>\r\n<td><strong>SID_0CALDAY_T</strong></td>\r\n<td><strong>MODEFROM</strong></td>\r\n<td><strong>MODETO</strong></td>\r\n</tr>\r\n<tr>\r\n<td>-1</td>\r\n<td>1</td>\r\n<td>20100504</td>\r\n<td>20100507</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>-1</td>\r\n<td>2</td>\r\n<td>20100504</td>\r\n<td>20100512</td>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Please note that the validity intervals for the 2 plants are not identical. If non-cumulative key figures are aggregated across several validity characteristics it may be necessary to determine non-cumulative values outside the corresponding validity periods. For this the system determines a kind of superset of all involved validity periods, please see note 1501907 for more details to this. In case the validity object is drilled down, the system highlights the values which are outside the validity by setting of square brackets.<br />Lets discuss this again with the help of our example. We assume that the total stock is 75 PC for the plant 2 on the 10.05.2010. If we restrict a query to this day we get the following overall result:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>0TOTALSTCK</strong></td>\r\n</tr>\r\n<tr>\r\n<td>165 PC</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In case 0plant gets drilled down, the query displays the following</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>0PLANT</strong></td>\r\n<td><strong>0TOTALSTCK</strong></td>\r\n</tr>\r\n<tr>\r\n<td>1</td>\r\n<td>[90]</td>\r\n</tr>\r\n<tr>\r\n<td>2</td>\r\n<td>75</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Result</strong></td>\r\n<td><strong>165</strong></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Please note that the value 90 for plant 1 is actually outside of the validity and therefore displayed in brackets. If 0PLANT were restricted to the value 1 (hard filter under 'Characteristic Restrictions' which can't be changed during query navigation) the query wouldn't display the value in brackets but issue the message 'no applicable data found' (since an aggregation over different plants won't take place anyway).<br /><br />Important Remarks:</p>\r\n<ul>\r\n<ul>\r\n<li>The non-cumulative logic works only if no posted non-cumulative movements exist outside the validity interval!&#160;For more details see&#160;note <a target=\"_blank\" href=\"/notes/1501907\">1501907</a>.</li>\r\n<li>If there is no time restriction in the query and e.g. the time-reference characteristic is in the drilldown, the entire validity interval is displayed. Hence very big validity intervals may lead to performance problems in&#160;certain situations.&#160;In particular, it is not allowed to use the minimum and maximum value (99991231) in transaction RSDV (see note <a target=\"_blank\" href=\"/notes/2062029\">2062029</a>).</li>\r\n<li>Hence you also need to assure that the reference time characteristic is always filled with correct/plausible values (when data is loaded in the cube), in particular it should never be initial or have the maximal value. Please see notes&#160;<a target=\"_blank\" href=\"/notes/2223058\">2223058</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/1921893\">1921893</a>&#160;for further details.</li>\r\n<li>If there are more than one partprovider with&#160;non-cumulative key figures in a HCPR/Multiprovider, it can happen that values are displayed for dates outside the validity interval(such values are displayed in brackets[..]) - see&#160;<a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/3YM_FQ\">Validity Interval</a> for details.</li>\r\n<li>If there is no time restriction at all in the query and the time characteristic in the drilldown, then the requested non-cumulative key figures are calculated(and displayed) for all values of the validity time interval. In addition, the day before the lower bound of the validity interval is displayed,&#160;see&#160;<a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/3YM_FQ\">Validity Interval</a>&#160;for details.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[II.f] Working without Stock Initialization</strong></p>\r\n<p>In principle it is also possible to load stock data into an InfoProvider without carrying out a Stock Initialization. This is a valid approach when all movements are transferred since a certain stock value can always be calculated when all movements are available. In this case the reference points are created during the (first) compression and it it is assumed that the initial stock value was zero(which should be correct in this case). This is a general procedure, whenever a movement for a (new) material is loaded where no reference point exists, the system creates it(starting at 0) since this is technically necessary.<br />So, e.g. when the first request with movements contains the following records</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>MATERIAL&#160;</td>\r\n<td>Calday&#160;</td>\r\n<td>0ISSTOTSTCK&#160;</td>\r\n<td>0RECTOTSTCK&#160;</td>\r\n<td>0recordtp</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; &#160; A</td>\r\n<td>01.01.2019</td>\r\n<td>&#160; &#160; &#160; &#160; 5</td>\r\n<td>&#160; &#160; &#160; 10</td>\r\n<td>&#160; &#160; 0</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; &#160; A</td>\r\n<td>01.01.2019</td>\r\n<td>&#160; &#160; &#160; &#160;&#160;</td>\r\n<td>&#160; &#160; &#160; &#160; 3</td>\r\n<td>&#160; &#160; 0</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; &#160; B</td>\r\n<td>01.01.2019</td>\r\n<td>&nbsp;</td>\r\n<td>&#160; &#160; &#160; &#160;20</td>\r\n<td>&#160; &#160; 0</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>then (after compression) the provider contains the following reference points(0recordtp = 1):</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>MATERIAL&#160;</td>\r\n<td>0recordtp&#160;</td>\r\n<td>0RECTOTSTCK</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; &#160; A</td>\r\n<td>&#160; &#160; &#160;1</td>\r\n<td>&#160; &#160; &#160; &#160;8</td>\r\n</tr>\r\n<tr>\r\n<td>&#160; &#160; B</td>\r\n<td>&#160; &#160; &#160;1</td>\r\n<td>&#160; &#160; &#160; &#160;20</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Further compressions of requests concerning these materials will update these reference points(as always).<br /><br /></p>\r\n<p style=\"padding-left: 30px;\"><strong>[III] LIS and CDS Extractors for Non-Cumulatives</strong></p>\r\n<p>In SAP ERP or SAP S/4HANA (on-premise), there are two ODP-SAPI extractors that cover initial stock balances, historic movements and deltas: 2LIS_03_BX and 2LIS_03_BF. For revaluations, the LIS extractor 2LIS_03_UM can be used.<br />The extractor 2LIS_03_BX is used to load initial stock balances that are stored in SAP ERP, no material movements must be posted while the initial stock is extracted.The extractor 2LIS_03_BF reads historic material movements as well as delta movements.<br />Note that the Update Mode 'Unserialized V3-update' is not suitable for ODP extraction and must not be used. See SAP Note <a target=\"_blank\" href=\"/notes/2758147\">2758147</a> for details.<br />More details on LIS extractors and CDS extractors which are available as of SAP S/4HANA 2021:</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"http://www.sdn.sap.com/irj/scn/go/portal/prtroot/docs/library/uuid/50df1d69-4d86-2f10-a2ac-8b95b98dbe85?QuickLink=index&amp;overridelayout=true&amp;58815282193486\">SAP First Guidance &#8211; SAP Netweaver BW 7.30 on HANA Inventory InfoCubes</a></li>\r\n<li><a target=\"_blank\" href=\"https://www.sap.com/documents/2019/04/2cd3175e-497d-0010-87a3-c30de2ffd8ff.html\">Inventory Handling and Non-Cumulative Key Figure</a></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\"><br />[IV] Stock Initialization &amp; DTP</strong></p>\r\n<p>If you use a BW70 data source and a DTP in order to carry out the stock initialization, the following points have to be fulfilled:</p>\r\n<ul>\r\n<ul>\r\n<li>for the data source the setting 'Opening Balance' has to be active (field name STOCKUPD of table ROOSOURCE(source system) or RSOLTPSOURCE(BW3x)/RSDS(BW7x), respectivelly)</li>\r\n<li>the data target has to be a InfoCube with non-cumulative key figures(NCUMTIM in table RSDCUBE filled)</li>\r\n<li>only then the DTP offers the Extraction Mode 'Initial Non-Cumulative for Non-Cumulative values' which must be chosen in order to get the time reference characteristic and 0recordtp filled correctly</li>\r\n<li>also check whether the correction of note <a target=\"_blank\" href=\"/notes/2450881\">2450881</a> is available on the source system. If not, you need to replicate the datasource again after note implementation</li>\r\n<li>regarding ADSOs, see also chapter&#160;[IX]</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Remarks to Expert Routines in Transformations</strong>: in case an Expert Routine is used, you need to define manually(in the routine) how the technical charcteristic 0recordtp is supposed to be updated. When 0recordtp is set to the constant 9(which is recommended), the value is set according the the corresponding settings of the DTP. So, e.g. when executing a DTP for stock initialization, 0recordtp will be set to 1 for all records of the request. Or, if the DTP uses the setting 'Historical Transactions',&#160;0recordtp will be set to 2.<br />For transformations from non-cumulative ADSO to non-cumulative ADSO(DataMart scenario, see also below), the expectation is that the values of 0recordtp of the source records shall be taken over. In such a case, one request can even include records with all different values (0, 1, 2). When using an expert routine, you need to code this assignment in the routine accordingly.</p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\"><br />[V] Data Mart in BW7x</strong></p>\r\n<p>If you want to use the data mart interface to&#160;load data from an Inventory InfoProvider to another one, you should proceed as described in note <a target=\"_blank\" href=\"/notes/2325774\">2325774</a>.&#160;Below you can find three important points (mentioned there)&#160;you must not forget:</p>\r\n<ul>\r\n<ul>\r\n<li><span style=\"color: #333333;\">Don't forget to run&#160;the DTP with Extraction Mode &#8216;Initial Non-Cumulative for Non-Cumulative Values&#8217;: </span>if both, the target and the source cube contain non-cumulative key figures (system checks whether the field NCUMTIM in table RSDCUBE is filled) the DTP offers the extraction mode 'Initial Non-Cumulative for Non-Cumulative values'. This mode has to be taken for the stock initialization. Then the system reads automatically all records with 0RECORDTP=1 from the source and updates the data records correctly into the target.</li>\r\n<li>Non-Flat InfoCubes</li>\r\n<ul>\r\n<li>The source cube must be completely compressed, otherwise data consistency cannot be guaranteed!</li>\r\n<li>Execute compression of the request with 'historical&#160;movements' WITHOUT 'Marker Update'!</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[VI] Design Considerations and Performance &amp; Memory Consumption<br /></strong></p>\r\n<ul>\r\n<ul>\r\n<li>As for all basis cubes it must be observed that as many requests as possible are compressed (see note <a target=\"_blank\" href=\"/notes/590370\">590370</a>) For inventory cubes the system has to take into account all uncompressed request when the marker is calculated. This additionally contributes to a long runtime in case of uncompressed cubes.</li>\r\n<li>Please only use validity-determining characteristics if its really necessary since too many of them ( or a validity-determining characteristic with a lot of characteristic values) decreases the performance considerably. See also chapter [II.e].</li>\r\n<li>Its important to restrict the query as far as possible regarding time. This obviously improves the runtime by simply lowering the number of points in time for which ncum values have to be calculated. Keep in mind that less calculations during the query runtime also decrease memory requirements.</li>\r\n<li>In case of performance problems with queries retrieving/calculating many data cells, check whether the situation improves when you switch off the 'first/last compression' as described in note <a target=\"_blank\" href=\"/notes/2116993\">2116993</a>.</li>\r\n<li>A query with ncum key figures calculates the stock values at a certain point in time for all characteristic values in the drilldown(which meet the query filters except time restrictions) and not only for the values(e.g. materials) where there is a movement posted in the Infoprovider(see following&#160;<a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/Y4rIGQ\">Example</a>). Hence,&#160;when you want to estimate the number of records which have to be retrieved from the DataManager by a certain ncum query, you need to use (e.g.)<a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/JwByEg\">Transaction Listcube</a> and apply the formula 'The number of calculated stock values in query' = 'number of reference points' * 'number of requested points in time'. See <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/3gSIFQ\">Example</a> for details.</li>\r\n<li>The semantic key of the reference point table is formed by all characteristics of the Provider&#160;except for time characteristics. Consequently, the data volume of the reference points is&#160;usually by factors smaller than the one of the movements.&#160;This is only true, however, if no characteristics such as posting date is used in the ncum Provider. Such characteristics typically change day by day and this could mean,in the worst case, that every new movement triggers the creation of a new reference point during compression/activation(ADSO).&#160;Therefore, it is recommended to avoid 0PSTNG_DATE, 0UPD_DATE or similar InfoObjects in InfoProviders with non-cumulative key figures.&#160;</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[VII] Analysis of Problems</strong></p>\r\n<p><br />In case its necessary to look at a non_cumulative value in a query more closely, you could follow the step-by-step description below:<br /><br /><strong>1)</strong> Simplify query: this helps a lot to be fast and keep track of things</p>\r\n<ul>\r\n<ul>\r\n<li>identify a material and set a corresponding filter</li>\r\n<li>add 0CALDAY (or the time reference characteristic) to the free characteristics</li>\r\n</ul>\r\n<ul>\r\n<li>add time filters and focus on one certain point in time</li>\r\n</ul>\r\n<ul>\r\n<li>if necessary add validity characteristics to the drill down</li>\r\n</ul>\r\n<ul>\r\n<li>take all 3 relevant key figures (0TOTALSTCK, 0RECTOTSTCK, 0ISSTOTSTCK) in the structure (no restriced/calculated key figures)</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>2)</strong> Compare query result with LISTCUBE (see also <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/fJjHGg\">NCUM Example</a>&#160;and note <a target=\"_blank\" href=\"/notes/2471394\">2471394</a>)</p>\r\n<ul>\r\n<ul>\r\n<li>under 'field selections for output' choose material, the time reference characteristic, validity char., requestID, recordtp, 0isstotstck, 0rectotstck</li>\r\n<li>restrict material to the chosen value</li>\r\n<li>display the marker (0RECORDTP = 1) and remember the value (note that the reference points are only displayed when you explicitly set the restriction <strong>0RECORDTP</strong>=1! , see <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/JwByEg\">Transaction Listcube</a>)</li>\r\n<li>display the deltas (0RECORDTP = 0)and sort by time</li>\r\n<li>compare if cumulative key figures in query match the values in LISTCUBE (deltas have been posted ok)</li>\r\n</ul>\r\n<ul>\r\n<li>check how many records aren't already compressed (not added to the marker) and hence need to be used in the calculation of the current stock</li>\r\n</ul>\r\n<ul>\r\n<li>calculate the marker and check for correctness</li>\r\n</ul>\r\n<ul>\r\n<li>calculate the stock for requested point in time (see point [IV])</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>3a)</strong> if query and LISTCUBE results don't match, please search for relevant notes under the component BW-BEX-OT-NC. In case no note can be found, please contact SAP support with the technical name of the simplified query and the corresponding LISTCUBE report(see note <a target=\"_blank\" href=\"/notes/1479893\">1479893</a>).<br /><br /><strong>3b)</strong> if query and LISTCUBE results match, it means that the data is already wrong in the cube - continue with 4)<br /><br /><strong>4)</strong> check PSA tables for the data sources 2LIS_03_BX, 2LIS_03_BF and 2LIS_03_UM: do the values correspond with the LISTCUBE output?<br /><br /><strong>5a)</strong> if no, the data extraction from the source system has to be analyzed in detail. The corresponding component is BW-BCT-MM-IM (if the delivered data sources 2LIS_03_BF and 2LIS_03_BX are used), see also note 586163 in order to get more background information.<br /><br /><strong>5b)</strong> if yes, continue with 6a and 6b.<br /><br /><strong>6)</strong> typical issues:</p>\r\n<ul>\r\n<ul>\r\n<li>a) Did you compress the requests correctly with or without marker update ? In case a request was compressed with 'no marker update' you should find the following message in the SLG1 log: Loading of historical data switched on</li>\r\n<li>b) Did you carry out the stock initialization in a dtp with the extraction mode 'Initialize Non-cumulatives'?</li>\r\n</ul>\r\n<ul>\r\n<li>c) In case the query diplays no values at all please check the validity table</li>\r\n</ul>\r\n<ul>\r\n<li>d) Do you have characteristics in the drilldown that were not loaded by the data source 2LIS_03_BX (e.g. storage location)? Please see note <a target=\"_blank\" href=\"/notes/589024\">589024</a> and the Wiki page <a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/x/-WeKGw\">NC:Simple Example</a> for further details.</li>\r\n</ul>\r\n<ul>\r\n<li>e) Please pay attention to the order of the loaded requests. At first the initialization needs to be done (data source 2LIS_03_BX), then the movements can be loaded. Please see note <a target=\"_blank\" href=\"/notes/1795551 \">1795551</a> for further details.</li>\r\n<li>f) Selective deletion with time restrictions can lead to inconsistencies and is therefore not&#160;allowed, see note <a target=\"_blank\" href=\"/notes/2374993\">2374993</a>. See also note&#160;<a target=\"_blank\" href=\"/notes/2891757\">2891757</a> where you can find more information about the topic Selective Deletion &amp; 0RECORDTP.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p style=\"padding-left: 30px;\"><strong style=\"font-size: 14px;\">[VIII] HANA Optimized Inventory Cubes</strong></p>\r\n<p><br />In this chapter we want to briefly describe (from a technical point of view)&#160;the differences when BW is based on HANA. If the Inventory Cube is an HANA optimized cube, it is important to know the following points:</p>\r\n<ul>\r\n<ul>\r\n<li>Instead of setting the flag for Marker update during the compression of an InfoCube you now have to decide within the <strong>DTP</strong> (flag 'Historical Transactions') whether the records are treated to be historical transactions or deltas. That means the logic of the marker update had been pushed down to the data staging DTPs. Therefore, the flag 'No Marker Update' does not exists any longer.</li>\r\n<li>Historical transactions are stored (in an own partition) in the fact table and can be identified by <strong>SID_0RECORDTP = 2</strong>. So, there is a new value for 0recordtp, in the non-HANA based handling all historical transactions had been assigned to SID_0RECORDTP = 0.</li>\r\n<li>In contrast to the non-HANA based Inventory InfoCube, the HANA based Inventory InfoCube has only one F fact table. This F fact table itself is partitioned having in total 4 partitions:</li>\r\n<ul>\r\n<li>1 Compressed deltas</li>\r\n<li>2 Initialization records</li>\r\n<li>3 Historic transactions</li>\r\n<li>4 Uncompressed requests</li>\r\n</ul>\r\n<li>If you already migrated your database to HANA you can use transaction <strong>RSMIGRHANADB</strong> to convert your standard non-cumulative InfoCube into an InMemory optimized InfoCube. Please, also review the notes <a target=\"_blank\" href=\"/notes/1766577\">1766577</a> 'Converting non-cumulative InfoCubes: All requests compressed' and <a target=\"_blank\" href=\"/notes/1780575\">1780575</a>.</li>\r\n<li>There are three different modes (field <strong>NCUM_REFP_UPD </strong>in table <strong>RSDCUBELOC) </strong>of ncum-handling on HANA-optimized cubes (Please see note <a target=\"_blank\" href=\"/notes/2097535\">2097535</a> for a detailed description of theses modes:)</li>\r\n<ul>\r\n<li>' ' - without reference point update during compression</li>\r\n<ul>\r\n<li>This mode was introduced with the HANA-optimized cubes in 7.30 and either the field NCUM_REFP_UPD contains the value &#8216; &#8216; (space) or the field is not existing (BW73 systems), yet. In this mode there are no updates of the reference point during the compression.</li>\r\n<li>This has led to some performance problems during the calculation of the marker during query runtime, thus the 'X' mode has been introduced.</li>\r\n</ul>\r\n<li>'X' - with reference point update during compression</li>\r\n<ul>\r\n<li>This mode was introduced in 7.40 with the SP05. Cubes, which are created in this mode, have the value &#8216;X&#8217; stored in the field NCUM_REFP_UPD.</li>\r\n<li>There were still some problem during extraction from inffocubes and the identification of historical and new deltas, thus the 'Y' mode has been introduced.</li>\r\n</ul>\r\n<li>'Y' - with reference point update (Version 2) and 0RECORDTP=2 during compression</li>\r\n<ul>\r\n<li>This mode was introduced in 7.40 with the SP06. Cubes, which are created in this mode, have the value &#8216;Y&#8217; stored in the field NCUM_REFP_UPD. In this mode the reference point is updated during the compression, and the recordtp of all the movements, that are into the compressed request (requid=0), are changed to &#8216;2&#8217;.</li>\r\n</ul>\r\n</ul>\r\n<li>Hence&#160;it is&#160;recommended to use the newest one which is possible in the system:</li>\r\n<ul>\r\n<li>BW74 (SP&gt;=6) --&gt; use mode 'Y'</li>\r\n<li>BW74 (=&lt;SP5) --&gt; update to a current&#160;support package and use mode 'Y'</li>\r\n<li>BW73 --&gt; there is no choice, the ' ' blank version is used</li>\r\n</ul>\r\n<li>If you use BW74 (SP&gt;=6) and have ncum InfoCubes that were created with BW73 running on HANA, you can convert them using report <strong>RSDD_SWITCH_NCUM_HANDLING</strong>. After the usage of this report, the field NCUM_REFP_UPD in table RSDCUBELOC is set to Y, meaning that the newest(and recommended) handling is active. Please see note <a target=\"_blank\" href=\"/notes/2097535\">2097535</a> for a detailed description of the report.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>SDN Article</strong></p>\r\n<ul>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"http://www.sdn.sap.com/irj/scn/go/portal/prtroot/docs/library/uuid/50df1d69-4d86-2f10-a2ac-8b95b98dbe85?QuickLink=index&amp;overridelayout=true&amp;58815282193486\">SAP First Guidance &#8211; SAP Netweaver BW 7.30 on HANA Inventory InfoCubes</a></li>\r\n</ul>\r\n</ul>\r\n<p><strong>SAP Online Documentation:</strong></p>\r\n<ul>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw73/helpdata/en/af/81f38183cf46b9b919f82d7bb7ec47/frameset.htm\">Using the sap hana data base for SAP BW 7.3</a></li>\r\n<li><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw73/helpdata/en/e1/5282890fb846899182bc1136918459/content.htm\">SAP HANA-Optimized InfoCube</a></li>\r\n<li><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw73/helpdata/en/14/268a8602b241d0b6df1e6f3028c702/content.htm\">Converting Standard InfoCubes to SAP HANA-Optimized InfoCubes</a></li>\r\n<li><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw74/helpdata/en/4a/8f83b763e138dde10000000a42189b/frameset.htm\">SAP BW 7.4 Modeling Non-Cumulatives with Non-Cumulative Key Figures</a></li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong><br />[IX] Non-Cumulatives in Advanced DataStores</strong></p>\r\n<p>Please review the following documents which contain a comprehensive discussion of the handling and Non-Cumulative Key Figures (when BW is based on HANA), including Non-Cumulatives in Advanced DataStores:</p>\r\n<p>SAP First Guidance SAP NetWeaver BW 7.40 / 7.50 powered by SAP HANA<br /><a target=\"_blank\" href=\"http://go.sap.com/docs/download/2016/02/7656180b-617c-0010-82c7-eda71af511fa.pdf\">Inventory Handling and Non-Cumulative Key Figures</a></p>\r\n<p>SAP First Guidance SAP BW/4HANA<br /><a target=\"_blank\" href=\"https://www.sap.com/documents/2019/04/2cd3175e-497d-0010-87a3-c30de2ffd8ff.html\">Inventory Handling and Non-Cumulative Key Figure</a></p>\r\n<p><strong>Most important points you need to know</strong></p>\r\n<p>The following explanations refer to advanced DataStores that use modeling properties 'Activate data' and 'All Characteristcs are Keys, Reporting on Union of Inbound and Active Table'. <br />Its properties are comparable to an InfoCube. We only focus on the differences in comparison to Hana optimized Inventory Cubes.</p>\r\n<ul>\r\n<li>Reporting on such a DSO reads both, the inbound and the active data table &#8211; using a union across both tables. Activating a request transfers data from the inbound table to the active data table, thereby aggregating the data, which behaves very much like the collapse for InfoCubes. By default, advanced DSOs store characteristic values instead of SIDs.</li>\r\n<li>The following tables are generated (for advanced DataStores using the&#160;modeling properties specified above)<br />/BIC/A'Name'<strong>1</strong> Inbound Table <br />/BIC/A'Name'<strong>2</strong> Active Data Table <br />/BIC/A'Name'<strong>3</strong> Change Log <br />/BIC/A'Name'<strong>4</strong> Validity Table <br />/BIC/A'Name'<strong>5</strong> Reference Point Table</li>\r\n<li>New requests are loaded into the inbound table.</li>\r\n<li>Activating a request transfers the data from the inbound table to the active data table. The activation of requests is semantically very similar to the collapse of requests in InfoCubes.</li>\r\n<li>The change log is always created but not used in this case.</li>\r\n<li>The validity table stores the time interval(s) for which non-cumulative values have been loaded into the InfoProvider.</li>\r\n<li>The reference point table contains reference points for non-cumulative key figures. Unlike for InfoCubes, reference points are stored in a separate table for advanced DSOs.</li>\r\n<li>As for InfoCubes, for advanced DSOs the reference points stored with RECORD_TP='1'. Unlike for InfoCubes, they are not stored for the fixed date 9999-12-31 (infinity) but for the date as provided in the data record. Reference points are always updated whenever a request with delta movements is activated (collapsed).&#160;</li>\r\n<li>When delta movements are activated(collapsed), the record type is switched from '0' in the inbound table to '2' in the active data table.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong><br />[X]&#160; &#160; External Hana Views</strong></p>\r\n<p><span style=\"font-size: 14px;\">Please review the following documentation:</span></p>\r\n<ul>\r\n<li>SDN Wiki</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://wiki.scn.sap.com/wiki/display/BI/External+SAP+HANA+Views\">External SAP HANA Views</a></li>\r\n</ul>\r\n<li>SAP First Guidance SAP BW/4HANA</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://www.sap.com/documents/2019/04/2cd3175e-497d-0010-87a3-c30de2ffd8ff.html\">Inventory Handling and Non-Cumulative Key Figure</a>&#160;(chapter 4.1 and 4.2)</li>\r\n</ul>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>see above</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-OT-OLAP (Analyzing Data)"}, {"Key": "Other Components", "Value": "BW4-AE-CORE-NC (Non-cumulatives)"}, {"Key": "Other Components", "Value": "BW-WHM-DBA-ICUB (InfoCubes)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I022439)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001548125/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001548125/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001548125/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001548125/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001548125/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001548125/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001548125/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001548125/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001548125/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2891757", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-Cumulatives: Selective Deletion & 0RECORDTP", "RefUrl": "/notes/2891757"}, {"RefNumber": "2471394", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Incorrect/Unexpected Values displayed in Transaction LISTCUBE", "RefUrl": "/notes/2471394"}, {"RefNumber": "2374993", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-Cumulatives: Selective Deletion with Time Restrictions", "RefUrl": "/notes/2374993"}, {"RefNumber": "2325774", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Data Mart Extraction from Non-Cumulative (ncum)  InfoCubes and aDSOs", "RefUrl": "/notes/2325774"}, {"RefNumber": "2062029", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "BW Non-Cumulatives: Maintaining the Validity Period using transaction RSDV", "RefUrl": "/notes/2062029"}, {"RefNumber": "1921893", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Error Message \"The validity interval has the initial value as lower limit\"", "RefUrl": "/notes/1921893"}, {"RefNumber": "590370", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Too many uncompressed request (f table partitions)", "RefUrl": "/notes/590370"}, {"RefNumber": "589024", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "Reports in BW with storage location and stock type chars.", "RefUrl": "/notes/589024"}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163"}, {"RefNumber": "375098", "RefComponent": "BW-WHM-DBA-DMA", "RefTitle": "Data mart extraction from non-cumulative InfoCubes", "RefUrl": "/notes/375098"}, {"RefNumber": "2758147", "RefComponent": "BC-BW-ODP", "RefTitle": "Unserialized V3 update in conjunction with ODP is not suitable", "RefUrl": "/notes/2758147"}, {"RefNumber": "2450881", "RefComponent": "BC-BW-ODP", "RefTitle": "Additional SAPI-Attributes for ODP 1.0", "RefUrl": "/notes/2450881"}, {"RefNumber": "2116993", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Switch for data compression for first/last value exception aggregation", "RefUrl": "/notes/2116993"}, {"RefNumber": "1780575", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Conversion fails with error \"RSDRI_HDB 059\"", "RefUrl": "/notes/1780575"}, {"RefNumber": "1710236", "RefComponent": "HAN-DP-DXC", "RefTitle": "SAP HANA DXC: DataSource Restrictions", "RefUrl": "/notes/1710236"}, {"RefNumber": "1681396", "RefComponent": "BW-BEX-OT", "RefTitle": "Query Performance", "RefUrl": "/notes/1681396"}, {"RefNumber": "1591837", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "How to analyze query results", "RefUrl": "/notes/1591837"}, {"RefNumber": "1591487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1591487"}, {"RefNumber": "1501907", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cumulative cubes, validity interval,non-cumulative logic", "RefUrl": "/notes/1501907"}, {"RefNumber": "1479893", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW LISTCUBE improvements", "RefUrl": "/notes/1479893"}, {"RefNumber": "1160520", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA and non-cumulative queries", "RefUrl": "/notes/1160520"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2374993", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-Cumulatives: Selective Deletion with Time Restrictions", "RefUrl": "/notes/2374993 "}, {"RefNumber": "1795551", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "BW Non-Cumulatives: Issues when Compressing/Activating Requests containing Reference Points(0RECORDTP=1)", "RefUrl": "/notes/1795551 "}, {"RefNumber": "2961847", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "BW Non-Cumulatives: Error DBMAN 380 when Compressing Request", "RefUrl": "/notes/2961847 "}, {"RefNumber": "2900686", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Short Dump CX_RSR_X_MESSAGE during inventory ADSO data load", "RefUrl": "/notes/2900686 "}, {"RefNumber": "2891757", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-Cumulatives: Selective Deletion & 0RECORDTP", "RefUrl": "/notes/2891757 "}, {"RefNumber": "2635942", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Marker Update option not available in RSA1", "RefUrl": "/notes/2635942 "}, {"RefNumber": "3328088", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Stock values missing in query if validity until maximum", "RefUrl": "/notes/3328088 "}, {"RefNumber": "2808520", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "NCUM: Ignoring initial and max. values for validity", "RefUrl": "/notes/2808520 "}, {"RefNumber": "2678507", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "SAP ERP / S/4HANA Inventory Management in SAP BW/4HANA (Composite SAP Note)", "RefUrl": "/notes/2678507 "}, {"RefNumber": "2367703", "RefComponent": "BW-SYS-DB-DB4", "RefTitle": "IBM i: Condensing flat InfoCubes: Wrong Ncum-Handling and other corrections", "RefUrl": "/notes/2367703 "}, {"RefNumber": "1591837", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "How to analyze query results", "RefUrl": "/notes/1591837 "}, {"RefNumber": "1710236", "RefComponent": "HAN-DP-DXC", "RefTitle": "SAP HANA DXC: DataSource Restrictions", "RefUrl": "/notes/1710236 "}, {"RefNumber": "1681396", "RefComponent": "BW-BEX-OT", "RefTitle": "Query Performance", "RefUrl": "/notes/1681396 "}, {"RefNumber": "1591487", "RefComponent": "BW-BCT-RE-REFX", "RefTitle": "Non-cumulative key figures from 01.01.1900", "RefUrl": "/notes/1591487 "}, {"RefNumber": "1501907", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cumulative cubes, validity interval,non-cumulative logic", "RefUrl": "/notes/1501907 "}, {"RefNumber": "1479893", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW LISTCUBE improvements", "RefUrl": "/notes/1479893 "}, {"RefNumber": "590370", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Too many uncompressed request (f table partitions)", "RefUrl": "/notes/590370 "}, {"RefNumber": "375098", "RefComponent": "BW-WHM-DBA-DMA", "RefTitle": "Data mart extraction from non-cumulative InfoCubes", "RefUrl": "/notes/375098 "}, {"RefNumber": "1160520", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA and non-cumulative queries", "RefUrl": "/notes/1160520 "}, {"RefNumber": "589024", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "Reports in BW with storage location and stock type chars.", "RefUrl": "/notes/589024 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DW4CORE", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "DW4CORE", "From": "200", "To": "200", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "750", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}