{"guid": "6EAE8B2860E91ED69BBE0F44354740C1", "sitemId": "SI12: FIN_CO", "sitemTitle": "S4TWL - Technical Changes in Material Ledger with Actual Costing", "note": 2354768, "noteTitle": "2354768 - S4TWL - Technical Changes in Material Ledger with Actual Costing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition, higher or equal to on-premise 1610. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ML, Actual Costing, CKM3, CKMLCP, CKMLCPAVR, MLDOC, MLDOC_CCS, MLAST, T001W, MGVUPD, plant, WERKS_D, BWKEY, CKMLQS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Material Ledger Actual Costing has been activated already in the system before the system conversion to SAP S/4HANA.</p>\n<p>You can verify if Material Ledger Actual Costing is active for one or more plants via SAP Reference IMG / SAP Customizing Implementation Guide (transaction SPRO)</p>\n<p>-&gt; Controlling</p>\n<p>  -&gt; Product Cost Controlling</p>\n<p>    -&gt; Actual Costing/Material Ledger</p>\n<p>      -&gt; Actual Costing</p>\n<p>        -&gt; Activate Actual Costing</p>\n<p>        -- Activate Actual Costing</p>\n<p>=&gt; Checkbox 'Act.Costing' (Updating Is Active in Actual Costing)</p>\n<p>When this checkbox is marked, this means that Material Ledger Actual Costing is active in this plant.</p>\n<p>Technically this can be verified via database table T001W, field MGVUPD. 'X' means that Material Ledger Actual Costing is active in this plant.</p>\n<p>In addition, the table T001W shows the link between the plant and the valuation area.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With S/4HANA the data model for material ledger data has been changed significantly, especially when actual costing is active.</p>\n<p>The main purpose for the changed data model is:</p>\n<ul>\n<li><span>Simplified Data Model</span></li>\n<li><span>Simplification of Calculation Logic</span></li>\n<li><span>Leverage the Strength of HANA</span></li>\n</ul>\n<p><strong><strong>Business Process related information</strong></strong></p>\n<ul>\n<li><span>Reduce complexity and improve runtime in the Material Ledger Costing Cockpit for Actual Costing</span></li>\n<li><span>Example: 4 separate process steps ('Single-Level Price Determination', 'Multilevel Price Determination', 'Revaluation of Consumption', 'WIP Revaluation' ) are merged to one process step ('Settlement')</span></li>\n<li><span>New 2-dimensional distribution logic to avoids rounding errors</span></li>\n<li>\n<div class=\"O2\">Less \"not-distributed\" values</div>\n</li>\n<li><span>No lock conflicts caused by material movements (in current period)</span></li>\n<li><span>Change of standard price for materials and activities within the period is supported</span></li>\n</ul>\n<p><strong><strong>Required and Recommended Action(s)</strong></strong></p>\n<ul>\n<li>Deactivation of the statistical moving average is not mandatory in SAP S/4HANA, but is nevertheless recommended in order to achieve a significant increase of transactional data throughput for goods movements. It is important to note that the deactivation is not reversible. For further details regarding this matter please see description in SAP Support Note 2267835. With SAP S/4HANA, on-premise edition 1610, this option also includes materials with Moving Average price control and all special stock types. Additional details can be found in SAP Support Note 2338387.</li>\n<li>Separate currency customizing of Material Ledger (transactions OMX2 / OMX3) has become obligatory, Material Ledger is acting on a subset of the currencies defined for Financials. There is no default Material Ledger Type “0000” anymore.</li>\n<li>It is not allowed to use an ML Type that references to currency settings defined in FI or CO (flags “Currency Types from FI” resp. “Currency Types from CO”). Instead you have to define explicitly the currency and valuation types that are relevant for Material Ledger.</li>\n<li>During system conversion material ledger type '0000' (if existing) will be exchanged by new specific material ledger types. (Example Material Ledger Type 9300 for Currency Type 10 + 30)</li>\n<li>Steps to be executed for new valuation areas created after system conversion in S/4HANA: 1. Assign the currency and valuation types that are relevant for Material Ledger using transaction OMX2. 2. Afterwards assign this ML Type to your valuation area using transaction OMX3.</li>\n<li>Before system conversion is started, all Material Ledger costing runs, no matter if actual costing (transaction CKMLCP) or alternative valuation run (transaction CKMLCPAVR) need to be finished (e.g. step 'post closing' successfully executed, no error, no materials with status 'open'). Reason: After system conversion to SAP S/4HANA it will not be possible to do any changes on costing runs created before system conversion. You can create new costing runs for previous periods or for the current period, after data conversion in the new system, but it is important to have no incomplete costing runs, where just some steps have been started. This means, if you are doing a conversion in an open period (e.g. in the middle of a month), do not start a costing run in the old system on the old release, for this open period. You can create the costing run for this period later, after system conversion in the new system.</li>\n<li>It is not allowed to change Material Ledger costing runs, nor to run steps of Material Ledger costing runs during the process of system conversion.</li>\n<li>It is not allowed to activate or deactivate Material Ledger Actual Costing during the process of system conversion for one or more plants.</li>\n</ul>\n<p><strong>Details:</strong></p>\n<p><strong>Simplified Data Model</strong></p>\n<ul>\n<li><strong>Tables MLDOC and MLDOCCCS</strong></li>\n<ul>\n<li>The new Material Ledger Document tables MLDOC and MLDOCCCS replace most former periodic tables (MLHD, MLIT, MLPP, MLPPF, MLCR, MLCRF,<br/>MLKEPH, CKMLPP, CKMLCR, MLCD, CKMLMV003, CKMLMV004, CKMLPPWIP, CKMLKEPH). For more information refer to note 2352383.</li>\n<li>Some of the former periodic tables are still required for the following purposes:</li>\n<ul>\n<li>The Material Ledger Closing Document is stored in the former ML document tables (ML*)</li>\n<li>Standard Price. Periodic Unit Price and Price Control are still managed in table CKLMLCR</li>\n<li>Cost Component Split for Prices is still managed in tables CKMLPRKEKO and CKMLPRKEPH (But the values to Price Type 'B')</li>\n</ul>\n<li>The design of tables MLDOC/ MLDOCCCS allows storing both line item and aggregated data.</li>\n<li>Periodic transactions (goods movements, activity confirmation, etc.) update tables MLDOC/ MLDOCCCS with line item data using the INSERT ONLY principle.</li>\n<li>Material Ledger Period Close creates (run-dependent) settlement records in tables MLDOC/ MLDOCCCS to store the results from Actual Costing. These records are stored under the costing run key (field ‘Run Reference’) so that they are ‘visible’ only by the corresponding costing run. In this way different runs (e.g. a periodic run and an AVR) can store their results independently from each other.</li>\n<li>Important design changes\r\n<ul>\n<li>Single- and multilevel differences are no longer distinguished. In table MLDOC all price/exchange rate differences are stored in fields PRD/KDM. In table MLDOCCCS the CCS for price/exchange rate differences are stored under the CCS types E/F (field MLCCT).</li>\n<li>The CCS is stored in a table format using the cost component (field ELEMENT) as additional key. This allows increasing the number of cost components without modification.</li>\n<li>The CCS for preliminary valuation is always stored</li>\n</ul>\n</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li><strong>Tables MLDOC_EXTRACT and MLDOCCCS_EXTRACT</strong></li>\n<ul>\n<li>The table MLDOC_EXTRACT holds information about quantity and valuation changes. Transaction based updates, like goods movements or invoice receipts, usually update tables MLDOC and MLDOC_EXTRACT in parallel. But table MLDOC_EXTRACT can be compressed. After compression, the table will contain only one entry per cost estimate number, period and currency type valuation view. Table MLDOC_EXTRACT allows fast and efficient calculation of total quantity and total value, by cumulating all records for specific cost estimate number(s). The same is true for table MLDOCCCS_EXTRACT which has been designed for holding cost component information. During period shift (transaction MMPV) the tables MLDOC_EXTRACT and MLDOCCCS_EXTRACT will be compressed automatically for periods older than previous period. Manual compression via program FCML4H_RMLDOC_EXTRACT_COMPRESS or via function module FCML4H_MLDOC_EXTRACT_COMPRESS is possible and recommended in case of very high number of material movements.</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li><strong>Table MLRUNLIST</strong></li>\n<ul>\n<li>\n<p>Table MLRUNLIST replaces the former table CKMLMV011 (Object List for Costing Run), but has some additional functions:</p>\n<ul>\n<li>Materials and activities selected for a costing run are stored under the corresponding run id.</li>\n<li>It manages the period status of materials and activities</li>\n</ul>\n</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>Data Conversion</strong></p>\n<p>In case Material Ledger Actual Costing has been activated already in the system, in one or more valuation areas,  before the system conversion to SAP S/4HANA (when the system is e.g. on release <span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text27-__clone37\">SAP ERP 6.0 or on SAP Simple Finance)</span>, the tables MLDOC, MLDOCCCS, MLDOC_EXTRACT and MLDOCCCS_EXTRACT are filled automatically with data, during Migration Step M10: Migrate Material Ledger Master Data for the valuation areas, where Material Ledger Actual Costing has been activated. The data will be created starting from last period of year before previous year until the current period in the system. All material related goods movements or price changes from previous years will be converted to the MLDOC tables as if the goods movement or the price change has taken place in the last period of year before previous year. The data of the current year and of the previous year will be converted not based on single material documents or price change documents, but there will be one entry per period, per currency type and valuation view, per category (e.g. \"Receipts\"), per process category (e.g. \"Production\") and per production process.</p>\n<p>Data related to ML Alternative Valuation Runs is usually not converted automatically during Migration Step M10. It is possible to convert data related to ML Alternative Valuation Runs with report FCML4H_MIGR_AVR_INVENTORY (To be executed later-on in the S/4HANA system, after migration has been completed.). Only data related to \"COGM costing runs\" (costing runs in accordance with the business function FIN_CO_COGM/multiple valuation of cost of goods manufactured, that were created in the source system prior to the migration) is converted automatically during Migration Step M10: Migrate Material Ledger Master Data.</p>\n<p><strong>Functional changes/improvements</strong></p>\n<p><strong>Material Price Analysis (Transaction CKM3):</strong></p>\n<ul>\n<li>\n<div>No separate view for Cost Components, but integrated in main screen</div>\n</li>\n<li>\n<div>Flag for selection of cost components not relevant for material valuation, or only  cost components relevant for material valuation; by default selection of inventory relevant cost component split. This behavior can be enhanced by implementing SAP Support Note 2467398 (further details are described within this note)</div>\n</li>\n<li>\n<div>Display of WIP Reduction for material; by default WIP Reduction is hidden; If WIP reduction is displayed, both WIP reduction and consumption of WIP for order are shown in opposite sign in different folder.</div>\n</li>\n<li>\n<div>Plan/Actual Comparison is removed in new CKM3</div>\n</li>\n<li>\n<div>Technically, data are retrieved from table MLDOC, MLDOCCCS and MLDOC_EXTRACT and MLDOCCCS_EXTRACT</div>\n</li>\n</ul>\n<p>Remark: Data older than last period of year before previous year (related to current period in the system for a specific company code, at the time of system conversion) cannot be displayed via transaction CKM3 because the 'old' data has not been converted to the MLDOC-tables. To show data older than last period of year before previous year you can use transaction CKM3OLD('Material Price Analysis') or CKM3PHOLD ('Material Price History'). To display single material ledger documents, created before system conversion, you can also use transaction CKM3OLD. The system conversion will not convert single material ledger documents, but there will be one entry per category, process category and procurement alternative/process, with aggregated quantities, amounts and price/currency differences of single material ledger documents. So CKM3 will show data also on this level for migrated data.</p>\n<p>CKM3 will show all details on single material ledger documents for postings created after system conversion.</p>\n<p>CKM3OLD will not show postings created after system conversion</p>\n<p> </p>\n<p><strong>ML Actual Costing Cockpit (Transaction CKMLCPAVR):</strong></p>\n<ul>\n<li>﻿In the transactions CKMLCP and CKMLCPAVR, there is an additional parameter “Application”. This means that the application can be chosen so that it is possible to process Alternative Valuation Runs via the transaction CKMLCP and Actual Costing Runs via the transaction CKMLCPAVR.</li>\n<li>In the toolbar of the transactions CKMLCP and CKMLCPAVR, there is a new button next to the “Display &lt;-&gt; Change”-Button to switch the application from “Costing Run” to “Run Reference” and back.</li>\n<li>When the application is switched to “Run Reference”, a run reference can be created, changed or displayed. A run reference is a reference that contains all settings of an Alternative Valuation Run. It can be used when creating an Alternative Valuation Run but it is only mandatory for creating Alternative Valuation Runs for Parallel COGM. It can also be created for a single period run, for year-to-date run or for a rolling run.</li>\n<li>There are 2 options for creating an AVR:\r\n<ul>\n<li>Create a “Classic AVR” which is the same like before.</li>\n<li>Create an AVR with run reference which means the settings are taken from the run reference and can’t be changed. Only the plants must be chosen.</li>\n</ul>\n</li>\n<li>Some of the programs and therefore also the steps in the section “Processing” have changed. The new steps of an ML costing run are:\r\n<ul>\n<li>Selection (program FCML4H_SELECTION)</li>\n<li>Preparation (program FCML4H_PREPROCESSOR)</li>\n<li>Settlement (program FCML4H_SETTLEMENT)</li>\n<li>Post Closing (program FCML4H_POST_CLOSING)</li>\n</ul>\n</li>\n<li>It is no longer possible to use delta posting runs.</li>\n<li>Additionally to the status of the materials, the status of the activity types is displayed in the section “Processing”. The button “Activity Types” for displaying the activity types value flow has been removed.</li>\n<li>The section “Costing Results” has been removed. It will be replaced by the report FCML4H_RUN_DISPLAY_MATERIALS that displays a material list with status. It will called via the button “Results” in the section “Processing”.</li>\n</ul>\n<p> </p>\n<ul>\n<li>The classical CKMLCP steps <em>Single Level Price Determination, Multilevel Price Determination, Revaluation of Consumption </em>and <em>WIP Revaluation </em>have been replaced by the new step <em>Settlement</em> which essentially does all cost allocations and actual price calculations. Additionally the step <em>Preparation</em> is required to prepare the data (e.g. reading of apportionment structures, actual activity prices, cost sequence determination).</li>\n<li>A change of standard price for materials and activities within the period is now supported</li>\n<li>Easier reprocessing: If a material is reprocessed by settlement the depending materials on higher costing levels which need to be reprocessed are recognized automatically</li>\n<li>Consumption Price Differences (i.e. Price Differences updated in Consumption Folder) are now taken into account</li>\n<li>A new 2-dimensional distribution logic avoids rounding errors. The new ‘CKM3’ will match vertically and horizontally even on level of Cost Components</li>\n<li>Price Limiter Logic is accurate on level of Cost Component Split</li>\n<li>Materials and Activity Types are widely treated as equivalent objects. The cost allocation logic is essentially the same. The same reports (e.g. CKM3, CKMVFM) can be used for both of<br/>them (-&gt; to be enabled yet!)</li>\n<li>Alternative Valuation Run (AVR):\r\n<ul>\n<li>The new AVR do no longer copy the data into a separate key area. (Only the settlement records are stored under the AVR ‘Run Reference’, see above). In particular, <br/>the cumulation of several period is done ‘On-the-fly’.</li>\n<li>The former step <em>Cumulate Data</em> is no longer needed</li>\n<li>The complex rescale logic, which adjusts all materials data to a common preliminary AVR price, is no longer needed.</li>\n<li>All AVR can now handle WIP</li>\n<li>Post Closing:\r\n<ul>\n<li>Post Closing uses a ‘push logic’ (in accordance to the new Settlement). This means that the price differences rolled from an input to the output are now posted <br/>in the closing document of the input (material/activity)</li>\n<li>For activity types (and business processes) a new posting has been introduced. See the documentation in transaction OBYC for the new transaction key PRL: <br/><em>Price differences for activity types or business processes. During settlement in the material ledger, these price differences are posted from cost centers <br/>(Transaction/Account Modification GBB/AUI) to the material ledger (transaction PRL) and then further allocated to the receivers (finished products/WIP).</em></li>\n</ul>\n</li>\n</ul>\n</li>\n</ul>\n<p> </p>\n<p><strong>Restrictions</strong></p>\n<ul>\n<li>AVR Delta Posting Logic is not available</li>\n<li>\n<p>The BAdI BADI_ENDING_INV for external valuation is currently not available. Static external valuation (for ending inventory or cumulated inventory) based on table CKMLPR_EB is available with note 2613656</p>\n</li>\n<li>The BAdI CKMLAVR_SIM has been replaced by the BAdI FCML4H_MODIFY_VALUES. For more information refer to the following note:<br/>    2876478 Documentation for BAdI FCML4H_MODIFY_VALUES<br/><br/></li>\n<li>The BAdI CKML_SETTLE (see note 527278) is currently not available.<br/><br/></li>\n<li>Material Ledger Costing Runs and Alternative Valuation Runs, which have been created before the system conversion cannot be changed nor reversed any more after the conversion to SAP S/4HANA<br/><br/></li>\n<li>Material Ledger Valuated Quantity Structure (Transaction CKMLQS) is available after implementing the following notes (, or related support package): <br/>    2378468 Enable CKMLQS based on table MLDOC<br/>    2495950 CKMLQS: Connection to CKM3</li>\n</ul>", "noteVersion": 29, "refer_note": [{"note": "2378468", "noteTitle": "2378468 - Enable CKMLQS based on table MLDOC", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You would like to use transaction CKMLQS, but you receive error message \"This function is not supported by this Release\" (Message no. CKMLAVR099)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MLDOC MLDOCCCS MALENA CKMLMV003 CKMLMV004</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The data model has been changed for Material Ledger/Actual Costing on S/4HANA 1610.</p>\n<p>This note changes the program logic so that quantity structure data is read from table MLDOC.</p>\n<p>Please also implement SAP Support Note 2473447.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections attached to this note and also perform the manual actions.</p>\n<p>Restriction: It is not possible to show the quantity structure for Alternative Valuation Runs</p>", "noteVersion": 5, "refer_note": [{"note": "2473447", "noteTitle": "2473447 - CKMLAVR800: Not an alternative valuation run", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><PERSON>rror message \"This is not an alternative valuation run\"</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CKMLAVR 800, CKM3, CKMLQS, WIP, ACTWIP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The quantity structure contains WIP quantities,</p>\n<p>Program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached corrections.</p>", "noteVersion": 2}]}, {"note": "2338387", "noteTitle": "2338387 - S4TWL - Goods movements without exclusive locking by material valuation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>One major innovation of SAP S/4HANA in the area of MM-IM is the significant increase of transactional data throughput for high volume transactions, especially goods movements. To enable this from a technical point of view, it is necessary for the application logic to avoid exclusive locking on the application layer.</p>\n<p>Currently two different locking methods are available for goods movements:</p>\n<ul>\n<li>The first method locks all affected materials exclusively on their plant level during the entire document processing stage. This method offers the fastest document processing speed, since only a few locks need to be set and all required material data only needs to be read once for the whole process. The drawback of this method is a high lock collision probability for similar parallel postings. Moreover, this effect worsens with document size since every additional item extends the total exclusive locking duration and scope. This method is called “Exclusive block” in Customizing.</li>\n<li>The second method involves individual locking of material master and valuation data. Reduced exclusive locking times are used to minimize the collision probability between parallel postings. To achieve this, intended stock changes are communicated early in the process via the central enqueue server. All similar postings need to read these numbers from the enqueue server and take them into account along with the data read from the database. The drawback of this method is increased effort resulting in a longer total runtime for each document. This method is called “Late block” in Customizing.</li>\n</ul>\n<p>With SAP S/4HANA 1511 a new option to increase transactional data throughput for materials with Standard price control was introduced (for anonymous stock only). This option requires deactivation of the statistical moving average price and activation of “Late block”. With SAP S/4HANA 1610 this option also includes materials with Moving Average price control and all special stock types. The benefit of this option is that exclusive locking is no longer required for material valuation, which enables parallel material document processing. However, exclusive locking is still required if material master data is created or changed by a goods movement. Material master data is created by a goods movement if split valuation is used and a new batch has to be created, for instance. Changes of material master data concerns especially materials with Moving Average price control. When a goods movement changes the ratio between the stock value (SALK3) and the valuated stock quantity (LBKUM), the moving average price must be adjusted accordingly, which ultimately requires an exclusive lock until the V1 update. The quantity/value ratio is mainly changed by goods movements with an external value (for example, from the purchase order). However, if the change originates due to rounding differences only, the moving average price is not adjusted anymore. In addition, exclusive locking is still required, if SAP Note 1704534 (Deactivation of delivery costs for ML update) has been implemented.</p>\n<p>SAP S/4HANA for Oil &amp; Gas does also benefit from the new option starting with SAP S/4HANA 1709. For previous releases of SAP S/4HANA for Oil &amp; Gas the recommendation is to use “Exclusive block”.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The drawback of the new option described above is that temporary inconsistencies due to rounding differences may occur:</p>\n<ul>\n<li>Valuated stock quantity (LBKUM) = 0 and Stock Value (SALK3) &lt;&gt; 0</li>\n<li>Valuated stock quantity (LBKUM) &gt; 0 and Stock Value (SALK3) &lt; 0</li>\n<li>Valuated stock quantity (LBKUM) &lt; 0 and Stock Value (SALK3) &gt; 0</li>\n</ul>\n<p>In addition, the same inconsistencies can occur for value-only articles with VKSAL instead of LBKUM, if SAP for Retail is used. These inconsistencies are temporary because they are usually corrected by the next goods movement. Otherwise, transaction code MR23 (Adjustment of Inventory Value) is available to correct inconsistent stock values. You can find more information about the Adjustment of Inventory Value transaction in the SAP Help Portal.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>See SAP Note 2267835 for more information about the deactivation of statistical moving average price, which is the prerequisite to increase transactional data throughput as described above.</p>", "noteVersion": 4, "refer_note": [{"note": "1985306", "noteTitle": "1985306 - Performance guide for goods movements", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With parallel goods movements, the system often issues message M3 024: \"Valuation data for material &amp; is locked by the user &amp;\" or M3 023: \"Plant data is locked by the user &amp;\", and you would like to find out more about what is behind these messages.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Late block, OMJI, MBEW, MARC, EM07W, EM07M, M3024, M3023, M3897, goods issue, post goods issue, PGI, VL02N, block error, goods movement, background processing, BAPI_GOODSMVT_CREATE, TCURM-MBEQU = 2, V_TCURM_ENQUEUE, BAPI_OUTB_DELIVERY_CONFIRM_DEC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p class=\"p1\">Messages M3 024 and M3 023 are not program errors. Instead, they are informative notes for locks that are required from a business point of view in connection with a material for which different processes compete at the same time.</p>\n<p class=\"p1\">The purpose of the lock is to ensure the consistency of your stock quantities and stock values. Therefore, if one of the two messages mentioned above is issued, they are always justified and cannot be avoided. Otherwise, this would have an unwanted negative impact on your inventory valuation and subsequent postings in accounting.</p>\n<p class=\"p1\">Experience shows some customers have lots of untapped potential due to a lack of optimizations. This consulting note therefore gives you and your implementation partner guidance and tools to better understand the system behavior and to minimize the exclusive lock time for a material through customer-specific optimizations on the business process side or between the business process and the system side.</p>\n<p class=\"p1\">Your implementation partner or in-house consultant is responsible for optimizing or determining the ideal settings in accordance with your individual business processes</p>\n<p class=\"p1\">The task of SAP Development Support is to process errors in delivered standard software licensed by SAP. If you require support from SAP to optimize and align your business processes to the SAP software, especially in connection with points mentioned in this consulting note, contact your local SAP consulting organization.</p>\n<p class=\"p1\">For more information about contacting SAP, see the document <a href=\"https://support.sap.com/content/dam/support/en_us/library/ssp/my-support/incidents/sap-ecosystem.pdf\" target=\"_blank\">\"The SAP Eco-System in a Nutshell</a>\".</p>\n<p class=\"p1\">Please understand that we cannot process incidents regarding the topics listed below as part of SAP Development Support in accordance with SAP Note 83020.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This guide provides information on how the lock needed from a business point of view works in relation to the use of the late, exclusive lock (late lock) with goods movements, and identifies performance optimization opportunities, particularly with respect to goods issues.</p>\n<p><strong>1) Late lock for goods movements</strong></p>\n<p>You can use transaction OMJI or the Implementation Guide (SPRO), using the path \"Materials Management -&gt; General Settings for Materials Management -&gt; Set Material Block for Goods Movements\", to carry this out.</p>\n<p>The late block with goods movements specifies that a material is not always blocked exclusively. It is blocked exclusively only when you actually want to save the data, in order to keep the block time as short as possible. The advantage of this process is that several users can enter goods movements simultaneously because when you enter a movement, the system only sets a shared block for the material.</p>\n<p>To begin with, you can leave the waiting time of the late block at the default value of 10 seconds. The waiting time for the late block specifies the number of blocking attempts permitted before the system rejects the goods movement with a block error. When the late block is activated, the system attempts to block the material master record data directly before the update starts. This is necessary in order to ensure that postings are consistent.</p>\n<p>If it is not possible to set the block because, for example, another user is currently carrying out an update process with the same material, the system waits for a second and then attempts to set the block again.</p>\n<p>A higher value for the waiting time means that the system rejects fewer good movements. However, the waiting time for the user may be longer.</p>\n<p>The late block is mapped on the central lock server via the lock objects EM07M for the quantities, EM07W for the values and EM07C for the batch valuation. In this case, the master tables (for example, MBEW, MARC) have a shared block only.</p>\n<p><strong>2) The late block for the material master</strong></p>\n<p>You can use transaction OMT0 or the Implementation Guide (SPRO), using the path \"Logistics - General -&gt; Material Master -&gt; Make Global Settings\", to set this block.</p>\n<p>If the indicator for the shared block is set in the material master, plant and valuation data (table MARC or MBEW) is not blocked exclusively in material master maintenance - this data is subject to a shared block. The advantage of this is that you can perform goods movements at the same time as material master maintenance, as long as you do not change any valuation-relevant or inventory management-relevant fields (for example, base unit of measure, batch management requirement indicator, valuation category or price unit) in material master maintenance.</p>\n<p>If you try to change a valuation-relevant or inventory management-relevant field, the system reacts as follows:</p>\n<ul>\n<li>It blocks the plant and valuation data in material master maintenance exclusively in order to prevent simultaneous goods movements.</li>\n<li>It reads the plant and/or valuation data from the database again because it is possible that it has changed due to a goods movement between the beginning of material master maintenance and changing the affected field.</li>\n<li>If a goods movement takes place or has taken place simultaneously or directly beforehand, the system does not perform the required change and issues a message. If this happens, try to change the data at a later time.</li>\n</ul>\n<p><br/><strong>3) The late block for invoice verification</strong></p>\n<p>You can use the Implementation Guide (SPRO), using the path \"Materials Management -&gt; General Settings for Materials Management -&gt; Set Up Material Block for Invoice Verification\", to make the setting.</p>\n<p>In this step you determine the time when materials in invoice verification are blocked:</p>\n<ul>\n<li>During the assignment phase, the system blocks all the materials in the invoice. </li>\n<li>With simulation/posting, the system blocks and reads the materials whose stock needs to be changed.</li>\n<li>The system blocks and reads the materials whose stock needs to be changed only during posting.</li>\n</ul>\n<p>With direct posting to the material, the system blocks the specified material in all settings immediately. Likewise, if the material ledger is active, the material is blocked exclusively. If you use SAP S/4HANA, also refer to SAP Note 3044825.</p>\n<p>If you often post invoices parallel to goods movements, resulting in overlapping blocks, you can then set the block to come into effect with the simulation/posting only.</p>\n<p><strong>4) Price control and the effects on the late block</strong></p>\n<p>Use \"S\" price control for materials that you would like to post in parallel very often. During calculation of the values for the accounting document, it is always imperative from a business point of view to block the material (or the material assessment with the lock object EM07W) exclusively for a certain period. If the goods movement changes the ratio between the stock value (MBEW-SALK3) and the valuated stock (MBEW-LBKUM), the moving average price is adapted or changes have to be made to the previous period in a similar way, then an exclusive block is set until the V1 update is completed. If you use the \"S\" price control, the probability that the late, exclusive block is replaced by a shared block before the update begins is significantly higher than if you use moving average price control because the quantity/value ratio will probably not change. However, you must bear in mind that with \"S\" price control, the system valuates the material with the statistical moving average price in parallel as well. This means that, based on the statistical total value in alternative price control (MBEW-SALKV), the system creates a quantity/value ratio in relation to the quantity (MBEW-SALK3) in the same way. If this is influenced by the current update, it also results in the exclusive value block not being converted into the shared block before the update. In this way you definitely ensure that the statistical moving average prices are maintained correctly and checked periodically. Alternatively, SAP Note 139176 contains a report that you can use to set the statistical moving average price to the current standard price.</p>\n<p>You must also bear in mind that if, with a goods receipt, an external value (for example, from the purchase order) is specified that affects the quantity/value ratio of the statistical moving average price, the exclusive block is then compulsorily kept until the end of the V1 update program. Try to optimize your processes to ensure that, during peak times, you do not post goods receipts parallel to goods issue postings, in order to attain maximum goods issue performance.  </p>\n<p><strong>5) Performance optimization of customer-defined program source code</strong></p>\n<p>Ensure that, within the function modules MB_CREATE_GOODS_MOVEMENT and MB_POST_GOODS_MOVEMENT, you do not use any customer enhancements that cause an increase in the overall runtime (ABAP statements LOOP, SELECT). In addition to the exits &amp; BAdIs in inventory management, this also includes implicit/explicit enhancements and the output determination conditions.</p>\n<p>You can use transaction SE95 or the report SNIF to find activated exits/BAdIs. You can use the report MBFINDENHS to find enhancements (restriction to customer namespaces (for example, Z*) is advisable).</p>\n<p>Using a performance trace (transaction ST12 - see SAP Note 755977), you can also record the goods movement process and check all the activities between setting the exclusive value block (EEM07WE) and possibly converting it into a shared block for loss of performance.</p>\n<p><strong>6) To be borne in mind by SAP SCM APO with late block for goods movements</strong></p>\n<p>Bear in mind the application-specific settings for the integration with APO in the integration guide under \"Integration with Other SAP Components -&gt; Advanced Planning and Optimization -&gt; Application-Specific Settings and Enhancements -&gt; Settings and Enhancements for Stocks -&gt; Read Stock Values Before the Transfer to SAP APO\".</p>\n<p><strong>7) Optimization of the database update for the tables MBEW, MARD and MCHB</strong></p>\n<p>SAP Note 1737609 makes it possible to move the database lock for the update to the tables MBEW, MARD and MCHB in the update program to a later time. This is helpful if several processes on the database compete for blocking a data record/tuple of a table. In transaction OMJI or using the Implementation Guide (SPRO), use the path \"Materials Management -&gt; General Settings for Materials Management -&gt; Set Material Block for Goods Movements\" to activate the late database update. The system then performs the table updates only at the end of the update program, thus significantly reducing the total blocking time on the database for the data records involved, and therefore speeding up the parallel postings in the update program.</p>\n<p><strong>8) Individual fine tuning</strong></p>\n<p>If you have performed the above changes, it is advisable to use a mass test to determine the optimum waiting time/the number of blocking attempts from Point 1. There are no general recommendations for this because the value depends to a large extent on the type and constellation of the postings. Try to determine the best value in a realistic scenario by starting with a low value of 10 seconds and increasing it slowly until the terminations due to blocks decrease. In principle, you can increase the time to a maximum of 999 seconds. However, bear in mind that this significantly increases the time a dialog user waits until the block error, and that the runtime of the background job also increases.  You should therefore also adapt this value in line with the maximum resources available to you.</p>\n<p>If you would like to differentiate between the waiting time of a background user and a dialog user because, for example, the dialog user is to receive a block error after only 10 seconds but the background user is only to receive one after 180 seconds, you can use the BAdI in SAP Note 1840264 to achieve this. Also see the information in this SAP Note.</p>\n<p><strong>9) Special optimization with goods movements via delivery</strong></p>\n<p>If you post several parallel goods movements for a material in the same plant via several individual deliveries (transaction VL02N, VL32N or derivatives of them, such as BAPIs, IDocs), you can use the solution from SAP Note \"1776807 - Performance improvement for GI parallel processing\" to reduce the exclusive block time of the material. The SAP Note changes the processing sequence in the delivery update so that the decision to convert the exclusive value block (EM07W) to the shared block happens earlier which, in positive cases, results in the block being converted significantly earlier than before. One cause of long block times in a delivery could be, for example, an extremely excessive condition technique in delivery output determination. A performance trace, as described in Point 5, can provide information on this.</p>\n<p><strong>10) Blocks when using batches for goods issue</strong></p>\n<p>You are to post to the same batch in the event of parallel goods movements. Although the late block for goods movements has been activated (transaction OMJI), the system issues the message M3682 \"Batch &amp; of material &amp; is already locked by &amp;\". Batches are all exclusively blocked. SAP Note 1501121 provides a BAdI that you can use to control the shared lock for goods issue with batches. You may use the BAdI only if the business processes are coordinated in such a way that, during the goods movements, it is not possible to change batch data or classification data. This is because the shared block that is now set means that a simultaneous change cannot be prevented. The last save then applies.</p>\n<p>However, the transaction for the batch master (MSC2N) is an exception; this means that an exclusive block is still set that then also prevents a parallel goods movement.</p>\n<p>For transaction MIGO, you should additionally observe SAP Note 1523750, which is to be implemented in analogy to SAP Note 1501121 as the MIGO has an additional logic of its own for rejecting batch blocks.</p>\n<p><strong>11) Locks for Brazilian company codes</strong></p>\n<p>Historically, in Brazilian company codes, the plant segment (MARC) was locked exclusively when the nota fiscal was used, regardless of the system-wide customizing setting (see SAP Note 132193). SAP Note 3106950 removed this restriction. If error M3 897 \"The plant data of the material ... is locked by the user ...\" occurs despite the system-wide \"Late Lock\" setting in a Brazilian company code, you must implement SAP Note 3106950. <strong>You must always carry out the manual activity for explicit activation, regardless of the release/technical SAP Note implementation.</strong></p>\n<p><strong>12) Locks for goods movements in SAP S/4HANA</strong></p>\n<p><strong>Important:</strong> All the following SAP Notes are <strong>consulting notes</strong>. The instructions they contain <strong>must always be executed manually</strong>. Even if an SAP Note contains assignment to a support package, the manual activities always apply generally for all S/4HANA releases from On Premise edition 1610 or higher.</p>\n<p><strong>a) Posting without exclusive quantity/value lock</strong><br/>The lock logic in SAP S/4HANA has been completely redefined and, therefore, an option is available with SAP S/4HANA On Premise edition 1610 that enables no exclusive lock to be set with the late lock behavior. For more details, see SAP Notes 2319579 and 2267835. The new option for the late lock is activated by the report SAPRCKM_NO_EXCLUSIVELY_LOCKING if you have not done so yet.</p>\n<p><strong>b) Reduction of the runtime of a posting using pre-compaction</strong><br/>If you experience problems with the performance during the posting of goods movements, in particular with direct or indirect accesses to the tables MATDOC_EXTRACT or ACDOCA_M_EXTRACT, perform pre-compaction as described in SAP Notes 2246602 and 2342347 as a high priority. Pre-compaction is executed accordingly with the report FML_ACDOCA_M_EXTRACT_PRECOMP or NSDM_MTDCSA_PRECOMP.</p>\n<p><strong>13) Optimization of the individual business process</strong></p>\n<p class=\"p1\">Optimizing the alignment of your individual business process with the aforementioned system parameters requires extensive series of tests which you should define and execute together with your implementation partner or in-house consultant.</p>\n<p class=\"p1\">In general, we recommend that you keep the lock time of a material as short as possible. This is only possible if a single process takes up the lowest possible processing time. Ideally, you have already ensured this via the previous optimization steps. Otherwise, create a performance trace for your single process using transaction ST12 and try to identify and optimize the parts of the program with the highest runtime. This generally concerns message determination and/or customer-specific code (see point 5 or point 9). You can identify the message determination from the programs SAPLV61Z and/or SAPLV61A or from the condition tables in the trace. If these programs and customer-specific programs are no longer listed under the programs with the highest runtime, it can be assumed that the single process is optimized as far as possible.</p>\n<p class=\"p1\">The longer the process runtime of the single process, the longer the total lock time of a material automatically becomes and the less parallelization is possible. The quicker a posting process is ended, the faster the lock for a material is released again, and a parallel process can complete its posting.</p>\n<p class=\"p1\">Therefore, ideally no more than 250 posting lines should be included in one posting, even with posting processes optimized to the highest degree. Compare the process runtime per material document in your system with, for example: packages of 50, 125, or 250 lines and choose the ideal value according to your performance tests.</p>\n<p class=\"p1\">In general, though, no absolute recommendation can be made since this is always a customer-specific optimization process. Depending on the industry and individual requirements, each material has different attributes such as split valuation, batches, serial numbers, classification, and so on. In turn, each additional attribute contributes to the total runtime of the individual posting process.</p>\n<p><strong>14) Further support for process optimization</strong></p>\n<p class=\"p1\">If you have not yet reached a satisfactory result despite carefully processing the previous consulting and guidance, a holistic overall view of your system must be created. In addition to the points already listed in this SAP Note, our experience shows that the design of your system (sizing, operating system, basis settings) and other causes (database settings, configuration for table indexes or table buffers, release/patch level status, network communication, and so on) may also have a significant influence on the single process runtime, which in turn increases a material’s lock time.</p>\n<p class=\"p1\">As an Enterprise Support customer, you have the option of using additional specific services as part of the <a href=\"https://support.sap.com/en/offerings-programs/enterprise-support/enterprise-support-academy/continuous-quality-check-improvement-services.html\" target=\"_blank\">Continuous Quality Check &amp; Improvement Services</a> in order to have a corresponding check carried out. Examples include Business Process Improvement, Business Process Performance Optimization, EarlyWatch Check, or Technical Performance Optimization.</p>\n<p class=\"p1\">Alternatively, get in touch with your local contact person at SAP or read the document \"<a href=\"https://support.sap.com/content/dam/support/en_us/library/ssp/my-support/incidents/sap-ecosystem.pdf\" target=\"_blank\">The SAP Eco-System in a Nutshell</a>\" for further support from SAP.</p>", "noteVersion": 9}]}, {"note": "2352383", "noteTitle": "2352383 - S4TWL - Conversion to S/4HANA Material Ledger and Actual Costing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP ERP Business Suite or SAP Simple Finance to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Migration On-Premise</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>﻿Description</strong></p>\n<p>In SAP S/4HANA, the material ledger is mandatory. Therefore, migration of the material ledger is required in the following scenarios:</p>\n<ul>\n<li>Migration from SAP ERP to SAP S/4HANA 1610 or higher</li>\n</ul>\n<ul>\n<li>Migration from SAP Simple Finance to SAP S/4HANA 1610 or higher</li>\n</ul>\n<p>You <strong>always</strong> need to migrate the material ledger, even if you were not using the material ledger in the source system.</p>\n<p> </p>\n<p><strong>Required Action(s)</strong></p>\n<p>Before starting the conversion, please make sure to implement relevant SAP Notes that are mentioned in SAP Note 2345739.</p>\n<p> </p>\n<p><strong>Customizing Migration </strong></p>\n<p>Before migrating the material ledger, you need to complete the following IMG activities under <em>Migration to SAP S/4HANA Finance</em>.</p>\n<p>Under <em>Preparations and Migration of Customizing</em>:</p>\n<ul>\n<ul>\n<li><em>Preparations and Migration of Customizing for General Ledger </em>(These activities only need to be executed if you are migrating from SAP ERP Business Suite to SAP S/4HANA. If you are already using SAP Simple Finance 2.0 or higher, they are not relevant.)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li><em>Preparations and Migration of Customizing for Material Ledger -&gt; Migrate Material Ledger Customizing (</em>This activity <strong>always</strong> needs to be executed, even if you are migrating from SAP Simple Finance to SAP S/4HANA.)</li>\n</ul>\n</ul>\n<p><strong>Data Migration</strong></p>\n<p>To start the material ledger migration activities, go to the IMG and navigate to <em>Migration to SAP S/4HANA Finance -&gt; Data Migration</em> and start the activity <em>Start and Monitor Data Migration</em>.</p>\n<p>For further information, see the IMG documentation on the <em>Start and Monitor Data Migration</em> activity.</p>\n<p>The currency conversion in step M10 and M11 is done via standard exchange rate type 'M'.</p>\n<p>The process of migrating the material ledger consists of the following activities in the <em>Start and Monitor Migration</em> activity:</p>\n<ul>\n<ul>\n<li><strong>M10: Migrate Material Ledger Master Data</strong></li>\n</ul>\n</ul>\n<p>This activity ensures that the material ledger is activated for all valuation areas, since it is mandatory in SAP S/4HANA. The activity creates material ledger master data (tables: CKMLHD, CKMLPR, CKMLPP and CKMLCR) in all material ledger currencies for periods more recent than the last period of the previous year.</p>\n<p>In addition, all existing inventory aggregate values (tables MBEW, EBEW, QBEW, OBEW) and their complete historic data (tables MBEWH EBEWH, QBEWH, OBEWH) are migrated into the new universal journal entry table ACDOCA and ACDOCA_M_EXTRACT. Period records more recent than the last period of the previous year are converted into material ledger currencies using standard exchange rate type 'M'. Periods earlier than the last period of the previous year are only migrated in home currency. During migration, balance carryforward records (fields: BSTAT = ‘C’ and MIG_SOURCE = ‘N’) are created in ACDOCA. If you are already using the material ledger, existing material ledger records (tables CKMLPP and CKMLCR) are transferred into ACDOCA and ACDOCA_M_EXTRACT with all existing currency information.</p>\n<p>This migration activity does <strong>not</strong> activate actual costing, since actual costing is still optional in SAP S/4HANA. However, if you are already using actual costing in the migration source system, ML data for actual costing will be transferred to new data structures to enable fast and efficient cost calculation. In particular, the following database tables will be filled for periods more recent than the last period of the year before the previous year until the current period.</p>\n<p><strong>Attention:</strong> The relevant periods are determined via table MARV (fields VVJGJ, VVJMO, LFGJA, LFMON). Make sure that data in MARV is correct. Do not transport table entries of table MARV from different systems, where MARV might not be up-to-date. This can lead to severe data inconsistencies.</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Table Name</strong></p>\n</td>\n<td>\n<p><strong>Description</strong></p>\n</td>\n<td>\n<p><strong>Replacement for</strong></p>\n</td>\n<td>\n<p><strong>Purpose</strong></p>\n</td>\n<td>\n<p><strong>Remark for Migration</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>MLDOC</p>\n</td>\n<td>\n<p>Material Ledger Document</p>\n</td>\n<td>\n<p>MLHD, MLIT, MLPP, MLPPF, MLCR, MLCRF, CKMLPP, CKMLCR, MLCD, CKMLMV003, CKMLMV004, CKMLPPWIP etc.</p>\n</td>\n<td>\n<p>Optimized data structure for ML actual costing. Will be  filled during transactional update of ML data and during ML settlement.</p>\n</td>\n<td>\n<p>During step M10, data in MLDOC is created from the last period of the previous year to the current period for all valuation areas where actual costing is activated.</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>MLDOCCCS</p>\n</td>\n<td>\n<p>Material Ledger Document Cost Component Split</p>\n</td>\n<td>\n<p>MLKEPH, CKMLKEPH, (CKMLPRKEKO)</p>\n</td>\n<td>\n<p>Optimized data structure for cost component split in ML actual costing</p>\n</td>\n<td>\n<p>During step M10, cost component split data are created based on MLDOC entries.</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>MLDOC_EXTRACT</p>\n</td>\n<td>\n<p>Extract of Material Ledger Document</p>\n</td>\n<td>\n<p>Similar to MLDOC, but contains only information about quantity and value. It is possible to compress this table to one entry per period and cost estimate number via report FCML4H_MLDOC_EXTRACT_COMPRESS.</p>\n</td>\n<td>\n<p>During migration, one entry is created for each combination of cost estimate number, currency type, period, category, and process category.</p>\n</td>\n<td></td>\n</tr>\n<tr>\n<td>\n<p>MLDOCCCS_EXTRACT</p>\n</td>\n<td>\n<p>Extract of Material Ledger Document Cost Component Split</p>\n</td>\n<td>\n<p>-</p>\n</td>\n<td>\n<p>Similar to MLDOC_EXTRACT but with information per cost component. Tables MLDOC_EXTRACT and MLDOCCCS_EXTRACT will be used especially to calculate information about beginning inventory in specific period, by cumulating quantities and values from all previous periods.</p>\n</td>\n<td></td>\n</tr>\n<tr>\n<td>\n<p>MLRUNLIST</p>\n</td>\n<td>\n<p>Object List for Costing Run</p>\n</td>\n<td>\n<p>CKMLMV011, status in CKMLPP</p>\n</td>\n<td>\n<p>Information about status of materials and activity types in ML costing runs</p>\n</td>\n<td>\n<p>During migration, table MLRUNLIST is filled based on costing runs created in the start release.</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Note</strong></p>\n<p>For all ML costing runs created in the start release, the <em>Post Closing</em> step needs to be finished. It will not be possible to process costing runs created earlier in the new release.</p>\n<p>The Posting Logic of the new Actual Costing has been changed in some details. These changes require the following adjustments in the Account Determination (Transaction OBYC or IMG -&gt; Materials Management -&gt; Valuation and Account Assignment -&gt; Account Determination -&gt; Account Determination Without Wizard -&gt; Configure Automatic Postings).</p>\n<ul>\n<ul>\n<ul>\n<li>\n<div>Transaction PRL (Activity Price Differences):</div>\n</li>\n</ul>\n</ul>\n</ul>\n<p>This transaction was introduced with SAP S/4HANA 1610. It is used for the offsetting posting of the cost center credit posting (Transaction/Account Modification GBB/AUI). Maintain the rules and posting key for transaction PRL. Then assign the accounts that will receive the activity price differences credited to the cost centers. From SAP S/4HANA 1610 all activity related postings are performed with valuation class &lt;blank&gt;. It is therefore not necessary to activate the Valuation Modification in the rules of transaction PRL.</p>\n<ul>\n<ul>\n<ul>\n<li>\n<div>Transaction GBB/Account Modification AUI:</div>\n</li>\n</ul>\n</ul>\n</ul>\n<p>From SAP S/4HANA 1610 all activity related postings are performed with valuation class &lt;blank&gt;. If you have activated the Valuation Modification in the rules of transaction GBB, make sure to create an account assignment entry for General Modification AUI and Valuation Class &lt;blank&gt; also.</p>\n<ul>\n<ul>\n<ul>\n<li>\n<div>Transactions PRV and KDV:</div>\n</li>\n</ul>\n</ul>\n</ul>\n<p>These transactions are obsolete with SAP S/4HANA 1610 (since the new Actual Costing no longer distinguishes between single-level and multilevel variances).<br/>Their account assignment entries may be removed.</p>\n<ul>\n<ul>\n<li><strong>M20: Check Material Ledger Master Data</strong></li>\n</ul>\n</ul>\n<p>After performing the material ledger master data migration activity, this activity checks and verifies the migrated data. For instance, existing values from the inventory and material ledger tables are compared with aggregation via table ACDOCA.</p>\n<p>Check the error messages. Usually no adjustment is necessary for insignificant errors, such as those from time periods long in the past or from company codes that are not productive. If you have any doubts, discuss them with your auditor. You have the option to accept errors that are not to be corrected After you have accepted all errors, you can continue with the next migration step. If anything was adjusted, you can reset the run and repeat it.</p>\n<ul>\n<ul>\n<li><strong>M11: Migrate Material Ledger Order History</strong></li>\n</ul>\n</ul>\n<p>If the material ledger was not active in any valuation area before SAP S/4HANA conversion, this actvity ensures that all existing purchase order history table records (tables EKBE, EKBEH, EKBZ, EKBZH) and production order history table records (tables MLAUFCR and MLAUFCRH) are converted into the material ledger currencies using standard exchange rate type 'M'.</p>\n<ul>\n<ul>\n<li><strong>M21: Check ML Production Order and Purchase Order History</strong></li>\n</ul>\n</ul>\n<p>This activity verifies whether all production and purchase order history records have been converted into the material ledger currencies.</p>\n<p><strong>Note</strong></p>\n<p>If you want to minimize the amount of data to be migrated, archive or delete any data that is no longer needed. This will decrease the migration downtime. The relevant tables are the following:</p>\n<ul>\n<ul>\n<li>Inventory valuation tables: MBEWH, EBEWH, QBEWH, OBEWH</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Material ledger inventory tables (only relevant if material ledger was active before SAP S/4HANA migration): CKMLPP, CKMLCR</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Purchase order history tables: EKBE, EKBEH, EKBZ, EKBZH</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Production order history tables: MLAUFCR, MLAUFCRH</li>\n</ul>\n</ul>\n<p><span>  </span></p>\n<p>The migration activities are parallelized using background jobs. Make sure that enough background jobs are available.</p>\n<p>During parallelization, a standard package size of 20,000 is used in all material ledger migration activities. You may adapt this size by setting user parameter FML_MIG_PKG_SIZE.</p>\n<p>This makes sense e.g. in case step M10 causes issues in Physical Memory of the system, like Runtime Error ‘TSV_NEW_PAGE_ALOC_FAILED’ (SAPLCKMS).</p>\n<p>To do so go to transaction SU3 / Tab \"Parameters\" -&gt; set parameter FML_MIG_PKG_SIZE to value 5000 (example) for the user, who is running step M10 in transaction FINS_MIG_STATUS.</p>\n<p>After that Step M10 can be repeated in transaction FINS_MIG_STATUS -&gt;‘Control’ Tab -&gt; set Cursor on line with step M10 -&gt; press button ‘Next Activity’ -&gt; Resume Migration.</p>", "noteVersion": 9}, {"note": "2267835", "noteTitle": "2267835 - S4TWL - Material Valuation - Statistical moving average price", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>One major innovation of SAP S/4HANA in the area of MM-IM is the significant increase of transactional data throughput for high-volume transactions, especially goods movements. To enable this from a technical point of view, it is necessary for the application logic to avoid exclusive locking on the application layer.</p>\n<p>For goods movements changing the valuated inventory, the system uses exclusive locks to calculate a consistent change in inventory value in situations where concurrent transactions for the same material are ongoing. But, the need for setting exclusive locks is mainly driven by the inventory valuation method:</p>\n<ul>\n<li>An inventory valuation following the \"Standard Price\" method does (with a few exceptions) not require to set an exclusive lock. This is because the relationship between unit costs for material inventory (standard price) on the one hand, and inventory quantity and inventory value on the other hand, is kept constant during goods movements.</li>\n<li>Whereas an inventory valuation following the \"Moving Average Price\" method often requires to set an exclusive lock. This is because the relationship between unit costs for material inventory (moving average price) on the one hand, and inventory quantity and inventory value on the other hand, is often changing during goods movements.</li>\n</ul>\n<p>In SAP ERP, for materials with price control \"Standard\" the system calculates the inventory valuation following the \"Standard Price\" method mentioned above. Additionaly, it does an inventory valuation following the \"Moving Average Price\" method in parallel, the so-called \"statistical\" moving average price. Unfortunately, this \"statistical\" moving average price valuation requires exclusive locking as well.</p>\n<p>As mentioned at the beginning, exclusive locking limits the transactional data throughput, and therefore needs to be avoided to increase transactional data throughput significantly for scenarios with a high volume of transactions.</p>\n<p>If you do a system conversion to SAP S/4HANA on-premise edition, you have the <strong>option</strong> to change locking behavior during goods movements to increase transactional data throughput. As said, this is not mandatory for you, but nevertheless recommended in order to achieve a significant increase of transactional data throughput for goods movements.</p>\n<p><strong>If you select option</strong> to increase your transactional data throughput during goods movements, this will have following effects:</p>\n<ul>\n<li>Materials with price control 'S': System will set (with a few exceptions) only a shared lock during goods movements.</li>\n<li>Materials with price control 'V': System will set an exclusive lock during goods movements in release SAP S/4HANA 1511. Beginning with release SAP S/4HANA 1610, system will set an exclusive lock during goods movements only in dedicated situations, for other situations it sets only a shared lock (see OSS note 2338387 for details).</li>\n<li>As described before, setting only a shared lock for materials with price control 'S' is in conflict with concept of calculating \"statistical\" moving average price, which would require an exclusive lock. Therefore, to gain the increase of transactional data throughput, <strong>system deactivates \"statistical\" moving average price and does not calculate it anymore</strong>.</li>\n<li>In addition, the following database table fields are affected by the deactivation of the \"statistical\" moving average price:</li>\n<ul>\n<li>In table MBEW, EBEW, OBEW, QBEW the fields SALKV and VERPR are not updated anymore for materials with price control “Standard”. The same applies to the history tables MBEWH, EBEWH, OBEWH and QBEWH.</li>\n<li>In table CKMLCR the fields PVPRS and SALKV are not updated anymore for materials with price control “Standard”.</li>\n<li>In table MLCR the fields SALKV, SALKV_OLD and PVPRS_OLD are not updated anymore for materials with price control “Standard”.</li>\n<li>If Actual Costing is used, the above mentioned fields will still be updated with the periodic unit price during the Actual Costing Closing Steps.</li>\n</ul>\n<li>As a consequence of not updating the above-mentioned fields, the \"statistical\" moving average price and the value based on the \"statistical\" moving average price are no longer available on the user interface. This concerns the following transaction codes: MM01, MM02, MM03, CKM3 and MR21.</li>\n<li>It is important to note that selecting this option <strong>can not be un-done</strong>.</li>\n</ul>\n<p><strong>If you do not select option</strong> to increase your transactional data throughput during goods movements, this will have following effects:</p>\n<ul>\n<li>Locking behaviour during goods movements is the same you know from SAP ERP. Means, exclusive locks are set during goods movements and transactional data throughput is limited.</li>\n<li>Materials with price control 'S': <strong>system still calculates \"statistical\" moving average price</strong> in parallel.</li>\n</ul>\n<p> </p>\n<p><strong>Business Process related information</strong></p>\n<p>As its name implies, the \"statistical\" moving average price is purely statistical and does not have any impact on actual financials-relevant valuation.</p>\n<p>Without the \"statistical\" moving average price, an alternative way of valuation needs to be used in few scenarios. This concerns, for instance, Balance Sheet Valuation and Product Cost Planning, where selection/valuation variants must be checked and adapted.</p>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>If within custom code the fields mentioned above under “Description” are evaluated for materials with price control “Standard”, then this usage should be removed. One possible reaction to this change could be to use instead of the V-Price (VERPR) either the S-Price (STPRS) or the V-Price (VERPR) depending on the configured price control for the respective material.</p>\n<p>To identify such locations, it is required to make use of the where-used functionality of transaction code SE11 and to consider other techniques like transaction code CODE_SCANNER to find locations which SE11 cannot handle – such as dynamic programming or native SQL statements.</p>\n<p>Finally, if you like to select option to increase your transactional data throughput during goods movements and you accept the drawback that \"statistical\" moving average price gets deactivated, you can do the following:</p>\n<ul>\n<li>In SAP S/4HANA 1511 and SAP S/4HANA 1610 execute report SAPRCKM_NO_EXCLUSIVELY_LOCKING. As changes in customizing table TCURM are not recorded in a customizing request, you have to run report in each of your systems (customizing, development, production) separately.</li>\n<li>Beginning with SAP S/4HANA 1709 you can use IMG activity \"Set Material Lock for Goods Movements\" (SPRO -&gt; Materials Management -&gt; General Settings for Materials Management).</li>\n</ul>\n<p>As said, this <strong>is optional</strong> for converted systems running the S/4HANA on-premise edition.</p>\n<p><strong> </strong></p>\n<p><strong>Further Remarks</strong></p>\n<p>In the S/4HANA cloud edition or new installs of the SAP S/4HANA on-premise edition, the option to increase transactional data throughput during goods movements is mandatory and being set automatically. Deactivation of \"statistical\" moving average price is therefore also mandatory there.</p>", "noteVersion": 5, "refer_note": [{"note": "2277568", "noteTitle": "2277568 - Activation of locking behavior \"no exclusively locking\" deprecates statistical moving average price", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Statistical moving average price is not available anymore, e.g. in valuation view of material master data, because new locking behavior \"no exclusively locking\" for goods movements is active (TCURM-MB_LOCK_MODE = '3').</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>To achieve a significant increase of transactional data throughput for goods movements the new locking behavior \"no exclusively locking\" for materials with price control \"Standard\" becomes obligatorily active</p>\n<ul>\n<li>for systems with S/4HANA cloud edition</li>\n<li>for systems with S/4HANA on-premise edition in case of new installation (\"green field approach\")</li>\n</ul>\n<p>In systems with S/4HANA on-premise edition that were built up with a data conversion e.g. from SAP ERP, the customer can decide on its own whether to activate the new locking behavior \"no exclusively locking\" or not. This new locking behavior can be activated with report SAPRCKM_NO_EXCLUSIVELY_LOCKING. Once activated this setting can not be deactivated anymore.</p>\n<p>The deactivation of the statistical moving average price is the consequence of the new locking behavior. This new locking behavior does not use exclusive locks, which is why the system is not able anymore to calculate a statistical moving average price. Therefore the activation of the new locking behavior leads to the deactivation of the statistical moving average price.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please apply attached correction instruction to prevent activation of locking behaviour \"no exclusively locking\" by executing report SAPRCKM_OBLIGATORY_SETTINGS.</p>", "noteVersion": 2}]}, {"note": "2495950", "noteTitle": "2495950 - CKMLQS: Connection to CKM3", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want the call up the valuated quantity structure from the material price analysis.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MALENA, MLDOC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Note 2378468 activates transaction CKMLQS, which is based on the table MLDOC.</p>\n<p>This SAP Note makes it possible to call up the valuated quantity structure based on the MLDOC from transaction CKM3.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached program corrections.</p>\n<p>Restriction: It is not possible to display the valuated quantity structure for alternative valuation runs.</p>", "noteVersion": 4}, {"note": "2332591", "noteTitle": "2332591 - S4TWL - Technical Changes in Material Ledger", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ML, Document, CKMB, CKM3, Period Totals, LBKUM, SALK3</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This simplification makes it obligatory to use the Material Ledger (ML) which is now part of the standard and automatically active in all SAP S/4HANA systems.</p>\n<p>Actual Costing (including Actual Cost Component Split) is still optional.</p>\n<p>When an existing SAP system is converted to SAP S/4HANA, the Material Ledger will be activated during the migration process (if not already active). If any additional plants are added at a later point in time, the material ledger has to be activated for those plants (valuation areas) manually via transaction OMX1.</p>\n<p>As a consequence of the close integration of Material Ledger into sFIN processes, further simplification, refactoring, and process redesign has been implemented. This has incompatible effects especially on database table design and therefore direct SQL accesses to the affected tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The content of most of the former Material Ledger database tables is now stored in table ACDOCA which allows simpler and faster (HANA optimized) access to the data. The attributes of the ML data model that are relevant for the inventory subledger functionality are now part of table ACDOCA. The former tables are obsolete. Therefore the following tables must not be accessed anymore via SQL statements. In most cases a corresponding access function or class has been provided that must be used instead.</p>\n<ul>\n<li>Period Totals and Prices\r\n<ul>\n<li>CKMLPP “Material Ledger Period Totals Records Quantity”</li>\n<li>CKMLCR “Material Ledger: Period Totals Records Values”</li>\n<li>Access function: CKMS_PERIOD_READ_WITH_ITAB</li>\n</ul>\n</li>\n<li>Material Ledger Document and associated views\r\n<ul>\n<li>MLHD</li>\n<li>MLIT</li>\n<li>MLPP</li>\n<li>MLPPF</li>\n<li>MLCR</li>\n<li>MLCRF</li>\n<li>MLCRP</li>\n<li>MLMST</li>\n<li>Access functions: CKML_F_DOCUMENT_READ_MLHD, CKML_F_DOCUMENT_READ_MLXX</li>\n<li>Further tables (with no access function): MLKEPH, MLPRKEKO, MLPRKEPH</li>\n<li>Obsolete database views: MLXXV, MLREPORT, MLREADST</li>\n</ul>\n</li>\n<li>Index for Accounting Documents for Material\r\n<ul>\n<li>CKMI1</li>\n<li>Obsolete, no further access possible</li>\n</ul>\n</li>\n</ul>\n<p>In addition, some further simplifications have to be taken into account:</p>\n<ul>\n<li>Separate currency customizing of Material Ledger (transactions OMX2 / OMX3) is now mandatory. The Material Ledger acts on the currencies defined for the leading ledger in Financials.<br/>There is no default Material Ledger Type “0000” anymore.<br/>Customizing in Financial applications allows you to assign more than three currency and valuation types as being relevant in your company code. As the Material Ledger still supports only three currency and valuation types, it is no longer allowed to use an ML Type that references currency settings defined in FI or CO (flags “Currency Types from FI” and “Currency Types from CO”). Instead you have to explicitly define the currency and valuation types that are relevant for the Material Ledger.<br/>Steps to be executed:<br/>1. Use transaction OMX2 to define the currency and valuation types that are relevant for the Material Ledger.<br/>2. Then use transaction OMX3 to assign this ML Type to your valuation area.<br/><br/>See also note <a href=\"/notes/2291076\" target=\"_blank\">https://i7p.wdf.sap.corp/sap/support/notes/2291076</a></li>\n<li>Material Price Analysis (transaction CKM3 / CKM3N)<br/>The transaction CKM3/CKM3N was refactored and now provides a simplified and improved view of materials in plants with active Actual Costing. It replaces the former CKM3 view Price Determination Structure.<br/>The former CKM3 Price History view is still available via transaction CKM3PH for all materials (independent of price determination control and active Actual Costing). <br/>All other views formerly offered by CKM3/CKM3N are no longer available.</li>\n<li>For details about new Actual Costing simplification, please check the corresponding upgrade information SAP Note.</li>\n</ul>", "noteVersion": 3, "refer_note": [{"note": "2291076", "noteTitle": "2291076 - Message FML_CUST-010: ML type referencing FI or CO is not allowed", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In one of the following situations you get the error message FML_CUST-010 \"ML type &amp;1 referencing FI or CO is not allowed\":</p>\n<ul>\n<li>\n<div>you try to set Material Ledger productive (transaction CKMSTART)</div>\n</li>\n<li>you assign a ML Type to a valuation area (transaction OMX3)</li>\n</ul>\n<p>The ML Type you use is defined with activated flag \"Currency Types from FI\" and / or \"Currency Types from CO\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CKMSTART, OMX1, OMX2, OMX3</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Customizing in Financial application allows you to assign more than three currency and valuation types as being relevant in your company code. As Material Ledger still supports only up to three currency and valuation types, it is not allowed anymore to use a ML Type that references to currency settings defined in FI or CO. Instead you have to define explicitly the currency and valuation types that are relevant for Material Ledger.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Define the currency and valuation types that are relevant for Material Ledger using transaction OMX2.</p>\n<p>Afterwards assign this ML Type to your valuation area using transaction OMX3.</p>", "noteVersion": 2}, {"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}, {"note": "2270546", "noteTitle": "2270546 - S4TWL - Technical Changes in Material Ledger", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Usage of Material Ledger for parallel currencies and parallel valuation purpose:</p>\n<ul>\n<li>Contents of tables MLIT, MLPP, MLPPF, MLCR, MLCRF, MLCD, CKMI1, BSIM are now stored in ACDOCA. MLHD data is stored in BKPF  </li>\n<li>Compatibility views V_&lt;TABLENAME&gt; (for example, V_MLIT) are provided to reproduce the old structures </li>\n<li>Access to old data in tables still possible via the views V_&lt;TABLENAME&gt;_ORI (for example, V_MLIT_ORI) </li>\n<li><PERSON><PERSON><PERSON>, MLIT, MLPP, MLCR still keep prima nota information, in case of manual price changes or material debit/credit.</li>\n<li>Separate currency customizing of Material Ledger is become obsolete, instead Material Ledger is acting on the currencies defined for the leading ledger in Financials.</li>\n</ul>\n<ul>\n<li>For materials that are not relevant for actual costing (xBEW-MLAST = '2') no ML update documents (MLHD-VGART = 'UP') are persisted anymore in the ML document tables MLHD, MLIT, MLPP, MLCR and MLCD. Instead those update document data are being reconstructed by read access compatibility views based on ACDOCA. Hence attributes from ML data model, relevant for a pure inventory sub-ledger functionality, are part of table ACDOCA.</li>\n<li>No “Price Determination Structure“ view for non-actual costing materials<br/> With the merge of the Material Ledger update document into the Universal  Journal (ACDOCA) actual costing relevant key figures will not be updated anymore for non-actual costing materials ( „Price Determination: Transaction-Based“).  Therefore transaction CKM3N „Material Price Analysis“ will provide only the view „Price History“ for non-actual costing materials, means the view „Price Determination Structure“ is not available any longer.</li>\n</ul>\n<p> Usage of Material Ledger for Actual Costing purpose:</p>\n<ul>\n<li>MLHD, MLIT, MLPP, MLPPF, MLCR, MLCRF, MLCD are used as before Simple Finance 2.0</li>\n</ul>\n<p>The following Customizing Transaction are obsolete win Sap S/4HANA:</p>\n<ul>\n<li>OMX2: Assign Currency Types to Material Ledger Type</li>\n<li>OMX3: Assign Material Ledger Types to Valuation Area</li>\n</ul>", "noteVersion": 1}]}, {"note": "2467398", "noteTitle": "2467398 - CKM3 on MLDOC for S/4HANA: Cost Component Mark Up and inventory valuation not relevant", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In CKM3 you can select the field for 'Relevant for Stock Valuation'. It is not clear how the system reacts on the selection, especially for SIT scenario.</p>\n<p>The column for price in CKM3 is shown incorrectly in certain circumstances.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SIT</p>\n<p>MLDOCCCS</p>\n<p>Delta Profit</p>\n<p>Cross company stock transfer</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program error and Design</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>1 Finish the following manual steps:</p>\n<ul>\n<li>Start the transaction SE11 and create the data element ML4H_SVREL_UI</li>\n<ul>\n<li>Set the Domain XFELD</li>\n<li>Input the 'Field Labels' as:</li>\n</ul>\n</ul>\n<p><span>Short:     </span><span>Disp. All</span><br/><span>Medium:  </span><span>Disp. All CCS</span><br/><span>Long:      </span><span>Display All Cost Components</span><br/><span>Heading: D</span><span>isplay All</span></p>\n<ul>\n<ul>\n<li>Activate it;</li>\n</ul>\n<li>Change the Data type FCML4H_CCS_DYNP_S with the transaction SE11</li>\n<ul>\n<li>Change the component type for SVREL from ML4H_SVREL to  ML4H_SVREL_UI</li>\n<li>Activate it.</li>\n</ul>\n<li>Change the field FCML4H_CCS_DYNP_S-SVREL on the screen 55 in program SAPLCKM8H </li>\n<ul>\n<li>Open the graphic screen by pushing the button 'Go To Layout' (see attachment screen_55)</li>\n<li>Change the text from 'Relevant for Stock Valuation' to 'Display All Cost Components'</li>\n<li>Set the flag for SET Parameter to X in the tab 'Dict'</li>\n<li>Set the flag for GET parameter to X in the tab 'Dict'</li>\n<li>Activate it;</li>\n</ul>\n</ul>\n<p>2 Maintain the correction in this note.</p>\n<p>After the above steps the selection is changed from 'Relevant for Stock Valuation' to 'Display all Cost Components'. As default, the flag for 'Display all Cost Components' is initial. As default, the cost components that are inventory valuation relevant(table TCKH3-BESBW) are shown in CKM3. The cost component for Delta Profit or Mark Up from SIT scenario is shown also if the user Id has the authority for it in this case.</p>\n<p>If you set the flag for 'Display all Cost Components', all of the cost components are shown in CKM3 independent on the relevance for inventory valuation.</p>\n<p>In case there isn't any other non-inventory-relevant cost components besides the Profit or Mark Up from SIT scenario, the flag for 'Display all Cost Components' is hidden on the UI.</p>\n<p>The column for price is shown correctly in CKM3.</p>", "noteVersion": 10}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Optional", "Additional_Information": "Deactivation of the statistical moving average is not mandatory in SAP S/4HANA, but is nevertheless recommended => Decide about strategy for statistical moving average price"}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Business Operations", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Business Operations", "Phase": "After conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}