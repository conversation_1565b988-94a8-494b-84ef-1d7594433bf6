SAP Note 2414624 addresses the non-compatibility issue of the Flexible Material Prices (FMP) functionality within SAP S/4HANA. It explains that even though some technical objects related to FMP exist in the system, FMP itself is not supported in S/4HANA.

The FMP module was introduced in EHP5 of the Industry Solution IS-CWM to allow for the migration of customer-created price types during the transition to the new architecture of CWM. However, SAP decided to discontinue support for the FMP module in S/4HANA as only a few customers utilized the ability to define their own price types.

The note provides a solution for users to determine if they have utilized the FMP module by checking the table '/FMP/D_MP_PRIC' using transaction SE16. If no entries exist, this indicates that no custom prices were maintained using FMP.

The note also warns that in S/4HANA, FMP data cannot be migrated, attempting to run FMP transactions or reports will result in system dumps (since FMP objects are blacklisted), and objects prefixed with '/FMP/' should not be used in custom coding, referencing another SAP Note 2296016 for further details.