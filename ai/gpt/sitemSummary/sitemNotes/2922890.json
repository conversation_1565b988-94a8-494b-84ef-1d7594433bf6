{"Request": {"Number": "2922890", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2309, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000848712020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002922890?language=E&token=DF0B4ADE5FB62EA21CD1898E87F18B81"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002922890", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002922890/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2922890"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.06.2020"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AP-AP-B1"}, "SAPComponentKeyText": {"_label": "Component", "value": "Payment transfer (w/o DE,  US)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Accounts Payable", "value": "FI-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AP-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Payment transfer (w/o DE, US)", "value": "FI-AP-AP-B1", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP-B1*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2922890 - Format RU_01: New Field 20 in Form J_3RF110_PDF"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Field 20 of Payment order could be populated with the following values: \"1\", \"2\" or \"3\" in case of Payroll payments according to legal document 5286-U \"Filling out rules of wage type code in Payment orders\". This Note propagates this value into field 20 of the printing form&#160;<strong>J_3RF110_PDF.</strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Russia, Payment order, J_3RF110_PDF&#160;, J_3RF_20, J_3RFPAY_DATA, Payment Purpose</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Check that you have installed notes:&#160;2892501,&#160;2893452</p>\r\n<p id=\"\">Legal Change Requirement</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement correction instruction with manual pre- and post-implementation steps or install relevant Support Package indicated.</p>\r\n<p>After the SAP Note is implemented the new payment purpose field will be propagated to PDF Form.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I335778)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I530130)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002922890/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002922890/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002922890/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002922890/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002922890/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002922890/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002922890/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002922890/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002922890/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "J_3RF110_PDF.zip", "FileSize": "19", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001174662020&iv_version=0004&iv_guid=0090FAAA5DF01EDAA6D7D1A5D0D220F3"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2931354", "RefComponent": "FI-LOC-FI-RU", "RefTitle": "SAP RU-FI: Manage Additional Payment Attributes", "RefUrl": "/notes/2931354 "}, {"RefNumber": "2928315", "RefComponent": "FI-LOC-FI-RU", "RefTitle": "LC RU Payment Order: Payment Purpose in FB01, FB02", "RefUrl": "/notes/2928315 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "702", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "750", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10011INS4CORE", "URL": "/supportpackage/SAPK-10011INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10109INS4CORE", "URL": "/supportpackage/SAPK-10109INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10207INS4CORE", "URL": "/supportpackage/SAPK-10207INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10305INS4CORE", "URL": "/supportpackage/SAPK-10305INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10403INS4CORE", "URL": "/supportpackage/SAPK-10403INS4CORE"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60033", "URL": "/supportpackage/SAPKH60033"}, {"SoftwareComponentVersion": "SAP_APPL 602", "SupportPackage": "SAPKH60223", "URL": "/supportpackage/SAPKH60223"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60322", "URL": "/supportpackage/SAPKH60322"}, {"SoftwareComponentVersion": "SAP_APPL 604", "SupportPackage": "SAPKH60423", "URL": "/supportpackage/SAPKH60423"}, {"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60520", "URL": "/supportpackage/SAPKH60520"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60626", "URL": "/supportpackage/SAPKH60626"}, {"SoftwareComponentVersion": "SAP_FIN 617", "SupportPackage": "SAPK-61721INSAPFIN", "URL": "/supportpackage/SAPK-61721INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 618", "SupportPackage": "SAPK-61815INSAPFIN", "URL": "/supportpackage/SAPK-61815INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 700", "SupportPackage": "SAPK-70018INSAPFIN", "URL": "/supportpackage/SAPK-70018INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 720", "SupportPackage": "SAPK-72014INSAPFIN", "URL": "/supportpackage/SAPK-72014INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 730", "SupportPackage": "SAPK-73016INSAPFIN", "URL": "/supportpackage/SAPK-73016INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 20, "URL": "/corrins/0002922890/15841"}, {"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 24, "URL": "/corrins/0002922890/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 20, "URL": "/corrins/0002922890/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKH60001 - SAPKH60032&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 602&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60222&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 603&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60321&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 604&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKH60401 - SAPKH60422&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Change Data Dictionary (DDIC):<br/><br/>1. Since following object has original language English, we recommend that you log on in English.<br/>2. Start the transaction SE11.<br/>3. Check radio button Data Type and type there J_3RFPAY_DATA.<br/>4. Press button Change.<br/>5. Scroll down and find first empty line.<br/>6. Insert into first three columns these values:<br/> a) Component: J_3RF_20<br/> b) Typing method: Type<br/> c) Component Type: J_3RF_20.<br/>7. Press Enter -additional columns will be filled automatically.<br/>8. Press Ctrl + F3 or click button Activate.<br/><br/>Upload of Adobe Form:<br/><br/>1.First download J_3RF110_PDF.zip file from the Note and extract it.<br/>2.Start the transaction SFP.<br/>3.Check radio button Form and type there name of a form - J_3RF110_PDF.<br/>4.If the form is already on your system, you have to delete it before proceeding.<br/>5.Choose from menu Utilities -&gt; Upload Form Object...<br/>6.Follow the instructions in the upload process:<br/> a) Select local XML file J_3RF110_PDF.XML<br/> b) Enter Package ID-FI-REU-FORMS(If does not exist use J3RPDF).<br/>7. Press Ctrl + F3 or click button Activate.<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60519&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKH60601 - SAPKH60625&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 616&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH61614&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>|Software Component&nbsp;&nbsp; SAP_FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 617&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-61701INSAPFIN - SAPK-61720INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 618&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-61814INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-70017INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-72013INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-73003INSAPFIN - SAPK-73015INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 100&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10010INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10108INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10206INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 103&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10304INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 104&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10402INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|NOT VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB70218&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB73118&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 740&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB74015&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 750&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPK-75002INSAPBASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 751&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 752&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 753&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 754&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 755&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Change Data Dictionary (DDIC):<br/><br/>1. Since following object has original language English, we recommend that you log on in English.<br/>2. Start the transaction SE11.<br/>3. Check radio button Data Type and type there J_3RFPAY_DATA.<br/>4. Press button Change.<br/>5. Scroll down and find first empty line.<br/>6. Insert into first three columns these values:<br/> a) Component: J_3RF_20<br/> b) Typing method: Type<br/> c) Component Type: J_3RF_20.<br/>7. Press Enter -additional columns will be filled automatically.<br/>8. Press Ctrl + F3 or click button Activate.<br/><br/>Upload of Adobe Form:<br/><br/>1.First download J_3RF110_PDF.zip file from the Note and extract it.<br/>2.Start the transaction SFP.<br/>3.Check radio button Form and type there name of a form - J_3RF110_PDF.<br/>4.If the form is already on your system, you have to delete it before proceeding.<br/>5.Choose from menu Utilities -&gt; Upload Form Object...<br/>6.Follow the instructions in the upload process:<br/> a) Select local XML file J_3RF110_PDF.XML<br/> b) Enter Package ID-FI-REU-FORMS(If does not exist use J3RPDF).<br/>7. Press Ctrl + F3 or click button Activate.<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH60519&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKH60601 - SAPKH60625&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 616&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH61614&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>|Software Component&nbsp;&nbsp; SAP_FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 617&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-61701INSAPFIN - SAPK-61720INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 618&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-61814INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-70017INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-72013INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-73003INSAPFIN - SAPK-73015INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 100&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10010INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10108INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10206INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 103&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10304INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 104&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10402INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB70201 - SAPKB70217&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB73101 - SAPKB73117&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 740&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKB74001 - SAPKB74014&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 750&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-75001INSAPBASIS - SAPK-75001INSAPBASIS&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Upload of Adobe Form:<br/><br/>1.First download J_3RF110_PDF.zip file from the Note and extract it.<br/>2.Start the transaction SFP.<br/>3.Check radio button Form and type there name of a form - J_3RF110_PDF.<br/>4.If the form is already on your system, you have to delete it before proceeding.<br/>5.Choose from menu Utilities -&gt; Upload Form Object...<br/>6.Follow the instructions in the upload process:<br/> a) Select local XML file J_3RF110_PDF.XML<br/> b) Enter Package ID-FI-REU-FORMS(If does not exist use J3RPDF).<br/>7. Press Ctrl + F3 or click button Activate.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 64, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 3, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 13, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1029488 ", "URL": "/notes/1029488 ", "Title": "Error F5165 while printing payment orders with a VAT-free", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1052306 ", "URL": "/notes/1052306 ", "Title": "Incorrect payee name in Russian Payment Order", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1090289 ", "URL": "/notes/1090289 ", "Title": "J_3RFZKR_PDF: 104-110 fields are empty", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1229491 ", "URL": "/notes/1229491 ", "Title": "VAT information missing in RU_MT103 e-payments", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1233701 ", "URL": "/notes/1233701 ", "Title": "RU_MT103: 77 /N9/ data copied into 70 or 72 /NZP/ (VAT line)", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1234745 ", "URL": "/notes/1234745 ", "Title": "Fields 70, 72, 59, 52, 57 in RU_MT103 e-payments", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1518295 ", "URL": "/notes/1518295 ", "Title": "Electronic Payment Formats RU_MT103 & RU_CITI_RUR changes", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1538819 ", "URL": "/notes/1538819 ", "Title": "Payment Order/Request form (RU): language of texts", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1570916 ", "URL": "/notes/1570916 ", "Title": "VAT information is missing in Payment Order form/file (RU)", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1992613 ", "URL": "/notes/1992613 ", "Title": "Payment order: Field 22 UIN", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "604", "Number": "1542512 ", "URL": "/notes/1542512 ", "Title": "Payment media formats (RU): \"Company code is not defined\"", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1090289 ", "URL": "/notes/1090289 ", "Title": "J_3RFZKR_PDF: 104-110 fields are empty", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1229491 ", "URL": "/notes/1229491 ", "Title": "VAT information missing in RU_MT103 e-payments", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1233701 ", "URL": "/notes/1233701 ", "Title": "RU_MT103: 77 /N9/ data copied into 70 or 72 /NZP/ (VAT line)", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1234745 ", "URL": "/notes/1234745 ", "Title": "Fields 70, 72, 59, 52, 57 in RU_MT103 e-payments", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1518295 ", "URL": "/notes/1518295 ", "Title": "Electronic Payment Formats RU_MT103 & RU_CITI_RUR changes", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1538819 ", "URL": "/notes/1538819 ", "Title": "Payment Order/Request form (RU): language of texts", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1570916 ", "URL": "/notes/1570916 ", "Title": "VAT information is missing in Payment Order form/file (RU)", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1992613 ", "URL": "/notes/1992613 ", "Title": "Payment order: Field 22 UIN", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1229491 ", "URL": "/notes/1229491 ", "Title": "VAT information missing in RU_MT103 e-payments", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1233701 ", "URL": "/notes/1233701 ", "Title": "RU_MT103: 77 /N9/ data copied into 70 or 72 /NZP/ (VAT line)", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1234745 ", "URL": "/notes/1234745 ", "Title": "Fields 70, 72, 59, 52, 57 in RU_MT103 e-payments", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1518295 ", "URL": "/notes/1518295 ", "Title": "Electronic Payment Formats RU_MT103 & RU_CITI_RUR changes", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1538819 ", "URL": "/notes/1538819 ", "Title": "Payment Order/Request form (RU): language of texts", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1570916 ", "URL": "/notes/1570916 ", "Title": "VAT information is missing in Payment Order form/file (RU)", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1992613 ", "URL": "/notes/1992613 ", "Title": "Payment order: Field 22 UIN", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1518295 ", "URL": "/notes/1518295 ", "Title": "Electronic Payment Formats RU_MT103 & RU_CITI_RUR changes", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1538819 ", "URL": "/notes/1538819 ", "Title": "Payment Order/Request form (RU): language of texts", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "604", "ValidTo": "605", "Number": "1570916 ", "URL": "/notes/1570916 ", "Title": "VAT information is missing in Payment Order form/file (RU)", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "604", "ValidTo": "605", "Number": "1992613 ", "URL": "/notes/1992613 ", "Title": "Payment order: Field 22 UIN", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1518295 ", "URL": "/notes/1518295 ", "Title": "Electronic Payment Formats RU_MT103 & RU_CITI_RUR changes", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1538819 ", "URL": "/notes/1538819 ", "Title": "Payment Order/Request form (RU): language of texts", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1542512 ", "URL": "/notes/1542512 ", "Title": "Payment media formats (RU): \"Company code is not defined\"", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1991954 ", "URL": "/notes/1991954 ", "Title": "Payment order: Field 22 UIN", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1991954 ", "URL": "/notes/1991954 ", "Title": "Payment order: Field 22 UIN", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "1991954 ", "URL": "/notes/1991954 ", "Title": "Payment order: Field 22 UIN", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "1991954 ", "URL": "/notes/1991954 ", "Title": "Payment order: Field 22 UIN", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "1991954 ", "URL": "/notes/1991954 ", "Title": "Payment order: Field 22 UIN", "Component": "XX-CSC-RU-FI"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2922890 ", "URL": "/notes/2922890 ", "Title": "Format RU_01: New Field 20 in Form J_3RF110_PDF", "Component": "FI-AP-AP-B1"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}