SAP Note 1077930 addresses errors encountered when creating a transformation using a template for a transfer rule or an update rule in SAP NetWeaver Business Intelligence (BI). The specific errors mentioned are RSTRAN 523 and RSTRAN 669. The note describes the issue, with RSTRAN 669 indicating a missing field in the new source, and RSTRAN 523 being a syntax error that occurs with routine type rules during the transformation migration process.

The reason for these issues is identified as a program error. To resolve the errors, the note advises customers to import Support Package 16 for SAP NetWeaver 7.0 BI (BI Patch 16, or SAPKW70016) into their BI system. The details of the Support Package are further described in Note 1074388, titled "SAPBINews BI7.0 Support Package 16."

For immediate corrective action, before the support package's general release, customers can implement the correction instructions as an advance correction. However, the note advises that customers familiarize themselves with transaction SNOTE and read Note 875986 for guidance on using it.

The note also mentions that customers may find the notes available before the actual release of the Support Package, and in such cases, the short text of the note might contain the words "Preliminary version" as an indication that the Support Package is not yet generally available.