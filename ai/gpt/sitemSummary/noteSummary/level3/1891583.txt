SAP Note 1891583 describes how to restrict user logons to an SAP application server, which may be necessary during system maintenance to ensure only authorized administrators have access. This functionality is available as part of a Support Package and requires at least kernel patch level for kernel releases 721, 740, 741, or higher, starting with SAP_BASIS 731.

The note introduces a new profile parameter, `login/server_logon_restriction`, which can be set to three different values:

- `0`: No restriction, allowing all users to log on to the application server.
- `1`: Only users with special rights can log on, specifically those with a security policy that includes the attribute `SERVER_LOGON_PRIVILEGE` set to `1`. Administrators can modify security policies using transaction SECPOL. Users without this privilege will receive an error message indicating "Server is currently not generally available (restricted logon)."
- `2`: Logon to the application server is completely prohibited, and users will see an error message stating "Server is currently not available (logon not permitted)."

Additional guidance provided by the note includes:

- Setting the profile parameter dynamically doesn't log off currently connected users.
- To save the parameter value permanently, use transaction RZ10.
- The user information system (transaction SUIM) can help determine which users are assigned specific security policies.

Lastly, the note warns that if the emergency user "SAP*" is activated (i.e., `login/no_automatic_user_sapstar` is set to `0` and "SAP*" is not defined in SU01), logon with this user is always possible regardless of the `login/server_logon_restriction` parameter settings. This could be a potential security risk if not managed properly.