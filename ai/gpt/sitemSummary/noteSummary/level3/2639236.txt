SAP Note 2639236 addresses issues with three 'Post Load Validation' applications specifically for Customer, Supplier, and Material data within the S/4HANA Cloud Edition (CE). The main points from this note are:

- The mentioned 'Post Load Validation' apps are no longer available in the Fiori Apps Library and cannot be accessed.
- Users have reported problems with these apps such as missing fields and download issues.
- There is no possibility of extending these three 'Post Load Validation' apps, meaning they cannot be customized to add more fields or functionalities.
- SAP recommends using the 'Data Migration Status' app as an alternative to these apps. The 'Data Migration Status' app offers the ability to check the status of migration projects and objects post data migration, and it has been available since the SAP S/4HANA Cloud 1805 release.
- The note provides guidance on how to use the 'Data Migration Status' app by assigning the business role 'SAP_BR_CONFIG_EXPERT_DATA_MIG' to Fiori users and accessing the app through the 'Data Migration' group in the Fiori Launchpad.
- It is indicated that the three 'Post Load Validation' apps are planned to be obsoleted in future releases of SAP S/4HANA CE.

Overall, this SAP Note advises on the discontinuation of certain 'Post Load Validation' apps and guides users to adopt the 'Data Migration Status' app for similar purposes within SAP S/4HANA Cloud.