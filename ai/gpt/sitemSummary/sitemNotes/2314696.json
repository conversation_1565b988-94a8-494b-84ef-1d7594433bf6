{"Request": {"Number": "2314696", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 762, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013670992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002314696?language=E&token=169C26AE89096D2BB994ED9E1D461EDA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002314696", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002314696/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2314696"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.03.2019"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CZ-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-CZ"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Czech Republic", "value": "XX-CSC-CZ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-CZ-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-CZ", "value": "XX-CSC-CZ-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CZ-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2314696 - S4TC CEEISUT Master Check for S/4 System Conversion Checks"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Implementation of CEEISUT check class for S/4 transformation checks as described in SAP note&#160;2182725.<a target=\"_blank\" href=\"/notes/2182725\" title=\"2182725  - S4TC Delivery of the S/4 Pre-Transition Checks\"><br /></a></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S4TC, CEEISUT, Pre-Check Class</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The pre-check class raises an error when none of the following Business Function Sets is in active state or any its underlying object (Business Function, Switch, Package) is not present in an active version in a system or any of Business Functions or Switches is not in active state:</p>\r\n<p><strong>Business Function Set 'UTILITIES' or 'MINING_WITH_UTILITIES or 'OIL_&amp;_GAS_WITH_UTILITIES':</strong></p>\r\n<ul>\r\n<li>Business Function&#160;/SAPCE/ISU_LOC_CEE</li>\r\n<ul>\r\n<li>Switch /SAPCE/ISU_LOC_CEE_SFWS_02&#160;</li>\r\n<ul>\r\n<li>Switch Package /SAPCE/IU_LOC_CEE_SFWS_02&#160;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Switch&#160;/SAPCE/FICA_LOC_CEE_SFWS_02</li>\r\n<ul>\r\n<li>Switch Package&#160;SAPCE/FK_LOC_CEE_SFWS_02</li>\r\n</ul>\r\n</ul>\r\n<li>Business Function ISU_UTIL_WASTE</li>\r\n<ul>\r\n<li>Switch&#160;/SAPCE/ISU_LOC_CEE_SFWS_01</li>\r\n<ul>\r\n<li>Switch Package&#160;/SAPCE/IU_LOC_CEE_SFWS_01</li>\r\n</ul>\r\n<li>Switch&#160;/SAPCE/FICA_LOC_CEE_SFWS_01</li>\r\n<ul>\r\n<li>Switch Package&#160;/SAPCE/FK_LOC_CEE_SFWS_01</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p><strong><strong>Business Function Set 'TELCO'</strong>:</strong></p>\r\n<ul>\r\n<li>Business Function /SAPCE/TEL_LOC_CEE</li>\r\n<ul>\r\n<li>&#160;Switch&#160;/SAPCE/FICA_LOC_CEE_SFWS_02</li>\r\n<ul>\r\n<li>Switch Package&#160;/SAPCE/FK_LOC_CEE_SFWS_02</li>\r\n</ul>\r\n</ul>\r\n<li>Business Function&#160;/SAPCE/TEL_LOC_CEE_HIDDEN</li>\r\n<ul>\r\n<li>Switch&#160;/SAPCE/ISU_LOC_CEE_SFWS_01</li>\r\n<ul>\r\n<li>Switch Package&#160;/SAPCE/IU_LOC_CEE_SFWS_01</li>\r\n</ul>\r\n</ul>\r\n<li>Business Function&#160;RM_CA</li>\r\n<li>\r\n<ul>\r\n<ul>\r\n<li>Switch&#160;/SAPCE/FICA_LOC_CEE_SFWS_01</li>\r\n<ul>\r\n<li>Switch Package&#160;/SAPCE/FK_LOC_CEE_SFWS_01</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n</li>\r\n</ul>\r\n<p><strong>&#160;</strong></p>\r\n<p>In case of a negative result of the pre-check please check whether all the condition above are met. The following check identifications can be shown:</p>\r\n<ul>\r\n<li>CEEISUT_SFW_BS &#160; &#160; &#160; &#160; &#160; &#160; Check of Business Function Sets</li>\r\n<li>CEEISUT_SFW_BF &#160; &#160; &#160; &#160; &#160; &#160; Check of Business Functions</li>\r\n<li>CEEISUT_SFW_SW &#160; &#160; &#160; &#160; &#160; &#160;Check of Switches</li>\r\n<li>CEEISUT_SFW_PC &#160; &#160; &#160; &#160; &#160; &#160; Check of Switch Packages</li>\r\n</ul>\r\n<p>Please check the note&#160;2323221 whether all the objects have been created a activated properly.</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the technical prerequisites for the&#160;S/4 transformation checks via this note.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-RO-IS-U (Utilities)"}, {"Key": "Other Components", "Value": "XX-CSC-RU-FICA (use FI-LOC-CA-RU)"}, {"Key": "Other Components", "Value": "XX-CSC-HU-IS-U (use FI-LOC-UT-HU)"}, {"Key": "Other Components", "Value": "XX-CSC-TR-FICA (use FI-LOC-CA-TR)"}, {"Key": "Other Components", "Value": "XX-CSC-HU-FICA (Contract Accounting)"}, {"Key": "Other Components", "Value": "FI-LOC-CA-HU (Hungary)"}, {"Key": "Other Components", "Value": "XX-CSC-SI-FICA (use FI-LOC-CA-SI)"}, {"Key": "Other Components", "Value": "XX-CSC-RO-FICA (use FI-LOC-CA-RO)"}, {"Key": "Other Components", "Value": "XX-CSC-GR-IS-U (use FI-LOC-UT-GR)"}, {"Key": "Other Components", "Value": "XX-CSC-SI-IS-U (use FI-LOC-UT-SI)"}, {"Key": "Other Components", "Value": "XX-CSC-SK-IS-U (use FI-LOC-UT-SK)"}, {"Key": "Other Components", "Value": "XX-CSC-BG-IS-U (use FI-LOC-UT-BG)"}, {"Key": "Other Components", "Value": "XX-CSC-UA-IS-U (use FI-LOC-UT-UA)"}, {"Key": "Other Components", "Value": "XX-CSC-CZ-FICA (use FI-LOC-CA-CZ)"}, {"Key": "Other Components", "Value": "XX-CSC-PL-IS-U (use FI-LOC-UT-PL)"}, {"Key": "Other Components", "Value": "XX-CSC-BG-FICA (use FI-LOC-CA-BG)"}, {"Key": "Other Components", "Value": "XX-CSC-TR-IS-U (use FI-LOC-UT-TR)"}, {"Key": "Other Components", "Value": "XX-CSC-RU-IS-U (Utilities)"}, {"Key": "Other Components", "Value": "XX-CSC-GR-FICA (use FI-LOC-CA-GR)"}, {"Key": "Other Components", "Value": "XX-CSC-UA-FICA (use FI-LOC-CA-UA)"}, {"Key": "Other Components", "Value": "XX-CSC-SK-FICA (use FI-LOC-CA-SK)"}, {"Key": "Other Components", "Value": "XX-CSC-PL-FICA (use FI-LOC-CA-PL)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I069660)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I069660)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002314696/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002314696/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002314696/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002314696/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002314696/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002314696/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002314696/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002314696/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002314696/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2399707", "RefComponent": "CA-TRS-PRCK", "RefTitle": "Simplification Item Check", "RefUrl": "/notes/2399707"}, {"RefNumber": "2330962", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "CEEISUT Retrofit: Business Functions - Modifying Assignments for CEEISUT 600", "RefUrl": "/notes/2330962"}, {"RefNumber": "2323221", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "CEEISUT Retrofit: Business Functions", "RefUrl": "/notes/2323221"}, {"RefNumber": "2182725", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2182725"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2338097", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "S4TWL - Utility & Telco related Business Functions", "RefUrl": "/notes/2338097 "}, {"RefNumber": "2323221", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "CEEISUT Retrofit: Business Functions", "RefUrl": "/notes/2323221 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CEEISUT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "CEEISUT", "NumberOfCorrin": 1, "URL": "/corrins/0002314696/532"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}