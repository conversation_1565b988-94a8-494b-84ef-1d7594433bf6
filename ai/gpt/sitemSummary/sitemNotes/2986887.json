{"Request": {"Number": "2986887", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 560, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001849662020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002986887?language=E&token=33FE56EE4700B6C193A27EE22A5486FE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002986887", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002986887/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2986887"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.11.2020"}, "SAPComponentKey": {"_label": "Component", "value": "PP-MRP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Material Requirements Planning"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Production Planning and Control", "value": "PP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Material Requirements Planning", "value": "PP-MRP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PP-MRP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2986887 - MRP live: dispatch parameter effectivity materials to classic MRP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>MRP Live on HANA does not support parameter effectivity.&#160;In this special case, MRP live (transaction MD01N) cannot detect automatically that the material cannot be planned in MRP live. You have to instruct the system to plan the material in classic MRP using the report PPH_MRP_SET_PLAN_IN_ABAP or transaction MD_MRP_FORCE_CLASSIC.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p id=\"\">MARA-KZEFF</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p id=\"\">Dependency on MARA-KZEFF is not evaluated</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Using parameter effectivity in MRP usually means to set indicator \"Assign effectivity parameter values\" (MARA-KZEFF) in material master screen \"Basic data 1\". If you do so, this setting will be deteted by MR<PERSON> live and the material will be automatically planned in classic MRP.</p>\r\n<p>Implement the attached correction.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D020784)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019576)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002986887/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002986887/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002986887/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002986887/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002986887/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002986887/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002986887/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002986887/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002986887/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1914010", "RefComponent": "PP-MRP-PR", "RefTitle": "MD01N: Restrictions for Planning in MRP Live on HANA", "RefUrl": "/notes/1914010"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2992703", "RefComponent": "PP-MRP", "RefTitle": "Incorrect results, dumps, or long runtimes in MRP or MRP Live or MD04 (ERP / SAP On-Premise)", "RefUrl": "/notes/2992703 "}, {"RefNumber": "1914010", "RefComponent": "PP-MRP-PR", "RefTitle": "MD01N: Restrictions for Planning in MRP Live on HANA", "RefUrl": "/notes/1914010 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10110INS4CORE", "URL": "/supportpackage/SAPK-10110INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10208INS4CORE", "URL": "/supportpackage/SAPK-10208INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10306INS4CORE", "URL": "/supportpackage/SAPK-10306INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10404INS4CORE", "URL": "/supportpackage/SAPK-10404INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 105", "SupportPackage": "SAPK-10501INS4CORE", "URL": "/supportpackage/SAPK-10501INS4CORE"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 5, "URL": "/corrins/0002986887/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2434791 ", "URL": "/notes/2434791 ", "Title": "MRP live: Enabling MRN-MRP set and Supersession chain", "Component": "IS-ADEC-MPN-MRP"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2449072 ", "URL": "/notes/2449072 ", "Title": "MD01n: Materials with External Planning are not planned", "Component": "PP-MRP"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2917032 ", "URL": "/notes/2917032 ", "Title": "MD01N: Incorrect order quantity generated in reorder point planning w/ ext. req and replenish to max stock level.", "Component": "PP-MRP-PP"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2917032 ", "URL": "/notes/2917032 ", "Title": "MD01N: Incorrect order quantity generated in reorder point planning w/ ext. req and replenish to max stock level.", "Component": "PP-MRP-PP"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2917032 ", "URL": "/notes/2917032 ", "Title": "MD01N: Incorrect order quantity generated in reorder point planning w/ ext. req and replenish to max stock level.", "Component": "PP-MRP-PP"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2937567 ", "URL": "/notes/2937567 ", "Title": "MRP Live: Checks for A&D Subcontracting", "Component": "PP-MRP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}