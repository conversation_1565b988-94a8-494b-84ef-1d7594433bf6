{"Request": {"Number": "1247785", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 367, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016592012017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001247785?language=E&token=9D2640460812B318ED23E07403B2C62C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001247785", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001247785/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1247785"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.09.2008"}, "SAPComponentKey": {"_label": "Component", "value": "GRC-SAC"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Access Control"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Governance, Risk and Compliance", "value": "GRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'GRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Access Control", "value": "GRC-SAC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'GRC-SAC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1247785 - AC 5.3 - VIRSANH & VIRSAHR Lower Import Conditions for 620"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you want to import the add-ons VIRSANH 530_620 and/or VIRSAHR 530_620 (for HR systems only) in SAP 620 systems. The system issues an error message in transaction SAINT stating that the import prerequisites are not met.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>VIRSANH, VIRSAHR, 530_620, ACP, attribute change package (ACP), SPAM, SAINT.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The add-ons VIRSANH 530_620 and/or VIRSAHR 530_620 (for HR systems only) were originally released on the following Support Package levels (see Notes 1133163 resp. 133164):<br />SAP_ABA&#x00A0;&#x00A0; 620 - SAPKA62063<br />SAP_BASIS 620 - SAPKB62063<br />SAP_HR&#x00A0;&#x00A0;&#x00A0;&#x00A0;470 - SAPKE47066<br /><br />The system will not allow to install these add-ons because of import prerequisites are not met.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>As of September '08 the Add-Ons VIRSANH 530_620 and VIRSAHR 530_620 has been released on lowered Support Package levels:<br />SAP_ABA&#x00A0;&#x00A0; 620 - SAPKA62026<br />SAP_BASIS 620 - SAPKB62026<br />SAP_HR&#x00A0;&#x00A0;&#x00A0;&#x00A0;470 - SAPKE47017<br /><br />You can install the add-on VIRSANH 530_620 and VIRSAHR 530_620 (for HR systems) on the lowered Support Packages levels only if the corresponding Attribute Change Package (ACP) is uploaded to the systems.<br />For this reason, you must download an attribute change package (ACP) in addition to the installation package(s).<br />For more information about attribute change packages, see Note 1119856.<br /><br />If you want to install VIRSANH 530_620 and VIRSAHR 530_620 (for HR System only) into SAP 620 system, proceed as follows:</p> <UL><UL><LI>At least SPAM/SAINT Version 0026 is a prerequisite for the processing of ACPs in release 620.</LI></UL></UL> <UL><UL><LI>Download the installation Packages SAPK-531COINVIRSANH and SAPK-531COINVIRSAHR (for HR System only) from the SAP Service Marketplace and download ACP files attached to this note.</LI></UL></UL> <UL><UL><LI>Load the contents of this file into the EPS inbox (SAINT &gt; Installation Package &gt; Load Packages &gt; From Front End).</LI></UL></UL> <UL><UL><LI>Then you can use SAINT to define the required queue.</LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I811179)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I811179)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001247785/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247785/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247785/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247785/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247785/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247785/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247785/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247785/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001247785/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ACP_VIRSANH_530_620.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000495472008&iv_version=0003&iv_guid=77145FD9A4082346AB388A44A036FFAF"}, {"FileName": "ACP_VIRSAHR_530_620.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000495472008&iv_version=0003&iv_guid=C494BEA8369A1E41B57D5C80AE94882E"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1133164", "RefComponent": "GRC", "RefTitle": "VIRSAHR 530_620 Install / Delta Upgrade on R/3 Enterprise 47", "RefUrl": "/notes/1133164"}, {"RefNumber": "1133163", "RefComponent": "GRC", "RefTitle": "VIRSANH 530_620 Install / Delta Upgrade on SAP_BASIS 620", "RefUrl": "/notes/1133163"}, {"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1133164", "RefComponent": "GRC", "RefTitle": "VIRSAHR 530_620 Install / Delta Upgrade on R/3 Enterprise 47", "RefUrl": "/notes/1133164 "}, {"RefNumber": "1133163", "RefComponent": "GRC", "RefTitle": "VIRSANH 530_620 Install / Delta Upgrade on SAP_BASIS 620", "RefUrl": "/notes/1133163 "}, {"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "VIRSAHR", "From": "530_620", "To": "530_620", "Subsequent": ""}, {"SoftwareComponent": "VIRSANH", "From": "530_620", "To": "530_620", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}