{"Request": {"Number": "2284745", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 379, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018270132017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002284745?language=E&token=5174177E7353A0BA12755564EFD36D88"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002284745", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002284745/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2284745"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.10.2021"}, "SAPComponentKey": {"_label": "Component", "value": "CA-MDG-APP-MM"}, "SAPComponentKeyText": {"_label": "Component", "value": "MDG Material"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data Governance", "value": "CA-MDG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-MDG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Applications", "value": "CA-MDG-APP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-MDG-APP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MDG Material", "value": "CA-MDG-APP-MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-MDG-APP-MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2284745 - Functional Restrictions in MDG for Material with SAP Master Data Governance 9.0"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The following restrictions exist for Master Data Governance for Material in SAP Master Data Governance 9.0.</p>\r\n<p><em><span style=\"text-decoration: underline;\"><strong>MDG, Central Governance</strong></span></em></p>\r\n<p>The following restrictions exist for the central governance processes.</p>\r\n<p><strong>1. Derivation of classification data not supported</strong></p>\r\n<p>It is not possible to change classification data using the following MDG derivations:</p>\r\n<ul>\r\n<li>BAdI 'Define Validations/Derivations',<br />method IF_EX_USMD_RULE_SERVICE&#126;DERIVE_ENTITY</li>\r\n</ul>\r\n<ul>\r\n<li>BAdI 'Validations/Derivations Across Entity Types',<br />method IF_EX_USMD_RULE_SERVICE2&#126;DERIVE</li>\r\n</ul>\r\n<ul>\r\n<li>BRF+ based derivations</li>\r\n</ul>\r\n<p><strong>2. Simultaneous display of classification not supported</strong></p>\r\n<p>It is not possible for multiple users to simultaneously display classification data for the same material using the single material maintenance UI or the PDF printing UI. If a second user attempts to display classification data for a locked material they will get an error message.</p>\r\n<p><strong>3. Disabling backend logic for classification not supported</strong></p>\r\n<p>The classification entities CLASSASGN and VALUATION always execute the checks 'Authorization Check', 'Existence Check' and 'Reuse Area Check'. Setting these checks to 'Not Relevant' in IMG activity 'Configure Properties of Change Request Step' will have no impact on the classification entities.</p>\r\n<p><strong>4. Change of Material Type</strong></p>\r\n<ul>\r\n<li>No replication via IDoc<br />Changes to the material type on an MDG-M hub can not be transported with any material IDoc. To prevent inconsistencies it is not recommended to change material type in a MDG hub scenario.</li>\r\n</ul>\r\n<ul>\r\n<li>No simulation of data deletion<br />In some cases the change of material type can result in UI changes (e.g. if a field is set to 'hidden' for the target material type via transaction OMSR). Some field values will be deleted at activation of the change request, some values could stay in the system.&#160;</li>\r\n</ul>\r\n<p><strong>5. Material Long Text</strong></p>\r\n<ul>\r\n<li>You cannot change formatted long text using MDG-M. Only plain (ASCII) text is supported. If you maintain a formatted long text using MDG-M it will be unformatted to plain ASCII text. This applies to all the material long texts, for example, \"Basic Text\" and \"Internal Comment\".</li>\r\n</ul>\r\n<ul>\r\n<li>Customer specific 'Text IDs' (defined in table TTXID) are not supported. MDG-M supports the standard 'Text IDs' for the material master (grouped by 'Application Object'):</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>MATERIAL:<br />BEST (Purchase order text)<br />GRUN (Basic data text)<br />IVER (Internal comment)<br />PRUE (Inspection text)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>MVKE: 0001 (Sales text)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>MDTXT: LTXT (MRP note)</li>\r\n</ul>\r\n</ul>\r\n<p><strong>6. Valuation Level \"Company Code\": Plant needed for accounting data</strong></p>\r\n<p>If the valuation level is 'Company Code', then valuation/accounting data can only be accessed if a plant for the company code is assigned and selected.</p>\r\n<p><strong>7. Replication with DRF (Data Replication Framework) and Import with DIF (Data Import Framework)</strong></p>\r\n<ul>\r\n<li>There is no integration of ALE message type Documents (DOCMAS). For distribution of this ALE message type you can use the standard ALE distribution.</li>\r\n<li>ALE and DRF&#160;replication for MRP Areas is not possible as no IDOC exists for distribution of MRP Areas (MDMA).</li>\r\n<li>Flex Entities are not supported by the DRF Material outbound implementation.</li>\r\n<li>DIF for material supports only ALE message types MATMAS and CLFMAS.</li>\r\n</ul>\r\n<p><strong>8. \"Fuzzy\" Enterprise Search and numerical material numbers</strong></p>\r\n<p>Fuzzy search on material numbers provides meaningful results if</p>\r\n<ul>\r\n<li>Either the material number is not numeric</li>\r\n<li>Or the material number is numeric but alpha conversion is switched off (option \"Leading Zeros\" in transaction OMSL)</li>\r\n</ul>\r\n<p>In all other cases, the (internally added) leading zeros will impact the search result for the fuzzy search.</p>\r\n<p><strong>9. Message configuration in OMT4</strong></p>\r\n<p>Transaction OMT4 provides configuration for message severity. Messages can be raised as errors, warnings, or not at all. MDG-M only supports configuration for a subset of these messages.</p>\r\n<ul>\r\n<li>If a message from this subset is configured as a warning, it will also be shown as warning in MDG-M</li>\r\n<li>If a message not in this subset is configured as a warning, it will not be shown in MDG-M</li>\r\n</ul>\r\n<p>Messages configured as errors will always be shown as errors.<br />For message class:</p>\r\n<ul>\r\n<li>M3, configuration for messages 132, 159, 285, 347, and 348 is supported</li>\r\n<li>MM, configuration for messages 189, 312, and 657 is supported</li>\r\n<li>MH and WE, configuration for messages is not supported</li>\r\n</ul>\r\n<p><strong>10. Defaulting data</strong></p>\r\n<p>The MM backend provides defaulting of data at a couple of places. For example, defaulting the weight UOM, deriving the item category group code/cross-plant material status from the material type or setting the industry sector to a constant value.<br />MDG-M only support a subset of the derivations defined in the backend.</p>\r\n<ul>\r\n<li>Default class as defined by material type</li>\r\n<li>Defaulting the valuation class if determined uniquely</li>\r\n<li>Defaulting the 'Checking Group for Availability Check' by material type and plant (table TMCFU)</li>\r\n<li>...</li>\r\n</ul>\r\n<p>In general, derivations can be implemented using BRF+ or the USMD_RULE_SERVICE-BAdI. Further information can be found in: http://scn.sap.com/docs/DOC-14915</p>\r\n<p><strong>11. Duplicate Check restrictions</strong></p>\r\n<p>The following restrictions apply:</p>\r\n<ul>\r\n<li>Text fields (like material description or the long texts)</li>\r\n<ul>\r\n<li>A pattern search is used, therefore all texts are found which contain the search string</li>\r\n</ul>\r\n<li>Match Profile: The Field Weight&#160;is not considered for Database search, Enterprise Search and Remote Key Search</li>\r\n<li>For Enterprise Search based duplicate check: The score in the duplicate check result is always 100% (when using HANA for duplicate check a valid score will be calculated).</li>\r\n<li>Duplicate checks in Enterprise Search are always performed in fuzzy mode, this could result in more hits than expected.</li>\r\n<li>For HANA based duplicate check: Classification data based on a valid-from date (e.g. valuation changed by a future date) is not supported.</li>\r\n</ul>\r\n<p><strong>12. Integration of the Document Management System's 'Object Links'</strong></p>\r\n<p>MDG-M does not support</p>\r\n<ul>\r\n<li>Classification of object links</li>\r\n<li>Assignment of 'additional objects' to an object link</li>\r\n<li>Plant specific object links to a material (linked SAP object 'MARC')</li>\r\n<li>There is no integration of ALE message types DOCMAS and DOLMAS into DRF. A workaround would be a manual distribution of documents and document links.</li>\r\n</ul>\r\n<p><strong>13. Simplified <strong>authority checks and </strong>determination of field properties</strong></p>\r\n<ul>\r\n<li>In MDG-M, the view determination is done internally during activation of a change request, and so the maintenance status is not part of the MDG-M data model. Therefore, a simplified maintenance status is used in the field property determination and the authority checks. That maintenance status may deviate from the maintenance status determined on activation of the change request. This may result in less strict authorizations, also in mandatory fields not marked as mandatory in the MDG-M UI, or others.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">&#160; Mitigation option: Use the field control BAdI USMD_ACC_FLD_PROP_CUST_DEP_SET to close any gaps in the field control you notice.</p>\r\n<ul>\r\n<li>Authorization object M_MATE_STA (maintenance status) cannot be used to restrict the display of material data for a specific maintenance status. It is mandatory to have activity 01 or 02 assigned for this authorization object, even you want only display material data.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">&#160; Mitigation option: Use other authorization objects (e.g. USMD_CREQ or M_MATE_MAT)</p>\r\n<p><strong>14. Parallel Change Request Processing</strong></p>\r\n<p>Classification data (incl. characteristic values) of one material can be changed in only one change request. Further processing using a parallel change request is not possible.</p>\r\n<p>When using change requests with parallel processing (that is, you have selected the PCR checkbox for the Type of Change Request in Customizing) that involve changed classification data, no error message indicating snapshot differences for the classification data is displayed, if the data has also been changed in the back-end system. Instead, any changed classification data is posted as-is (back-end changes are overwritten).</p>\r\n<p><strong>15. Multiple Record Processing</strong></p>\r\n<ul>\r\n<li>Text fields&#160;(e.g. material description) and long texts are not fully supported (only in logon language).</li>\r\n<li>There is no support of duplicate check when using Multiple Record Processing UI.</li>\r\n<li>Classification data cannot be used in Multiple Record Processing.</li>\r\n<li>When&#160;using&#160;the copy functionality with Multiple Record Processing from search screen, classification data&#160;is not copied.&#160;</li>\r\n</ul>\r\n<p><strong>16. EAN number ranges</strong></p>\r\n<p>SAP recommends to avoid overlapping number ranges for different EAN categories. Each EAN category should have its specific number range interval to ensure that the EAN category can be derived from the EAN.</p>\r\n<p>MDGM does not support use cases where this is not the case. MDGM only supports EAN handling for EAN categories where this uniqueness is ensured.</p>\r\n<p><strong>17. Classification authorization for MDG Material maintenance</strong></p>\r\n<p>Restricted authorization (e.g. display only) for classification is not supported in MDG and can lead to class type exclusion. That means a class type is not displayed and cannot be maintained if any classification authorization is restricted.<br /><br />MDG classification runs internally in change mode, therefore all authorizations and activities for insert, update, delete and display can occur, and are consequently required.<br />The restriction is on class type level.</p>\r\n<p><strong>18. Class type 100 for EH&amp;S is not supported in MDG-M</strong></p>\r\n<p>Due to technical reasons class type 100 is not supported in MDG-M and cannot be selected.</p>\r\n<p><strong>19. Material ledger</strong></p>\r\n<p>It is not possible to change the valuation category after the material valuation data has been activated. This means:</p>\r\n<ul>\r\n<li>It is not possible to switch to split valuation if the material has been saved with stocks valuated together.</li>\r\n<li>Also if the material has been saved with split valuation, it is not possible to have the stocks to be valuated together.</li>\r\n</ul>\r\n<p>On activation the accounting data always gets written into the current period. This is even the case if between creation and activation of a change request a period shift has been done. It is not possible to update past periods with MDG-M.</p>\r\n<p><strong>20. Change Management and Revision Level</strong></p>\r\n<p>When using a change master with the setting \"Automatic generation with dialog box\" for material in the management record, this dialog box will not appear in MDG.</p>\r\n<p>There is no integration of ALE message type ECMMAS and ECMREV into DRF. For distribution of these ALE message types you can use the standard ALE distribution.</p>\r\n<p><strong>21. SAP HANA-based search for MDG</strong></p>\r\n<p>With MDG 7.0 SP02 it is possible to use SAP HANA-based search for MDG for the search and duplicate check functionality in addition to Enterprise Search.</p>\r\n<ul>\r\n<li>With SAP HANA-based search for MDG, the search for classification data based on a valid-from date (e.g. valuation changed by a future date) is not supported.</li>\r\n<li>With SAP HANA-based search for MDG, the federated search is not supported. If you require search capabilities across systems and beyond the MDG system, Enterprise Search can still be used.</li>\r\n</ul>\r\n<p><strong>22. Change documents for classification</strong></p>\r\n<p>Change documents for classification coming from the activated data in the backend are not shown in MDG-M.</p>\r\n<p><strong>23. Context Based Adaptation (CBA)</strong></p>\r\n<p>Classification UIBB cannot be configured using CBA.</p>\r\n<p><strong>24. Extensibility for SOA service</strong></p>\r\n<p>The SOA service MaterialERPBulkReplicateRequest cannot be extended to cover also flex entities. It only supports extensibility for reuse area attributes/tables.</p>\r\n<p><strong>25. Purchasing tax data and sales tax data are not updated after changes in SPRO</strong></p>\r\n<p>Customizing changes that are relevant for tax classification are not taken into account in MDG-M, when changing an existing material. For example, if you use the IMG activity \"Enterprise Structure -&gt; Assignment -&gt; Sales and Distribution -&gt; Assign sales organization - distribution channel - plant\" to add an additional plant to a distribution chain, the tax classification for a different country may be relevant as a result. Likewise, new tax categories may be defined or assigned. These countries are not taken into account when you change a material. It is also not possible to add these countries to the tax classification manually in MDG-M.</p>\r\n<p>Mitigation option: See OSS note 2058369</p>\r\n<p><strong>26.&#160;Reference characteristics cannot be used as search attributes</strong></p>\r\n<p>Reference characteristics cannot be selected as characteristics in the MDG-M search UI.</p>\r\n<p>As a replacement: attributes of the MM model can anyway used as search criteria. If they are not already available as search attributes, please check the following how-to guide:&#160;<a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-29002\">http://scn.sap.com/docs/DOC-29002</a></p>\r\n<p><strong>27. Customer function calls (User exits in SMOD)</strong></p>\r\n<p>The customer function calls CLFM0001, CLFM0002 and CLFM0003 as defined in transaction SMOD are not considered when running MDG-M.</p>\r\n<p><strong>28. Printing</strong></p>\r\n<p>MDG-M provides printing capabilities for change requests. But the following entities are not part of the printing template: Production Version, Quality Inspection Setup, MRP Areas and Material Ledger (mitigation: see how-to guide <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-51871\">http://scn.sap.com/docs/DOC-51871</a>).</p>\r\n<p><strong>29. Key mapping for Long Material Number (LAMA)</strong></p>\r\n<p>MDG does not support key mapping for object identifier type code 20 (&#8220;Material ID &#8211; internal representation&#8221;), if the DIMP LAMA solution is active in the system.</p>\r\n<p><strong>30. Usage of MDG on S/4HANA</strong></p>\r\n<p>When upgrading an&#160;MDG system using Long Material Number (LAMA)&#160;to S/4HANA you have the following restriction:</p>\r\n<p>For the usage of Long Material Numbers we recommend the \"target mode\" for your client. If you use the \"compatibility mode\", change documents for active materials are not supported (in MDG-M as well in MM backend transactions).</p>\r\n<p><strong>31. Valuation for a Single Batch</strong></p>\r\n<p>MDG does not support materials with&#160;Split Valuation&#160;and&#160;Valuation Category = X (automatic valuation, only for batch) or custom valuation categories with indicator: valuation type is set automatically (KZBAA in T149C).</p>\r\n<p><strong>32. Classification: Classes as BOM items</strong></p>\r\n<p>It is not possible to use classes, assigned to a class type which allows the function Class node, with a value in field Base Unit of Measure (Default data for component) on tab Additional data.</p>\r\n<p>&#160;</p>\r\n<p><em><span style=\"text-decoration: underline;\"><strong>MDG, Consolidation and Mass Processing</strong></span></em></p>\r\n<p>The following restrictions exist for the processes&#160;<em>Consolidation of Master Data</em>&#160;and&#160;<em>Mass Processing of Master Data</em>.</p>\r\n<p><strong>1. Support&#160;for classification</strong></p>\r\n<p>Classification is not supported in consolidation and mass processing.</p>\r\n<p><strong>2. Matching with SAP HANA fuzzy search</strong></p>\r\n<p>Longtexts for document assignments (MARA_DRAD_STXH) are not supported in match configurations.</p>\r\n<p><strong>3. Show changed&#160;field values&#160;in Fiori UI</strong></p>\r\n<p>Changed values between the previous process step and the current step&#160;are highlighted in the Fiori UI. When processing more than 100000 records&#160;these changes are not tracked anymore and are not visible in the Fiori UI due to performance reasons.</p>\r\n<p><strong>4. Engineering Change Number</strong></p>\r\n<p>You can use the engineering change number (ECN) to make changes (or assign a revision level) to existing products using MDG, Mass Processing. To apply a change number, you have to use the file upload capability of Mass Processing by providing an entry in table AEOI. AEOI entries of active products are not considered in Mass Processing. Please check Note <a target=\"_blank\" href=\"/notes/3074922\">3074922</a>.</p>\r\n<p><strong>5. Activation using MDG Change Request</strong></p>\r\n<p>In the activation step for consolidation or mass processes you can select the option to create an MDG change request instead of activating the data directly. In this case you have to consider, that there are differences in the data model for MM used in consolidation/mass processing and the data model in MDG central governance. Fields, which are not part of the data model in MDG central governance cannot be activated using a change request. Also fields, which are not part of the governance scope cannot be activated.</p>\r\n<p>Exceptional large objects with large numbers of sub-entities (e.g. products with many plants, valuation and classification data) should not be activated into a change request. This could lead to performance problems and timeouts.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Classification, Engineering Change Management, Class Type, Restriction, SOA, DMS, Document Management System, Material Ledger, Consolidation, Mass Processing</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>n/a</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>n/a</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-MDG-APP-CLF (Classification)"}, {"Key": "Other Components", "Value": "CA-MDG-CMP-MM (Material)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D043257)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D020450)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002284745/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002284745/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002284745/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002284745/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002284745/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002284745/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002284745/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002284745/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002284745/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3245531", "RefComponent": "CA-MDG-APP-MM", "RefTitle": "MDG-M: Usage of MDG Tools and Processes", "RefUrl": "/notes/3245531 "}, {"RefNumber": "2559917", "RefComponent": "CA-MDG-APP-MM", "RefTitle": "Material Variants in MDG-M", "RefUrl": "/notes/2559917 "}, {"RefNumber": "2349002", "RefComponent": "CA-MDG", "RefTitle": "SAP S/4HANA Master Data Governance 1610: Release Information Note", "RefUrl": "/notes/2349002 "}, {"RefNumber": "2291824", "RefComponent": "CA-MDG", "RefTitle": "SAP Master Data Governance 9.0: Release Information Note", "RefUrl": "/notes/2291824 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "MDG_APPL", "From": "620", "To": "620", "Subsequent": ""}, {"SoftwareComponent": "MDG_APPL", "From": "801", "To": "801", "Subsequent": ""}, {"SoftwareComponent": "MDG_MDC", "From": "200", "To": "200", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}