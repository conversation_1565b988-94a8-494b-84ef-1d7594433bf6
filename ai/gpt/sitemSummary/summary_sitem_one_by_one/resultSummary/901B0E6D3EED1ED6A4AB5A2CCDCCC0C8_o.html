<h3>SI1: FIN_SLL_CLS</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2376556">2376556 - S/4 HANA - Material classification with commodity codes in SAP S/4HANA 1610</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA 1610 or a later release. You classify materials with commodity codes in transaction MM01 / MM02 or you classify articles with commodity codes in transaction MM41 / MM42. This functionality (material/article classification with commodity codes) is redesigned in SAP S/4HANA 1610. Following redesign applies:</p>
<p>1. Management of commodity codes
<ul>
<li>Old in ECC and S/4HANA 1511: not time-dependent, valid for a country.</li>
<li>New in S/4HANA 1610 onwards: time-dependent, valid for a country or multiple countries, new app, new data model.</li>
</ul></p>
<p>2. Classification of materials/articles with commodity codes
<ul>
<li>Old in ECC and S/4HANA 1511: not time-dependent, valid for material/article and plant/site, done in material master apps (e.g. MM01, MM02) or article master apps (e.g. MM41, MM42), classification of materials/articles can be distributed via MATMAS/ARTMAS IDoc (outbound and inbound processing possible) and inbound processing via BAPIs supported.</li>
<li>New in S/4HANA 1610 onwards: time-dependent, valid for material/article and a country or multiple countries, functionality accessible only via FIORI apps, new data model, classification of materials/articles cannot be distributed via MATMAS/ARTMAS IDoc (inbound processing not possible anymore, only simplified outbound processing supported) and inbound processing via BAPIs not supported anymore.</li>
</ul></p>
<p><strong>Business Process Impact:</strong></p>
<p>The redesign of the material classification with commodity codes in SAP S/4HANA 1610 requires the usage of new FIORI apps and impacts how commodity codes are managed and classified within the system. Data migration and consistency checks are critical to ensure successful conversion. Custom code must be adapted to interface with the new data model, and user training on the new tools is essential.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Inconsistent data is identified by simplification item consistency check and is not taken over in the new data model. If there is inconsistent data, you can check and correct it before conversion is triggered.</p>
<p>Go through KBA 2432527 for detailed information on classification functionality in S/4HANA</p>
<p>Knowledge transfer to key and end users</p>
<p>Run ATC checks for custom code adaptation.</p>
<p>Execute consistency check report as mentioned in SAP note 2332472</p>
<p>Implement Fiori apps described in SAP note 2432527.</p>
<p><strong>Reference Notes:</strong> 
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/2378796">2378796 - Material classification: Change in data model in SAP S/4HANA 1610</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2332472">2332472 - Pre-Transition Checks for Software Component SD-FT-PRO (Foreign Trade Basic Functions)</a></li>
</ul>
</p>