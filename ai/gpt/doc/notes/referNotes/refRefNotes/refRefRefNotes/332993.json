{"Request": {"Number": "332993", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 988, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001396382017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000332993?language=E&token=2209CD9B9D3E1FD67E6AFEB5FC176328"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000332993", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000332993/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "332993"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.09.2000"}, "SAPComponentKey": {"_label": "Component", "value": "IS-OIL-DS-MAP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Marketing, Accounting and Pricing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oil", "value": "IS-OIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Downstream", "value": "IS-OIL-DS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL-DS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Marketing, Accounting and Pricing", "value": "IS-OIL-DS-MAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL-DS-MAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "332993 - Formula Price calculation in relation to quantity"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><b>********************************************************************** * WARNING: This is an IS-OIL / IS-MINE-specific note. If you DON'T&#x00A0;&#x00A0; * * have IS-OIL / IS-MINE installed on your system, this note does&#x00A0;&#x00A0;&#x00A0;&#x00A0; * * not apply to you. If this note is applied and you do not have&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;* * IS-OIL / IS-MINE installed, you could cause serious damage to&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;* * your system.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; * **********************************************************************</b><br /> <OL>1. You are using IS-OIL module</OL> <OL>2. A sales contract is created with Formula &amp; Average price condition record which determines the price of the item.</OL> <OL>3. The rate currency for the Formula &amp; Average calculation is different from that of the document currency.</OL> <OL>4. When order is created with reference to above contract with pricing date in the future (quotation fixation period in future) and no quotations exist for the fixation period which lies in future and with only the final rules being defined in the formula for the F&amp;A condition type, the condition value is not recalculated (should result in 0 value) and an inccorect value is proposed.</OL> <OL>5. As a consequence of the above problem, when the quantity is changed to 0 in the order, a core dump occurs in the system.</OL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IS-OIL, IS-OIL-DS-MAP, Formula and Average pricing, quotation fixation period in future, sales order called off from the contract, formula value not recalculated, core dump when quantity in the order changed to 0, form TERM_ITEM_EVALUATION, function group OICQ.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Programming error.<br />The error is due to programming error in form routine TERM_ITEM_EVALUATION in funtion group OICQ, where after setting the term status to quot_lookup_error (fixation period being in the future and no quoations exist for the quotation period which lies in future), the formula value is not recalculated with the correct fixation period. As an consequence of the above problem, when the quantity in the order changes to 0, core dump occurs in the system</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Please follow the instructions given below.<br />The exit statement after setting the term status to quot_lookup_error is commented out in form routine TERM_ITEM_EVALUATION in function group OICQ. Also the flag variable l_skip_loop is set which skips only the subsequent do loop and the formula is recalculated with the correct quotation period.<br />Please implement this note to correct the problem.<br />31H -------------------------------------------------------------------<br />Download and import Transport SODK006314.<br />The transport files are available on the following servers:<br />SAPSERVx: ~ftp/specific/isoil/31H/NOTES/Note.0332993/...<br />The object list for this transport can be found on:<br />SAPSERVx: ~ftp/specific/isoil/31H/NOTES/Note.contents/SODK006314.TXT<br />This transport should only be applied to IS-OIL systems (see Note 47531). Note 13719 describes how to import a correction to a customer system.</p> <b>IMPORTANT supplementary information: Before installing the note as described above, refer to OSS Note 98642 and/or 98876 for the correct sequence.</b><br /> <p><br />40B--------------------------------------------------------------------<br />Download and import Transport SOEK006652.<br />The transport files are available on the following servers:<br />SAPSERVx: ~ftp/specific/isoil/40B/NOTES/Note.0332993/...<br />The object list for this transport can be found on:<br />SAPSERVx: ~ftp/specific/isoil/40B/NOTES/Note.contents/SOEK006652.TXT<br />This transport should only be applied to IS-OIL systems (see Note 47531). Note 13719 describes how to import a correction to a customer system.</p> <b>IMPORTANT supplementary information: Before installing the note as described above, refer to SAPnet Note 145850 and/or 145854 for the correct sequence.</b><br /> <p>46B--------------------------------------------------------------------<br />Download and import Transport SOFK001453.<br />The transport files are available on the following servers:<br />SAPSERVx: ~ftp/specific/isoil/46B/NOTES/Note.0332993/...<br />The object list for this transport can be found on:<br />SAPSERVx: ~ftp/specific/isoil/46B/NOTES/Note.contents/SOFK001453.TXT<br />This transport should only be applied to IS-OIL / IS-MINE systems (see Note 47531). Note 13719 describes how to import a correction to a customer system.</p> <b>IMPORTANT supplementary information: Before installing the note as described above, refer to SAPnet Notes 312430, 312435 and/or 312371 for the correct sequence.</b><br /></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I017776"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332993/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332993/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332993/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332993/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332993/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332993/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332993/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332993/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332993/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98876", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98876"}, {"RefNumber": "98642", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 2.0d & 1.0d on R/3 3.1H (SP)", "RefUrl": "/notes/98642"}, {"RefNumber": "47531", "RefComponent": "IS-OIL", "RefTitle": "IS-OIL / IS-MINE / IS-CWM correction guideline", "RefUrl": "/notes/47531"}, {"RefNumber": "397831", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 57-60 incl. 4.0B", "RefUrl": "/notes/397831"}, {"RefNumber": "375158", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL: Additional Info - SPs 54-56 incl. 4.0B", "RefUrl": "/notes/375158"}, {"RefNumber": "355923", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/355923"}, {"RefNumber": "355922", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/355922"}, {"RefNumber": "351482", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 45-53 incl. 4.0B", "RefUrl": "/notes/351482"}, {"RefNumber": "351475", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 45-53 incl. 4.0B", "RefUrl": "/notes/351475"}, {"RefNumber": "312430", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL / IS-MINE 4.6B Notes (SAP_APPL)", "RefUrl": "/notes/312430"}, {"RefNumber": "179557", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Pricing date> sy-datum, quotation error routine not trigger.", "RefUrl": "/notes/179557"}, {"RefNumber": "145854", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (LCP)", "RefUrl": "/notes/145854"}, {"RefNumber": "145850", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (SP)", "RefUrl": "/notes/145850"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "351482", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 45-53 incl. 4.0B", "RefUrl": "/notes/351482 "}, {"RefNumber": "375158", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL: Additional Info - SPs 54-56 incl. 4.0B", "RefUrl": "/notes/375158 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "397831", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 57-60 incl. 4.0B", "RefUrl": "/notes/397831 "}, {"RefNumber": "145850", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (SP)", "RefUrl": "/notes/145850 "}, {"RefNumber": "145854", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (LCP)", "RefUrl": "/notes/145854 "}, {"RefNumber": "351475", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 45-53 incl. 4.0B", "RefUrl": "/notes/351475 "}, {"RefNumber": "312430", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL / IS-MINE 4.6B Notes (SAP_APPL)", "RefUrl": "/notes/312430 "}, {"RefNumber": "179557", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Pricing date> sy-datum, quotation error routine not trigger.", "RefUrl": "/notes/179557 "}, {"RefNumber": "98642", "RefComponent": "0 IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 2.0d & 1.0d on R/3 3.1H (SP)", "RefUrl": "/notes/98642 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31H", "To": "31H", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "IS-OIL", "From": "2.0D/1.0D", "To": "2.0D/1.0D", "Subsequent": ""}, {"SoftwareComponent": "IS-OIL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "IS-OIL", "From": "46B", "To": "46B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-OIL 2.0D/1.0D", "SupportPackage": "SAPKI3HC81", "URL": "/supportpackage/SAPKI3HC81"}, {"SoftwareComponentVersion": "IS-OIL 40B", "SupportPackage": "SAPKI40C45", "URL": "/supportpackage/SAPKI40C45"}, {"SoftwareComponentVersion": "IS-OIL 46B", "SupportPackage": "SAPKI46H14", "URL": "/supportpackage/SAPKI46H14"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-OIL", "NumberOfCorrin": 3, "URL": "/corrins/0000332993/11"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-OIL", "ValidFrom": "2.0D/1.0D", "ValidTo": "2.0D/1.0D", "Number": "179557 ", "URL": "/notes/179557 ", "Title": "Pricing date> sy-datum, quotation error routine not trigger.", "Component": "IS-OIL-DS-MAP"}, {"SoftwareComponent": "IS-OIL", "ValidFrom": "2.0D/1.0D", "ValidTo": "2.0D/1.0D", "Number": "179606 ", "URL": "/notes/179606 ", "Title": "MAP: Wrong currency exchange rate for F&A condition", "Component": "IS-OIL-DS-MAP"}, {"SoftwareComponent": "IS-OIL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "179557 ", "URL": "/notes/179557 ", "Title": "Pricing date> sy-datum, quotation error routine not trigger.", "Component": "IS-OIL-DS-MAP"}, {"SoftwareComponent": "IS-OIL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "179606 ", "URL": "/notes/179606 ", "Title": "MAP: Wrong currency exchange rate for F&A condition", "Component": "IS-OIL-DS-MAP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}