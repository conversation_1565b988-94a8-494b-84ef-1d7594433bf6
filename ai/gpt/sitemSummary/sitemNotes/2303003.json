{"Request": {"Number": "2303003", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 607, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013607522017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002303003?language=E&token=A8CE7CB221F825889AF26D0BA6AC7737"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002303003", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002303003/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2303003"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.02.2020"}, "SAPComponentKey": {"_label": "Component", "value": "LO-MD-BP-SYN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Synchronization Business Partner with Customer/Vendor"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Logistics Basic Data", "value": "LO-MD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-MD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partners", "value": "LO-MD-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-MD-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Synchronization Business Partner with Customer/Vendor", "value": "LO-MD-BP-SYN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-MD-BP-SYN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2303003 - S4TC BP data exchange between SAP S/4HANA, on-premise edition and SAP CRM: Pre-check class for BAdI implementation CVI_MAPPER"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note delivers pre-conversion checks regarding the business partner data exchange in an integration scenario of SAP S/4HANA, on-premise solution with SAP CRM. The checks must be executed before the Customer Vendor Integration (CVI) is activated and the mass synchronization based on the customer master or vendor master is performed.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Conversion report, CL_S4_CHECKS_CRMBP,&#160;Precheck, mass synchronization, S4TC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The pre-conversion check&#160;CHECK_BADI_IMPLEMENTED is executed to ensure that&#160;the enhancement implementation PI_BP_SET_BP_GUID_FROM_CRM is available in SAP ERP.&#160;For more information, see SAP Note 2283695.</p>\r\n<p>It is imperative that SAP Note 2283695 is implemented before the Customer Vendor Integration (CVI) is activated and the mass synchronization of customer master or customer vendor data for the generation of business partners is started. If you start the synchronization before SAP Note 2283695 has been implemented, the mapping between business partners that is used in integration scenarios with SAP CRM is irretrievably lost.</p>\r\n<p>The pre-conversion check CVI_CRM_MAPPING identifies any existing inconsistencies (if you have been working with an active CVI all along) or any inconsistencies that appear after mass synchronization. If CVI mappings do not correspond to&#160;the CRM mappings, this is detected.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>For information about the various checks, see SAP Note&#160;2304337.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CRM-MD-BP-IF (Data Exchange CRM Online <-> R/3)"}, {"Key": "Other Components", "Value": "AP-MD-BP-SYN (Business Partner Synchronization)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D025609)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D053797)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002303003/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002303003/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002303003/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002303003/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002303003/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002303003/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002303003/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002303003/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002303003/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2285062", "RefComponent": "CRM-MD-BP-IF", "RefTitle": "S4TWL: Business partner data exchange between SAPCRM and S/4 HANA, on-premise edition", "RefUrl": "/notes/2285062"}, {"RefNumber": "2283695", "RefComponent": "CRM-MD-BP", "RefTitle": "Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables", "RefUrl": "/notes/2283695"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2713963", "RefComponent": "XX-SER-MCC", "RefTitle": "FAQ: CVI - Customer Vendor Integration for system conversion to SAP S/4HANA", "RefUrl": "/notes/2713963 "}, {"RefNumber": "2304337", "RefComponent": "LO-MD-BP-SYN", "RefTitle": "Description note for Pre Check Class for Badi implementation CVI_MAPPER", "RefUrl": "/notes/2304337 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0002303003/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0002303003/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "2283695 ", "URL": "/notes/2283695 ", "Title": "Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables", "Component": "CRM-MD-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "2283695 ", "URL": "/notes/2283695 ", "Title": "Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables", "Component": "CRM-MD-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "2283695 ", "URL": "/notes/2283695 ", "Title": "Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables", "Component": "CRM-MD-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "2283695 ", "URL": "/notes/2283695 ", "Title": "Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables", "Component": "CRM-MD-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "2283695 ", "URL": "/notes/2283695 ", "Title": "Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables", "Component": "CRM-MD-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2283695 ", "URL": "/notes/2283695 ", "Title": "Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables", "Component": "CRM-MD-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2283695 ", "URL": "/notes/2283695 ", "Title": "Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables", "Component": "CRM-MD-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "616", "ValidTo": "617", "Number": "2283695 ", "URL": "/notes/2283695 ", "Title": "Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables", "Component": "CRM-MD-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "618", "ValidTo": "618", "Number": "2283695 ", "URL": "/notes/2283695 ", "Title": "Synchronization cockpit generates business partners with wrong GUIDs by ignoring CRM mapping tables", "Component": "CRM-MD-BP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}