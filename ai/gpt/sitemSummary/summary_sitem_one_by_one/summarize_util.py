import logging
import requests
from bs4 import BeautifulSoup

# copied from ai/gpt/sitemSummary/summary_sitem_one_by_one/summarize_sitem_in_html_format.py
# changing the get_item_summary function to return the summary instead of writing it to a file

prompt_msg = """
Assume you are an SAP expert, particularly adept at helping customers convert an SAP ERP 6.0 system to SAP S/4HANA or upgrade existing SAP S/4HANA system to newer product version.
You are familiar with two proprietary terms:

1. **SAP Notes**: These describe a software issue and its solution, normally including some typical sections, like "Symptom", "Reason and Prerequisites" and "Solution". "Symptom" refers to a specific situation, scenario, system state, or technical challenge that arises during an upgrade, conversion, or due to compatibility issues, which indicates an underlying issue, triggers the need for solutions, and determines the applicability and relevance of an SAP note to the user's current system or planned changes. "Reason and Prerequisites" and "Solution" are referring to the literal meaning.

2. **Simplification Item**: This helps customers prepare for the transition to SAP S/4HANA and SAP BW/4HANA by providing a description of all relevant changes that might have an impact when converting from SAP ERP to SAP S/4HANA. A Simplification Item typically includes an ID, Title, Business Impact SAP Note, and Activities.

I will provide you with a Simplification Item JSON, including its corresponding Business Impact SAP Note, Activities, and other information. Some Simplification Items may have no activities.

Your task is to first understand this Simplification Item, then provide a summary of this item based only on the information I give.
Don't directly pick up some of the content in the document I provided,but to summarize the content. 
The summary is for a SAP customer user logged into the SAP Readiness Check. The customer is trying to solve the issue in this Simplification Item and wants to get a quick overview as well as a clear step by step list what he needs to do. Also, he needs to understand how this Simplification Item will impact his overall conversion or upgrade. 

The summary should be in HTML format and follow this structure:

<h3>{sitem Id}</h3>
<h4>{sitem Title}</h4>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/{note number}">{note number} - {note title}</a></strong></p>
<p><strong>Description:</strong></p>
<p>{Description of the note}</p>
<p><strong>Business Process Impact:</strong></p>
<p>{Business Process Impact based on the note information and reference notes}</p>
<p><strong>Required and Recommended Actions:</strong></p>
{If there is only one action:
<p>{what customer needs to do to implement this Simplification Item, based on the notes and activities. Identify the high-level tasks, including crucial details that would make them truly actionable(e.g., providing direct links or specific locations for those references).}</p>
If there are multiple actions:
<ol>
{A clear, easily followable step by step list for what customer needs to do to implement this Simplification Item, based on the notes and activities. Identify the high-level tasks, including crucial details that would make them truly actionable(e.g., providing direct links or specific locations for those references).}
</ol>}
<p><strong>Reference Notes:</strong></p>
<ul>
<li><a href= Link of SAP Note> {note title}</a></li>
</ul>

**Important:** Strictly follow the given HTML structure to generate the content.
**Important:** Do not include ```html tags in the returning content
**Important:** Do not add other headings or subheadings in the content.
**Important:** Do not add directly copied content from the provided document, like Activities, Notes, or other content.
**Important:** In Business Impact Note and Reference Notes, if there is no note number in note title, add note number so the title follow this pattern: note number - note title.
**Important:** Do not show Reference Notes paragraph if the refer_note array from the JSON is empty.
**Important:** In Reference Notes, only use items from refer_note array, and make sure all items from the refer_note array and items from refer_note's nested refer_note array in the provided JSON are all listed in the unordered list.
**Important:** If no actions needed, or no expected business impact, then clearly states that no action is required or no business impact. If possible, elaborate on why this is the case, to provide more context and reassurance to the customer.
**Important:** Understand first, think step by step, from system conversion or system upgrade perspective, then give the summary.
### Notes:
- Omit any section that does not have content.
- Ensure all HTML tags are properly closed and the format is consistent.
- Link of SAP Note is https://me.sap.com/notes/{note number}
"""


def get_API_token() -> str:
    client_id = "sb-a9868d84-4cbb-4e26-8be6-1442d51051b3!b313091|aisvc-662318f9-ies-aicore-service!b540"
    client_secret = "894922a4-4635-4766-b00b-a63c963ae7bc$EsobP8Zk1BTwCBCivtnOopYfkZlTRyyaR7OgGqe4oOQ="

    params = {"grant_type": "client_credentials"}
    resp = requests.post(f"https://sapit-core-playground-vole.authentication.eu10.hana.ondemand.com/oauth/token",
                         auth=(client_id, client_secret),
                         params=params)
    token = resp.json()["access_token"]
    return token


def get_completion_with_claude(msg, guid, system_msg=prompt_msg, temperature=1):
    auth_token = get_API_token()
    svc_url = "https://api.ai.prod.eu-central-1.aws.ml.hana.ondemand.com/v2/inference/deployments/d5b49d430ef72ad6/invoke"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json",
        "AI-Resource-Group": "a9868d84-4cbb-4e26-8be6-1442d51051b3",
    }
    request_body = {
        "temperature": temperature,
        "anthropic_version": "bedrock-2023-05-31",
        "max_tokens": 200000,
        "system": system_msg,
        "messages": [
            {"role": "user", "content": msg}
        ]
    }

    response = requests.post(svc_url, headers=headers, json=request_body)
    try:
        summary = response.json()['content'][0]['text']
    except:
        logging.warning(f"{guid} Error in completion: {response.content}")
        summary = ''
    return summary


def get_completion_with_gemini(msg, guid, system_msg=prompt_msg, temperature=1):
    auth_token = get_API_token()
    svc_url = "https://api.ai.prod.eu-central-1.aws.ml.hana.ondemand.com/v2/inference/deployments/db1ee5ec51f3f35c/models/gemini-1.5-pro-002:generateContent"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "AI-Resource-Group": "a9868d84-4cbb-4e26-8be6-1442d51051b3",
    }
    request_body = {
        "contents": [
            {"role": "model", "parts": {"text": system_msg}},
            {"role": "user", "parts": {"text": msg}}
        ],
        "generationConfig": {
            "temperature": temperature,
        }
    }

    response = requests.post(svc_url, headers=headers, json=request_body)
    try:
        summary = response.json()["candidates"][0]["content"]["parts"][0]["text"]
    except:
        logging.error(f"{guid} Error in completion: {response.content}")
        logging.error("Failed to generate the summary.")
        summary = ''
    return summary

def call_orchestration_service(msg, guid, type="filter", system_msg=prompt_msg, temperature=1):
    auth_token = get_API_token()
    svc_url = "https://api.ai.prod.eu-central-1.aws.ml.hana.ondemand.com/v2/inference/deployments/d4988db264d98e37/v2/completion"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json",
        "AI-Resource-Group": "default",
    }

    if type == "filter":
        request_body = {
            "placeholder_values": {
                "input": msg
            },
            "config": {
                "modules": {
                    "prompt_templating": {
                        "model": {
                            "name": "gemini-1.5-pro",
                            "params": {
                                "max_tokens": 300,
                                "temperature": temperature
                            }
                        },
                        "prompt": {
                            "template": [
                                {
                                    "content": "{{?input}}",
                                    "role": "user"
                                }
                            ]
                        }
                    },
                    "filtering": {
                        "input": {
                            "filters": [
                                {
                                    "type": "llama_guard_3_8b",
                                    "config": {
                                        "code_interpreter_abuse": True,
                                        "elections": True,
                                        "intellectual_property": True,
                                        "sex_crimes": True,
                                        "child_exploitation": True,
                                        "violent_crimes": True,
                                        "defamation": True,
                                        "non_violent_crimes": True,
                                        "specialized_advice": True,
                                        "privacy": True,
                                        "indiscriminate_weapons": True,
                                        "hate": True,
                                        "self_harm": True,
                                        "sexual_content": True
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
    else:  # mask
        request_body = {
            "placeholder_values": {
                "input": msg
            },
            "config": {
                "modules": {
                    "prompt_templating": {
                        "model": {
                            "name": "gemini-1.5-pro",
                            "params": {
                                "max_tokens": 300,
                                "temperature": temperature
                            }
                        },
                        "prompt": {
                            "template": [
                                {
                                    "content": "{{?input}}",
                                    "role": "user"
                                }
                            ]
                        }
                    },
                    "masking": {
                        "masking_providers": [
                            {
                                "type": "sap_data_privacy_integration",
                                "method": "anonymization",
                                "entities": [
                                    {"type": "profile-person"},
                                    {"type": "profile-email"},
                                    {"type": "profile-phone"}
                                ]
                            }
                        ]
                    }
                }
            }
        }

    response = requests.post(svc_url, headers=headers, json=request_body)
    try:
        return response.json()
    except Exception as e:
        logging.error(f"{guid} Error in orchestration service: {response.content}")
        logging.error(f"Exception: {str(e)}")
        return None



def get_completion_with_gpt4_32k(msg, system_msg="", temperature=1):
    auth_token = get_API_token()
    svc_url = "https://api.ai.prod.eu-central-1.aws.ml.hana.ondemand.com/v2/inference/deployments/dbb58e36fb7f1100/chat/completions?api-version=2024-10-21"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json",
        "AI-Resource-Group": "a9868d84-4cbb-4e26-8be6-1442d51051b3",
    }
    request_body = {
        "temperature": temperature,
        "messages": [
            {"role": "system", "content": system_msg},
            {"role": "user", "content": msg}
        ]
    }

    response = requests.post(svc_url, headers=headers, json=request_body)
    try:
        result = response.json()['choices'][0]['message']['content']
    except:
        logging.warning(f"Error in completion: {response.content}")
        result = ""
    return result


def get_completion(msg, system_msg="", temperature=1):
    auth_token = get_API_token()
    svc_url = "https://api.ai.prod.eu-central-1.aws.ml.hana.ondemand.com/v2/inference/deployments/d845443dfb9de2f6/chat/completions?api-version=2024-10-21"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json",
        "AI-Resource-Group": "a9868d84-4cbb-4e26-8be6-1442d51051b3",
    }
    request_body = {
        "temperature": temperature,
        "messages": [
            {"role": "system", "content": system_msg},
            {"role": "user", "content": msg}
        ]
    }

    response = requests.post(svc_url, headers=headers, json=request_body)
    try:
        result = response.json()['choices'][0]['message']['content']
    except:
        logging.warning(f"Error in completion: {response.content}")
        result = ""
    return result


def get_completion_with_gpt_4o_mini(msg, system_msg = ""):
    auth_token = get_API_token()
    svc_url = "https://api.ai.prod.eu-central-1.aws.ml.hana.ondemand.com/v2/inference/deployments/dbb733e42c4f6c8f/chat/completions?api-version=2023-05-15"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json",
        "AI-Resource-Group": "a9868d84-4cbb-4e26-8be6-1442d51051b3",
    }
    request_body = {
        "temperature": 0,
        "messages": [
            {"role": "system", "content": system_msg},
            {"role": "user", "content": msg}
        ]
    }
    response = requests.post(svc_url, headers=headers, json=request_body)
    try:
        result = response.json()['choices'][0]['message']['content']
    except:
        logging.warning(f"Error in completion: {response.content}")
        result = ""
    return result

def get_summary_gpt_4o(msg, guid):
    summary = get_completion(msg, prompt_msg)
    if len(summary) == 0:
        logging.warning(f"{guid} Failed to generate the summary with gpt-4o.")
    return summary


def is_valid_html(html_string):
    try:
        soup = BeautifulSoup(html_string, 'html.parser')
        isValid = bool(soup.find())
        if not isValid:
            logging.warning("The generated summary is not valid HTML format.")
    except Exception:
        logging.warning("Failed to validate HTML.")
        isValid = False
    return isValid


def get_item_summary(item_info: dict) -> str:
    guid = item_info.get('guid')
    logging.info(f"get Summary Start for GUID: {guid}")
    # handle special case 00109B1315FA1ED9A1C226A27EBB20DA, the response is truncated due to limited response of GPT4o
    # remove this after GPT4o is updated from 05-13 to version 08-13 to generate longer output.
    summary = get_summary_gpt_4o(item_info.get('item_info'), guid) if guid != "00109B1315FA1ED9A1C226A27EBB20DA" else ""
    if len(summary) == 0 or not is_valid_html(summary):
        summary = get_completion_with_claude(item_info.get('item_info'), guid)
    if len(summary) == 0 or not is_valid_html(summary):
        summary = get_completion_with_gemini(item_info.get('item_info'), guid)
    logging.info(f"get Summary Done for GUID: {guid}")
    return summary
