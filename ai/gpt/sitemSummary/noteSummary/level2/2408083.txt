SAP Note 2408083 - FAQ: Data Model of S/4HANA Finance Data Migration provides a detailed FAQ on technical aspects of data migration within S/4HANA Finance, with a focus on the changes in the data model. Here is a summary of the key points from the note:

1. The note outlines how records from tables like BSEG, ANEP, COEP, and others are consolidated into the ACDOCA table during migration. It explains that BSEG and COEP records arising from the same transaction ("prima nota") are merged into single ACDOCA records if possible, while also noting that records cannot always be matched. Examples of data constellations and resulting ACDOCA records are provided in the attachment.

2. The meaning of ACDOCA fields MIG_SOURCE and BSTAT is clarified. MIG_SOURCE identifies the source of migrated records, while BSTAT='C' signifies balance carry forward items and items from migration of balances.

3. The note addresses why tables like COEP appear empty in SE16 before migration, explaining that several tables are replaced by compatibility views in S/4HANA Finance and that the data in the old tables can be accessed using “ORI” views, as indicated in SAP Note 1976487.

4. It details the exposure of ACDOCA records in compatibility views for COEP, FAGLFLEXA, etc., and explains why there may be more items shown in the compatibility views than in the original tables.

5. It explains that the migration of balances occurs to ensure that balances in FI-AA, CO, and G/L are not altered due to any differences between aggregated line items and the old totals tables.

6. The note states that verifying the correctness of data migration is complex and cannot be achieved by simply comparing the number of records in compatibility views to the original tables. Instead, reconciliation checks R23 and R24 included in the data migration process should be used.

7. It discusses how customer extensions like CI_COBL and append structures are handled during migration with references to further details in SAP Note 2160045.

8. Guidance for adapting custom code to the data model changes is provided by referencing several other SAP Notes (1976487, 2076652, 2185026, 2219527, 2221298, 2156822).

9. The specific conditions for merging BSEG and COEP records into single ACDOCA records are outlined.

10. For cases where multiple source records are merged into a single ACDOCA record, the note explains how fields are filled with reference to SAP Note 2156822.

11. The note describes why data migration might create document numbers starting with characters like ********** or ********** and provides reasoning for using technical document numbers.

References within SAP Note 2408083 include additional SAP Notes that offer guidance on various aspects related to data migration, such as adjusting customer-specific programs to the simplified data model, mapping of ACDOCA fields to old FI/CO tables, using compatibility views in custom programs, and optimizing the use of said views. These notes provide insights and instructions to manage possible challenges arising from data model changes in the S/4HANA environment, ensuring that data migration and subsequent operations are accurately and efficiently performed.