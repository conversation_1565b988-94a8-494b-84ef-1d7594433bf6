SAP Note 139176 addresses an issue where users encounter an error message M7302 stating 'Field overflow in routine WERT_SIMULIEREN in field NEUER_PREIS' when attempting to post a goods receipt. This error message indicates that the system is unable to display the new moving average price of a material due to a limitation in the number of digits that can be handled, resulting in the inability to post the goods movement.

The issue arises after implementing Note 76251, and it occurs because the moving average price being recalculated during goods receipt exceeds the numerical capacity of the field NEUER_PREIS in the SAP system.

To solve this problem, SAP Note 139176 suggests the following actions:

1. Change the moving average price of the material using Transaction MR21 for materials controlled by the moving average price, or directly in the accounting view of the material master for sales price controlled materials.
2. Set a price for the material that is smaller than the currently valid price.
3. Alternatively, utilize the attached report ZREPGLD to set the statistical moving average price equal to the current standard price. However, users should avoid using this report for valuation areas where the material ledger is activated; instead, they should use the report ZREPGLD_ML. This requires recalculation of prices if the material ledger is active.
4. If altering the material valuation is undesirable, users can opt to select a smaller price unit for the material, which will reduce the moving average price without changing the total material value.

The note also advises caution regarding the use of report ZREPGLD, especially in areas where the material ledger is activated, and the use of ZREPGLD_ML, which necessitates price recalculation.

Additionally, SAP Note 139176 references other notes:
- Note 76251, which details an issue of overflow during moving average price calculation during goods movement postings.
- Note 339169, which discusses an incorrect statistical MAP for materials with negative stocks and provides corrective actions.
- Note 202166, which serves as a collective note to guide users in fixing issues related to incorrect statistical MAP updates by referring to other relevant SAP Notes.

Users encountering the error M7302 should directly address the configuration of the moving average price, while also considering related issues covered in the referenced notes to ensure a comprehensive resolution of the problem.