SAP Note 576513 addresses frequently asked questions (FAQs) about change management in environments where there's a combination of a centralized Enterprise Resource Planning (ERP) system and a decentralized Warehouse Management System (WMS). This note is applicable as of SAP R/3 Enterprise 4.70.

Key points of the note are:

1. How to Activate Change Management:
   - Activate change management via IMG activity 'Generate Distribution Model' or report RLALEACU.
   - Set up ALE model with relevant methods (InboundDelivery.DeliveryChange and OutboundDelivery.DeliveryChange).
   - Establish RFC connections and set up cross-system lock CSL.
   - Use IDoc types SHP_OBDLV_SAVE_REPLICA02 and higher for outbound and SHP_IBDLV_SAVE_REPLICA02 and higher for inbound processing.

2. How to Deactivate Change Management:
   - Avoid deactivating change management when the system is operating to prevent inconsistencies.
   - If needed, permanently deactivate it by ensuring no ongoing processes in the decentralized system and reset the 'Change Management' indicator.
   - For temporary deactivation, use transaction LECMOFF for processing without centralized system influence.

3. Response to System Connection Breakdown:
   - Define system response via Customizing.
   - Manage CSL inconsistencies if the connection drops during an update. Use Note 762571 for assistance.

4. Determining the Reason for a Blocked Delivery:
   - Check if the partner system has confirmed changes or look for error messages.
   - Utilize transaction SMCL to monitor CSL token statuses.

5. Using the CSL Monitor (SMCL):
   - Requires authorization to release blocks, meant for system administrators.
   - Allows to track CSL token statuses to manage locks.

6. Releasing a Block:
   - Release block in SMCL when status is MOVD in both systems.
   - Do not release if status is REFD; instead, handle errors in the inbound IDoc.

7. Changes Transferred to Partner System:
   - IDocs are sent after simulating and saving changes in delivery dialog.
   - Specifies which fields are under change management.

8. Purpose of the Simulation:
   - Validates changes in the local system before sending an IDoc to partner system.
   - Ensures successful adaptation of changes by the partner system.
   - Simulation can be triggered manually via transactions VL02N and VL32N.

This note provides detailed answers to common queries concerning activating and managing change processes between centralized and decentralized systems, helping users to avoid inconsistencies and ensure smooth inter-system communication for delivery changes.