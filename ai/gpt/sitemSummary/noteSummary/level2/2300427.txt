SAP Note 2300427 outlines steps required for systems that have SAP Product and REACH Compliance 2.0, SAP REACH Compliance 1.1, or Compliance for Products 2.2 installed when preparing to upgrade to SAP S/4HANA. These systems have tasks with a processing history that needs to be retained post-upgrade.

The issue addressed by this note is that all database tables related to these products will be deleted during the upgrade to SAP S/4HANA, which means that the data within those tables will be lost. To prevent data loss, the note suggests moving the required data to database tables that are not part of the product.

The solution provided within the note involves implementing correction instructions or importing a specified Support Package. The note details the creation of new tables to store the task management data records that are currently in the archive tables, so they can be retained and accessed post-upgrade. It specifies the structure of these new tables—ZSRC_S4_TASKH for Task Header, ZSRC_S4_TASKI for Task Item, and ZSRC_S4_TASKD for Task Attachment—along with their relationships.

It also instructs to extend the new tables with any additional columns that might have been added to the archive tables, with a caution to avoid using data elements whose names begin with /TDAG/.

The note emphasizes that all changes from this note must be activated before proceeding with any subsequent SAP Notes.

References within the note include:
- SAP Note 2298456, which provides instructions for uninstalling certain products from the system using transaction SAINT.
- SAP Note 2275942, which discusses corrections contained in Support Package 08 for the add-on SAP Product and REACH Compliance 2.0. 

Conclusion: In summary, SAP Note 2300427 provides guidance for retaining task management data during an upgrade to SAP S/4HANA if certain compliance products are installed. It underscores the importance of data migration to new tables to avoid loss of processing history and underscores adhering to the steps outlined prior to upgrading.