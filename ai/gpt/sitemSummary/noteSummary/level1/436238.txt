SAP Note 436238 provides a comprehensive set of restrictions, limitations, recommendations, and workarounds related to the use of Grouping, Pegging, and Distribution (GPD) in SAP. Here is a summary of the key points mentioned in the note:

1. Size Restrictions: It is important to consider the size limitations of projects as larger project data volume is not supported by grouping.

2. Manual Maintenance: Without automatic requirements grouping, manual maintenance is necessary for WBS element assignments, using transactions GRM3, GRM4, or GRM5. Regular consistency checks using transaction GRM6 are recommended.

3. Performance Issues: Assigning numerous WBS elements to a group can lead to performance issues during various GPD-related activities, including MRP runs, availability checks, and pegging runs.

4. Network Components: Never assign a group WBS element to network components to avoid conflicts and performance issues.

5. Lot Sizing: Grouping should be used to manage materials beneficially affected by larger lot sizes, while plant stock should be used for lower-value materials to reduce the complexity of GPD.

6. Pegging: Avoid using groups valid for an entire plant when pegging, as this can necessitate a single large pegging run with possible performance issues. Instead, set up groups for specific projects.

7. Transfers Between Groups: Avoid using TBLP_TRLO for transfers between independent groups. Use alternative movement types to avoid future pegging run dependencies.

8. Optimization: Turn off pegging for purchase requisitions and planned orders when unnecessary to improve performance.

9. Manual Workarounds: For material to material transfers, manually create production orders for internally procured materials.

10. Pegging Data Synchronization: Ensure synchronization between pegging and project stock tables. Regular checks using transaction PEG10 are advisable.

11. GPD and S/4HANA: Be aware of limitations with cross-plant postings in release 4.6C/2 and S/4HANA integration.

12. Cost Distribution: Manage cost distribution efficiently by splitting group WBS elements into selection variants and distributing the load.

13. Non-Supported Transactions: Certain functionalities are not supported, including breakpoints with valuated project stock and multiple schedule lines in purchase orders.

14. Budget Management: Exercise caution when including grouping WBS elements in budget availability control, as actual costs may appear as zero after distribution.

15. Circular Assignments: Only use circular or recursive assignments with type 2 grouping WBS elements.

16. Compatibility with New General Ledger Accounting: For ECC-DIMP 500 and above, apply the specified notes for compatibility.

17. Blocked Stock: GPD does not allow goods issues from blocked stock, and an error message will be displayed in such cases.

18. GPD Replacement: SAP is developing Project Manufacturing Management & Optimization (PMMO) to eventually replace GPD in future S/4HANA OnPremise releases. GPD will continue to receive maintenance updates for existing customer issues, but primary feature enhancements will be focused on PMMO.

Users of GPD should review this note thoroughly and consider contacting an SAP consultant for further guidance. The note also references SAP Note 797046, which addresses the issue of doubled assigned values after DIS01 distribution and provides code changes to resolve the issue.