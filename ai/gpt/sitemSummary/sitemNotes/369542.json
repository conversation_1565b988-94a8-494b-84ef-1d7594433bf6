{"Request": {"Number": "369542", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 346, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014947382017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000369542?language=E&token=51CD3B6CA54D3522FCB228D83DED3AC8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000369542", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000369542/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "369542"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.01.2001"}, "SAPComponentKey": {"_label": "Component", "value": "PS-REV-IO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Project-Related Incoming Orders"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Project System", "value": "PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Revenues and Eamings", "value": "PS-REV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS-REV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-Related Incoming Orders", "value": "PS-REV-IO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS-REV-IO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "369542 - CJA1: Upgrade of Release 4.0 preliminary solution to >= 4.5"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>During the upgrade from Release 4.0B to Release 4.5A or higher, you cannot activate the CE* tables of the Profitability Analysis because data element KAZD_SORHI does not exist.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SORHIST, incoming orders</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The problem occurs only if you use the preliminary solution that was published with Note 118780 for the project-related incoming orders.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Overview</b><br /> <p>Before the upgrade, modify data element KAZD_SORHI and the related domain in Release 4.0 systems (text change only!). You should make the change in the development system and transport it into the production system. As a result, after the Repository switch, the data element and the domain are created in the target systemduring the upgrade.<br />After that, activation of the Profitability Analysis tables runs without errors.<br />After completion of the system upgrade, replace data element KAZD_SORHIwith RKE_SORHI in the affected tables (see below).<br />As a last step, adjust two Customizing tables (see below) and regenerate the environment of the affected operating concerns.<br />First of all, carry out these steps in the development system. Save all changes to a transportable request and import it after the system upgrade of the production system into the production system.<br /><br /></p> <b>Procedure in detail:</b><br /> <p></p> <OL>1. In the 4.0 development system, change the text for data element KAZD_SORHI and domain KAZD_SORHI. Save and activate the change. Transport the change into the production system.</OL> <OL>2. Carry out the system upgrade.</OL> <OL>3. Replace data element KAZD_SORHI in the table/structure mentioned below with RKE_SORHI.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example: Transaction SE11, table CE0&lt;ERGB&gt; -&gt; choose 'Change'. Search for the table field with data element KAZD_SORHI and replace it with RKE_SORHI. Save the change.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Remark: In the table mentioned below, you might have to replace character string &lt;ERGB&gt;with the four-digit ID of the operating concerns in question.<br /></p> <UL><UL><LI>CE0&lt;ERGB&gt;</LI></UL></UL> <UL><UL><LI>CE1&lt;ERGB&gt;</LI></UL></UL> <UL><UL><LI>CE2&lt;ERGB&gt;</LI></UL></UL> <UL><UL><LI>CE4&lt;ERGB&gt;</LI></UL></UL> <UL><UL><LI>CE4&lt;ERGB&gt;_KENC</LI></UL></UL> <UL><UL><LI>CE7&lt;ERGB&gt;</LI></UL></UL> <UL><UL><LI>CE8&lt;ERGB&gt;</LI></UL></UL> <UL><UL><LI>KOMPAKE</LI></UL></UL> <p></p> <OL>4. This may also apply to the following tables (if there is a referenceto data element KAZD_SORHI):</OL> <UL><UL><LI>COPACRIT</LI></UL></UL> <UL><UL><LI>COPABBSEG</LI></UL></UL> <UL><UL><LI>COPAFIELDS</LI></UL></UL> <UL><UL><LI></LI></UL></UL> <OL>5. In addition, use Transaction SM30to change the data element for characteristic SORHIST in views V_TKES and V_TKEF from KAZD_SORHI to RKE_SORHI.</OL> <OL>6. Call Transaction KEBC ('Set Operating Concern'). Specify the operating concern in question and confirm your entry. The system now regeneratesthe environment of the operating concern. The generation should run without error.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Prerequisite: The operating concern was active in Source Release 4.0.</p> <OL>7. Call Transaction KEA0. All traffic lights should be 'green', and the action log should not contain any error messages (Menu -&gt; Extras -&gt; Action log). Table COPABBSEG possibly generates warning messages during activation, for example; you can ignore these messages.</OL><OL>8. Use Transaction KE24to check on a sample basis that the data records with a sales order history characteristic between 1 and 5 were transferred correctly (KE24 -&gt; Additional Selection Criteria -&gt; Sales order history -&gt; restrict interval 1 to 5 period -&gt; select).</OL> <p><br />Remarks:<br />Tables KAZD_ORPDB and KAZD_ORPDP are transferred to the target system during the upgrade. These tables contain the document history of the Release 4.0 Add-On solution and are not required for the standard solution from Release 4.5.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PROJ-IMS-UPGR (Application Specific Upgrade Tools)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031049)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031049)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000369542/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000369542/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000369542/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000369542/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000369542/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000369542/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000369542/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000369542/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000369542/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "689574", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/689574"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062"}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285"}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "118780", "RefComponent": "PS-REV", "RefTitle": "Implement Project related Incoming Orders for 4.0", "RefUrl": "/notes/118780"}, {"RefNumber": "117668", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/117668"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062 "}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285 "}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373 "}, {"RefNumber": "118780", "RefComponent": "PS-REV", "RefTitle": "Implement Project related Incoming Orders for 4.0", "RefUrl": "/notes/118780 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}