SAP Note 2429588 is an informational document that provides details about ABAP Support Package 04 for SAP BW/4HANA 1.0 SP 04. This note serves as a central repository for all the corrections or enhancements included in Support Package 04 for SAP BW/4HANA 1.0. The note also mentions that it will be updated as new related notes are included.

Key points from the note include:

- A strong recommendation to apply the latest SAP HANA revision before importing the support package.
- A directive to read SAP Note 2248091 for information on handling the reimplementation of notes.
- An advisory to review SAP Note 2455856 regarding Nearline Storage (NLS) for the Advanced DataStore Object.
- Information that support for some application server platforms will be discontinued with future releases of SAP BW/4HANA, with reference to SAP Note 2620910 for recommended platforms.

The note also warns about potential issues that may arise after importing the Support Package, including:
- Situations where ABAP routines are not handled correctly or there are errors in transformations, with references to various other notes for solutions (e.g., SAP Notes 2491221, 2467802, and others).
- A specific issue with the generic delta of a DTP that may return duplicate or incorrect data, which is resolved in SP 6 or SAP Note 2500221.
- Various possible errors with ABAP routines and SAP HANA script routines after implementing BW/4HANA 1.0 SP4.

Finally, for a full list of errors corrected and important enhancements delivered with this Support Package, the note directs users to the SAP support package website.

References provided:
- SAP Note 2620910 discusses the supported application server platforms for different versions of SAP S/4HANA and SAP BW/4HANA and informs about the discontinuation of support for certain platforms in future releases.
- SAP Note 2347382 offers general information about SAP BW/4HANA, including installation, recommended SAP HANA revisions, and security corrections.
- SAP Note 2248091 provides a solution to a problem with the Note Assistant's handling of the reimplementation of successor notes without code changes.