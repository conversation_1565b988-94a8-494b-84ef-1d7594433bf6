{"Request": {"Number": "1158200", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1093, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006981022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001158200?language=E&token=49069F3FC69055BCF81177E703BF28A1"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001158200", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001158200/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1158200"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with High Priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.05.2008"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-AT-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "XX-CSC-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-AT-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-AT-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1158200 - IS-H: Patient Routing System"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1158200&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1158200/D\" target=\"_blank\">/notes/1158200/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the case of major accidents, the patient is flagged using the patient routing system (PLS). At the accident location, each patient receives an orange bag with an identification number (PLS number) reassigned.<br />You can now enter this PLS number in the accident data of NV2000. It is saved on a case-related basis.<br /><br />In the country version Austria (AT), the PLS number has nine digits and is defined as follows:</p> <UL><LI>1-Digit: State (0-9)</LI></UL> <UL><LI>2-character abbreviation for the emergency service</LI></UL> <UL><LI>2-Character: Subgroup of Emergency Service</LI></UL> <UL><LI>4-Digit: Sequence Number</LI></UL> <p><br />During entry, the system checks in the country version Austria (AT) whether the PLS number entered has nine digits.<br />In the public list (as of IS-H 6.00), in the case list and in the clinical work station (Outpatient Clinic and Occupancy/Arrivals/Departures view types), you can display the PLS number in the layout.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Patient Routing System PLS Number NFAL PLSNR NV2000</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>See symptom</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><OL>1. Before you implement the source code corrections, implement the attached attachment as follows:</OL> <OL><OL>a) Unpack the attached files:</OL></OL> <p>                       HW1158200_603.zip and HW1158200_603_cust.zip for IS-H Version 6.03 <p>                       HW1158200_600.zip and HW1158200_600_cust.zip for IS-H Version 6.00 <p>                       HW1158200_472.zip and HW1158200_472_cust.zip for IS-H Version 4.72 <p>                       Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>2. Import the unpacked requests into your system.</OL> <OL>3. Now implement the source code corrections from this SAP Note.</OL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON> (C5021076)"}, {"Key": "Processor                                                                                          ", "Value": "C5013524"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001158200/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1158200_603.zip", "FileSize": "66", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000096682008&iv_version=0003&iv_guid=77C3A70B07851243A653976E1E83BBD5"}, {"FileName": "HW1158200_600.zip", "FileSize": "66", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000096682008&iv_version=0003&iv_guid=1A40AE0DCE96624B95F0E24264C6574C"}, {"FileName": "HW1158200_600_cust.zip", "FileSize": "74", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000096682008&iv_version=0003&iv_guid=AA0AD21213CDD8469EC4A8B2C5269910"}, {"FileName": "HW1158200_472_cust.zip", "FileSize": "2", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000096682008&iv_version=0003&iv_guid=ABCA46FC12339A43A55569068B872C9A"}, {"FileName": "HW1158200_603_cust.zip", "FileSize": "74", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000096682008&iv_version=0003&iv_guid=200C59C11CA9674585A2CC1A72F8EFF6"}, {"FileName": "HW1158200_472.zip", "FileSize": "66", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000096682008&iv_version=0003&iv_guid=BA73517B48975D4FA5FCC55009C6C7CC"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF27", "URL": "/supportpackage/SAPKIPHF27"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60014INISH", "URL": "/supportpackage/SAPK-60014INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60203INISH", "URL": "/supportpackage/SAPK-60203INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60302INISH", "URL": "/supportpackage/SAPK-60302INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 3, "URL": "/corrins/0001158200/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 43, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "472", "Number": "742644 ", "URL": "/notes/742644 ", "Title": "IS-H: Timeout when calling case overview, readmission status", "Component": "IS-H-MD-DOC"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "744465 ", "URL": "/notes/744465 ", "Title": "IS-H*MED: Outpatient Clinic/Service Facility: Private Patient", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "745170 ", "URL": "/notes/745170 ", "Title": "IS-H/IS-H*MED: Clinical Work Station: Outpatient Clinic/Service Facility", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "753630 ", "URL": "/notes/753630 ", "Title": "IS-H: Sublist of public list displays too many patients", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "763054 ", "URL": "/notes/763054 ", "Title": "IS-H*MED: Clinical Work Station: Outpatient Clinic: OU Short Names", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "765311 ", "URL": "/notes/765311 ", "Title": "IS-H*MED: Clinical Work Station: Outpatient Clinic/Service Control - Diagnoses", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "771388 ", "URL": "/notes/771388 ", "Title": "IS-H: Error in Clinical Process Builder", "Component": "IS-H-PM-GEN"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "773855 ", "URL": "/notes/773855 ", "Title": "Pathways: Hotspot in CWS for Patient Pathway Status Icon", "Component": "XX-PART-ISHMED-DOC"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "807717 ", "URL": "/notes/807717 ", "Title": "IS-H: Number of Days Since Admission in NWP1 Incorrect", "Component": "IS-H-PM-CUM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "819202 ", "URL": "/notes/819202 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Facility - Tech. Document", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "822824 ", "URL": "/notes/822824 ", "Title": "IS-H: NV2000 - PBO BAdI for Accident Data", "Component": "IS-H-PM-INP"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "822882 ", "URL": "/notes/822882 ", "Title": "Accident Data: Create Movement - Accident Data Not Transferred", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "838523 ", "URL": "/notes/838523 ", "Title": "Clinical Work Station: Display Leading Zeros", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "839456 ", "URL": "/notes/839456 ", "Title": "IS-H AT: ELDA - Outpatient Service Report", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "842007 ", "URL": "/notes/842007 ", "Title": "IS-H AT: ELDA Outpatient Admission notification or Construction Progress Report", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "864320 ", "URL": "/notes/864320 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Facility: Document Status", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "874475 ", "URL": "/notes/874475 ", "Title": "Clinical Work Station: Outpatient Clinic - Performance Optimization", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "875622 ", "URL": "/notes/875622 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Master - Duplicate Entries", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "887406 ", "URL": "/notes/887406 ", "Title": "IS-H: NP47, Update of Mechanical Ventilation Hours Missing", "Component": "IS-H-MD-DOC"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "889488 ", "URL": "/notes/889488 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Facility - Performance", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "896436 ", "URL": "/notes/896436 ", "Title": "IS-H: Number assignment problem during batch input on NV2000", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "898290 ", "URL": "/notes/898290 ", "Title": "Clinical Work Station: Outpatient Clinic View Type: Lab Document Exists", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "903953 ", "URL": "/notes/903953 ", "Title": "Clin. Work Station: Outpatient Clinic/Service Facility - Performance", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "904630 ", "URL": "/notes/904630 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Station: Departmental OU Code", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "924909 ", "URL": "/notes/924909 ", "Title": "Clinical Work Station: Outpatient Clinic View - Column \\&quot;Proc.Indicator\\&quot;", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "981976 ", "URL": "/notes/981976 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Facility View Selection", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "982031 ", "URL": "/notes/982031 ", "Title": "IS-H AT: Scoring - Adjustments to Model 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1010978 ", "URL": "/notes/1010978 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Facility - Desired Date", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1034899 ", "URL": "/notes/1034899 ", "Title": "Clinical Work Station: Occupancy View Type - Worklist Column", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1050830 ", "URL": "/notes/1050830 ", "Title": "IS-H AT: EDI Message Dispatch with Comment (Delta)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1081108 ", "URL": "/notes/1081108 ", "Title": "Clinical Work Station: Documents View Type with SAP-ACM", "Component": "IS-H-ACM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1106037 ", "URL": "/notes/1106037 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Master View Type: Documents", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1128532 ", "URL": "/notes/1128532 ", "Title": "IS-H: Discharge Reason Mandatory Field Saved Without Value", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "600", "Number": "897746 ", "URL": "/notes/897746 ", "Title": "IS-H: Filter &#39;Patient/Age/Sex&#39; Column", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "600", "Number": "1100057 ", "URL": "/notes/1100057 ", "Title": "IS-H: Unnecessary message when maintaining death data", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "603", "Number": "1137376 ", "URL": "/notes/1137376 ", "Title": "IS-H: Message N1 436 appears twice when you save/print", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "874475 ", "URL": "/notes/874475 ", "Title": "Clinical Work Station: Outpatient Clinic - Performance Optimization", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "875622 ", "URL": "/notes/875622 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Master - Duplicate Entries", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "889488 ", "URL": "/notes/889488 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Facility - Performance", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "896436 ", "URL": "/notes/896436 ", "Title": "IS-H: Number assignment problem during batch input on NV2000", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "898290 ", "URL": "/notes/898290 ", "Title": "Clinical Work Station: Outpatient Clinic View Type: Lab Document Exists", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "903953 ", "URL": "/notes/903953 ", "Title": "Clin. Work Station: Outpatient Clinic/Service Facility - Performance", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "904630 ", "URL": "/notes/904630 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Station: Departmental OU Code", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "924909 ", "URL": "/notes/924909 ", "Title": "Clinical Work Station: Outpatient Clinic View - Column \\&quot;Proc.Indicator\\&quot;", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "959916 ", "URL": "/notes/959916 ", "Title": "Clin. Work Station: Outpatient Clinic View Type - Order Filler Selection", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "981976 ", "URL": "/notes/981976 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Facility View Selection", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "982031 ", "URL": "/notes/982031 ", "Title": "IS-H AT: Scoring - Adjustments to Model 2007", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1000486 ", "URL": "/notes/1000486 ", "Title": "Public List: System Parameter ADR_BER", "Component": "IS-H-PM-GEN"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1010978 ", "URL": "/notes/1010978 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Facility - Desired Date", "Component": "XX-PART-ISHMED-ORD"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1034597 ", "URL": "/notes/1034597 ", "Title": "Public List - Sublist <PERSON><PERSON> for First Names", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1034899 ", "URL": "/notes/1034899 ", "Title": "Clinical Work Station: Occupancy View Type - Worklist Column", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1050830 ", "URL": "/notes/1050830 ", "Title": "IS-H AT: EDI Message Dispatch with Comment (Delta)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1077952 ", "URL": "/notes/1077952 ", "Title": "IS-H: No Admitting Hospital for Absence Reason 16", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1081108 ", "URL": "/notes/1081108 ", "Title": "Clinical Work Station: Documents View Type with SAP-ACM", "Component": "IS-H-ACM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1106037 ", "URL": "/notes/1106037 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Master View Type: Documents", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1138058 ", "URL": "/notes/1138058 ", "Title": "IS-H AT: Error Message Geographical Area Can Be Set", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "603", "Number": "1128532 ", "URL": "/notes/1128532 ", "Title": "IS-H: Discharge Reason Mandatory Field Saved Without Value", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "603", "Number": "1137705 ", "URL": "/notes/1137705 ", "Title": "IS-H: Enhancement of BAdI ISH_WP_VW123_COLUMN", "Component": "IS-H-PM-CUM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "603", "Number": "1141869 ", "URL": "/notes/1141869 ", "Title": "Public List - Discharge Date for Non-Discharged Case", "Component": "IS-H-PM-PAT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1106037 ", "URL": "/notes/1106037 ", "Title": "Clinical Work Station: Outpatient Clinic/Service Master View Type: Documents", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1138058 ", "URL": "/notes/1138058 ", "Title": "IS-H AT: Error Message Geographical Area Can Be Set", "Component": "XX-CSC-AT-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1158200&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1158200/D\" target=\"_blank\">/notes/1158200/D</a>."}}}}