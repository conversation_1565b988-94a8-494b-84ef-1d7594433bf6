SAP Note 1393114 provides an overview of the new features and enhancements introduced in SAP GUI for Windows 7.20 compared to its predecessor version 7.10. These improvements include:

1. Support for the Windows 7 operating system.
2. Compatibility with the 32-bit version of the Office 2010 package.
3. Built with Visual Studio 2008, allowing for extended support until April 2013.
4. Hierarchical organization of items in SAP Logon.
5. Central storage of SAP Logon .ini files.
6. A redesigned and user-friendly Options Dialog incorporating a search function and combining elements from Tweak SAP GUI and the previous options dialog.
7. Activation of Internationalization (I18N) mode by default for global compatibility.
8. Default use of SAP Signature Design, retaining the availability of alternative designs.
9. Introduction of a high contrast mode in SAP Signature Design to accommodate visually impaired users.
10. Security enhancements, including rules to prevent unwanted access to client PCs from the SAP system, along with a corresponding maintenance dialog.
11. Implementation of a non-transparent frame for SAP Signature Design to boost performance on Windows Terminal Server (WTS) machines.
12. Enhanced focus visualization with an Animated Focus Frame.
13. Better SAP GUI Scripting Access integration with NetWeaver Business Client (NWBC).
14. Reorganization of the SAPWorkDir according to Windows standards, with dedicated subfolders for specific items.
15. New frontend screenshot functionality allowing the transfer of current SAP GUI state screenshots to the server.
16. Implementation of standard keyboard shortcuts for select all (CTRL+A), undo (CTRL+Z), and redo (CTRL+Y) in input fields.
17. SNC (Secure Network Communication) logon without Single Sign-On (SSO), with details available in SAP Note 1580808.

This SAP Note is useful for users and administrators looking to upgrade to SAP GUI for Windows 7.20, as it outlines the feature enhancements and potential benefits of the upgrade.