<h3>SI15: CT_Integration</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2259818">2259818 - RFC enabled Function Modules with incompatible signature change compared to its version in ERP are blocked from external access.</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are using SAP S/4HANA. Certain RFC enabled Function Modules have been blocked from external access using RFC Unified Connectivity Filter Services.</p>
<p>Many RFC Function Modules that were delivered with ERP, are now available in SAP S/4HANA delivering the same functionality and services. In SAP S/4HANA, some RFC enabled Function Modules have had their signature modified in an incompatible way, compared to their version in ERP. Using these Function Modules across systems may harm the data integrity of your SAP S/4HANA System, since the caller might still use the old signature. Additionally, some RFC enabled Function Modules have been marked as "Obsolete" and hence they are also blocked from external access.</p>
<p><strong>Business Process Impact:</strong></p>
<p>This change impacts any business process that relies on external RFC calls to Function Modules whose signatures have changed in SAP S/4HANA. Such incompatibility can lead to data corruption or integrity issues. Business processes relying on obsolete Function Modules will need to adapt to new modules or completely overhaul the integration points to ensure smooth operation.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Adjust any interfaces that use obsolete RFC. Review and ensure that all Function Modules being called externally are adapted to the changed signatures. Verify blocked Function Modules by remotely calling them and receiving a short dump, as described in the note. If necessary, follow the instructions in note 2408693 to override the blocklist for specific Function Modules, but proceed with caution and consult with SAP Development Support.</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2408693">2408693 - Override blocklist of Remote Enabled Function Modules</a>, <a href="https://me.sap.com/notes/2249880">2249880 - Dump SYSTEM_ABAP_ACCESS_DENIED caused through Blocklist Monitor in SAP S/4HANA on premise</a>, <a href="https://me.sap.com/notes/2234168">2234168 - Blocklist Monitor not started or has to be restarted - message P013(TG)</a>, <a href="https://me.sap.com/notes/2933993">2933993 - Enable Report ABLM_MODIFY_ITEMS to reset local modificaitons</a>, <a href="https://me.sap.com/notes/2753393">2753393 - Erroneous entry on S4 blacklist</a>, <a href="https://me.sap.com/notes/2476734">2476734 - Runtime error SYSTEM_ABAP_ACCESS_DENIED</a></p>