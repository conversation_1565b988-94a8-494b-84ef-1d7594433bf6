{"Request": {"Number": "1166480", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 607, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007036562017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001166480?language=E&token=DF05C1B84F7C0D588D552CC973AC2CE4"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001166480", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001166480/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1166480"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with High Priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.05.2008"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-AT-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "XX-CSC-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-AT-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-AT-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1166480 - IS-H: Patient Routing System Advanced Search"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1166480&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1166480/D\" target=\"_blank\">/notes/1166480/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the case of major accidents, the patients are identified using the<br />so-called patient routing system (PLS). At the accident location, everyone receives<br />Patient an orange bag with an identification number (PLS number)<br />reassigned.<br />In the advanced search of the case overview, you can search for the<br />PLS number by selecting the PLS Cases list and entering the PLS number. The output is based on the definition of the list type PLS Outpatient and Inpatient.<br />The enhanced search is based on the IS-H Movement List report (RNLPFB00), which has also been enhanced with the PLS number.<br />For more information, see the report documentation.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Patient Routing System PLS Number NFAL PLSNR Advanced Search Case Overview NP10</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>See symptom</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><OL>1. Before you implement the source code corrections, implement the attached attachment as follows:</OL> <OL><OL>a) Unpack the attached files:</OL></OL> <p>                       HW1166480_603.zip and HW1166480_603_Cust.zip for IS-H Version 6.03 <p>                       HW1166480_600.zip and HW1166480_600_Cust.zip for IS-H Version 6.00 <p>                       HW1166480_472.zip and HW1166480_472_Cust.zip for IS-H Version 4.72 <p>                       Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL><OL>b) Import the unpacked requests into your system.</OL></OL> <OL>2. Now perform the following manual activities:</OL> <UL><LI>Call transaction SE80.</LI></UL> <UL><LI>In the input help of the upper of the two input fields, choose &quot;Program&quot;. Enter the program name RNLPFB00 in the lower input field. Confirm your entry.</LI></UL> <UL><LI>Double-click the node RNLPFB00.</LI></UL> <UL><LI>In the menu bar, choose &quot;Goto -> Text Elements -> Text Symbols&quot;.</LI></UL> <UL><LI>Click on &quot;Change&quot; button in the application toolbar.</LI></UL> <UL><LI>Position the cursor on the text element A01 and choose &quot;Insert Row&quot;.</LI></UL> <UL><LI>Enter the following values:</LI></UL> <UL><UL><LI>Sym: 099</LI></UL></UL> <UL><UL><LI>Text: PLS Outpatient and Inpatient</LI></UL></UL> <UL><UL><LI>mLen: 25</LI></UL></UL> <UL><LI>Save the change as active.</LI></UL> <OL>3. Now implement the source code corrections from this SAP Note.</OL> <OL>4. Now perform the following manual post-implementation steps:</OL> <UL><LI>Call transaction SE80.</LI></UL> <UL><LI>In the input help of the upper of the two input fields, choose &quot;Program&quot;. Enter the program name RNLPFB00 in the lower input field. Confirm your entry.</LI></UL> <UL><LI>Double-click the node RNLPFB00.</LI></UL> <UL><LI>In the menu bar, choose &quot;Goto -> Text Elements -> Selection Texts&quot;.</LI></UL> <UL><LI>Click on &quot;Change&quot; button in the application toolbar.</LI></UL> <UL><LI>Enter the following value for the field PLSNR:</LI></UL> <UL><UL><LI>PLS Number</LI></UL></UL> <UL><LI>Save the change as active.</LI></UL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON> (C5021076)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON> (C5021076)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001166480/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1166480_600.zip", "FileSize": "25", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000193932008&iv_version=0002&iv_guid=392C430050D1264C87DC71F232D0F00E"}, {"FileName": "HW1166480_600_Cust.zip", "FileSize": "75", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000193932008&iv_version=0002&iv_guid=81B0C21DF35B4246B9929B4298A07186"}, {"FileName": "HW1166480_603.zip", "FileSize": "25", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000193932008&iv_version=0002&iv_guid=B3533084108B0147B5A5C155481BCDE7"}, {"FileName": "HW1166480_603_Cust.zip", "FileSize": "75", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000193932008&iv_version=0002&iv_guid=74218D4FAD69A841869A39E3A40A0A39"}, {"FileName": "HW1166480_472.zip", "FileSize": "24", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000193932008&iv_version=0002&iv_guid=4F9DCD465F2A2B4292723B0F9034F98A"}, {"FileName": "HW1166480_472_Cust.zip", "FileSize": "2", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000193932008&iv_version=0002&iv_guid=9E052F6BB36C6447A746CB11171FBCB2"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1309412", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: GINA - Enhancements", "RefUrl": "/notes/1309412"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1309412", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: GINA - Enhancements", "RefUrl": "/notes/1309412 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF28", "URL": "/supportpackage/SAPKIPHF28"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60015INISH", "URL": "/supportpackage/SAPK-60015INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60204INISH", "URL": "/supportpackage/SAPK-60204INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60303INISH", "URL": "/supportpackage/SAPK-60303INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 3, "URL": "/corrins/0001166480/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "821738 ", "URL": "/notes/821738 ", "Title": "IS-H CH: RNLPFB00 - Movement Lists - Patient Class", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "922863 ", "URL": "/notes/922863 ", "Title": "IS-H: RNLPFB00: New Checkbox \\&quot;Number of Outpatient Clinic Days\\&quot;", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1158200 ", "URL": "/notes/1158200 ", "Title": "IS-H: Patient Routing System", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "600", "Number": "1060700 ", "URL": "/notes/1060700 ", "Title": "IS-H: RNLPFB00 F4 help incorrect for case type multiple sel.", "Component": "IS-H-IS-STA"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "922863 ", "URL": "/notes/922863 ", "Title": "IS-H: RNLPFB00: New Checkbox \\&quot;Number of Outpatient Clinic Days\\&quot;", "Component": "IS-H-PM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1158200 ", "URL": "/notes/1158200 ", "Title": "IS-H: Patient Routing System", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "603", "ValidTo": "603", "Number": "1158200 ", "URL": "/notes/1158200 ", "Title": "IS-H: Patient Routing System", "Component": "XX-CSC-AT-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1166480&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1166480/D\" target=\"_blank\">/notes/1166480/D</a>."}}}}