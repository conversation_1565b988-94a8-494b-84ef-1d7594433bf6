{"Request": {"Number": "2575521", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 439, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000020380682017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002575521?language=E&token=A408FB16654BA687509315750CAE35C5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002575521", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2575521"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.01.2018"}, "SAPComponentKey": {"_label": "Component", "value": "CA-EPT-BRC-FBI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Floorplan Manager BOPF Integration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Processes and Tools for Enterprise Applications", "value": "CA-EPT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-EPT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Reusables around BOPF In Business Suite Foundation", "value": "CA-EPT-BRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-EPT-BRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Floorplan Manager BOPF Integration", "value": "CA-EPT-BRC-FBI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-EPT-BRC-FBI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2575521 - Expand All / Collapse All not working"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<div class=\"WordSection1\">\r\n<p>In a tree UIBB there are two issues:</p>\r\n<p>1. The user selects a line&#160;and checks a checkbox in another line.</p>\r\n<p>The selected line is wrongly processed.</p>\r\n<p>2. WebDynpro event parameters are not pased to BOBF action class.</p>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<div class=\"WordSection1\">\r\n<p>/BOFU/CL_FBI_GUIBB_BASE, IV_EVENT_INDEX, PROCESS_EVENT, /BOFU/CL_FBI_GUIBB_TREE</p>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<div class=\"WordSection1\">\r\n<p>Processing a correct selected line was handled only for a list UIBB.</p>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<div class=\"WordSection1\">\r\n<p>Implement the attached correction instructions or apply the corresponding support package.</p>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "Uttam Y P (I320744)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I330334)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002575521/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575521/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575521/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575521/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575521/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575521/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575521/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575521/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575521/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2622551", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Collective Note SP08: Basic Functions - SAP Solution Manager 7.2 SPS 08", "RefUrl": "/notes/2622551 "}, {"RefNumber": "2575565", "RefComponent": "SV-SMG-TWB", "RefTitle": "Collapse All/ Expand All not working on immediate test execution UI", "RefUrl": "/notes/2575565 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "SAP_BS_FND", "From": "702", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BS_FND", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BS_FND", "From": "746", "To": "746", "Subsequent": ""}, {"SoftwareComponent": "SAP_BS_FND", "From": "747", "To": "747", "Subsequent": ""}, {"SoftwareComponent": "SAP_BS_FND", "From": "748", "To": "748", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10006INS4CORE", "URL": "/supportpackage/SAPK-10006INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10104INS4CORE", "URL": "/supportpackage/SAPK-10104INS4CORE"}, {"SoftwareComponentVersion": "SAP_BS_FND 731", "SupportPackage": "SAPK-73122INSAPBSFND", "URL": "/supportpackage/SAPK-73122INSAPBSFND"}, {"SoftwareComponentVersion": "SAP_BS_FND 746", "SupportPackage": "SAPK-74613INSAPBSFND", "URL": "/supportpackage/SAPK-74613INSAPBSFND"}, {"SoftwareComponentVersion": "SAP_BS_FND 747", "SupportPackage": "SAPK-74717INSAPBSFND", "URL": "/supportpackage/SAPK-74717INSAPBSFND"}, {"SoftwareComponentVersion": "SAP_BS_FND 748", "SupportPackage": "SAPK-74811INSAPBSFND", "URL": "/supportpackage/SAPK-74811INSAPBSFND"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BS_FND", "NumberOfCorrin": 3, "URL": "/corrins/0002575521/6134"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 2, "URL": "/corrins/0002575521/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 11, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2235403 ", "URL": "/notes/2235403 ", "Title": "Application specific feeder class based on FBI,  cannot check  BOBF failed event indicator", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2360751 ", "URL": "/notes/2360751 ", "Title": "Multiple selected lines in FPM list UI ignored.", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2392266 ", "URL": "/notes/2392266 ", "Title": "IO_UI_INFO Parameter to get UI-related information from ATS List UIBB is added to /BOFU/IF_FBI_VIEW_EXITINTF_RUN-ADAPT_EVENT", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2360751 ", "URL": "/notes/2360751 ", "Title": "Multiple selected lines in FPM list UI ignored.", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2392266 ", "URL": "/notes/2392266 ", "Title": "IO_UI_INFO Parameter to get UI-related information from ATS List UIBB is added to /BOFU/IF_FBI_VIEW_EXITINTF_RUN-ADAPT_EVENT", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "731", "ValidTo": "731", "Number": "2023907 ", "URL": "/notes/2023907 ", "Title": "List - UI action on an attribute ignores its value", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "731", "ValidTo": "731", "Number": "2025086 ", "URL": "/notes/2025086 ", "Title": "Dump after implementing note number 2023907", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "731", "ValidTo": "731", "Number": "2053909 ", "URL": "/notes/2053909 ", "Title": "Call FBI view exit class for FPM GAF navigation events", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "731", "ValidTo": "731", "Number": "2112874 ", "URL": "/notes/2112874 ", "Title": "Tree: wrong selected line, webdynpro parameters not passed to BOBF action class", "Component": "CA-EPT-BRC"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "731", "ValidTo": "731", "Number": "2113398 ", "URL": "/notes/2113398 ", "Title": "Decouple the event result (EV_RESULT) in base feeder PROCESS_EVENT from action failed keys", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "731", "ValidTo": "731", "Number": "2129116 ", "URL": "/notes/2129116 ", "Title": "Enable the ADAPT_EVENT of FBI view exit class to return the event result in case the result is DEFER", "Component": "CA-EPT-BRC"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "731", "ValidTo": "731", "Number": "2131860 ", "URL": "/notes/2131860 ", "Title": "Multiple selection does not work in list", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "731", "ValidTo": "731", "Number": "2235403 ", "URL": "/notes/2235403 ", "Title": "Application specific feeder class based on FBI,  cannot check  BOBF failed event indicator", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "731", "ValidTo": "731", "Number": "2360751 ", "URL": "/notes/2360751 ", "Title": "Multiple selected lines in FPM list UI ignored.", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "731", "ValidTo": "731", "Number": "2392266 ", "URL": "/notes/2392266 ", "Title": "IO_UI_INFO Parameter to get UI-related information from ATS List UIBB is added to /BOFU/IF_FBI_VIEW_EXITINTF_RUN-ADAPT_EVENT", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "1856972 ", "URL": "/notes/1856972 ", "Title": "Asset are not editable after enter", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "2023907 ", "URL": "/notes/2023907 ", "Title": "List - UI action on an attribute ignores its value", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "2025086 ", "URL": "/notes/2025086 ", "Title": "Dump after implementing note number 2023907", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "2053909 ", "URL": "/notes/2053909 ", "Title": "Call FBI view exit class for FPM GAF navigation events", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "2112874 ", "URL": "/notes/2112874 ", "Title": "Tree: wrong selected line, webdynpro parameters not passed to BOBF action class", "Component": "CA-EPT-BRC"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "2113398 ", "URL": "/notes/2113398 ", "Title": "Decouple the event result (EV_RESULT) in base feeder PROCESS_EVENT from action failed keys", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "2129116 ", "URL": "/notes/2129116 ", "Title": "Enable the ADAPT_EVENT of FBI view exit class to return the event result in case the result is DEFER", "Component": "CA-EPT-BRC"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "2131860 ", "URL": "/notes/2131860 ", "Title": "Multiple selection does not work in list", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "2235403 ", "URL": "/notes/2235403 ", "Title": "Application specific feeder class based on FBI,  cannot check  BOBF failed event indicator", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "2360751 ", "URL": "/notes/2360751 ", "Title": "Multiple selected lines in FPM list UI ignored.", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "747", "ValidTo": "747", "Number": "2392266 ", "URL": "/notes/2392266 ", "Title": "IO_UI_INFO Parameter to get UI-related information from ATS List UIBB is added to /BOFU/IF_FBI_VIEW_EXITINTF_RUN-ADAPT_EVENT", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "748", "ValidTo": "748", "Number": "2235403 ", "URL": "/notes/2235403 ", "Title": "Application specific feeder class based on FBI,  cannot check  BOBF failed event indicator", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "748", "ValidTo": "748", "Number": "2360751 ", "URL": "/notes/2360751 ", "Title": "Multiple selected lines in FPM list UI ignored.", "Component": "CA-EPT-BRC-FBI"}, {"SoftwareComponent": "SAP_BS_FND", "ValidFrom": "748", "ValidTo": "748", "Number": "2392266 ", "URL": "/notes/2392266 ", "Title": "IO_UI_INFO Parameter to get UI-related information from ATS List UIBB is added to /BOFU/IF_FBI_VIEW_EXITINTF_RUN-ADAPT_EVENT", "Component": "CA-EPT-BRC-FBI"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}