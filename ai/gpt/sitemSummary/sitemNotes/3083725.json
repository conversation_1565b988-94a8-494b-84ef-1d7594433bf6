{"Request": {"Number": "3083725", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 448, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001831102021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003083725?language=E&token=3B4FF2EFECC6812981650346946D8539"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003083725", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003083725/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3083725"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "TM-CF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Cross Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Transportation Management (no software transports)", "value": "TM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cross Functions", "value": "TM-CF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TM-CF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3083725 - SAP S/4HANA 2021 Supply Chain for Transportation Management - Release information"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note contains release information when using S/4HANA Supply Chain for Transportation Management on-premise edition&#160;2021 (TM in S/4HANA 2021, also referred to as S/4HANA TM).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S/4HANA TM 2021, TM in SAP S/4HANA 2021</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Release information for&#160;SAP S/4HANA&#160;2021 Supply Chain for Transportation Management</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Refer to the Feature Scope Description of S/4HANA&#160;2021 for an overview of the features contained in each version.</p>\r\n<p>1) S/4HANA Transportation Management integrates into the embedded applications in S/4HANA and supports also&#160;side-by-side scenarios, means it can receive and process transportation requirements from other ERP or S/4HANA instances (in addition to the processing of system internal transportation demand)</p>\r\n<ul>\r\n<li>Following source systems are supported for side-by-side scenarios with S/4HANA TM&#160;2021 as target system:</li>\r\n<ul>\r\n<li>S/4HANA 1709&#160;FPS02 or higher (1809, 1909, ...)</li>\r\n<li>SAP ERP 6.0 EhP 7 and EhP 8</li>\r\n<ul>\r\n<li>For freight settlement integration SAP Note&#160;<a target=\"_blank\" href=\"/notes/2640069\">2640069</a>&#160;(ERP 6.0) has to be implemented and in case of an upgrade from S/4HANA 1511/1610 to S/4HANA 1909, SAP Note&#160;<a target=\"_blank\" href=\"/notes/2465978\">2465978</a>&#160;has to be taken into account</li>\r\n<li>For replication of location master data (shipping point, plant) SAP Note&#160;<a target=\"_blank\" href=\"/notes/2638834\">2638834</a>&#160;(ERP 6.0) has to be implemented</li>\r\n<li>For order and delivery integration SAP Note&#160;<a target=\"_blank\" href=\"/notes/2634924\">2634924</a>&#160;(ERP 6.0) has to be implemented</li>\r\n<ul>\r\n<li>Web Services Reliable Messaging (WS-RM) for direct point-to-point integration is available with SP17 for SAP ERP 6.0 EhP7&#160;and&#160;SP11 for&#160;SAP ERP 6.0 EhP8 and cannot be delivered via SAP Note</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<li><a target=\"_blank\" href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900001825982021&amp;iv_guid=00109B36D5C21EDBBD8B9CDE0CF100E8&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F4933B334368BF734378A3730B4F273353275754F312E2970B1CCF2AD0A303633AA482FCBAF320E4ACFB034CBF04B4C49890F4D8D37D3750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\">Master Data integration</a>&#160;is done with Data Replication Framework (DRF) and in addition SAP Master Data Governance can be used</li>\r\n<ul>\r\n<li>Customer/Vendor Integration (CVI) has to be activated in SAP ERP 6.0 as Business Partner is used for integration with S/4HANA TM</li>\r\n<ul>\r\n<li>The strategic object model in SAP S/4HANA is the Business Partner (BP). Business Partner is capable of centrally managing master data for Business Partners, Customers, and Vendors.&#160;To use the SAP Business Partner as leading object in SAP S/4HANA, the Customer/Vendor Integration (CVI) must be used. The CVI component ensures the synchronization between the Business Partner object and the Customer/Vendor objects.&#160;CVI is an automated procedure supported by the Master Data Synchronization Cockpit tool. It is used to synchronize Customer Master and Vendor Master objects with SAP Business Partner objects. CVI assigns every Customer and Vendor master data object to a newly created SAP Business Partner object and vice versa.</li>\r\n<li>Direct transfer of customer/vendor from ERP to S/4HANA TM without activated CVI is not supported</li>\r\n<ul>\r\n<li>Activation of CVI is also a prerequisite step for the conversion of ERP to S/4HANA (see also&#160;<a target=\"_blank\" href=\"https://support.sap.com/content/dam/SAAP/SAP_Activate/S4H.0781%20SAP%20S4HANA%20Cookbook%20Customer%20Vendor%20Integration.pdf\">SAP S/4HANA Cookbook \"Customer/Vendor Integration\"</a>) and compatibility to continue running S/4HANA with S/4HANA TM side by side after ERP conversion has to be ensured</li>\r\n<li>Data quality for correct creation of Business Partner data via CVI has to be ensured in the source system and not after the transfer of the data to the target system</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<li>Integration from S/4HANA TM Freight Order to LE-TRA Shipment and vice versa is not supported (embedded in S/4HANA and side-by-side with ERP and S/4HANA)</li>\r\n<li>Integration of&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/f132c385e0234fe68ae9ff35b2da178c/LATEST/en-US/a004ec57a7b5bc12e10000000a4450e5.html\">Advanced Available-to-Promise (aATP)</a>&#160;with&#160;S/4HANA TM is not supported&#160;for Alternative based Confirmation(see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2982461\">2982461</a>).</li>\r\n<li>For the usage of a&#160;decentralized S/4HANA Extended Warehouse Management (EWM) in the same S/4HANA client as&#160;S/4HANA TM please see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2812981\">2812981</a>.</li>\r\n</ul>\r\n<p>2) Following restrictions do apply for the&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/e3dc5400c1cc41d1bc0ae0e7fd9aa5a2/LATEST/en-US/65c4edc97c5c45048192fb079cfd87f0.html\">Advanced Shipping and Receiving</a>&#160;process (see corresponding chapter in <a target=\"_blank\" href=\"https://help.sap.com/viewer/e3dc5400c1cc41d1bc0ae0e7fd9aa5a2/LATEST/en-US/3dbac2009df0459da845e03e6802817f.html\">SAP Help Portal</a>&#160;of Transportation Management for more information):</p>\r\n<ul>\r\n<li>General (Inbound and Outbound process):</li>\r\n<ul>\r\n<li>The Adv. SR is functionality is only supported for system internal scenarios (means all involved applications like SD, MM, LE, <a target=\"_blank\" href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/9832125c23154a179bfa1784cdc9577a/c34f79e13e0c4de7ae72d8741cb25384.html?locale=en-US\">EWM</a> need to be part of the same system instance and client).</li>\r\n<li>The focus of the current supported&#160;Adv. SR process is road with trucks, so the shipping and receiving activities are currently only supported for the active vehicle resource of a road freight order.</li>\r\n<li>The communication of the (Un-)Loading status for packages in the direct TM-EWM integration is done on delivery header level (and not on HU level).</li>\r\n<li>Cross delivery HUs are not supported.</li>\r\n<li>Handling of packages which are not part of the TM Freight Unit are not transferred to LE and/or EWM. Futhermore such local packages outside of the TM Freight Unit prevent subsequent update propagation from EWM to TM during warehouse execution until they are deleted manually.</li>\r\n<li>Multi-Pick/Multi-Drop Freight Orders with Adv. SR/non-Adv. SR Items in the same Freight Order are not yet supported, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/3158252\">3158252</a> for the release information of EWM TU based integration on Freight Order stopp level based on the A2A service replication mechanism which is mentioned in point 3) below.</li>\r\n<li>Initial creation of deliveries in EWM are not yet supported (as no Freight Units are created in such a case). The same applies when you try to increase the delivery quantity after it has been set to zero.&#160; &#160;</li>\r\n<li>Changes of volume or weight in the EWM warehouse request item won&#8217;t be communicated to TM when it is unpacked. Only for HUs/packages the information is part of the interface to TM and will also be passed to TM as defined for the individual process steps. This also affects the catch weight process which is therefore not supported.</li>\r\n<li>Complex unloading and loading in EWM based on warehouse tasks is not yet released.</li>\r\n<li>RF Loading and Unloading by internal FO Number and internal CO number in EWM is not yet possible.</li>\r\n<li>The shortcut planning process without Freight Unit within the TM document chain, where Freight Unit building results directly into other document types like Freight Order, is not yet supported.</li>\r\n<li>Multiple Freight Units for one EWM delivery item are supported starting with Feature Package Stack 02 (FPS02). Moreover, updates from EWM with a package across Freight Units for one EWM delivery are also supported starting with FPS02. Therefore it is recommended to configure the Freight Unit Building Rule in such a way that only one Freight Unit per EWM delivery is created if FPS02 is not yet implemented in the system.</li>\r\n<li>Stock Transport Orders are not yet supported.</li>\r\n<li>Stock Transport Scheduling Agreements &#160;are not yet supported.</li>\r\n<li>The returns process for Stock Transport Order and Stock Transport Scheduling Agreements is&#160;not yet supported.</li>\r\n<li>Integration with SAP GTS (Compliance Check, Customs Declaration) is&#160;supported starting with Feature Package Stack 01 (FPS01) except for the following processes:</li>\r\n<ul>\r\n<li>Outbound Delivery without Order Reference</li>\r\n<li>Supplier Returns Delivery</li>\r\n<li>Subcontracting Purchase Order</li>\r\n<li>Customer Returns</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 120px;\"><span style=\"font-size: 14px;\">Note: Compliance check and customs declaration for LE and EWM delivery are no longer supported in the Adv. SR process. Instead, the compliance check is performed on the basis of freight units/freight orders and export declaration is done based on consignment or freight order.</span></p>\r\n<ul>\r\n<li>Inbound process:</li>\r\n<ul>\r\n<li>SD Customer Returns Order and corresponding returns process&#160;is not supported (Supported with release 2022).</li>\r\n<li>Quantity increases out of LE to TM&#160;is&#160;supported starting with S/4HANA 2021 Feature Package Stack 02 (FPS02).</li>\r\n<li>Quantity changes after Goods Receipt and during final Put-Away in EWM is not supported.</li>\r\n<li>In the phase before &#8220;Ready for Warehouse Processing&#8221; status the non-cargo related data in EWM (e.g. license plate of truck) might be not up to date with current TM data.</li>\r\n<li>In certain cases (e.g. packaging update from TM to LE and EWM) the message &#8220;Changed in parallel session&#8220; might occur in the next process step after save in TM (due to parallel locking and updates of TM documents).&#160;For further information about endless transaction mode and optimistic locks in TM single document screens see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2240387\">2240387</a>.</li>\r\n<li>Inbound processing for Subcontracting Purchase Orders is not yet supported.</li>\r\n</ul>\r\n<li>Outbound process</li>\r\n<ul>\r\n<li>MM&#160;Returns Purchase Order&#160;and corresponding returns process is not supported.</li>\r\n<li>Only execution driven process is supported with the direct TM-EWM integration. As a result, packages are always created out of pick/pack activities in warehouse (no Packaging Proposal from TM).</li>\r\n</ul>\r\n</ul>\r\n<p>3) Features which are not using single-system integration qualities:</p>\r\n<ul>\r\n<li>S/4HANA EWM to S/4HANA TM system internal integration based on EWM TU and replication of TM Freight Order is done via A2A services in the same way as cross system communication (for direct TM-EWM integration based on Freight Order w/o EWM TU see point 2) above and Adv. S&amp;R functionality).</li>\r\n<ul>\r\n<li>The general scope description of TM EWM TU-based integration applies, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/3018355\">3018355</a>.</li>\r\n</ul>\r\n</ul>\r\n<p>4)&#160;Subcontracting for a <a target=\"_blank\" href=\"https://help.sap.com/viewer/e3dc5400c1cc41d1bc0ae0e7fd9aa5a2/LATEST/en-US/908e8ab6945a47edb1ade56ec6533b2f.html\">Consignment Order</a>&#160;is&#160;not yet supported.</p>\r\n<p>5) Incoterms 2010&#160;is&#160;not yet supported.</p>\r\n<p>6) S/4HANA&#160;<a target=\"_blank\" href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/35751be3d6ee423197492574e016a512/679a1d3744d34b43832da6890b9095c0.html?locale=en-US\">Product Compliance</a>&#160;functionality is not compatibile with the usage of the TM Dangerous Goods functionality based on PS&amp;S in the same client, means only one of the both options can be activated:</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/e3dc5400c1cc41d1bc0ae0e7fd9aa5a2/6553f7f9797e45a68417c58ad82f2024.html?locale=en-US\">Dangerous Goods Processing Based on PS&amp;S</a></li>\r\n<li><a target=\"_blank\" href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/e3dc5400c1cc41d1bc0ae0e7fd9aa5a2/a9b6c6f85377409cb93e2613bef07caa.html?locale=en-US\">Dangerous Goods Processing Based on Product Compliance</a></li>\r\n</ul>\r\n<p>7)&#160;Multiple Address Handling for Business Partners in Sales and Distribution and Multiple Address Handling for Customer Master Data in Business Partner&#160;is supported according to the description of SAP Note&#160;<a target=\"_blank\" href=\"/notes/3166861\">3166861</a> (delivered as part of FPS02).</p>\r\n<ul>\r\n<li>Business Partner function&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/74b0b157c81944ffaac6ebc07245b9dc/LATEST/en-US/6f33f1778b1e45f4bad6e8bce4be4125.html\">Multiple&#160;Assignment</a>&#160;is not supported.</li>\r\n</ul>\r\n<p>8) The TM Collaboration Portal is available in S/4HANA with restrictions which are described in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2858217\">2858217</a>.</p>\r\n<p>The following features which are supported through the TM Collaboration Portal are also available via means of EDI or mail communication:</p>\r\n<ul>\r\n<li>1:1 Tendering: RFQ to Service Agent</li>\r\n<li>Peer to Peer Tendering; Broadcast Tendering, Subcontract List</li>\r\n<li>Interface Tendering of Freight Order to Business Partner (for example&#160;Descartes)</li>\r\n<li>Transportation Tendering through freight exchange</li>\r\n<li>Rate change request from Carrier (Freight Agreements)</li>\r\n<li>View Freight Agreements</li>\r\n<li>Respond to Request for Quotation (Strategic Freight Procurement)</li>\r\n<li>Award Confirmation from Carrier (FSP)</li>\r\n<li>Carrier Invoice submission (B2B) and Dispute Management on the carrier invoice.</li>\r\n</ul>\r\n<p>Following features which are supported through the TM Collaboration Portal are not available via other means:</p>\r\n<ul>\r\n<li>Self-Billing Freight Order&#160;</li>\r\n</ul>\r\n<p>9) Compared to SAP TM 9.6, the following functions are discontinued (&#8220;Simplification&#8221;) and are not available:</p>\r\n<ul>\r\n<li>Integration into Customer Relationship Management</li>\r\n<li>Integration of TM Resources to Enterprise Asset Management Objects (Equipment, Functional Location, Downtimes based on Maintenance Orders)</li>\r\n<li>Prepayment of Forwarding Orders</li>\r\n<li>Selected Planning Functionality</li>\r\n<ul>\r\n<li>Condition-Based loading and unloading duration determination</li>\r\n<li>Fixed Loading and unloading duration determination per requirement document</li>\r\n<li>Condition-Based filtering for Transportation Cockpit (via Profile)</li>\r\n<li>Take away Load Cost Function from UI (Usage is possible via BAdI)</li>\r\n<li>Option for Time-Related Selection of Documents based on All Stop Times</li>\r\n<li>Transportation Proposal for multiple Freight Units at once</li>\r\n</ul>\r\n<li>TransportationOrderGenericRequest_In: Item and Stage IDs must follow internal number ranges (SAP Note <a target=\"_blank\" href=\"/notes/3192202\">3192202</a>)</li>\r\n</ul>\r\n<p>10) Compared to SAP TM 9.6, the following functions are not yet released:</p>\r\n<ul>\r\n<li>Internal Settlement for Resources (other&#160;Internal Settlement Management scenarios are released)</li>\r\n<li>Group Logistics is only supported for side-by-side scenarios, but not yet for system internal processing</li>\r\n<li>Creation of Freight Units out of Scheduling Agreements (outbound)&#160;is only supported for side-by-side scenarios, but not yet for system internal processing</li>\r\n<li>Sales Order Scheduling</li>\r\n<li>Business Partner hierarchy&#160;(see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2409939\">2409939</a>)</li>\r\n</ul>\r\n<p>11) Supported integration with other SAP application instances</p>\r\n<p>An overview is shown in attached document <a target=\"_blank\" href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900001825982021&amp;iv_guid=00109B36D5C21EDBBD8B9D8A9BD3A0E8&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F4933B334368BF734378A3730B4F273353275754F312E2970B1CCF2AD0A303633AA482FCBAF320E4ACFB034CBF04B4C49890F4D8D37D3750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\">Supported Integration for S4HANA TM.pdf</a></p>\r\n<p>Following applications must be installed separately in the case they are required in connection with S/4HANA TM:</p>\r\n<ul>\r\n<li>GTS&#160;(see SAP Note&#160;3296252)</li>\r\n<li>BI</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D031522)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D031522)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003083725/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003083725/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003083725/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003083725/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003083725/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003083725/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003083725/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003083725/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003083725/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "S4HANA_TM_Master_Data_Side_by_Side_Integration.pdf", "FileSize": "40", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900001825982021&iv_version=0022&iv_guid=00109B36D5C21EDBBD8B9CDE0CF100E8"}, {"FileName": "Supported Integration for S4HANA TM.pdf", "FileSize": "40", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900001825982021&iv_version=0022&iv_guid=00109B36D5C21EDBBD8B9D8A9BD3A0E8"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2963724", "RefComponent": "TM-BF-BG", "RefTitle": "Mandatory corrections for trigger and strategy processing (bgRFC)", "RefUrl": "/notes/2963724"}, {"RefNumber": "2641251", "RefComponent": "TM-BF-BG", "RefTitle": "bgRFC concept in TM 9.5 and S/4HANA TM", "RefUrl": "/notes/2641251"}, {"RefNumber": "3296252", "RefComponent": "SLL-LEG-FUN", "RefTitle": "FAQ for conversion to SAP Global Trade Services, edition for SAP HANA", "RefUrl": "/notes/3296252"}, {"RefNumber": "3221699", "RefComponent": "BC-ESI-BOF", "RefTitle": "ALV Trace is active without explicit enabling", "RefUrl": "/notes/3221699"}, {"RefNumber": "3192202", "RefComponent": "TM-BF-INT", "RefTitle": "Checks for Item ID and Stage ID in TransportationGenericRequest_In", "RefUrl": "/notes/3192202"}, {"RefNumber": "3166861", "RefComponent": "TM-MD-BP", "RefTitle": "BP Adoption - Enabling BP Multiple Address - Transportation Management", "RefUrl": "/notes/3166861"}, {"RefNumber": "3158252", "RefComponent": "TM-INT-EWM", "RefTitle": "Supported Functions and Restrictions on TU-Based TM-EWM Integration MultiPick/MultiDrop Feature", "RefUrl": "/notes/3158252"}, {"RefNumber": "3107298", "RefComponent": "TM-BF-INT", "RefTitle": "Enable Web Service communication w/o SLD", "RefUrl": "/notes/3107298"}, {"RefNumber": "3081750", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA: Restriction Note for \"Multiple Address Handling in SD Documents Using SAP Business Partner\"", "RefUrl": "/notes/3081750"}, {"RefNumber": "3080846", "RefComponent": "SCM-EWM-UPG", "RefTitle": "SAP S/4HANA 2021: Release information and restrictions for EWM in SAP S/4HANA", "RefUrl": "/notes/3080846"}, {"RefNumber": "3079550", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2021: Restriction Note", "RefUrl": "/notes/3079550"}, {"RefNumber": "3062697", "RefComponent": "TM-INT-LI", "RefTitle": "Handling of One Time Locations in MM Integration", "RefUrl": "/notes/3062697"}, {"RefNumber": "3018355", "RefComponent": "TM-INT-EWM", "RefTitle": "Supported functional scope and restrictions of the TU-based TM EWM Integration for shippers", "RefUrl": "/notes/3018355"}, {"RefNumber": "3015309", "RefComponent": "TM-INT-LI-CS-DLV", "RefTitle": "Adv. SR Inbound - Optimize delivery back updates and locking - Part 1", "RefUrl": "/notes/3015309"}, {"RefNumber": "2982461", "RefComponent": "XX-SER-REL", "RefTitle": "Restriction Note for advanced Available-to-Promise (aATP) in SAP S/4HANA 2021", "RefUrl": "/notes/2982461"}, {"RefNumber": "2933925", "RefComponent": "TM-INT-EWM", "RefTitle": "Setup embedded TM- embedded EWM Integration via soamanager (WSRM)", "RefUrl": "/notes/2933925"}, {"RefNumber": "2858217", "RefComponent": "TM-CP", "RefTitle": "SAP TM Collaboration Portal usage in S/4HANA", "RefUrl": "/notes/2858217"}, {"RefNumber": "2812981", "RefComponent": "SCM-EWM-IF-TM", "RefTitle": "Transportation Management and decentralized EWM in same SAP S/4HANA system and client", "RefUrl": "/notes/2812981"}, {"RefNumber": "2714892", "RefComponent": "TM-CF", "RefTitle": "SAP Transportation Management - Deployment Options", "RefUrl": "/notes/2714892"}, {"RefNumber": "2673954", "RefComponent": "TM-FWS-BIL", "RefTitle": "ERP Sales organization missing when transferring Forwarding settlement document to ERP", "RefUrl": "/notes/2673954"}, {"RefNumber": "2640349", "RefComponent": "TM-FWS", "RefTitle": "Posting of Forwarding settlement documents in S/4HANA 1809 and above", "RefUrl": "/notes/2640349"}, {"RefNumber": "2638834", "RefComponent": "TM-MD-TN-LOC", "RefTitle": "Location Replication Out of ERP", "RefUrl": "/notes/2638834"}, {"RefNumber": "2634924", "RefComponent": "LO-INT-TM-SLS", "RefTitle": "Service Enhancements for TransportationRequestSUITERequest", "RefUrl": "/notes/2634924"}, {"RefNumber": "2620322", "RefComponent": "SBN-LBN-INT", "RefTitle": "Business Network for Logistics: Integration to SAP TM and TM in S/4HANA", "RefUrl": "/notes/2620322"}, {"RefNumber": "2583557", "RefComponent": "TM-FRS", "RefTitle": "Change the value of Plant during the accruals posting of freight settlement/Credit memo/Charge Correction Advice documents to S4 ERP system", "RefUrl": "/notes/2583557"}, {"RefNumber": "2465978", "RefComponent": "TM-FRS-IP", "RefTitle": "S4TWL : Impact on TM process with conversion of SAP ERP or upgrade of S/4HANA 1511/1610 to S/4HANA 1709", "RefUrl": "/notes/2465978"}, {"RefNumber": "2409939", "RefComponent": "LO-MD-BP", "RefTitle": "S4TWL - Business Partner Hierarchy Not Available", "RefUrl": "/notes/2409939"}, {"RefNumber": "2408419", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA - Multi-Node Support", "RefUrl": "/notes/2408419"}, {"RefNumber": "2240387", "RefComponent": "TM-BF-UI", "RefTitle": "UI: Endless transaction mode and optimistic locks in single document screens", "RefUrl": "/notes/2240387"}, {"RefNumber": "2238445", "RefComponent": "SCM", "RefTitle": "Integration of Supply Chain Management Applications to SAP S/4HANA", "RefUrl": "/notes/2238445"}, {"RefNumber": "1978857", "RefComponent": "TM-INT-LI-CS-OTR", "RefTitle": "WS/RM Integration Guide for SAP Transportation Management", "RefUrl": "/notes/1978857"}, {"RefNumber": "1606493", "RefComponent": "SCM-EWM-IF-MIG", "RefTitle": "SAP EWM Deployment Options Best Practices", "RefUrl": "/notes/1606493"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2618827", "RefComponent": "LO-MD-MM", "RefTitle": "Performance: GUID conversion report for SCM", "RefUrl": "/notes/2618827 "}, {"RefNumber": "3289248", "RefComponent": "TM-CF", "RefTitle": "Auditing TM vs. Advanced TM", "RefUrl": "/notes/3289248 "}, {"RefNumber": "3225241", "RefComponent": "TM-CF", "RefTitle": "Advanced Shipping and Receiving - Setup Guide", "RefUrl": "/notes/3225241 "}, {"RefNumber": "3081750", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA: Restriction Note for \"Multiple Address Handling in SD Documents Using SAP Business Partner\"", "RefUrl": "/notes/3081750 "}, {"RefNumber": "2714892", "RefComponent": "TM-CF", "RefTitle": "SAP Transportation Management - Deployment Options", "RefUrl": "/notes/2714892 "}, {"RefNumber": "3079550", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2021: Restriction Note", "RefUrl": "/notes/3079550 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}