{"Request": {"Number": "2592580", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 530, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000155382018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=F52A6402693B04FE96FC790DEA9E7F17"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2592580"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.02.2018"}, "SAPComponentKey": {"_label": "Component", "value": "FI-RA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Revenue Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Revenue Accounting", "value": "FI-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2592580 - Inflight Check C04 Is Reported When Do Transition For the Contract  which Time-Based POB's spreading revenue amount is not equal to their allocated amount"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>When&#160;do transition&#160;the&#160;Inflight Check C04 will be raised.</p>\r\n<p>The issue only occurs when do transition for the contracts on below condition:</p>\r\n<p>1. The source contract has several time-based Performance Obligations (POBs).</p>\r\n<p>2. All of POBs has done the manual spreading.</p>\r\n<p>3. Some of POBs' total spreading revenue amount is not equal to their allocated amount.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FARR, Revenue Accounting, Manual Spreading, Transition, Inflight Check C04</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Program error.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p id=\"\">&#160;Apply this note.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I312172)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I318625)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2580961", "RefComponent": "FI-RA-MIG", "RefTitle": "RAR Data Migration and Transition - Additional Information", "RefUrl": "/notes/2580961 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "REVREC", "From": "120", "To": "120", "Subsequent": ""}, {"SoftwareComponent": "REVREC", "From": "130", "To": "130", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "REVREC 120", "SupportPackage": "SAPK-12007INREVREC", "URL": "/supportpackage/SAPK-12007INREVREC"}, {"SoftwareComponentVersion": "REVREC 130", "SupportPackage": "SAPK-13005INREVREC", "URL": "/supportpackage/SAPK-13005INREVREC"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "REVREC", "NumberOfCorrin": 2, "URL": "/corrins/**********/14019"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 14, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2318621 ", "URL": "/notes/2318621 ", "Title": "Error revenue schedule after change spreading by calling API", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2318790 ", "URL": "/notes/2318790 ", "Title": "Resolve the issue that the revenue schedule is missing of migration period", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2380602 ", "URL": "/notes/2380602 ", "Title": "Manual spreading cannot be saved as expected", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2390493 ", "URL": "/notes/2390493 ", "Title": "Enhancement of interface IF_FARR_CONTRACT_INIT_LOAD~TRANSFER_SCHEDULED_FULFILL( ) for resolving spreading conflict", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2390501 ", "URL": "/notes/2390501 ", "Title": "Change Spreading error: can't save change correctly for combined contract", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2412327 ", "URL": "/notes/2412327 ", "Title": "Manual spreading after contract modification is incorrect", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2416848 ", "URL": "/notes/2416848 ", "Title": "Enable transferring manual spreading flag during initial load step \"transfer scheduled fulfill\"", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2425982 ", "URL": "/notes/2425982 ", "Title": "Migration: RAR will calculate exchange rate if sender component is not able to send the exchange rate", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2428482 ", "URL": "/notes/2428482 ", "Title": "Allow Time-Based Performance Obligation with Start Date Type &#x2018;3&#x2019; to Do Manual Spreading", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2434654 ", "URL": "/notes/2434654 ", "Title": "Migration: Initial Load Cleanup - Deletion of temporarily stored CO Object Numbers", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2437650 ", "URL": "/notes/2437650 ", "Title": "RAR 1.2 - The Result of Manual Spreading is Incorrect for Time-Based Performance Obligation with Fulfillment Entries in Inconsecutive Accounting Periods", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2405633 ", "URL": "/notes/2405633 ", "Title": "Error revenue schedule after change spreading by calling API", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2406251 ", "URL": "/notes/2406251 ", "Title": "Change Spreading error: can't save change correctly for combined contract", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2412327 ", "URL": "/notes/2412327 ", "Title": "Manual spreading after contract modification is incorrect", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2425982 ", "URL": "/notes/2425982 ", "Title": "Migration: RAR will calculate exchange rate if sender component is not able to send the exchange rate", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2428482 ", "URL": "/notes/2428482 ", "Title": "Allow Time-Based Performance Obligation with Start Date Type &#x2018;3&#x2019; to Do Manual Spreading", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2434654 ", "URL": "/notes/2434654 ", "Title": "Migration: Initial Load Cleanup - Deletion of temporarily stored CO Object Numbers", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2475759 ", "URL": "/notes/2475759 ", "Title": "The Result of Manual Spreading is Incorrect for Time-Based Performance Obligation with Fulfillment Entries in Inconsecutive Accounting Periods", "Component": "FI-RA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}