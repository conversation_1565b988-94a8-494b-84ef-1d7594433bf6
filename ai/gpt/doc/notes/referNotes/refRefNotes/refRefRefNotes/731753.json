{"Request": {"Number": "731753", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2003, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003949962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000731753?language=E&token=AC9D4F771AB70E9062F8372D293A02D7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000731753", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000731753/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "731753"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "PY-PT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Portugal"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portugal", "value": "PY-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "731753 - HR-PT: Article 220º \"Efeitos da suspensao do contrato ...\""}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2</td>\r\n<td>July 19, 2016</td>\r\n<td>SAR files were removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The article 220&#186; of Portuguese Labor Law applies in the following situation:</p>\r\n<ul>\r\n<li>when the employee has an absence which is starting and finishing in different years and the calendar days is greater than 30 days.</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;According to the law, the employee should have the same treatment as a new contract(n.&#186; 2 do artigo 212.&#186;).<br /><br /><br /><strong>Artigo 220.&#186;</strong><br />Efeitos da suspens&#227;o do contrato de trabalho por impedimento prolongado<br /><br />1 - No ano da suspens&#227;o do contrato de trabalho por impedimento prolongado, respeitante ao trabalhador, se se verificar a impossibilidade total ou parcial do gozo do direito a f&#233;rias j&#225; vencido,o trabalhador tem direito &#224; retribui&#231;&#227;o correspondente ao per&#237;odo de f&#233;rias n&#227;o gozado e respectivo subs&#237;dio.<br />2 - No ano da cessa&#231;&#227;o do impedimento prolongado o trabalhador tem direito &#224;s f&#233;rias nos termos previstos no n.&#186; 2 do artigo 212.&#186;<br />3 - No caso de sobrevir o termo do ano civil antes de decorrido o prazo referido no n&#250;mero anterior ou antes de gozado o direito a f&#233;rias, pode o trabalhado usufru&#237;-lo at&#233; 30 de Abril do ano civil subsequente.<br /><br /><br /><strong>Artigo 212.&#186;</strong><br />Aquisi&#231;&#227;o do direito a f&#233;rias<br /><br />2 - No ano da contrata&#231;&#227;o, o trabalhador tem direito, ap&#243;s seis meses completos de execu&#231;&#227;o do contrato, a gozar 2 dias &#250;teis de f&#233;rias por cada m&#234;s de dura&#231;&#227;o do contrato, at&#233; ao m&#225;ximo de 20 dias &#250;teis.<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>220; quota; HR_CHANGE_QUOTAS_GEN_19; HR_QTACC_ACCRUAL_ENTITLE_19;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The correction described in this note will be included in an HR Support<br />Package, as indicated in item \"Reference to Support Packages\".<br /><br />The support package includes:</p>\r\n<ul>\r\n<li>changes in function module HR_CHANGE_QUOTAS_GEN_19 and HR_QTACC_ACCRUAL_ENTITLE_19;</li>\r\n</ul>\r\n<ul>\r\n<li>new field RVABS(absence classification) in table T5PAB;</li>\r\n</ul>\r\n<ul>\r\n<li>view V_T5PAB has been changed in order to show the new field RVABS;</li>\r\n</ul>\r\n<ul>\r\n<li>new domain and data element PPT_RVABS.</li>\r\n</ul>\r\n<p><br />Note: The field RVABS in view V_T5PAB must be filled with \"Aus&#234;ncia prolongada\" for the relevant absences. There is a list box for this field.<br /><br /><strong>For Advance Delivery:</strong><br />Please, see the attachment of this note.</p>\r\n<ul>\r\n<li>For release 4.5B: L4DK118095.CAR</li>\r\n</ul>\r\n<ul>\r\n<li>For release 4.6B: L9BK123658.CAR</li>\r\n</ul>\r\n<ul>\r\n<li>For release 4.6C: L9CK177465.CAR</li>\r\n</ul>\r\n<ul>\r\n<li>For release 4.70: L6BK081795.CAR</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I823284)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I827735)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000731753/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000731753/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000731753/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000731753/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000731753/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000731753/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000731753/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000731753/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000731753/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1091783", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Generation of the VA contingent/long absences", "RefUrl": "/notes/1091783"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1091783", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Generation of the VA contingent/long absences", "RefUrl": "/notes/1091783 "}, {"RefNumber": "958071", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Article 220º - Long absences and VA days entitlement", "RefUrl": "/notes/958071 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45BC1", "URL": "/supportpackage/SAPKE45BC1"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BA4", "URL": "/supportpackage/SAPKE46BA4"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C95", "URL": "/supportpackage/SAPKE46C95"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47040", "URL": "/supportpackage/SAPKE47040"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50007", "URL": "/supportpackage/SAPKE50007"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 2, "URL": "/corrins/0000731753/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "691640 ", "URL": "/notes/691640 ", "Title": "HR-PT: Corrections to the New Labor Law 2003", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "416743 ", "URL": "/notes/416743 ", "Title": "HR-PT: Corrections to the new Vacation Allowance", "Component": "PY-PT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}