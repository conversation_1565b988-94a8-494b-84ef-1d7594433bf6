{"Request": {"Number": "600460", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1102, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003060982017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000600460?language=E&token=E2D4BA0A8E0DACCEDF8B16932477ABFB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000600460", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000600460/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "600460"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.08.2006"}, "SAPComponentKey": {"_label": "Component", "value": "IS-R-LG-SPV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Sales Price Valuation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Specific Component Retail", "value": "IS-R", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-R*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Merchandise Logistics", "value": "IS-R-LG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-R-LG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Sales Price Valuation", "value": "IS-R-LG-SPV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-R-LG-SPV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "600460 - Reval at retail updte:2LIS_40_REVAL, 2LIS_03_BF, 2LIS_03_UM"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><UL><LI>The data of the revaluation at retail is updated twice when you reorganize the info sources 2LIS_40_REVAL and 2LIS_03_UM. This problem only affects quantity-based inventory-managed articles.</LI></UL> <UL><LI>For purchase price changes due to revaluations that are not margin-related, no data enters the standard stock structures in LIS. If you use the revaluation at retail with revaluation profile in which the 'Not affect.&#x00A0;&#x00A0;margins' indicator is set, stock inconsistencies occur in all update targets that are not retail-specific.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Update, 2LIS_40_REVAL, 2LIS_03_UM, 2LIS_03_BF</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by a design error.<br />Problems are only expected if the valuation at retail is used with a revaluation profile without affecting margins in connection with the normal, quantity-based inventory management. In all other cases, it is not essential that you implement the note - you should assess if this is necessary. After you implement the note, however, the update behavior also changes for the update for articles with value-based inventory management (revaluation profile both affecting margins and without affecting margins).<br />The previous update behavior depends on how the revaluation is set and on the type of inventory management. The system only updates the stock changes (value for the purchase and retail price) when you use&#x00A0;&#x00A0;the statistics update for the revaluation document segment USEG itself - the data is included in InfoSource 2LIS_40_REVAL or in the LIS using event 'RP' and update group 000810 (IS-Retail).<br />Follow-on documents (for example, material documents or purchase price change documents (see below)) that may be generated are NOT updated!<br />Since an update from retail actions (update group 000810) does not update standard LIS structures, all stock changes from revaluations are consequently missing in all standard structures both in the LIS and in BW.<br />During the revaluation at retail, the following cases may occur:</p> <OL>1. Quantity-based inventory management</OL> <OL><OL>a) Affecting margins</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the R/3 system, a revaluation segment (USEG) is generated without follow-on documents. The data is updated to BW using InfoSource 2LIS_40_REVAL and to the LIS using event RP(000810).</p> <OL><OL>b) Without affecting margins</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the R/3 system, a revaluation segment (USEG) and a price change document of the purchase price change (transaction MR21) are generated. The data of the price change document is neither updated to the InfoSource 2LIS_03_UM nor to the standard LIS structures of the inventory management since the data is already being entered statistically by the retail update 2LIS_40_REVAL or the update group 000810 for event RP.&#x00A0;&#x00A0;During the reorganization of the stock data, both the revaluation documents and the price change documents incorrectly lead to an update of the purchasing stock.</p> <OL>2. Value-based inventory management</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Basically, a material document is generated during value-based inventory management. The material document itself is not updated to BW or RIS since the update occurs using the retail-specific extraction just like in the quantity-based case. Since the movement types (721, 722, 731 and 732) are not delivered as relevant for statistics, no update occurs during the reorganization either.</p> <OL><OL>a) Affecting margins</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;InfoSource 2LIS_40_REVAL only updates the sales value. In the same way, the sales value for event RP(000810) is updated to the LIS.</p> <OL><OL>b) Without affecting margins</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Purchasing and sales value changes are updated to the stock in BW using InfoSource 2LIS_40_REVAL and using event RP(000810) in the LIS.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note changes the update of the revaluation documents into LIS and BW for the 'quantity-based inventory management without affecting margins' cases and 'value-based inventory management'cases. You can either implement the source code changes manually or import them with a Support Package; however, you must activate the note manually using a modification after you implement it automatically because otherwise, stock inconsistencies may occur. We recommend that you implement the corresponding Support Package of the relevant ERP release (and implement the corresponding Support Package of the relevant Plug-In release if a BW is connected). You only need to implement the changes in the Plug-In if a BW system exists.<br /><br /><STRONG>Caution !</STRONG><br /><br />If you implement this note in a system which is not linked to a BW system, you have to make sure that this note is also implemented in the corresponding Plug-In if a BW system is linked later.<br />In addition, you have to make sure that the specified modification is not changed back to the delivery status during an upgrade.<br /><br />We strongly recommended that you thoroughly test the entire update of the revaluation at retail after you implement it. This should only be done by an experienced SAP consultant.</p> <UL><LI>Implement the attached source code corrections.</LI></UL> <UL><UL><LI>Create a new include with the name 'LWBW1FBW' in the function group WBW1 . The value of constant G_Note_600460 in this include defines if the correction specified in this note becomes active. However, do not change the value before you have changed the Customizing of the movement types (see below).</LI></UL></UL> <UL><UL><LI>Create the following new function module in the function group WBW1:<br />Name:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'GET_NEW_UPDATE_600460'<br />Export parameter: E_UPDATE_TYPE TYPE INT1</LI></UL></UL> <UL><LI>Use the table maintenance Transaction SE16 to change the T156-XSTBW indicator to the value \" \" (space) for movement types 722 and 732. To do this, you can also use the specified report ZZCORR_MOVTYPE. You can use also the specified report 'ZZCORR_MOVTYPE' for that. The changes are transported when you make further changes to&#x00A0;&#x00A0;Customizing in the next step.</LI></UL> <UL><LI>Change the Customizing of movement types 721, 722, 731 and 732. To do this, go to the standard IMG as follows:<br /> + Materials Management and Physical Inventory<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;+ Movement Types<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;+ Copy, Change Movement types<br />Select the movement types and change the indicator from \"statistics relevant\" from ' ' (space) to '2' on the detail screen of the movement type.&#x00A0;&#x00A0;Remember to transport this change only with the source code change of the G_Note_600460 indicator. This MUST be set to 'X' after the Customizing change. However, it is also possible to deactivate the note again by cancelling the constant and the statistical relevance of the above movement types again.</LI></UL> <UL><LI>Only transport the changes initially to the test system. Make sure that there are no Infocubes in the BW (if this exists) that are updated from the InfoSource 2LIS_40_REVAL since the actual stock changes are now updated using the InfoSources 2LIS_03_BF or 2LIS_03_UM (except for the quantity-based inventory management and revaluations without affecting margins). For all update targets that are updated from the InfoSources 2LIS_03_BF, 2LIS_03_UM and 2LIS_40_REVAL on the purchasing and sales side or only 2LIS_03_BF and 2LIS_03_UM on the purchasing side, the update should work. It is very likely that a complete reorganization of the stock data is required after you have implemented this note. If you are in doubt, refer to SAP via the SAPNet R/3 Frontend.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-ISR-LOG (BW only - Logistic)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025477)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022839)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000600460/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000600460/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000600460/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000600460/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000600460/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000600460/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000600460/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000600460/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000600460/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1149830", "RefComponent": "BW-BCT-ISR-RSL", "RefTitle": "Double countings of revaluations in stock ledger", "RefUrl": "/notes/1149830"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1149830", "RefComponent": "BW-BCT-ISR-RSL", "RefTitle": "Double countings of revaluations in stock ledger", "RefUrl": "/notes/1149830 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2003_1_46B", "To": "2003_1_470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "PI 2003_1_46B", "SupportPackage": "SAPKIPZH33", "URL": "/supportpackage/SAPKIPZH33"}, {"SoftwareComponentVersion": "PI 2003_1_46C", "SupportPackage": "SAPKIPZH43", "URL": "/supportpackage/SAPKIPZH43"}, {"SoftwareComponentVersion": "PI 2003_1_470", "SupportPackage": "SAPKIPZH53", "URL": "/supportpackage/SAPKIPZH53"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B54", "URL": "/supportpackage/SAPKH46B54"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C45", "URL": "/supportpackage/SAPKH46C45"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C44", "URL": "/supportpackage/SAPKH46C44"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47013", "URL": "/supportpackage/SAPKH47013"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 4, "URL": "/corrins/0000600460/1"}, {"SoftwareComponent": "PI", "NumberOfCorrin": 3, "URL": "/corrins/0000600460/48"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 7, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "46B", "ValidTo": "46B", "Number": "493530 ", "URL": "/notes/493530 ", "Title": "Revaluation at retail: Missing purchasing price change", "Component": "IS-R-LG-SPV"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}