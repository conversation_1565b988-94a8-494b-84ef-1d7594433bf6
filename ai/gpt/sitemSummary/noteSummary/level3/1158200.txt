SAP Note 1158200 pertains to the Patient Routing System (PLS) used in IS-H (Industry-Specific Hospital Solutions) for major accidents in the country version for Austria (AT). The note describes how each patient receives a PLS number in an orange bag at the accident location, which can be entered into NV2000 and saved on a case-related basis. The PLS number in Austria consists of nine digits, which include a state code, a two-character emergency service abbreviation, a two-character subgroup of the emergency service, and a four-digit sequence number.

The system ensures that the entered PLS number consists of nine digits. The PLS number can be displayed in the layouts of public lists, case lists, and the clinical workstation (in the Outpatient Clinic and Occupancy/Arrivals/Departures view types), starting from IS-H version 6.00.

The solution provided in the note involves a few steps:

1. Users must first download and unpack the provided zip files related to the IS-H version they are using (6.03, 6.00, or 4.72) from the SAP Service Marketplace. The files for download are HW1158200_603.zip and HW1158200_603_cust.zip for version 6.03, HW1158200_600.zip and HW1158200_600_cust.zip for version 6.00, and HW1158200_472.zip and HW1158200_472_cust.zip for version 4.72.

2. Then, users should import the unpacked requests into their system.

3. Finally, they need to implement the source code corrections as outlined in this SAP Note.

The note also includes other terms related to the PLS, such as NFAL and PLSNR, and references NV2000. There are no specific prerequisites mentioned other than those inherent to the symptoms and issues being addressed. The note implies that users should follow the steps outlined to resolve the issues with the PLS number entry and display within the IS-H system in the Austrian country version.