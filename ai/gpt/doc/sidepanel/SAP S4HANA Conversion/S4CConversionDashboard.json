{"title": "Learn More About SAP Readiness Check for SAP S/4HANA", "next": "<p>\n            To see your check results in more detail, choose a check title.<br /><br />\n            The available checks support you to plan and organize your transition to SAP Cloud ALM completely or by following a scenario or capability approach. To explore the check results, you can take the following additional steps:<br />\n            <ul>\n              <li>Contact SAP or your preferred implementation partner.</li>\n              <li>SAP Enterprise Support customers can <a rel='noopener noreferrer' href='https://support.sap.com/en/offerings-programs/enterprise-support/enterprise-support-academy/learn.html'>sign up</a> to attend any of the following sessions, at no additional cost:</li>\n              <ul>\n                <li><a rel='noopener noreferrer' href='https://saplearninghub.plateau.com/learning/user/deeplink_redirect.jsp?linkId=CATALOG_SEARCH&sbArSel=&keywords=SUP_EDE_0080_1712&selKeyWordHeader=SUP_EDE_0080_1712&catSel=&srcSel=ESAC&delMthSel=&ILDateFrm=&ILDateTo=&ILBlend=&ILSchd=&fromSF=Y&company=learninghub&_s.crb=LS72DBTT1uDrxWTrNJuAPfZmUL4%3d'>SAP Readiness Check tool for SAP S/4HANA</a> (course ID: SUP_EDE_0080_1712), which is part of the <a rel='noopener noreferrer' href=\"https://support.sap.com/en/offerings-programs/enterprise-support/enterprise-support-academy/expert-guided-implementation.html\">Expert Guided Implementation</a> (EGI) offerings</li>\n                <li><a rel='noopener noreferrer' href='https://accounts.sap.com/saml2/idp/sso?sp=https://www.successfactors.eu/learninghub&RelayState=%2Fsf%2Flearning%3FdestUrl%3Dhttps%253a%252f%252fsaplearninghub%252eplateau%252ecom%252flearning%252fuser%252fdeeplink%255fredirect%252ejsp%253flinkId%253dCATALOG%255fSEARCH%2526sbArSel%253d%2526keywords%253dSUP_EBW_1330_1910%2526selKeyWordHeader%253dSUP_EBW_1330_1910%2526catSel%253d%2526srcSel%253dESAC%2526delMthSel%253d%2526ILDateFrm%253d%2526ILDateTo%253d%2526ILBlend%253d%2526ILSchd%253d%2526fromSF%253dY%26company%3Dlearninghub'>Live Must-Know Webinar</a> (course ID: SUP_EBW_1330_1910)</li>\n                <li><a rel='noopener noreferrer' href='https://accounts.sap.com/saml2/idp/sso?sp=https://www.successfactors.eu/learninghub&RelayState=%2Fsf%2Flearning%3FdestUrl%3Dhttps%253a%252f%252fsaplearninghub%252eplateau%252ecom%252flearning%252fuser%252fdeeplink%255fredirect%252ejsp%253flinkId%253dCATALOG%255fSEARCH%2526sbArSel%253d%2526keywords%253dSUP_EWI_2040_1812_REQUEST%2526selKeyWordHeader%253dSUP_EWI_2040_1812_REQUEST%2526catSel%253d%2526srcSel%253dESAC%2526delMthSel%253d%2526ILDateFrm%253d%2526ILDateTo%253d%2526ILBlend%253d%2526ILSchd%253d%2526fromSF%253dY%26company%3Dlearninghub'>Accelerated Innovation Enablement</a> (AIE) (course ID: SUP_EWI_2040_1812_REQUEST)</li>\n              </ul>\n              <li>Explore the <a rel='noopener noreferrer' href='https://go.support.sap.com/roadmapviewer/#/group/AAE80671-5087-430B-9AA7-8FBE881CF548/roadmapOverviewPage/S4HANATRANSONPRE'>SAP S/4HANA project roadmap for the transition to SAP S/4HANA</a>.</li>\n              <li>See the <a rel='noopener noreferrer' href='https://help.sap.com/doc/2b87656c4eee4284a5eb8976c0fe88fc/latest/en-US/'>Conversion Guide for SAP S/4HANA</a>.</li>\n              <li>See the <a rel='noopener noreferrer' href='https://www.sap.com/documents/2019/05/44b3ebd5-4b7d-0010-87a3-c30de2ffd8ff.html'>Mapping Your Journey to SAP S/4HANA</a> guide.</li>\n              <li>See the <a rel='noopener noreferrer' href='https://www.sap.com/services/implementation/implementation-pkg.html'>SAP Value Assurance service packages for SAP S/4HANA</a>.</li>\n              <li>Read the book <a href='https://www.sap-press.com/4782'>SAP S/4HANA, An Introduction</a>.</li>\n            </ul>\n            Note that a transition from a typical SAP ERP 6.x system to SAP S/4HANA requires a database migration to SAP HANA (if you are not currently running Suite on SAP HANA). If you are already running on SAP HANA 1.0, an update to SAP HANA 2.0 is required. In addition, the installation of new simplified code and adaptations is required. If your source system is not on Unicode yet, you need to convert to Unicode prior to making the move to SAP S/4HANA.<br /><br />\n            Please also consider that beyond the checks provided in this tool, additional restrictions and landscape requirements might be relevant for your system. For more information, see the release information and release restriction notes, which appear on the <a rel='noopener noreferrer' href='https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/latest/en-US?task=discover_task'><cite>Discover</cite></a> tab on SAP Help Portal for your target SAP S/4HANA release. The documentation for the current release is accessible via this link: <a rel='noopener noreferrer' href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/latest/\">https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/latest/</a><br />\n            </p>", "info": "<p>\n            With SAP Readiness Check for SAP S/4HANA, SAP provides a self-service tool to check the readiness of your source system for a transition to a selected target SAP S/4HANA release. It helps to identify required preparations and possible pre-projects for your system well before the transition project starts and enables you to understand respective implications. This early insight means that you can scope and plan your transition project with a higher degree of accuracy. For useful transactional data and history, we recommend running SAP Readiness Check for SAP S/4HANA on a production system (or a recent copy of one).<br /><br />\n            Based on the analysis of your source system, the tool provides a comprehensive overview of specific parameters throughout various checks:<br />\n            <ul>\n              <li><span style='font-style:italic'>Simplification Items: </span>Simplification items that are relevant for your transition and their characteristics, such as effort ranking categories. For simplification items categorized with the default effort ranking category of <cite>Potentially High</cite>, identified effort drivers provide insight into the required effort and time.</li>\n              <li><cite>Compatibility Scope Analysis</cite>: Provides an overview of relevant compatibility packages, which offer limited usage rights to run certain classic SAP ERP solutions on SAP S/4HANA.</li>\n              <li><span style='font-style:italic'>Activities Related to Simplification Items: </span>Project-related activities that apply to relevant simplification items.</li>\n              <li><span style='font-style:italic'>Add-On Compatibility: </span>Compatibility of add-ons and vendor categorization.</li>\n              <li><span style='font-style:italic'>Active Business Functions: </span>Compatibility of business functions that are active in the source system.</li>\n              <li><span style='font-style:italic'>SAP S/4HANA Sizing: </span>Simulation of the target SAP S/4HANA size, sizing of top database tables, and potential for data volume management.</li>\n              <li><span style='font-style:italic'>Custom Code Analysis: </span>Interoperability between your custom code and the target SAP S/4HANA release and required adaptations.</li>\n              <li><span style='font-style:italic'>Integration: </span>Automated interface impact analysis that identifies interfaces that depend on restricted functionality or break upon transition.</li>\n              <li><span style='font-style:italic'>Customer Vendor Integration Analysis: </span>Evaluation of system status and data quality related to customer vendor integration, which is mandatory for a technical conversion from SAP ERP to SAP S/4HANA.</li>\n              <li><span style='font-style:italic'>Planned Downtime Calculator: </span>Calculation of planned business downtime for the conversion, based in part on empirical values and on customer-submitted statistics.</li>\n              <li><span style='font-style:italic'>Financial Data Quality: </span>Identification of the quality and consistency of your financial data, based on the FIN_CORR_RECONCILE report results and other essential indicators.</li>\n              \n              <li><span style='font-style:italic'>App Availability: </span>Identification of apps in the source system using the UI technology type SAP GUI, Web Dynpro, or SAP Fiori, which will be deprecated, unavailable, or available with a successor in the SAP S/4HANA target release. If available, recommendations for successor apps are provided.</li>\n              <li><span style='font-style:italic'>Recommended SAP Fiori Apps: </span>Recommendations for SAP Fiori apps, grouped by SAP Fiori roles.</li>\n              <li><span style='font-style:italic'>Business Process Discovery: </span>As-is analysis of key figures for selected business processes and your improvement potential.</li>\n              <li><span style='font-style:italic'>SAP Innovative Business Solutions: </span>Compatibility of your customized developments, built specifically for your business.</li>\n          \n            </ul>\n            </p>", "disclaim": "<p>\n            The findings presented by SAP Readiness Check include a mix of both client-dependent and client-independent results. If the analyzed system contains more than one active client, it is recommended to collect and upload the results as separate analyses. More information about the nature of each check is available within the respective <span style='font-style:italic'>Learn More</span> side panel content.<br/>\n          </p>"}