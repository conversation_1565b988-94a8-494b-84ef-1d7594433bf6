SAP Note 1828344 addresses an issue with the function module RSSM_PROCESS_EVENT_RESET in SAP NetWeaver Business Warehouse (BW) where each call to the function module requires up to a one-second wait time, leading to performance problems.

Summary of the issue:
- The symptom is that the function module RSSM_PROCESS_EVENT_RESET is taking up to one second per call, which impacts the performance negatively.
- The reason for this issue is identified as a program error.

Solution provided in the SAP Note:
- To resolve this issue, SAP recommends importing the appropriate Support Package for the version of SAP NetWeaver BW being used. The specific Support Package levels mentioned for each version are as follows:
  - SAP NetWeaver BW 7.00: Support Package 31 (SAPKW70031)
  - SAP NetWeaver BW 7.01: Support Package 14 (SAPKW70114)
  - SAP NetWeaver BW 7.02: Support Package 14 (SAPKW70214)
  - SAP NetWeaver BW 7.11: Support Package 12 (SAPKW71112)
  - SAP NetWeaver BW 7.30: Support Package 10 (SAPKW73010)
  - SAP NetWeaver BW 7.31: Support Package 8 (SAPKW73108)
  - SAP NetWeaver BW 7.40: Support Package 3 (SAPKW74003)

Each of these Support Packages will become available when the corresponding detailed SAP Note for the Support Package is released to customers.

For immediate relief, SAP provides an option to implement correction instructions as an advance correction if the issue is urgent.

Before applying these changes, users must read SAP Note 875986, which contains information about using transaction SNOTE, a standard SAP transaction that is used to download and implement SAP Notes.

The SAP Notes that describe the Support Packages in more detail may be available before the official release of the Support Package itself. If this is the case, such SAP Notes will be marked with the phrase "Preliminary version" in the short text.

To summarise, users facing performance issues with the RSSM_PROCESS_EVENT_RESET function module should plan to import the relevant Support Packages for their version of SAP BW, or if immediate action is required, implement the correction instructions provided in the note after reading the important information in SAP Note 875986.