SAP Note 762949 addresses a series of corrections and improvements for SCM (Supply Chain Management) 4.1, specifically for the LCA (Live Cache Applications) Build 03. The note details various issues that customers might experience and provides solutions for them. Key points include:

Symptoms and Corrections:
1. General improvements:
   - Fixed errors in accessing global memory.
   - Resolved issues with reading liveCache and LCA build version information.
   - Corrected errors in accessing BlockArray.
   - Solved problems in the LCA logging function, important for SAP Support.

2. ATP (Available-to-Promise):
   - Corrected errors that incorrectly updated negative stocks as positive in ATP time series.

3. Scheduling:
   - Enhanced test routines.
   - Resolved a short dump error with finite scheduling on BucketVectors.
   - Solved error behavior during scheduling in insert mode with block planning.
   - Fixed scheduling issues with fixed intermittent activities.

4. Order Creation/Change:
   - Fixed an error with the creation of requests with internal constraints.
   - Corrected a short dump related to request changes involving dummy pegareas.

5. Pegging:
   - Resolved short dumps in OM_RG_OF_COVER_MATRIX due to invalid PegArea.
   - Fixed a short dump in OM_PEGID_GET_IO function module.
   - Addressed errors with consecutive IO nodes overlapping in time.

6. RPM (Resource and Portfolio Management):
   - Corrected errors when deactivating the RPM time series.
   - Fixed problems with reading forecast time series.

7. Alerts:
   - Resolved recalculating alerts post-order change.
   - Corrected errors in alert specifications with characteristic blocks.

8. Optimizer Interface:
   - Fixed errors with reading indirectly determined activities from liveCache.

9. Demand Planning/SNP Time Series:
   - Normalized disaggregation logic.
   - Error correction in OM_TS_QUERY_OBJECTS for liveCache filling level specification in DP.
   - Improved consistency checks for time series in liveCache.

Other Terms:
- The note refers to liveCache and LCA-Build within the context of SCM version 4.1.

Reason and Prerequisites:
- The issues arose due to programming errors.

Solution:
- Users should import SCM 4.1 LCA build 03 patch level 001 along with the corresponding liveCache kernel 7.5.0 build 016.
- The patch is available on SAP Service Marketplace, with separate instructions for AIX 5.1 users which can be found in note 737752.
- After installing liveCache kernel and LCA build, a liveCache restart is required, but initialization is not.
- The note references additional SAP Notes for further information and guidance on operating system parameters and system requirements for AIX.

By following the instructions and implementing the provided solutions, users will be able to resolve the specified issues and improve their SCM 4.1 LCA-Build 03 system performance.