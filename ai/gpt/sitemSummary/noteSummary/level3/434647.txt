SAP Note 434647 pertains to the procedure and considerations for conducting a point-in-time recovery within a system group in SAP. Here’s a summary:

**Symptom:**
The note addresses scenarios where a point-in-time recovery is necessary to revert a database in a system group to a prior state when alternative options (as described in Note 434645) are not viable.

**Other Terms:**
Key terms related to this process include a point-in-time restore or recovery, PIT, flashback, etc.

**Reason and Prerequisites:**
This kind of recovery often leads to data loss and inconsistencies across the system landscape. The note recommends against performing such recoveries in a production system unless absolutely necessary because of the complexity and time required to reconcile inconsistencies.

**Summary of Recovery Options:**
1. **Point-in-time recovery of the affected system only:** Minimizes data loss to one system, but results in numerous inconsistencies.
2. **Point-in-time recovery of all components in the landscape:** Reduces inconsistencies but increases data loss and lacks recovery options from other components.
3. **Importing a consistent backup of the entire landscape:** Requires a previously created backup. This option involves the most significant data loss but no inconsistencies.

**Solution:**
The note does not provide a one-size-fits-all solution due to the dependency on specific business processes. Still, it emphasizes certain steps:

1. **Opening a CSS Message**: Before proceeding with the recovery, create a CSS message to potentially explore alternative solutions.
2. **Decision Making**: Decide whether to minimize data loss by only restoring the affected system (higher cleanup effort due to more inconsistencies) or restore all systems (more data loss but fewer inconsistencies).
3. **Reconcile Inconsistencies**: Address logical inconsistencies across systems before resuming production operations. SAP tools and documents (e.g., "Data Consistency Check for Logistics" Best Practices) are available for assistance.

The note also suggests the use of SAP-provided tools for data analysis to help identify and correct data inconsistencies that can occur during normal operation as well as after a recovery.

**Additional Information:**
For further guidance, including backup and restore concepts, the note refers to the Best Practice document "Backup and Restore for mySAP.com Business Suite" available at http://service.sap.com/runsap.

In summary, SAP Note 434647 guides on careful planning and execution of point-in-time recoveries within system groups, emphasizing the trade-off between data loss and system inconsistencies and the requirement for meticulous inconsistency resolution post-recovery.