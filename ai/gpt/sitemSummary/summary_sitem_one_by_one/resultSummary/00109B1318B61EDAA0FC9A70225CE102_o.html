<h3>SI09: AS_ABAP_WORKFLOW_VISUALIZATION</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2896183">2896183 - ABAPTWL – visualization of SAP Business Workflow metadata</a></strong></p>
<p><strong>Description:</strong></p>
<p>With the upgrade or system conversion to SAP S/4HANA 2020, all SWFVISU settings are overwritten with the delivery data. Customer-specific workflow tasks may respond differently at runtime or are not displayed as usual.</p>
<p>For technical reasons, the delivery class of SWFVISU tables had to be changed from type "G" to "E". With type "G", customers could only add data records delivered by SAP. Existing settings were not overwritten. Type "E" also allows changing contents.</p>
<p>You make the settings for displaying tasks and object types using SWFVISU or SWFVMD1.</p>
<ul>
<li>SWFVISU saves the settings for all clients and is used by SAP to deliver the data.</li>
<li>SWFVMD1 saves the data client-dependently (customizing) and should be used by customers to enter own settings or overwrite data delivered by SAP.</li>
</ul>
<p>Up to now, SWFVISU settings were not overwritten on the customer side. Only data that was newly delivered by SAP was added.</p>
<p><strong>Business Process Impact:</strong></p>
<p>If you have entered your own settings in SWFVISU or have adjusted delivered data, the appearance or response of individual work items in MyInbox may change.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Transfer your settings manually to SWFVMD1 before the upgrade or conversion.</p>
<ul>
<li>Call transaction SWFVISU.</li>
<li>Call transaction SWFVMD1 in a second window.</li>
<li>Transfer only your changed settings to SWFVMD1 and save them.</li>
</ul>
<p>To be on the safe side, you can export the entire content of SWFVISU to a Microsoft Excel file. This file serves only documentation purposes and cannot be imported automatically.</p>