{"Request": {"Number": "752440", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 651, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015719372017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=83A71C4D1EB18EB8A0C1FC724BFD5B78"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "752440"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.06.2005"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "XX-CSC-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "752440 - Overview about changes for ISS, PIS, COFINS; CSSL, IR, WHT"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>New laws in Brazil for taxes:</p> <UL><LI>ISS</LI></UL> <UL><LI>PIS</LI></UL> <UL><LI>COFINS</LI></UL> <UL><LI>CSSL</LI></UL> <UL><LI>IR</LI></UL> <UL><LI>WHT</LI></UL> <p><br />For a release- and support-package dependened implementation overview of legal change MP-135/LC116, see note 852302.<br /><br />************************************************************************<br />***&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; A T T E N T I O N&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;***<br />***&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;M A K E&#x00A0;&#x00A0; S U R E&#x00A0;&#x00A0; Y O U&#x00A0;&#x00A0; H A V E<br />***&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R E A D&#x00A0;&#x00A0; N O T E&#x00A0;&#x00A0;852302&#x00A0;&#x00A0;F I R S T !!!<br />***<br />***********************************************************************<br /><br />***&#x00A0;&#x00A0;This note will be updated with newest information about&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ***<br />***&#x00A0;&#x00A0;additional changes and notes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;***<br />************************************************************************<br />***&#x00A0;&#x00A0;01.12.2004&#x00A0;&#x00A0;Note 792798 - correction (release 4.0B only)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;***<br />***&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function Module KNA1_SINGLE_READ in Formula 320&#x00A0;&#x00A0;&#x00A0;&#x00A0; ***<br />***&#x00A0;&#x00A0;22.06.2005&#x00A0;&#x00A0;Note 852302 added; overview of required<br />***&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;implementation steps depending on release and<br />***&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;support-package level from 4.0B upwards overview for ***<br />************************************************************************</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Brazil; MP135; LC116, ISS; PIS; COFINS; CSSL; DDIC, Data Dictionary,<br />withholding tax; WHT; IR</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note is only relevant for Brazil.<br /><br />Changes in Brazilian tax legeslation require calculation and posting<br />of taxes on transaction level. For withholding taxes, the<br />calculation is required on item level.<br />For the functionality, new dictionary objects are necessary.<br /></p> <b>Prerequisites for release 4.0B:</b><br /> <p>Support package 83.<br />The functionality is deliverd with support package 84.<br /></p> <b>Prerequisites for release 4.5B:</b><br /> <p>Support package 61.<br />The functionality is deliverd with support package 62.<br /></p> <b>Prerequisites for release 4.6B:</b><br /> <p>Support package 56.<br />The functionality is deliverd with support package 57.<br /><br />Because of the high complexity and volume of the change, the<br />installation via support package is recommended.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note includes all relevant notes and steps to implement the<br />solution to the legal change of service taxes.<br /><br />Comment: Due to inter-dependencies of objects handled in the steps 2 to<br />5, the activation of certain objects may not be possible (--&gt; return<br />code '8'). In this case, continue the implementation until step 5 and<br />repeat the activation for the transports/objects, which were not<br />completed successfully.<br /><br /></p> <b>Implement for release 4.0B:</b><br /> <p><br />1. Make sure, support package 83 is implemented.<br />2. Import the file with new DDIC objects as described in note 774936.<br />3. Change DDIC objects as described in note 774873.<br />4. Import the file with new source code as described in note 775078.<br />5. Implement the source-code changes described in note 775079.<br />6. Follow the Customizing guide in Note 747607 for classic tax<br />&#x00A0;&#x00A0; calculation.<br /><br /></p> <b>Implement for release 4.5B:</b><br /> <p><br />1. Make sure, support package 61 is implemented.<br />2. Import the file with new DDIC objects as described in note 774936.<br />3. Change DDIC objects as described in note 774873.<br />4. Import the file with new source code as described in note 775078.<br />5. Implement the source-code changes described in note 775079.<br />6. Follow the Customizing guide in Note 747607 for classic tax<br />&#x00A0;&#x00A0; calculation.<br /><br /></p> <b>Implement for release 4.6B:</b><br /> <p><br />1. Make sure, support package 56 is implemented.<br />2. Import the file with new DDIC objects as described in note 777427.<br />3. Change DDIC objects as described in note 777428.<br />4. Import the file with new source code as described in note 777429.<br />5. Implement the source-code changes described in note 777430.<br />6. Follow the Customizing guide in Note 747607 for classic tax &#x00A0;&#x00A0; calculation.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-AP (Accounts Payable)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D043057)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (D034883)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "852302", "RefComponent": "XX-CSC-BR", "RefTitle": "Implementation Overview ISS 2004 - Legal Change MP135/LC116", "RefUrl": "/notes/852302"}, {"RefNumber": "802518", "RefComponent": "XX-CSC-BR", "RefTitle": "Adjustment of objects for ISS, PIS, COFINS, CSSL, WHT", "RefUrl": "/notes/802518"}, {"RefNumber": "792798", "RefComponent": "FI-AR-AR", "RefTitle": "Function Module KNA1_SINGLE_READ in Formula 320 - Brazil", "RefUrl": "/notes/792798"}, {"RefNumber": "777430", "RefComponent": "XX-CSC-BR", "RefTitle": "Modified Objects (excluding DDIC) for ISS, PIS, COFINS; CSLL", "RefUrl": "/notes/777430"}, {"RefNumber": "777429", "RefComponent": "XX-CSC-BR", "RefTitle": "New Objects (excluding DDIC) for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/777429"}, {"RefNumber": "777428", "RefComponent": "XX-CSC-BR", "RefTitle": "Modified DDIC Objects for ISS, PIS, COFINS; CSLL", "RefUrl": "/notes/777428"}, {"RefNumber": "777427", "RefComponent": "XX-CSC-BR", "RefTitle": "New DDIC Objects for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/777427"}, {"RefNumber": "775079", "RefComponent": "XX-CSC-BR", "RefTitle": "Modified Objects (excluding DDIC) for ISS, PIS, COFINS; CSLL", "RefUrl": "/notes/775079"}, {"RefNumber": "775078", "RefComponent": "XX-CSC-BR", "RefTitle": "New Objects (excluding DDIC) for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/775078"}, {"RefNumber": "774936", "RefComponent": "XX-CSC-BR", "RefTitle": "New DDIC Objects for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/774936"}, {"RefNumber": "774873", "RefComponent": "XX-CSC-BR", "RefTitle": "Modified DDIC Objects for ISS, PIS, COFINS; CSLL", "RefUrl": "/notes/774873"}, {"RefNumber": "747607", "RefComponent": "XX-CSC-BR", "RefTitle": "Basic Customizing for MP135 and ISS legal change Brazil", "RefUrl": "/notes/747607"}, {"RefNumber": "747323", "RefComponent": "XX-CSC-BR", "RefTitle": "Wrong ICMS Value After MP-135 Changes (Usage=\"Consumption\")", "RefUrl": "/notes/747323"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "747607", "RefComponent": "XX-CSC-BR", "RefTitle": "Basic Customizing for MP135 and ISS legal change Brazil", "RefUrl": "/notes/747607 "}, {"RefNumber": "777429", "RefComponent": "XX-CSC-BR", "RefTitle": "New Objects (excluding DDIC) for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/777429 "}, {"RefNumber": "852302", "RefComponent": "XX-CSC-BR", "RefTitle": "Implementation Overview ISS 2004 - Legal Change MP135/LC116", "RefUrl": "/notes/852302 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "774936", "RefComponent": "XX-CSC-BR", "RefTitle": "New DDIC Objects for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/774936 "}, {"RefNumber": "747323", "RefComponent": "XX-CSC-BR", "RefTitle": "Wrong ICMS Value After MP-135 Changes (Usage=\"Consumption\")", "RefUrl": "/notes/747323 "}, {"RefNumber": "775079", "RefComponent": "XX-CSC-BR", "RefTitle": "Modified Objects (excluding DDIC) for ISS, PIS, COFINS; CSLL", "RefUrl": "/notes/775079 "}, {"RefNumber": "774873", "RefComponent": "XX-CSC-BR", "RefTitle": "Modified DDIC Objects for ISS, PIS, COFINS; CSLL", "RefUrl": "/notes/774873 "}, {"RefNumber": "775078", "RefComponent": "XX-CSC-BR", "RefTitle": "New Objects (excluding DDIC) for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/775078 "}, {"RefNumber": "777427", "RefComponent": "XX-CSC-BR", "RefTitle": "New DDIC Objects for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/777427 "}, {"RefNumber": "777428", "RefComponent": "XX-CSC-BR", "RefTitle": "Modified DDIC Objects for ISS, PIS, COFINS; CSLL", "RefUrl": "/notes/777428 "}, {"RefNumber": "777430", "RefComponent": "XX-CSC-BR", "RefTitle": "Modified Objects (excluding DDIC) for ISS, PIS, COFINS; CSLL", "RefUrl": "/notes/777430 "}, {"RefNumber": "802518", "RefComponent": "XX-CSC-BR", "RefTitle": "Adjustment of objects for ISS, PIS, COFINS, CSSL, WHT", "RefUrl": "/notes/802518 "}, {"RefNumber": "792798", "RefComponent": "FI-AR-AR", "RefTitle": "Function Module KNA1_SINGLE_READ in Formula 320 - Brazil", "RefUrl": "/notes/792798 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B62", "URL": "/supportpackage/SAPKH45B62"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "747323", "RefTitle": "Wrong ICMS Value After MP-135 Changes (Usage=\"Consumption\")", "RefUrl": "/notes/0000747323"}]}}}}}