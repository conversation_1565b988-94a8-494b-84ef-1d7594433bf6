{"guid": "00109B1315FA1EDBBF881894AFAB20FE", "sitemId": "SI10_FIN_GL", "sitemTitle": "S4TWL - Profit Center and Segment Reorganisation", "note": 3087195, "noteTitle": "3087195 - Profit center reorganization and segment reorganization", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The profit center and segment reorganization of ECC are not available in SAP S/4HANA. The business functions FIN_GL_REORG_1 and FIN_GL_REORG_SEG cannot be activated in SAP S/4. When you create a new reorganization plan, the system issues an error message.</p>\n<p>With Release SAP S/4HANA 2022, there is a new solution for profit center reorganization. This new solution does not have to be activated using a business function, and can be configured and used directly, instead. The new solution is part of the SAP S/4 product and does not require an additional license.</p>\n<p>With Release 2022, the new profit center reorganization solution does not yet include the full scope of functions of the reorganization solution in ECC.</p>\n<p>Segment reorganizations are not yet possible in SAP S/4.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization, segment reorganization, reorganization, organizational change</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><span>Due to the changed architecture of the finance applications in SAP S/4, the ECC profit center and segment reorganization solution cannot be supported. The mentioned business functions cannot be activated. As of Release 2022, please use the new profit center solution.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP plans to enhance the new solution for reorganizations. Due to the complexity of the solution, there will be a phased rollout starting with functions for profit center reorganization (as of OP2022).</p>\n<p>For information about the SAP Road Map, see the following link:</p>\n<p><a href=\"https://roadmaps.sap.com/board?PRODUCT=73554900100800000266&amp;range=CURRENT-LAST&amp;q=org#Q4%202020;INNO=6CAE8B28C5DB1ED799F409A6F94920C3\" target=\"_blank\">https://roadmaps.sap.com/board?PRODUCT=73554900100800000266&amp;range=CURRENT-LAST&amp;q=org#Q4%202020;INNO=6CAE8B28C5DB1ED799F409A6F94920C3</a></p>\n<p> </p>", "noteVersion": 2, "refer_note": [{"note": "1471153", "noteTitle": "1471153 - Composite note for profit center and FM reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note is a composite SAP Note. Under \"Related notes\", you will find all relevant SAP Notes for the profit center and Funds Management reorganization (reassignment with FI-GL (new) in Funds Management). We have used the following nomenclature in the short text for the purpose of differentiation:</p>\n<ul>\n<li>PRCTR: Profit Center reorganization</li>\n</ul>\n<ul>\n<li>FM: Funds Management reorganization</li>\n</ul>\n<ul>\n<li>No entry: Both areas</li>\n</ul>\n<ul>\n<li>SEG: Some notes get this additional prefix if they are also relevant for Segment reorganization.</li>\n</ul>\n<p><br/>Important information:<br/>Contact your SAP Account Executive to find out whether there are additional license fees for the use of profit center reorganization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Reorganization, profit center, profit center reorganization, Funds Management, reassignment with FI-GL (new)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have activated the business function FIN_GL_REORG_1 or PSM_FM_REASSIGN and you use profit center or Funds Management reorganization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Check the related notes.</p>\n<p>For problems with the implementation of SAP Notes, check whether the following SAP Note of SAP Basis exists in your system:</p>\n<p>1668882 - Note Assistant: Important notes for SAP_BASIS 730,731,740,750</p>\n<p>If you require support for the implementation, open an incident under the component BC-UPG-NA.</p>\n<p> </p></div>", "noteVersion": 7, "refer_note": [{"note": "1767501", "noteTitle": "1767501 - Error message KI 100 after profit center rorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>After you reassign cross-company code sales and distribution documents in a profit center reorganization, the system issues error message KI 100 with the \"profitability segment\" object type.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>KI100, VF01, VF11, reorg, intercompany, cross-company, Prctr, CO-PA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the reassignment, the company code is derived again in the profitability segment.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions from SAP Note 1427243.<br/>Then execute the function module RKE_GENERATE_INTERFACE_ACT for the<br/>affected operating concern so that the generated program RK2L&lt;operating concern&gt; is also adjusted.<br/><br/>These corrections only affect sales and distribution documents that are reassigned after you implement the corrections. The system still issues error messages for documents that have already been reorganized. These documents are not affected by the corrections. If a correction is required in this case as well, you must manually adjust the company code in the result object.</p></div>", "noteVersion": 1}, {"note": "1677539", "noteTitle": "1677539 - PRCTR reorganization: Internal clearing for sales orders", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In a cross-company code business process,<br/>the invoice receipt in the selling company code is performed by Electronic Data Interchange (EDI).<br/>Even though the sales and distribution documents are reorganized in the selling company code, the account assignment is made to the old profit center for the invoice receipt.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization, intercompany, IDOC, IV, F2, VF04, VF31<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Prerequisites:</p> <ol>1. In Customizing (transaction VOFC), a setting is made so the account assignment is to be transferred from the customer billing document during the invoice receipt.</ol> <ol>2. A customer billing document already exists (at least for a partial delivery).</ol> <ol>3. The reorganization of the relevant sales order item is performed after the creation of this customer billing document.<br/></ol> <p>Reason:<br/>The reorganization that has already been performed is not considered.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the Support Package or the advance correction.<br/></p></div>", "noteVersion": 3}, {"note": "1747900", "noteTitle": "1747900 - PRCTR: Runtime error ASSERTION_FAILED", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate or reassign one or more object lists in a reorganization plan, the runtime error ASSERTION_FAILED occurs in the method GET_PLAN_RESTRICTIONS_P of the class CL_FAGL_R_OBJLIST.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>GET_OBJECT_INFO, parameter passing, parameter transfer, reference</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1803526", "noteTitle": "1803526 - PRCTR: Incorrect bal. pstg for purch. orders w/ acct assgmt", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization for purchase orders with account assignment. This may lead to the following errors if the account assignment object is a cost center:<br/>1) The balance determination does not determine any amounts to be transferred if the quantity of goods received is the same as the invoice receipt quantity even though postings are already made fully or partially with the new profit center.<br/>2) The balance determination determines values that are too high.<br/>In both cases, the system does not check whether postings are already made fully or partially with the new profit center.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_POA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The purchase order is assigned to a cost center for which a time dependency is maintained for the assignment to the profit center.<br/>For this reason, postings to the new profit center may be made in the reorganization period before the account assignment change. This is not taken into account during the balance sheet transfer.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections. After you do this, during the balance sheet transfer for purchase orders with account assignment, the goods movements and invoices that were already posted with the new profit center are not reposted. In addition, the purchase order is processed despite the clearing of the quantity of goods received and the invoice receipt quantity.</p></div>", "noteVersion": 1}, {"note": "1639690", "noteTitle": "1639690 - PRCTR/SEG: Unjustified error FAGL_REORGANIZATION 532", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you reassign receivables and payables, the system issues error FAGL_REORGANIZATION 532 (\"Receivable/payable &amp;1 has the wrong status (&amp;2) for reassignment\") even though the object in the object list has a status lower than 30.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>New GL<br/>General Ledger Accounting (new)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Prerequisites: You must have implemented Note 1597693 manually or by Support Package.<br/><br/>The correction instructions contained a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1751367", "noteTitle": "1751367 - PRCTR/SEG: Reassigned amount is incomplete for AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have successfully reassigned receivables and payables. However, you notice that for certain receivables or payables, the system did not reassign the total amount even though the relevant receivable or payable was not yet cleared at the time of the account assignment change.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorg<br/>Profit center reorganization<br/>Segment reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>If a reorganization plan is already open in your system, reassign all balances for receivables and payables first, and then implement the correction instructions.<br/><br/>This SAP Note prevents the error from occurring in the future. To correct the amounts that have been reassigned incompletely, contact SAP Support.<br/></p></div>", "noteVersion": 1}, {"note": "1816152", "noteTitle": "1816152 - PRCTR/SEG: Dump GLT0 000 BALANCE_CCODE after reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The runtime error (dump) GLT 000 BALANCE_CCODE occurs if you execute a follow-on function (for example, clearing) for a receivable in which an account assignment change took place during a profit center reorganization or a segment reorganization.<br/>This affects receivables that were posted in SD by the invoicing.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FI-GL (New), Profit Ctr Reorganization and Segment Reports<br/>Segment reorganization<br/>Reorganization<br/>Reorganization<br/>Reorg<br/>SAPGLT0</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error in the reorganization.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.<br/><br/>This SAP Note prevents this problem from occurring in future reorganizations.<br/>If a document that had an account assignment change during an earlier profit center reorganization or segment reorganization caused this error, contact SAP Support.</p></div>", "noteVersion": 2}, {"note": "1696418", "noteTitle": "1696418 - PRCTR/SEG: Inconsistencies in reorganization of receivables", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have implemented SAP Note 1677834.<br/>However, data inconsistencies occur after you reassign or make transfer posting for receivables.</p> <ul><li>Subsequent processes (for example, the clearing) are not performed in all new profit centers and/or</li></ul> <ul><li>multiple transfer postings are made for amounts for a receivable and/or</li></ul> <ul><li>The system issues the following error message for some receivables: \"Receivable/payable &lt;(&gt;&amp;&lt;)&gt;1 has incorrect status 40 for being transferred\" (FAGL_REORGANIZATION 552)</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorg<br/>Profit center reorganization<br/>AR<br/>Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/><br/>Note the following:<br/>This note ensures that this problem does not occur either for future reassignments and transfer postings.<br/>Contact SAP Support for the correction of an existing inconsistency.<br/></p></div>", "noteVersion": 3}, {"note": "1794458", "noteTitle": "1794458 - PRCTR: No P&L account, new profit center still derived", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>For postings that do not go to a profit and loss account, you notice that the virtual time-dependency of the reorganization still applies, and the new profit center is derived and postings are made to it.<br/>This has no known effects for postings that do not affect Asset Accounting. However, if assets are reorganized, this may result in incorrect balance sheet transfers for Asset Accounting.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>K_COBL_CHECK; second level objects; balance</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.<br/><br/>This SAP Note prevents these problems from occurring in future reorganizations.<br/>However, it does not correct existing inconsistencies. For this purpose, contact SAP Support (component FI-GL-REO).</p></div>", "noteVersion": 1}, {"note": "3134230", "noteTitle": "3134230 - PRCTR/SEG: Memory overflow during generation of receivables and payables", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you generate receivables and payables, an endless loop may occur that leads to a runtime error due to a memory overflow. The error occurs in the method CL_FAGL_R_SPLIT_REORG: CHECK_ELI_REORG_SPL_WT_SPLINFO.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">Profit center reorganization<br/>Segment reorganization<br/>Reorg</p>\n<p>Error category: Resource shortage<br/>Runtime error: TSV_TABH_POOL_NO_ROLL_MEMORY, TSV_TNEW_PAGE_ALLOC_FAILED</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>During the simulation of the document split, large rounding differences occurred that cannot be expanded.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the corrections.<br/><br/><br/></p>", "noteVersion": 2}, {"note": "1987194", "noteTitle": "1987194 - ASSERTION_FAILED during object generation III.", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Generation of a certain object type results in Assertion Failed error in method  CL_FAGL_R_OBJLIST-&gt;GET_OBJECT_INFO_P. If the standard hierarchy is reduced by the user and then a certain object is referring to a missing node of the hierarchy (not a first level object), then it can result into an assertion violation during the object list generation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ASSERTION_FAILED, GET_OBJECT_INFO, GET_OBJECT_INFO_P, hierarchy version, object type</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this correction note.</p>\n<p>Example:</p>\n<p>Standard hierarchy:</p>\n<p>CC-FA-POA-AP,</p>\n<p>FA-POA-AP</p>\n<p>POA-AP</p>\n<p>AP</p>\n<p>Modified hierarchy (Fix assets - FA excluded from the hierarchy):</p>\n<p>CC-POA-AP</p>\n<p>POA-AP</p>\n<p>AP</p>\n<p>Assume a certain AP with a superior object POA which has a superior object FA. Following logic is applied:</p>\n<ul>\n<li>during generation of account payables (AP) this object is excluded from the first level AP objects</li>\n<li>during generation of purchase orders assigned (POA) respective POA is included in the plan as first level object because superious object FA is not in the hierarchy</li>\n<li>during ressignment of POA, the respective AP is generated and reassigned as second level object.</li>\n</ul>", "noteVersion": 5}, {"note": "1918511", "noteTitle": "1918511 - Settlement reversal is possible after PC reorganization II", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have reorganized CO object, where costs or work in process has been settled in previous period(s) before reorganization date. According note 1366671 this settlement must not be reversed. However when you do settlement reversal into current period, no error message stops this posting.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>profit center reorganization, settlement, reversal, FAGL_REORG_SEG 002<br/>FAGL_R_CHECK_SETTLEMENT_REVERS</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the advance code correction.</p></div>", "noteVersion": 1}, {"note": "2164215", "noteTitle": "2164215 - Change of profit center during profit center reorganization", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You execute a reorganization of the profit centers. During the reorganization, the profit center in sales and distribution documents must not be changed. This is ensured by the following: The field for profit centers on the account assignment screen is not ready for input. However, if you change the internal order there, the profit center is then redetermined from the master data of the internal order.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FAGL_R_SPL_OBJECT, FAGL_REORGANIZATION304, FAGL_REORGANIZATION, 304</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached program corrections. This correction ensures that, during profit center reorganization, the profit center in sales and distribution documents is not also changed automatically. For this, the fields for the order and WBS element are made not ready for input.</p>", "noteVersion": 2}, {"note": "1820945", "noteTitle": "1820945 - Reorganization incorrectly uses default account assignment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As part of the profit center reorganization (project based on the function provided with the business function FIN_GL_REORG_1), the system uses the account assignment from the constants during the reorganization of receivables and payables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You use General Ledger Accounting (new) and document splitting.<br/>Within document splitting, you have customized the fallback account assignment strategies for \"inheritance in case of uniqueness\" and the usage of constants.<br/>Furthermore, you use the function of the profit center reorganization that is activated with the business function FIN_GL_REORG_1 within a reorganization project.<br/><br/>The document to be reorganized was once assigned not using the rule-based split, but receives its general ledger characteristics in line items to be assigned using the activated Customizing fallback function \"inheritance of account assignments in case of uniqueness in document\".<br/>The reorganization cannot support this fallback logic. Due to a program error, these documents are now processed by the additional fallback case of the usage of a general constant. Since the account assignment of the constants may be different compared to each account assignment from the inheritance, the use of this second fallback function leads to inconsistencies. </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The reorganization must neither process the fallback function of the constants nor the use of the fallback function of the inheritance. Implement the attached correction instructions to avoid the use of the constants within the reorganization if this is customized in document splitting.</p></div>", "noteVersion": 4}, {"note": "1911623", "noteTitle": "1911623 - PRCTR/SEG: Dump when generating APAR / 566 (X)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the generation of receivables and payables, the system issues<br/>a dump in the method <br/>CL_FAGL_R_SPLIT_REORG:CHECK_AND_ELIM_ROUND_REORG_SPL.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566<br/>CL_FAGL_R_SPLIT_REORG:CHECK_AND_ELIM_ROUND_REORG_SPL<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><ul><li>During the reorganization, rounding differences are triggered. The reason is, for example: As a result of the different summarization due to the additional reorganization characteristics, the tax credit-side price differences are is not distributed in accordance with the original document split characteristics. The rounding differences can occur either at the level of the table FAGL_SPLINFO, in FAGL_SPLINFO_VAL, or in a combination of both. To distribute these rounding differences, the method CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL was created starting in SAP Note 1869309 and has been enhanced several times since.<br/>You can test any document using the report FAGL_R_APAR_SIM, as long as it has not been REASSIGNED yet.<br/><br/>Every FI document is enriched with reorganization characteristics (CL_FAGL_R_SPLIT_REORG: SIMADD_GET_SPLITINFO_FOR_DOC_P) during generation in the simulation of the document split. These characteristics serve to determine the higher-level object in the hierarchy. Both the simulation (see SAP Note) and the subsequent comparison with the original values in FAGL_SPLINFO or FAGL_SPLINFO_VAL can fail. In these cases, the receivable or payable is added to the reorganization plan as a level 1 object.</li></ul> <ul><li>For more information about the assignment of objects to level 1 or subordinate levels, refer to the SAP Help portal under:<br/>\"http://help.sap.com\" and search for \"Profit Center Reorganization\"<br/>Profit Center Reorganization (see attachment SAP_HELP_PORTAL)</li></ul> <ul><ul><li>Prerequisites and Customizing Settings for the Reorganization<br/>..Derivation Hierarchy</li></ul></ul> <ul><ul><li>Specific Functions for Objects that Can Be Reorganized<br/>..Reorganization of WIP Objects and SD Objects</li></ul></ul> <ul><li>You have implemented SAP Note 1907741 and the related previous notes.</li></ul> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the following corrections.</p></div>", "noteVersion": 5}, {"note": "1619269", "noteTitle": "1619269 - Transf.posting: Security check, no account or CoCd specified", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The balances to be transferred are determined by the object type implementations and are transferred to the transfer posting program.<br/><br/>However, if the balance is transferred without an account and/or company code, the balance to be transferred is saved, but cannot be transferred.<br/><br/>Currently, the system does not check before the balance is saved if the account and company code have been transferred.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>New General Ledger Accounting<br/>NewGL<br/>Reassignment</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1571329", "noteTitle": "1571329 - Invalid account assignment combination is allowed", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>At first, the system allows an invalid combination of an old and a new account assignment. During follow-on processing, however, the system then rejects this combination after all. This correction prevents the system from allowing an invalid account assignment combination.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Default account assignment, account assignment proposal<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1870718", "noteTitle": "1870718 - Settlement reversal is possible after PC reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have reorganized CO object, where costs or work in process has been settled in previous period(s) before reorganization date. According note 1366671 this settlement must not be reversed. However when you do settlement reversal into current period, no error message stops this posting.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>profit center reorganization, settlement, reversal, FAGL_REORG_SEG 002<br/>FAGL_R_CHECK_SETTLEMENT_REVERS,</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the advance code correction.<br/><br/><b>Please also implement follow-up note 1918511!</b></p></div>", "noteVersion": 3}, {"note": "1484364", "noteTitle": "1484364 - Wrong processing on IDs with namespace", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When selecting the specific restrictions tab in plan detail application, there is a dump.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization of profit center, reorganization of FM documents, specific restrictions.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The field name has a namespace. The IDs which are generated out of field name have wrong format.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions. The generated IDs now have the right name.</p></div>", "noteVersion": 1}, {"note": "1837340", "noteTitle": "1837340 - PRCTR: Check of transfer of billing documents", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As a prerequisite for the complete processing of documents from SD, the chapter \"Reorganization of WIP and SD Objects\" in the Help Portal informs you of the following:<br/>All billing documents must be transferred to financial accounting before the account assignment change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Reorg<br/>Reassignment of SD orders<br/>FAGL_REORGANIZATION 343<br/>Transfer billing documents<br/>Billing document transfer block</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This was previously not queried in the program.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>If the SD order, contract, and so on were already billed, the system will check the transfer of the billing document in future.<br/>Implement the attached source code.<br/><br/>In future, the system will issue error FAGL_REORGANIZATION 343.<br/><br/>In the case of an error, release the billing document to financial accounting and restart the account assignment change of the SO/SOI objects.<br/><br/></p></div>", "noteVersion": 2}, {"note": "1849101", "noteTitle": "1849101 - PRCTR: Status during selection of CO objects", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the selection of CO objects in the profit center reorganization, fewer objects may be selected than expected. This may affect the following object types:<br/>- Orders of order categories 1 - 40 (O01-O40)<br/>- WBS elements (WBS)<br/>- Network activities (NWA)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the selection, the system takes into account status fields in the object tables, for example, AUFK-LOEKZ or AUFK-PHAS3, that may not be filled correctly by the relevant application component (see, for example, SAP Note 498387).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the SAP Note or import the relevant Support Package. After you do this, only the table JEST (as described in SAP Note 1762222) is still relevant for deciding whether the objects specified above are open or closed with regard to the reorganization.</p></div>", "noteVersion": 2}, {"note": "1759668", "noteTitle": "1759668 - PRCTR: Status when selecting sales document items", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The selection of sales document items (object types SO and SOI) does not meet your requirements with regard to the status.<br/>According to the current logic, items are not selected if the field for the overall processing status (VBUP-GBSTA) has the value \"Completely processed\" ('C'). Note that you can define exceptions in Customizing under<br/>\"Reorganization -&gt;<br/>Basic Settings -&gt;<br/>Specify Periods for Sales Document Selection\"<br/><br/><br/>However, you require a selection taking into account the system status table JEST.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/><br/>After you implement this SAP Note, the current status logic only applies for sales document items without a CO object number (VBAP-OBJNR is initial).<br/><br/>Instead, the following applies for sales document items with an OBJNR that is not initial:<br/>Sales document items are not selected if they have the status 'Deletion indicator' (I0013), 'Closed' (I0046) or 'Deletion Flag' (I0076) in system status management (table JEST).</p></div>", "noteVersion": 1}, {"note": "1485204", "noteTitle": "1485204 - PRCTR: FB01 posts document after msg. KC 030 despite error", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p><PERSON><PERSON><PERSON> posts a document after the system issues message KC 030 even though this message informs you of an error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center, reorganization, FAGL_REORGANIZATION008, REORGANIZATION 008, FB01, KC030, KC 030</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>A CO object (for example, an internal order) participates in a profit center reorganization. However, in the reorganization plan, the new profit center has not been determined yet for the CO object (the object is still in the status 20 \"Approved for Further Processing\" at least).<br/>In this case, a posting to the CO object must be refused with the message FAGL_REORGANIZATION 008 in the reorganization period.<br/>In transaction FB01, the document is still posted after the system issues message KC 030.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>After you implement these corrections, the system issues error message FAGL_REORGANIZATION 008.<br/>The document can no longer be posted.</p></div>", "noteVersion": 1}, {"note": "1748695", "noteTitle": "1748695 - PRCTR: Message FAGL_REORGANIZATION 556 during AP/AR generation", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables and payables, the following error occurs: FAGL_REORGANIZATION 556<br/>This error also occurs after regeneration and reassignment.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p><br/>Invoice reference<br/>Profit center reorganization<br/>Reorganization<br/>Reorg</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 1}, {"note": "1833462", "noteTitle": "1833462 - PRCTR/SEG: MESSAGE_TYPE_X during generation of AP/AR (4)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The error messsage type X occurs during the generation of AP/AR within the PCA Reorganization Tool.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profitcenter-Reorg, Segment-Reorg, Reorganisation, Reorganization, Reorg, Receivables, Payables</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is a follow up note of the corrections 1825167. Ths message type X after grouping is caused by incorrect setting of X_LEAD for more than one memebers of the same group in case of partial payments document chain.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement his note or download the corresponding support pack.</p></div>", "noteVersion": 3}, {"note": "1858541", "noteTitle": "1858541 - PRCTR: Dump DBIF_RSQL_INVALID_RSQL for SO acct assgmt change", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the account assignment change of sales document items in the profit center reorganization, a dump DBIF_RSQL_INVALID_RSQL occurs in the method CL_FAGL_R_OBJ_TYPE_001_SO-&gt;LOG_SUBSEQUENT_DOCUMENTS.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In document flows with very many billing documents for a sales document item (for example, for scheduling agreements), the database management system can no longer process an SQL request due to its size.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions or import the Support Package.</p></div>", "noteVersion": 1}, {"note": "1650324", "noteTitle": "1650324 - PRCTR/SEG: Generation terminates: Unstable grouping AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The generation of the object types receivables and payables terminates with a runtime error because invoice-related parts of the receivables or payables are not grouped stably.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit Center reorganization<br/>Segment reorganization<br/>Reorg<br/>MESSAGE_TYPE_X <br/>CL_FAGL_R_OBJLIST=============CP</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Payables or receivables with invoice reference (for example, partial payments or residual items) are grouped together with the original payables or receivables using a grouping number to ensure that their account assignment is changed to the same target account assignment (profit center/segment).<br/><br/>However, only the parts of the items that are assigned to the same source account assignment (profit center (old) or segment (old)) can be grouped.<br/>For this purpose, the grouping number must also be stable for the repeated generation.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1810605", "noteTitle": "1810605 - General information about profit center reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to perform a profit center reorganization and you want to know what you need to take into account.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization; reorg; business function FIN_GL_REORG_1; PC Reorg</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>1. The functions for the profit center reorganization as part of General Ledger Accounting (new) are a component of the SAP standard product SAP Landscape Transformation (SAP LT). Therefore, you need a license for this product to use the functions. Contact your account manager regarding the purchase of a license. For more information about the license purchase as well as installation and using SAP LT in connection with its solution for profit center reorganization, see SAP Note 1534197.</ol><ol>2. Note that a profit center reorganization is a complex project. For a project of this type to be successful, careful planning (with the support of an advisor, if necessary) is required.</ol><ol>3. Extensive tests in a suitable test environment are an essential part of a reorganization project. If possible, your test system should represent a copy of your production system with a dataset that is complete and as up-to-date as possible.</ol><ol>4. Both your test system and your production system should have the most current Support Package level. Before you start your project, ensure that all of the systems relevant for the profit center reorganization contain all of the SAP Notes that are required for the profit center reorganization (these SAP Notes are listed in SAP Note 1471153). To execute the profit center reorganization, you require either a portal or the SAP NetWeaver Business Client.</ol><ol>5. The execution of the profit center reorganization in your production system is permitted only after the successful completion of the reorganization in your test system. In the case of problems during the reorganization in your production system, note that SAP Support will request access to your test system.</ol><ol>6. If you encounter problems during the test of the profit center reorganization, open a customer message under the component FI-GL-REO*. Note that our support colleagues require both a standard SAP R/3 connection (see SAP Note 35010) and an HTTP connection (see SAP Note 592085) to your test system and, if necessary, also to your production system to process this message.</ol><ol>7. See the information in SAP Note 891144 (\"General Ledger Accounting (new)/document splitting: Risks of subsequent changes\"). Subsequent changes can lead to inconsistencies and errors for the FI objects during reorganization.</ol></div>", "noteVersion": 4}, {"note": "1810142", "noteTitle": "1810142 - PRCTR: Correction report for Fagl_splinfo", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This SAP Note provides an analysis/correction report. This report must not be used in the update run without prior consultation with SAP.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Purchase orders with account assignment, purchase orders without account assignment, profit center reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>This report must only be executed after consulting with SAP. Also, SAP Note 1808494 MUST be implemented in your system.</p></div>", "noteVersion": 4}, {"note": "2130322", "noteTitle": "2130322 - Not possible to change the Profit Center in a Cost Center due to Error Message FAGL_REORGANIZATION 601", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When changing the Profit Center of a Cost Center the system raises an Error Message FAGL_REORGANIZATION 601 in Controlling.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Environment\">Environment</h3><div class=\"longtext\">\n<ul>\n<li>Controlling (CO)</li>\n<li>SAP R/3</li>\n<li>SAP R/3 Enterprise 4.7</li>\n<li>SAP ERP Central Component</li>\n<li>SAP ERP</li>\n<li>SAP enhancement package for SAP ERP</li>\n<li>SAP enhancement package for SAP ERP, version for SAP HANA</li>\n</ul>\n</div><h3 class=\"section\" data-toc-skip=\"\" id=\"Reproducing the Issue\">Reproducing the Issue</h3><ol>\n<li>Execute the Transaction KS02</li>\n<li>and try to Change the Profit Center to another</li>\n<li>Save the following message is displayed: FAGL_REORGANIZATION601 Error : \"Profit center assignment in cost center XXX cannot be changed\"</li>\n</ol><h3 class=\"section\" data-toc-skip=\"\" id=\"Cause\">Cause</h3><p>Error or Bug</p>\n<p>Profit Center period incorrect</p>\n<p>No suitable Reorganisation Plan</p>\n<p>According to the long text of the error :</p>\n<p>The assignment of the profit center in cost center XXX must not be changed.</p>\n<p>Since the change in profit center assignment from profit center XXX to profit center XXX on the key dates not contained in a reorganization plan for profit centers, a change in cost center is not permitted either.</p>\n<p>It is important to note that changing message 601 into a warning message can lead to inconsistencies in the system. SAP cannot accept any responsibility for such inconsistencies. We recommend that the message is only changed from an error message to a warning message in special circumstances. If you intend to make this change, first contact SAP Development Support.</p>\n<p>Procedure for System Administration</p>\n<p>You can specify when this system message is issued in accordance with your requirements. To do this, go to Customizing and choose Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Tools -&gt; Change Message Control). You can find the work area and message number in the technical information on this message.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Resolution\">Resolution</h3><ol>\n<li>Check if Transaction OKEG states that Profit Center is a \"period time dependant field\", if yes. It means that change the Profit Center is only possible starting from the first day of a period (e.g.: period = calendar month). You can change the profit center at 1<sup>st</sup> August but not at 13<sup>th</sup> August, if not...</li>\n<li>Try to create a suitable reorganization plan that takes care of related balances and processes that the Profit Center receives from the Cost Center and that are reposted and reassigned. If after that you decide to use the Profit Center reorganization solution and you want to change the assignment to the Profit Center in a Cost Center and anyway the system raise the error message, please ensure that <strong>SAP *Note: </strong><a href=\"/notes/1684679\" target=\"_blank\">1684679</a> is already implemented in your system.</li>\n<li>But if you have a Cost Center without transaction data, without dependent objects and with a single time slot, and now now want to change the Profit Center in the entire time slot, which must be allowed and still not allowed, then please check if the if <strong>SAP *Note: </strong><a href=\"/notes/1684679\" target=\"_blank\">1684679 </a>correction is already implemented in your system.  </li>\n</ol><h3 class=\"section\" data-toc-skip=\"\" id=\"See Also\">See Also</h3><p>If you are planning to <span>convert</span> the<span> Error Message 601</span> into <em><strong>warning message</strong></em>, notice that it may cause inconsistencies in the system, for further details please read <strong>SAP *Notes: </strong><a href=\"/notes/1881515\" target=\"_blank\">1881515</a> ,<a href=\"/notes/1900735\" target=\"_blank\">1900735</a>.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Keywords\">Keywords</h3><p>Cost Center, Profit Center, Cost Centre, Profit Centre, Centre, KS02, FAGL_REORGANIZATION601, 601, Error Message</p>", "noteVersion": 6}, {"note": "1745135", "noteTitle": "1745135 - PRCTR: multiple improvements in obj. type POA", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have created a reorganization plan in you company. You tried to reassign purchase orders with account assignment (POA). It takes too long or there is an error ITAB_DUPLICATE_KEY for multiple superior objects.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_POA<br/>purchase orders with account assignment - object list<br/>ITAB_DUPLICATE_KEY</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Performance, Program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the provided correction instructions.</p></div>", "noteVersion": 1}, {"note": "1597693", "noteTitle": "1597693 - PRCTR/SEG: AP/AR: Various PRCTR/SEG for same account assgnmt", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>A receivable or payable that has derived different profit centers (in the case of a profit center reorganization) or segments (in the case of a segment reorganization) from the same account assignment must be reorganized.<br/></p> <b>Example:</b><br/> <p>Payable using EUR 130 with the following account assignments in the general ledger view:<br/><br/>Order       Profit center     Amount<br/>  123          PCA          EUR 20.00<br/>  123          PCB          EUR 60.00<br/>  123          PCC          EUR 40.00<br/>              PCA           EUR 10.00<br/><br/>Profit center in order 123:  PCB<br/><br/>Only the parts of the payable (in this case, EUR 60.00) for which the same profit center as that in the master record of the higher-level account assignment (in this case, order 123) has been derived (in this case, profit center PCB) should appear in the reorganization plan at subordinate hierarchy level for order 123.<br/>The other parts should be displayed at first hierarchy level.<br/>In this case:  EUR 30.00 for profit center PCA and EUR 40.00 for profit center PCC<br/><br/>The example can be applied in the same way for other account assignments in the profit center reorganization.<br/>In the case of a segment reorganization, various segments have been derived from the same profit center. In this case, all parts of the payable that have derived the segment that is entered in the profit center master record should appear at subordinate hierarchy level for the profit center.<br/>The other parts should be displayed at first hierarchy level.<br/><br/>The response is mapped incorrectly in the current implementation.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>NewGL<br/>New General Ledger Accounting</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions or import the relevant Support Package.<br/><br/><b><b>Note the following:</b></b><br/>This note or the relevant Support Package must not be imported during a reorganization.<br/>Complete all reorganization plans before you implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1793304", "noteTitle": "1793304 - Performance: Generation not performed in parallel processing", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The generation of reorganization objects is no longer distributed to several jobs.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Parallel processing, background job, job, dispatch, proceed</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import the relevant Support Package or implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1766461", "noteTitle": "1766461 - PRCTR: Sales order stock and project stock with MAT with ML", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You reorganize materials as part of a profit center reorganization plan. After the account assignment change and the balance determination you notice that the balance sheet value is incorrect and that it does not contain the sales order stock or the project stock.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_MAT<br/>Material stock<br/>Sales order stock<br/>Project stock<br/>EBEW<br/>QBEW</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 2}, {"note": "1747717", "noteTitle": "1747717 - PRCTR/SEG: Short dump when generating AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The generation of receivables and payables terminates with the short dump MESSAGE_TYPE_X (message FAGL_REORGANIZATION 113) in the method SET_OBJECTS_P of the class CL_FAGL_R_OBJLIST.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJLIST=============CP<br/>FAGL_REORGANIZATION113, FAGL_REORGANIZATION 113<br/>FAGL_REORGANIZATION<br/>113<br/>CL_FAGL_R_OBJLIST=============CM00H<br/>GRP<br/>XGRP_LEAD<br/>Reorganization<br/>Reorg<br/>Profit center reorganization<br/>Segment reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the generation, the simulation of the original invoice and of at least two invoice-related items failed.<br/>Since the items are all connected by an invoice reference, they must be grouped together when they are transferred to the reorganization tool.<br/>As of the second invoice-related item in this case, the system did not ensure that the same grouping number is used as for the item from the original invoice due to a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 2}, {"note": "1564633", "noteTitle": "1564633 - Diff profit center in profitability segment is overwritten", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you reassign a CO object, the profit center in the profitability segment is changed even if it is different from the profit center of the CO object.<br/><br/>Example: An internal order with the profit center PCA has a settlement rule to a profitability segment with the profit center PCX.  When you reassign the internal order from PCA to PCB, the system creates a new settlement rule as of the reorganization period. The profitability segment of this new settlement rule has the profit center PCB, even though you may not want a change from PCX to PCB in CO-PA.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Perform the steps described in the manual advance tasks and implement the attached correction instructions. The system then checks whether the profit center of the profitability segment is the same as that of the CO object before changing the settlement rule.  Otherwise, the settlement rule is not changed and the system issues a relevant message informing the user of the situation.  In the example above, the internal order would be reassigned to PCB, but the settlement rule to the profitability segment with PCX would remain unchanged.<br/><br/></p></div>", "noteVersion": 2}, {"note": "2953916", "noteTitle": "2953916 - PRCTR/SEG: FAGL_REORGANIZATION 514/515 when generating AP/AR/GLOI", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you generate receivables, payables, or open G/L account items, the system issues message FAGL_REORGANIZATION 514 or 515.</p>\n<p>514: \"Simulation: Simulated doc. splitting for receivable/payable &amp;1 differs\"</p>\n<p>515: \"Simulation: Simulated doc. splitting for invoice-related doc. &amp;1 differs\"</p>\n<p>The open item is generated as an object of the first hierarchy level and is therefore not reorganized together with the higher-level object in the hierarchy.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">GET_SPLINFO_ORI <br/><br/>COMPARE_REORG_SPL_WITH_SPLINFO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The currency append table SPLINFO_VAL is sorted incorrectly.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 2}, {"note": "1852410", "noteTitle": "1852410 - PRCTR: Error FAGL_REORGANIZATION545 During Reassignment II", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables to sales and distribution documents, the system issues error message<br/>FAGL_REORGANIZATION545:<br/>\"Reassignment: Error in document splitting (sales and distribution document is in<br/>reorganization (FAGL_REORGANIZATION 008))\".<br/><br/>If the receivable has several sales and distribution document items with different account assignment (among other things AUBEL/AUPOS and KDAUF/KDPOS), the system issues error message FAGL_REORGANIZATION566:<br/>\"Reassignment: difference between PRCTR/SEGMENT in FAGL_SPLINFO and FAGL_R_SPL &amp;1\"<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorg<br/>Reorganization<br/>Object type AR<br/>Object type SO<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1794282", "noteTitle": "1794282 - PRCTR: Gener. AP/AR - Assign hierarchy for higher-level obj.", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate receivables and payables, parts of the document (for example, tax) are generated in the first hierarchy level instead of under a higher-level object (for example, sales order).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorganization<br/>Reorg<br/>Derivation hierarchy, hierarchy<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/>SAP Notes 1782671 and 1690702 are prerequisites.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement SAP Notes 1782671 and 1690702 first.<br/>Implement the attached correction instructions.</p></div>", "noteVersion": 6}, {"note": "1484365", "noteTitle": "1484365 - Add display button in the plan overview application", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>There is no display button in the Plan Overview. Although user can display the plan by clicking the link of plan id, it would be much helpful if the display button is available on the toolbar just as the change button.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization of profit center, reorganization of FM documents, plan overview, display button.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Display button is not required before.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions. Display button now appears on the toolbar.</p></div>", "noteVersion": 1}, {"note": "1660067", "noteTitle": "1660067 - Runtime error ASSERTION_FAILED: Update of dummy objects", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During generation or an account assignment change, the runtime error ASSERTION_FAILED occurs in the class CL_FAGL_R_OBJLIST.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Dummy object, FAGL_R_PL_COUNT, UPDATE_COUNT_P, FAGL_R_PROCEED.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections.</p></div>", "noteVersion": 2}, {"note": "1572725", "noteTitle": "1572725 - Identical restriction fields cause a dump", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the Customizing activity for determining restriction characteristics per reorganization object type, you have selected two identical fields for two different tables for an object type. The setting may lead to a dump if you want to display the specific restrictions for a plan.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>V_FAGL_R_OBJTRES, object type, TOOLTIP<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1586447", "noteTitle": "1586447 - PRCTR: some values are not filled in CHECK_CALLBACK_BUFF_P", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the method CL_FAGL_R_OBJ_TYPE_APAR-&gt;CHECK_CALLBACK_BUFF_P profit center and object close flag are not filled</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit Center Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions accordingly.</p></div>", "noteVersion": 2}, {"note": "1683456", "noteTitle": "1683456 - Objects not reassigned or transferred", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You perform a reassignment or transfer and notice that certain objects are not reassigned or transferred without a recognizable reason.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Partition ID, EV_END_ID</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 2}, {"note": "1513720", "noteTitle": "1513720 - Master data for segment reporting: Company code", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the \"Fill Master Data for Segment Reporting\" report. Even though you have selected multiple company codes, the system only displays the results from one company code.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Report \"Fill Master Data for Segment Reporting\"<br/>Segment reporting</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You use segment reporting for Asset Accounting.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections specified below in your system.</p></div>", "noteVersion": 1}, {"note": "1707832", "noteTitle": "1707832 - FAGL_ASSET_MASTERDATA_UPD: Various errors", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you execute the initialization report FAGL_ASSET_MASTERDATA_UPD to fill the \" Profit center\" and \"Segment\" fields for the first time, the following problems may occur:</p> <ol>1. When you execute the report in the background as a test run, a short dump occurs with the following key words:</ol> <ul><ul><li>GETWA_NOT_ASSIGNED</li></ul></ul> <ul><ul><li>SAPLKKBL</li></ul></ul> <ul><ul><li>LKKBLF01</li></ul></ul> <ul><ul><li>CHECKBOX_OUT</li></ul></ul> <ol>2. In the Schedule Manager Monitor (SCMO), the system does not display any messages from the application log.</ol> <ol>3. If errors occur during the update run, the relevant error messages are not displayed. Instead, the system issues message FAGL_REORGANIZATION 412 (\"Fixed asset &amp;1 is not changed because postings already exist\"), which is not applicable.</ol> <ol>4. The derivation is performed without taking into account the account assignment objects \"WBS element investment project\" (ANLA-POSNR) and \"Investment Order\"(ANLA-EAUFN).</ol> <ol>5. The system issues error AIST 009 even though one of the error messages AIST 016 or AIST 017, which can be deactivated, would be correct.</ol> <ol>6. The symptom described in SAP Note 1513720 (only one company code is processed) can also occur if SAP Note 1513720 was implemented by Support Package.</ol> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>AIST009, AIST 009, AIST016, AIST 016, AIST017, AIST 017, PRCTR, SEGMENT, XINVM<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections as described in the attached correction instructions.</p></div>", "noteVersion": 4}, {"note": "1808980", "noteTitle": "1808980 - PRCTR/SEG: Error message FAGL_REORGANIZATION 566", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system issues error message FAGL_REORGANIZATION 566 when you reassign receivables and payables.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to the error analysis.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>This SAP Note enables SAP Support to analyze and solve the cause of the error.</p></div>", "noteVersion": 3}, {"note": "1481296", "noteTitle": "1481296 - PRCTR: Acct assignment change for POs: Unneccessary warnings", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you carry out an account assignment change for purchase orders (POs) (object types PO and POA), the system issues unnecessary warning messages.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization, reorganization, PO, POA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Cause: There is a program error.<br/>Prerequisites: You have activated the business function FIN_GL_REORG_1.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1774057", "noteTitle": "1774057 - PRCTR/SEG: Error message FAGL_REORGANIZATION 535", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables or payables, the system issues error FAGL_REORGANIZATION 535.<br/><br/>This happens for documents, where several items have an invoice reference, and the simulation with enhanced document splitting characteristics failed for at least one of the invoice references during the generation.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorg<br/>Reorganization<br/>Profit center reorganization<br/>Segment reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.<br/><br/>However, the corrections prevent this error in the future only. To correct errors that already occurred, contact SAP Support.</p></div>", "noteVersion": 2}, {"note": "1702346", "noteTitle": "1702346 - FAGL_ASSET_MASTERDATA_UPD generates transfer postings", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute the initialization report FAGL_ASSET_MASTERDATA_UPD to fill the \"Segment\" and \"Profit center\" fields for the first time in the asset master record. In the process, transfer documents are then generated or the system issues error messages from the transfer posting.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Segment reporting, PRCTR, SEGMENT, FIN_GL_REORG_1, XZORG<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error, which may occur if you have already activated the profit center reorganization, the segment reorganization or the time-independent management of organizational units when starting the report.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections as described in the attached correction instructions.<br/>The system no longer generates any transfer postings when changes that were caused by the report FAGL_ASSET_MASTERDATA_UPD are made in the master record.<br/></p></div>", "noteVersion": 1}, {"note": "1823868", "noteTitle": "1823868 - Reorganization: selection of asset", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The dump CX_SY_DYNAMIC_OSQL_SYNTAX, SAPSQL_WHERE_PARENTHESE occurs when you want to generate the object list for assets.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Error CX_SY_DYNAMIC_OSQL_SYNTAX, SAPSQL_WHERE_PARENTHESES<br/>Reorganization of profit centers for assets</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You use the reorganization of profit centers.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections specified below in your system.</p></div>", "noteVersion": 2}, {"note": "1762222", "noteTitle": "1762222 - PRCTR: Selection of CO objects using status", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the selection of CO objects in the profit center reorganization, the system selects fewer objects than required. The following object types are affected:<br/>- WBS elements (WBS)<br/>- Network activities (NWA)<br/>- Orders of order categories 1 - 40 (O01-O40)<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>- The reorganization does not take the system status in table JEST into account.<br/>- The system fills the field AUFK-PHAS3 for certain order types only; see explanations in SAP Note 498387.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions or import the Support Package. After you do this, the system no longer selects objects of the object types specified above if one of the following system statuses is active in the JEST table:<br/>'Deletion indicator' (I0013),<br/> 'Closed' (I0046) or<br/>'Deletion Flag' (I0076)<br/><br/></p></div>", "noteVersion": 1}, {"note": "1942121", "noteTitle": "1942121 - PRCTR/SEG: Reassignment - FAGL_REORGANIZATION 535", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the reassignment of accounts payable/receivable (AP/AR), the system issues error message FAGL_REORGANIZATION 535.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566<br/>CL_FAGL_R_OBJ_TYPE_APAR: R_UPDATE_ACCIT_P</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In the case of documents with residual items, the new profit center is not assigned correctly for the documents to be cleared, particularly if a document to be cleared is an artificial level 1 object.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the following corrections.</p>\n<p>Then implement SAP Note 1910342.</p>", "noteVersion": 3}, {"note": "1820354", "noteTitle": "1820354 - PRCTR: Performance improvement for purchase orders II", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This SAP Note improves the runtime in the area of reassigning purchase orders. The improvement relates to the runtime behavior of both purchase orders with account assignment and purchase orders without account assignment.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>GET_SPLIT_ITEMS</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>None</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 2}, {"note": "1737132", "noteTitle": "1737132 - Job status display is unreliable", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute an account assignment change. You choose \"Update\". The system incorrectly displays the status \"Completed\" in the \"JobStatus\" column even though you can see that the account assignment change is not yet completed in transaction SM37.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization plan</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the following corrections.</p></div>", "noteVersion": 1}, {"note": "1731429", "noteTitle": "1731429 - Performance of WD applicatn for reorganizatn plan in detail", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The performance of the Web Dynpro application for the reorganization plan in detail is poor. In particular, it takes very long to start the application and update the object list view.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>TBTCP, full table scan, FAGL_R_PLAN_DETAIL</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 2}, {"note": "1652700", "noteTitle": "1652700 - PRCTR:Wrning FAGL_REORGANIZATION 505 w/ multiple acct assgmt", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate receivables or payables, the system issues the following warning message for items that have been posted to several account assignment objects (for example, cost center and order):<br/><br/>FAGL_REORGANIZATION 505: Object type not determined for payable/receivable &amp;1 &amp;2 &amp;3</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorg<br/>Reorganization<br/>Reorganization plan</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The program tries to determine the account assignment from which the profit center originates. The determination fails due to a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1592400", "noteTitle": "1592400 - Dump during regeneration for grouped object type", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You repeatedly generate objects of an object type that takes a grouping of objects into account - this is the case, for example, in the profit center reorganization for the object types \"receivable\" and \"payable\".<br/>In the plan, the system displays an error log with the information \"Internal session terminated with a runtime error MESSAGE_TYPE_X (see ST22)\".<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJLIST, XDEL, GRP, XGRP_LEAD<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1903491", "noteTitle": "1903491 - PRCTR/SEG: Dump when generating APAR / 566 (VII)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate receivables and payables, a dump occurs in method CL_FAGL_R_SPLIT_REORG:CHECK_AND_ELIM_ROUND_REORG_SPL.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566<br/>Rounding differences due to document split<br/>CL_FAGL_R_SPLIT_REORG:CHECK_AND_ELIM_ROUND_REORG_SPL<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the reorganization, rounding differences are triggered. The reason is, for example: As a result of the different summarization due to the additional reorganization characteristics, the tax is not distributed in accordance with the original document split characteristics.<br/><br/>You have implemented SAP Note 1885761 and the related previous notes.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the following corrections.</p></div>", "noteVersion": 5}, {"note": "2079684", "noteTitle": "2079684 - Error when implementing SAP Note 1878955 (SAP_FIN 617)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You implement SAP Note 1878955 in Release 617. After the implementation, you cannot activate the function module FAGL_R_CO_OBJ_GET_REORG_INFO. The system issues an error message stating that the FORM (subroutine) \"CHECK_OBJECT_IN_PLAN\" does not exist.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LFAGL_R_SERVICESU04</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The correction instructions for SAP Note 1878955 is missing an entry for Release 617 that adds the include lfagl_r_servicesf02 to the main program SAPLFAGL_R_SERVICES.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections. Alternatively, you can also import the Support Package SAPK-61704INSAPFIN, which contains the full program corrections.</p>", "noteVersion": 1}, {"note": "1739651", "noteTitle": "1739651 - PRCTR/SEG: Error message when generating AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system returns error messages when you generate receivables and payables.<br/><br/>The error messages occur during the simulation of receivables or payables and do not originate in the message classes (FAGL_REORGANIZATION or FAGL_REORG_SEG).<br/><br/>In addition, the receivables or payables that the error messages are issues for are not specified in any object list.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorg<br/>Profit center reorganization<br/>Segment reorganization<br/>AP<br/>AR<br/>FAGL_REORGANIZATION<br/>F5 808</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In order to find out from which objects a receivable or payable has derived their profit center or segment account assignment, the document splitting for the relevant document is simulated with additional attributes (for example, purchase order item, order, and so on) during the generation.<br/>During the simulation, several components of Financial Accounting are processed. Error may occur in the process.<br/><br/>Whenever an error occurs during simulation, the system should issue warning message FAGL_REORGANIZATION 503, 504 or 513 and the receivable or payable should be displayed at the first hierarchy level because the objects imparting the account assignment can no longer be determined.<br/><br/>Due to a program error, the system did not always behave as required.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1480563", "noteTitle": "1480563 - Error layout when displaying the transaction", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When displaying transaction out of the REORG process, the document/screen layout looks differently compared with direct display in backend system.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization of profit center, reorganization of FM documents, display document, FB03, RFFMCCF_DISP.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The transaction used a different layout when running in batch mode.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions. The transaction now is launched directly, instead of in batch mode.</p></div>", "noteVersion": 1}, {"note": "1783742", "noteTitle": "1783742 - Message FAGL_REORGANIZATION 123: Cut name of parameters", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In terms of profit center reorganization  you do object generation and<br/>via BAdI FAGL_R_GENERATE you might apply certain logic making object assignment already. Then a object validation is carried out and in case of an error the message FAGL_REORGANIZATION 123 with object name as a parameter is risen. In case the object contains leading zeros the name is cut and not displayed complete.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_REORGANIZATION 123, reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the advance code correction.</p></div>", "noteVersion": 2}, {"note": "1756916", "noteTitle": "1756916 - PRCTR: Incomplete selection of down payments", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You notice that not all down payments are contained in the object lists for the object types receivables and payables, even though these object types were marked as completely processed on the \"Reassign\" tab page.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_REORGANIZATION<br/>Reorganization<br/>Reorg<br/>Profit center reorganization<br/>UMSKS<br/>UMSKZ</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.<br/><br/>Generate the object types \"Receivables\" and \"Payables\" again and ensure that the delta mode is deactivated when you do this. Delete all entries in the table FAGL_R_OI_TRACK0 for your reorganization plan.</p></div>", "noteVersion": 1}, {"note": "1684679", "noteTitle": "1684679 - Reorg: Profit center in cost center cannot be changed", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the profit center reorganization solution.<br/>You want to change the assignment to the profit center in a cost center. You receive the incorrect error message FAGL_REORGANIZATION 601 (Profit center assignment in cost center &amp;1 cannot be changed).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_REORGANIZATION 601, KS02</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/>You have a cost center without transaction data, without dependent objects and with a single time slot. If you now want to change the profit center in the entire time slot, this must be allowed.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1674639", "noteTitle": "1674639 - FAGL-REORG: Couldn't display hierarchy correctly", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Sometimes the hierarchy structure displayed in object lists table of reorganization plan page is incorrect.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Hierarchy, Object Lists, Reassignment.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instruction.<br/></p></div>", "noteVersion": 2}, {"note": "1780066", "noteTitle": "1780066 - PRCTR: Additional fields for purchase order object list", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the reorganization plan for the profit center, you open the object list for purchase orders with account assignment or purchase orders without account assignment. In Customizing, you selected additional display fields. The fields are displayed, but without any contents.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_POA<br/>CL_FAGL_R_OBJ_TYPE_001_PO</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 1}, {"note": "1738491", "noteTitle": "1738491 - SEG: Failed split sim. leads to objects class. as 1st level", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You are using the Segment Reorganization functionality. You are generating the object list for payable or receivable objects. You receive the following message:<br/>Document was not simulated (FAGL_REORGANIZATION503)<br/>The payable/receivable item for which the message is issued was properly split when it was posted and the General Ledger view of the document contains a profit center.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Segment reorganization, Failed split simulation, 1st level objects</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Apply the correction instructions in this note.</p></div>", "noteVersion": 3}, {"note": "1785239", "noteTitle": "1785239 - PRCTR: Acct assgmt change AR/AP - missing \"Segment\" feature", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>After the account assignment change, the \"Segment\" account assignment feature is missing in the table FAGL_SPLINFO. This results in subsequent problems in the further processing of the documents.<br/>This occurs only if the mechanism introduced in SAP Note 1663555 is used for locating and changing the document splitting information.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorganization<br/>Reorg<br/>FAGL_SPLINFO<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1599168", "noteTitle": "1599168 - PRCTR/SEG: Message FAGL_REORGANIZATION 544", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Receivables or payables can have invoice-related items (such as partial payments and residual items).<br/>Normally, all invoice-related items should be reorganized together.  This is checked before a receivable or payable is reassigned and, if required, the system issues error message FAGL_REORGANIZATION 544.<br/><br/>When you generate the object lists, the system simulates document splitting with extended attributes for each payable or receivable and its invoice-related items. The simulation of some invoice-related items may fail.  The system displays these as objects of the first hierarchy level in the reorganization plan, and the other items as subordinate objects.<br/><br/>In this case, the payable or receivable is not reassigned together with all its invoice-related items.<br/>The system should not issue error message FAGL_REORGANIZATION 544 in this case.<br/>However, this message is currently issued.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>NewGL<br/>New General Ledger Accounting<br/>Realignment<br/>Reorg<br/>Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 2}, {"note": "1930498", "noteTitle": "1930498 - PRCTR: Materials with a deletion flag", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization. Materials for which the deletion flag has already been set at plant level are also selected. This causes follow-on problems because dependent data or Customizing is no longer available. This also has an adverse effect on system performance during the \"generation\" and \"reassignment\" steps.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Performance improvement, valuation class, account determination</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>None</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections. The system will now exclude materials for which the deletion flag is set at plant level from the \"generation\" step.</p></div>", "noteVersion": 1}, {"note": "1473789", "noteTitle": "1473789 - FAGL_ASSET_MASTERDATA_UPD terminates", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you use the report FAGL_ASSET_MASTERDATA_UPD to fill the asset master records with information for segment reporting, the program may terminate due to a missing database cursor.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center, segment<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached source code corrections.</p></div>", "noteVersion": 1}, {"note": "1958701", "noteTitle": "1958701 - PRCTR/SEG: Clearing document with profit center in entry view 566 (XVII)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In the entry view of a clearing document, a profit center is already assigned to the cleared line using default account assignment, for example, but there is no invoice reference.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>As a rule, this profit center is inherited from the cleared documents (table BSE_CLR) but has no entry of an invoice reference (BSEG-REBZG).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In future, these documents will be reorganized as artificial level 1 objects. This SAP Note must be implemented <strong>prior to the generation</strong>.</p>\n<p>Implement the program corrections.</p>", "noteVersion": 2}, {"note": "1590724", "noteTitle": "1590724 - PRCTR: Runtime error due to missing object number", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system returns a runtime error in the function module OBJECT_KEY_MOVE. This occurs, for example, when you create a recurring entry document with real estate account assignment.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>GETWA_NOT_ASSIGNED; BO00<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Cause: There is a program error.<br/>Prerequisites: You have activated the business function FIN_GL_REORG_1.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1734472", "noteTitle": "1734472 - PRCTR/SEG: Meaningful message text for message 503", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system issues warning message FAGL_REORGANIZATION 503: \"Document was not simulated\".<br/>You are not sure what effects this warning message has.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_REORGANIZATION 503<br/>Document splitting<br/>Reorg<br/>FI-GL (New), Profit Ctr Reorganization and Segment Reports<br/>Segment reorganization<br/>Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The documentation is not precise.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>In order to find out from which objects a receivable or payable has derived their profit center or segment account assignment, the document splitting is simulated with additional document splitting attributes (for example, cost center, purchase order item, CO orders, sales order item, network, network activity, WBS element).<br/><br/>The simulation of the document splitting may fail for various reasons.<br/><br/>Message 503 notifies the reorganization manager of this.<br/><br/>The receivable or payable still takes part in the reorganization. However, it is displayed at first hierarchy level, as the system can no longer determine from which object the profit center has been derived.<br/>As a result, the receivable or payable is handled as if the profit center was assigned directly.<br/>An object owner must determine the profit center manually.<br/><br/>For more information, see the KM documentation:<br/>http://help.sap.com/erp2005_ehp_05/helpdata/en/e4/3c55094d6442368beed884ffe68ede/content.htm<br/><br/>Warning message 503 has been made more precise.<br/>Implement the attached correction instructions to ensure the complete document key is displayed in the message text in future.<br/>Import the next Support Package for additional explanations in the message text.</p></div>", "noteVersion": 1}, {"note": "1693664", "noteTitle": "1693664 - Incomplete deletion of a reorganization plan", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you delete a reorganization plan, the system currently does not delete the relevant entries in the following tables:</p> <ul><li>FAGL_R_BLNCE</li></ul> <ul><li>FAGL_R_BLNCE_VAL</li></ul> <ul><li>FAGL_R_APAR</li></ul> <ul><li>FAGL_R_APAR_VAL</li></ul> <ul><li>FAGL_R_OI_TRACK0</li></ul> <ul><li>FAGL_R_OI_TRACK1</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/>Reorg<br/>Profit center reorganization<br/>Segment reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1824847", "noteTitle": "1824847 - PRCTR: Incorrect balance sheet posting for materials II", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute the reorganization run for profit centers. The system posts inexplicable transfer postings for balances.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The current balance at the time of the account assignment change of the material balances is zero. The balance of the prior period is not zero.<br/>This means that the entire balance was reduced in the reorganization period.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 1}, {"note": "1479987", "noteTitle": "1479987 - Changing derivation history results in error message TK 428", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you edit a derivation hierarchy, the system issues error message TK 428:<br/>\"Table FAGL_R_DERH_VER0 is not part of the Customizing object V_FAGL_R_DERH_DELV\".<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Cause: There is an inconsistency in the Customizing object V_FAGL_R_DERH_DELV.<br/>Prerequisites: You have activated the business function FIN_GL_REORG_1 or PSM_FM_REASSIGN.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import the relevant Support Package or perform the following manual tasks:</p></div>", "noteVersion": 1}, {"note": "1750217", "noteTitle": "1750217 - PRCTR/SEG: Error message FAGL_REORGANIZATION 533", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you reassign receivables and payables, the system issues error message FAGL_REORGANIZATION 533 frequently, which states that the document splitting information was not found for the receivable or payable.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Segment reorganization<br/>Profit center reorganization<br/>Reorg<br/>Reorganization<br/>FAGL_R_APAR_SPL</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Due to a program error, the system overwrites entries of the table FAGL_R_APAR_SPL when you save the foreign currency balances to be transferred.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.<br/>Generate it again and ensure that all receivables and payables are selected again. Delta mode must be switched off for this.<br/>Delete all entries in the table FAGL_R_OI_TRACK0 for your reorganization plan.</p></div>", "noteVersion": 1}, {"note": "1657156", "noteTitle": "1657156 - PRCTR: Performance issue - additional fields for materials", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You try to display additional fields in the object list for type \"Material\".<br/>Timeout occurs.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Materials object list<br/>additional fields<br/>timeout<br/>performance</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Performance optimization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions to improve performance.</p></div>", "noteVersion": 1}, {"note": "1782671", "noteTitle": "1782671 - PRCTR: Generating AR - all receivables from SD at level 1", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate receivables from SD, these are generated at level one but not under the sales order as expected.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorganization<br/>Reorg<br/>Hierarchy<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 8}, {"note": "1746777", "noteTitle": "1746777 - <PERSON><PERSON><PERSON> job terminates", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have started a generation process in the reorganization plan. The background job terminates with the error message ERROR_MESSAGE.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the following correction.</p></div>", "noteVersion": 1}, {"note": "1687570", "noteTitle": "1687570 - Wrong plan status when using service to create plan", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When creating reorganization plan from the link in services area of plan overview application, the plan will be wrongly set to completed status after saved.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Plan Overview, Services, Create Reorganization Plan.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a programming error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instruction.</p></div>", "noteVersion": 1}, {"note": "1571141", "noteTitle": "1571141 - PRCTR:Making Customzing for SD documents generally available", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In Customizing, you can set which time periods are relevant for the reorganization of sales and distribution documents (SD documents).<br/>This setting was previously intended for the profit center reorganization only and was therefore available under 'Specific Settings for Profit Center Reorganizations'. With this change, this setting is also provided for other plan types and can be found under 'Basic Settings'.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_R_SDCUS_1, V_FAGL_R_SDCUS_1<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Change with regard to the system design<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import the relevant Support Package.</p></div>", "noteVersion": 1}, {"note": "2997721", "noteTitle": "2997721 - <PERSON> Ledger closing CKLMP in connection with profit center reorganization plan derives incorrect PRCTR", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are trying to perform closing posting in transaction CKLMP but system issues error message FINS_ACDOC056 'Invalid profit center'.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CKLMP, FAGL_R_PRCTR_COIOB COEP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is a program error. In this case the internal table LT_PERIODS contains mor than one line and there is no logic to decide which date range is valid for PRCTR derivation. This correction brings the linkage between LT_PERIOD and LT_COIOB via items in COEP.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the advanced correction or download the corresponding support package.</p>", "noteVersion": 7}, {"note": "1706481", "noteTitle": "1706481 - PRCTR/SEG: Short dump when generating object types AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When receivables and payables are generated,<br/>the runtime error ASSERTION_FAILED occurs in the method COMPARE_REORG_SPL_WITH_ORI of the class (program CL_FAGL_R_SPLIT_REORG=========CM00K).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>CL_FAGL_R_SPLIT_REORG=========CP</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have implemented SAP Note 1640228.<br/>This is due to a program error caused by SAP Note 1640228.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 3}, {"note": "1552845", "noteTitle": "1552845 - Authorization check for plan overview", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You notice that there is no authorization check in the plan overview. The relevant check only takes place when you navigate to the details of a plan.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>F_REORG_PL, FAGL_REORGANIZATION126, reorganization, profit center, FM reassignment<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1763488", "noteTitle": "1763488 - No account assignment change for sales orders (XCLOSED = X)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You notice that some sales orders have not been reassigned even though the account assignment change was successful. For these sales orders, the value \"X\" is set in the XCLOSED field of table FAGL_R_PL_OBJECT.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>GET_OBJECT_INFO; XCLOSED; XDUMMY<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1824013", "noteTitle": "1824013 - PRCTR/SEG: MESSAGE_TYPE_X During Generation of AR / AP (2)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The generation of receivables/payables terminates with a runtime error. (MESSAGE_TYPE_X - error FAGL_REORGANIZATION 113)<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization<br/>Reorganization<br/>Reorg<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There are process chains with an invoice reference.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.<br/></p></div>", "noteVersion": 4}, {"note": "1690702", "noteTitle": "1690702 - PRCTR: Subsequent processes partly post to old accnt assgmt", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>After the reassignment of receivables and payables, the system still partially posts with the old profit center when posting subsequent processes (clearing, partial payments and residual items, for example) to the receivables or payables account.<br/>However, the amount transferred to the new profit center during the reorganization includes the total amount.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorganization<br/>Reorganization<br/>Reorg<br/>G_BEB_CHECK_ITEM_ASSIGNED</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1648850", "noteTitle": "1648850 - PRCTR: Short dump in FAGL_R_BSIK_BSEG_WRITE_DB", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you reassign down payment requests, a program termination (short dump) occurs in the program SAPLFAGL_R_OI_UPDATE.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorg<br/>Reorganization<br/>FAGL_R_BSIK_BSEG_WRITE_DB</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1482830", "noteTitle": "1482830 - PRCTR: Cst center change not possible due to reorgniztn mssg", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In a cost center, you change the profit center, and the system issues error message FAGL_REORGANIZATION 601.<br/>You have activated the business function FIN_GL_REORG_1. However, you do not use the profit center reorganization, that is, the reorganization plan type \"001\" has not been activated in Customizing.<br/>Therefore, you do not expect the system to issue an error message before the business function is activated for a cost center without values, and you expect the system to issue the customizable message FAGL_ORG_UNITS 011 for a cost center with values.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Segment reporting<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 3}, {"note": "1795649", "noteTitle": "1795649 - PRCTR: Analysis report for material balances", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This report displays the material balances in a list for a reorganization run for profit centers. This can be helpful when you want to analyze problematic cases. This is a display report only. It does not change any data.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Support during troubleshooting</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program.</p></div>", "noteVersion": 2}, {"note": "1683439", "noteTitle": "1683439 - PRCTR/SEG: Preventing inconsistencies for invoice reference", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Invoice-related items for receivables or payables are not reassigned together.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>Reorganization<br/>REBZG<br/>FAGL_REORGANIZATION</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1869445", "noteTitle": "1869445 - PRCTR/SEG: General restrict. PRCTR assignment in cycles", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You receive a dump when repeatedly generating for already reorganized documents/document lines in the area AP/AR.<br/>Cycles are maintained in the assignment table in such a way that a profit center (B) can exist both as an old and as a new profit center (for example, A -&gt; B and B-&gt; C). Entries for which the old profit center is identical with the new profit center (B-&gt; B) have not been analyzed yet.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>New GL<br/>New general ledger<br/>General Ledger Accounting (new)<br/>Cycle<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In a reorganization plan under \"General restrictions -&gt; Assignments\", as the old account assignment you entered an account assignment that also occurs as a new account assignment in the assignments table.<br/><br/>Example:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Profit center (old) </th><th align=\"LEFT\">   Profit center (new) </th><th align=\"LEFT\">  Comment</th></tr> <tr><td>PCA</td><td> PCB</td></tr> <tr><td>PCB</td><td> PCB</td><td> &lt;- identical PRCTR</td></tr> </table> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/></p></div></div>", "noteVersion": 4}, {"note": "1858781", "noteTitle": "1858781 - PRCTR: Performance when reassigning sales documents", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The runtime for the reassignment of the object type SO/SOI is long; there is a large amount of data in the table FAGL_R_SDLOG_001.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>When you reassign sales document items in the profit center reorganization, the system reads the document flow and the follow-on documents are logged in the table FAGL_R_SDLOG_001 so that follow-on processes (such as the cancelation of billing documents, or credit memo, for example) can be performed in the correct profit center. Currently, the system determines and updates more follow-on documents than necessary. In particular, this may increase the data volume and runtime unnecessarily for sales documents with many follow-on documents, such as scheduling agreements, for example.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions or import the Support Package.<br/><br/>If the parameter NO_ACC_DOC does not yet exist in the interface of the function module RV_ORDER_FLOW_INFORMATION, implement SAP Note 1827079.</p></div>", "noteVersion": 4}, {"note": "2098891", "noteTitle": "2098891 - PRCTR/SEG: Migrated documents not reassigned (FAGL_REORGANIZATION 533)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Migrated documents are not reassigned because the document splitting information is missing (error message FAGL_REORGANIZATION 533).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>Migration to General Ledger Accounting (new)<br/>FAGL_R_APAR<br/>FAGL_REORGANIZATION 533</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This affects documents that were migrated to General Ledger Accounting (new) before the PRCTR/segment reorganization or documents that have a migrated document as the invoice reference.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This SAP Note replaces SAP Note 2082695.</p>\n<p>Implement the corrections below. You must implement this SAP Note <strong>before you generate</strong> Accounts Payable/Accounts Receivable (AP/AR).</p>", "noteVersion": 3}, {"note": "1949840", "noteTitle": "1949840 - PRCTR/SEG: Dump when generating AP/AR 566 (XIV)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the generation of receivables and payables, the system issues a<br/>dump in the method <br/>CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566<br/>CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 1911623.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 1}, {"note": "1907741", "noteTitle": "1907741 - PRCTR/SEG: Dump when generating APAR/566 (VIII)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the generation of receivables and payables, a dump occurs in the method<br/>CL_FAGL_R_SPLIT_REORG:CHECK_AND_ELIM_ROUND_REORG_SPL.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566<br/>CL_FAGL_R_SPLIT_REORG:CHECK_AND_ELIM_ROUND_REORG_SPL<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the reorganization, rounding differences are triggered. For example, because<br/>tax differences or credit-side price differences are not distributed in accordance with the original document split characteristics as a result of the different summarization due to the additional reorganization characteristics.<br/><br/>You have implemented SAP Note 1903491 and the related previous SAP Notes.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the following corrections.</p></div>", "noteVersion": 6}, {"note": "1588379", "noteTitle": "1588379 - PRCTR: Cycle not permitted in assignment of plan", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>in a reorganization plan, you have maintained the assignment table in such a way that a profit center (B) exists both as an old and as a new profit center (for example, A -&gt; B and B-&gt; C). Entries for which the old profit center is identical with the new profit center (B-&gt; B) are not analyzed in this context.<br/>As a result, objects that are already correctly assigned still have to be maintained in the reorganization plan, which is not required in most cases.<br/>This correction prevents a setting of this type. By implementing further corrections, the message FAGL_REORGANIZATION 029 can be set as required. This is already mentioned in the long text. In addition, an alternative process is described there.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Cycle, FAGL_REORGANIZATION 029<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is due to a design error and program error. <br/> </p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the following correction and then perform the manual postprocessing steps.</p></div>", "noteVersion": 3}, {"note": "1864587", "noteTitle": "1864587 - PRCTR: Incorrect company code for a purchase order with account assignment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You carry out a profit center reorganization run for the object type POA (purchase order with account assignment). After you generate the object list, you notice that purchase orders with account assignment that do not originate from the specified controlling area or company code were selected.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_POA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>When you defined the general restrictions for the reorganization run, you did not further restrict or explicitly enter the company code (or company codes). </p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached changes.</p></div>", "noteVersion": 5}, {"note": "1773094", "noteTitle": "1773094 - PRCTR/SEG: Dump ITAB_DUPLICATE_KEY Gen. object list AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The generation of receivables and payables terminates with a<br/>short dump 'ITAB_DUPLICATE_KEY' in the method G_GROUP_ITEMS_P.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>Grouping<br/>GRP<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The problem is due to a program error during generation.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 3}, {"note": "1761156", "noteTitle": "1761156 - PRCTR: Error FAGL_REORGANIZATION 505 - derivation hierarchy", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system issues the error FAGL_REORGANIZATION 505 when you generate receivables because the derivation hierarchy used is incomplete.<br/><br/>You have already adjusted the standard derivation hierarchy (version 001) for plan type 001 using SAP Note 1693804.<br/>However, the following nodes are still missing:</p> <ul><li>Object type AR (receivable) below every node CC (cost center),<br/>Subtree: CC-AR</li></ul> <ul><li>Object type AR (receivable) below the node O30 (maintenance order),<br/>Subtrees: O30-AR   and  CC-030-AR</li></ul> <ul><li>Object type AR (receivable) below the node O02 (accrual order),<br/>Subtree: O02-AR</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorg<br/>Reorganization<br/>Derivation hierarchy, hierarchy<br/>FAGL_REORG_1_FAGL_R_DERH</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Delivery Customizing is incomplete.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions. In particular, note the manual post-implementation steps.<br/><br/>Note that existing reorganization plans will continue to use the derivation hierarchy that was valid at the time of creation.<br/>If you want to use the derivation hierarchy that is changed with this note, you must create a new reorganization plan (and delete the existing reorganization plan, if required).</p></div>", "noteVersion": 3}, {"note": "1733459", "noteTitle": "1733459 - PRCTR/SEG: Runtime error during generation of receivables", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the generation of receivables, a runtime error occurs if a receivable has a reference to a sales order that has either been archived or that comes from another system.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SAPLV60B<br/>Reorganization<br/>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>AR<br/>BL 203<br/>The message is incomplete (severity, area, number, or exception is missing).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 2}, {"note": "1931520", "noteTitle": "1931520 - PRCTR/SEG: Endless loop when generating AP/AR / 566 (XI)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>An endless loop occurs when you generate receivables and payables using the method CL_FAGL_R_SPLIT_REORG:CHECK_AND_ELIM_ROUND_REORG_SPL.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566<br/>CL_FAGL_R_SPLIT_REORG:CHECK_AND_ELIM_ROUND_REORG_SPL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The cause is described in SAP Note 1911623.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the following corrections.<br/><br/>For critical documents, make sure to save the tables FAGL_SPLINFO and FAGL_SPLINVO_VAL before you start the REASSIGN. SAP Note 1930908 provides a correction report.</p></div>", "noteVersion": 6}, {"note": "1828887", "noteTitle": "1828887 - PRCTR: Adjustments on the GR/IR account", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization. The balance that is determined for the transfer posting on the GR/IR account for purchase orders is incorrect.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>PO, POA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>A program error occurs because the system ignores adjustments for purchase orders on the GR/IR account.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 1}, {"note": "1730611", "noteTitle": "1730611 - PRCTR: Error SAPSQL_INVALID_TABLENAME in select_first in PO", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have created a reorganization plan in your company. You try to generate the POA object list. An error SAPSQL_INVALID_TABLENAME occurs in method \"select_first_level\" for object type PO.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>SAPSQL_INVALID_TABLENAME<br/>CL_FAGL_R_OBJ_TYPE_001_PO<br/>select_first_level<br/>purchase order without account assignment- object list</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the provided correction instructions.</p></div>", "noteVersion": 1}, {"note": "1939867", "noteTitle": "1939867 - PRCTR/SEG: Error message FAGL_REORGANIZATION 544", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the reassignment of accounts payable/receivable (AP/AR), the system issues error message FAGL_REORGANIZATION 544.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 544</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Documents with an invoice reference should be assigned to a group and reassigned together. At present, this does not happen if the original document has a reference to a noted item.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the following corrections. Create a customer message with the component FI-GL-REO-GL; the documents need to be regenerated.</p>", "noteVersion": 4}, {"note": "1831242", "noteTitle": "1831242 - PRCTR: Adjustments on the GR/IR account II", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization. The balance that is determined for the transfer posting on the GR/IR account for purchase orders is incorrect.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>POA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>A program error occurs because the system ignores adjustments for purchase orders on the GR/IR account.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 1}, {"note": "1891569", "noteTitle": "1891569 - <PERSON>rror FAGL_REORGANIZATION 051 during transfer posting", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>System issues an error FAGL_REORGANIZATION051 'No transaction currency determined' during the balance reposting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FAGL_REORGANIZATION, SAVE_BALANCES_P, FAGL_REORGANIZATION051</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is a program error. So far experienced only by MAT object type but it could happen under specific circumstances also for other object types.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this correction or download the corresponding support pack.</p></div>", "noteVersion": 3}, {"note": "1617885", "noteTitle": "1617885 - PRCTR: Correction to note 1612114", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Instructions in note 1612114 contain an error in the constructor of class CL_FAGL_R_OBJ_TYPE_APAR.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Apply the correction instruction in this note.</p></div>", "noteVersion": 1}, {"note": "1628687", "noteTitle": "1628687 - PRCTR/SEG: Cash discount clearing lines not reassigned", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You want to reassign payables with cash discount clearing lines (vendor net procedure), but the system does not reassign all cash discount clearing lines.  The payable has the following properties:</p> <ul><li>Different profit centers (in the case of a profit center reorganization) or segments (in the case of a segment reorganization) are derived from the same account assignment (for more information, see Note 1597693).</li></ul> <ul><li>The account assignment has a partner assignment (partner profit center and/or partner segment).</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>New GL<br/>General Ledger Accounting (new)<br/>Reorg</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 3}, {"note": "1885761", "noteTitle": "1885761 - PRCTR/SEG: Dump when generating APAR / 566 (V)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate receivables and payables, a dump occurs in method CL_FAGL_R_SPLIT_REORG:CHECK_AND_ELIM_ROUND_REORG_SPL.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the reorganization, rounding differences are triggered. The reason is, for example: As a result of the different summarization due to the additional reorganization characteristics, the tax is not distributed in accordance with the original document split characteristics.<br/><br/>You have already implemented SAP Note 1869309.<br/>SAP Note 1869309 solves the rounding differences when they affect the local currency '10', but not when they occur in currency type '20' or '30'.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the following corrections.<br/><br/>Then perform a new test in a copy of the production system. This is a mandatory prerequisite.</p></div>", "noteVersion": 12}, {"note": "1574285", "noteTitle": "1574285 - PRCTR: Cost center not removed from reorganisation plan", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You change the assignment of a profit center to a cost center. As a result, the cost center is included in a reorganization plan. You then delete the new analysis period from the cost center master record. The system does not remove the cost center from the reorganization plan. This is incorrect.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>KS02</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import the relevant Support Package or implement the corrections manually.</p></div>", "noteVersion": 1}, {"note": "1823482", "noteTitle": "1823482 - PRCTR/SEG: FAGL_REORGANIZATION 575 during reassignment of AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During a reassignment of receivables and payables, error FAGL_REORGANIZATION 575 is output, which should not be the case.<br/>Example: For a payable with 2 items, only one item is involved in the reorganization, the other is not (status = '60').<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Cycle<br/>FAGL_REORGANIZATION 575<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is a follow-on error to SAP Note 1628255 as of Release 606.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/></p></div>", "noteVersion": 1}, {"note": "1654363", "noteTitle": "1654363 - PRCTR: Unjustified error FAGL_REORGANIZATION 222", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You tried to reassign Purchase Orders with Account Assignment in a Reorganisation plan for Profit Center.<br/><br/>Error message FAGL_REORGANIZATION 222: 'Error when reading a/c assignmt information; purch. order &amp;1 item &amp;2, &amp;3' occurs, although account assignment is explicit.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_POA<br/>real account assignment<br/>K_COBL_CHECK</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1733158", "noteTitle": "1733158 - Objects are not reorganized", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You copy the standard derivation hierarchy and adjust the copy by deleting some nodes. You perform the generation. In the process, the system does not find objects that, in the deviation hierarchy, would belong to object lists deleted from the derivation hierarchy. These objects are supposed to be included in the object list in the first level.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization plan, generation</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1651132", "noteTitle": "1651132 - Input help for hierarchy version", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you create a reorganization plan, the selected plan type is not included in the input help of the hierarchy version.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections in accordance with the correction instructions.</p></div>", "noteVersion": 1}, {"note": "1600154", "noteTitle": "1600154 - PRCTR:FAGL_REORGANIZATION 503 when simulating residual items", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Receivables or payables can have invoice-related items (such as partial payments and residual items).<br/>When you generate the object lists, the system simulates document splitting with extended attributes for each payable or receivable and its invoice-related items. This is to ensure that the system can determine, for the various partial amounts, from which account assignment the profit center was derived in each case.<br/><br/>If the simulation fails, the receivable or payable and its invoice-related items are displayed at the first hierachy level.<br/><br/>Currently, the simulation of a residual item may fail because the extended document splitting information of the original receivable or payable (cleared by the residual item) is incomplete.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>Reassignment<br/>Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Perform the steps described in the manual correction instructions.</p></div>", "noteVersion": 3}, {"note": "1600063", "noteTitle": "1600063 - PRCTR REORG: UNASSIGN in method BUILD_DECISION_TAB_P AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Class:CL_FAGL_R_OBJ_TYPE_001_APAR<br/>method: BUILD_DECISION_TAB_P<br/>Field symbol &lt;flag&gt; is checked after statement \"assign\", after usage, it is not unassigned. It should be unassigned before it is assigned to new field.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit Center Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Programming error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction accordingly.</p></div>", "noteVersion": 1}, {"note": "1585472", "noteTitle": "1585472 - PRCTR: dump when adding additional fields for object display", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>IMG:Specify Display Characteristics for Each Object Type<br/>1. You have added entries for AP/AR BSIK/BSID field BSTAT or UMSKS.<br/>2. You want to display the AP/AR object list with selected fields BSTAT or UMSKS<br/>3. A dump shows up.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit Center Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions accordingly.</p></div>", "noteVersion": 1}, {"note": "1731380", "noteTitle": "1731380 - PRCTR: wrong balances of materials(object type MAT)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have created a reorganization plan in your company. After reassigning and transfer posting you notice that values posted for the Materials object list are doubled by amount or quadrupled.<br/>This happens for plants, where Material Ledger is active.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>double values<br/>Material balances<br/>CL_FAGL_R_OBJ_TYPE_001_MAT<br/>reassign_md_and_get_balances</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the provided correction instructions.</p></div>", "noteVersion": 3}, {"note": "1601283", "noteTitle": "1601283 - PRCTR: Error FAGL_REORGANIZATION 558", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables or payables, the system issues error FAGL_REORGANIZATION 558.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorg<br/>Profit center reorganization<br/>Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1910342", "noteTitle": "1910342 - PRCTR/SEG: Error message FAGL_REORGANIZATION 566/567", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the reassignment of receivables and payables, the system issues<br/>error message FAGL_REORGANIZATION 566.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>'FAGL_REORGANIZATION 566'</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>After analysis by Development Support, error FAGL_REORGANIZATION 566 can be set to customizable.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Based on SAP Notes 1808980 and 1797558, an analysis of the differences between the reorganization view (tables: FAGL_R_SPL/_VAL) and the general ledger view (tables FAGL_SPLINFO/_VAL) can be performed. The total per profit center or segment and currency type does not match. In this case, the system issues error message FAGL_REORGANIZATION 566.<br/><br/>You have already implemented the following SAP Notes in advance:</p>\n<p>1934371  PRCTR/SEG: Generation: Comparing a simulated AR/AP doc incl. reorg. characteristics against the original doc / 566<br/>1931520  PRCTR/SEG: Endless loop when generating AP/AR / 566 (XI)<br/>1911623  PRCTR/SEG: Dump when generating APAR / 566 (X)<br/>1907741  PRCTR/SEG: Dump when generating APAR / 566 (VIII)<br/>1903491  PRCTR/SEG: Dump when generating APAR / 566 (VII)</p>\n<p>1) Payment documents often receive their profit center through inheritance. In this case, the document is reassigned manually.</p>\n<p><br/>2) This SAP Note implements the prerequisites for the customizable error message FAGL_REORGANIZATION 567, which enables a reassignment to level 1 after a prior check and release by SAP Development Support.</p>\n<p> </p>\n<p>Create message FAGL_REORGANIZATION 567 (transaction SE91) with the following short text:<br/>\"Reassignment: PRCTR/SEGMENT deviation in FAGL_SPLINFO and FAGL_R_SPL &amp;1\"</p>\n<p> </p></div>", "noteVersion": 15}, {"note": "1812836", "noteTitle": "1812836 - PRCTR/SEG: AP/AR objects unjustified at first level", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have already implemented the following SAP Notes, and the system still displays partial amounts of receivables or payables at the first hierarchy level, which is incorrect: 1794282, 1782671, 1690702</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorg<br/>Segment reorganization<br/>Reorganization<br/>Hierarchy</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 4}, {"note": "2131691", "noteTitle": "2131691 - CO-PA revaluation does not work for cancelation invoices", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use report FAGL_R_COPA_CORRECT to repost CO-PA documents in order to update new profit center for reorganized sales documents. However you encounter the issue, that the reposted CO-PA documents still contain the old profit center for cancelation billing documents.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PC reorganization, FAGL_R_COPA_CORRECT, KES4</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The reposting of cancelation documents uses the original documents, where \"real\" reversal is carried out. As long as the original documents are not still revaluated, the original documents with the old profit center are uses.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the advance correction.</p>\n<p>This ensure, that the cancelled orginal documents are revaluated before processing the cancelation documents.</p>", "noteVersion": 1}, {"note": "1845527", "noteTitle": "1845527 - PRCTR: Delivery costs", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization for purchase orders. When you do this, the delivery costs are not determined correctly. The accounting documents for the invoice of delivery costs are not taken into account.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>PO, POA, EKBZ, EKBZ_MA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The error occurs both for purchase orders with account assignment and also purchase orders without account assignment.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 4}, {"note": "1796970", "noteTitle": "1796970 - PRCTR: Analysis report for purchase orders", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This SAP Note provides an analysis report for both purchase orders with account assignment and purchase orders without account assignment. In each report, you have the option to display the balances and also the information from the table FAGL_SPLIINFO for the purchase orders to be selected. For certain methods, it is necessary to change the attribute from private to public.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Support during troubleshooting</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program. No manual steps are required.</p></div>", "noteVersion": 1}, {"note": "1643715", "noteTitle": "1643715 - PRCTR/SEG: APAR objs. missng for docs. w/ partner assignment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have posted a receivable or payable with partner assignment(s), for example, partner profit center or partner segment.<br/>However, parts of the receivable or payable are not displayed in the object list.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>New General Ledger Accounting<br/>NewGL<br/>Reorganization<br/>Reorg</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1479650", "noteTitle": "1479650 - Errors in processing of multiple assignment of objects", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>For objects that do not have unique higher-level objects in the hierarchy, errors may occur in processing because the assignment changes even though this is not provided for.  For example, this is the case for assets that can be under a cost center as well as under an internal order according to their account assignment.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>General Ledger Accounting (new), new general ledger, reorganization, profit center<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/>You have activated the business function FIN_GL_REORG_1 or PSM_FM_REASSIGN.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "2095521", "noteTitle": "2095521 - License audit measurement", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The function modules for the license audit measurements are not available for the Reorganization Framework.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>USMM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the relevant Support Package or implement the attached corrections.</p>", "noteVersion": 2}, {"note": "3025393", "noteTitle": "3025393 - SEG: Specific Restrictions not applied during Reassignment", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the segment reorganization the reassignment phase generates enormous amount of GLOI objects that are present in the system. This leads to performance issue as well as overwhelming of the log storage.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SEGMENT, PARALLELIZATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>During step GENERATION the parallelization can be activated and is realized by company codes.</p>\n<p>During step REASSIGNMENT the parallelization is based on the \"superior objects\" of the reorganization hierarchy. For segment reorganization there is only the profit center as superior object of the segment. Therefore the parallelization would be effective if there were several profit centers included in the reorganization plan. With only one profit center or with only one profit center which has \"many\" open items the parallel jobs have no effect.</p>\n<p>It is necessary to consider the specific restrictions in such cases even on the subordinated object levels (not only for the 1st level objects).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the advanced correction manually or upgrade to the corresponding support package.</p>", "noteVersion": 3}, {"note": "1808494", "noteTitle": "1808494 - PRCTR: Deadlock in fagl_splinfo", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>1) During the reorganization of profit centers, a short dump occurs during the account assignment change for materials/orders (deadlock).<br/>2) During the reassignment of purchase orders without account assignment, the system assigns the new profit center too many entries from table FAGL_SPLINFO.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>deadlock, FAGL_SPLINFO, CX_SY_OPEN_SQL_DB, runtime error, CL_FAGL_R_OBJ_TYPE_001_PO, UPDATE_SPLIT_ITEMS</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by a lock problem. </p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 2}, {"note": "1609861", "noteTitle": "1609861 - Background processes terminate due to deadlock", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute one of the three activities \"Generate\", \"Reassign\" or \"Transfer\" and receive information in the logs of your reorganization plan telling you that the internal session was terminated due to a deadlock.<br/>To clarify: Various jobs were started in the background. These jobs possibly process large volumes of data. In the original concept, this may mean that various jobs lock each other, which leads to a deadlock situation. This problem rarely occurs.<br/>In addition, it was not possible under adverse conditions in the original concept to prevent the relevant objects being included in the reorganization plan, which meant they were not reorganized.<br/>Both problems are solved with these corrections.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Deadlock, dispatcher, dispatcher ID, FAGL_R_PL_COUNT<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The original design could not prevent this and retain an acceptable level of performance at the same time.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Due to the effort required for the manual tasks, we recommend that you implement the corrections in the relevant Support Package. However, it is also possible to perform a manual implementation.</p></div>", "noteVersion": 2}, {"note": "1486343", "noteTitle": "1486343 - Wording of error messages that can be customized", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The message type (error, warning, information, and so on) can be configured in Customizing for some messages.<br/>You have configured a message controlled by default as an error, as a warning. However, the long text still maintains, for example, that processing has been terminated.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>OBA5, reorganization, FAGL_REORGANIZATION</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The long text of the following messages ignores the fact that the message type of the relevant message can be configured.</p> <ul><li>FAGL_REORGANIZATION 043</li></ul> <ul><li>FAGL_REORGANIZATION 071</li></ul> <ul><li>FAGL_REORGANIZATION 072</li></ul> <ul><li>FAGL_REORGANIZATION 339</li></ul> <ul><li>FAGL_REORGANIZATION 502</li></ul> <ul><li>FAGL_REORGANIZATION 707</li></ul> <ul><li>FAGL_REORGANIZATION 708</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The relevant long text are adjusted with Support Package 02.</p></div>", "noteVersion": 1}, {"note": "1762390", "noteTitle": "1762390 - Profit center: Runtime error TSV_TNEW_PAGE_ALLOC_FAILED", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the generation or reassignment of sales and distribution documents within a reorganization plan, the runtime error TSV_TNEW_PAGE_ALLOC_FAILED occurs.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>If there are no sales and distribution documents to be reorganized, the entire table VBKD is selected due to a program error. If this table in your system is large compared to the working memory available, the runtime error may occur.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.<br/></p></div>", "noteVersion": 1}, {"note": "1668882", "noteTitle": "1668882 - Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753,754,755,756", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The Note Assistant allows you to automatically implement note corrections in your ABAP systems. You can find further information about the Note Assistant on SAP Service Marketplace at service.sap.com/noteassistant.<br/><br/>Before you implement notes with the Note Assistant, you should upgrade to the latest version of the Note Assistant. This note references the most important notes for correcting errors and updating the Note Assistant. It is the successor of Note 875986.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP notes, SNOTE, SAP_NOTES</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note updates the application / transaction snote.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><br/><br/><strong>Implement the current version of this note to update the Note Assistant.</strong> This ensures that errors in the Note Assistant are corrected and that you can use the latest functions. During the implementation, all of the SAP notes that are listed in this note under <strong>III/ Attachment</strong> and that are relevant for your release level and Support Package level are implemented in your SAP system.<br/>Depending on the release level and Support Package level of your SAP system, you have the following options:<br/><br/>You can implement Note 1668882 automatically using the Note Assistant.<br/>           The prerequisites and procedure can be found under \"<strong>I/ Implementing Note 1668882 using the Note Assistant\".</strong><br/><br/>You can individually implement the notes that are relevant for your SAP system using the Note Assistant.<br/>           The prerequisites and procedure can be found under \"<strong>II/ Implementing notes from Note 1668882 individually using the Note Assistant\".</strong><br/><br/>If your SAP system has a different release and Support Package level than those mentioned under I/ and II/, then the note is not relevant for your SAP system.<br/><br/><strong><span>I/ Implementing Note 1668882 using the Note Assistant</span></strong><br/><br/><br/><strong>Prerequisites</strong><br/>Your system must have one of the following release and Support Package levels or higher:<br/><br/><strong>Soft.Comp. Release Support Package</strong><br/>SAP_BASIS 7.30 Without Support Packages</p>\n<p>SAP_BASIS 7.31 Without Support Packages</p>\n<p>SAP_BASIS 7.40 Without Support Packages</p>\n<p>SAP_BASIS 7.50 Without Support Packages</p>\n<p>SAP_BASIS 7.51 Without Support Packages</p>\n<p>SAP_BASIS 7.52 Without Support Packages</p>\n<p>SAP_BASIS 7.53 Without Support Packages</p>\n<p>SAP_BASIS 7.54 Without Support Packages</p>\n<p>SAP_BASIS 7.55 Without Support Packages</p>\n<p>SAP_BASIS 7.56 Without Support Packages</p>\n<p><strong>Please implement note 2248091 before implementing this note.</strong></p>\n<p><br/><strong>Procedure</strong><br/><br/>1. Use Note Assistant to implement Note 1668882.<br/><strong>Note the following:</strong> If you cannot implement changes during the implementation (yellow traffic light on the 'Confirm Changes' input screen), use the 'Cancel' (F12) function to terminate the implementation of the note and create a customer message under the application component BC-UPG-NA.<br/><br/>2. After you confirm the activation of the changed objects, the dialog window for selecting the main program for CWBNTCNS appears in certain SAP_BASIS Support Packages that were previously imported. Select one of the listed programs and choose \"Select\".<br/>              Important : If you do not select a program and choose \"Terminate\" instead, the system does not activate the changes that you want to implement using the composite note.<br/><br/>              All of the notes that are relevant for your release and Support Package level are implemented in your SAP system.<br/><br/>3. Use transaction /NSNOTE to restart the Note Assistant.<br/>              The Note Assistant is now updated to the current version. You can implement more SAP notes using the Note Assistant.<br/><br/><br/><strong><span>II/ Implementing notes from Note 1668882 individually using the Note Assistant</span></strong><br/>You can implement notes from Note 1668882 individually using the Note Assistant.<br/><br/><br/><br/><strong>Procedure</strong><br/><br/>1. Load Note 1668882 into your system.<br/>2. Load all of the notes contained in Note 1668882 into your system. Under \"III/ Attachment: List of notes from Note 1668882\", you will find a list of the notes contained in Note 1668882.<br/>              If you load notes into the Note Assistant via an RFC connection, you can proceed as follows:<br/><br/>a) Select all note numbers listed in Note 1668882 under \"III/ Attachment: List of notes from Note 1668882\"\" and copy them.<br/>b) In the Note Assistant, choose \"Download SAP Note\".<br/>c) Choose \"Multiple selection\".<br/>d) Choose \"Upload from clipboard\".<br/>                      The system displays a list of note numbers.<br/><br/>e) Choose \"Copy\" and start the note download.<br/>                      All of the notes contained in the note are loaded into your system.<br/><br/>                      The Note Assistant shows you which notes can be implemented in your system. You can classify notes that cannot be implemented as \"not relevant\" to remove them from your worklist.<br/><br/>3. Implement the notes that can be implemented one after the other and pay attention to the note texts when you do this.<br/><br/><br/><br/><strong><span>III Attachment: List of notes from Note 1668882</span></strong><br/>The corrections of the following listed notes are implemented in your system when Note 1668882 is automatically implemented in your system if their release level and Support Package level is relevant for your system.<br/>The list of notes corresponds mostly with the related notes.  However, the related notes are displayed only in the download area, but not in the Note Assistant. You require the list to be able to download the notes together if you implement the notes individually from Note 1668882 with the Note Assistant as described under II/.</p>\n<p>0001291055<br/>0001487461<br/>0001487489<br/>0001487661<br/>0001494322<br/>0001500456<br/>0001504500<br/>0001505242<br/>0001517468<br/>0001518861<br/>0001523687<br/>0001524252<br/>0001530273<br/>0001532264<br/>0001535724<br/>0001537354<br/>0001539505<br/>0001541531<br/>0001542835<br/>0001543395<br/>0001549103<br/>0001552560<br/>0001557768<br/>0001566290<br/>0001571213<br/>0001610942<br/>0001621321<br/>0001627683<br/>0001639074<br/>0001673013<br/>0001718058<br/>0001720495<br/>0001930917<br/>0001953150<br/>0001975910<br/>0002007838<br/>0002025616<br/>0002059257<br/>0002077553<br/>0002115211<br/>0002116888<br/>0002130489<br/>0002158475<br/>0002159134<br/>0002192729<br/>0002248091<br/>0002254096<br/>0002254211<br/>0002264123<br/>0002289302<br/>0002292923<br/>0002314876<br/>0002328318<br/>0002411418<br/>0002398161</p>\n<p>0002697766<br/>0002691847<br/>0002684471<br/>0002671774<br/>0002623459<br/>0002624337<br/>0002617883</p>\n<p>0002396769</p>\n<p>0002042123</p>\n<p>0002478661</p>\n<p>0002589309</p>\n<p>0003041970</p>\n<p>0003047860</p>\n<p>0003079593</p>\n<p>0003085447</p>\n<p>2715783<br/>2757237<br/>2764725<br/>2765308<br/>2459558<br/>2212925<br/>2606986<br/>2541236<br/>2536585<br/>2368460<br/>2598809<br/>2597808<br/>2568276<br/>2292923<br/>2739641<br/>2770960</p>\n<p>2773977</p>\n<p>2775698</p>\n<p>2730170</p>\n<p>2792897</p>\n<p>2799582</p>\n<p>2802126</p>\n<p>2810041</p>\n<p>2844646</p>\n<p>2860125</p>\n<p>2910608</p>\n<p>2930611</p>\n<p>3008844</p>\n<p>3006946</p>\n<p>2958954</p>\n<p>2953369</p>\n<p>3001279</p>\n<p>3111925</p>\n<p>3123184</p></div>", "noteVersion": 41}, {"note": "1765330", "noteTitle": "1765330 - Closing plans for which successful processing is not complete", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to close a reorganization plan even though all steps have not been processed successfully yet.<br/>For example, you want to create a follow-up plan for the same organizational unit in a test system, but an old plan is still open due to small anomalies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CLOSE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Additional function</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Proceed as follows:<br/>Add '&amp;special_closing=X' (Web Dynpro application FAGL_R_PLAN_OVERVIEW) to the URL of the plan overview in the browser. The system then displays the additional button \"Close Plan with Open Objects\" to close these plans.<br/>Additional information, if you cannot see the URL:<br/>From the ERP system, call the Web Dynpro application FAGL_R_PLAN_OVERVIEW. This can be done using a favorites entry that has already been made. Otherwise, use transaction SE80. In the package FAGL_REORGANIZATION for Ehp5 or FAGL_REORGANIZATION_FW as of EhP6, in the folders Web Dynpro -&gt; Web Dynpro applications, the application FAGL_R_PLAN_OVERVIEW is then available. You can start it by right-clicking it and choosing \"Test\".</p>\n<p>Note the following:</p>\n<p>In the 'SAP NetWeaver Business Client' environment, it is not possible to add the parameter to the URL. In this case, you must use transaction SE80 to start the call.</p>\n<p>In some Web browsers, the URL ends with the special character '#'. In this case, the string '&amp;special_closing=X' must be inserted BEFORE the special character.</p></div>", "noteVersion": 2}, {"note": "1771400", "noteTitle": "1771400 - PRCTR: wrong balance of materials (obj.list MAT)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have created a reorganization plan in your company. After the reassignment of materials (MAT objects list) and balance determination you encounter errors in the material balances. The calculated value for some objects is doubled or quadrupled in comparison to the balance in the system.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_MAT<br/>Material balances<br/>Object list MAT</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the provided correction instructions.</p></div>", "noteVersion": 2}, {"note": "1649508", "noteTitle": "1649508 - PRCTR: ITAB_ILLEGAL_SORT_ORDER,short dump GET_SPLIT_ITEMS", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you try to reassign purchase orders with account assignment there is a short dump in class CL_FAGL_R_OBJ_TYPE_001_POA. The program error  ITAB_ILLEGAL_SORT_ORDER occurs in method GET_SPLIT_ITEMS. The ABAP command 'Append' is used for sorted table. It causes dump.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorg<br/>Reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_POA<br/>GET_SPLIT_ITEMS<br/>ITAB_ILLEGAL_SORT_ORDER</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 3}, {"note": "1818096", "noteTitle": "1818096 - PRCTR: Performance improvement for purchase orders", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This SAP Note improves the runtime in the area of reassigning purchase orders. The improvement relates to the runtime behavior of both purchase orders with account assignment and purchase orders without account assignment.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>UPDATE_SPLIT_ITEMS</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>None</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 3}, {"note": "1611108", "noteTitle": "1611108 - System does not react when save plan", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When saving the reorganization plan on the 'General Restrictions' tabpage, the system does not react. But if you try to switch to another tabpage, this is not possible because there are some error messages displayed.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Save, Reorganization Plan, General Restrictions, FAGL_R_PLAN_DETAIL.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The error messages are not displayed when saving reorganization plan.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instruction.</p></div>", "noteVersion": 2}, {"note": "2106730", "noteTitle": "2106730 - Unable to reverse the settlement", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As one of the restrictions resulting from the Profit center reorganization is preventing executing or reversing the settlement to previous periods before the reorganization date. The check is applied on the controling area level. However it prevents running or reversing settlements also for CO objects, which have not been included in the reorganization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FAGL_REORGANIZATION 335, FAGL_R_CHECK_SETTLEMENT_REVERS, PC reorganization, KO88, CO88, VA88, CJ88</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is due to program design.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the advance correction. It improves the check logic based on selection parameters in the settlement run.</p>", "noteVersion": 4}, {"note": "1737204", "noteTitle": "1737204 - PRCTR: Message text FAGL_REORGANIZATION 331 is imprecise", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you change the account assignment of objects of the \"Sales Document\" type, the system issues error message FAGL_REORGANIZATION 331 even though no user is manually editing the sales order and no account assignment of an object of the \"Sales Document (Cross-Company)\" type is being changed at the same time.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Several items of the same sales document are being edited by parallel reorganization processes, and the lock is set at document level.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>You have to start the account assignment change several times.<br/><br/>Import the Support Package or implement the manual correction instructions to make the message text more precise.<br/><br/></p></div>", "noteVersion": 1}, {"note": "1780051", "noteTitle": "1780051 - PRCTR: Incorr balance for purchase orders/scheduling agrmts.", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You start the balance sheet transfer in the reorganization plan. You<br/>notice that the balance of one or more purchase order items<br/>is incorrect.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_PO<br/>CL_FAGL_R_OBJ_TYPE_001_POA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/><br/>Prerequisites:<br/>There are reversals. This must be taken into account as negative amounts to ensure that the balance is determined correctly.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 1}, {"note": "1686377", "noteTitle": "1686377 - PRCTR: Error - get_object_info in assgd PO for sales order", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>A program termination occurs in get_object_info in the class CL_FAGL_R_OBJ_TYPE_001_POA.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/>Profit center reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_PO<br/>CL_FAGL_R_OBJ_TYPE_001_POA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions relevant for your release.</p></div>", "noteVersion": 3}, {"note": "1869309", "noteTitle": "1869309 - PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (IV)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables and payables, the system issues<br/>error message FAGL_REORGANIZATION 566.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the reorganization, rounding differences are triggered. The reason is, for example: As a result of the different summarization due to the additional reorganization characteristics, the tax is not distributed in accordance with the original document split characteristics. This causes inconsistencies and error 566.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the following corrections.</p></div>", "noteVersion": 13}, {"note": "2989795", "noteTitle": "2989795 - PRCTR/SEG: Open items posted only to specifc ledgers not selected during reorganization of open G/L account items (GLOI)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You carry out a reorganization of open G/L account items. Open items posted only to specifc ledgers (NOT posted to the leading ledger) are not selected and reassigned or transfer posted.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BSTAT = L</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">Program error</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p id=\"\">Implement the program corrections.</p>", "noteVersion": 3}, {"note": "1661117", "noteTitle": "1661117 - PRCTR: Short dump DBIF_RSQL_INVALID_RSQL in RKERV002", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you execute the program FAGL_R_COPA_CORRECT, the<br/>short dump \"DBIF_RSQL_INVALID_RSQL\" may occur in the program RKERV002 if too many sales orders exist for the reorganization date.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization, COPA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1838568", "noteTitle": "1838568 - PRCTR: GR/IR account without document splitting", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization for purchase orders. Document splitting in the general ledger is not active. In this case, no balance sheet transfer should be carried out. The system still determines the balances.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>POA, PO</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections. This prevents the balance sheet transfer for purchase orders if document splitting is not active.</p></div>", "noteVersion": 2}, {"note": "2078642", "noteTitle": "2078642 - FAGL_ASSET_MASTERDATA_UPD: Error AA003 (\"Asset ... is being processed by user ...\")", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you execute the report FAGL_ASSET_MASTERDATA_UPD for the initial filling of the profit center and segment, locking errors (AA003) occur for subnumbers if multiple subnumbers exist.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There are several subnumbers in different packages that are not unlocked in parallel.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instructions or import the specified Support Package.</p>\n<p>You can then execute the report again.</p>", "noteVersion": 1}, {"note": "1660365", "noteTitle": "1660365 - PRCTR: Purch. orders with acc.ass. sales order", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have a reorganization plan for profit center reorganization in your company. You want to reorganize all sales orders, which have a profit center different from the profit center in material master. You discover, that purchase orders for these sales orders don't occur in the object lists.<br/><br/>This note handles purchase orders, for which the profit center in the sales order is different than the profit center in the material master data. Purchase orders with same profit centers in sales order and material are handled by note 1666478.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_POA<br/>purchase order - object list</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the provided correction instructions.</p></div>", "noteVersion": 4}, {"note": "1560518", "noteTitle": "1560518 - PRCTR: Too many objects at the first hierarchy level", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When the system generates the object lists in the profit center reorganization, it selects CO objects or sales orders at the first hierarchy level that should actually be reorganized at a lower hierarchy level (that is, automatically) or should not be reorganized at all.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization, derivation hierarchy</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions. The logic that applies when deciding whether an object is to be reorganized at the first hierarchy level or at a lower hierarchy level or not reorganized at all is displayed here in tabular form. In this case, the column \"Hi.Obj.Reo.\" indicates whether the higher-level object (for example, the material of a production order) was reorganized according to the restrictions of the reorganization plan. \"Hi.Obj.Closed\" means that all higher-level objects in the derivation hierarchy are closed, \"Prctr same\" means that the profit center of the object corresponds to that of the higher-level object (before the reorganization) (the higher-level object therefore imparted the account assignment) and \"Obj.\" indicates whether the object was reorganized at the first hierarchy level, at a lower hierarchy level or reorganized at all.<br/><br/>Hi.Obj.Reo.  | Hi.Obj.Closed   | Prctr same  | Obj.<br/>----------------------------------------------------------<br/> Yes       |   No          |   Yes       |  lower HL<br/> Yes       |  Yes        |   Yes       |  first HL<br/> Not Imp.  |  Not Imp.   |   No         |  first HL<br/> No          |  Not Imp.   |   Yes      |  not in reorg<br/><br/>Therefore, an object is reorganized at a lower hierarchy level if there is an object that is not closed imparting the account assignment in the derivation hierarchy. An object is reorganized at the first hierarchy level if there is no object imparting the account assignment or if the object imparting the account assignment and all higher-level objects are closed. If there is an object imparting the account assignment that is not reorganized, the object itself is also not reorganized.<br/><br/>There may be deviations from this general logic for individual object types. For example, assets with an object imparting the account assignment that is closed are not reorganized for objects of the first hierarchy level.  This is because the profit center of the asset must correspond to that of the object imparting the account assignment. If the asset was reorganized at the first hierarchy level, but the object imparting the account assignment was not (because it was closed), this would not be ensured after the reorganization.<br/><br/>In addition, objects are reorganized at the first hierarchy level if they have no higher-level object (for example, assets that are assigned directly to the profit center without having an additional account assignment such as cost center).<br/></p></div>", "noteVersion": 2}, {"note": "1725063", "noteTitle": "1725063 - PRCTR: Error SAPSQL_INVALID_TABLENAME in select_first_level", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have created a reorganization plan in your company. You try to generate the POA object list. An error SAPSQL_INVALID_TABLENAME occurs in method \"select_first_level\" for object type POA.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>SAPSQL_INVALID_TABLENAME<br/>CL_FAGL_R_OBJ_TYPE_001_POA<br/>select_first_level<br/>purchase order with account assignment- object list</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is an error in program</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the provided correction instructions.</p></div>", "noteVersion": 1}, {"note": "1882483", "noteTitle": "1882483 - PRCTR: Clearing documents in goods/invoice receipt account", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization for purchase orders. The determination of the balances for the goods receipt account and the clearing account also takes financial accounting documents into account that have already been cleared. This is incorrect.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_PO, CL_FAGL_R_OBJ_TYPE_001_POA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections. As a result, the documents that have already been cleared are excluded from the selection.</p></div>", "noteVersion": 1}, {"note": "1685927", "noteTitle": "1685927 - PRCTR/SEG: Error messages use term \"Profit Center\"", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You are using the segment reorganization tool. However, when the system shows error messages, the term \"Profit Center\" is used instead of \"Segment\".</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_REORGANIZATION, FAGL_REORG_SEG</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Apply manual instructions first. Then apply the automatic correction instructions.</p></div>", "noteVersion": 2}, {"note": "2883324", "noteTitle": "2883324 - Reorganization Profit Center: Runtime Error BCD_FIELD_OVERFLOW occurs", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the generation phase of the profit center reorganization the error BCD_FIELD_OVERFLOW exception CX_SY_CONVERSION_OVERFLOW is raised.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FAGL_R_OBJ_GENERATE_SIM, rounding differences.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The generation phase of the reorganization executes the document split function to elaborate the relations on the account receivable / payables to the profit center determineing objects. Therefore additional attributes are handled by the document split function, which are more detailed.</p>\n<p>In some rare document situation the resulting rounding differences cannot be passed to the interface. This kind of documents cannot be handled by the related objetcs and needs to be handled as a first level object.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the following correction instruction.</p>", "noteVersion": 3}, {"note": "1482713", "noteTitle": "1482713 - PRCTR: Checking periods (acct assgmt change): Period 3", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you carry out an account assignment change, the system checks whether the previous period (period before the reorganization date) is already closed.<br/>However, in this case, the system checked only period 1 for normal postings.<br/><br/>In period 3, return postings may still be made from CO to FI,<br/>for example, during the allocation or settlement of one CO object to another.<br/>If both CO objects have different profit centers, this affects the financial statement.<br/>For this reason, period 3 (for CO-FI postings) must also be closed.<br/><br/>Furthermore, when you open or close periods, the system issues message GU 506 (\"Posting period 000 is not defined for fiscal year variant &amp;\") if the interval 3 has no entry for some entries.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_EHP4_T001B_COFI, GU 506, interval 3</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have activated the business function 'FIN_GL_CI_2' (New General Ledger Accounting 2) and FIN_GL_REORG_1 (profit center reorganization).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1657805", "noteTitle": "1657805 - PRCTR: additional fields in object lists of purchase order", "noteText": "<div class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\" id=\"DISCLAIMER\"><div class=\"sapMMsgStripMessage\"><span class=\"sapMText sapUiSelectable sapMTextMaxWidth\" dir=\"auto\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1657805&amp;TargetLanguage=EN&amp;Component=FI-GL-REO-MM&amp;SourceLanguage=DE&amp;Priority=04\" style=\"color:var(--sapLinkColor);\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a href=\"/notes/1657805/D\" style=\"color:var(--sapLinkColor);\" target=\"_blank\">/notes/1657805/D</a>.</span></div></div><div><div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You open the object list with purchase orders. You try to display additional fields it the table. The new columns occur but no data is displayed.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Weitere Begriffe\">Other Terms</h3><p>Profit Center Reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_POA<br/>CL_FAGL_R_OBJ_TYPE_001_PO<br/>purchase order - object list</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Lösung\">Solution</h3><p>Implement the provided correction instructions.</p></div></div>", "noteVersion": 1}, {"note": "1822330", "noteTitle": "1822330 - PRCTR/SEG: MESSAGE_TYPE_X during generation of AR/AP", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The generation of receivables/payables terminates with a runtime error. (MESSAGE_TYPE_X - error FAGL_REORGANIZATION 113)<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization<br/>Reorganization<br/>Reorg<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There are process chains with an invoice reference.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.<br/></p></div>", "noteVersion": 2}, {"note": "3195251", "noteTitle": "3195251 - Profit Center Reorganization: Object list generation continues even though reorg plan is already closed.", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have setup the profit center reorganization plan for the certain period. A background job was released for the object list generation and ran repeatedly. <br/>Atfer the closure of the reorganization plan the scheduled background jobs continues to be processed and updating the plan with new objects in status 'Not processed'.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FAGL_R_DISPATCH, FAGL_R_PROCEED</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the advanced correction or upgrade to the corresponding support package.</p>", "noteVersion": 1}, {"note": "1680404", "noteTitle": "1680404 - FAGL_ASSET_MASTERDATA_UPD: AuC of investment measures", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute the report FAGL_ASSET_MASTERDATA_UPD to fill your asset master records with information for segment reporting. Fixed assets that belong to an investment measure cannot be edited and are displayed as incorrect.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>ANLA-XINVM, PRCTR, SEGMENT<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to a program error that occurs only for assets under construction that belong to an investment measure.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections as described in the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1863965", "noteTitle": "1863965 - Incorect numbers of objects in reorganization plan", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the reorganization plan you have total number of objects  and objects not processed for each node of the hierarchy for generation, reassignment and transfer posting. For certain object types like fix asset you find, that these numbers do not correspond to real number of objects in the object list.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_R_PL_OBJLST, FAGL_R_PL_COUNT</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the advance correction</p></div>", "noteVersion": 2}, {"note": "1778408", "noteTitle": "1778408 - PRCTR - Reorg: Runtime error MESSAGE_TYPE_X gen. AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The generation of receivables and payables terminates with the short dump 'MESSAGE_TYPE_X'.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The simulation terminates for a receivable for which the clearing was reset. As a result, the new assignment logic is not performed consistently.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 1}, {"note": "1654065", "noteTitle": "1654065 - PRCTR: Exception cond. NO_OBJ_TYPE in multiple accnt assgmt", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you reassign receivables and payables, the runtime error (dump) RAISE_EXCEPTION occurs in the program CL_FAGL_R_OBJ_TYPE_APAR=======CP, because the exception condition NO_OBJ_TYPE is triggered.<br/>The runtime error occurs when you reassign a receivable or payable that could have received the profit center from several account assignment objects (order and cost center, for example).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorg<br/>Reorganization<br/>Reorganization plan</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The program tries to determine the account assignment from which the profit center originates. The determination fails due to a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/>See also Note 1652700.</p></div>", "noteVersion": 3}, {"note": "1667206", "noteTitle": "1667206 - Usability improvement for download/upload of assignments", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When downloading the assignments list into an XML file on the 'Specific Restrictions' tabpage, the leading zeros are displayed for some numeric characters. And when uploading the assignments list from an XML file, the sequence of the entries is ignored.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Download, Upload, Reorganization Plan, FAGL_R_PLAN_DETAIL.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a programming error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instruction.</p></div>", "noteVersion": 1}, {"note": "1684748", "noteTitle": "1684748 - Transfer posting object list not flagged as processed", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In your reorganization plan, all object lists in the generation and reassignment area are processed, that is, all traffic lights are green. However, during the transfer posting, there is an object list that no longer has unprocessed objects, but is not flagged as processed. This is not possible as there is no additional dependency here.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Derivation hierarchy, FAGL_R_PL_COUNT, XDONE, GET_HIERARCHY</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections.</p></div>", "noteVersion": 2}, {"note": "1578507", "noteTitle": "1578507 - Changing posting period: Warning msg due to reorg missing", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You experience one of the following symptoms:<br/>1) During a period change you expect a warning message due to an existing reorganization. However, the system does not issue this message.<br/>2) Both for the past and the future, the system does not always track the changed period during a reorganization, both for the past and the future.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Period tracking<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections. First, execute the specified manual activity.</p></div>", "noteVersion": 2}, {"note": "1801767", "noteTitle": "1801767 - Profit center reorganization: Scope of functions", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to execute the reorganization of profit centers and want to know which objects and processes are covered by the business function \"FI-GL (New), Profit Center Reorganization and Segment Reports\" (FIN_GL_REORG_1).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Reorganization of profit centers; profit center reorganization; segment reporting; FIN_GL_REORG_1; New GL; New G/L; General Ledger Accounting (new)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You use General Ledger Accounting (new) and manage profit centers as an additional reporting unit. You have activated document splitting.<br/>As of EhP 5, you can find more information about the prerequisites for a profit center reorganization in SAP Library in the application documentation for SAP ERP under \"SAP ERP Central Component -&gt; Accounting -&gt; Financial Accounting (FI) -&gt; General Ledger Accounting (FI-GL) (New) -&gt; Profit Center Reorganization\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With Enhancement Package 5 for SAP ERP 6.0 (EhP5, SAP_APPL 605), the business function \"FI-GL (New), Profit Center Reorganization and Segment Reports\" (FIN_GL_REORG_1) is available. This contains functions for the reorganization of specific financial statement items of affected profit centers. In addition, the objects belonging to the items, for example, material and sales order, as well as the related process chains, are taken into account in the reorganization. As a result, the business function supports the reorganization of profit centers based on General Ledger Accounting (new). However, there are certain exceptions.  <br/>The following provides information about objects, components and processes that are <strong>not</strong> taken into account by the reorganization. (Note the following: This list does <strong>not</strong> claim to specify all possible objects and processes that are <strong>not</strong> taken into account from a functional point of view; this is <strong>not</strong> possible due to the different approaches for implementations, the wide range of business processes in SAP ERP, and due to customer-specific adjustments.)<br/><br/>The \"Reorganization of profit centers\" function includes the change in assignment from </p>\n<ul>\n<li>Assets (but <strong>not</strong> group assets)</li>\n</ul>\n<ul>\n<li>Materials, purchase orders (no service orders and no purchase orders with account assignment category 'U', see also SAP Note 1826570)</li>\n</ul>\n<ul>\n<li>Sales documents (for example, sales order, contract)</li>\n</ul>\n<ul>\n<li>Cost centers</li>\n</ul>\n<ul>\n<li>Cost objects</li>\n</ul>\n<ul>\n<li>WBS elements</li>\n</ul>\n<ul>\n<li>Networks</li>\n</ul>\n<ul>\n<li>Network activities</li>\n</ul>\n<ul>\n<li>Internal orders</li>\n</ul>\n<ul>\n<li>Accrual orders</li>\n</ul>\n<ul>\n<li>CO production orders</li>\n</ul>\n<ul>\n<li>Product cost collectors</li>\n</ul>\n<ul>\n<li>QM orders</li>\n</ul>\n<ul>\n<li>PPS production orders</li>\n</ul>\n<ul>\n<li>Service orders and maintenance orders</li>\n</ul>\n<ul>\n<li>Process orders</li>\n</ul>\n<p><br/>to profit centers and the related transfer postings of financial statement items in general ledger accounting.  There is also the option to reorganize receivables and payables. The reorganization of planning data in general ledger accounting is <strong>not</strong> supported. The reorganization also does <strong>not</strong> currently support</p>\n<ul>\n<li>G/L accounts (also <strong>no</strong> accounts managed on an open item basis)</li>\n<li>Sales and distribution documents with active revenue recognition</li>\n<li>Group assets</li>\n<li>Allocation cycles in general ledger accounting</li>\n<li>Costs for a work order at transaction level (OLC)</li>\n<li>Projects at project definition level (as higher-level object for WBS elements)  </li>\n<li>Business processes</li>\n<li>General cost objects</li>\n<li>Stock transport orders</li>\n<li>Special G/L transactions (except down payments (type A))</li>\n<li>Material stock transfer posting when using cost element category 90</li>\n<li>Material stock transfer posting when using stock in transit (SIT)</li>\n</ul>\n<p><br/>The following are <strong>not</strong> taken into account by the reorganization:</p>\n<ul>\n<li>Objects and data from Real Estate (RE-FX)</li>\n<li>Objects and data from FI-CA</li>\n<li>Objects and data from Joint Venture Accounting (JVA)</li>\n<li>Production and Revenue Accounting (IS-OIL-PRA)</li>\n<li>SD statistics</li>\n<li>Objects and data from Profitability Analysis (CO-PA)</li>\n<li>Objects and data from consolidation (EC-CS and SEM-BCS)</li>\n<li>Objects and data in Business Warehouse (SAP BW)</li>\n</ul>\n<p><br/>In addition, the following restrictions apply for the reorganization of profit centers:</p>\n<ul>\n<li>If postings are no longer to be made to certain profit centers after the reorganization, these profit centers must be locked (for example, using transaction KE52).  Caution: Due to the reset clearing, profit centers to be replaced appear as an account assignment in open items again. During reprocessing of these items, the system issues error messages if the affected profit center is locked. For this reason, you must ensure that no reset of the clearing is executed for non-reorganized items after a reorganization.</li>\n</ul>\n<ul>\n<li>The system does <strong>not</strong> support retroactive reorganization. The reorganization date must be in the future, that is, when creating a reorganization plan, the reorganization date must <strong>not</strong> yet be reached. In the reorganization, all balances that have accrued up to the reorganization date are transfer posted.</li>\n</ul>\n<ul>\n<li>If you have ledger groups with several ledgers that are affected by the reorganization due to transfer postings, certain restrictions apply. For more information, see the application documentation for the profit center reorganization.</li>\n</ul>\n<ul>\n<li>For ledgers of general ledger accounting with non-calendar fiscal years, the following applies: The reorganization date is always at the beginning of the period of the leading ledger. </li>\n</ul>\n<ul>\n<li>For day ledgers, restrictions also apply. For detailed information, see the application documentation for the profit center reorganization.</li>\n</ul>\n<ul>\n<li>During the transfer posting, the reorganization uses as a basis those items and values that were settled by subledger accounting to general ledger accounting. In other words, the reorganization supports the standard derivation for profit centers; subsequent substitutions or derivations of profit centers with Business Add-Ins (BAdIs) are <strong>not</strong> taken into account.</li>\n</ul>\n<ul>\n<li>The reorganization takes into account only profit centers, but <strong>not</strong> partner profit centers.</li>\n</ul>\n<ul>\n<li>A cross-company code reorganization in terms of transfer postings of financial statement items from one company code to another company code is <strong>not</strong> possible.</li>\n</ul>\n<ul>\n<li>The reorganization of profit centers <strong>cannot</strong> be carried out at the same time as the reorganization of segments.</li>\n</ul>\n<ul>\n<li>P&amp;L postings in the reorganization period: For postings to the reorganization period (posting date &gt;= reorganization date), financial statement items are taken into account by the reorganization only if a cost object is assigned in the financial statement item to which a profit center affected by the reorganization is assigned.  P&amp;L postings without a cost object are <strong>not</strong> taken into account.</li>\n</ul>\n<ul>\n<li>Central Finance (CFIN) or other distributed system landscapes are not supported. This means that, for example, reorganized open items are not transferred.</li>\n</ul>\n<p><br/>There are further restrictions for business processes in the areas</p>\n<ul>\n<li>Reorganization of Receivables and Payables</li>\n</ul>\n<ul>\n<li>Reorganization of material and purchase orders</li>\n</ul>\n<ul>\n<li>Reorganization of WIP objects and SD objects</li>\n</ul>\n<ul>\n<li>Reorganization of cost centers</li>\n</ul>\n<ul>\n<li>Reorganization of assets</li>\n</ul>\n<p><br/>For detailed information, see the application documentation for the profit center reorganization.</p></div>", "noteVersion": 7}, {"note": "1479743", "noteTitle": "1479743 - PRCTR: Error FI 026 when opening or closing periods", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you open and close periods in transaction OB52, the system issues the following error message:<br/>\"No controlling area has been assigned to company code &amp;\" (FI 026).<br/><br/>The error occurs if the changed variant contains a company code<br/>that has not been assigned a controlling area.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>General Ledger Accounting (new), new general ledger, reorganization, profit center, OB52</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Due to a reorganization, the periods of affected company codes must not be opened and closed as often as required.<br/>When you open or close a period, the system therefore checks whether this is allowed for all company codes of the variant.<br/>The controlling area has to be derived for the check.<br/><br/>If no controlling area has been assigned to one of the company codes contained in the variant, the system issues the message mentioned above.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Company codes that have not been assigned to a controlling area are not part of the reorganization. As a result, the system does not have to check whether it is allowed to open or close the period for these company codes.<br/><br/>Implement the attached correction instructions to correct the error.</p></div>", "noteVersion": 1}, {"note": "1664060", "noteTitle": "1664060 - PRCTR/SEG: Data inconsistency during generation of AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate receivables/payables, the extended document splitting (table FAGL_R_SPL) for non-credit-side lines or non-debit-side lines (field KOART is not K or D) is set up incorrectly.<br/><br/>This occurs only if the simulation of the extended document splitting fails for these lines (indicator X_SPLINFO).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_R_SPL<br/>Reorganization<br/>Reorg</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1563552", "noteTitle": "1563552 - FM: Incorrect document type in asset reorgaization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In an asset transfer posting within a Public Sector reorganization (reorganization plan type 002), the system does not use the document type from Customizing for the reorganization.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "2055537", "noteTitle": "2055537 - PRCTR/SEG: FAGL_REORGANIZATION 566 (XXI)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you generate receivables and payables, the system does not take technical fields into account. This results in error 566 during reassign.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segment reorganization<br/>Reorganization<br/>Generate<br/>FAGL_REORGANIZATION 566<br/>FAGL_R_SPL<br/>Technical fields</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Indicator XSKRL - posting line not relevant for cash discount? - is not taken into account.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections. You have to implement this SAP Note before generating receivables and payables.</p>\n<p>Consequently, the system treats the receivable or payable as if the profit center account were assigned directly (artificial level 1 object).</p>", "noteVersion": 4}, {"note": "1825167", "noteTitle": "1825167 - PRCTR/SEG: MESSAGE_TYPE_X During Generation of AR / AP (3)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p> The generation of receivables/payables terminates with a runtime error. (MESSAGE_TYPE_X - error FAGL_REORGANIZATION 113)<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization<br/>Reorganization<br/>Reorg<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There are process chains with an invoice reference.<br/>This is a follow-up note for Notes 1822330 / 1824013<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 2}, {"note": "1470469", "noteTitle": "1470469 - Activate reorganization plan type: Message SV 065", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you try to execute the Customizing activity \"Activate reorganization plan type\", the system issues the message \"No entries exist, double-click for long text\". No plan types are displayed.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SV 065, reorganization, plan type, activate</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1807801", "noteTitle": "1807801 - PRCTR: Analysis report for purchase orders II", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>A dump occurs when you execute the program FAGL_R_BALANCE_SPL_POA.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>None</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 1}, {"note": "1756691", "noteTitle": "1756691 - PRCTR/SEG: Dump GLT0 000 BALANCE_CCODE after reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The runtime error (dump) GLT 000 BALANCE_CCODE occurs if you call one of the follow-on functions (for example, clearing) in a receivable or a payable in which an account assignment change took place during a profit center reorganization or a segment reorganization.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>SAPGLT0</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error during the account assignment change.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.<br/><br/>This SAP Note prevents this problem from occurring in future reorganizations.<br/>If a document that had an account assignment change during an earlier profit center reorganization or segment reorganization caused this error, contact SAP Support.</p></div>", "noteVersion": 1}, {"note": "1721168", "noteTitle": "1721168 - PRCTR/SEG: Reassignment AP/AP: Unjustified error messages", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables and payables with an invoice reference (partial payments, residual items), the system issues error messages that do not apply.<br/><br/>In particular, the following messages are mixed up:</p> <ul><li>FAGL_REORGANIZATION 533</li></ul> <ul><li>FAGL_REORGANIZATION 539</li></ul> <ul><li>FAGL_REORGANIZATION 556</li></ul> <ul><li>FAGL_REORGANIZATION 559</li></ul> <ul><li>FAGL_REORGANIZATION 561</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Segment reorganization<br/>Profit center reorganization<br/>Reorg<br/>Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1663555", "noteTitle": "1663555 - PRCTR/SEG: Error message when reassigning AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you reassign receivables and payables, the system issues the following error messages, among others:</p> <ul><li>FAGL_REORGANIZATION 533</li></ul> <ul><li>FAGL_REORGANIZATION 539</li></ul> <ul><li>FAGL_REORGANIZATION 559</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorg<br/>Profit center reorganization<br/>Segment reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a design error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>After you implement the correction instructions, a more stable mechanism for finding and changing the document splitting information is used for new reorganization plans.</p></div>", "noteVersion": 2}, {"note": "1775191", "noteTitle": "1775191 - PRCTR: incorrect balances of purch. order for ext. services", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have created a reorganization plan in your company. After the reassignment of purchase orders with account assignment (POA) and balance determination you encounter errors in the balances  - for the case of POA for external services. The purchase order is reassigned but no balance is calculated.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_POA<br/>Object list POA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the provided correction instructions.</p></div>", "noteVersion": 1}, {"note": "1687685", "noteTitle": "1687685 - FAGL_ASSET_MASTERDATA_UPD: Assets for investment measure", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute the report FAGL_ASSET_MASTERDATA_UPD to fill your asset master records with information for segment reporting. Fixed assets that belong to an investment measure cannot be edited and are displayed as incorrect.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>ANLA-XINVM, PRCTR, SEGMENT<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to a program error that occurs only for assets under construction that belong to an investment measure.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections as described in the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1655348", "noteTitle": "1655348 - PRCTR: CO-PA document in SD incoming orders", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the CO-PA integration in incoming sales orders. You change a sales order item that was already reorganized so that a CO-PA line item is written. This line item receives the old profit center instead of the new profit center.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have implemented pilot note 1566107.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the Support Package or the advance correction.</p></div>", "noteVersion": 1}, {"note": "1647747", "noteTitle": "1647747 - Performance improvement for active BF FIN_GL_REORG_1", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have activated the business function FIN_GL_REORG_1. This SAP Note improves the performance outside the profit center reorganization because required checks regarding the profit center reorganization are made more efficient.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization, runtime, segment reporting<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Reason: There is a program error.<br/>Prerequisite: You have activated the business function FIN_GL_REORG_1.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections.</p></div>", "noteVersion": 1}, {"note": "2127046", "noteTitle": "2127046 - FAGL_ASSET_MASTERDATA_UPD: Navigation to an incorrect asset", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You execute the report FAGL_ASSET_MASTERDATA_UPD. When you navigate to an asset master record, the system may display an incorrect asset.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PRCTR, SEGMENT</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is a program error, which may occur if the output list is filtered and you then want to navigate to an asset.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the corrections as described in the attached correction instructions.</p>", "noteVersion": 2}, {"note": "1810392", "noteTitle": "1810392 - PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (II)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables and payables, the system issues<br/>error message FAGL_REORGANIZATION 566.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to the error analysis.<br/><br/>This is a supplement to SAP Notes:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>1808980</td><td> PRCTR/SEG:  Error message FAGL_REORGANIZATION 566</td></tr> <tr><td>1797558</td><td> PRCTR/SEG:  FAGL_SPLINFO - Incorrect profit center/segment after reorg</td></tr> </table> <p><br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>This SAP Note enables SAP Support to analyze and solve<br/>the cause of the error.<br/></p></div></div>", "noteVersion": 1}, {"note": "2127072", "noteTitle": "2127072 - The settlement rule to CO-PA has incorrect characteristics after reassignment", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You executed profit center reorganization. As part of the reassignment  of the internal, productive or other types of order the settlement rule to CO-PA is updated with the new Profit center. During the determination of the profitability segment also characteristic derivation is called in order to derive all characteristics again according the new profit center. It might be disruptive in case, that the derivation steps are set up in a way, that might overwrite the characteristics, which should be unchanged. As an example is the productive order, where material number is changed and differs from the one from the order.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PC reorganization, settlement rule,  K_SRULE_PSG_REALIGN, profitability segment</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is due to program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the advance correction.</p>", "noteVersion": 1}, {"note": "1744690", "noteTitle": "1744690 - Poorer performance after activating a business function", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have activated one of the business functions FIN_GL_REORG_1, FIN_GL_REORG_SEG or PSM_FM_REASSIGN, and you notice a deterioration in performance even though the function underlying the business function is not used.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Performance, switch, CL_FAGL_SWITCH_CHECK, CL_PSM_SWITCH_CHECK</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have activated one of the following business functions:</p> <ul><li>FIN_GL_REORG_1; but you do not use the profit center reorganization</li></ul> <ul><li>PSM_FM_REASSIGN; but you do not use the Funds Management account assignment change</li></ul> <ul><li>FIN_GL_REORG_SEG; but you do not use the segment reorganization.</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections.<br/>Note that manual tasks may be required.</p></div>", "noteVersion": 1}, {"note": "1776393", "noteTitle": "1776393 - PRCTR: Splitting inf. for open items of POAs not updated", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have created a reorganization plan in your company. After the reorganization of purchase orders with account assignment (POA) you encounter errors in the splitting information of open items. For some of the Purchase Orders the table FAGL_SPLINFO has not been updated correctly with the new Profit Center.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_POA<br/>Object list POA<br/>FAGL_SPLINFO</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program Error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the provided correction instructions.</p></div>", "noteVersion": 1}, {"note": "2100111", "noteTitle": "2100111 - Adding of an additional field in the object list display results in a shortdump", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you display the object list of a segment reorganization plan and try to add the object type as an additional field in the field catalogue for a display you get get the error message FAGL_REORGANIZATION 001 saying 'Object type 001/GLOI does not exist'.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segment reorganization, Additional fields, Object list, GET_ADDITIONAL_FIELDS, GLOI</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is a program error caused by the fact that the method GET_ADDITIONAL_FIELDS of the object type GLOI of a segment reorganization is inherited from the profit center reorganization class  CL_FAGL_R_OBJ_TYPE_APAR so the object type '001' was passed instead of '003'.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this correction manually or download the corresponding support pack.</p>", "noteVersion": 3}, {"note": "1641528", "noteTitle": "1641528 - Lock entry for processing object list does not exist", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>After you have opened an object list from the processing of a reorganization plan, you close the window for planned processing again. The object list is still available, but the processing lock no longer exists.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Locking concept, lock<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1660943", "noteTitle": "1660943 - Runtime error DBIF_RSQL_INVALID_RSQL during reassignment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you execute the reassignment function, you receive runtime error DBIF_RSQL_INVALID_RSQL. This problem can occur at different points, but always affects a SELECT instruction in a class that starts with the name CL_FAGL_R_.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization, packaging, XDIALOG<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions. See also the manual post-implementation steps.</p></div>", "noteVersion": 2}, {"note": "1640228", "noteTitle": "1640228 - PRCTR/SEG: Warning message \"Document was not simulated\"", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When receivables and payables are generated, the system often issues warning message FAGL_REORGANIZATION 503 (\"Document was not simulated\"), and several receivables or payables are displayed at the first hierarchy level even though this is not required.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit Center reorganization<br/>Segment reorganization<br/>Profit center organization<br/>Segment organization<br/>Reorg<br/>FAGL_REORGANIZATION503<br/>FAGL_REORGANIZATION 503<br/>Extended document splitting</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/>When receivables and payables are generated, they are simulated with extended document splitting characteristics to determine from which objects the relevant account assignments (profit center/segment) were derived.<br/><br/>For security reasons, the simulation result is compared to the results of the original document splitting.<br/>If there are differences, the system displays warning message FAGL_REORGANIZATION 503 (\"Document was not simulated\") and the receivable or payable is displayed at the first hierarchy level.<br/>However, the validation is too strict.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/><br/>See other SAP Notes for this symptom under \"Related Notes\".</p></div>", "noteVersion": 1}, {"note": "1620467", "noteTitle": "1620467 - Completion progress is not calculated correctly", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>On the reorganization plan overview, the completion progress is not calculated correctly.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Overall level of completion, plan overview, FAGL_R_PLAN_OVERVIEW.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instruction.</p></div>", "noteVersion": 1}, {"note": "1798910", "noteTitle": "1798910 - PRCTR: Object type not determined (FAGL_REORGANIZATION505)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate payables or receivables, you receive the following error or warning message:<br/>\"Object type not determined for payable/receivable &amp;1 &amp;2 &amp;3\" (FAGL_REORGANIZATION505).<br/>The document is derived from a payment run. The original document is a payment request with a filled subgroup for a fixed asset (field ANLN2). The fixed asset itself is not filled (field ANLN1).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Profit center reorganization<br/>Reorg<br/>ANLN1, ANLN2<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 3}, {"note": "1677834", "noteTitle": "1677834 - PRCTR/SEG: Inconsistencies in reorganization of receivables", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you reassign and/or make a transfer posting for receivables, data inconsistencies occur:</p> <ul><li>Subsequent processes (for example, the clearing) are not performed in all new profit centers and/or</li></ul> <ul><li>multiple transfer postings are made for amounts for a receivable and/or</li></ul> <ul><li>The system issues the following error message for some receivables: \"Receivable/payable &amp;1 has incorrect status 40 for being transferred\" (FAGL_REORGANIZATION 552)</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorg<br/>Profit center reorganization<br/>AR<br/>Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/><br/>Note the following:<br/>This note ensures that this problem does not occur either for future reassignments and transfer postings.<br/>Contact SAP Support to remove an existing inconsistency.</p></div>", "noteVersion": 1}, {"note": "1667603", "noteTitle": "1667603 - Archiving in reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the new general ledger accounting, you use the reorganization and want to archive completed reorganization plans. However, you have not found an archiving object for the reorganization in transaction SARA.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Archiving object, FAGL_REORG, FIN_GL_REORG1, FIN_GL_REORG_SEG, PSM_FM_REASSIGN</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Previously, this function did not exist.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions or import the attached Support Package. If you implement the correction instructions, note the manual steps.<br/></p></div>", "noteVersion": 3}, {"note": "1656973", "noteTitle": "1656973 - Authorization check", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>A runtime error occurs in the function module SUSR_AUTHORITY_CHECK_SIMULATE, which is called from the class CL_FAGL_R_PLAN_FACTORY.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Exceptions, authorization check<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections.</p></div>", "noteVersion": 1}, {"note": "1707428", "noteTitle": "1707428 - BAdI for overwriting the transaction type", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In Customizing, you define the transaction type (field RMVCT) for acquisitions and retirements that is to be used to make a transfer posting for balances.<br/>However, you want to override the transaction type for some of the balances to be transferred.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/>Reorganization<br/>Reorg<br/>Consolidation</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The relevant function is missing.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The BAdI FAGL_R_REPOST has been added. Implement the attached correction instructions to make the BAdI available in your system.<br/><br/>After you have implemented the correction instructions, you can implement the BAdI. To do this, proceed as follows:</p> <ol>1. Call transaction SE19.</ol> <ol>2. Under \"Create Implementation\", enter the following value in the \"New BAdI - Enhancement Spot\" field:<br/>FAGL_REORGANIZATION</ol> <ol>3. Choose \"Create Impl.\".</ol> <ol>4. Enter a name for the enhancement implementation, for example, ZFAGL_REORGANIZATION.<br/>Enter a short text of your choice.</ol> <ol>5. Enter a name in the \"BAdI Implementation\" field, for example, Z_FAGL_REORGANIZATION_REPOST.<br/>Enter a name for the implementation class, for example, ZCL_FAGL_REORGANIZATION_REPOST.<br/>Select the BAdI FAGL_R_REPOST.</ol> <ol>6. On the \"Enh. Spot Element Definitions\" tab page, expand the tree Z_FAGL_REORGANIZATION_REPOST and click \"Implementing Class\".<br/>Double-click the following method: IF_FAGL_R_REPOST~CHANGE_MOVEMENT_TYPE<br/>Implement the method to overwrite the transaction types according to your requirements.</ol> <ol>7. Save and activate all objects.</ol></div>", "noteVersion": 2}, {"note": "1549019", "noteTitle": "1549019 - Performance improvement regarding checks for reorg plan", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have activated the business function for the profit center reorganization. For some activities, such as posting to CO objects, the performance is reduced significantly since then.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FIN_GL_REORG_1, performance<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1810739", "noteTitle": "1810739 - Error message KI013 during CO posting", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You encounter the cancel message KI 013 'Internal program error -&gt; contact SAP' during CO posting.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>PC reorganization, KI013, FAGL_REORGANIZATION 707</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><b>Prerequisites:</b></p> <ul><ul><li>reorganization plan is active</li></ul></ul> <ul><ul><li>message control for the message FAGL_REORGANIZATION 707 has been changed to warning</li></ul></ul> <p><b>Reason:</b><br/>This is due to program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the advance correction.</p></div>", "noteVersion": 1}, {"note": "1655685", "noteTitle": "1655685 - Object lists are not indicated as processed", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the reorganization process, some object types are not indicated as processed; this means that the relevant traffic light does not change to green. If all object types have been indicated as processed in the previous step (generation in the account assignment change step or account assignment change in the transfer posting step), the problem cannot be solved either by executing the process step again for all object types.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Initialize, constructor, XERROR_GEN, XERROR_REASSIGN, XERROR_REPOST<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 2}, {"note": "1628542", "noteTitle": "1628542 - Error AIST 009 for account assignments to investment measure", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system issues error AIST 009 (\"Profit center is not unique (&amp;1/&amp;2)\") when you create or change an asset. The same error may occur when you execute the report FAGL_ASSET_MASTERDATA_UPD.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>ANLA-EAUFN, ANLA-POSNR, ANLZ-PRCTR, AIST_DERIVE_PRCTR_SEGMENT, ANLA-XINVM, AIST009, AIST 009, AIST016, AIST 016, AIST017, AIST 017<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The relevant check is too strict. The profit center from the investment order (ANLA-EAUFN) or from the investment WBS element (ANLA-POSNR) is not normally used for the account assignment and should therefore also not cause an error due to it not being unique.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections as described in the attached correction instructions. The system then responds as follows for assets that do not belong to any investment measure:</p> <ul><li>The profit center is first derived from the following objects (if it is available and the profit center is not already filled):</li></ul> <ul><ul><li>WBS element (ANLZ-PS_PSP_PNR2)</li></ul></ul> <ul><ul><li>Internal order (ANLZ-CAUFN)</li></ul></ul> <ul><ul><li>Key for real estate (ANLZ-IMKEY)</li></ul></ul> <ul><ul><li>Cost center (ANLZ-KOSTL)</li></ul></ul> <ul><li>Only then will the system perform a derivation from the account assignments to investment measure for an initial profit center:</li></ul> <ul><ul><li>Investment WBS element (ANLA-POSNR)</li></ul></ul> <ul><ul><li>Investment order (ANLA-EAUFN)</li></ul></ul> <p><br/>For assets that belong to an investment measure, the sequence mentioned above in used in reverse for the derivation.<br/></p> <ul><li>If the profit center is not unique, the system issues the following error message in the future for differences:</li></ul> <ul><ul><li>AIST 016: The system issues this message if the asset does not belong to an investment measure and the difference is caused by the investment WBS element or the investment order. You can deactivate this error message using transaction OBA5.</li></ul></ul> <ul><ul><li>AIST 017:  The system issues this message if the asset belongs to an investment measure and the difference is not caused by the investment WBS element, the investment order or the profit center in the asset master record. You can deactivate this error message using transaction OBA5.</li></ul></ul> <ul><ul><li>AIST 009: The system issues this message for all other differences.This error message cannot be deactivated.</li></ul></ul></div>", "noteVersion": 2}, {"note": "1761757", "noteTitle": "1761757 - PRCTR: Reorganization of purchase orders for ext. services", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You try to include the purchase order items for services in the profit center reorganization. The balances are not reorganized correctly.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_POA<br/>Purchase order with account assignment - object list</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This function is not yet supported.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 1}, {"note": "3088873", "noteTitle": "3088873 - Segment reorganization: AA347 with account assignment change for assets", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\"> When you make an account assignment change for assets, the system incorrectly issues error message AA347 even though the fiscal year change was already carried out.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">REORG, AA 347</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">There is a reorganization at the fiscal year change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instructions or import the specified support package.</p>\n<p>After you have implemented the corrections, the system will check again the correct fiscal year. The system will also check with the actual posting date of the transfer posting in Asset Accounting and no longer with the reorganization date. The posting date is either the reorganization date or if this is older the current system date.</p>\n<p id=\"\"></p>", "noteVersion": 1}, {"note": "1759519", "noteTitle": "1759519 - Source document of transfer posting is not displayed", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You are in the document display (transaction FB03) for a transfer posting document from the reorganization. You choose \"Environment -&gt; Document Environment -&gt; Original Document\" to access the original document.<br/>However, no data is displayed in the new session; the selection parameters for the company code and the transfer posting document numer are filled correctly but the transfer posting fiscal year is incorrectly set to initial.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_R_DOCUMENT_SENDER, AC_DOCUMENT_SENDER, TTYP, T001-XGJRV<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/>The indicator T001-XGJRV is not set for this company code.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1768369", "noteTitle": "1768369 - Object list not marked as 'Completely processed'", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>After a successful generation or reassignment, an object list is not indicated as completely processed (green traffic light). There may be various reasons for this; in rare cases, this may be caused by a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>XIGNORE; FAGL_R_S_MESSAGE; FAGL_R_S_MESSAGE2</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections.</p></div>", "noteVersion": 2}, {"note": "1749843", "noteTitle": "1749843 - Termination when generating payables", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have started the generation of payables or receivables from the reorganization plan. The background job is terminated.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1669474", "noteTitle": "1669474 - PRCTR: error in get_object_info - purch. ord. w. network act", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have a reorganization plan in your company. You try to reorganize receivables and payables and get an error in method GET_OBJECT_INFO. This happens for purchase orders assigned to a network activity.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_PO<br/>CL_FAGL_R_OBJ_TYPE_001_POA<br/>purchase order - object list</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a programm error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the provided correction instructions</p></div>", "noteVersion": 2}, {"note": "1644579", "noteTitle": "1644579 - Last refresh label is not aligned with table above", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>On the 'Object Lists' tabpage of displaying reorganization plan, the label for last refresh information is not aligned with table above.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Alignment, Last Refresh, FAGL_R_PLAN_DETAIL.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instruction.</p></div>", "noteVersion": 1}, {"note": "1627187", "noteTitle": "1627187 - PRCTR: append dump in CL_FAGL_R_OBJ_TYPE_001_MAT", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the class CL_FAGL_R_OBJ_TYPE_001_MAT method IF_FAGL_R_OBJ_TYPE~REASSIGN_MD_AND_GET_BALANCES there is programming error. The ABAP command 'Append' is used for sorted table. It causes dump.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit Center Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program Error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions accordingly</p></div>", "noteVersion": 4}, {"note": "1832076", "noteTitle": "1832076 - PRCTR: Additional local currencies for purchase orders", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization for purchase orders. Rounding differences occur for the second and third local currency in the transfer posting.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>PO, POA, OB22</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In transaction OB22, you have activated the update for the second and third local currency.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 2}, {"note": "1797558", "noteTitle": "1797558 - PRCTR/SEG: FAGL_SPLINFO - incorr. Prctr/segment after reorg", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>After a reorganization, FAGL_SPLINFO contains entries with an incorrect profit center or segment. In the reorganization table FAGL_R_SPL, the new profit center and the new segment are set correctly.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_R_SPL FAGL_SPLINFO</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>A reconciliation of the profit center or segment between the reorganization data and FAGL_SPLINFO is missing.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections or perform the manual activities. A reconciliation with the reorganization data is executed after the generation of the new profit centers/segments of the FAGL_SPLINFO. If there is no match, the system issues an error message.<br/><br/></p></div>", "noteVersion": 2}, {"note": "1736015", "noteTitle": "1736015 - PRCTR: purch. orders not reorganized - error in acc. assign.", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You try to reorganize purchase orders with account assignment (POA) in the reorganization plan in your company. You cannot find these purchase orders are under their superior object - for example internal order. The system encounters an error while determining the account assignment. Therefore they are reorganized as first level objects.<br/>You want to reorganize them under the superior object type.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_POA<br/>object list - POA - Purchase orders with account assignment<br/>determination superior object</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is an error in determining the account assignment</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the provided correction instructions</p></div>", "noteVersion": 1}, {"note": "1856825", "noteTitle": "1856825 - PRCTR: Object type not determined (FAGL_REORGANIZATION505)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate receivables or payables, the system issues<br/>the following error or warning message:<br/>\"Object type not determined for payable/receivable &amp;1 &amp;2 &amp;3\"<br/>(FAGL_REORGANIZATION505).<br/>The document was created with transaction FB01 with reference to a  purchase order with account assignment and in addition to the purchase order, is assigned to the WBS object.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Profit center reorganization<br/>Reorg<br/>EBELN, EBELP, and ZEKKN / PROJK</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 3}, {"note": "1722628", "noteTitle": "1722628 - Reorganization: Generating object list for requests", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate the object list for assets as part of the reorganization, the list contains assets as second level objects that are not supposed to be reorganized.<br/>This case occurs if it is an asset for investment measures. The profit center from the investment order (ANLA-EAUFN) or from the investment WBS element (ANLA-POSNR) is not normally used for the account assignment and should therefore also not cause an error since it is not unique. However, you can set that the profit center is supposed to be derived from the origin and not from the cost center (to do this, see SAP Note 1628542).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization of asset, investment measure</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You use an asset for an investment measure with a cost center and a WBS element or an order as the origin object, and different profit centers are assigned to them. The profit center from the origin object is supposed to be updated in the asset master record.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections in accordance with the attached correction instructions.</p></div>", "noteVersion": 4}, {"note": "1772403", "noteTitle": "1772403 - PRCTR/SEG: Error message FAGL_REORGANIZATION 575", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system issues error message FAGL_REORGANIZATION 575 even though all objects of the first hierarchy level for a receivable or payable have the status 20 or 60.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorg<br/>Reorganization<br/>Profit center reorganization<br/>Segment reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1654444", "noteTitle": "1654444 - PRCTR: Error FAGL_REORGANIZATION545 during reassignment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables to sales and distribution documents, the system issues error message FAGL_REORGANIZATION 545:<br/>\"Reassignment: Error in document splitting (sales and distribution document is in reorganization plan (FAGL_REORGANIZATION 008))\"</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorg<br/>Reorganization<br/>Object type AR<br/>Object type SO</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 3}, {"note": "1864855", "noteTitle": "1864855 - PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (III)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables and payables, the system issues<br/>error message FAGL_REORGANIZATION 566.<br/>This is an follow-on error of SAP Note 1810392.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to the error analysis.<br/><br/>This is a supplement to SAP Notes:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>1808980</td><td> PRCTR/SEG: Error message FAGL_REORGANIZATION 566</td></tr> <tr><td>1797558</td><td> PRCTR/SEG: FAGL_SPLINFO - Incorrect profit center/segment after reorg</td></tr> <tr><td>1810392</td><td> PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (II)</td></tr> </table> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>This SAP Note enables SAP Support to analyze and solve<br/>the cause of the error.<br/></p></div></div>", "noteVersion": 3}, {"note": "1757942", "noteTitle": "1757942 - Runtime err. TSV_TNEW_PAGE_ALLOC_FAILED due to too many objs", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system issues the runtime error TSV_TNEW_PAGE_ALLOC_FAILED during the generation or reassignment process in a reorganization plan. The termination occurs in a class with the prefix CL_FAGL_R_....<br/>The reason for this is that many objects (approximately one million) are to be processed at the same time.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Data volumes, DS_BUFFER, DS_BUFFER_DB<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The memory consumption can be optimized.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1471414", "noteTitle": "1471414 - Error messages for fixed assets", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>If several fixed assets cannot be reassigned, the system issues an error message for one fixed asset only. This fixed asset is correctly transferred to status 25 (reassignment failed). However, the other fixed assets receive status 40 (balance determination successful), which is incorrect.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization of profit centers, reorganization of FM documents, profit center reorganization, reassignment of fixed assets</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1872614", "noteTitle": "1872614 - PRCTR/SEG: Error message FAGL_REORGANIZATION 544", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables and payables, the system issues<br/>error message FAGL_REORGANIZATION 544.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>New GL<br/>General Ledger Accounting (new)<br/>Realignment<br/>Reorg<br/>Reorganization<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SAP Note 1599168<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.<br/></p></div>", "noteVersion": 3}, {"note": "1810191", "noteTitle": "1810191 - Short dump ITAB_ILLEGAL_SORT_ORDER when opening a period", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have made a change in transaction FAGL_EHP4_T001B_COFI (Open and Close Posting Periods). Saving the change caused a short dump.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>An error occurs when you enter data into a sorted table or make changes to it.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the advance corrections.</p></div>", "noteVersion": 1}, {"note": "1693804", "noteTitle": "1693804 - PRCTR: Incomplete standard derivation hierarchy", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The standard derivation hierarchy (version 001) for plan type 001 is incomplete; the following nodes are currently missing:</p> <ul><li>Object type POA (purchase orders assigned to account) with subobject type AP (payable) below each SO node (sales and distribution document),<br/>for example, the subtree MAT-SO-POA-AP</li></ul> <ul><li>Object type PO (purchase orders not assigned to account) at first hierarchy level with subobject type AP</li></ul> <ul><li>Object type POA (purchase orders assigned to account) at first hierarchy level with subobject type AP</li></ul> <ul><li>Object type AR (receivable) below node O01 (internal order)</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorg<br/>Reorganization<br/>Derivation hierarchy, hierarchy<br/>FAGL_REORG_1_FAGL_R_DERH</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Delivery Customizing is incomplete.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions. In particular, note the manual post-implementation steps.<br/><br/>Note that existing reorganization plans will continue to use the derivation hierarchy that was valid at the time of creation.<br/>If you want to use the derivation hierarchy that is changed with this note, you must create a new reorganization plan (and delete the existing reorganization plan, if required).</p></div>", "noteVersion": 4}, {"note": "1585262", "noteTitle": "1585262 - Additional fields are not displayed", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In Customizing, you added and selected display fields for each object type. However, these fields are not filled with values even though they are available as a column in the view of the object list.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization, V_FAGL_R_OBJTRES, V_FAGL_R_OBJTDIS<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1555601", "noteTitle": "1555601 - PRCTR: Error message appears while generating materials", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>While generating the material object list in REORG, you will get error message FAGL_REORGANIZATION 201.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit Center Reorganization, generate material object list</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>No valuation area or plants found for the given company code while generating material object list, which causes the error message. However it should not be considered as an error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction accordingly.</p></div>", "noteVersion": 1}, {"note": "1666478", "noteTitle": "1666478 - PRCTR: Missing purchase orders with SO acct assignment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have reassigned the materials successfully and you have also reassigned the purchase orders. However, you have discovered that some purchase orders are missing. These purchase orders are assigned to sales orders, and the profit center of each material is identical to that of the sales order.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_PO<br/>purchase order - object list</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instruction provided</p></div>", "noteVersion": 2}, {"note": "1848703", "noteTitle": "1848703 - PRCTR: Incorrect profitability segment in sales document", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment, the system generates a profitability segment in a sales document item that should not actually have a profitability segment.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions or import the Support Package.</p></div>", "noteVersion": 2}, {"note": "1826570", "noteTitle": "1826570 - PRCTR: Item categories of POs with account assignment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization. Certain purchase orders with account assignment are not included in the reorganization even though the account assignments contain a profit center from the reorganization run.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Service purchase order, purchase order for external service</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is not a program error. The following purchase orders are excluded from the reorganization:<br/>- Service purchase orders (ekpo-pstyp = 9)<br/>- Purchase orders with an unknown account assignment category (ekpo-knttp = 'U').</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The current scope of functions of the profit center reorganization does not cover ALL values of service purchase orders, in particular if the service entry takes place via service entry sheets. Program-technically, all service purchase orders must be excluded. If you want to include service purchase orders in the reorganization, SAP can check whether this function can be activated. In this case, send a customer message to SAP Support under the component FI-GL-REO-MM.</p></div>", "noteVersion": 1}, {"note": "1797601", "noteTitle": "1797601 - PRCTR: Incorrect balance sheet posting for materials", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute the reorganization run for profit centers. The system posts inexplicable transfer postings for balances.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><ul><li>You start the account assignment change for materials in a period that is not the same as the reorganization period.</li></ul> <ul><li>The split valuation is active for the materials to be reorganized.</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 2}, {"note": "1790465", "noteTitle": "1790465 - PRCTR/SEG: Transfer posting of cleared items", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You notice that a transfer posting is made for balances for receivables and payables even though these receivables and payables were already cleared at the time of the account assignment change.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/>Profit center reorganization<br/>Reorganization<br/>Segment reorganization<br/>AP/AR</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/><br/>This SAP Note prevents the error from occurring in the future. However, it does not correct the amounts that were already transfer posted.<br/>If you notice the errors in your system, implement this SAP Note first and then create a customer message.</p></div>", "noteVersion": 1}, {"note": "1592080", "noteTitle": "1592080 - PRCTR: Message FAGL_REORGANIZATION 514 and 515", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate the object types AR and AP (receivables and payables), the system simulates document splitting with extended document splitting characteristics (additional account assignments).<br/><br/>For security reasons, the result of the simulation in the entities \"profit center\" and \"segment\" is always compared with the result of document splitting in the case of a posting of the document.<br/><br/>During this, minor rounding differences may occur. These differences are ignored.<br/>In the case of larger rounding differences, the system issues warning message FAGL_REORGANIZATION 513 or 514.<br/><br/>However, the warning messages are also issued for small negative rounding differences.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>New GL<br/>New general ledger<br/>Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 2}, {"note": "3131931", "noteTitle": "3131931 - Missing transfer between organizational units in reserves areas", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You manage reserves in a separate depreciation area (pure reserves area).</p>\n<p>Your system is set up in such a way that when the account assignment in the asset master record is changed, a transfer is automatically made to the new objects. However, this automatic transfer does not take place in the reserves area.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">Reserves, reserves area, BUHBKT 4, XZORG, OCAB, OCZU, profit center, PRCTR, segment, FIN_AA_PARALLEL_VAL, S2I</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">Program error</p>\n<div class=\"longtext\">\n<p>You use Asset Accounting (new) (FIN_AA_PARALLEL_VAL).</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the corrections from SAP Note <a href=\"/notes/2818581\" target=\"_blank\">2818581</a> or import the relevant support package. The corrections also solve this problem.</p>", "noteVersion": 1}, {"note": "1626583", "noteTitle": "1626583 - PRCTR/SEG: Error FAGL_REORGANIZATION 533 during reassignment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables and payables,<br/>the error FAGL_REORGANIZATION 533 occurs:<br/>Document splitting information not found for receivable/payable &amp;1<br/><br/>Table FAGL_R_SPL contains duplicate entries for this receivable<br/>or payable. Several entries have the indicator<br/>XSPLINFO = X.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>New General Ledger Accounting<br/>NewGL<br/>Reorganization<br/>Reorg</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1658386", "noteTitle": "1658386 - PRCTR/SEG: Warning message \"Document was not simulated\"", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When receivables and payables are generated, the system often issues warning message FAGL_REORGANIZATION 503 (\"Document was not simulated\"), and very many receivables or payables are displayed at the first hierarchy level even though this is not required.<br/><br/>This occurs particularly if the receivable or payable was posted with additional currencies that are not updated in Financial Accounting (for example, currencies of the material ledger).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Profit center organization<br/>Segment organization<br/>Reorg<br/>FAGL_REORGANIZATION503<br/>FAGL_REORGANIZATION 503<br/>Extended document splitting</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/>When receivables and payables are generated, they are simulated with extended document splitting characteristics to determine from which objects the relevant account assignments (profit center/segment) were derived.<br/><br/>For security reasons, the simulation result is compared to the results of the original document splitting.<br/>If there are differences, the system displays warning message FAGL_REORGANIZATION 503 (\"Document was not simulated\") and the receivable or payable is displayed at the first hierarchy level.<br/><br/>If the receivable or payable was posted with additional currencies that are not updated in Financial Accounting (for example, currencies of the material ledger), the simulation result inevitably differs from the original document splitting and the system issues the warning mentioned above.<br/><br/>However, this validation is too strict.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1672235", "noteTitle": "1672235 - Dump ASSERTION_FAILED during reassignment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You perform the reassignment within the reorganization. If several, but not all object lists are selected for the reassignment, the termination 'ASSERTION_FAILED' may occur in the method UPDATE_COUNT_P of the class CL_FAGL_R_OBJLIST.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Dump, profit center, segment, CL_FAGL_R_OBJLIST=============CP, CL_FAGL_R_OBJLIST=============CM02V, UPDATE_COUNT_P</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You activated a business function for the reorganization of general ledger account assignments (FIN_GL_REORG_1 or FIN_GL_REORG_SEG) or you activated the business function PSM_FM_REASSIGN.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions or import the relevant Support Package.</p></div>", "noteVersion": 1}, {"note": "2045737", "noteTitle": "2045737 - PRCTR/SEG: AP/AR reassignment - reading of split information", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The split information is not transferred according to the database key.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization</p>\n<p>CL_FAGL_R_SPLIT_REORG: GET_SPLIT_INFO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The table is used for the comparison including the currency type. Sorting must therefore take the currency type into account.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached source code.</p>", "noteVersion": 5}, {"note": "1480048", "noteTitle": "1480048 - PRCTR: Search using standard hierarchy does not work", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In a reorganization plan you search for a profit center using the search help of the standard hierarchy.<br/>The system issues an error message stating the following: It is not possible to send screen SAPMSSY0 0120: No Windows system category has been specified<br/> </p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>PRCT, PRCTH, elementary search help, Web Dynpro, search help exit<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error. This search help cannot be used here for technical reasons and is deactivated in this environment.<br/> </p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections and carry out the manual tasks.</p></div>", "noteVersion": 1}, {"note": "1718567", "noteTitle": "1718567 - BAdI FAGL_R_GENERATE: Status cannot be set", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the BAdI FAGL_R_GENERATE and you want to set the status 10, 20 or 60 for objects. However, the objects are already included in the object list with a status lower than 20.<br/>The system does not transfer these changes to the BAdI.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SET_OBJECTS_P, generate, reorganization, profit center, segment<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections.</p></div>", "noteVersion": 2}, {"note": "1589176", "noteTitle": "1589176 - Asset master data with profit center / filling segment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you execute the report FAGL_ASSET_MASTERDATA_UPD to fill the<br/>asset master records with information for the segment reporting,<br/>a termination may occur due to a missing database cursor.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Segment reporting, profit center, segment</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections specified below in your system.</p></div>", "noteVersion": 3}, {"note": "2148718", "noteTitle": "2148718 - Additional info displayed incorrectly", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the Reorganization framework. Additional information about licenses is displayed during the Plan type activation even if it is not relevant for the activated plan type.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This issue is caused by a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the relevant Support Package or implement the attached corrections.</p>", "noteVersion": 1}, {"note": "1721166", "noteTitle": "1721166 - PRCTR/SEG: Error 505 for down payment requests", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You generate the \"Payables\" object list.<br/>The system issues an error message stating that the higher-level object type could not be determined (FAGL_REORGANIZATION 505).<br/><br/>The error occurs for down payment requests for purchase orders with account assignments.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Reorg<br/>Segment reorganization<br/>ZEKKN</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1664739", "noteTitle": "1664739 - PRCTR/SEG: ASSERTION_FAILED when reassigning AP/AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you reassign receivable or payables, the runtime error (dump) ASSERTION_FAILED occurs in the method R_SPLIT_MANUALLY_P of the program CL_FAGL_R_OBJ_TYPE_APAR=======CM026.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Profit center organization<br/>Segment reorganization<br/>Segment organization<br/>Reorg<br/>Reassign, account assignment change<br/>CL_FAGL_R_OBJ_TYPE_APAR</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1853736", "noteTitle": "1853736 - ASSERTION_FAILED during object generation II.", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The generation of the object list terminates with runtime error ASSERTION_FAILED in the method GET_OBJECT_INFO_P of the class CL_FAGL_R_OBJLIST.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>ASSERTION_FAILED, GET_OBJECT_INFO, GET_OBJECT_INFO_P, hierarchy version, object type</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Prerequisite:<br/>Note 1844856 is applied.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Apply the advance correction.</p></div>", "noteVersion": 2}, {"note": "1709894", "noteTitle": "1709894 - PRCTR: Error TYPE_NOT_FOUND for material object list", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have created a reorganisation plan in your company. In customizing you have added a new field for the object type \"Material\" in \"Display Characteristics of an Object Type\". You try to generate the material object list. An Error \"TYPE_NOT_FOUND\" occurs.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_MAT<br/>material object list<br/>display characteristics of object type</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the provided correction instructions</p></div>", "noteVersion": 1}, {"note": "1800223", "noteTitle": "1800223 - PRCTR: Object type not determined (FAGL_REORGANIZATION 505)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate payables or receivables, you receive the following error or warning message:<br/>\"Object type not determined for payable/receivable &amp;1 &amp;2 &amp;3\" (FAGL_REORGANIZATION505).<br/>The document contains account assignment information for a purchase order with account assignment with SD account assignment. This was entered manually in the document and is not complete for the purpose of the purchase order. (Field ZEKKN is initial).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Profit center reorganization<br/>Reorg<br/>EBELN EBELP ZEKKN<br/>VBELN POSNR<br/>Object type SO POA PO<br/>Derivation hierarchy, hierarchy<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/><br/>You have already adjusted the standard derivation hierarchy (version 001) for plan type 001 using SAP Notes 1693804 and 1761156.<br/>However, the following nodes are still missing:<br/></p> <ul><li>Object type AP (Payables) below each node SO (SD order),<br/>Subtrees:</li></ul> <ul><ul><li>MAT-SO-AP (Material)</li></ul></ul> <ul><ul><li>SO-AP (Sales Document)</li></ul></ul> <ul><ul><li>CC-O30-SO-AP (Cost Center)</li></ul></ul> <ul><ul><li>WBS-SO-AP (WBS Element)</li></ul></ul> <ul><ul><li>O01-SO-AP (Internal Order)</li></ul></ul> <ul><ul><li>O30-SO-AP (Maintenance Order)</li></ul></ul> <p><br/><br/><b>Adjustment of derivation hierarchy</b><br/></p> <ul><li>Proceed as described in the manual activities or import the Support Package.</li></ul> <p><br/>Note that existing reorganization plans will continue to use the derivation hierarchy that was valid at the time of creation. If you want to use the derivation hierarchy that is changed with this note, you must create a new reorganization plan (and delete the existing reorganization plan, if required).<br/></p></div>", "noteVersion": 5}, {"note": "1762744", "noteTitle": "1762744 - PRCTR: Object type not determined (FAGL_REORGANIZATION505)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate receivables or payables, the system issues the following warning message for items that have been posted to several account assignment objects (for example, WBS element and cost center):<br/>\"Object type not determined for payable/receivable &amp;1 &amp;2 &amp;3\" (FAGL_REORGANIZATION505).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Profit center reorganization<br/>Reorg<br/>PS_PSP_PNR, KOSTL, KSTRG, AUFNR, NPLNR, AUBEL<br/>CO_KAKST, CO_KAAUF, CO_KAKTR, CO_KANPL, CO_KAPRO, CO_KAKDA<br/>POA, SO<br/>WBS<br/>FAGL_REORGANIZATION 505<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1760882", "noteTitle": "1760882 - <PERSON><PERSON><PERSON> err TSV_TNEW_PAGE_ALLOC_FAILED due to too many objects", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system issues runtime error TSV_TNEW_PAGE_ALLOC_FAILED during the generation or reassignment process in a reorganization plan. The termination occurs in a class with the prefix CL_FAGL_R_.<br/>The reason for this is that many objects (approximately one million) are to be processed at the same time.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Data volumes, first level, CT_OBJECT_STRING<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The memory consumption can be optimized.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1686832", "noteTitle": "1686832 - Minor adjustments in area of reorganization framework", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This SAP Note implements the following adjustments:</p> <ul><li>The call of individual components of the previous, rudimentary development for the topic reorganization is prevented. As a result, functional problems should be avoided in particular.</li></ul> <ul><li>A short dump is prevented when you branch from the document display via the source document from transaction FB03 if the reorganization plan has been archived.</li></ul> <ul><li>A detailed description is provided in the long text of a message. This is displayed if not all conditions are fulfilled when closing a plan.</li></ul> <ul><li>A direct link to all objects of an object list is made possible.</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_REORG023, FAGL_REORGANIZATION013, FAGL_REORGANIZATION032<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections.<br/><b>Note the following:</b> To keep the manual tasks to a minimum, only the required changes are provided. You can obtain the complete functionality only by importing the relevant Support Package.</p></div>", "noteVersion": 3}, {"note": "1665678", "noteTitle": "1665678 - Runtime error ASSERTION_FAILED when changing acct assignment", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the reorganization plan, you change the account assignment for the object type for the receivable or payable.  The background job terminates with a short dump.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_APAR, GET_OBJ_LIST_P</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to a programming error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import the Support Package or implement the correction instructions manually.</p></div>", "noteVersion": 2}, {"note": "1628255", "noteTitle": "1628255 - PRCTR/SEG: Configuring message FAGL_REORGANIZATION 029", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In a reorganization plan under \"General restrictions -&gt; Assignments\", as the old account assignment you entered an account assignment that also occurs as a new account assignment in the assignments table.<br/><br/>Example:<br/><br/>Profit center (old)  Profit center (new)<br/>  PCA                        PCB<br/>  PCB                        PCM<br/><br/>When you save the reorganization plan, the system issues message FAGL_REORGANIZATION 029: \"&lt;(&gt;&amp;&lt;)&gt;1 &lt;(&gt;&amp;&lt;)&gt;2 exists as sender assignment and recipient assignment\"<br/><br/>The long text of the message states that the display of the message can be set using the message control.  However, if you enter message number 029 in the message control, the system issues the following message:<br/>\"Message number 029 is not allowed\"<br/><br/>On the one hand, these corrections enable message FAGL_REORGANIZATION 029 to be configured - this is the enhancement mentioned in Note 1588379. On the other hand, only this system response is allowed technically.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>New GL<br/>New general ledger<br/>General Ledger Accounting (new)<br/>Cycle</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This function was not available up to now.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/><br/>The message can then be set as a warning.<br/>However, as described in the long text of the message, you must then accept that correctly assigned objects could be added to the reorganization plan.<br/></p></div>", "noteVersion": 1}, {"note": "1567542", "noteTitle": "1567542 - Asset without value under cost center: Error AIST 009", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you reassign an asset without values as a second level object below a<br/>cost center in a profit center reorganization, the system issues error message AIST 009.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization, reorg, FI-AA, AIST009, AIST 009</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the reorganization, no new time interval is created in the asset master record for assets without values. Instead, the new profit center is entered for the entire validity period. The \"reorganization date\" field is not filled in the process, and postings to the old time interval therefore remain possible. In the case of objects imparting the account assignment that are themselves time-dependent, such as, for example, internal orders and WBS elements, this does not result in problems because the object imparting the account assignment and the asset are consistent as regards the profit center after the reorganization.<br/>However, since the cost center itself is time-dependent, the profit center of the cost center (old profit center) differs from the profit center of the asset (new profit center) in the time interval before the reorganization date, which results in error message AIST 009.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>In the case described above, the system no longer issues error message AIST 009 and the asset can be reorganized. For the period before the reorganization date, the profit center of the asset is subsequently different from the profit center of the cost center. The \"reorganization date\" field is not filled. This corresponds to the situation that would arise if an asset with account assignment to the reorganized cost center was created after the reorganization.<br/><br/></p></div>", "noteVersion": 1}, {"note": "1821248", "noteTitle": "1821248 - PRCTR: Obj. class. of first level due to failed splitting", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the profit center reorganization. You generate the object list for objects from payables or receivables.<br/>Even though the simulation of the document splitting with extended document splitting characteristics is not successful, the objects are classified incorrectly (FAGL_R_APAR-XLEVEL1 = space).<br/><br/>As a result, these objects cannot be reassigned due to various errors.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization, PRCTR reorganization,<br/>failed splitting simulation, objects of first level<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to SAP Note 1738491.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached source code.<br/></p></div>", "noteVersion": 2}, {"note": "1655212", "noteTitle": "1655212 - No new time interval during reorg for assests not posted to", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization. When you do so, the system does not generate a new time interval on the reorganization date for assets that are not posted to.<br/>- or -<br/>You add a new time interval to a fixed asset that is not posted to.<br/>After you you execute the change, the correct, new profit center exists in the new interval; however, the existing interval contains the same profit center. Transaction AS02 terminates and the system issues message AIST 009; you can no longer correct the error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>PRCTR, ASSET_ORG_ASSIGNMENT_CHANGE, T093_BSN_FUNC-SEGMENT_REPORT, FIN_GL_REORG_1</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections as described in the attached correction instructions.<br/>Assets that are not yet posted to then also receive a new time interval with the new profit center.</p></div>", "noteVersion": 2}, {"note": "1671509", "noteTitle": "1671509 - Error F5 800: Inconsistent currency information", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the transfer posting of balances, the system issues error message F5 800:  \"Inconsistent currency information\"<br/><br/>This error occurs if the amount to be transferred in local currency is 0.00.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/>Profit center reorganization<br/>Segment reorganization<br/>Funds Management reassignment<br/>Funds Management reassignment<br/>FACI<br/>FI_DOCUMENT_COLLECT</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1659618", "noteTitle": "1659618 - PRCTR: Runtime error NO_OBJ_TYPE in case of price difference", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you reassign payables, you receive the runtime error<br/>NO_OBJ_TYPE.<br/><br/>This happens if a part of a payable is a price difference for an unassigned order and its account assignment was determined by transaction OKB9 (\"Change default account assignment\").</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/>Profit center reorganization<br/>Reorg<br/>Object type PO<br/>OKB9</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 5}, {"note": "1889169", "noteTitle": "1889169 - PRCTR/SEG: Dump when generating APAR / 566 (VI)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reassignment of receivables and payables, the system issues<br/>error message FAGL_REORGANIZATION 566.<br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566<br/>Split characteristic 'SEGMENT'<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by differences between the reorganization view and the main ledger view (FAGL_SPLINFO) due to the field 'SEGMENT'. Even though the field 'SEGMENT'  is not entered in the split characteristics, the system still determines the segment for the new PRCTR during the reassignment.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the following corrections.<br/><br/>If the 'SEGMENT' field is not a split characteristic, the profit center reorganization does not set it.</p></div>", "noteVersion": 4}, {"note": "3044229", "noteTitle": "3044229 - Error in FAGLCORC due to open profit center Reorganization Plan", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the transaction FAGLCORC the unjustified error message FAGL_REORGANIZATION 008 is raised.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\"> K_COBL_CHECK, GET_PRCTR, FAGL_R_OBJECT_IN_PRCTR_REORG</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In case of maintenance order (object type O30) the error message is raised because the FORM GET_PRCTR returns flag involved_in_reorg = true but the ev_prctr_new is empty. The root cause is the fact that particular maintenance order is closed already. This fact is not properly evaluated.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p id=\"\">Implement the advanced correction manually or upgrade to corresponding support package.</p>", "noteVersion": 2}, {"note": "1869269", "noteTitle": "1869269 - PRCTR: Error message FAGL_REORGANIZATION008 returned", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Transactions which generate postings and call the CO assignment check can under certain cisrcumstances incorrectly return error message FAGL_REORGANIZATION008.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_REORGANIZATION008, MAT, FAGL_REORGANIZATION, GENERATE_OBJLIST</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is a program error. In the object type specific implementation of the method IF_FAGL_R_OBJ_TYPE~GENERATE_OBJLIST(MAT) there is incorrectly built a where_clause statementt for the selection of objects involved in PCA reorganization.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement this correction or download the corresponding support pack.</p></div>", "noteVersion": 1}, {"note": "2059369", "noteTitle": "2059369 - Specific restrictions not considered for MAT object type", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You do profit center reorganization. When you do a posting in phase B1, you might face with the issue, that profit center is trying to be changed also for objects, which are excluded from the reorganization with specific restricitions defined in the reorganization plan. It is for postings with CO account assignment or when FI substitution with profit center check is applied As a subsequence an incorrect profit center is being derived for the posting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MM, ev_involved_in_reorg, get_object_info, general restrictions, specific restrictions,</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The reorganization framework passes  general and specific restrictions to the object type and each object type is responsible for its own check. During the standard object generation there is just pure conversion of the specific restrictions to database where-clause. However call of the method GET_OBJECT_INFO implementation for object type MAT does not take the specific restrictions into account.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this correction note or download the corresponding support pack.</p>", "noteVersion": 2}, {"note": "1626389", "noteTitle": "1626389 - PRCTR/SEG: Document splitting not updated (without message)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When receivables and payables are generated, the system simulates document splitting with extended attributes, to determine for which partial amounts the profit center (in the case of a profit center reorganization) or the segment (in the case of a segment reorganization) was derived from which account assignment.<br/><br/>If the partial amounts were derived from different account assignments (for example, internal order, project, cost center), the partial amounts are displayed at different hierarchy levels in the reorganization plan and are also reassigned independently.<br/><br/>During the account assignment change, document splitting is performed again with updated profit centers or segments, and the result is updated so that subsequent processes such as the clearing are executed in the new account assignments.<br/><br/>Previously, the partial amounts for one account assignment could be correct and the other partial amounts not reassigned at all without an error message being issued.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>NewGL<br/>New General Ledger Accounting<br/>Reassignment<br/>Reorg</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1900735", "noteTitle": "1900735 - Reorg: Profit center in cost center cannot be changed (III)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the profit center reorganization solution.<br/>You want to change the assignment to the profit center in a cost center.<br/>The system issues error message FAGL_REORGANIZATION 601<br/>(Profit center assignment in cost center &amp;1 cannot be changed)<br/>(also see SAP Note 1684679).<br/>The system issues error message FAGL_REORGANIZATION 603 even though you have implemented SAP Note 1881515.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_REORGANIZATION 601,603,605<br/>Call transaction KS02<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SAP Note 1881515<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached source code corrections by importing the Support Package<br/>by or implementing the correction instructions.</p></div>", "noteVersion": 2}, {"note": "1880282", "noteTitle": "1880282 - PRCTR: Check of transfer of billing documents (III)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization for distribution documents. Even though distribution documents were reorganized successfully and receive the relevant status is the reorganization, the system did not reassign the master data. The error log contains the warning message FAGL_REORGANIZATION 343.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_SO</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have changed the message FAGL_REORGANIZATION 343 from E to W. In advance, you have implemented SAP Note 1864097 which allows this change.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.<br/>Note that you are only permitted to change of the message type from E to W if certain prerequisites apply. To clarify what these prerequisites are, make sure you first contact SAP Development Support.</p></div>", "noteVersion": 1}, {"note": "1787464", "noteTitle": "1787464 - <PERSON><PERSON><PERSON> after acct assgmt chg: Incorr first-level objects", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use cycles in your reorganization. The account assignment change was performed successfully. You regenerate, and surprisingly, there are many new objects at first level that were previously in the plan as subordinate objects.<br/>This is due to a program error and must be prevented by implementing these corrections.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Cycle, cycles, memory, memory consumption</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error when you use cycles (for example, A -&gt; B and B -&gt; C).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections.</p></div>", "noteVersion": 1}, {"note": "1850969", "noteTitle": "1850969 - PRCTR: Analysis report for material and purchase order", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The analysis reports FAGL_R_BALANCE_SPL_POA and FAGL_R_BALANCE_MAT do not display all lines for the determined balances in your output list. For each object, the system displays only one line for the prior period and one line for the current period.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 1}, {"note": "1824220", "noteTitle": "1824220 - MESSAGE_TYPE_X during object list generation", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The generation of the object list terminates with runtime error FAGL_REORGANIZATION 113. This might occur, when you do for instance a payment reversal for an invoice, which has been already assigned and approved, so the corresponding object in the object list reached the status 20.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>PRCTR reorganization, FAGL_REORGANIZATION 113, grouping, reestablish group</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the advance correction.</p></div>", "noteVersion": 2}, {"note": "1716690", "noteTitle": "1716690 - PRCTR/SEG: Short dump when generating object type AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When receivables are generated, the runtime error<br/>RAISE_EXCEPTION occurs in the method GET_ACCIT_FROM_SD_P of the class<br/>CL_FAGL_R_SPLIT_REORG.<br/>The following exception was triggered:  ACCIT_ERROR<br/><br/>This occurs for receivables for SD billing documents for which down payment clearings have meanwhile been executed.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>CL_FAGL_R_SPLIT_REORG=========CP<br/>CALL_SPLITTER<br/>AC_DOWNPAYMENT_CHARGE<br/>F5 847</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 3}, {"note": "1778036", "noteTitle": "1778036 - PRCTR: Perform. problems for purchase orders w/ acct assgmt", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You created a reorganization plan in your company.<br/>Performance problems occur in the \"reassign\" step for purchase orders with account assignment.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_POA, PRCTR, profit center<br/>Object list, purchase order with account assignment</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The problem is database-specific. If there is no purchase order with account assignment for an object type, for example, sales orders in the table EKKN, the relevant index table is initial and certain databases respond to this with a full table scan. Up to now, this response was known only for Oracle databases.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions relevant for your release.</p></div>", "noteVersion": 2}, {"note": "1924356", "noteTitle": "1924356 - PRCTR/SEG: Dump during generation / ASSERTION_FAILED", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you generate receivables, the system generates a dump with the runtime error ASSERTION_FAILED.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_APAR<br/>FILL_VAL_SUP_P<br/>FAGL_R_PROCEED<br/>ASSERT 1 = 0</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The reorganization hierarchy is missing the entries for receivables below the network activity. In addition, there are also requirements whose profit center you want to reorganize and that were derived from a network activity.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.<br/><br/>In addition, add the missing entries to the hierarchy for future reorganizations. Note that you can no longer enhance the hierarchy during a reorganization. The enhancements only affect new reorganization plans.</p></div>", "noteVersion": 3}, {"note": "1507039", "noteTitle": "1507039 - Wrong Web Dynpro Application Parameter", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Web Dynpro Application will enter Accessibility mode by default.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FAGL_R_CHANGE_MDATTR, FAGL_R_CHANGE_RESP, FAGL_R_OBJLIST_OVERVIEW, FAGL_R_PLAN_DETAIL, FAGL_R_PLAN_OVERVIEW.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The reason is the parameter WDACCESSIBILITY is set on.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Remove this wrong Web Dynpro application parameter.<br/></p></div>", "noteVersion": 2}, {"note": "1844856", "noteTitle": "1844856 - ASSERTION_FAILED during object generation", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The generation of the object list terminates with runtime error ASSERTION_FAILED in the method GET_OBJECT_INFO_P of the class CL_FAGL_R_OBJLIST. This might occur, when you maintain own hierarchy version, where some object types are missing.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>ASSERTION_FAILED, GET_OBJECT_INFO, GET_OBJECT_INFO_P, hierarchy version, object type</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Apply the advance correction.</p></div>", "noteVersion": 3}, {"note": "1723893", "noteTitle": "1723893 - PRCTR: Error 222 while reassigning POA purch.order", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have created a reorganization plan for profit center reorganization in your company. You try to reassign the list with purchase orders with account assignment (POA). An error occurs: \"Error when reading a/c assignmt information...\" - FAGL_REORGANIZATION 222</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_POA<br/>purchase order - object list<br/>POA reassignment</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The system cannot determine the real account assignment of the purchase order with account assignment (POA). In this case this purchase order shall occur as an object on the first level of the hierarchy, so that the user is able to assign the correct profit center</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the provided correction instruction.</p></div>", "noteVersion": 1}, {"note": "1663603", "noteTitle": "1663603 - PRCTR/SEG: Message GLT0 002 after successful reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You successfully execute a reorganization.<br/><br/>During a follow-on activity (for example, clearing) of a reassigned receivable or payable, the system issues the following message: \"Document splitting: Items for clearing &lt;(&gt;&amp;&lt;)&gt;1/&lt;(&gt;&amp;&lt;)&gt;2/&lt;(&gt;&amp;&lt;)&gt;3/&amp;4 not found\" (message number GLT0 002).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Profit Center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_SPLINFO<br/>FAGL_SPLINFO_VAL</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/>After a successful reassignment, entries are missing in the table FAGL_SPLINFO_VAL.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/><br/>This note prevents these problems from occurring in future reorganizations.<br/>However, it does not correct existing inconsistencies. For this purpose, contact SAP Support (component FI-GL-REO-GL).</p></div>", "noteVersion": 2}, {"note": "1864097", "noteTitle": "1864097 - PRCTR: Check of transfer of billing documents (II)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>With SAP Note 1837340, the transfer of billing documents is checked. The system issues this message as an error message.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization, FAGL_REORGANIZATION<br/><br/>Call transaction OBA5.</p> <ul><ul><li>Change the message control.<br/>Work area 'FAGL_REORGANIZATION'</li></ul></ul> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You want to configure this message.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><b><b>Warning:</b></b><br/>Note that if the transfer of billing documents is <b>not</b> taken into account, this leads to inconsistencies after a reorganization since the inclusion of FI follow-on documents in the reorganization plan is not guaranteed. In this case, SAP <b>cannot accept any responsibility</b>.<br/><br/>The message should be set from <b>E</b>rror to <b>W</b>arning in special cases only - as the transfer of billing documents is not checked in this case. If you plan to do this, contact SAP Development Support in advance.<br/><br/>Implement the attached source code corrections by importing the Support Package or implementing the correction instructions.</p></div>", "noteVersion": 3}, {"note": "1667694", "noteTitle": "1667694 - PRCTR: Purch. orders for SO, special case valuated stock", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have a reorganization plan in your company. You try to reorganize materials and sales orders. You don't find the purchase orders for those materials and sales orders in following special case: sales order is with valuated stock and no controlling, but has a co object in its master data.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>CL_FAGL_R_OBJ_TYPE_001_PO<br/>CL_FAGL_R_OBJ_TYPE_001_POA<br/>purchase order - object list<br/>valuated stock for sales order</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a programm error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the provided correction instructions</p></div>", "noteVersion": 2}, {"note": "1952112", "noteTitle": "1952112 - PRCTR/SEG: FAGL_REORGANIZATION 535 during reassignment of AP/AR", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the reassignment of accounts payable/receivable (AP/AR), the system issues error message FAGL_REORGANIZATION 535.</p>\n<p>For the second symptom, the system may issue error F5201.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566<br/>CL_FAGL_R_OBJ_TYPE_APAR: R_UPDATE_ACCIT_P<br/>F5201, F5 201</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Preceding corrections, for example SAP Notes 1942121/1939299/1910342</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the following corrections.<br/>Check before each test whether all SAP Note according to SAP Note 1471153 have been implemented</p>", "noteVersion": 6}, {"note": "1781344", "noteTitle": "1781344 - PRCTR: Incorrect balance sheet transfers", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a reorganization of your profit center using a reorganization plan. You notice that the system generates asset balance sheet values for materials that are too high.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_MAT</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 1}, {"note": "1598745", "noteTitle": "1598745 - Reorg transfer posting is made w/ unexpected posting date", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The business function FIN_GL_REORG_1 and segment reporting for Asset Accounting are active in your system.  If you use transaction AS02 to manually change an alternative profit center in a future time interval (that is, with a start date that is after the current date), the system executes an intracompany transfer from the old profit center to the new one, which is correct.  However, the system uses the current date instead of the future start date of the time interval as the posting date.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>PRCTR, ASSET_ORG_ASSIGNMENT_CHANGE, T093_BSN_FUNC-SEGMENT_REPORT, FIN_GL_REORG_1<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.<br/><br/>After you implement the corrections, the system uses the start date of the time interval in which the profit center was changed as the posting date under the conditions mentioned above.</p></div>", "noteVersion": 2}, {"note": "1534197", "noteTitle": "1534197 - SAP LT: Transformation Solution - Profit Center Reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are looking for detailed information regarding SAP Landscape Transformation (SAP LT) and the transformation solution \"Profit Center Reorganization\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PC REORG, Profit Center Reorganization, SAP LT, SAP Landscape Transformation, DMIS 2010_1,<br/>T-Solution, SAP LT Docu, SAP Landscape Transformation Documentation,<br/>new General Ledger, new G/L,<br/>FIN_GL_REORG_1</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>- You require detailed information about how to utilize the function \"PC REORG\" (Profit Center Reorganization) using the product \"SAP Landscape Transformation\" (Release 1.0).<br/>- You ordered the product \"SAP Landscape Transformation\" and installed it on your ERP 6.0 (EHP 05) system (optional) to be able to execute PC REORG.<br/>- You use New General Ledger (new G/L).<br/>- Your system is an ERP 6.0 system with EHP05 and function FIN_GL_REORG_1 is active.<br/>- The SAP Landscape Transformation (SAP LT) level is at least Support Package 01, and you have also implemented Note 1539101.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP Landscape Transformation (SAP LT) software enables SAP customers to execute various data transformations in their SAP system landscapes.<br/><br/>To use the Profit Center Reorganization functions, you require the following SAP LT license:<br/>- SAP Landscape Transformation, Enterprise Edition - material number 7018629<br/><br/>Note: Only SAP customers who have licensed SAP Landscape Transformation accordingly may use the \"Profit Center Reorganization\" function. The function is available as of SAP ERP 6.0 - Enhancement Package 05. The SAP business function that is required for this (-&gt; FIN_GL_REORG_1) also contains other functions - in addition to profit center reorganization - such as using the profit center and the segment as an entity in the asset master record. You do <strong>not</strong>require a license for SAP Landscape Transformation for these functions.<br/>If you require more information, contact your SAP Account Team.</p>\n<p>You have the choice of either carrying out the profit center reorganization either directly in ERP 6.0 (EHP 05), in the business function FIN_GL_REORG_1 - that is, without using SAP Landscape Transformation - or using SAP LT - that is, using the SAP LT process tree, which leads and guides you through the reorganization process. SAP LT also offers the following analyses.<br/><br/><br/>Technical prerequisites for using the function \"Profit Center Reorganization\" with the product \"SAP Landscape Transformation\" are:<br/>1. Business function FIN_GL_REORG_1 in SAP ERP 6.0 - EHP05 system is active.<br/>2. SAP Landscape Transformation is installed correctly in the central system and in the relevant \"Execution\" system - also see the \"SAP LT Master Guide\" and SAP Note 1465272.<br/>\" At least support package 1 for Release SAP Landscape Transformation 1.0 and SAP Note 1539101 have been implemented in the system (not applicable in case of reorganization without SAP LT).<br/><br/>If you encounter any problems, open a message under one of the following support components:<br/>Profit Center Reorganization-relevant problems: FI-GL-REO</p>\n<p>SAP LT-relevant problems:<br/>o SAP LT Work Center and Project Execution Plan: CA-LT-FRM<br/>o Process tree and access to the PC Reorg function: CA-LT-CNV<br/> <br/><br/>Further information about SAP Landscape Transformation is available at: <a href=\"https://help.sap.com/viewer/p/SAP_LANDSCAPE_TRANSFORMATION\" target=\"_blank\">https://help.sap.com/viewer/p/SAP_LANDSCAPE_TRANSFORMATION</a><br/><br/>For more information about the Profit Center Reorganization function: <a href=\"https://help.sap.com/erp2005_ehp_05/helpdata/de/fb/84a48d665e4fa9a2dc7b60adeb7c7d/content.htm?no_cache=true\" target=\"_blank\">https://help.sap.com/erp2005_ehp_05/helpdata/de/fb/84a48d665e4fa9a2dc7b60adeb7c7d/content.htm?no_cache=true</a><br/><br/></p>\n<p><strong>Analyses for Profit Center Reorganization </strong></p>\n<p>As of SP06, SAP Landscape Transformation provides analyses for the Profit Center Reorganization. You can use these analyses to ensure that the profit center balances in General Ledger Accounting (new) (FI-GL) are consistent with the balances in some subledgers and with the goods receipt/invoice receipt (GR/IR) account.</p>\n<p>The analyses relate to the following areas:</p>\n<ul>\n<ul>\n<li>Asset portfolios (FI-AA)</li>\n<li>Material stocks (MM)</li>\n<li>Work in process (WIP) and SD (Sales and Distribution)</li>\n<li>GR/IR accounts </li>\n</ul>\n</ul>\n<p>You can use these analyses before the reorganization to ensure that you convert a consistent system and to ensure that the reorganization is processed correctly after it is carried out.</p>\n<p><br/><span>Related SAP Notes</span>:<br/>SAPLT - Central Note (1465272)<br/>PC Reorg - Central Note (1471153)<br/>Landscape Transformation - Profit Center Reorganization Analysis: Business Function LT_PC_REORG_ANALYSIS (1943178)</p>\n<p> </p></div>", "noteVersion": 7}, {"note": "1674191", "noteTitle": "1674191 - Transfer posting: System error in FI interface (F5 691)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the transfer posting of balances, the system issues the error message \"System error in the FI interface (F5 691)\".<br/><br/>This happens if the amount for which the transfer posting is to be made for a line item is zero.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorg<br/>Reorganization<br/>Profit center reorganization<br/>Segment reorganization<br/>FM reassignment</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 2}, {"note": "1612114", "noteTitle": "1612114 - PRCTR: Changes in the program structure", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>As of SP6 of EhP5, the structure in the Reorganization program changes. The intention of the change is to provide separate objects to:</p> <ul><li>process the generic functions of Reorganization</li></ul> <ul><li>process specific functions of the Profit Center Reorganization</li></ul> <p>Up until SP5, the program structure for Profit Center Reorganization is the following:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Class</th><th align=\"LEFT\"> Inherits from</th></tr> <tr><td>CL_FAGL_R_OBJ_TYPE_001_APAR</td></tr> <tr><td>CL_FAGL_R_OBJ_TYPE_001_AP</td><td> CL_FAGL_R_OBJ_TYPE_001_APAR</td></tr> <tr><td>CL_FAGL_R_OBJ_TYPE_001_AR</td><td> CL_FAGL_R_OBJ_TYPE_001_APAR</td></tr> </table> <p>From SP6, the structure is:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Class</th><th align=\"LEFT\"> Inherits from</th></tr> <tr><td>CL_FAGL_R_OBJ_TYPE_APAR</td></tr> <tr><td>CL_FAGL_R_OBJ_TYPE_001_APAR</td><td> CL_FAGL_R_OBJ_TYPE_APAR</td></tr> <tr><td>CL_FAGL_R_OBJ_TYPE_001_AP</td><td> CL_FAGL_R_OBJ_TYPE_001_APAR</td></tr> <tr><td>CL_FAGL_R_OBJ_TYPE_001_AR</td><td> CL_FAGL_R_OBJ_TYPE_001_APAR</td></tr> </table> <p><br/>This structure allows you to keep a separate logic between the generic Reorganization logic (CL_FAGL_R_OBJ_TYPE_APAR), the specific logic for Profit Center Reorganization (CL_FAGL_R_OBJ_TYPE_001_APAR ) and the specific logic for account payable or receivable (CL_FAGL_R_OBJ_TYPE_001_AP and CL_FAGL_R_OBJ_TYPE_001_AR).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit Center Reorganization PC Reorg. AP AR Payable Receivable</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>For support packages prior to SAPKH60506, follow the manual instructions in this note.<br/>After you have successfully completed the manual instructions, proceed to implement the automatic correction instruction.</p></div></div></div>", "noteVersion": 9}, {"note": "1809521", "noteTitle": "1809521 - PRCTR/SEG: Error messages FAGL_REORGANIZATION 534 and 536", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system issues error message FAGL_REORGANIZATION 534 or 536 when you reassign receivables and payables.<br/>Parts of a payable or receivable were actually already reassigned; the system issues the error message mentioned for the parts that are not yet reassigned.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorg<br/>Profit center reorganization<br/>Segment reorganization<br/>Reorganization<br/>Reorganization</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 3}, {"note": "1720661", "noteTitle": "1720661 - PRCTR: Error FAGL_REORGANIZATION 533 during reassgmt of AR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During reassignment, the system issues error message FAGL_REORGANIZATION 533 for receivables that derived the profit center from sales orders that are CO objects (make-to-order production).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization<br/>Profit center reorganization<br/>AUBEL<br/>KDAUF</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 2}, {"note": "1677974", "noteTitle": "1677974 - Profit center reorganization: IV before F2", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In a cross-company code business process, the invoice receipt is carried out in the selling company code using EDI after a profit center reorganization.<br/>The profit center assignment does not correspond to expectations, for example, an account assignment is made to the dummy profit center.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>IDOC, IDE, intercompany data exchange, reorganization, intercompany, intercompany billing, IV, F2, VF04, VF31<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Prerequisites:<br/>Intercompany billing is created before the customer billing document.<br/><br/>Cause:<br/>In the SAP concept, the customer billing document is posted before intercompany billing. In this case, you can specify in Customizing (transaction VOFC) that the account assignment is to be transferred from the customer billing document during the invoice receipt.<br/>However, if intercompany billing is posted before the customer billing document, this mechanism does not work. This problem occurs regardless of a profit center reorganization.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>This can be corrected with a modification only. Contact SAP Consulting. See the attached SAP Note 170183.<br/><br/>SAP Note 607799 provides a possible solution.<br/>Check whether the implementation of the SAP Note provides a solution for you.<br/></p></div>", "noteVersion": 2}, {"note": "1752608", "noteTitle": "1752608 - Runtime error ASSERTION_FAILED in CL_FAGL_R_OBJLIST...", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During generation or the account assignment change, the runtime error ASSERTION_FAILED occurs in the program CL_FAGL_R_OBJLIST=============CP. The error occurs in the method GET_OBJECT_INFO_P of the class CL_FAGL_R_OBJLIST.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>GET_OBJECT_INFO, GET_OBJECT_INFO_P, object type, calling object list, calling instance</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>", "noteVersion": 1}, {"note": "1738789", "noteTitle": "1738789 - PRCTR: generating PO takes too long or ITAB_DUPLICATE_KEY", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have created a reorganization plan in you company. You tried to reassign all materials. In this step the system generates the purchase orders for these materials too. You get an error ITAB_DUPLICATE_KEY or the generation takes too long.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_PO<br/>purchase orders - object list<br/>ITAB_DUPLICATE_KEY<br/>CX_SY_ITAB_DUPLICATE_KEY</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the provided correction instructions.</p></div>", "noteVersion": 1}, {"note": "3115636", "noteTitle": "3115636 - PRCTR reorganization: FI documents with exclusively cleared customer/vendor line items are processed at first level", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the generation of the object type GLOI, the system currently checks whether a customer or vendor line item exists in the FI document. If this is the case, the document split for the FI document is simulated during generation and reassignment.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization, segment reorganization<br/>generation, reassignment, runtime<br/>GLOI</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">To avoid performance and memory problems, the document split can be omitted if the customer or vendor line items of the FI document have already been cleared. The FI document can be processed at the first level for the reorganization of open G/L account items.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For documents that do not have any open customer or vendor line items, the document split will no longer be simulated/run. As a side effect, the runtime is reduced during the generation and reassignment.</p>\n<p>Implement the program corrections. The source code must be implemented prior to generation.</p>", "noteVersion": 2}, {"note": "1659613", "noteTitle": "1659613 - PRCTR: Object type not determined (FAGL_REORGANIZATION505)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When generating payables, the system issues the warning message \"Object type not determined for payable/receivable &amp;1 &amp;2 &amp;3\" (FAGL_REORGANIZATION 505).<br/><br/>This occurs, in particular, for payables that received the profit center from purchase order items, and these purchase order items derived the profit center from a sales document item.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Profit center reorganization<br/>Reorg<br/>POA<br/>SO<br/>FAGL_REORGANIZATION 505</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1571140", "noteTitle": "1571140 - Creation object type: Deactivating activation in IMG", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>A specific activation in Customizing is required for the reorganization of assets. After you implement the corrections, this activity is no longer required.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Asset Accounting, FI-AA<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Simplification of the Implementation Guide (IMG)<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import the relevant Support Package.<br/>Only implement these corrections without using the relevant Support Package if this note is required as a prerequisite for other notes.</p></div>", "noteVersion": 1}, {"note": "1366671", "noteTitle": "1366671 - Reversal of settlement not permitted after reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p><br/>When you try to execute a settlement or reverse a settlement, the system issues the error message \"Settlement/reversal not permitted after reorganization\".<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p><br/>Settlement, reversal, reorganization, profit center reorganization, FAGL_REORGANIZATION335, FAGL_REORG_SEG002, segment reorganization<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p> <b>A) Profit center reorganization</b><br/> <p><br/>In profit center reorganization, the profit center assignments of objects (for example, orders or WBS elements) are changed. It is assumed that the period-end closing operations have been carried out completely and correctly before the reorganization. After the reorganization, a reversal of the settlement of earlier periods or a new settlement of corrected values can no longer be carried out consistently with regard to Profit Center Accounting. Therefore, the system prevents this type of transaction.<br/><br/>This is illustrated by the following example:<br/><br/>You use Profit Center Accounting in General Ledger Accounting (New GL).<br/>You perform period-end closing for an order in period 3 and you settle 1000 euro work in process to Financial Accounting. The order is also settled to a profitability segment of Profitability Analysis (CO-PA). During the settlement, the old profit center PCA is updated.<br/><br/>For the key date 1.4., you reorganize the order and assign it to the new profit center PCN. A new time segment is also created in the settlement rule, which means that as of period 4 the order is settled to a profitability segment that contains the new specification PCN of the profit center characteristic. In Financial Accounting, a reposting is made from the old profit center to the new profit center in the inventory account for work in process.<br/><br/>After the reorganization, you reverse the settlement of period 3. The reversal relates to the original settlement and is carried out for the old profit center PCA. You make corrections and repeat the determination of the work in process for period 3, which results in a value of 800 euro. You then perform the order settlement for period 3 again using the corrected value. Since the order was already assigned to the new profit center, the posting is made to PCN in Financial Accounting. However, for the settlement to Profitability Analysis, the time segment of the settlement rule that is valid up to period 3 is used, which means that PCA is updated.<br/><br/>This results in the following inconsistencies:<br/>1.) When the settlement is performed again, the system updates different profit centers in Financial Accounting and in Profitability Analysis.<br/><br/>2.) In Financial Accounting, the following balances now exist for the order in the inventory account for work in process: -1000 euro for PCA (due to the reversal) and +1800 euro for PCN (due to the reposting and the new settlement). You want this to be 0 euro for PCA and +800 euro for PCN.<br/><br/>Therefore, the reversal of settlements must be prevented after the reorganization. This means that the settlement itself is not allowed. If a validation was executed for each individual object (WBS element, order, and so on) to determine whether the object (or another object contained in its settlement hierarchy) was reorganized, this would be too time-consuming and would lead to runtime problems during the settlement. For this reason, the transaction is prevented if it applies to a period that is before the reorganization date of a reorganization plan that was already used to reassign objects.<br/></p> <b>B) Segment reorganization </b><br/> <b></b><br/> <p>For the settlement of work in process, the system usually determines the segment from the profit center. Within a segment reorganization, the assignment of profit centers to segments may change. Since this assignment is time-dependent, the problem is reduced compared to the profit center reorganization. However, at \"segment\" account assignment level, unwanted balances may occur on the inventory account for work in process in Financial Accounting if you reverse a settlement and then repeat the settlement using a changed or corrected amount.<br/><br/>In the segment reorganization, work in process is not reposted at the level of individual CO objects, but is reposted as totals at totals record level using the \"G/L account\" object type. Reversals of settlements, and also actual settlements, are prevented if they apply to a period that is before the reorganization date of a reorganization plan that contains objects with at least the status \"Balance Determination Successful\".</p> <b></b><br/> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><br/>Check carefully whether there is an alternative to the transaction. If you must perform this transaction, you can deactivate the validation. However, SAP does not accept responsibility for the possible consequences of this.<br/><br/>To deactivate the validation, call transaction SE16N and make an entry in the table T811FLAGS. In the \"Table\" field, enter PRCTR_REORG or SEGMENT_REORG depending on the type of reorganization (profit center or segment) for which the validation is to be omitted. In the \"Field\" field, enter the period whose settlement you want to reverse (for example, 009 if you want to reverse the settlement of period 9).<br/><br/>When checking the procedure, take the following points into account:<br/></p> <b>General</b><br/> <p><br/>- If the settled data to be corrected is settled using the \"full settlement\" settlement type, we recommend that you perform the required corrections using the next allowed settlement after the reorganization.<br/></p> <b>Profit center reorganization</b><br/> <p><br/>- If none of the objects that are affected by the reversal or the new settlement have been reorganized, you can perform the transaction. The object lists of the reorganization plan (table FAGL_R_PL_OBJECT) display which objects have been reorganized. Note that several reorganizations may have taken place after the settlement that is to be reversed. The error message displays only the first reorganization plan found; however, all affected plans must be checked.<br/><br/>- If an object (or an object that occurs in its settlement hierarchy) has been reorganized, the transaction is not allowed for the reasons mentioned above.  If you perform the transaction despite this, SAP does not accept responsibility for any inconsistencies in Profit Center Accounting that may result from this process or its effects. You are responsible for any manual steps that are required, such as the reposting of accrued inventories to FI accounts or the documentation of the reason for profit center rejections between FI and CO-PA. The list of possible inconsistencies in the example above is not necessarily conclusive.<br/></p> <b>Segment reorganization</b><br/> <p><br/>- If no repostings for the \"G/L account\" object type have been made yet, you can perform the transaction. Otherwise, it is not allowed for the reasons mentioned above. If you perform the transaction despite this, SAP does not accept responsibility for any inconsistencies that result from this. You are responsible for ensuring the correctness of the inventories of work in process at \"segment\" account assignment level in the general ledger.<br/><br/><br/><br/><br/></p></div>", "noteVersion": 1}, {"note": "1615987", "noteTitle": "1615987 - Deselect operation does not affect sub-nodes", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>On the hierarchy display of object lists, you have already selected some nodes. Then if you deselect one collapsed node using CTRL and mouse click, the sub-nodes are not deselected automatically.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization Plan, Object Lists, Reassignment, Hierarchy Display, FAGL_R_PLAN_DETAIL.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instruction.</p></div>", "noteVersion": 2}, {"note": "1865363", "noteTitle": "1865363 - TSV_TNEW_PAGE_ALLOC_FAILED during display of object list", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you select the object list for generation, reassignment or transfer posting in terms of reorganization plan you get a runtime error TSV_TNEW_PAGE_ALLOC_FAILED in method CL_FAGL_R_OBJLIST-&gt;IF_FAGL_R_OBJLIST~GET_LOG.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>TSV_TNEW_PAGE_ALLOC_FAILED, log, GET_LOG, FAGL_REORGANIZATION 080,</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is too many messages in the application log.<br/>Majority of the messages is the message FAGL_REORGANIZATION 080.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Apply the advance correction.<br/><br/>In case you are aware of the fact, that many objects do have zero balances, there is an option to avoid logging the info message FAGL_REORGANIZATION 080 in the application log. In this case you can switch off this message in the message control in transaction OBA5.</p></div>", "noteVersion": 4}, {"note": "1854990", "noteTitle": "1854990 - PRCTR: Posting to old profit center", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute a profit center reorganization for purchase orders with account assignment. However, after the reorganization, postings are executed using the old profit center.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_POA, fagl_splinfo</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The purchase order has only one account assignment, in other words, only one entry in the table EKKN.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.</p></div>", "noteVersion": 1}, {"note": "1598626", "noteTitle": "1598626 - Posting date for ledger-specific transfers", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>By default, the date of the transfer is the reorganization date. However, some object types decide themselves in which period the balance is to be transferred.<br/>If the balance is to be corrected in all ledgers, the first day of the period of the leading ledger is selected as the posting date.<br/>If the balance is only corrected in one ledger group, the first day of the period of the representative ledger of the ledger group is selected as the posting date.<br/><br/>The previous implementation always determines the first date of the period of the leading ledger, even if the ledger group to be corrected uses a different fiscal year variant.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization transfer</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "1760025", "noteTitle": "1760025 - PRCTR: incorrect balance with purchase orders (PO or POA)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You start the balance sheet transfer in the reorganization plan. You notice that the balance of one or several purchase order items is not correct.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_FAGL_R_OBJ_TYPE_001_PO<br/>CL_FAGL_R_OBJ_TYPE_001_POA<br/>Purchase order with account assignment - object list<br/>Purchase orders without account assignment - object list</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions.</p></div>", "noteVersion": 1}, {"note": "1581575", "noteTitle": "1581575 - Show the same message several times", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When editing reorganization plan on the 'General Restrictions' tabpage and if there are some messages in the message area, these messages will be showed several times after open and close togglelinks.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Message, General Restrictions, FAGL_R_PLAN_DETAIL.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The message buffer is not cleared.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Clear the message buffer.</p></div>", "noteVersion": 2}]}, {"note": "1627018", "noteTitle": "1627018 - Composite SAP Note for segment reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This SAP Note is a composite SAP Note. Under \"Related Notes\" you will find all SAP Notes that are relevant for the reorganization of segments.<br/><br/>Important information:<br/>Contact your SAP Account Executive to find out whether there are additional license fees for the use of segment reorganization.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Reorganization, segment<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have activated the business function FIN_GL_REORG_SEG.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Check the related notes.</p></div>", "noteVersion": 2, "refer_note": [{"note": "2120457", "noteTitle": "2120457 - Reorganization: Entries disappeared from the objectlist after regeneration.", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You regenerated the objectlist for a certain object type after the specific settings have been changed. As a result all the previously generated objects suddenly disappeared from the objectlist.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profitcenter reorganization, Objectlist generation, Specific restrictions, FAGL_R_PL_RES_S</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The reason is the fact that reorganization framework delivers in certain cases the object type specific restrictions defined for a specific field devided into  different lines in the table IT_RESTRICTION_SPECIFIC which are then incorrectly transformed into a where clause. This can happen if the object type specific restrictions have been changed after the object generation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This is a program error. Please implement this correction or download the corresponding support pack.</p>", "noteVersion": 2}, {"note": "2132800", "noteTitle": "2132800 - PRCTR: Central master data system MDG", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the profit center reorganization via the activation of the business function \"FI-GL (New), Profit Center Reorganization and Segment Reports\" (FIN_GL_REORG_1). At the same time, you use the central administration of the material master data, that is, the master data governance (MDG) business function is active. You want to know which application controls the profit center change for materials and which settings are required for this.</p>\n<p>As part of the profit center change for materials, the system does not only adjust the master data in the table MARC, but also performs transfer postings from the old to the new profit center. It is absolutely necessary that the stock determination is performed at the same time as the master data change. This can be ensured only via the profit center reorganization business function. To enable this, the change of the profit center must be removed from the scope of the master data governance (MDG).</p>\n<p>To do this, proceed as follows:</p>\n<p>a) Remove the \"Profit Center\" field from the governance scope of MDG:</p>\n<p>Call transaction MDGIMG (\"Master Data Governance Customizing\") and choose \"General Settings -&gt; Process Modeling -&gt; Define Governance Scope\". Select the data model MM and then select the \"Referencing Relationships\" view. For the entity type PRCTR, change the setting for \"Governed\" from \"Yes\" to \"No\". Note that this path may be different depending on the release. If you have any questions, contact your MDG consultant.</p>\n<p>b) Reduce the IDoc MATMAS.</p>\n<p>Use transaction BD53. For instructions, go to: <a href=\"http://wiki.scn.sap.com/wiki/display/ABAP/Reduced+Message+Types\" target=\"_blank\">http://wiki.scn.sap.com/wiki/display/ABAP/Reduced+Message+Types</a></p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MDG, master data governance</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>None</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For further questions regarding the settings in MDG or the IDoc reduction contact your consultant.</p>\n<p> </p>", "noteVersion": 1}, {"note": "1880655", "noteTitle": "1880655 - PRCTR/SEG: Error message FAGL_REORGANIZATION 535", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you change the account assignment of receivables and payables, the system issues<br/>error message FAGL_REORGANIZATION 535.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The system issues the error in the following context: You have a down payment clearing for a down payment, and the down payment has been reversed in the meantime.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the following corrections.<br/><br/>The error occurs in the test system. After you have implemented the corrections, restart the reorganization with a new backup of your productive system because the error is triggered by the generation of the objects.<br/><br/>If the productive system issues the error during account assignment change, submit a message to SAP so that SAP Support can support you in cleaning up the objects.<br/></p></div>", "noteVersion": 6}, {"note": "2023029", "noteTitle": "2023029 - PRCTR/SEG: Generate AP/AR - recognition of rounding differences", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>See SAP Note 2005825.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization</p>\n<p>CL_FAGL_R_SPLIT_REORG: COMPARE_REORG_SPL_WITH_ORI, CHECK_AND_ELIM_ROUND_REORG_SPL; CHECK_ELI_REORG_SPL_WT_SPLINFO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The new method CHECK_ELI_REORG_SPL_WT_SPLINFO tries to recognize rounding differences after the simulation of Accounts Payable/Accounts Receivable (AP/AR) documents. In addition, document splitting information has been integrated in this method. Document splitting information is also taken into account during the subsequent test via the method COMPARE_REORG_SPL_WITH_SPLINFO (SAP Note 2005825).</p>\n<p>The method CHECK_ELI_REORG_SPL_WT_SPLINFO will replace the method CHECK_AND_ELIM_ROUND_REORG_SPL. To activate the method CHECK_ELI_REORG_SPL_WT_SPLINFO, set the parameter ls_in_use = abap_true in the method COMPARE_REORG_SPL_WITH_ORI.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this SAP Note.</p>", "noteVersion": 1}, {"note": "1984087", "noteTitle": "1984087 - Accessing unsorted tables for profit center reorganization", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With the profit center reorganization, the system may issue errors when accessing unsorted tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the Support Package or implement the correction instructions.</p>", "noteVersion": 1}, {"note": "2057183", "noteTitle": "2057183 - PRCTR: Analysis report for account reassignment of receivables and payables", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note provides an analysis report for the account reassignment of receivables and payables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note provides support during troubleshooting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this SAP Note or import the relevant Support Package.</p>\n<p>Follow the instructions in the manual post-implementation steps. Alternatively, you can implement the attached transport for the translation-relevant components of the report FAGL_R_APAR_REASSIGN_SIM (see attachment \"Note 2057183 - Translation relevant objects.zip\").</p>\n<p><strong>Note for Release 605 only:</strong><br/>Note that there are two correction instructions for this release. The second one contains subsequent changes to the method <br/>METH CL_FAGL_R_OBJ_TYPE_APAR IF_FAGL_R_OBJ_TYPE~REASSIGN_MASTERDATA.</p>\n<p><strong>Note:</strong> <br/>You can execute the report FAGL_R_APAR_REASSIGN_SIM only as of Release 606.<br/>For Release 605, the name of the report is FAGL_R_APAR_REASSIGN_SIM_605.</p>\n<p>****************************************************************************************************************************************</p>\n<p>General information: The display screens are divided into several areas. Choose \"Goto\" to call additional analyses. If the system does not determine any data for an area, it remains empty.</p>\n<p>Currently:</p>\n<ol>\n<li><strong>Object Information</strong><br/>The upper table gives you an overview of the selected object (corresponds to the table FAGL_R_PL_OBJECT).<br/>The lower table displays the object entries for all relevant tables of an Accounts Payable/Accounts Receivable (AP/AR) object including grouping and invoice reference).</li>\n<li><strong>Analysis Account Reassignment</strong><br/>During the account reassignment of AP/AR objects, the system adjusts only the tables FAGL_SPLINFO and FAGL_SPLINFO_VAL.<br/>The system compares these tables to the relevant reorganization tables.<br/>If warnings occur during the account reassignment, the system displays these as warning messages or error messages in the lower table.</li>\n<li><strong>Plan Overview<br/></strong>Each object can be converted several times in various plans.</li>\n<li><strong>Analysis Hierarchy</strong><br/>This displays the origin of the profit center (PRCTR) and the conversion status. </li>\n</ol>", "noteVersion": 8}, {"note": "1981792", "noteTitle": "1981792 - Additional info for users", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the Reorganization framework. Important information about licenses and functionality are not displayed to the user.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This information was not provided.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the relevant Support Package or implement the attached corrections.</p>", "noteVersion": 1}, {"note": "2019645", "noteTitle": "2019645 - PRCTR-REORG: Error F5 800", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>For profit center reorganization of materials, the system issues error F5 800.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>profit center reorganization</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The material ledger is active. There are materials for which there are no differences for all the currency types.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached changes.</p>", "noteVersion": 2}, {"note": "1988777", "noteTitle": "1988777 - PRCTR/SEG: Exclusion of cleared documents during display II", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you display the object lists for receivables and payables, the already cleared items are also displayed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Reorganization<br/>Accounts Payable/Accounts Receivable, AP/AR, display, object lists</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Note 1974980</p>\n<p>Implement SAP Note 1987194 for Releases SAP_FIN 700/617 and SAP_APPL 606 due to method<br/>CL_FAGL_R_OBJ_TYPE_APAR: IF_FAGL_R_OBJ_TYPE~GET_ADDITIONAL_FIELDS.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instructions.</p>\n<p>To display the field, proceed as set out in SAP Note 1974980.</p>\n<p>The system will evaluate the field according to the table FAGL_R_APAR-XDEL. The system will update this field \"only\" with regeneration (also takes place during the reassignment). For this reason, there may be differences with the tables BSAK/BSAD because current clearings are <strong>not</strong> taken into account to display the number of reorganized open items even after completion of the reorganization.</p>", "noteVersion": 4}, {"note": "1987684", "noteTitle": "1987684 - PRCTR: Generate AP/AR - runtime analysis", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The first time you generate AP/AR, long runtimes are calculated.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The first time you generate AP/AR, long runtimes are calculated.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this SAP Note to support the runtime analysis.<br/>The system then write the number of entries and percentage of processing completion in the job log.</p>", "noteVersion": 5}, {"note": "2148363", "noteTitle": "2148363 - PRCTR/SEG: FAGL_REORGANIZATION 566 (XXV)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you generate receivables and payables, the system does not take technical fields into account. This results in error 566 during reassignment.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segment reorganization<br/>Reorg<br/>Generate<br/>FAGL_REORGANIZATION 566<br/>FAGL_R_SPL<br/>Technical fields</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In the same way as for SAP Notes 2055537/2070401, the indicator 'Tax on sales/purchases code' - MWSKZ must be taken into account.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections. You have to implement this SAP Note before generating receivables and payables.</p>\n<p>Afterwards, the system treats the receivable or payable as if the profit center were directly assigned to an account (artificial level 1 object).</p>", "noteVersion": 5}, {"note": "1649426", "noteTitle": "1649426 - Restriction Characteristics for Segment Reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You are using the functionality of segment reorganization and you want to restrict the reorganization to a company code. But this is not available.<br/><br/>The BC set FAGL_REORG_3_V_FAGL_R_OBJTDI0 needs a change.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Segment reorganization, FAGL_REORG_SEG_SFWC_E, FAGL_REORG_3_V_FAGL_R_PLTYRE0</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Reason : A BC set was not delivered.<br/>Prerequisites : You have activated business function FIN_GL_REORG_SEG.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please implement the correction instructions.</p></div>", "noteVersion": 1}, {"note": "2097091", "noteTitle": "2097091 - PRCTR: Analysis report for stock determination of CO orders", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This report displays the balances of the various CO orders in a list for a reorganization run for profit centers. This can be helpful when you want to analyze problematic cases. This is only a display report. It does not change any data.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization, WIP, material stock</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note provides support during troubleshooting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program.</p>", "noteVersion": 1}, {"note": "2025982", "noteTitle": "2025982 - Change of an assignment possible even though the reorg. plan is closed", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When user displays the objects of the already closed reorganization plan he can still change the object's status as well as object's owner regardless of reorganization plan status or change/display authorization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Reorganization, Change Person Responsible, CL_FAGL_R_UI_CHANGE_RESP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please, implement this  SAP Note manually or download the corresponding support package.</p>", "noteVersion": 2}, {"note": "2100217", "noteTitle": "2100217 - <PERSON><PERSON><PERSON>_REORGANIZATION 335: Empty key date in the long text", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You receive the error message FAGL_REORGANIZATION 335 during settlement or settlement reversal due to already executing Profit center reorganization. However in the long text the key date of the respective reorganization plan is initial.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FAGL_REORGANIZATION 335, PC reorganization, FAGL_R_CHECK_SETTLEMENT_REVERS, CHECK_SETTLEMENT_REVERSAL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is due to program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the advance code correction.</p>", "noteVersion": 1}, {"note": "1964504", "noteTitle": "1964504 - PRCTR: Check of transfer of billing documents (IV)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With SAP Notes 1837340/1864097/1880282, the transfer of billing documents is checked.</p>\n<p>The entire partition is not set to reassignable even though the error FAGL_REORGANIZATION 343 occurs for individual documents.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Reorganization</p>\n<p>FAGL_REORGANIZATION 343</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached source code corrections by importing the Support Package or implementing the correction instructions.</p>", "noteVersion": 1}, {"note": "2005825", "noteTitle": "2005825 - PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (XX)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>See SAP Note 1910342.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization</p>\n<p>CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 1910342.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>\n<p>When the result of splitting the rounding differences fails, the system can no longer determine the reference to the object that provides the account assignment. As a result, it has to handle the receivable or payable as an object of the first level of the derivation hierarchy. Consequently, the system treats it as if the profit center account were assigned directly (artificial level 1 object).</p>\n<p>You have to implement this SAP Note before generating receivables and payables.</p>", "noteVersion": 3}, {"note": "1987207", "noteTitle": "1987207 - PRCTR: AP/AR - Document account not reassigned despite status message of account reassignment successful or higher", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Although a document appears with a status message of account reassignment successful or a later status in the object overview, the document account was not reassigned. The document appears as processed in the reorganization tables, but not in the general ledger view (table FAGL_SPLINFO). The original PRCTR segment appears there.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Profit center reorganization<br/>Reorg</p>\n<p>FAGL_REORGANIZATION 568<br/>CL_FAGL_R_SPLIT_REORG: COMPARE_REORG_SPL_WITH_SPLINFO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Due to adjustments to the document split fields, additional or other document split characteristics would be processed during the reorg process and set in the general ledger view than the ones used when the document was generated. In SAP Note 891144 (item 6. - Document splitting characteristics are supplemented or changed subsequently), we already pointed out that such changes has critical side-effects.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To prevent the account reassignment of such open items, the system will issue error 568 in the future. SAP Note 2005825 was written so that, in the future, such documents can be recognized when they are generated (method CL_FAGL_R_SPLIT_REORG: COMPARE_REORG_SPL_WITH_SPLINFO).</p>\n<p>Before you start a PRCTR reorganization, make sure that the documents are adjusted in accordance with the current document split fields. Note that SAP does not assume any responsibility for these adjustments to the document split fields; we recommend contacting your consultant for assistance.<br/><br/><strong>Implement all current SAP Notes from SAP Note 1471153.</strong></p>", "noteVersion": 6}, {"note": "1958869", "noteTitle": "1958869 - PRCTR/SEG: Dump during the reassignment of receivables and payables - manual split 566 (XIX)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the reassignment of receivables and payables, a dump occurs.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566</p>\n<p>CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL, CL_FAGL_R_OBJ_TYPE_APAR: R_UPDATE_ACCIT_P</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Note 1939299</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 3}, {"note": "2015638", "noteTitle": "2015638 - Additional balancing fields - exceptions handling", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have maintained customer specific fields in the include CI_FAGL_R_MAP_CUST and propably applied note 2011705 in order to dermine values for these fields from the object master data. Unfortunately during the run you get a short dump which says that table type 'XXXX' was not found.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FAGL_REORGANIZATION, CHECK_ADD_BAL_FIELDS,  Additional fields,</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The root cause of the short dump is the uncatched exception raised by the method  cl_abap_structdescr=&gt;describe_by_name( lv_tabname ) in the check_add_bal_fields.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this correction note manually or download the corresponding support pack.</p>", "noteVersion": 4}, {"note": "1895810", "noteTitle": "1895810 - Tim<PERSON> for CO accrual/deferral posting", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You execute an accrual calculation in your system. The accrual/deferral run does not end, even though the system contains only a small amount of data.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>KSA3, FIN_GL_REORG_1</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have activated the business function FIN_GL_REORG_1 in your system.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections in your system.</p></div>", "noteVersion": 4}, {"note": "2048665", "noteTitle": "2048665 - PRCTR/SEG: Generate AP/AR - recognition of rounding differences III", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This is a correction for SAP Note 2043647.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization</p>\n<p>CL_FAGL_R_SPLIT_REORG: COMPARE_REORG_SPL_WITH_SPLINFO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See symptom.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this SAP Note. You have to implement this SAP Note before generating receivables and payables.</p>", "noteVersion": 2}, {"note": "2090764", "noteTitle": "2090764 - PRCTR: Maintenance of specific restrictions for unassigned purchase orders not possible", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to maintain specific restrictions for the unassigned purchase order (PO) object type in the context of a profit center reorganization. However, the system does not display any tables or fields in the input help.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CL_FAGL_R_OBJ_TYPE_001_PO, PO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections. Afterwards, the system should display the table EKPO and its related fields for selection.</p>", "noteVersion": 1}, {"note": "2093824", "noteTitle": "2093824 - PRCTR/SEG: FAGL_REORGANIZATION 566 (XXIII)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the reassignment of accounts payable/receivable (AP/AR), the system issues error message FAGL_REORGANIZATION 566.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization<br/>FAGL_REORGANIZATION 566<br/>CL_FAGL_R_OBJ_TYPE_APAR: R_UPDATE_ACCIT_P<br/>KDAUF, KDPOS, AUBEL, AUPOS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>During partial account assignments to sales order items, the system does not correctly assign the \"new\" PRCTRs in the internal tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections. Then start the reassignment again.</p>", "noteVersion": 10}, {"note": "1718793", "noteTitle": "1718793 - FIN_GL_REORG_SEG Object type GL should be reassignable", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The G/L accounts object type is defined as relevant for just Generation and Transfer posting. So the second level objects are never generated and this results in problems with closing of a reorganization plan.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>G/L object type, Segment Reorganization, FAGL_REORG_3_V_FAGL_R_OBJTY</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Reason : BC set definition error<br/>Pre-requisites : You should be on EhP4 and business function FIN_GL_REORG_SEG is active.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the manual correction instructions.</p></div>", "noteVersion": 1}, {"note": "1974980", "noteTitle": "1974980 - PRCTR/SEG: Exclusion of cleared documents during display", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you display the object lists for receivables and payables, the already cleared items are also displayed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>profit center reorganization<br/>reorg<br/>APAR display, object lists</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>When you generate in a reorganization plan, open receivables and payables are selected. With each reassignment or regeneration, new open receivables and payables are selected and the system checks whether those previously generated have been canceled or cleared in the meantime - the last one is registered in the flag FAGL_R_APAR-XDEL. When you display the object lists, however, all receivables and payables included in the plan are displayed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li>\n<div>Implement the attached correction instructions.<br/>The system then displays the field FAGL_R_APAR-XDEL for selection. Each time the object list is called, the system <strong>checks once again</strong> which items have been cleared and sets the field to X, but the database is not updated. Therefore, this can result in differences with the table FAGL_R_APAP, which represents the status of the last run (generate or reassign).</div>\n</li>\n<li>\n<div><strong>IMG</strong><br/><strong>\"Specify Display Characteristics for Each Object Type\"</strong> - the change becomes visible only after you restart the Web Dynpro application</div>\n</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Plan Type</td>\n<td>Object Type</td>\n<td>Table Name</td>\n<td>Field Name</td>\n<td>Field Label:</td>\n<td>Used</td>\n<td>Selection Field</td>\n<td>Sequence Number</td>\n</tr>\n<tr>\n<td>001</td>\n<td>AP</td>\n<td>FAGL_R_APAR</td>\n<td>XDEL</td>\n<td>Deleted</td>\n<td>X</td>\n<td>X</td>\n<td> </td>\n</tr>\n<tr>\n<td>001</td>\n<td>AR</td>\n<td>FAGL_R_APAR</td>\n<td>XDEL</td>\n<td>Deleted</td>\n<td>X</td>\n<td>X</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>Maintenance of selection criteria in the object list;<br/>the field \"Deleted Entry\" is also displayed.</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Deleted Entry</td>\n<td>=</td>\n<td> </td>\n<td>Display of open receivables and payables</td>\n</tr>\n<tr>\n<td>Deleted Entry</td>\n<td>=</td>\n<td>X</td>\n<td>Display of already cleared receivables and payables that were open in a previous selection</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>Via settings, you can include the field as a selection criterion.</li>\n</ul>", "noteVersion": 3}, {"note": "1982408", "noteTitle": "1982408 - PRCTR: Object type not determined (FAGL_REORGANIZATION505)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you generate receivables or payables, the system issues the following warning message for items that have been posted to several account assignment objects (for example, WBS element and cost center):<br/>\"Object type not determined for payable/receivable &amp;1 &amp;2 &amp;3\" (FAGL_REORGANIZATION505)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Profit center reorg<br/>Reorg<br/>PS_PSP_PNR, KOSTL, KSTRG, AUFNR, NPLNR, AUBEL<br/>CO_KAKST, CO_KAAUF, CO_KAKTR, CO_KANPL, CO_KAPRO, CO_KAKDA<br/>POA, SO<br/>WBS<br/>FAGL_REORGANIZATION 505</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Note 1762744: The function for multiple account assignment for the sales order (CO_KAKDA: AUBEL/AUPOS) and cost center (CO_KAKST: KOSTL) is missing.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Supplement to SAP Note 1762744; implement the attached correction instructions.</p>", "noteVersion": 2}, {"note": "1984829", "noteTitle": "1984829 - PRCTR/SEG: Dump during reassignment - TSV_TNEW_PAGE_ALLOC_FAILED", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the reassignment of receivables and payables, the runtime error TSV_TNEW_PAGE_ALLOC_FAILED occurs. Under \"Active Calls/Events\" in the dump, there are a lot of entries for method R_CHECK_SPLIT_RESULT_P.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Resource shortage</p>\n<p>CL_FAGL_R_OBJ_TYPE_APAR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 3}, {"note": "2006565", "noteTitle": "2006565 - PRCTR: Activating checkpoints", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>For the problem analysis within the profit center reorganization, jobs have to be started in the customer system. For this, you have to set a dialog indicator in the debugging mode. In the customer system, the relevant authorizations are often missing to set this indicator in the debugging mode. With this SAP Note, you can start the dialog processing using the activation of a checkpoint group.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Assertion, checkpoint</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This concerns tools for troubleshooting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 2}, {"note": "1873064", "noteTitle": "1873064 - PRCTR: Valuation classes ignored", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note delivers two error symptoms:<br/>You carry out the profit center reorganization in a plant in which the material ledger is activated. Transfer postings are not generated in all currencies.<br/>Special stocks are reorganized. The transfer postings are not generated on an account-specific basis.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MBEW; QBEW; EBEW; CL_FAGL_R_OBJ_TYPE_001_MAT;</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Profit centers are reorganized for plants in which the material ledger is active.<br/>Special stocks should be reorganized. The same valuation classes are not maintained for the individual customer stocks and for the master segments.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Create new message texts. To do this, call transaction SE91, go to message class FAGL_REORGANIZATION and create the following messages:</p>\n<p>Message\tMessage Short Text\t\t\t\t\t   Self-Explanatory</p>\n<p>223           Error determining the current period                                                               X<br/>224           Error determining the previous period                                                          X<br/>225           No period data for the current period in the material ledger</p>\n<p>226           Error reading material ledger period data                                                          X</p>\n<p>Maintain the following text as the long text for message 225:</p>\n<p class=\"U1berschriftnurfrF1-Doku\">CAUSE&amp;</p>\n<p class=\"ASStandardabsatz\">The current period defined in the material ledger differs from the current period.</p>\n<p class=\"U1berschriftnurfrF1-Doku\">&amp;SYSTEM_RESPONSE&amp;</p>\n<p class=\"ASStandardabsatz\">As a result, no material ledger data is determined for the current period.</p>\n<p class=\"U1berschriftnurfrF1-Doku\">&amp;WHAT_TO_DO&amp;</p>\n<p class=\"ASStandardabsatz\">Check the current period in the material ledger and change this if necessary.</p>\n<p class=\"U1berschriftnurfrF1-Doku\">&amp;SYS_ADMIN&amp;</p>\n<p>Then implement the attached corrections.</p>\n<p> </p>\n<p> </p></div>", "noteVersion": 6}, {"note": "1866467", "noteTitle": "1866467 - Object list of assets with service orders", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the reorganization of profit centers. No assets that are exclusively assigned to a service order (order type 30) as a 2nd level object are included in the list.<br/>In the asset master record, this service order is entered as a cost order.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>PRCTR, CAUFN</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The function is not available.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections as described in the attached correction instructions.<br/><br/>The following nodes are to be added:</p> <ul><ul><li>O30-FA</li></ul></ul> <ul><ul><li>O30-FA-POA</li></ul></ul> <ul><ul><li>O30-FA-POA-AP</li></ul></ul> <ul><ul><li>O30-FA-AP</li></ul></ul> <ul><ul><li>O30-FA-AR</li></ul></ul> <p><br/><b>Adjustment of the derivation hierarchy</b><br/></p> <ul><li>Proceed as described in the manual activities or import the Support Package.</li></ul> <p><br/>Note that existing reorganization plans will continue to use the derivation hierarchy that was valid at the time of creation. If you want to use the derivation hierarchy that is changed with this SAP Note, you must create a new reorganization plan (and delete the existing reorganization plan, if required).<br/></p></div>", "noteVersion": 4}, {"note": "2131911", "noteTitle": "2131911 - PRCTR: Error log is incomplete", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You execute a profit center reorganization. The error log for the stock determination of materials does not contain all messages.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CL_FAGL_R_OBJ_MM_SERVICES_001</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is an error:</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 1}, {"note": "2009162", "noteTitle": "2009162 - PRCTR/SEG: Rounding differences for foreign currency valuation differences", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You cannot transfer post receivables and payables because PRCTR = ########## is referenced in the internal reorganization tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization</p>\n<p>CL_FAGL_R_SPLIT_REORG: IS_ROUND_SIM_MODE_ACTIVE</p>\n<p>FAGL_BSBW_HISTRY<br/>FAGL_R_APAR<br/>FAGL_R_BLNCE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>For the reassignment, the differences for foreign currencies are divided again due to the document split. The document split should process any rounding differences that may arise in the document split (in contrast to SAP Note 1869309 - when generating, the rounding differences are adjusted by the reorganization tool due to the additional reorganization characteristics).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 6}, {"note": "2098392", "noteTitle": "2098392 - PRCTR: Analysis report for balance determination for network activities and cost objects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This report displays the balances of network activities and cost objects in a list for a reorganization run for profit centers. This can be helpful when you want to analyze problematic cases. This is only a display report. It does not change any data.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization, WIP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note provides support during troubleshooting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program.</p>", "noteVersion": 1}, {"note": "2125807", "noteTitle": "2125807 - Segment reorganization: Do not select deactivated assets", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the reorganization of segments, the system also selects assets that are already deactivated.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instructions or import the specified Support Package.</p>\n<p>Deactivated assets are then ignored.</p>", "noteVersion": 3}, {"note": "2048514", "noteTitle": "2048514 - PRCTR/SEG: Generating clearing documents", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you <strong>generate</strong> clearing documents that are linked by an entry of an invoice reference (BSEG-REBZG) and due to additional documents that are linked only by table BSE_CLR, the system writes additional rows with PRCTR =  ########## in the table FAGL_R_SPL with rounding differences. These documents cannot be <strong>transfer posted</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segment reorganization<br/>Reorganization<br/>Generate, transfer post</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See symptom.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 8}, {"note": "2009252", "noteTitle": "2009252 - PRCTR: Checkpoint group for dialog indicator", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You try to debug the program execution for the profit center reorganization, but you do not have change authorization to debug. This authorization is absolutely necessary to activate the dialog flag.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CL_FAGL_R_PLAN,  rv_xdialog,   IS_XDIALOG_P</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Authorizations are often missing on the production system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections from SAP Note 2006565 and note the following procedure:</p>\n<p>Activate the checkpoint group Reorg_01 using transaction SAAB. On the \"Activation\" tab page for the \"Assertions\" selection, choose the \"Break\" or \"Abort\" option. This selection activates the dialog flag.</p>\n<p>We recommend you also activate the \"Break\" option for the \"Breakpoints\" selection; this causes the system to activate an additional breakpoint in the program. Alternatively, you can also do these activations in the debug mode.</p>\n<p>Save your changes. The activation is now complete. Then the profit center reorganization application should be started.</p>\n<p><strong>Note on authorizations</strong></p>\n<p>When you activate a group, the system checks the display authorization for debugging. If a user is to be able to activate a checkpoint group for other users or all users, the change authorization for debugging is required (activity 02). The system also checks the authorization object S_ADMI_FCD (with PADM).</p>\n<p><strong>Note on activation</strong></p>\n<p>If you activate a checkpoint group at the user level, the activation is automatically valid for all servers Conversely, an activation on the server level causes the checkpoint group to be valid for all users. In the standard system, the group is always only activated for the respective user for the current day.</p>\n<p><strong>Vital to note</strong></p>\n<p>When you exit the system, it is essential that you deactivate the group because there is no parallel processing in the dialog mode and so part of the function will not run normally.</p>\n<p> </p>", "noteVersion": 2}, {"note": "1939237", "noteTitle": "1939237 - Document splitting: Line items returned with zero amounts and quantities", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you generate the reorganization worklist, the system issues an error message.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center, reorganization</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>When you generate the reorganization worklist, the system performs document splitting so that it can determine the reference between the reference object and any open items (customers and vendors). Document splitting produces lines with zero amounts and quantities, which are relevant for the general ledger (GL). These lines are unavoidable when you generate the reorganization worklist.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instructions.</p>", "noteVersion": 4}, {"note": "1955532", "noteTitle": "1955532 - PRCTR: Determining material stock for cost element category 90", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note enhances the material stock determination functions within profit center reorganization. Up to now, anonymous stock and special stock for materials were determined using only the \"Material\" object type. Furthermore, transfer postings were always determined with the profit center from the material stock. However, special stock for sales and distribution documents and special stock for WBS elements are posted with the profit center of the relevant CO object if cost element category 90 is defined for the relevant balance sheet account. In these cases, the previous standard logic results in unwanted balances in Profit Center Accounting.</p>\n<p>Note that these changes concern a redesign of material stock determination. Material stock is then not only determined under the \"Material\" object type but also under the \"Sales and Distribution Documents\" and \"WBS Elements\" object types. Unfortunately, due to the extensive program modifications, a manual pre-implementation step cannot be avoided.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CL_FAGL_R_OBJ_MAT_SERVICES_001, CL_FAGL_R_OBJ_TYPE_001_MAT, CKMLMVADMIN, REORG90<br/>IF_FAGL_R_OBJ_TYPE~REASSIGN_MD_AND_GET_BALANCES</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>A function is missing.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program enhancement. Afterwards, the system behavior is as follows:</p>\n<p>Scenario 1: Cost element category 90 is not defined for the BSX account. In this case, the system behavior remains unchanged. All special stock is transfer posted, together with the anonymous stock and the profit center from the material master, separately for each account (if different valuation classes are configured for each material).</p>\n<p>Scenario 2: Cost element category 90 is defined for the BSX account AND you have used the indicator REORG90_XXXX to activate the new logic in the table CKMLMVADMIN. In addition, the sales and distribution document or WBS element must be a CO object.</p>\n<p>In the table CKMLMVADMIN, create the following entry for each controlling area:</p>\n<p>kkey = REORG90_XXXX where XXXX is the controlling area.</p>\n<p>kdata = X</p>\n<p>In this case, anonymous material stock continues to be determined using the \"Material\" object type and transfer posted with the profit center from the material master record. However, special stock is determined using the \"Sales and Distribution Document\" or \"WBS Element\" object type and transfer posted with the profit center from the account assignment object. This logic ensures that the special stock of all materials associated with a sales and distribution document or WBS element is grouped together for each account. The system does not check materials for a specific restriction in the \"Material\" object type.</p>\n<p> IMPORTANT:</p>\n<p>Note that you are not permitted to activate this SAP Note during a current reorganization. In other words, you are not permitted to reset or change the relevant table entry during this time. It is absolutely necessary that all reorganization plans have the status \"Closed\" (= 20).</p>\n<p> </p>", "noteVersion": 15}, {"note": "2043647", "noteTitle": "2043647 - PRCTR/SEG: Generate AP/AR - recognition of rounding differences II", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>See SAP Notes 2005825 / 2023029.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization</p>\n<p>CL_FAGL_R_SPLIT_REORG: COMPARE_REORG_SPL_WITH_SPLINFO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Note 2005825 provided method COMPARE_REORG_SPL_WITH_SPLINFO, which takes account of the SPLITI criteria during the comparison. The currency key is not taken into consideration in the comparison of local currencies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this SAP Note. You have to implement this SAP Note before generating receivables and payables.</p>", "noteVersion": 2}, {"note": "2002242", "noteTitle": "2002242 - PRCTR/SEG: Error message FAGL_REORGANIZATION 566/567 II - code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you change the account assignment of receivables and payables, the system issues<br/>the error message FAGL_REORGANIZATION 566 \"Reassignment:  PRCTR/SEGMENT deviation in FAGL_SPLINFO and FAGL_R_SPL &amp;1\"</p>\n<p>(See also SAP Note 1910342.)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg</p>\n<p>FAGL_REORGANIZATION 566; FAGL_REORGANIZATION  567</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The error message FAGL_REORGANIZATION 566 may be caused by a variety of reasons. For example, some SAP Notes may not have been implemented fully or at all in your system, the Customizing for document splitting may have been changed since the activation, or customer line items/vendor line items may have been assigned manually.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li><strong>Implement the attached program corrections or import the relevant Support Package.<br/></strong></li>\n<li><strong>Check</strong> <strong>that all reference notes from this SAP Note have been implemented in your system </strong>(with the exception of composite SAP Note1471153). If SAP Notes are missing in your system, implement these and then check if the error message still appears.<br/>For some of these SAP Notes, it is essential that you have implemented them <strong>before the first generation of the AP/AR objects</strong> in the system. If this was not the case in your system, you must fully reset your test environment after implementing these SAP Notes subsequently. In this case, never configure the message FAGL_REORGANIZATION 567 as a warning.</li>\n<li>Open a customer message under the component FI-GL-REO-GL if you have ensured that all SAP Notes were promptly and correctly implemented in your test system and you still get error message 566 when you try to reassign individual documents.<br/>This <strong>special case</strong> may occur, for example, if the profit center was manually manually entered in an incoming invoice. SAP Support staff will check if your test system is affected by one of these special cases or if another solution is required. <br/>-&gt; <strong>Following confirmation from SAP Support staff</strong> that a special case of this type is involved, use transaction OBA5 to set the  message FAGL_REORGANIZATION 567 as a warning message. By configuring the message FAGL_REORGANIZATION 567 as a warning and thus skipping the error message FAGL_REORGANIZATION 566, you have the option of manually reassigning the affected document(s).</li>\n</ol>\n<p><strong>   Transaction: OBA5</strong></p>\n<ul>\n<ul>\n<li>Change the message control.<br/>Enter the work area FAGL_REORGANIZATION.</li>\n<li>Include the following entry:</li>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>MsgNo</td>\n<td>Message Text</td>\n<td>User Name</td>\n<td>Online</td>\n<td>BatchI</td>\n<td>Standard</td>\n</tr>\n<tr>\n<td>567</td>\n<td>Account assignment change: PRCTR/SEGMENT deviation...</td>\n<td> </td>\n<td><strong>W</strong></td>\n<td><strong>W</strong></td>\n<td>E</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 2}, {"note": "1694903", "noteTitle": "1694903 - SEG: FIN_GL_REORG_SEG should check whether New G/L is active", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The business function for segment reorganization (FIN_GL_REORG_SEG) should also check whether New G/L is active.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>FIN_GL_REORG_SEG, NewGL</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Reason : Program Error.<br/>Prerequisite ; you should be on EhP 6.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Apply the attached correction instructions.</p></div>", "noteVersion": 1}, {"note": "2130053", "noteTitle": "2130053 - PRCTR: Analysis report for account assignment change/generating receivables and payables II a", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This is an enhancement of the analysis report in accordance with SAP Note 2057183 and a prerequisite for the integration of the generation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note provides support during troubleshooting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this SAP Note or import the relevant Support Package.</p>", "noteVersion": 3}, {"note": "2116852", "noteTitle": "2116852 - SEG: Segment Reorganization for GL accounts is not correct", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">When trying to execute a segment reorganization on GL account, the calculation is not correct.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">Reason: Program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the correction instructions contained in this note.</p>", "noteVersion": 11}, {"note": "1984719", "noteTitle": "1984719 - Check on Hierarchy modification", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You modified the standard Reorganization hierarchy so that you deleted an object on the first level. This object however still exists on lower levels. This can cause runtime errors.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This issue is caused by a program errror.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the relevant Support Package or implement the attached corrections.</p>\n<p>New check is added to the Hierarchy modification customizing activity that avoids deleting an object on the first hierarchy level if it still exists on lower levels.</p>", "noteVersion": 1}, {"note": "1955477", "noteTitle": "1955477 - PRCTR/SEG: Error in reassignment of AP/AR; 566 (XVI)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you change the account assignment of receivables and payables, the system issues<br/>error message FAGL_REORGANIZATION 566.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Amount lines with amount = 0.00 in all currencies, as in SAP Note 1864855</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached source code. These amount lines are then eliminated.</p>", "noteVersion": 3}, {"note": "1675485", "noteTitle": "1675485 - Error AAPO190 when you make an account assignment change for assets", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the reorganization transfer posting, the system may incorrectly issue error AAPO 190 (\"Account object Segment differs from that in asset master record\") .<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>AAPO190, PRCTR, SEGMENT, ASSET_ORG_ASSIGNMENT_CHANGE<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error, which may arise if the profit center is changed, but the segment remains the same.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program correction.</p></div>", "noteVersion": 2}, {"note": "2136922", "noteTitle": "2136922 - PRCTR/SEG: AP/AR reassigned despite complete clearing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Receivables or payables are not reassigned (transferred), if an item was open at the time of generation, but already fully cleared at the time of the reassignment. The problem does not occur if a residual item is created as part of the clearing process.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Reorg<br/>Profit center reorganization<br/>Segment reorganization</p>\n<p>FAGL_REORGANIZATION 562<br/>FAGL_REORGANIZATION 502, FBRA (Reset Cleared Items)<br/>CL_FAGL_R_OBJ_TYPE_APAR: R_REASSIGN_DOCUMENTS_P, R_CHECK_CLEARED_APAR_P</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Note 1751367 does not cover the case described here.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached corrections no later than before executing the reassignment, and ideally before starting your reorganization project.</p>\n<p>Once the corrections are successfully implemented, the log for items cleared at the time of the reassignment shows the following information: '562  - Receivable/payable already cleared and will therefore not be reassigned\".</p>\n<p>Note that, due to the clearing, the item is not an element of the reorganization; that is, it would neither be reassigned nor transferred if the clearing were subsequently reset.</p>", "noteVersion": 3}, {"note": "2082695", "noteTitle": "2082695 - PRCTR/SEG:  Objects without plan assignment in the reorganization tables", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you generate receivables and payables, the table FAGL_R_APAR contains entries without triggering plan and account type.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization<br/>Migration to General Ledger Accounting (new)<br/>FAGL_R_APAR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This affects documents that were migrated to General Ledger Accounting (new) in advance.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the corrections below.</p>\n<p>Before each test, check whether all SAP Notes have been implemented in accordance with SAP Note 1471153.</p>", "noteVersion": 4}, {"note": "1961091", "noteTitle": "1961091 - PRCTR/SEG: FAGL_REORGANIZATION 533 during reassignment of AP/AR objects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the reassignment of accounts payable/receivable (AP/AR) objects, the system issues error message FAGL_REORGANIZATION 533.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>segment reorganization<br/>reorg<br/>FAGL_REORGANIZATION 533</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>Important: This assignment must be undone. Therefore, the original source code was set as a comment.</strong></p>\n<p><strong>Create an error message for the above errors under the component FI-GL-REO-GL.</strong></p>\n<p>For process chains in FI with partial payments, the affected documents (residual items) are already partially reassigned, but not the associated original document.</p>\n<p>Both documents are artificially level 1 objects.</p>\n<ul>\n<li>See table FAGL_R_APAR:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>GJAHR</strong></td>\n<td><strong>BELNR</strong></td>\n<td><strong>BUZEI</strong></td>\n<td>\n<p><strong>REBZG</strong></p>\n</td>\n<td>\n<p><strong>REBZJ</strong></p>\n</td>\n<td>\n<p><strong>REBZZ</strong></p>\n</td>\n<td>\n<p><strong>XDEL</strong></p>\n</td>\n<td><strong>STATUS</strong></td>\n<td><strong>XLEVEL1</strong></td>\n<td><strong>Comment</strong></td>\n</tr>\n<tr>\n<td>2012</td>\n<td>4500004520</td>\n<td>001</td>\n<td> </td>\n<td>0000</td>\n<td>000</td>\n<td> </td>\n<td>30</td>\n<td>X</td>\n<td>Posting line reassigned</td>\n</tr>\n<tr>\n<td>2013</td>\n<td>4900000573</td>\n<td>001</td>\n<td>4500004520</td>\n<td>\n<p>2012</p>\n</td>\n<td>001</td>\n<td> </td>\n<td>20</td>\n<td>X</td>\n<td>Posting line with error</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>See table FAGL_R_APAR:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>GJAHR</strong></td>\n<td><strong>BELNR</strong></td>\n<td><strong>BUZEI</strong></td>\n<td>\n<p><strong>REBZG</strong></p>\n</td>\n<td>\n<p><strong>REBZJ</strong></p>\n</td>\n<td>\n<p><strong>REBZZ</strong></p>\n</td>\n<td>\n<p><strong>REBZT</strong></p>\n</td>\n<td>\n<p><strong>XDEL </strong></p>\n</td>\n<td><strong>STATUS</strong></td>\n<td><strong>XLEVEL1</strong></td>\n<td><strong>Comment</strong></td>\n</tr>\n<tr>\n<td>2013</td>\n<td>2200000355</td>\n<td>001</td>\n<td>163500020</td>\n<td>2012</td>\n<td>001</td>\n<td>V</td>\n<td> </td>\n<td>20</td>\n<td>X</td>\n<td>Posting line with error</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 7}, {"note": "2139388", "noteTitle": "2139388 - PRCTR: Information for table FAGL_R_SPL and FAGL_SPLINFO", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As part of the profit center reorganization, changes are made for the object types PO and POA in the tables FAGL_SPLINFO and FAGL_R_SPL. For analysis purposes, it is necessary to document which reorganization plan is used for this.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PO, POA, FAGL_SPLINFO, FAGL_R_SPL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Report for analysis purposes</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program enhancement.</p>", "noteVersion": 3}, {"note": "2005245", "noteTitle": "2005245 - CheckMan: ATC Missing Interface Methods", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note brings a default coding of the interface methods IF_FAGL_R_OBJ_TYPE~GET_ADD_BAL_FIELDS, IF_FAGL_R_OBJ_TYPE~GET_BALANCES etc. for a different object types.  It is directly related to note 1921058 Additional balancing field in transfer posting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ATC checks, FAGL_REORGANIZATION, IF_FAGL_R_OBJ_TYPE_CUST, IF_FAGL_R_OBJ_TYPE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Missing interface methods implementation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this note or download the corresponding support pack.</p>", "noteVersion": 7}, {"note": "2125315", "noteTitle": "2125315 - PRCTR/SEG: Error 566 during reassignment for documents from online CO-FI integration (XXIV)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Documents posted as part of online CO-FI integration cannot be reassigned. (FAGL_REORGANIZATION 566)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566<br/>CL_FAGL_R_SPLIT_REORG: SIMADD_GET_SPLITINFO_FOR_DOC_P</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Documents posted by means of a transaction (BKPG-GLVOR = COFI) can use more than the local currencies defined in customizing (transaction OB22). This cannot be taken into account during document splitting in the reassignment and results in deviations -&gt; error 566.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections. You have to implement this SAP Note before generating receivables and payables.</p>\n<p>Afterwards, the system treats the receivable or payable as if the profit center account were assigned directly (artificial level 1 object).</p>", "noteVersion": 4}, {"note": "2115768", "noteTitle": "2115768 - FAQ: Profit center reorganization hierarchy", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have questions concerning the profit center reorganization hierarchy.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PRCTR reorganization<br/>Hierarchy, standard derivation hierarchy<br/>Excluding objects<br/>Closing a plan</p>\n<p>Empty PRCTR (source PRCTR)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note documents frequently asked questions and answers (FAQs) in relation to the profit center reorganization in General Ledger Accounting (new). Before you start your reorganization project, read SAP Note 1810605.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>1. What does \"standard hierarchy\" mean?</strong></p>\n<p>SAP provides Version 001 of the <strong>derivation hierarchy</strong> (standard derivation hierarchy version) for each reorganization plan type. This is defined in Customizing (SPRO):</p>\n<p>Financial Accounting (New)<br/>-&gt; General Ledger Accounting (New)<br/>---&gt; Reorganization<br/>----&gt; Basic Settings<br/>-----&gt; Remove Object Types from Derivation Hierarchy</p>\n<p>You can generate and change additional derivation hierarchy versions by creating copies.</p>\n<p>When you create a profit center reorganization plan, the selected derivation hierarchy version is transferred to the current plan and <strong>cannot</strong> be changed for the current plan. It is not possible to add object types during the project.</p>\n<p><strong>2. How can objects be excluded from the reorganization?</strong></p>\n<p>The following options can be used to exclude objects:</p>\n<p><strong>a) Exclusion when assigning profit centers to object lists</strong></p>\n<p>Only level 1 objects can be excluded from the reorganization.</p>\n<p>Under \"General Restrictions -&gt; Assignment\", the following profit centers (PRCTR) are also required:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Old PRCTR</td>\n<td>New PRCTR</td>\n<td> </td>\n</tr>\n<tr>\n<td>PRCTR A</td>\n<td>PRCTR A</td>\n<td>Status 60 (\"Not Included in Reorganization\") is set automatically.</td>\n</tr>\n</tbody>\n</table></div>\n<p>As a result, either the original profit center or a new profit center can be assigned to the object.</p>\n<p>After the object is released by the person responsible for the object, the status of the object changes to \"Not Included in Reorganization\" (if the new profit center is identical to the old profit center) or \"Approved for Further Processing\". Then, the assignment can no longer be changed. If subsequent changes are required, contact your consultant for profit center reorganizations.</p>\n<p><strong>b) Exclusion by means of restrictions</strong></p>\n<p>To ensure that only the relevant objects are displayed in the object list, you can restrict a reorganization plan to certain characteristics (known as <strong>restriction characteristics</strong>).<br/>(see Customizing/SPRO)</p>\n<p><strong>c) Use of the \"BAdI: Enhancement of Object List Generation\"</strong></p>\n<p>Here, you can set the target account assignment (MDATTR_NEW) to the original profit center. Therefore, in accordance with 2a), you can set the object to status 60 (\"Not Included in Reorganization\"). (see Customizing/SPRO)</p>\n<p><strong>d) Copying and changing the standard derivation hierarchy version and using the derivation hierarchy version in the current plan</strong></p>\n<p>You do this in Customizing (SPRO) under<br/>Financial Accounting (New)<br/>-&gt; General Ledger Accounting (New)<br/>---&gt; Reorganization<br/>----&gt; Basic Settings<br/>-----&gt; Remove Object Types from Derivation Hierarchy</p>\n<p><strong>Note that it is absolutely necessary that all object types that can be selected as a dependent object are also defined as level 1 objects. If you ignore this, dumps may occur. After you implement SAP Note 1984719, this receivable is checked in the program.</strong></p>\n<p>To exclude objects, proceed as described under 2 a), b), and c).</p>\n<p><strong>SAP recommendation:</strong><br/>Bear in mind that it is not possible to add object types during the project. Therefore, for the first test in a copy of the production system, use the standard derivation hierarchy to check which object types are not used for your reorganization.</p>\n<p><strong>3. When are the objects from the second hierarchy level (level 2 objects) included in a reorganization plan? What are level 1 and level 2 objects?</strong></p>\n<p>On the basis of the derivation hierarchy assigned to the reorganization plan, all object types listed there are generated initially. During the generation process, the system checks whether an object is considered to be a level 1 object or a level 2 object. <strong>Level 1</strong> objects are recognizable by the fact that the <strong>profit center is entered directly in the object</strong> and was not derived. Therefore, there is no reference to its higher-level object type. Level 2 objects are displayed as lower-level objects after the higher-level object has been REASSIGNED. Note that level 2 objects are not simulated.</p>\n<p><strong>Special scenario: Accounts payable/receivable (AP/AR)<br/></strong>To determine the higher-level object type for open items, document splitting involving all CO account assignments is executed again during the generation process. <br/>The higher-level object type is determined on the basis of existing CO account assignments. The following (incomplete) table contains examples in which the object type POA is set (the table header contains the CO account assignment field names).</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>KOSTL</td>\n<td>KSTRG</td>\n<td>ANLN1</td>\n<td>ANLN2</td>\n<td>AUFNR</td>\n<td>VORNR</td>\n<td>PRCTR</td>\n<td>EBELN</td>\n<td>EBELP</td>\n<td>ZEKKN</td>\n<td>VBELN</td>\n<td>POSNR</td>\n<td>PSPNR</td>\n<td>OBJ_TYPE</td>\n<td>Comment</td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>   X</td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>PC</td>\n<td>\n<p>If there is no other CO account assignment, this row is a<br/>level 1 object.</p>\n</td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>   X</td>\n<td>   X</td>\n<td>   X</td>\n<td>   X</td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>POA</td>\n<td>The tool expects the higher-level POA document and checks whether this document participates in the reorganization.</td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>   X</td>\n<td>   X</td>\n<td>   X</td>\n<td>   X</td>\n<td>   X</td>\n<td>   X</td>\n<td> </td>\n<td>POA</td>\n<td>The tool expects the higher-level POA document and checks whether this document participates in the reorganization.</td>\n</tr>\n<tr>\n<td>   X</td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>   X</td>\n<td>   X</td>\n<td>   X</td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>POA</td>\n<td>The tool expects the higher-level POA document and checks whether this document participates in the reorganization.</td>\n</tr>\n<tr>\n<td>......</td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>POA</td>\n<td>The tool expects the higher-level POA document and checks whether this document participates in the reorganization.</td>\n</tr>\n</tbody>\n</table></div>\n<p>During the generation process for AP/AR, there may be differences in relation to the original posting, which cause the document to be handled as an artificial \"level 1\" document (field FAGL_R_APAR-XLEVEL1 is set).</p>\n<p>This is necessary because, during a reassignment, it would not be possible to process the document in document splitting without errors (error FAGL_REORGANIZATION 566, among others).  Possible reasons for artificial \"level 1\" documents include (the following list is not complete):</p>\n<ul>\n<li>\n<div>Changes to Customizing for document splitting compared to the time when the document was entered.</div>\n</li>\n<li>\n<div>Simulation of the document splitter causes errors. Reasons include, among others:</div>\n</li>\n<ul>\n<li>User exits</li>\n<li>The PRCTRs in the original document were determined by inheritance and not by split rules. (use transaction: /nfagl_mig_sim_spl for the simulation of the document)</li>\n<li>Customer-specific messages</li>\n<li>Substitution (PRCTR/SEGMENT)</li>\n<li>Messages during the FI process: It may not be possible to recreate the document without errors during the generation process, for example, because:</li>\n<ul>\n<li>\n<div>The posting period is closed,</div>\n</li>\n<li>\n<div>Accounts are no longer defined,</div>\n</li>\n<li>\n<div>CO account assignments no longer have the permitted status.</div>\n</li>\n</ul>\n</ul>\n<li>\n<div>Clearing documents because they may cause a memory overflow  (see SAP Note 2085069)</div>\n</li>\n<li>\n<div>Rounding differences can no longer be assigned to the same rows. When you enter the document, rounding differences are assigned to individual split rows (see the rows in the table FAGL_SPLINFO/FAGL_SPLINFO_VAL). In the case of a new splitting in accordance with the CO account assignment, these rounding differences are redistributed and sometimes cannot be resolved.</div>\n</li>\n</ul>\n<p><strong>Note that it is absolutely necessary that all object types that can be selected as a dependent object are also defined as level 1 objects. If you ignore this, dumps may occur or the system may issue error message FAGL_REORGANIZATION 505.</strong></p>\n<p><strong>An overview of all objects is only possible after the reassignment.</strong><br/>In the <strong>list view</strong>, the objects are displayed as the sum of all objects in the first level and all sublevels for the relevant object type. In the <strong>hierarchy display</strong>, the objects are displayed under the object type used by the object to derive the profit center.</p>\n<p><strong>Example:</strong> <br/>You reorganize a material A to which profit center A is assigned. You have used this material in purchase orders and SD orders as well as in dependent invoices and payments.<br/><br/>During the reassignment process, dependent objects are generated and then displayed in the derivation hierarchy under the material.<br/><br/>The following is an extract of the standard derivation hierarchy (when you expand the material under the object list for the hierarchy display)<br/>(All objects marked with * have additional subobjects):</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>* Material</td>\n<td> </td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td>* Sales and distribution document</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td>* Purchase order (assigned)</td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>Payable</td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td>Receivable</td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td>Payable</td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td>* Purchase order (unassigned)</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td>Payable</td>\n<td> </td>\n</tr>\n<tr>\n<td>* Purchase order (unassigned)</td>\n<td> </td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td>Payable</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>* Purchase order (assigned)</td>\n<td> </td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td>Payable</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>* Sales and distribution document</td>\n<td> </td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td>* Purchase order (assigned)</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td>Payable</td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td>Receivable</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td> </td>\n<td>Payable</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>Receivable</td>\n<td> </td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>Payable</td>\n<td> </td>\n<td> </td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Example:</strong><br/>Prerequisite: Profit centers A and B are reorganized in the plan.<br/>You have posted three sales and distribution documents with material A and profit center A. In other words: In these cases, the profit center was derived from the material.<br/>Material A was posted in two additional sales and distribution documents, but the profit center in the document was set manually or by means of substitution.</p>\n<p><strong>Overview of generation:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Object type</td>\n<td>Objects</td>\n</tr>\n<tr>\n<td><strong>* Material</strong></td>\n<td><strong>1</strong></td>\n</tr>\n<tr>\n<td>* Purchase order (unassigned)</td>\n<td>0</td>\n</tr>\n<tr>\n<td>* Purchase order (assigned)</td>\n<td>0</td>\n</tr>\n<tr>\n<td><strong>* Sales and distribution document</strong></td>\n<td><strong>2</strong></td>\n</tr>\n<tr>\n<td>Receivable</td>\n<td>0</td>\n</tr>\n<tr>\n<td>Payable</td>\n<td>0</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Overview of reassignment: (Hierarchy display) after reassigning the material</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Object type</td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>Objects</td>\n</tr>\n<tr>\n<td><strong>* Material</strong></td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td><strong>1</strong></td>\n</tr>\n<tr>\n<td> </td>\n<td><strong>* Sales and distribution document</strong></td>\n<td> </td>\n<td> </td>\n<td><strong>3</strong></td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td><strong>* Purchase order (assigned)</strong></td>\n<td> </td>\n<td><strong>2</strong></td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>Payable</td>\n<td>0</td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td><strong>Receivable</strong></td>\n<td> </td>\n<td><strong>3</strong></td>\n</tr>\n<tr>\n<td>* Purchase order (unassigned)</td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>0</td>\n</tr>\n<tr>\n<td> </td>\n<td>Payable</td>\n<td> </td>\n<td> </td>\n<td>0</td>\n</tr>\n<tr>\n<td>* Purchase order (assigned)</td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>0</td>\n</tr>\n<tr>\n<td> </td>\n<td>Payable</td>\n<td> </td>\n<td> </td>\n<td>0</td>\n</tr>\n<tr>\n<td><strong>* Sales and distribution document</strong></td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td><strong>2</strong></td>\n</tr>\n<tr>\n<td> </td>\n<td>* Purchase order (assigned)</td>\n<td> </td>\n<td> </td>\n<td>0</td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td>Payable</td>\n<td> </td>\n<td>0</td>\n</tr>\n<tr>\n<td> </td>\n<td><strong>Receivable</strong></td>\n<td> </td>\n<td> </td>\n<td><strong>2</strong></td>\n</tr>\n<tr>\n<td> </td>\n<td>Payable</td>\n<td> </td>\n<td> </td>\n<td>0</td>\n</tr>\n<tr>\n<td>Receivable</td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>0</td>\n</tr>\n<tr>\n<td>Payable</td>\n<td> </td>\n<td> </td>\n<td> </td>\n<td>0</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Overview of reassignment: (List display) after reassigning the material</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Object type</td>\n<td>Objects</td>\n</tr>\n<tr>\n<td><strong>* Material</strong></td>\n<td><strong>1</strong></td>\n</tr>\n<tr>\n<td>* Purchase order (unassigned)</td>\n<td>0</td>\n</tr>\n<tr>\n<td><strong>* Purchase order (assigned)</strong></td>\n<td><strong>2</strong></td>\n</tr>\n<tr>\n<td><strong>* Sales and distribution document</strong></td>\n<td><strong>5</strong></td>\n</tr>\n<tr>\n<td><strong>Receivables</strong></td>\n<td><strong>5</strong></td>\n</tr>\n<tr>\n<td>Payables</td>\n<td>0</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Further information and examples of the derivation hierarchy are available on SAP Help Portal under:<br/></strong>Profit center reorganization<br/>..Prerequisites and Customizing Settings for the Reorganization<br/>....Derivation Hierarchy<strong><br/><br/></strong>..Specific Functions for Objects that Can Be Reorganized<br/>....Reorganization of WIP Objects and SD Objects</p>\n<p><strong>4. When can a plan be closed?</strong></p>\n<p>The reorganization plan can be closed once all of the process steps for each object type in your hierarchy have been processed.<br/>In test systems, you can, in principle, also close incomplete plans (see SAP Note 1765330). However, this procedure causes inconsistencies in your system since, afterwards, it is impossible to determine in which status the plan was closed. For this reason, <strong>SAP strongly advises you to close incomplete reorganization plans in the production system</strong>. SAP will not provide any support for inconsistencies that arise as a result of closing incomplete reorganization plans.</p>\n<p><strong>5. If the source PRCTR is empty?</strong></p>\n<p><strong>This is not a procedure supported by SAP and must be <strong>carried out by your SAP consultant.</strong></strong></p>\n<p>You want to perform a reorganization and must also allow the value blank as the source account assignment.</p>\n<p>A modification allows you to enter the value blank as a valid value for the source account assignment, but the target account assignment must still be filled with a valid value other than blank.</p>\n<p><strong>Refer to the following SAP Note:</strong></p>\n<p><strong>1702540</strong> - Modification: Allow account assignment value blank as old value</p>\n<p>If you want to convert AP/AR/GLOI, the objects are selected, but cannot be assigned to an object type (FAGL_REORGANISATION 505) and are therefore <strong>NOT</strong> included in the reorganization tables.<br/>In addition, the error message 505 must also be set as a warning in the productive system, but only for this project:</p>\n<p><strong>2514254</strong> - PRCTR/SEG: Warning FAGL_REORGANIZATION 505 for inconsistent account assignment in documents/recursive start of generation during account assignment change</p>\n<p>Afterwards, the generation must be started again.</p>", "noteVersion": 7}, {"note": "2070401", "noteTitle": "2070401 - PRCTR/SEG: FAGL_REORGANIZATION 566 (XXII)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you generate receivables and payables, the system does not take technical fields into account. This results in error 566 during reassign.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segment reorganization<br/>Reorganization<br/>Generate<br/>FAGL_REORGANIZATION 566<br/>FAGL_R_SPL<br/>Technical fields</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The system ignores the \"Indicator: Line Item Not Liable to Cash Discount?\" indicator (XSKRL). Supplement to SAP Note 2055537.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections. You have to implement this SAP Note before generating receivables and payables.</p>\n<p>Afterwards, the system treats the receivable or payable as if the profit center account were assigned directly (artificial level 1 object).</p>", "noteVersion": 6}, {"note": "2010551", "noteTitle": "2010551 - PRCTR: Payables for purchase orders", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>For the profit center reorganization, the system generates payables for purchase orders even though purchase orders are not involved with the reorganization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Accounts Payable/Accounts Receivable, AP/AR, generating an object list, purchase order, PO, payment on account, POA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>If you exclude purchase orders using the specific restrictions or they are not involved in the reorganization for other reasons, the dependent payables should also not be reorganized.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections. </p>", "noteVersion": 3}, {"note": "1746704", "noteTitle": "1746704 - Unjustified issue of AIST009 for reorganization transfer posting", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the reorganization transfer posting, the system may issue error AIST 009 (\"Profit center is not unique (&amp;1/&amp;2)\"), which is unjustified.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>AIST_DERIVE_PRCTR_SEGMENT, AIST009<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error; time intervals are checked for consistency before the reorganization date.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the corrections. After you implement the corrections, the system checks only time intervals as of the reorganization date for consistency in the reorganization transfer posting.</p></div>", "noteVersion": 3}, {"note": "1878955", "noteTitle": "1878955 - Changing a purchase order after profit center reorganization", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Due to a reorganization, the Profit Center of an assigned purchase order item is reset. If the purchase order item is now changed again (for example the quantity is changed), the system performs a check of the account assignment objects of the purchase order item (with the creation date of the purchase order). This may lead to the following problems:</p> <ul><ul><li>The new Profit Center is not yet valid on the document date (creation date) of the purchase order. The system issues error message FAGL_ORG_UNITS001 \"Profit center &amp;1 not found in controlling area &amp;2\".</li></ul></ul> <ul><ul><li>If the account assignment object of the purchase order item is a cost center, on the document date (creation date) of the purchase order the Profit Center is derived again from the cost center master data and overwritten in the purchase order item.</li></ul></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>ME22N K_COBL_CHECK</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the program corrections.<br/>*******************************************<br/>You can take the following organizational measures to work around the first problem (error message FAGL_ORG_UNITS001):<br/>Extend the validity period of the new Profit Center so that<br/>it is valid on the document date of the purchase orders that are still open.<br/>As far as possible, replace the old purchase orders with new purchase orders.<br/> <br/></p></div>", "noteVersion": 6}, {"note": "1975633", "noteTitle": "1975633 - PRCTR: AR simulation of ext. document splitting characteristics", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Not all SD conditions are taken into account when receivables are generated.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorganization<br/>CL_FAGL_R_SPLIT_REORG: GET_ACCIT_FROM_SD_P<br/>SAPFACCO0 / SAPFACCO1 / RV_ACCOUNTING_DOCUMENT_CREATE / LV60BU01</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Every FI document is enriched with reorganization characteristics during GENERATION in the simulation of the document split (CL_FAGL_R_SPLIT_REORG: SIMADD_GET_SPLITINFO_FOR_DOC_P). These characteristics serve to determine the higher-level object in the hierarchy.</p>\n<p>For FI documents with reference transaction = 'VBRK' the report SAPFACCO0, or since SAP Note 1971695 a direct call of the function module 'RV_ACCOUNTING_DOCUMENT_CREATE', is used to structure the internal SAP interface (ACCHD/ACCIT/ACCCR). This has the restrictions named in SAP Note 1487345 compared to SAPFACC1.</p>\n<p>SAP Note <strong>1801767</strong> describes restrictions of the functional scope, including the following:</p>\n<ul>\n<li>Sales and distribution documents with active revenue recognition</li>\n<li>Business processes</li>\n<li>Invoice lists</li>\n</ul>\n<p>For detailed information, see the application documentation for the profit center reorganization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached corrections.</p>\n<p>In future the complete document (an invoice) will be imported (according to report SAPFACCO1) including all conditions and not only \"selected\" conditions. <br/>The following can now also be simulated:</p>\n<p>- Statistical conditions</p>\n<p>- Conditions for transfer prices</p>\n<p>- Conditions for revenue recognition processes</p>\n<p>- Conditions for down payment clearing (partial)</p>\n<p> </p>\n<p>This note provides the necessary preparation for the switchover. Open a customer message under component FI-GL-REO-GL if you want to use SAPFACC1.</p>\n<p>Implement the relevant Support Package or implement the source code attached to this note.</p>", "noteVersion": 6}, {"note": "1964587", "noteTitle": "1964587 - PRCTR/SEG: Dump when generating AP/AR 566 (XVIII)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the generation of receivables and payables, the system issues a<br/>dump in method CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 566<br/>CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 1911623.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 4}, {"note": "1994499", "noteTitle": "1994499 - <PERSON><PERSON>r FAGL_REORGANIZATION 566 during reassignment", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you reassign a profit center to receivables/payables, the system issues error message FAGL_REORGANIZATION 566 (\"Reassignment: PRCTR/SEGMENT deviation in FAGL_SPLINFO and FAGL_R_SPL\").</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization, constant, tax, segment</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In the rule for document splitting, a fixed value (constant) is defined for certain lines. During the reassignment by the reorganization, a new derivation of the segment is forced. As a result, the system no longer executes the rule with the fixed value, which results in a deviation from the existing split. The system then no longer executes the reassignment.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the following advance corrections.</p>", "noteVersion": 1}, {"note": "2094622", "noteTitle": "2094622 - PRCTR: Analysis report for stock determination for SD documents and WBS elements", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This report displays the balances for Sales and Distribution (SD) documents and work breakdown structure (WBS) elements in a list for a reorganization run for profit centers. This can be helpful when you want to analyze problematic cases. This is only a display report. It does not change any data.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization, WIP, material stock</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note provides support during troubleshooting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program.</p>", "noteVersion": 1}, {"note": "1973715", "noteTitle": "1973715 - AA347 with account assignment change for assets", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you make an account assignment change for assets, the system incorrectly issues error message AA347 even though the fiscal year change was already carried out.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>REORG</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is a reorganization at the fiscal year change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instructions or import the specified Support Package.</p>\n<p>After you implement these corrections, the system will check again the correct fiscal year. The system will also check with the actual posting date of the transfer posting in Asset Accounting and no longer with the reorganization date. The posting date is either the reorganization date or if this is older the current system date.</p>", "noteVersion": 2}, {"note": "1881515", "noteTitle": "1881515 - Reorganization: Profit center in cost center cannot be changed (II)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the profit center reorganization solution.<br/>You want to change the assignment to the profit center in a cost center.<br/>The system issues error message FAGL_REORGANIZATION 601<br/>(Profit center assignment in cost center &amp;1 cannot be changed)<br/>(also see SAP Note 1684679).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FAGL_REORGANIZATION 601,603,605<br/>transaction KS02<br/><br/>transaction OBA5</p>\n<ul>\n<ul>\n<li>Change the message control.<br/>Enter the application area 'FAGL_REORGANIZATION'</li>\n</ul>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>Warning:</strong><br/>Note that converting the messages 601, 603, and 605 into warning messages may cause inconsistencies in the system. SAP <strong>does not accept any liability</strong> for these inconsistencies.<br/><br/>Only in special cases should you convert the message from an error message to a warning message. If you plan to do this, contact SAP Development Support in advance.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Changing messages 601, 603, and 605 to warning messages is a workaround. SAP recommends the following:</p>\n<p>When the business function FIN_GL_REORG_1 is activated in the Switch Framework (transaction SFW5), the Profit Center of a cost center should only be changed with the support of the reorganization tool. The message FAGL_REORGANIZATION 601, 603 or 605 is therefore issued if a new Profit Center is assigned to a cost center and the Profit Centers involved are not maintained accordingly in a reorganization plan. <br/>If you only want to reorganize cost centers, create a reorganization plan with a hierarchy that contains only cost centers. Pay attention to the reorganization date and the combination of old/new PRCTR (use the Save/Load function). After you change the cost centers, the plan can be closed without further activities.</p>\n<p>For information on how to include the cost centers in the reorganization plan, see the chapter in the business function documentation that describes the reorganization of cost centers.</p>\n<p>********************************************************************************************************************</p>\n<p>Implement the attached source code corrections by importing the Support Package or implementing the correction instructions. See also follow-on Note <a class=\"th-lk\" href=\"/notes/0001900735\" id=\"C39_W128_V129_solutions_table[3].numm\" target=\"_blank\" title=\"1900735\">1900735</a>.</p></div>", "noteVersion": 7}, {"note": "2146307", "noteTitle": "2146307 - PRCTR: Postprocessing orders without material assignment", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You execute a profit center reorganization. Only production orders that have an assignment to a material are selected. For postprocessing orders, this assignment is often missing. These are not selected for the reorganization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Postprocessing order</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program error. SAP Note 1560518 does not take orders without a material assignment into account.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program correction. The system then selects orders without a material assignment as first-level objects, too.</p>", "noteVersion": 1}, {"note": "2028638", "noteTitle": "2028638 - Derivation of the profit center from the cost center after reorganization", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>After you implement SAP Note 1878955, there is an incorrect derivation of the profit center from the cost center such as when posting an FI document to the old period. The system incorrectly uses the profit center of the new period for a posting to the period before the reorganization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>K_COBL_CHECK, COSTCENTER_SPEC_CHECK, KM026</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program error</p>\n<p>The error may arise if the cost center was included in a reorganization plan. There is a posting to the period before the reorganization. In this case, the system incorrectly posts to the old period with the profit center for the new period. The system should post to the old period with the profit center of the old period.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>\n<p> </p>", "noteVersion": 2}, {"note": "1965884", "noteTitle": "1965884 - PRCTR/SEG: Dump when generating AP/AR 566 (XIX)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the generation of receivables and payables, the system issues a<br/>dump in method CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 503<br/>FAGL_REORGANIZATION 518<br/>CL_FAGL_R_SPLIT_REORG: CHECK_AND_ELIM_ROUND_REORG_SPL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 1911623.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>\n<p>When you implement this SAP Note, the dump is prevented and instead, the system writes the warning FAGL_REORGANIZATION 518 - \"Simulation: Rounding diff. after simulated doc. splitting &amp;1 not expanded\" to the log, because the rounding difference cannot be expanded.</p>\n<p>When the result of splitting the rounding differences fails, the system can no longer determine the reference to the object that provides the account assignment. As a result, it has to handle the receivable or payable as an object of the first level of the derivation hierarchy. Consequently, the system treats it as if the profit center account were assigned directly (artificial level 1 object).</p>\n<p> </p>\n<p>For more information, see the following SAP Notes:</p>\n<p>1739651 - PRCTR/SEG: Error message when generating AP/AR</p>\n<p>1734472 - PRCTR/SEG: Meaningful message text for message 503</p>", "noteVersion": 7}, {"note": "2127049", "noteTitle": "2127049 - PRCTR/SEG: Error message FAGL_REORGANIZATION 534 (II)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With the implementation of SAP Note 1663555, cash discount clearing lines are not taken into account during an account assignment change. This can result in the error FAGL_REORGANIZATION 534 for a repeated segment or PRCTR reorganization.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Reorg<br/>Profit center reorganization<br/>Segment reorganization<br/>Reorganisation<br/>Reorganization</p>\n<p>BSIK-XNETB / FAGL_R_SPL-XSKV cash discount clearing line<br/>CL_FAGL_R_OBJ_TYPE_APAR: R_UPDATE_REORG_SPLINFO_P / R_CHECK_SPLIT_RESULT_P</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program error: With the implementation of SAP Note 1663555, cash discount clearing lines were not taken into account during an account assignment change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the correction instructions.</p>", "noteVersion": 3}, {"note": "2120604", "noteTitle": "2120604 - PRCTR/SEG: Dump when generating AR (CX_SY_DYNAMIC_OSQL_SEMANTICS)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you generate accounts receivable (AR), the following dump occurs if you use specific restrictions: CX_SY_DYNAMIC_OSQL_SEMANTICS.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Profit center reorganization<br/>Segment reorganization<br/>Reorg<br/>CL_FAGL_R_OBJ_TYPE_APAR: G_SELECT_ITEMS_P</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is a program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the following corrections and then regenerate accounts receivable.</p>", "noteVersion": 2}, {"note": "2105830", "noteTitle": "2105830 - SEG: Assert on Profit Center in Segment Reorganization of sales orders", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the Segment Reorganization functionality. You have generated the object list for Sales orders. You received an assertion error after calling function module FAGL_GET_SEGMENT_FROM_PRCTR.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segment reorganization, SO, Sales order</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the correction instruction associated with this note.</p>", "noteVersion": 1}, {"note": "2152390", "noteTitle": "2152390 - Check Manager: ATC - missing interface methods II", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note refers to SAP Note 1852318.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ATC, potential SQL injection,<br/>CL_FAGL_R_OBJ_TYPE_APAR:G_SELECT_ITEMS_P, CL_FAGL_R_OBJ_TYPE_APAR:G_BUILD_QUERY_P<br/>Parameters: IV_WHERE_DOWN_D, IV_WHERE_DOWN_K, IT_WHERETAB_DOWN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Potential SQL injection</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please implement SAP Note 2014156 first. Implement this SAP Note or import the relevant Support Package.</p>\n<p>For Version SAP_APPL 605, please implement SAP Note 2190346 first.</p>", "noteVersion": 3}, {"note": "2150945", "noteTitle": "2150945 - PRCTR/SEG: FAGL_REORGANIZATION 533 during reassignment of AP/AR objects II", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the reassignment of accounts payable/receivable (AP/AR) objects, the system issues error message FAGL_REORGANIZATION 533.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segment reorganization<br/>Reorg<br/>FAGL_REORGANIZATION 533</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In the case of process chains within FI with partial payments, the affected documents (residual items) are already partially reassigned. However, the associated original document is not reassigned because it was fully cleared during the first generation process.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the program corrections.</p>", "noteVersion": 2}]}], "activities": [{"Activity": "Data migration", "Phase": "Before or after conversion project", "Condition": "Conditional", "Additional_Information": "In case you need to do a profit center or segment reoganization, before the planned \"Organizational flexibility in financial accounting\" solution is available, please contact FI-GL-REO."}]}