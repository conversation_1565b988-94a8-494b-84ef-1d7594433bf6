{"Request": {"Number": "1632754", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 678, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017311692017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001632754?language=E&token=5132846ACCD7E19526E97C6A5BB592AD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001632754", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001632754/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1632754"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.02.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "IBM AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IBM AS/400", "value": "BC-OP-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1632754 - IBM i: Changeover to instance-specific directory"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>With SAP NetWeaver 2004s and associated products on the application basis 7.0x together with the 7.2X kernel, SAP enables a new directory structure with IBM i; you can use this to maintain a kernel while the instances of the system are active. This minimizes the amount of time for which the system is unavailable.<br /><br />With the new directory structure, the SAP kernel is installed in a central directory (indicated by the profile parameter DIR_CT_RUN). From there, the kernel is copied during the system start to the instance-specific kernel directories (indicated by the profile parameter DIR_EXECUTABLE). The kernel is replicated using the program sapcpe. Each SAP instance then uses the kernel from its own instance-specific directory.<br /><br />This SAP Note describes how you can configure SAP systems that have the application release 7.0x for the new directory structure if a 7.2X kernel is active in the system (see SAP Note 1636252).<br /><br />For releases based on SAP NetWeaver PI (kernel 7.1x), the directory structure was changed again. The changes now support the heterogeneous system installations more efficiently. In SAP systems that are based on SAP NetWeaver 2004s (application releases 7.0x), the SAP NetWeaver 7.1x directory structure is not supported.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>AS/400, OS400, system i, i5OS, iSeries</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The following procedure requires a standard SAP NetWeaver 2004s directory structure: The programs are in the directory /sapmnt/&lt;SID&gt;/exe or /sapmnt/&lt;SID&gt;/exe/uc or /sapmnt/&lt;SID&gt;/exe/{uc,nuc,jvm}/as400_pase_64 and are used from there when the SAP system starts up. In addition, a 7.2X kernel is active in this SAP system (see SAP Note 1636252).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>To clarify: (&lt;SID&gt; = system name - &lt;INSTNO&gt; = instance number)<br />Up to now, the ILE objects were stored in a kernel library with a user-defined name (in kernel 7.0x); from this library, the PASE programs were copied to the directory /sapmnt/&lt;SID&gt;/exe and they were loaded and used from here when the SAP system started. However, in this situation, you cannot perform maintenance tasks for a kernel (such as applying a patch) while the system is running.<br />The change to the system configuration described in this note results in the system creating the new subdirectory called /exe in each instant-specific directory (for example, /usr/sap/&lt;SID&gt;/DVEBMGS&lt;INSTNO&gt;/...). When you call the program SAPCPE in the start directory to start the instance before the SAP system actually starts, the instance ensures that all programs that were changed are copied from the global directory /sapmnt/&lt;SID&gt;/exe into the instance-specific directory. The instance then loads the programs that it requires from this instance-specific program directory; at the same time, an instance-specific library (*LIB) also exists for ILE objects. The global directory /sapmnt/&lt;SID&gt;/exe and the library of ILE objects that is associated with it are therefore no longer used by the SAP instances and are available for maintenance operations; only the user that is logged on (&lt;SID&gt;ADM or &lt;SID&gt;OFR) works with these cross-instance system objects. Since an ILE library is required for each instance, the system now generates the names of these libraries. The global ILE library is called SAP&lt;SID&gt;IND, in addition, the menus are stored in the libraries SAP&lt;SID&gt;IND0 - SAP&lt;SID&gt;IND9; the instance-specific ILE libraries are created by SAPCPE as SAP&lt;SID&gt;I&lt;INSTNO&gt; (no libraries for menus exist here).<br />If your SAP system is split over several LPARs, you must install SAPHOSTAGENT on each LPAR to ensure that the system remains consistent (see Note 1031096); in addition, the global directory /sapmnt/&lt;SID&gt;/exe must be visible on each LPAR.<br /><br />To change the instance-specific program directories, proceed as follows:</p>\r\n<ol>1. Use the system user &lt;SID&gt;ADM to log on to each LPAR on which an instance of the SAP system runs and stop this.</ol><ol><ol>2. As the system user &lt;SID&gt;ADM, you must now create the directory /usr/sap/&lt;SID&gt;/&lt;APPL&gt;&lt;INSTNO&gt;/exe (&lt;APPL&gt; indicates the role of the instance; for example, DVEBMGS or D or J or SCS) on each LPAR on which an instance of the SAP system is to run:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>MKDIR DIR('/usr/sap/&lt;SID&gt;/&lt;APPL&gt;&lt;INSTNO&gt;/exe')</ol></ol><ol><ol>3. Log on to your central LPAR as the system user &lt;SID&gt;ADM (here, the directory /sapmnt/&lt;SID&gt;/profile is created locally) and insert the following entries into the start profile for each instance.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Search for the line that contains the definition of the variable DIR_EXECUTABLE. It might look something like:</ol></ol><ol><ol>DIR_EXECUTABLE = $(DIR_EXE_ROOT)/run&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;or</ol></ol><ol><ol>DIR_EXECUTABLE = /usr/sap/&lt;SID&gt;/SYS/exe/run</ol></ol><ol><ol>In this line, change DIR_EXECUTABLE to DIR_CT_RUN:</ol></ol><ol><ol>DIR_CT_RUN = $(DIR_EXE_ROOT)/run&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;or</ol></ol><ol><ol>DIR_CT_RUN = /usr/sap/&lt;SID&gt;/SYS/exe/run</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Insert the following line:</ol></ol><ol><ol>DIR_EXECUTABLE = $(DIR_INSTANCE)/exe</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Insert the following line:</ol></ol><ol><ol>Execute_00 = immediate $(DIR_CT_RUN)/sapcpe pf=$(_PF)</ol></ol><ol>If your start profile already contains a line with Execute_00, search for all following Execute_XX parameters and increase their number by one (for example,  Execute_01 changes to Execute_02). Ensure that all Execute_XX have an ascending number in the current order, beginning with 00. </ol><ol>4. Copy the profile parameters DIR_CT_RUN and DIR_EXECUTABLE from the start profile to the instance profile. It is important that these are the same in both profiles.</ol><ol>5. For dual-stack systems, check the values for javaVMLibPath in the file .vmprop for each Java instance (for example, usr/sap/&lt;SID&gt;/&lt;APPL&gt;&lt;INSTNO&gt;/j2ee/cluster/instance.properties.vmprop, /usr/sap/&lt;SID&gt;/APPL&gt;&lt;INSTNO&gt;/SDM/program/config/sdm_jstartup.properties.vmprop, and so on) and replace /usr/sap/&lt;SID&gt;/SYS/exe/run with an instance-related value (for example, /usr/sap/&lt;SID&gt;/&lt;APPL&gt;&lt;INSTNO&gt;/exe).</ol><ol>6. Check the SAP CCMS agents and reconfigure them (if necessary) as described in Note 1547201.</ol><ol><ol>7. As the user QSECOFR, reset all authorizations; to do this, use the following two commands:</ol></ol><ol><ol>ADDLIBLE SAP&lt;SID&gt;IND</ol></ol><ol>FIXSAPOWN SID(&lt;SID&gt;)</ol><ol>8. Start all instances as &lt;SID&gt;ADM.</ol></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D042520)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001632754/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632754/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632754/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632754/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632754/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632754/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632754/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632754/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001632754/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "919046", "RefComponent": "BC-OP-NT", "RefTitle": "Upgrade to the New Instance-Specific Directory Structure", "RefUrl": "/notes/919046"}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173"}, {"RefNumber": "1683418", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: System maint. diffs between 7.20 and 4.6D to 7.11", "RefUrl": "/notes/1683418"}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755"}, {"RefNumber": "1547201", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Start and stop standalone agents", "RefUrl": "/notes/1547201"}, {"RefNumber": "1299618", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Installing SAPHOSTAGENT on a blank computer", "RefUrl": "/notes/1299618"}, {"RefNumber": "1149318", "RefComponent": "BC-OP-AS4", "RefTitle": "User concept conversion using the tool CONVUSRCPT", "RefUrl": "/notes/1149318"}, {"RefNumber": "1104735", "RefComponent": "BC-INS", "RefTitle": "Upgrade to the new instance-specific directory on UNIX", "RefUrl": "/notes/1104735"}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751"}, {"RefNumber": "1078134", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Distribution of ILE and PASE system components", "RefUrl": "/notes/1078134"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2375098", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Kernel requirements for Software Update Manager", "RefUrl": "/notes/2375098 "}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755 "}, {"RefNumber": "1078134", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Distribution of ILE and PASE system components", "RefUrl": "/notes/1078134 "}, {"RefNumber": "1683418", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: System maint. diffs between 7.20 and 4.6D to 7.11", "RefUrl": "/notes/1683418 "}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173 "}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751 "}, {"RefNumber": "1547201", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Start and stop standalone agents", "RefUrl": "/notes/1547201 "}, {"RefNumber": "1149318", "RefComponent": "BC-OP-AS4", "RefTitle": "User concept conversion using the tool CONVUSRCPT", "RefUrl": "/notes/1149318 "}, {"RefNumber": "1104735", "RefComponent": "BC-INS", "RefTitle": "Upgrade to the new instance-specific directory on UNIX", "RefUrl": "/notes/1104735 "}, {"RefNumber": "1299618", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Installing SAPHOSTAGENT on a blank computer", "RefUrl": "/notes/1299618 "}, {"RefNumber": "919046", "RefComponent": "BC-OP-NT", "RefTitle": "Upgrade to the New Instance-Specific Directory Structure", "RefUrl": "/notes/919046 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.20", "To": "7.22", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}