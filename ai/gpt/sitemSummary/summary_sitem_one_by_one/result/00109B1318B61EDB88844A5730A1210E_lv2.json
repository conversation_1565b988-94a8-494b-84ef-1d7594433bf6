{"guid": "00109B1318B61EDB88844A5730A1210E", "sitemId": "SI02: EC_CS", "sitemTitle": "S4TWL - Consolidation (EC-CS) and preparations for consolidation", "note": 2999249, "noteTitle": "2999249 - S4TWL - Consolidation and Preparation for Consolidation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">You are doing a system conversion from SAP ERP to SAP S/4HANA or an upgrade from a lower to a higher SAP S/4HANA release and are using the functionality described in this note. The following SAP S/4HANA Simplification Item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Compatibility Scope, Compatibility Package, System Conversion, Upgrade, Consolidation, ID 434</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p id=\"\"><strong>Description<br/></strong></p>\n<p>Consolidation (EC-CS) and preparations for consolidation are part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it’s expiry date and links to further information please refer to SAP note <strong>2269324</strong>. In the compatibility matrix attached to SAP note <a href=\"/notes/2269324\" target=\"_blank\">2269324</a>, Consolidation (EC-CS) and preparations for consolidation can be found under the ID 434.</p>\n<p>This means that you need to migrate from Consolidation (EC-CS) and preparations for consolidation to the designated alternative functionality <em>SAP S/4HANA for Group Reporting</em>. The license for <em>SAP S/4HANA for Group Reporting</em> includes usage right for EC-CS until December 31st, 2027. This note shall provide further information on what to do to move to the alternative functionality.</p>\n<p><strong>Business Process related information</strong></p>\n<p>For a description of the functional scope of EC-CS in the context of SAP S/4HANA refer to SAP note <a href=\"/notes/2371973\" target=\"_blank\">2371973</a> and to the SAP S/4HANA Feature Scope Description in sections SAP S/4HANA Compatibility Packs &gt; <a href=\"https://help.sap.com/viewer/029944e4a3b74446a9099e8971c752b9/2020/en-US/2f2f7156b419c259e10000000a441470.html\" target=\"_blank\">Consolidation (EC-CS)</a> and <a href=\"https://help.sap.com/viewer/029944e4a3b74446a9099e8971c752b9/2020/en-US/b5aea4563f45cf4fe10000000a44147b.html\" target=\"_blank\">Preparations for Consolidation</a>.<br/><br/>For information about the alternative solution <em>SAP S/4HANA for Group Reporting</em> refer to the <a href=\"https://help.sap.com/viewer/4ebf1502064b406c964b0911adfb3f01/2020.000/en-US/c27c73226dac4e07ac8aa59a45f92fd8.html\" target=\"_blank\">SAP Help Portal</a> and SAP note <a href=\"/notes/2659672\" target=\"_blank\">2659672 - FAQ About SAP S/4HANA Finance for Group Reporting (On Premise)</a>.</p>\n<p><strong><strong>Required and Recommended Action(s)</strong></strong></p>\n<p>Make yourself familiar with <em>SAP S/4HANA for Group Reporting</em> and define your strategy when to switch to the new solution.<br/>For information how to transition from EC-CS to <em>SAP S/4HANA for Group Reporting</em> refer to SAP note <a href=\"/notes/2833748\" target=\"_blank\">2833748</a>.<br/>Most important aspect to consider are as follows:</p>\n<ul>\n<li>In SAP S/4HANA 1809, coexistence of EC-CS and <em>SAP S/4HANA for Group Reporting</em> in the same SAP S/4HANA instance is not supported. You must set up a separate (new) SAP S/4HANA instance for usage of Group Reporting.</li>\n<li>As of SAP S/4HANA 1909, parallel usage of EC-CS and <em>SAP S/4HANA for Group Reporting</em> is possible (\"hybrid system state\"). This allows you to test usage of Group Reporting in parallel to the productive use of EC-CS.</li>\n<li>If you are on SAP S/4HANA 1909 or later and wish to run your system in the hybrid state to prepare the transition to Group Reporting, you need to contact SAP Support because the transition from EC-CS into hybrid system state is executed in a multiple step procedure of which some steps are executed by SAP Support. </li>\n</ul>", "noteVersion": 2, "refer_note": [{"note": "2371973", "noteTitle": "2371973 - EC-CS in the context of SAP S/4HANA OP ( \"on premise\" )", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Clarification of status of EC-CS in the context of SAP S/4HANA OP ( \"on premise\" )</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Consolidation, S/4HANA OP, EC-CS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have installed SAP S/4HANA OP.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The SAP consolidation solution EC-CS is carried forward as part of SAP S/4HANA OP ( \"on premise\" ) compatibility scope.</p>\n<p>The functionality of EC-CS in SAP S/4HANA OP is identical to the one of EC-CS in SAP ERP 6.0, including all Enhancement Packages up to SAP FIN 618 (EHP8). For further details on this functional scope, see SAP Note <a href=\"/notes/1223308\" target=\"_blank\"><strong>1223308</strong></a>.</p>\n<p><strong>Limitations for EC-CS in SAP S/4HANA OP ( \"on premise\" )</strong></p>\n<ul>\n<li>EC-CS is part of the SAP S/4HANA compatibility scope. Therefore, the license for usage of EC-CS in SAP S/4HANA OP will expire at end of year 2025. See SAP Notes <a href=\"/notes/2999249\" target=\"_blank\"><strong>2999249</strong></a> and <a href=\"/notes/2269324\" target=\"_blank\"><strong>2269324</strong></a> for details.</li>\n<li>If you are using EC-CS in SAP S/4HANA, you need to migrate to the alternative functionality <em>SAP S/4HANA for Group Reporting</em>. See SAP Note <a href=\"/notes/2833748\" target=\"_blank\"><strong>2833748</strong></a> for details. The license for <em>SAP S/4HANA for Group Reporting</em> includes usage right for EC-CS until December 31st, 2027.</li>\n<li>If you have been using the field for material number (technical field MATNR) as customer-defined subassignment in EC-CS on the context of SAP ERP, you must adjust your EC-CS data model before being able to upgrade from SAP ERP to SAP S/4HANA OP. See SAP Note <a href=\"/notes/2209784\" target=\"_blank\"><strong>2209784</strong></a> for details. </li>\n</ul>\n<p><strong>EC-CS in SAP S/4HANA OP 1709</strong></p>\n<ul>\n<li>For usage of EC-CS in S/4HANA OP 1709, software component S4CORE, release level 102 is required.</li>\n<li>The current time horizon for mainstream maintenance of EC-CS in SAP S/4HANA OP 1709 is end of year 2022.</li>\n</ul>\n<p><strong>EC-CS in SAP S/4HANA OP 1809</strong></p>\n<ul>\n<li>For usage of EC-CS in S/4HANA OP 1809, software component S4CORE, release level 103 is required.</li>\n<li>The current time horizon for mainstream maintenance of EC-CS in SAP S/4HANA OP 1809 is end of year 2023.</li>\n</ul>\n<p> <strong>EC-CS in SAP S/4HANA OP 1909</strong></p>\n<ul>\n<li>For usage of EC-CS in S/4HANA OP 1909, software component S4CORE, release level 104 is required.</li>\n<li>The current time horizon for mainstream maintenance of EC-CS in SAP S/4HANA OP 1909 is end of year 2024.</li>\n<li>Starting with SAP S/4HANA OP 1909, transition from EC-CS to <em>SAP S/4HANA for Group Reporting</em> within the same SAP S/4HANA instance is supported. See SAP Note <a href=\"/notes/2833748\" target=\"_blank\"><strong>2833748</strong></a> for details.</li>\n</ul>\n<p><strong>EC-CS in SAP S/4HANA OP 2020</strong></p>\n<ul>\n<li>For usage of EC-CS in S/4HANA OP 2020, software component S4CORE, release level 105 is required.</li>\n<li>The current time horizon for mainstream maintenance of EC-CS in SAP S/4HANA OP 2020 is end of year 2025.</li>\n</ul>\n<p><strong>EC-CS in SAP S/4HANA OP 2021</strong></p>\n<ul>\n<li>For usage of EC-CS in SAP S/4HANA OP 2021, software component S4CORE, release level 106 is required.</li>\n<li>Mainstream maintenance of EC-CS in SAP S/4HANA OP 2021 will be terminated with the compatibility scope, at end of year 2025.</li>\n<li>If you are using EC-CS in SAP S/4HANA, you need to migrate to the alternative functionality <em>SAP S/4HANA for Group Reporting</em>. See SAP Note <a href=\"/notes/2833748\" target=\"_blank\"><strong>2833748</strong></a> for details. The license for <em>SAP S/4HANA for Group Reporting</em> includes usage right for EC-CS until December 31st, 2027.</li>\n</ul>\n<p><strong><strong>EC-CS in SAP S/4HANA OP 2022</strong></strong></p>\n<ul>\n<li>For usage of EC-CS in S/4HANA OP 2022, software component S4CORE, release level 107 is required.</li>\n<li>Mainstream maintenance of EC-CS in SAP S/4HANA OP 2022 will be terminated with the compatibility scope, at end of year 2025.</li>\n<li>If you are using EC-CS in SAP S/4HANA, you need to migrate to the alternative functionality <em>SAP S/4HANA for Group Reporting</em>. See SAP Note <a href=\"/notes/2833748\" target=\"_blank\"><strong>2833748</strong></a> for details. The license for <em>SAP S/4HANA for Group Reporting</em> includes usage right for EC-CS until December 31st, 2027.</li>\n</ul>\n<p><strong>EC-CS in SAP S/4HANA OP 2023</strong></p>\n<ul>\n<li>For usage of EC-CS in S/4HANA OP 2023, software component S4CORE, release level 108 is required.</li>\n<li>Mainstream maintenance of EC-CS in SAP S/4HANA OP 2023 will be terminated with the compatibility scope, at end of year 2025.</li>\n<li>If you are using EC-CS in SAP S/4HANA, you need to migrate to the alternative functionality <em>SAP S/4HANA for Group Reporting</em>. See SAP Note <a href=\"/notes/2833748\" target=\"_blank\"><strong>2833748</strong></a> for details. The license for <em>SAP S/4HANA for Group Reporting</em> includes usage right for EC-CS until December 31st, 2027.</li>\n</ul>", "noteVersion": 11, "refer_note": [{"note": "2209784", "noteTitle": "2209784 - S4TC SAP_APPL - checks for EC-CS", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span>Pre-transition checks for the application EC-CS SAP Consolidation must be carried out before an upgrade to SAP S/4HANA.</span></p>\n<p><span>If you have included a field of the type MATNR (material number) as a customer-defined subassignment in the EC-CS data model, the EC-CS data model must be adjusted before the upgrade.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, the material number was extended to a maximum length of 40 characters. As a result, the material number is longer than the maximum length defined for characteristics in the EC-CS data model.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The correction instructions in this SAP Note implement a consistency check that checks whether a field of the type MATNR (material number) is used in the EC-CS data model.</p>\n<p>If the consistency check determines that a field of this type is used as a <span>customer-defined subassignment</span> in the EC-CS data model, contact SAP Support by creating a customer incident on SAP Service Marketplace in the application component EC-CS.</p>\n<p>Background: In this case, you must adjust the EC-CS data model in your system. Adjusting the data model means that the existing EC-CS transaction data must be converted. This may lead to loss of information because fields of the type MATNR (material number) need to be removed from the data model.</p>\n<p> </p>", "noteVersion": 9}, {"note": "1223308", "noteTitle": "1223308 - EC-CS in the context of SAP ERP 6.0", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Clarification of maintenance of EC-CS in the context of SAP ERP 6.0 (formerly mySAP ERP 2005)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Consolidation, SAP ERP 6.0, EC-CS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have installed EC-CS on release SAP ERP 6.0.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The SAP consolidation solution EC-CS is an integral part of SAP ERP (Enterprise Ressource Planning). It is continued to be maintained on release SAP ERP 6.0 in the frame of the general SAP ERP maintenance strategy.<br/><br/>Recent extensions in EC-CS for improved system support of legal requirements cover the following topics:</p>\n<ul>\n<li>Business Combinations, basic scope (note 1249778)</li>\n<li>Separation of goodwill from investment (note 1290470)</li>\n<li>Merger activities (note 1368421)</li>\n<li>Validation of cumulative investment share percentages (note 1392076)</li>\n<li>Assets held for sale and discontinued operations (note 1470938)</li>\n<li>Business Combinations, extended scope (note 1479191)</li>\n<li>Organizational changes with acquisitions using either direct shares or group shares (note 1543705)</li>\n<li>Adjustment of other comprehensive income in step acquisitions (note 1626022)</li>\n<li>Method change as change of control (note 1691379)</li>\n<li>Method change with proportionate consolidation (note 1833554)</li>\n<li>Increase in capitalization at decrease of investment share percentage (note 1902594)</li>\n</ul>\n<p>Maintenance horizon for SAP ERP 6.0 EHP (Enhancement Packages) is outlined in SAP Note <strong>2881788</strong>.</p>\n<ul>\n<li>Mainstream maintenance of EHP0 - EHP5: until end of year 2025 (no extended maintenance will be provided)</li>\n<li>Mainstream maintenance of EHP6 - EHP8: until end of year 2027</li>\n<li>Extended maintenance of EHP6 - EHP8: until end of year 2030 (optional, additional fee)</li>\n</ul>\n<p>Additional information about maintenance of SAP ERP is available on Service Marketplace at: support.sap.com/maintenance. For more detailed information contact your Account Team.</p></div>", "noteVersion": 15}, {"note": "2269324", "noteTitle": "2269324 - Compatibility Scope Matrix for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Through the Compatibility Packages (CP) listed in the attachment \"Compatibility Scope Matrix\", SAP provides a limited use right to SAP S/4HANA on-premise customers to run certain classic SAP ERP solutions on their SAP S/4HANA installation. Condition is that these customers have licensed the applicable solutions as set forth in their License Agreements. Compatibility Pack use rights may apply to selected private cloud deployments as well, without the prerequisite of an on-premise classic license. Please refer to the respective Service Description Guide for details.</p>\n<p>This use right expires on Dec 31, 2025, and is available to installed-base as well as net-new customers.</p>\n<p>The <a href=\"https://news.sap.com/2020/02/sap-s4hana-maintenance-2040-clarity-choice-sap-business-suite-7/\" target=\"_blank\">announcement</a> about the extension of maintenance for Business Suite solutions has no influence on the end of compatibility pack use rights - they will be terminated after 2025.<sup>(1)</sup></p>\n<p>Besides reading the attached documents, SAP recommends the latest SAP Community webinar on the topic: <a href=\"https://www.youtube.com/live/muPrV5J7ffM?feature=shared\" target=\"_blank\">https://www.youtube.com/live/muPrV5J7ffM</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA Compatibility Scope Matrix, Way Forward</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In the “Attachments” section, the “Overview Presentation” and “Detail FAQ” documents explain the “why”, “what” and “how” of CPs. For functional details, please also refer to the Feature Scope Description (FSD) of SAP S/4HANA, <a href=\"https://help.sap.com/doc/e2048712f0ab45e791e6d15ba5e20c68/latest/\" target=\"_blank\">latest version</a> (or through http://help.sap.com/s4hana), chapter 5.</p>\n<p>The “Way Forward” presentation and overview list, also under “Attachments”, provide information about solution alternatives in the perpetual scope of SAP S/4HANA. They describe the status of each Compatibility Pack and provide links to more detailed information about its strategy. This information is provided via SAP standard documentation, such as the Roadmaps and the Innovation Discovery. The “Item ID” helps to cross-reference between the original, descriptive CP matrix and the future-oriented “Way Forward” spreadsheet.</p>\n<p>This blog <a href=\"https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/\" target=\"_blank\">https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/</a> provides more details about the procedure for Compatibility Packs after their use right expiry in 2025/2030.</p>\n<p>SAP is planning regular updates of the attachments. All forward-looking information is non-binding and with preview character. ​The released SAP Roadmaps are the proper source of information.</p>\n<p>For questions and feedback about this note and its content, please create a customer message on component XX-SER-REL with the prefix ‘CompScope’​.</p>\n<p>(1) In the exceptional cases of CS, LE-TRA and PP-PI, the usage right to their respective compatibility pack items (cf. matrix) terminates at the end of 2030.</p>", "noteVersion": 84}]}, {"note": "2833748", "noteTitle": "2833748 - SAP S/4HANA OP ( \"on premise\" ) - Transition from EC-CS to SAP S/4HANA for Group Reporting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Clarification on transition from EC-CS to SAP S/4HANA for Group Reporting (S/4GR) within <strong>one</strong> SAP S/4HANA instance</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>EC-CS in SAP S/4HANA, SAP S/4HANA for Group Reporting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You are running EC-CS in SAP S/4HANA, release 1909 OP ( \"on premise\" ) or higher, according to SAP Note <strong>2371973</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>SAP S/4HANA 1809 OP ( \"on premise\" ) - No Coexistence of EC-CS and SAP S/4HANA for Group Reporting </strong></p>\n<p>SAP S/4HANA for Group Reporting (S/4GR) reuses some parts of SAP EC-CS (Enterprise Controlling, Consolidation). EC-CS \"as is\" can be run in SAP S/4HANA, too. (See SAP Note <strong>2371973</strong>.)</p>\n<p>In S/4HANA 1809 OP ( \"on premise\" ), coexistence of EC-CS and SAP S/4HANA for Group Reporting (S/4GR) in the same SAP S/4HANA instance is <strong>not</strong> supported. The system state depends on the actual fiscal year which is set by the user in the global parameters (transaction CXGP). This fiscal year is compared to the \"<em>From Year for Line Items in SAP S/4HANA</em>\" which is specified in the \"<em>Global System Settings</em>\" (Transaction CXB3) and acts as sharp boundary between EC-CS and S/4GR:</p>\n<ul>\n<li>Before the \"<em>From Year for Line Items in SAP S/4HANA</em>\", the system state is \"EC-CS\"</li>\n<li>In the \"<em>From Year for Line Items in SAP S/4HANA</em>\" or in later years, the system state is \"S/4GR\"</li>\n</ul>\n<ul>\n<li>Transactions which are accessible in EC-CS only (example: CX5U0 for \"<em>Elimination of InterUnit Profit/Loss in Inventory</em>\")</li>\n<li>Transactions which are accessible in S/4GR only (example: FINCS_ADDLFLD_SEL_A for \"<em>Additional Characteristics</em>\")</li>\n<li>Transactions which are accessible in both applications (example: CXEC for \"<em>Reclassification</em>\")</li>\n</ul>\n<p>In SAP S/4HANA 1809 OP ( \"on premise\" ), transition from EC-CS to S/4GR in the same SAP S/4HANA instance is <strong>not</strong> possible. Customers who are using EC-CS in SAP S/4HANA 1809 OP must set up a separate (new) SAP S/4HANA instance for usage of S/4GR.</p>\n<p><strong>SAP S/4HANA 1909 OP ( \"on premise\" ) - \"Hybrid\" System State for Parallel Usage of EC-CS and SAP S/4HANA for Group Reporting</strong></p>\n<p>In S/4HANA 1909 OP ( \"on premise\" ), the system state depends on both dimension and fiscal year. To this aim an additional \"<em>From Year for Migration Phase to SAP S/4HANA</em>\" can be specified in the \"<em>Global System Settings</em>\" (transaction CXB3), in system state \"EC-CS\". The \"<em>From Year for Migration Phase to SAP S/4H</em>ANA\" must be earlier than the \"<em>From Year for Line Items in SAP S/4HANA</em>\". This setting enables a \"hybrid\" system state which starts at the \"<em>From Year for Migration Phase to SAP S/4HANA</em>\" and ends with the \"<em>From Year for Line Items in SAP S/4HANA</em>\". Namely, in S/4GR there is only <strong>one</strong> dimension value possible, and it is fixed to value <strong>Y1</strong>. This enables separation between both applications during the \"hybrid\" phase:</p>\n<ul>\n<li>Before the \"<em>From Year for Migration Phase to SAP S/4HA</em>NA\", the system state is \"EC-CS\"</li>\n<li>In the \"<em>From Year for Migration Phase to SAP S/4HANA</em>\" or in later years before the \"<em>From Year for Line Items in SAP S/4HANA</em>\" the system state depends on the dimension value:</li>\n<ul>\n<li>System state is \"S/4GR\" in dimension Y1</li>\n<li>System state is \"EC-CS\" in other dimensions</li>\n</ul>\n<li>In the \"<em>From Year for Line Items in SAP S/4HANA</em>\" or in later years, the system state is \"S/4GR\" </li>\n</ul>\n<p>The \"hybrid\" system state supports parallel usage of both applications:</p>\n<ul>\n<li>Productive usage of EC-CS in dimensions others than Y1</li>\n<li>Test usage of S/4GR in dimension Y1</li>\n</ul>\n<p><strong>SAP S/4HANA 2020 OP ( \"on premise\" ) and higher - Correction of Ledger / Version Combinations Before Setup of \"Hybrid\" System State</strong></p>\n<p>In S/4HANA 2020 OP ( \"on premise\" ) and higher, the \"hybrid\" system state is same as in S/4HANA 1909 OP. However, one-to-one correspondence of consolidation ledger and consolidation version is mandatory. This means that, prior to setup of \"hybrid\" system state, it might be necessary to correct existing master data (for consolidation groups) and transaction data, to achieve the required one-to-one correspondence of ledger and version. This process usually requires involvement of SAP Support. For further details, see SAP Note <strong>2901554</strong>.</p>\n<p><strong>Prerequisite for Transition from EC-CS into \"Hybrid\" System State </strong></p>\n<p>Prerequisite for transition from EC-CS into \"hybrid\" state is completion of setup and execution of group close in EC-CS in the fiscal year prior to the \"<em>From Year for Migration Phase to SAP S/4HANA</em>\", in at least one dimension different from Y1. (Usage of dimension Y1 in EC-CS is generally critical from the perspective of transition to S/4GR.) If you are using integration between Financial Accounting and EC-CS, this dimension must be assigned to the integrated consolidation type \"<em>Company Consolidation</em>\".</p>\n<p><strong>Process Steps for Transition from EC-CS into \"Hybrid\" System State</strong></p>\n<p>Transition from EC-CS into hybrid system state is executed in a multiple step procedure. The sequence of the conversion steps is important. Some steps are executed by customer, whereas other steps are executed by SAP Support. In the process description below it is indicated for each step whether the step is to be executed by customer or by SAP Support.</p>\n<p>From perspective of system landscape, <em>all</em> conversion steps must be executed in the source environment (i.e., in the customizing client of your development system). For the majority of the steps, the result can be recorded into a customizing transport request and then transported into the target environments (i.e., further clients of the development system, as well as quality system and productive system). For those conversion steps for which you need to launch Fiori apps, the result cannot be transported. There are few subsequent steps which are transportable, but depend on the result of the Fiori-related (non-transportable) steps. Therefore, it is recommended that you prepare several customizing transport requests, in the source environment:</p>\n<ul>\n<li>Customizing transport request no. 1 is for storage of the results of all conversion steps which are executed prior to the first step which needs Fiori apps</li>\n<li>Customizing transport request no. 2 is for storage of the results of the few conversion steps which are executed after the steps which need Fiori apps</li>\n</ul>\n<p><span>Sequence of steps in source environment</span></p>\n<ol>\n<li>Preparation steps</li>\n<ol>\n<li>Customer: Implement SAP Notes according to instructions provided by SAP Support, store note implementations into a workbench transport request</li>\n<li>Customer: Communicate intended \"<em>From Year for Line Items in SAP S/4HANA</em>\" (i.e., year of go-live with S/4GR which defines end of \"hybrid\" state)</li>\n<li>Customer: Communicate intended \"<em>From Year for Migration Phase to SAP S/4HANA</em>\" (i.e., start year of \"hybrid\" state). This from year must be earlier than the \"<em>From Year for Line Items in SAP S/4HANA</em>\".</li>\n<li>Customer: Communicate intended EC-CS source dimension for migration to S/4GR (choose your EC-CS main dimension as \"source dimension\"). If you are using integration between Financial Accounting and EC-CS, this dimension must have been assigned (in EC-CS) to the integrated consolidation type \"<em>Company Consolidation</em>\".</li>\n<li>Customer: Communicate technical name of tasks for validation of reported, standardized and consolidated data which are to be created (by SAP Support) in step 3.d</li>\n<li>Customer: Communicate information on the charts of accounts for which FS item master data and hierarchies are to be replicated (by SAP Support) from EC-CS into S/4GR in step 4.a</li>\n<li>Customer: Communicate technical name of consolidation of investments (C/I) method for parent unit which is to be created (by SAP Support) in step 5.a. Communicate technical name of default C/I method for purchase consolidation which is to be used in step 5.b, if there is no method assignment existing in EC-CS.</li>\n<li>Customer: Create customizing transport requests no. 1 and 2, provide tasks for SAP Support user in these requests</li>\n<li>Release 2020 OP (\"on premise\") and higher - SAP Support: Perform check for uniqueness of ledger / version combinations (of EC-CS legacy data) in both source and target environments, align with customer about technical names of new consolidation versions and ledger versions which are to be created (by SAP Support) in steps 3.e and 3.f</li>\n</ol>\n<li>Global system settings (<em>all</em> steps in this part are executed by SAP Support)</li>\n<ol>\n<li>Create dimension value Y1 and its settings relevant for S/4GR, store into request no. 1</li>\n<li>Switch <strong>on</strong> indicator \"<em>SAP S/4HANA Consolidation</em>\" in setting \"<em>SAP Consolidation: System Status in SAP S/4HANA</em>\" (transaction CX8S4H), store into request no. 1</li>\n<li>Set \"<em>From Year for Line Items in SAP S/4HANA</em>\" as specified by customer in step 1.b, store into request no. 1. (Remark: Definition of a long \"hybrid\" phase is <em>not </em>harmful, because the hybrid phase can be shortened by SAP Support upon demand later on)</li>\n<li>Set \"<em>From Year for Migration Phase to SAP S/4HANA</em>\" as specified by customer in step 1.c, store into request no. 1</li>\n<li>Set \"<em>From Year for New Group Reporting Logic</em>\" equal to \"<em>From Year for Migration Phase to SAP S/4HANA</em>\" specified by customer in step 1.c, store into request no. 1</li>\n<li>Release 2021 OP ( \"on premise\") - Set \"<em>From Year for Group Reporting Preparation Ledger</em>\" equal to \"<em>From Year for Migration Phase to SAP S/4HANA</em>\" specified by customer in step 1.c, store into request no. 1</li>\n<li>Switch <strong>on</strong> indicators in frame \"<em>Settings for Configuration Control</em>\" in the \"<em>Global System Settings</em>\" (transaction CXB3), store into request no. 1</li>\n<li>Persist fix value for unit of measure for statistical items for share percentages</li>\n</ol>\n<li>Setup of configuration in target dimension Y1 (<em>all</em> steps in this part are executed by SAP Support)</li>\n<ol>\n<li>Set indicator for \"<em>Source for Migration to SAP S/4HANA</em>\" for the EC-CS source dimension as specified by customer in step 1.d (transaction CX1K), store into request no. 1</li>\n<li>Copy configuration settings from EC-CS source dimension into target dimension Y1. In this conversion step, <em>all</em> consolidation units in target dimension Y1 get assigned data transfer method U (\"<em>Flexible Upload</em>\"), store into request no. 1</li>\n<li>Create configuration settings in target dimension Y1 which do <strong>not</strong> exist in EC-CS, but only in S/4GR (examples: Configuration of Additional Characteristics, FS Item Attributes), store into request no. 1</li>\n<li>Create tasks for validation of reported, standardized and consolidated financial data with technical names as specified by customer in step 1.e, store into request no. 1</li>\n<li>Release 2020 OP (\"on premise\") and higher - Create new consolidation versions from reference versions (transaction CX8CPV) according to alignment with customer in step 1.i, store into request no. 1</li>\n<li>Release 2020 OP (\"on premise\") and higher - Create new ledger versions and assign them to new consolidation versions (transaction CXB1) according to alignment with customer in step 1.i, store into request no. 1</li>\n<li>Release 2020 OP (\"on premise\") and higher - Correct non-unique ledger / version combinations in master data of consolidation groups</li>\n<li>Release 2020 OP (\"on premise\") and higher - Convert consolidation ledger settings into version settings, replicate consolidation unit master data from EC-CS into S/4GR</li>\n<li>Release 2020 OP (\"on premise\") and higher - Edit special versions for consolidation unit attributes which were created in step 3.h (transaction CXB1), store into request no. 1</li>\n</ol>\n<li>Replication of FS items (master data and hierarchies) from EC-CS into S/4GR</li>\n<ol>\n<li>Customer: You must use the EC-CS chart of accounts also in S/4GR. This implies that the breakdowns of FS items cannot deviate between EC-CS and S/4GR. If you intend to use deviating breakdowns, contact SAP Support.</li>\n<li>SAP Support: Perform replication of FS item master data and hierarchies from EC-CS into S/4GR, restrict to charts of accounts specified by customer in step 1.f (if applicable)</li>\n</ol>\n<li>Conversion of consolidation group structure from consolidation unit hierarchies into \"scopes\" (in S/4GR, consolidation groups do <strong>not</strong> appear in consolidation unit hierarchies, but they represent flat lists of consolidation units which are denoted \"scopes\")</li>\n<ol>\n<li>SAP Support: Launch \"<em>Consolidation of Investments (C/I) Methods</em>\" (transaction CXI4), create new C/I method with accounting technique \"<em>Parent Unit</em>\" with technical name as specified by customer in step 1.g, store into request no. 1</li>\n<li>SAP Support: Convert consolidation unit hierarchies into \"scopes\"</li>\n</ol>\n<li>Adjustment of transportable settings in target dimension Y1 (<em>all</em> steps in this part are executed by customer)</li>\n<ol>\n<li>Launch \"<em>Global Parameters</em>\" (transaction CXGP), set dimension value to EC-CS \"source dimension\" (which you have specified in step 1.d) and fiscal year to year prior to \"<em>From Year for Migration Phase to SAP S/4HANA</em>\"</li>\n<li>Review the consolidation units which have assigned data transfer method R (\"<em>Realtime Update from FI</em>\") in EC-CS</li>\n<li>Review the FS items and FS item sets which you are using in the following transactions of EC-CS:</li>\n<ol>\n<li>\"<em>Currency Translation Methods</em>\" (transaction CXD1)</li>\n<li>\"<em>Reclassification Methods</em>\" (transaction CXEB)</li>\n<li>\"<em>Selected Items</em>\" (transaction CXE5)</li>\n<li>\"<em>Consolidation of Investment Methods</em>\" (transaction CXI4)</li>\n<li>Goodwill posting items in \"<em>Global Settings for Consolidation of Investments</em>\" (button \"<em>FS Items</em>\" in transaction CXI0)</li>\n<li>\"<em>Miscellaneous Selected Items for Consolidation of Investments</em>\" (transaction CXI9)</li>\n<li>\"<em>Posting Items for the Equity Method</em>\" (transaction CXJ3)</li>\n<li>\"<em>Minority Interest Items</em>\" (transaction CXH1)</li>\n</ol>\n<li>Launch \"<em>Global Parameters</em>\" (transaction CXGP), set dimension value to Y1 and fiscal year to \"<em>From Year for Migration Phase to SAP S/4HANA</em>\"</li>\n<li>Launch \"<em>Tasks for Preparation for Consolidation Group Changes (PCC)</em>\" (launch transaction CXED), usage of PCC task in consolidation monitor is mandatory in S/4GR</li>\n<ul>\n<li>If no PCC task is existing so far, create new PCC task, assign document types on <em>all</em> relevant posting levels (02, 12, 22) to this new PCC task, store into request no. 1</li>\n<li>Otherwise, assign document types on <em>all</em> relevant posting levels (02, 12, 22) to that existing PCC task which is used in consolidation monitor, store into request no. 1</li>\n<li>If automatic consolidation of investments (C/I) is <strong>not</strong> used in EC-CS, ensure that the FS item roles for \"<em>Net Income Before First Consolidation</em>\" and \"<em>Clearing Consolidation of Investments</em>\" are maintained and assigned (steps 6.c, 6.f, 7.b, 7.c)</li>\n</ul>\n<li>Launch \"<em>FS Item Attributes</em>\" (transaction CX8ITAVC), create FS item attribute values in accordance to settings for FS items which are you are using in the EC-CS transactions mentioned in step 6.c, store into request no. 1</li>\n<li>Launch \"<em>Document Types</em>\" (transaction CXE1), create or assign number ranges to existing document types in dimension Y1 for <em>all</em> relevant versions, store into request no. 1</li>\n<li>Launch \"<em>Document Types for Reported Financial Data</em>\" (transaction CXER), create document types for posting of reported financial data (on posting level 00) with business applications \"<em>Flexible Upload</em>\", \"<em>Net Profit Calculation</em>\" and \"<em>Rounding</em>\", store into request no. 1</li>\n<li>Launch \"<em>Document Types for Posting of Group Shares</em>\" (transaction CXES), create document type for posting of group shares (on posting level 30 with business application \"<em>Posting of Group Shares</em>\"), store into request no. 1</li>\n<li>Launch \"<em>Consolidation Tasks: Simple Tasks</em>\" (transaction CX5TB), create tasks of categories \"<em>Release of Reported Financial Data</em>\", \"<em>Validation of Universal Documents</em>\" and \"<em>Posting of Group Shares</em>\", store into request no. 1</li>\n<li>Launch \"<em>Task Groups</em>\" (transaction CXE0), copy the task groups which you are using in EC-CS in data monitor and consolidation monitor to new task groups, remove all tasks of categories which are <strong>not</strong> supported in S/4GR (\"<em>Apportionment</em>\", \"<em>Copy Task</em>\", \"<em>Custom Task, Data Monitor</em>\", \"<em>Preparation for Consolidation Group Changes</em>\") from the new task group for data monitor, remove all tasks of categories which are <strong>not</strong> supported in S/4GR (\"<em>Intercompany Elimination</em>\", \"<em>Elimination of Interunit Profit/Loss in Inventory</em>\", \"<em>Custom Task, Consolidation Monitor</em>\") from the new task group for consolidation monitor, add tasks of category \"<em>Release of Reported Financial Data</em>\", \"<em>Validation of Universal Documents</em>\", \"<em>Validation of Reported Financial Data</em>\" (specified in step 1.e, created in step 3.d), \"<em>Validation of Standardized Financial Data</em>\" (specified in step 1.e, created in step 3.d) to the new task group for data monitor, add tasks of category \"<em>Posting of Group Shares</em>\", \"<em>Preparation for Consolidation Group Changes</em>\" (if missing so far), \"<em>Validation of Consolidated Financial Data</em>\" (specified in step 1.e, created in step 3.d) to the new task group for consolidation monitor, store into request no. 1</li>\n<li>Launch \"<em>Assign Task Groups</em>\" (transaction CXP1), assign new task groups for data monitor and consolidation monitor to dimension Y1, store into request no. 1</li>\n<li>Launch \"<em>Check Customizing Settings for Consolidation of Investments</em>\" (transaction CX6C3), execute selection screen, review possible error messages and revise settings for automatic C/I to correct the errors</li>\n</ol>\n<li>Non-transportable settings and subsequent transportable settings in target dimension Y1 (<em>all</em> steps in this part are executed by customer)</li>\n<ol>\n<li>If you are using real-time update for integrated consolidation type \"<em>Company Consolidation</em>\" without mapping in EC-CS, launch Fiori app \"<em>Consolidation Unit, Create or Change</em>\" (or \"<em>Import Consolidation Unit Master Data</em>\"), change data transfer method for those consolidation units which you have identified in step 6.b, from R (\"<em>Realtime Update from FI</em>\") to H (\"<em>Read from Universal Document</em>\"), set \"<em>From Year for Migration Phase to SAP S/4HANA</em>\" as \"<em>Effective Year for Read from Universal Document</em>\" (on tab \"<em>Data Collection</em>\"), set \"<em>Source for Local Currency</em>\" (on tab \"<em>Master Data</em>\"), set \"<em>Source for Group Currency</em>\" (in menu entry \"<em>Goto -&gt; Fiscal Year Variant</em>\")</li>\n<li>Launch Fiori app \"<em>Define FS Items</em>\" from Fiori launchpad, assign attribute values which you created in step 6.f, to those FS items which you are actually using in the EC-CS transactions mentioned in step 6.c</li>\n<li><span>Launch the EC-CS transactions mentioned in step 6.c, assign the corresponding FS item attribute values which you have created in step 6.g, store into request no. 2</span></li>\n<li><span>Launch Fiori app \"</span><em>Define Selections</em><span>\" from Fiori launchpad, define selection objects which contain the characteristic values which you are actually using in the following transactions of EC-CS:</span></li>\n<ol>\n<li>\"<em>Breakdown Categories</em>\" (transaction CX1I4, maximum sets)</li>\n<li><span>\"<em>Currency Translation Methods</em>\" (transaction CXD1, selection)</span></li>\n<li><span>\"<em>Reclassification Methods</em>\" (transaction CXEB, trigger and percentage)</span></li>\n<li><span>\"<em>Miscellaneous Selected Items for Consolidation of Investments</em>\" (transaction CXI9, investment, equity and other comprehensive income (OCI))</span></li>\n<li><span>\"<em>Reported Items for Equity Holdings Adjustments</em>\" (transaction CXJ2)</span></li>\n</ol>\n<li>Launch the EC-CS transactions mentioned in step 7.d, assign the corresponding selection objects which you have created in step 7.d, store into request no. 2</li>\n<li>Launch Fiori app \"<em>Define FS Items</em>\" from Fiori launchpad, refer to review of EC-CS reclassification settings performed in step 6.c, set FS item attribute values for attribute \"<em>Elimination Target</em>\" for each trigger item used in \"<em>Reclassification Methods</em>\" by assigning the corresponding source or destination item as \"<em>Elimination Target</em>\"</li>\n<li>Launch Fiori app \"<em>Define FS Items</em>\" from Fiori launchpad, refer to review of EC-CS minority items performed in step 6.c, set FS item attribute values for attribute \"<em>Non-Controlling Interest (NCI) Target</em>\" for each investment and equity item by assigning the corresponding minority item as \"<em>NCI Target</em>\"</li>\n<li>Launch Fiori app \"<em>Define Validation Rules</em>\" from Fiori launchpad, create the validation rules you need to validate financial data, create the validation methods you need to validate reported, standardized and consolidated financial data, assign the validation rules which you have created before to those validation methods</li>\n<li>Launch Fiori app \"<em>Define Validation Methods</em>\" from Fiori launchpad, create the validation methods you need to validate reported, standardized and consolidated financial data, assign the validation rules which you have created in step 7.h to these validation methods</li>\n<li>Launch Fiori app \"<em>Assign Validation Methods</em>\" from Fiori launchpad, assign the validation methods which you have created in step 7.i to the validation tasks which were created (by SAP Support) in step 3.d</li>\n</ol>\n<li>Definition of field mapping from EC-CS custom subassignments onto additional characteristics of S/4GR (see SAP Note <strong>2911481</strong>): Only relevant if you are using custom subassignments in EC-CS</li>\n<ol>\n<li>SAP Support: Feasibility evaluation based on existing definition of EC-CS custom subassignments, if feasible, define field mapping for execution during balance carry forward (step 11), store into request no. 1</li>\n</ol>\n<li>Preparation for migration of FS item substitution (see SAP Note <strong>2947061</strong>): Only relevant if you are using FS item substitution (posting of contra items on posting level 08) in EC-CS (<em>all</em> steps in this part are executed by customer)</li>\n<ol>\n<li>Launch \"<em>Global Parameters</em>\" (transaction CXGP), set dimension value to Y1 and fiscal year to \"<em>From Year for Migration Phase to SAP S/4HANA</em>\"</li>\n<li>Launch \"<em>Document Types</em>\" (transaction CXE1), create document types and assign number ranges for <em>all</em> relevant versions, store into request no. 1</li>\n<ul>\n<li>Document type for reclassification for FS item substitution in S/4GR (posting level 10, business application \"<em>reclassification</em>\", with automatic document reversal)</li>\n<li>Document type for migration of EC-CS legacy data for FS item substition (posting level 10, business application \"<em>other</em>\", without automatic document reversal)</li>\n</ul>\n<li>Communicate information on these document types to SAP Support</li>\n<li>Launch \"<em>Reclassification Methods</em>\" (transaction CXEB), create sign-triggered reclassification method for item substitution, store into request no. 1</li>\n<li>Launch \"<em>Reclassification Tasks</em>\" (transaction CXEA), create reclassification task for item substitution, assign reclassification method and document type which you created in steps 9.b and 9.c, store into request no. 1</li>\n<li>Launch \"<em>Global Parameters</em>\" (transaction CXGP), set dimension value to EC-CS \"source dimension\" (which you have specified in step 1.d)</li>\n<li>Launch \"<em>Dimension</em>\" settings (transaction CX1K) for EC-CS \"source dimension\", assign the document type for migration which you created in step 9.b as \"<em>Document Type for Item Substitution in SAP S/4HANA</em>\", store into request no. 1</li>\n</ol>\n<li>Preparation for migration of investment data (see SAP Note <strong>2908825</strong>): Only relevant if you are using automatic consolidation of investments (C/I) in EC-CS (<em>all</em> steps in this part are executed by customer)</li>\n<ol>\n<li>Launch \"<em>Global Parameters</em>\" (transaction CXGP), set dimension value to Y1 and fiscal year to \" <em>From Year for Migration Phase to SAP S/4HANA</em>\"</li>\n<li>Launch Fiori app \"<em>Define FS Items</em>\" from Fiori Launchpad, create a separate (new) FS item with breakdown for quantity which you will be using for investment data in S/4GR, starting with the \"<em>From Year for Migration Phase to S/4HANA</em>\"</li>\n<li>Launch Fiori app \"<em>Define Selections</em>\" from Fiori Launchpad, include the new investment item into the selection object for investment which you assigned in \"<em>Miscellaneous Selected Items for Consolidation of Investments</em>\" (transaction CXI9), in step 7.e. (Do <strong>not</strong> include the EC-CS investment item into this selection object.)</li>\n<li>Launch the settings for \"<em>FS Items to be Carried Forward</em>\" (transaction CXS3), maintain the EC-CS investment item as \"<em>FS Item in the Old Year</em>\" and assign the new investment item as \"<em>Debit / Credit Item in the New Year</em>\", store into request no. 2</li>\n<li>SAP Note <strong>3080015</strong> outlines alternative use cases for investment items during \"hybrid\" state. If you decide to use the separate (new) investment items in S/4GR only, but you intend to continue usage of the \"old\" investment item for investment data in EC-CS also during \"hybrid\" state, you must switch <strong>on</strong> the indicator \"<em>Separated Investment Items</em>\" in the settings of the EC-CS source dimension (transaction CX1K), store into request no. 1</li>\n</ol>\n<li>Balance carry forward into \"hybrid\" system state (<em>all</em> steps in this part are executed by customer)</li>\n<ol>\n<li>Launch \"<em>Global Parameters</em>\" (transaction CXGP), change dimension value to EC-CS \"source dimension\" (which you have specified in step 1.d) and time (year, period) to first closing period in \"<em>From Year for Migration Phase to S/4HANA</em>\"</li>\n<li>Launch data monitor (transaction CXCD), execute balance carry forward task in EC-CS \"source dimension\", this step creates the opening balance for EC-CS in \"hybrid\" state</li>\n<li>Change \"<em>Global Parameters</em>\" to dimension value Y1, keep time (year, period) as first closing period in \"<em>From Year for Migration Phase to S/4HANA</em>\"</li>\n<li>Execute balance carry forward task in dimension Y1, this step creates the opening balance for S/4GR in \"hybrid\" state</li>\n</ol>\n<li>Conversion of FS item substitution (see SAP Note <strong>2947061</strong>, executed by SAP Support)</li>\n<li>Conversion of additional financial data for investment (see SAP Note <strong>2908825</strong>, executed by SAP Support)</li>\n<li>Conversion of automatic reversal documents (see SAP Note <strong>2885858</strong>, executed by SAP Support)</li>\n<li>Release 2020 OP (\"on premise\") and higher - Correct non-unique ledger / version combinations in transaction data</li>\n</ol>\n<p><span>Sequence of steps in target environments</span></p>\n<ol>\n<li>Customer: Import workbench transport request which contains implementation of relevant SAP Notes, from source system</li>\n<li>Customer: Import customizing transport request no. 1 from source environment</li>\n<li>SAP Support: Persist fix value for unit of measure for statistical items for share percentages (same as step 2.h)</li>\n<li>Release 2020 OP (\"on premise\") and higher - SAP Support: Correction and conversion of ledger / version settings</li>\n<ol>\n<li>Correct non-unique ledger / version combinations in consolidation group master data (same as step 3.g)</li>\n<li>Convert ledger / version settings, replicate consolidation unit master data (same as step 3.h)</li>\n</ol>\n<li>SAP Support: Replicate FS items from EC-CS into S/4GR (same as step 4.a)</li>\n<li>SAP Support: Convert consolidation group structure from hierarchies into \"scopes\" (same as step 5.b)</li>\n<li>Customer: Execute Fiori-related conversions steps</li>\n<ol>\n<li>Set global parameters to dimension Y1 in \"hybrid\" state (same as step 6.a)</li>\n<li>Change assignment of data transfer method for integrated consolidation units (same as step 7.a)</li>\n<li>Assign FS item attributes to FS items (same as step 7.b)</li>\n<li>Assign FS item attributes in EC-CS customizing settings (same as step 7.c)</li>\n<li>Define selection objects (same as step 7.d)</li>\n<li>Assign selection objects in EC-CS customizing settings (same as step 7.e)</li>\n<li>Assign FS item target attributes for \"<em>Elimination Target</em>\" and \"<em>NCI Target</em>\" to FS items (same as steps 7.f and 7.g)</li>\n<li>Create validation rules and validation methods, assign validation methods to validation tasks (same as steps 7.h, 7.i and 7.j)</li>\n</ol>\n<li>Customer: Preparation for migration of investment data</li>\n<ol>\n<li>Define new investment item with breakdown for quantity (same as step 10.b)</li>\n<li>Include new investment item into selection object for investment (same as step 10.c)</li>\n</ol>\n<li>Customer: Import customizing transport request no. 2 from source environment</li>\n<li>Customer: Execute balance carryforward task (same as step 11)</li>\n<li>SAP Support: Convert FS item substitution, investment data and automatic reversal documents (same as steps 12, 13 and 14)</li>\n<li>Release 2020 OP (\"on premise\") and higher - SAP Support: Correct non-unique ledger / version settings in transaction data (same as step 15)</li>\n</ol>\n<p><strong>Parallel Processing of Group Close in \"Hybrid\" System State</strong></p>\n<p>During the \"hybrid\" system state, the fiscal year is the \"<em>From Year for Migration to SAP S/4HANA</em>\" or later, but it is before the \"<em>From Year for Line Items in SAP S/4HANA</em>\". In those fiscal years, you must process the group close <strong>twice</strong>:</p>\n<ul>\n<li>Productive group close with EC-CS in dimensions others than Y1 (including \"<em>Source Dimension for Migration to SAP S/4HANA</em>\")</li>\n<li>Testing of group close with S/4GR in dimension Y1</li>\n</ul>\n<p>For FS item master data and hierarchy settings, you must apply double maintenance:</p>\n<ul>\n<li>Dimensions others than Y1: Launch \"<em>Edit FS Item Hierarchies</em>\" (transaction CX16) - this is required even for new FS items which are only intended for usage in S/4GR</li>\n<li>Dimension Y1: Launch Fiori apps \"<em>Define FS Items</em>\" (for maintenance of FS item master data) and \"<em>Manage Global Hierarchies</em>\" (for maintenance of FS item hierarchies) from Fiori launchpad</li>\n</ul>\n<p>Release 2020 OP (\"on premise\") and higher - For consolidation unit master data, you must apply double maintenance:</p>\n<ul>\n<li>Dimensions others than Y1: Launch \"<em>Create Consolidation Unit</em>\" (transaction CX1M) or \"<em>Change Consolidation Unit</em>\" (transaction CX1N)</li>\n<li>Dimension Y1: Launch Fiori app \"<em>Define Consolidation Units</em>\" from Fiori launchpad</li>\n</ul>\n<p>For group structure, you must apply double maintanance:</p>\n<ul>\n<li>Dimensions others than Y1: Launch \"<em>Edit Consolidation Group Hierarchies</em>\" (transaction CX1X)</li>\n<li>Dimension Y1: Launch Fiori app \"<em>Manage Group Structure</em>\" from Fiori launchpad</li>\n</ul>\n<p>You must process <strong>all </strong>tasks in data monitor and consolidation monitor for <strong>both</strong> applications in <strong>every</strong> fiscal year for which the \"hybrid\" system state is applicable:</p>\n<ul>\n<li>Dimensions others than Y1 in EC-CS</li>\n<li>Dimension Y1 in S/4GR</li>\n</ul>\n<p>Some functionalities which you might be using in EC-CS are currently <strong>not</strong> supported in S/4GR (examples: Item Substitution, Apportionment, Elimination of InterUnit Profit/Loss in Inventory) or processed in different way in S/4GR than in EC-CS (example: EC-CS task category \"<em>Intercompany Elimination</em>\" is handled in S/4GR by tasks of category \"<em>Reclassification</em>\"). In view of these differences between both applications, it is generally very likely that you have to apply manual postings in dimension Y1, to supplement or adjust the results of S/4GR to match with your productive group close in EC-CS in the \"<em>Source Dimension for Migration to SAP S/4HANA</em>\". The actual scope of the manual postings which you have to apply in dimension Y1 strongly depends on the functional scope which you are using in EC-CS.</p>\n<p><strong>Transition from \"Hybrid\" System State into SAP S/4HANA for Group Reporting</strong></p>\n<p>It is expected that by the parallel processing of group close during the \"hybrid\" system state you have validated that, at the end of the \"hybrid\" phase, the cumulative results in dimension Y1 and \"<em>Source Dimension for Migration to SAP S/4HANA</em>\" are congruent. (As stated in the previous chapter, it is generally necessary to apply manual postings in dimension Y1 to achieve this congruence.) When such congruence has been reached, you execute the \"<em>Balance Carry Forward</em>\" task in the data monitor in dimension Y1 in the first closing period of the \"<em>From Year for Line Items in SAP S/4HANA</em>\". The Balance Carry Forward task then creates the opening balance in the \"<em>From Year for Line Items in SAP S/4HANA</em>\" which acts as starting point for your productive usage of SAP S/4HANA for Group Reporting.</p>", "noteVersion": 15, "refer_note": [{"note": "2901554", "noteTitle": "2901554 - Consulting note to correct Version / Ledger combinations", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As per SAP notes <a href=\"/notes/2868331\" target=\"_blank\">2868331</a> it is not supported, within one version, to assign different ledgers to the consolidation group(s).</p>\n<p>However, such incorrect combinations of Versions and Ledgers exist in your system.</p>\n<p>You need to correct these inconsistencies before the upgrade to the version CE 2008.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>When you open the Consolidation  Monitor a message indicates that inconsistent master data and/or transaction exist in your system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span>Identify the incorrect combinations of Versions and Ledgers in your system.</span></p>\n<p class=\"Text\">Example: In the Version Y10, the Consolidation Group CG1 is assigned to Ledger Y1 (EUR) and the Consolidation Group CG2 is assigned to Ledger Y2 (USD). In this case, one of this two assignments must be done in a separate Version. Hence you can you decide to assign the Consolidation Group CG2 to Ledger Y2 (USD) in a new version Y20, and correct the assignment of Consolidation Group CG2 in Version Y10 to the Ledger Y1.</p>\n<p><span>Situation before correction:</span></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Version</td>\n<td>Consolidation group</td>\n<td>Ledger</td>\n</tr>\n<tr>\n<td>Y10</td>\n<td>CG1</td>\n<td>Y1</td>\n</tr>\n<tr>\n<td>Y10</td>\n<td>CG2</td>\n<td>Y2</td>\n</tr>\n</tbody>\n</table></div>\n<p>Situation after correction:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Version</td>\n<td>Consolidation Group</td>\n<td>Ledger</td>\n</tr>\n<tr>\n<td>Y10</td>\n<td>CG1</td>\n<td>Y1</td>\n</tr>\n<tr>\n<td>Y10</td>\n<td>CG2</td>\n<td>Y1</td>\n</tr>\n<tr>\n<td>Y20</td>\n<td>CG2</td>\n<td>Y2</td>\n</tr>\n</tbody>\n</table></div>\n<p><span>To correct this, follow the following steps:</span></p>\n<p><span>In the Configuration Step </span><em>Create Version From Reference Version, c</em><span>reate a new Version with the existing Version as a reference.</span></p>\n<p>In the Configuration Step <em>Define Version, a</em>djust settings of this newly created Version. The Special Version \"Ledger\" (named \"Cons Group Attribute\" as of release CE 2008) must be different from the one used in the original Version. Other Special Versions should be identical in both Versions.</p>\n<p class=\"Text\">In the App <em>Consolidation Groups – Create and Change</em>, adjust the assignment of the Ledger to the Consolidation group for the existing and for the new version. Example: For version Y10, assign Ledger Y1 to Consolidation Group CG2, and in Version Y20, assign Ledger Y2 to Consolidation Group CG2. You must correct this ledger assignment for the same year as of which the ledger assignment had been created. You also need to adjust the Consolidation Group settings which are Ledger dependent (Consolidation Frequency, Fiscal Year Variant, Sender Group Currency).</p>\n<p class=\"Text\">If Variants are defined in Group Reports, or specific reports have been defined using the Version key, this must be adjusted also because of the change of the version.</p>\n<p class=\"Text\">If transaction data also exists on incorrect combinations of Versions and Ledgers, this also needs to be corrected. For this, open an incident on the component FIN-CS-COR.</p>\n<p class=\"Text\">When transaction data is corrected, and if transaction data was already replicated to external systems, data transfer must be executed again because the change of the version.</p>", "noteVersion": 3}, {"note": "2911481", "noteTitle": "2911481 - Transition from EC-CS to SAP S/4HANA for Group Reporting - Conversion of EC-CS Custom Subassignments", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides a supplement to the process for transition from EC-CS to SAP S/4HANA for Group Reporting which is described in SAP Note <strong>2833748</strong>. This supplement covers the conversion of custom subassignments which you have been using in EC-CS, into additional characteristics in SAP S/4HANA for Group reporting.</p>\n<p>Both EC-CS and SAP S/4HANA for Group Reporting provide the option to enhance the data model of the respective application by custom fields (subassignments). However, there is a difference between those two applications with regard to additional fields:</p>\n<ul>\n<li>Custom subassignments in EC-CS are <strong>cross-client</strong>. You enable usage of those fields in the transaction \"<em>Edit Characteristics</em>\" (transaction CX0A1).</li>\n<li>Additional fields in SAP S/4HANA for Group Reporting are <strong>client-dependent</strong>:</li>\n<ul>\n<li>Fields for \"<em>Additional Characteristics</em>\" predefined by SAP for Group Reporting. You enable usage of those fields in the transaction \"<em>Additional Characteristics</em>\" (transaction FINCS_ADDLFLD_SEL_U).</li>\n<li>Custom fields in SAP S/4HANA for Group Reporting. You enable usage of those fields via the generic SAP S/4HANA Extensibility framework.</li>\n</ul>\n</ul>\n<p>Transition from EC-CS into SAP S/4HANA for Group Reporting is facilitated by the concept of \"hybrid\" system state. (For details, see SAP Note <strong>2833748</strong>.) In particular, there is a special logic applied during execution of Balance Carry Forward (BCF), if you execute BCF in dimension Y1 in that fiscal year which is defined as \"<em>From Year for Migration Phase into SAP S/4HANA</em>\", in the \"<em>Global System Settings</em>\" (transaction CXB1). In this case, the system reads the EC-CS totals records from prior year from that dimension which you have defined as \"<em>Source for Migration to SAP S/4HANA</em>\", in dimension settings (transaction CX1K), and it writes the carry forward data into period zero of the \"<em>From Year for Migration Phase into SAP S/4HANA</em>\" into the \"<em>Consolidation Universal Journal Entry Line Items</em>\".</p>\n<p>Balance Carry Forward into \"hybrid\" system state provides the option to apply mapping functionality, to convert EC-CS custom fields into additional characteristics in SAP S/4HANA for Group Reporting. This mapping functionality must be activated in an additional step in the transition process, which is executed by SAP Support. See process step 6 in long text of SAP Note <strong>2833748</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>EC-CS in SAP S/4HANA, SAP S/4HANA for Group Reporting, balance carry forward, EC-CS custom fields, edit characteristics, CX0A1, additional characteristics, FINCS_ADDLFLD_SEL_U, FINCS_ADDLFLD_SEL_D</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ul>\n<li>You are running EC-CS in SAP S/4HANA, release 1909 OP ( \"on premise\" ), according to SAP Note <strong>2371973</strong>.</li>\n<li>You have been using custom subassignments in EC-CS.</li>\n<li>You have enabled usage of additional characteristics in SAP S/4HANA for Group Reporting.</li>\n<li>The technical properties of the EC-CS custom subassignments match with a suitable additional characteristic in SAP S/4HANA for Group Reporting.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import of Support Package or advanced correction by implementation of correction instruction.</p>\n<p>Contact SAP Support for conversion of EC-CS custom fields into additional characteristics in SAP S/4HANA for Group Reporting, before execution of Balance Carry Forward into \"hybrid\" system state. (See process step 6 in SAP Note <strong>2833748</strong>.)</p>\n<p> </p>", "noteVersion": 1}, {"note": "2908825", "noteTitle": "2908825 - Transition from EC-CS to SAP S/4HANA for Group Reporting - Conversion of Investment Data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides a supplement to the process for transition from EC-CS to SAP S/4HANA for Group Reporting which is described in SAP Note <strong>2833748</strong>. This supplement covers the conversion of additional financial data for investment which had been created in EC-CS.</p>\n<p>In SAP S/4HANA for Group Reporting, additional financial data for investment (and equity) are <strong>not</strong> used. Instead, you maintain investment (and equity) data in the \"<em>Consolidation Universal Journal Entry Line Items</em>\". You record the investment share percentages in SAP S/4HANA for Group Reporting in the quantity field of the consolidation universal journal entry line items. Therefore, the investment item which you are using in SAP S/4HANA for Group Reporting requires a breakdown for quantity, with a fix value for unit of measure being specified in the breakdown category. - In EC-CS, the investment item usually does <strong>not</strong> have a breakdown for quantity. Therefore, if you intend to use the EC-CS consolidation chart of accounts also in SAP S/4HANA for Group Reporting, you must define a separate (new) investment item which has a breakdown for quantity. In the settings for \"<em>FS Items to be Carried Forward</em>\" (transaction CXS3), you must define the EC-CS investment item as \"<em>FS Item in the Old Year</em>\" and assign this separate (new) investment item as \"<em>Debit / Credit Item in the New Year</em>\".</p>\n<p>There is a special logic applied during execution of Balance Carry Forward (BCF), if you execute BCF in dimension Y1 in that fiscal year which is defined as \"<em>From Year for Migration Phase into SAP S/4HANA</em>\", in the \"<em>Global System Settings</em>\" (transaction CXB1). In this case, the system reads the EC-CS totals records from prior year from that dimension which you have defined as \"<em>Source for Migration to SAP S/4HANA</em>\", in dimension settings (transaction CX1K), and it writes the carry forward data into period zero of the \"<em>From Year for Migration Phase into SAP S/4HANA</em>\" into the \"<em>Consolidation Universal Journal Entry Line Items</em>\".</p>\n<p>Before writing any transaction data into the \"<em>Consolidation Universal Journal Entry Line Items</em>\", the system applies consistency checks. For those FS items which are part of the unified selection for investment or equity which you have defined in the \"<em>Miscellaneous Selected Items for Consolidation of Investments</em>\" (transaction CXI9), the system applies an enhanced posting logic for investee unit (if the field for investee unit is initial in the investment or equity data record to be posted):</p>\n<ul>\n<li>For investment data, the system copies the value which is contained in the field for partner unit into the field for investee unit.</li>\n<li>For equity data, the system copies the value which is contained in the field for consolidation unit into the field for partner unit.</li>\n</ul>\n<p>After execution of Balance Carry Forward into \"hybrid\" system state for parallel usage of EC-CS and SAP S/4HANA for Group Reporting (see above), the equity data in \"<em>Consolidation Universal Journal Entry Line Items</em>\" is suitable for usage in SAP S/4HANA for Group Reporting, in dimension Y1, starting with the \"<em>From Year for Migration Phase into SAP S/4HANA</em>\". For investment data in \"<em>Consolidation Universal Journal Entry Line Items</em>\", they are usually <strong>not</strong> suitable for direct usage in SAP S/4HANA for Group Reporting, because these data were carried forward from EC-CS totals records on investment FS items which have initial values for investee unit. Therefore, information in investee unit field in carry forward data for investment in SAP S/4HANA for Group Reporting must be enriched in an additional step in the transition process, which is executed by SAP Support. See process steps 7 and 8 in long text of SAP Note <strong>2833748</strong> and chapter <strong>Reason and Prerequisites </strong>in the long text of this note <strong>2908825</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>EC-CS in SAP S/4HANA, SAP S/4HANA for Group Reporting, investment additional financial data, balance carry forward</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ul>\n<li>You are running EC-CS in SAP S/4HANA, release 1909 OP ( \"on premise\" ), according to SAP Note <strong>2371973</strong>.</li>\n<li>You have been maintaining additional financial data for investment in EC-CS.</li>\n<li>You have defined <strong>one</strong> dimension as \"<em>Source for Migration into SAP S/4HANA</em>\" (transaction CX1K).</li>\n<li>You have maintained unified selection for investment (and equity) in the \"<em>Miscellaneous Selected Items for Consolidation of Investments</em>\" (transaction CXI9).</li>\n</ul>\n<p>Precondition for conversion of investment from EC-CS additional financial data into SAP S/4HANA for Group Reporting is usage of a separate (new) investment item. See also process step 7 in long text of SAP Note <strong>2833748</strong>.</p>\n<ul>\n<li>Create a separate (new) FS item with breakdown for quantity which you will be using for investment data (in both EC-CS and S/4GR), starting with the \"<em>From Year for Migration Phase into SAP S/4HANA</em>\".</li>\n<li>Include the new investment item into the selection object for investment which you assign in \"<em>Miscellaneous Selected Items for Consolidation of Investments</em>\" (transaction CXI9). (Do <strong>not</strong> include the EC-CS investment item into this selection object.)</li>\n<li>In the settings for \"<em>FS Items to be Carried Forward</em>\" (transaction CXS3), maintain the EC-CS investement item as \"<em>FS Item in the Old Year</em>\" and assign the new investment item as \"<em>Debit / Credit Item in the New Year</em>\".</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import of Support Package or advanced correction by implementation of correction instruction.</p>\n<p>Contact SAP Support for conversion of investment data after execution of Balance Carry Forward. (See process step 7 in long text of SAP Note <strong>2833748</strong>.)</p>\n<p> </p>\n<p> </p>\n<p> </p>", "noteVersion": 1}, {"note": "2885858", "noteTitle": "2885858 - Transition from EC-CS to SAP S/4HANA for Group Reporting - Conversion of Automatic Reversal Documents", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides a supplement to the process for transition from EC-CS to SAP S/4HANA for Group Reporting which is described in SAP Note <strong>2833748</strong>. This supplement covers the conversion of automatic reversal documents which had been created in EC-CS during the last closing before entering into the \"hybrid\" system state. These reversal documents which in EC-CS in the first closing periods (according to the settings for consolidation frequency in the document types with automatic reversal across fiscal years) of the \"hybrid\" system state. They must be made available for SAP S/4HANA for Group Reporting by an additional step in the transition process, which is executed by SAP Support. See process step 9 in long text of SAP Note <strong>2833748</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>EC-CS in SAP S/4HANA, SAP S/4HANA for Group Reporting, automatic reversal</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ul>\n<li>You are running EC-CS in SAP S/4HANA, release 1909 OP ( \"on premise\" ), according to SAP Note <strong>2371973</strong>.</li>\n<li>You are using EC-CS document types for which you have defined the settings for automatic reversal as follows:</li>\n<ul>\n<li>You have activated the indicator \"<em>Automatic Reversal in Subsequent Period/Year</em>\"</li>\n<li>You did <strong>not</strong> activate the indicator \"<em>Suppress Posting of Automatic Reversals to Subsequent Year</em>\"</li>\n</ul>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import of Support Package or advanced correction by implementation of correction instruction</p>\n<p> </p>", "noteVersion": 2}]}, {"note": "2659672", "noteTitle": "2659672 - FAQ About SAP S/4HANA Finance for Group Reporting (On Premise)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The FAQ Note lists the frequently asked questions we collect from SAP customers or employees using <strong>SAP S/4HANA Finance for group reporting</strong> (On-Premise). For the FAQs about Group Reporting in latest release of <strong>SAP S/4HANA Cloud</strong>, see SAP Note <a href=\"/notes/2659656\" target=\"_blank\">2659656</a>.</p>\n<p>Table of Contents:</p>\n<p><strong>1. General</strong><br/>1.1. What are the important prerequisites for implementing Group Reporting in <strong>SAP S/4HANA</strong>?<br/>1.2. For the flexible upload of financial data, what should the file look like?<br/>1.3. Where are the documentation and guides available to implement Group Reporting?<br/>1.4. When opening the <em>Interunit Reconciliation - Group View</em> app, why is the error <strong>No value could be determined for variable !CDS_P_P_CONSOLIDATIONGROUP/!CDS_P_P_CONSOLIDATIONRPTGRUL</strong> raised?<br/>1.5. In the <em>Map FS Items with G/L Accounts</em> app, when trying to filter by an FS item in a mapping revision, why is the the error <strong>Parameter FSITEMMAPPINGUUID not set.</strong> raised?<br/>1.6. Can financial data with different ledgers be consolidated for a same group?<br/>1.7. Can I release reported financial data from the universal journal for a prior period?<br/>1.8. Can I maintain the <strong>Source for Group Currency Key Figure</strong> for a consolidation unit?<strong> <br/></strong>1.9. Why are my settings of <strong>Source for Local Currency Key Figure </strong>and <strong>Source for Group Currency Key Figure</strong> for a consolidation unit/group not applied to planning versions?<br/>1.10. Can I delete the data released from universal journal?<br/>1.11. When trying to run flexible upload, why do I get the error message:<strong> No. GK 357</strong>, text: “<strong>Char C/I Activity contains an invalid value (1)</strong>”?<br/>1.12. When trying to run flexible upload or balance carryforward, why do I get the warning message “<strong>Posting with value for field Investee unit to item XXXXXX is not possible</strong>”?<br/>1.13. I have an initial release SAP S/4HANA prior to 1909. Can I migrate my data from the old reporting logic to the new reporting logic?<br/>1.14. How can I connect OData service <em>API_GRTransactionData_SRV</em> to <strong>SAP Analytics Cloud</strong>?<br/>1.15. How can I set up APIs to transfer reported financial data to <strong>SAP S/4HANA Finance for group reporting</strong>?<br/>1.16. While running the elimination task, the system deletes and recreates the document numbers for the calculated amounts. Is the complete deletion of the previously created document numbers in ACDOCU, when re-running group reporting tasks, the expected behavior?<br/>1.17. How can I transport changed objects to the production system?<br/>1.18. Can I activate only parts of scope item 1SG?<br/>1.19. Can I use both solutions, EC-CS and SAP S/4HANA Finance for group reporting, at the same time in the same client of the same system?<br/>1.20. Can I change the pre-delivered hierarchies, for example, hierachy type FS item?<br/>1.21. Can I reactivate scope item <em>1SG</em> after a release upgrade from 1809 to 1909?<br/>1.22. Why are investment percentages on the investment FS item not carried forward?<br/>1.23. What do I have to take into consideration, if I use flexible upload for additional control data for activity-based Consolidation of Investment (C/I)?<br/>1.24. Can I upload an entry with the fields <strong>RACCT/GLAccount </strong>and <strong>ROBUKRS </strong>using flexible upload?<br/>1.25. Can I use consolidation versions 100 and 200 in SAP S/4HANA Finance for group reporting?<br/>1.26. I have not installed the Best Practice content. Which configuration steps of the consolidation of investments are required when I do not use the activity-based consolidation of investments?<br/>1.27. <strong>Data transfer method and Balance carry forward (Release OP2020)</strong>: I need to change the data transfer method to read universal journals from accounting. When shall I do this change?<br/>1.28. What do I have to consider, when I use Equity Pickup?</p>\n<p>1.29. How can I use SAP Group Reporting Data Collection together with SAP S/4HANA Group Reporting?</p>\n<p>1.30. How can I use custom tasks to perform elimination postings for intra-company transactions?</p>\n<p>1.31. How can I recover historical data from SAP Financial Consolidation to generate the first consolidated opening balance in Group Reporting?</p>\n<p>1.32. How can I process a consolidation method change in Group Reporting?</p>\n<p><strong>2. Validation</strong></p>\n<p>2.1. In validation apps or tasks, why are the numeric or alphanumeric values not included in amount calculation for some fields, for example, when a range of FS items was specified so that the system aggregates their values, but the sum is somehow incomplete?<br/>2.2. When running validation tasks from <em>Data Monitor </em>or <em>Consolidation Monitor </em>or directly from the <em>Manage Data Validation Tasks</em> app, why is the error <strong>Object VEC_RUN_UCCS_VALIDATION of class RE and language EN does not exist </strong>raised?<br/>2.3 After upgrading to S/4HANA 2020 FPS01, I run into system dump error when trying to run validation tasks from <strong>Data Monitor</strong> or <strong>Consolidation Monitor</strong> or directly from the <strong>Manage Data Validation Tasks</strong>.<br/>2.4 When executing a validation task, I run into a system dump error. How to solve it?<br/><a href=\"#q25\" target=\"_self\">2.5 I have big number of validation rules, how to optimize system performance?</a></p>\n<p><strong>3. Analytics </strong><br/>3.1. Why does the <em>Consolidated Balance Sheet </em>app show no data, even though the <em>Integration of transaction data into consolidation group </em>task has been executed in the <em>Consolidation Monitor</em>?<br/>3.2. Why do the balance sheet and profit and loss (P&amp;L) statements (by nature of expense) show irrelevant Financial Statement (FS) items under hierarchy node <strong>REST_H (Unassigned FS Items</strong><em><strong>)</strong>?<br/></em>3.3. How can I create custom analytical queries in <strong>SAP S/4HANA</strong> 1809 for group reporting, for example, to include a quarter-to-date (QTD) reporting measure?<br/>3.4. Why can’t I find the <strong>Local Reports</strong>, after upgrading from <strong>SAP S/4HANA Finance for group reporting</strong> 1809 to 1909?<br/>3.5. In the <strong>Interunit Reconciliation </strong>apps, why the leading unit and consolidation unit 2 I specified in <strong>Prompts</strong> are not always displayed respectively as the “CU1” and “CU2”, i.e. sometimes reversed as “CU2” and “CU1”?<br/>3.6. In <strong>Prompts</strong> of the <strong>Interunit Reconciliation - Group View</strong> app, why cannot I find my desired consolidation unit hierarchy in the <strong>Hierarchy</strong> value help?<br/>3.7. Why do I receive an error message in the <strong>SAP Analytics Cloud</strong> apps when the default parameter values are selected and how do I solve the problem?<br/>3.8. When defining reporting rules, how can I have a subitem category display only data record with blank subitems?<br/>3.9. How can I create a custom analytic query with QTD measure?<br/>3.10. Why are FS item medium texts/descriptions displayed in Analytics, even though the FS Item long texts/descriptions have been selected? Why are FS item short texts/descriptions displayed in Analytics, even though the FS Item medium texts/descriptions have been selected?   <br/>3.11. Why do I receive the error message \"No value could be determined for variable !CDS_P_P_FISCALYEAR\" in the <em>Group Data Analysis</em> app?</p>\n<p>3.12. How can I integrate SAP Analytics Cloud for planning with SAP S/4HANA for group reporting?</p>\n<p>3.13. Which apps are based on the <span>new</span> reporting logic?</p>\n<p>3.14. Which apps are based on the old reporting logic and what does this mean?</p>\n<p>3.15. Which CDS should I use for an analytical query?</p>\n<p><strong>4. Monitor Tasks<br/></strong>4.1. Why does the <em>Release Universal Journals </em>task have the <strong>Irrelevant</strong> status for a consolidation unit or a consolidation group even though the data transfer method is set to <strong>Read</strong><strong> from Universal Journals</strong> for the consolidation unit or all consolidation units within that consolidation group?<br/>4.2. When running the <em>Release Universal Journal</em> task in the<em> Data Monitor,</em> how is the trading partner treated in group reporting?<br/>4.3. In currency translation, is it possible to translate based on the posting date field by accessing the exchange rate table with the posting date, and therefore achieve a currency translation based on the date?<br/>4.4. Why is the status in the task log not updated when running the validation task in the Data or Consolidation Monitor?<br/>4.5. In which period does the balance carryforward task appear in the Data Monitor?</p>\n<p><strong>5. Data Volume and Data Granularity</strong></p>\n<p>5.1. How can I influence the volume and granularity of data in ACDOCU and avoid unnecessary information in Group Reporting, and thus having better performance and hardware resource consumptions?</p>\n<p><strong>6. Usage of Group Reporting Preparation Ledger (GRPL)</strong></p>\n<p>This is only valid when you activated scope item 1SG before cloud version CE 2208 and GRPL is not active by default.</p>\n<p>6.1. What needs to be considered when planning to activate Group Reporting Preparation Ledger and where do I find the latest information?</p>\n<p>6.2. What is the best practice for the transition, especially for customers who have already data in Accounting.</p>\n<p><strong>1. General</strong></p>\n<p><strong>1</strong><strong>.1. What are the important prerequisites for implementing Group Reporting in SAP S/4HANA?</strong></p>\n<p>To use <strong>SAP S/4HANA Finance for group reporting 1909 or later</strong>, it is not mandatory but recommended that you install the SAP Best Practices (BP) configuration content (scope item 1SG) for the solution. You will gain a configuration starting point for <strong>SAP S/4HANA Finance for group reporting</strong> which you can adapt to your needs in customizing. For more information on installing the BP content, see the <a href=\"https://help.sap.com/viewer/S4HANA1909_AdminGuide/17d958a88d244ee293aed687f9bfe37f.html\" target=\"_blank\">administration guide</a> on SAP Help Portal.</p>\n<p><strong>Note:</strong></p>\n<p>The administration guide covers more than 200 scope items where 1SG is one of them. The guide assumes customers either use the configuration content of client 000 or install the BP content for their solution. But Group Reporting can be a mixed scenario, that is, customers can install Group Reporting content in a productive client on top of the client 000 configuration content (with one condition that EC-CS configuration content doesn't exist in your system <strong>and there’s no special ledger Y1/Y2 in your system</strong> as EC-CS and 1SG content have conflicts to be resolved).This is supported. However, this has to be done by strictly following the procedure described in the attached file <em>How To Install Best Practices Content 1SG_1909_V4.pdf</em>.</p>\n<p><br/>If you don’t want to install the BP content, you can run an initialization program to create the minimum system settings that are needed to use the solution and which are not accessible in customizing. These settings include, for example, defining the dimension Y1 and defining financial statement item attributes. To start the initialization program, go to <em>Customizing for SAP S/4HANA for Group Reporting</em> under <em>Global Settings for Consolidation</em> -&gt; <em>Initialize Settings</em>.</p>\n<p><em>Note that if you are installing the</em><em> scope item XX_1SG_OP for Group Reporting before installing the FI Accounting Best Practices content <em><em>(scope item J58, building block J02)</em></em>, an error shows up <strong>Entry YCOA does not exist in T004 (check entry</strong>). </em><em>This is because that operational chart of accounts <strong>YCOA</strong> from FI Accounting Best Practices is required by Group Reporting for integration purpose. You can skip this error and continue with activation procedure.</em></p>\n<p>Note for <strong>SAP S/4HANA 1809</strong><em>,</em> <span 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" en;=\"\" lang=\"EN\" line-height:=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">for running Group Reporting it is a mandatory prerequisite to install the BP content (scope item <span arial',sans-serif;\"=\"\">1SG</span>) for the solution. </span>You can only make configuration settings in <strong>Customizing for SAP S/4HANA for Group Reporting</strong> (IMG) after that.</p>\n<p><strong>1.2. For the flexible upload of financial data, what should the file look like?</strong></p>\n<p>Use the attached flexible upload template file and replace the sample data line with your own data. Then access the <em>Flexible Upload of Reported Financial Data</em> app, select the upload method <em>SRD1</em> (with Tab as the field separator) or <em>SRD2</em> (with semicolon as the field separator), and select the Excel file in <strong>Physical File Name</strong>. For more information, see <a href=\"https://help.sap.com/viewer/4ebf1502064b406c964b0911adfb3f01/latest/en-US/03caba7eac06470ebfd8fd769f5e2e19.html\" target=\"_blank\">Flexible Upload of Reported Financial Data</a> on SAP Help Portal.</p>\n<p>If you open the file with Microsoft Excel and enter values that start with the number \"0\", you might have to add an apostrophe before the value so that the first \"0\" does not dissapear. For example, instead of <em>00</em>, type in <em>'00</em>.</p>\n<p>Note for <strong>SAP S/4HANA on-premise 2020</strong><strong> </strong><strong>FPS 01,</strong> the Column \"ledger\" is no longer needed. Please delete this data column without any replacements.</p>\n<p>Note for <strong>SAP S/4HANA on-premise 1809</strong> and its feature packages, the <em>SRD1</em> upload method is configured incorrectly. We recommend you to use <em>SRD2</em> upload method. If you prefer to use tab as the field separator, you need to correct the <em>SRD1</em> settings by 1) Accessing transaction code <em>CXCC</em> in the back-end system; 2) Double-clicking the method <em>SRD1</em>; 3) Clicking the button<strong> Field Separator</strong> and replacing \";\" with letter ‘<strong>T</strong>’ as the field separator; 4) Pressing the <strong>ENTER</strong> key and saving your changes.</p>\n<p>Do <strong><span>not</span></strong> create your own upload method. Other upload methods than <em>SRD1</em> and <em>SRD2</em> are not supported. Do not change other settings than described above, for example, the filed separator.</p>\n<p>If you run the initialization program and do not install SAP Best Practices content, you need to customize the upload method in the <strong>Upload Methods</strong> transaction (CXCC). The setting for the upload method should look like the screenshot attached (UploadMethod.png) (either chose tab or semicolon as the field separator). The field catalog (the fields that need to be filled in the upload file in the data records) is determined in the upload file.</p>\n<p><span><strong>1.3. Where are the documentation and guides available to implement Group Reporting?</strong></span></p>\n<ul>\n<li><span><strong>Product documentation: </strong><span>https://help.sap.com/S4_OP_GR</span>. (The latest product release is shown by default. You can switch to your product release.)</span></li>\n<li><strong>SAP S/4HANA On-Premise 1809</strong> (FPS00, FPS01, and FPS02):</li>\n<ul>\n<li><em><strong>How to Create New Consolidation COA and Replaces FS Items_1809.pdf</strong>: </em>Attached to this SAP Note. </li>\n<li><strong>Implementation Guide for 1809</strong><em><em>: </em></em>Attached to this SAP Note, <em>Implementation Guide for Group Reporting_1809.pdf</em>, or to <strong>SAP S/4HANA Finance for group reporting</strong> JAM group: <a href=\"https://jam4.sapjam.com/groups/aSaYjAZtSw9dfThvHcL2KV/documents/kVRlIjOs7QjoF3sUUlhUIs/slide_viewer\" target=\"_blank\">https://jam4.sapjam.com/groups/aSaYjAZtSw9dfThvHcL2KV/documents/kVRlIjOs7QjoF3sUUlhUIs/slide_viewer</a>. You may need to request access.</li>\n</ul>\n<li><strong>SAP S/4HANA On-Premise </strong><strong>1909 </strong>(FPS00):</li>\n<ul>\n<li><em><strong>How to Create New Consolidation COA and Replaces FS Items_1909.pdf</strong>: </em>Attached to this SAP Note. </li>\n<li><em><strong>SAP Best Practices content lists for scope item 1SG</strong>:</em> Attached to this SAP Note, <em>Configuration Content_1909_V5.xlsx</em>. It replaces the implementation guide and explains which settings are automatically implemented when activating 1SG. </li>\n</ul>\n<li><strong>SAP S/4HANA On-Premise 2021 </strong>(SAPK-106):</li>\n<ul>\n<li>\n<p>SAP note <a href=\"/notes/3219651\" target=\"_blank\">3219651</a><a href=\"/notes/%value\" target=\"_blank\"><strong> - </strong></a>Group Reporting in OP 2021</p>\n</li>\n</ul>\n</ul>\n<p><span><strong>1.4. When opening the <em>Interunit Reconciliation - Group View</em> app, why is the error <em>No value could be determined for variable !CDS_P_P_CONSOLIDATIONGROUP<strong>/!CDS_P_P_CONSOLIDATIONRPTGRUL</strong></em> raised?</strong></span></p>\n<ul>\n<li><span>The error for <strong><em>!CDS_P_P_CONSOLIDATIONGROUP </em></strong>occurs because no consolidation group is selected in the global parameters. This problem can be solved by specifying a consolidation group using the <strong>Set Global Parameters</strong> app.</span></li>\n<li><span><span>The error for <strong><em><strong>!CDS_P_P_CONSOLIDATIONRPTGRUL</strong></em></strong><strong><em> </em></strong>occurs due to the missing settings for reporting rules. Interunit reconciliation is a rule-based report. As such, you must first define FS item hierarchies, define reporting rules, and assign rules to versions. </span></span><span>For more information, see <a href=\"https://help.sap.com/viewer/4ebf1502064b406c964b0911adfb3f01/1909.000/en-US/3d9050fc32494dbf9a3cc2c1a70b6e47.html\" target=\"_blank\">https://help.sap.com/viewer/4ebf1502064b406c964b0911adfb3f01/1909.000/en-US/3d9050fc32494dbf9a3cc2c1a70b6e47.html</a>. </span></li>\n</ul>\n<p><span><strong>1.5. In the <em>Map FS Items with G/L Accounts</em> app, when trying to filter by an FS item in a mapping revision, the error <em>Parameter FSITEMMAPPINGUUID not set.</em> is raised.</strong></span></p>\n<p><span>You can use the following workaround for the filter function: 1) C</span>lose the pop-up error message and go back to the revision list screen; 2) Open the revision detail page again; 3) Clear the filter that was set before and stored by system automatically and choose <strong>OK</strong>; 4) Set the filter again and then choose <strong>OK</strong>.</p>\n<p><strong>1.6. Can financial data with different ledgers be consolidated for a same group?</strong></p>\n<p>For customers with an initial release <strong>SAP S/4HANA on-premise 1809 FPS02 </strong>or lower, a consolidation group hierarchy can have multiple subgroup hierarchies. It is required to have a unique consolidation ledger in all groups and subgroups within a hierarchy and consolidation version. If you need to consolidate with different ledgers for a group, parallel hierarchies must be created where all groups in each hierarchy still have a unique ledger.</p>\n<p class=\"elementtoproof\">If the requirement to have a unique ledger/version setting was disregarded in release S/4HANA 1809 (with the \"old\" group reporting logic) or in release S/4HANA 1909 (with the \"new\" group reporting logic) and you <em>plan </em>to upgrade to a higher release, you <strong>must</strong> proceed as described in SAP Note <a href=\"/notes/2941945\" target=\"_blank\">2941945</a> <strong>before performing the upgrade. </strong>You have to:</p>\n<ol><ol>\n<li>Implement the code correction as described in SAP Note <a href=\"/notes/2941945\" target=\"_blank\">2941945</a>.</li>\n<li>Run the FINCS_CHECK_LEDGER report.</li>\n</ol></ol>\n<p class=\"elementtoproof\">3. If there are inconsistencies revealed by FINCS_CHECK_LEDGER in the master data regarding ledger/version, rectify the ledger assignment in the master data of the consolidation groups (t-code CX1Q) <strong><span><span>before performing the upgrade</span>.</span></strong></p>\n<p class=\"elementtoproof\">Existing year-dependent assignments of ledger to consolidation group(s) can be listed based on database table TF184.</p>\n<p class=\"elementtoproof\">4. If you have disregarded this information for any reason <em>before </em>performing the upgrade, an error message is displayed in the <strong>Data Monitor</strong> app after the upgrade, which points out the inconsistent ledger/version assignment.</p>\n<p class=\"elementtoproof\">If this happens, please open a customer case on component FIN-CS-COR. Such a case represents a special <strong><span>service request</span></strong>, which is processed by SAP Support for Group Reporting on priority <strong>MEDIUM</strong>.</p>\n<p><strong>1.7. Can I release reported financial data from the universal journal for a prior period?</strong></p>\n<p>You can only release data from the accounting period that corresponds to the consolidation period in which the release task is executed, provided that the accounting period is still open. The accounting data from a prior period can only be displayed in the \"<strong>Release Universal Journals</strong>\" task on tab \"<strong>Data from Prior Periods Without Release</strong>\". In this case, for Group Reporting, the missing data can be posted manually to a later period that is still open in Data Monitor by using the <strong>Post Group Journal Entries </strong>app. .</p>\n<p><strong>1.8. Can I maintain the Source for Group Currency Key Figure<em> </em>for a consolidation unit?</strong></p>\n<p>When maintaining master data of a consolidation unit by using the <em>Consolidation Units - Change</em> app, you can specify its sender group currency by choosing <strong>Go to</strong> -&gt; <strong>Fiscal Year Variant</strong> -&gt; <strong>Source for Group Currency Key Figure</strong>.</p>\n<p>Note that setting the source for group currency key figure for a consolidation unit is only relevant if the group currency of the consolidation ledger (e.g. Y1 or Y2 as SAP predefines) is same as the global currency of the reference ledger (e.g. 0L as SAP predefines). For example, if the reference ledger has the global currency USD while the consolidation ledger has the group currency EUR, the sender group currency must not be maintained.</p>\n<p><strong>1.9. Why are my settings of <em>Source for Local Currency Key Figure</em> and <em>Source for Group Currency Key Figure </em>for a consolidation unit/group not applied to planning versions? </strong></p>\n<p>The <strong>Source for Local Currency Key Figure </strong>and <strong>Source for Group Currency Key Figure</strong> settings are only applicable to regular versions that are not marked as <strong>Version for Plan Data</strong>. For planning versions, the system always takes fields <em>HSL (Amount in Company Code Currency)</em> or <em>KSL (Amount in Global Currency)</em> from the plan data line items as long as one of those fields has a currency that matches the local currency of the consolidation unit (for field <em>HSL</em>) or the ledger group currency (for field <em>KSL</em>). This means, the ledger currency setting in the backend transaction FINSC_LEDGER is considered.</p>\n<p><strong>1.10 Can I delete the data released from universal journal?</strong></p>\n<p>This is not supported. However, for testing purpose or in exceptional case, you can use this workaround for deletion of released data:</p>\n<ol>\n<li>Ensure that the <em>Release Universal Journals</em> task is not executed for the same consolidation unit by anyone else.</li>\n<li>Delete the current assignment of FS item mappings in the <em>Assign FS Item Mappings </em>app. A warning displays that the mapping may already be used in transactional data. This warning needs to be accepted.</li>\n<li>Release the data again for the respective consolidation unit in <em>Data Monitor</em>. Make sure you execute this step only for the desired consolidation unit and period.</li>\n<li>Create the FS item mapping assignment that you deleted in step 1 again in the <em>Assign FS Item Mappings </em>app. </li>\n</ol>\n<p>However, this workaround affects <strong>all</strong> consolidation units, therefore, you must be very careful to release data only for the consolidation units for which you want to reset the earlier released data.</p>\n<p><strong>1.11 When trying to run flexible upload, why do I get the error message: No. GK 357, text: “Char C/I Activity contains an invalid value (1)”?</strong></p>\n<p>For consolidation of investments (C/I)-related information, you need to add leading zeros to the values (for example <strong>‘01</strong> instead of 1 or 01. If you type an apostrophe (<strong>'</strong>) in front of the number, Excel will treat it as a text).</p>\n<p><strong>1.12 When trying to run flexible upload or balance carryforward, why do I get the warning message No. G01 036: “Posting with value for field Investee unit to item XXXXXX is not possible”?</strong></p>\n<p>Due to inconsistency in the pre-delivered selections, for customers having SAP Best Practices content installed on SAP S/4HANA 1909, the following steps are required:</p>\n<ol>\n<li>Open the <strong>Import/Export Validation Settings</strong> app and choose <strong>Upload </strong>to upload the file <em>Validation Rules and Methods for NCI Target.xlsx</em> (attached in this SAP Note)</li>\n<li>You can find the duplicate selections in the <strong>Duplicated</strong> tab, choose the <strong>Import </strong>button.</li>\n<li>In the <strong>Imported</strong> tab, find the duplicate selections and choose the <strong>Mass Activation</strong> button.</li>\n</ol>\n<p><strong>1.13 I have an initial release SAP S/4HANA prior to 1909. Can I migrate my data from the old reporting logic to the new reporting logic?</strong></p>\n<p>After you have run an upgrade from 1809 to 1909 or higher you can use the new reporting logic.</p>\n<p>Yes, legacy data can be migrated to the new reporting logic with the help of SAP. Please create a customer ticket under the component <strong>FIN-CS-COR</strong>. SAP Support for Group Reporting then performs the requested system conversion from \"old\" to \"new\" group reporting logic in the context of the customer case with priority <span><strong>MEDIUM.</strong></span> After the migration, these old apps will not be applicable to you:</p>\n<ul>\n<li><em>Consolidation Group Hierarchy - Display and Change</em></li>\n<li><em>Accounting Method Assignment - Display and Change</em></li>\n<li><em>Interunit Reconciliation - Group View</em></li>\n<li>All reports under the Group Reports except <em>Group Data Analysis</em> and <em>Group Data Analysis - With Reporting Rules</em></li>\n</ul>\n<p>You will need to use the following new apps instead:</p>\n<ul>\n<li><em>Manage Consolidation Group Structure - Group View</em></li>\n<li><em>Manage Consolidation Group Structure - Unit View</em></li>\n<li><em>Group Data Analysis</em></li>\n<li><em>Group Data Analysis - With Reporting Rules</em></li>\n</ul>\n<p>Note that data is only migrated as of the year you have specified for the migration. The migration of data is done during the balance carry forward execution into this year. Consequently, consolidated data before this year is not migrated, and you need to continue to use the prior group reports (i.e not <em>Group Data Analysis</em> nor <em>Group Data Analysis - With Reporting Rules</em>) to retrieve this data. For subsequent years, you need to use the new group reports (i.e <em>Group Data Analysis and Group Data Analysis - With Reporting Rules</em>). Please, do not use the <em>Data Analysis</em> app as it does not support the new group reporting and therefore only shows consolidation groups from the old group reporting logic.</p>\n<p>With the new group reporting logic, there is a flat list of consolidation groups instead of the consolidation group hierarchies. Please see the Knowledge Base Article (KBA) 2946165 for further details.</p>\n<p>After a system conversion from \"old\" to \"new\" reporting logic, the group-dependent (manual) postings are not automatically inherited to higher-level consolidation groups. Thus, manual postings on level 30 need to be entered on each higher-level consolidation group separately.</p>\n<p>For customers with an initial release 1909 or higher, the new reporting logic is automatically activated. The new apps should be used, while the old apps are inaccessible.</p>\n<p><strong>Caution: </strong>After system conversion you should not change the group structure for a period BEFORE the change from old to new reporting logic. The first period for including a consolidation unit into the group structure is the first period of the new reporting logic.</p>\n<p><strong>1.14 How can I connect OData service API_GRTransactionData_SRV to SAP Analytics Cloud?</strong></p>\n<p>See SAP Knowledge Base Article <a href=\"/notes/2814835\" target=\"_blank\">2814835</a>.</p>\n<p><strong>1.15 How can I set up APIs to transfer reported financial data to SAP S/4HANA Finance for group reporting?</strong></p>\n<p>To set up APIs, see the set-up guide attached to the SAP Note (<em>How to Set Up APIs for Group Reporting_V1.pdf</em>).</p>\n<p><strong>1.16 While running the elimination task, the system deletes and recreates the document numbers for the calculated amounts. Is the complete deletion of the previously created document numbers in ACDOCU, when re-running group reporting tasks, the expected behavior?</strong></p>\n<p>Yes, <span>up to release S/4HANA 2021</span>, reclassifications post a brand new document each time and delete the earlier posted one.</p>\n<p><strong>1.17 How can I transport changed objects to the production system?</strong></p>\n<p>If you make configuration setting changes in the Implementation Guide (IMG), these changes need to be transported to the production system. For more information on the IMG, see <a href=\"https://help.sap.com/viewer/4ebf1502064b406c964b0911adfb3f01/1809.002/en-US/cf395fcc2b13467ab2c10057a16717c7.html\" target=\"_blank\">Making Configuration Setting</a>.</p>\n<p>Note: For a complete transport, SAP Note <a href=\"/notes/2735532\" target=\"_blank\">2735532</a> (valid for release 1809 only) that explains how to enable transporting the configuration settings for Define Selections, needs to be installed.</p>\n<p>Settings in SAP Fiori apps that are related to <em>Master Data</em> or <em>Consolidation Settings</em> need to be maintained in the production system. For more information, see <a href=\"https://help.sap.com/viewer/DRAFT/4ebf1502064b406c964b0911adfb3f01/1909.001/en-US/e609d9c04dcc4d0b82006e0bf4ac57c7.html\" target=\"_blank\">Master Data</a> and <a href=\"https://help.sap.com/viewer/DRAFT/4ebf1502064b406c964b0911adfb3f01/1909.001/en-US/360bf9c6554a4f058d934f76fcf8f9ad.html\" target=\"_blank\">Configuration Settings</a>. Only settings that are made in the <em>Assign Validation Method</em> app can be transported. To transport these settings completely, you need to install SAP Notes <a href=\"/notes/2777942\" target=\"_blank\">2777942</a> and <a href=\"/notes/2705560\" target=\"_blank\">2705560</a> (valid for release 1809 only).</p>\n<p>To import selections that were created in the <em>Define Selections</em> app in another system on release 1909, use the export and import feature in the <em>Import/Export Validation Settings</em> app.</p>\n<p><strong>1.18 Can I activate only parts of scope item 1SG?</strong></p>\n<p>No, when you activate scope item 1SG, all settings described in the attachment <em>Configuration Content_1909_V5.xlsx</em> are activated.</p>\n<p><strong>1.19 Can I use both solutions, EC-CS and SAP S/4HANA Finance for group reporting, at the same time in the same client of the same system?</strong></p>\n<p>For information on using both solutions at the same time (“hybrid system state”) and transition scenarios from EC-CS to SAP S/4HANA Finance for group reporting, see SAP Note <a href=\"/notes/2833748%20\" target=\"_blank\">2833748</a>.</p>\n<p><strong>1.20 Can I change the pre-delivered hierarchies, for example, hierachy type FS item?</strong></p>\n<p>No, the pre-delivered content should not be changed and adjusted. For any changes, please copy the content and adjust the copied hierarchy.</p>\n<p><strong>1.21 Can I reactivate scope item 1SG after a release upgrade?</strong></p>\n<p>No, do not reactivate scope item 1SG when you upgrade your system from lower release to a new release. The upgrade is only a technical system upgrade. A content lifecycle compatible upgrade in an On Premise system is not supported in the <strong>SAP Best Practices Solution Builder</strong>. To implement <strong>SAP S/4HANA</strong> <strong>Finance for group reporting</strong> with <strong>SAP Best Practices</strong>, see the <a href=\"https://help.sap.com/viewer/S4HANA1909_AdminGuide/17d958a88d244ee293aed687f9bfe37f.html\" target=\"_blank\">administration guide</a> on SAP Help Portal.</p>\n<p><strong>1.22 Why are investment percentages on the investment FS item not carried forward?</strong></p>\n<p>To carry forward investment percentages, the breakdown category assigned to the investment FS item must have a breakdown for <strong>Unit of Measure</strong>, just like the pre-delivered example for breakdown category<em> 1D20</em>.</p>\n<p><strong>1.23 What do I have to take into consideration, if I use flexible upload for additional control data for activity-based Consolidation of Investment (C/I)?</strong></p>\n<p>Already loaded data without control data needs to be added into the file with reversed sign (to delete the earlier loaded data) and then added with expected sign and the control data. Only a single document type may be used for flexible upload. Below there is an example:</p>\n<p>0) You loaded item 123 without control data with the amount 10</p>\n<p>1) Now you want to upload additional control data for item 123.</p>\n<p>To do so you have to upload the file with overwrite mode:</p>\n<p>- 123    -10</p>\n<p>- 123     10  and additional control data</p>\n<p><strong>1.24. Can I upload an entry with field <em>RACCT/GLAccount</em> using flexible upload?</strong></p>\n<p>Yes, this is possible. To do so, you also need to provide the operational chart of accounts, the fields in the <strong>Define Consolidation Master Data Fields</strong> configuration step have to be input enabled, and the master data needs to be maintained for both fields.</p>\n<p>However, it is not supported to use flexible upload with a value in the field <strong>Original Company Code - ROBUKRS</strong> in the upload file. The structure <strong>FC03_S_UPL04_S4H</strong> does not have the original company code field <strong>ROBUKRS.</strong></p>\n<p><strong>1.25. Can I use consolidation versions 100 and 200 in SAP S/4HANA Finance for group reporting?</strong></p>\n<p>No, consolidation versions 100 and 200 originate from EC-CS and must not be used in SAP S/4HANA when starting with a new setup.</p>\n<p><strong>1.26. I have not installed the Best Practice content. Which configuration steps of the consolidation of investments are required when I do not use the activity-based consolidation of investments?</strong></p>\n<p>The following settings are required by the <em>Preparation for Changes in the Consolidation Group</em> task and the <em>Post Group Shares</em> task:</p>\n<p>- Specify the FS items for the <em>Net Income Before First Cons.</em> <em></em></p>\n<p>- Specify the FS items for the <em>Clearing consolidation of investments.</em></p>\n<p><em>-</em> Specify the selection for Investments</p>\n<p>You can specify these settings using the activity <em>Specify Miscellaneous Selected Items </em>under <em>Consolidation of Investments </em>in the Implementation Guide.</p>\n<p><strong>1.27. <strong>Data transfer method and Balance carry forward (Release OP2020): </strong>I need to change the data transfer method to read universal journals from accounting. When shall I do this change?</strong></p>\n<p>You should do this change on the first period of the year, otherwise data released from accounting will not be carried forward.</p>\n<p><strong>1.28. What do I have to consider, when I use Equity Pickup?</strong></p>\n<p>For more information on Equity Pickup, please see the document \"How To Configure Equity Pickup\" in the attachments.</p>\n<p><strong>1.29.  How can I use SAP Group Reporting Data Collection in SAP S/4HANA Group Reporting?</strong></p>\n<p>From OP 2020 onwards please follow the information in the <a href=\"https://blogs.sap.com/2022/02/15/how-to-run-a-step-consolidation-in-sap-s-4hana-for-group-reporting-using-sap-group-reporting-data-collection/\" target=\"_blank\">How to Run a Step Consolidation in S/4HANA Group Reporting with SAP Group Reporting Data Collection</a> blog.</p>\n<p><strong>1.30. How can I use custom tasks to perform elimination postings for intra-company transactions?</strong></p>\n<p>For more information, please see note <a href=\"/notes/3279409\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/3279409</a>.</p>\n<p><strong>1.31. How can I recover historical data from SAP Financial Consolidation to generate the first consolidated opening balance in Group Reporting?</strong></p>\n<p>To recover historical data from SAP Financial Consolidation in the case of transferring the last closing to generate the first consolidated opening balance in Group Reporting, see the attachment <em>How to Guide - Recovery of Historical Data from FC to GR.pdf </em>in the attachments of this SAP Note.</p>\n<p><strong>1.32. How can I process a consolidation method change in Group Reporting??</strong></p>\n<p>To understand how to process consolidation method changes (from equity to purchase method or from purchase to equity method) or if you want to build a set of data to demonstrate a method change in Group Reporting, see the how-to guide attached to this SAP Note: How to Handle Method Change with Group Reporting Rule-Based Consolidation of Investments_OP.zip.</p>\n<p><strong>Validation</strong></p>\n<p><strong>2.1. In validation apps or tasks, why are the numeric or alphanumeric values not included in amount calculation for some fields, for example, when a range of FS items was specified so that the system aggregates their values, but the sum is somehow incomplete?</strong></p>\n<ul>\n<li>For <strong>S/4HANA OP 1809 </strong>and all its feature packages, the ALPHA conversion routine needs to be considered for the relevant fields when defining validation rules. See the following guidelines:</li>\n<ul>\n<li>To include pure numeric values, you need to add all necessary leading zeroes before your keyword, followed by a wildcard (*).</li>\n<li>To include alphanumeric values that are not preceded with leading zeroes, do not add leading zeroes.</li>\n</ul>\n</ul>\n<p><span>Example:</span> If you want to include both numeric values <em>such as 123456 and</em> alphanumeric values such as<em> 1A3456 </em>for <strong>FinancialStatementItem </strong>that start with 1, use two WHERE condition rows: 00001* and 1*, such as:</p>\n<ul>\n<ul>\n<ul>\n<li><em>FinancialStatementItem Like '00001*'</em></li>\n<li><em>FinancialStatementItem Like '1*'</em></li>\n</ul>\n</ul>\n<li>Starting from <strong>S/4HANA OP 1909 FPS00</strong>, you can use one condition row <em>FinancialStatementItem Like '1*</em>' to include numberic and alphanumeric values that start with 1. However, adding leading zeroes, for example 0001*, returns only <strong>alphanumeric</strong> values with the exact number of leading zeroes you entered. Therefore, SAP recommends you remove leading zeroes to return complete search results.</li>\n</ul>\n<p><strong>2.2. When running validation tasks from <em>Data Monitor </em>or <em>Consolidation Monitor </em>or directly from the <em>Manage Data Validation Tasks</em>, why is the error <em>Object VEC_RUN_UCCS_VALIDATION of class RE and language EN does not exist</em> raised?</strong></p>\n<p>This is because the predefined/imported validation rules and methods are not yet activated in test/production system. The issue can be solved by activating the validation rules/methods via the<em> Import/Export Validation Settings</em> app (or <em>Import and Export Validation Rules and Methods</em> app or transaction code<em> VECIE </em>for releases lower than S/4HANA 1909 FPS00), e.g. using mass activation to activate all rules/methods at once.</p>\n<p><strong>2.3. After upgrading to S/4HANA 2020 FPS01, I run into system dump error when trying to run validation tasks from <em>Data Monitor </em>or <em>Consolidation Monitor </em>or directly from the <em>Manage Data Validation Tasks.</em></strong></p>\n<p>This is because multiple group-by fields of validation rules are introduced with S/4HANA 2020 FPS01, which brings incompatible changes. The issue can be solved by activating all your existing validation rules using the <strong>Schedule Mass Activation Jobs - Rules, Methods, and Selections</strong> app.</p>\n<p><strong>2.4 When executing a validation task, I run into a system dump error. How to solve it?</strong></p>\n<p>This may be caused by changes of the configuration content. To solve the issue, you can activate the relevant validation method by using the <strong>Schedule Mass Activation Jobs - Rules, Methods, and Selections</strong> app. In the app, you create an immediate job run for activating the method.</p>\n<p><strong><a name=\"q25\" target=\"_blank\"></a>﻿2.5 I have big number of validation rules, how to optimize system performance?</strong></p>\n<p> To process large volume of data with many validation rules, note the following:</p>\n<ul>\n<li>Keep rules in each method and operands in each rule to a necessary minimum.</li>\n<li>When defining filter conditions in a rule, consider using direct input values instead of field attributes and hierarchies that cost more system memory. </li>\n<li>If similar rules apply to a same dimension (characteristic), consider using \"group-by\" fields in rule definition.</li>\n</ul>\n<p><span>Additionally, depending on your product version, you may need to apply SAP Note </span><a href=\"/notes/3082123\" target=\"_blank\">3082123</a> to support up to 3000 validation rules in one method and improve system performance.</p>\n<p><strong>3. Analytics</strong></p>\n<p><strong>3.1. Why does the <em>Consolidated Balance Sheet </em>app show no data, even though the <em>Integration of transaction data into consolidation group task </em>has been executed in the <em>Consolidation Monitor</em>?</strong></p>\n<p>The global parameters have to be maintained (via the <em>Set Global Parameters </em>app) as Ledger needs to be derived from the global parameters when executing the report. The global parameters are user-specific, i.e. are to be maintained by each end user who executes the report. The corresponding ledger of a consolidation group can be viewed in the <em>Consolidation Group Hierarchy - Display</em> app.<br/><br/></p>\n<p><strong>3.2. Why do the balance sheet and profit and loss (P&amp;L) statements (by nature of expense) show irrelevant Financial Statement (FS) items under hierarchy node <strong>REST_H (Unassigned FS Items</strong><em><strong>)</strong>?</em></strong></p>\n<p>Under the <strong>REST_H</strong> hierarchy node of the <em>Balance Sheet / P&amp;L by Nature of Expense </em>analytical apps, the FS items that are not assigned to your specified <strong>Item Hierarchy </strong>(for BS or P&amp;L statements) in the <strong><strong>Prompts</strong></strong> window are displayed by default.</p>\n<p>To suppress the unassigned FS item nodes in the report display, you need to either (1) Specify the top node of the selected FS item hierarchy in <strong>FS Item (Node) </strong>field in the <strong><strong>Prompts</strong></strong>, or (2) Within the report display, choose \"<strong>Filter</strong> -&gt; <strong>Filter Member</strong>\" from the context menu (right-click) of the FS item node and restrict to the right FS item node (for BS or P&amp;L statements).</p>\n<p><strong>3.3 How can I create custom analytical queries in SAP S/4HANA 1809 for group reporting, for example, to include a quarter-to-date (QTD) reporting measure?</strong></p>\n<p>See the attached how-to guide <em>How to Create a Custom Analytical Query with QTD Measure_1809_V1.pdf</em> for a step-by-step procedure.</p>\n<p><strong>3.4. Why can’t I find the Local Reports, after upgrading from SAP S/4HANA Finance for group reporting 1809 to 1909?</strong></p>\n<p>Due to the new reporting logic, the <strong>Local Reports</strong> were removed. For more information, see SAP Note 2730981: <a href=\"/notes/2730981\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2730981 </a>.</p>\n<p><strong>3.5. In the <strong>Interunit Reconciliation </strong>apps, why the leading unit and consolidation unit 2 I specified in <strong>the Prompts</strong> are not always displayed respectively as the “CU1” and “CU2”, i.e. sometimes reversed as “CU2” and “CU1”?</strong></p>\n<p class=\"xmsonormal\">The amounts in key figures for <strong>'GC Recon CU1'</strong> / <strong>'GC Recon CU2'</strong> are not influenced by the <strong>'Leading Cons. Unit'</strong> / <strong>'Cons. Unit 2'</strong> in the <strong><strong>Prompts</strong></strong>, but are based on the transaction data records in ACDOCU. Therefore, it is suggested to follow the alphanumeric order to specify the 'Leading Cons. Unit' / Cons. Unit 2' in <strong>Prompts</strong>. For example, for a consolidation unit pair of Z1 and A1, put A1 as the leading cons. unit and Z1 as cons. unit 2 in <strong>Prompts</strong>.</p>\n<p class=\"xmsonormal\"><strong>3.6.</strong> <strong>In Prompts of the Interunit Reconciliation - Group View app, why cannot I find my desired consolidation unit hierarchy in the Hierarchy value help?</strong></p>\n<p class=\"xmsonormal\">Consolidation unit hierarchy is version-dependent. It is controlled by the ”<strong>Structure</strong>“ special version of consolidation version. Note that once you save a different structure version to a consolidation version using the transaction <strong>CXB1</strong>, you must access the transaction <strong>CX1X</strong> to save the hierarchy without making any changes. This is to trigger replication of cons. unit hierarchies for the relevant report execution.</p>\n<p><strong>3.7. Why do I receive an error message in the SAP Analytics Cloud apps when the default parameter values are selected and how do I solve the problem?</strong></p>\n<p>In <strong>SAP Analytics Cloud</strong> apps, most of the parameters are mandatory. These apps always need default values for mandatory fields, or else the stories cannot be saved.</p>\n<p>Solution: It is planned to be fixed by release 2020.</p>\n<p>Workaround:<br/>Step 1: Remove all default parameters and provide proper parameter values according to your own system.<br/>Step 2: In the top-left corner open <em>My Views</em>, and save the changes as a new view (you can also make it as default).</p>\n<p>For more information, see SAP Note <a href=\"/notes/2906664\" target=\"_blank\">290664</a>.</p>\n<p><strong>3.8. When defining reporting rules, how can I have a subitem category display only data record with blank subitems?</strong></p>\n<p>To display subitem categories with blank subitems, you need to first create a rule to display all data records. Then you need to create a second rule to display all data records that have subitem category and subitem filled. For both rules all fields should be the same except <strong>Subitem Category</strong>, <strong>Subitem From</strong>, <strong>Subitem To</strong>, and <strong>Rev.Sign</strong>. For example, in the second rule, <strong>Subitem Category</strong> could have the value 2 for functional area and the values for <strong>Subitem From</strong> and <strong>Subitem To</strong> should include all subitems, such as <strong>Subitem From</strong> = ‚<em>AAAAAAAAA</em>A‘ und <strong>Subitem To</strong> = ‚<em>ZZZZZZZZZZ</em>‘. <strong>Rev. Sign</strong> should have the value ‘X’. You then need to map the rules for the same reporting item.</p>\n<p>The resulting runtime behavior of a query using these reporting rules is that for data records of subitem category that have value 2 for functional area, only the entries with blank subitem are considered. Data records of subitem category that have value 2 and filled subitem sum up to zero in the aggregated results according to both rules.</p>\n<p><strong>3.9.  How can I create a custom analytic query with QTD measure?</strong></p>\n<p>To do so please follow the steps in the guide <em>How to Create a Custom Analytical Query with QTD Measure_1809_V1.pdf</em>, which you can find in the attachments.</p>\n<p><strong>3.10.</strong> <strong>Why are FS item medium texts/descriptions displayed in Analytics, even though the FS Item long texts/descriptions have been selected?</strong></p>\n<p><strong>or</strong></p>\n<p><strong>Why are FS item short texts/descriptions displayed in Analytics, even though the FS Item medium texts/descriptions have been selected?</strong></p>\n<p>The medium text/description of the FS item master data is offered as long text in the Analytics UIs (Design Studio, Analysis for Office). Similarly, the short text/description of the FS item master data is offered as medium text in the Analytics UIs.</p>\n<p>If the long text/description of the FS item to Analytics would be exposed to Analytics, then it would be offered as long text, and the medium text/description would be offered as medium text. However, this is an incompatible change to existing queries and user variants.</p>\n<p>As a result, the long text/description of FS items can’t be exposed to Analytics. Instead, the FS item medium texts/descriptions are displayed as long texts, and the FS item short texts/descriptions are displayed as medium texts.</p>\n<p><strong>3.11.  Why do I receive the error message \"No value could be determined for variable !CDS_P_P_FISCALYEAR\" in the <em>Group Data Analysis</em> app?</strong></p>\n<p>Before starting the <em>Group Data Analysis</em> app, you must first select values for the parameters in the <em>Set Global Parameters</em> app or alternatively in the transaction <em>CXGP. </em>Once these values are saved in the global parameters, such as <em></em><em>Fiscal Year</em>, they are then derived for the corresponding variables in the <em>Group Data Analysis</em> app.</p>\n<p><strong>3.12. How can I integrate SAP Analytics Cloud for planning with SAP S/4HANA for group reporting?</strong></p>\n<p>For more information see the blog <a href=\"https://blogs.sap.com/2022/08/02/how-to-integrate-sap-analytics-cloud-for-planning-with-sap-s-4hana-for-group-reporting/\" target=\"_blank\">How to integrate SAP Analytics Cloud for planning with SAP S/4HANA for group reporting | SAP Blogs</a></p>\n<p><strong>3.13. Which apps are based on the <span>new</span> reporting logic?</strong></p>\n<p>The following apps are based on the new reporting logic:</p>\n<ul>\n<li>W0136  Group Data Analysis - with Reporting Rules</li>\n<li>W0135  Group Data Analysis</li>\n<li>F6133   Group Financial Statement Review Booklet</li>\n<li>F6132   Review Booklet Base App</li>\n</ul>\n<p>For more information, see <a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/86b7e4024e4b4264891ce4d377ca075c.html?locale=en-US\" target=\"_blank\">Reporting Logic</a>.</p>\n<p><strong>3.14. Which apps are based on the old reporting logic and what does this mean?</strong></p>\n<p>The following apps are based on the old reporting logic:</p>\n<ul>\n<li><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/f34333b3c2f9407b8e1907648cf95e1a.html?locale=en-US\" target=\"_blank\">Consolidated Balance Sheets</a> </li>\n<li><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/da46569a2b6c43b0be77ab1ed94b2ab2.html?locale=en-US\" target=\"_blank\">Consolidated P&amp;L Statements by Nature of Expense</a> (deprecated with SAP S/4HANA 2022, to be removed with SAP S/4HANA 2023)</li>\n<li><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/8c16cad56daf4a42b7acb8f93e0788b1.html?locale=en-US\" target=\"_blank\">Data Analysis - Reporting Logic</a></li>\n<li><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/6f9ed8c88ce94ddc8884b5d0f1acfd04.html?locale=en-US\" target=\"_blank\">Rule-Based Reports</a> (deprecated with SAP S/4HANA 2022, to be removed with SAP S/4HANA 2023)</li>\n</ul>\n<p>With the introduction of a new architecture/reporting logic, these apps are only applicable to customers who have an initial release lower than SAP S/4HANA 1909 and haven't migrated to the new reporting logic. Customers with an initial release 1909 or higher should use the <a class=\"xref\" href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/b00975f83e1c46df9b15f98de88078d5.html?locale=en-US\" target=\"_blank\">Group Data Analysis</a> app instead.</p>\n<p>For more information, see <a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/86b7e4024e4b4264891ce4d377ca075c.html?locale=en-US\" target=\"_blank\">Reporting Logic</a>.</p>\n<p><strong>3.15. Which CDS View should I use for an analytical query?</strong></p>\n<p>For OP 2022 and 2023 the recommended cubes are:</p>\n<ul>\n<li><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/8308e6d301d54584a33cd04a9861bc52/26238f3caa954f3c83ccff79f959b1c1.html\" target=\"_blank\">I_CnsldtnGrpJrnlItemC</a></li>\n<li>\n<p><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/8308e6d301d54584a33cd04a9861bc52/6ddc99f15da24bd09baafd9011b48203.html\" target=\"_blank\">I_CnsldtnRuleBsdGrpJrnlItemC</a></p>\n</li>\n</ul>\n<p>For OP 2021 and 2020 the recommended cubes are:</p>\n<ul>\n<li>\n<p><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/8308e6d301d54584a33cd04a9861bc52/14e876823f684d65980c848c215461cf.html?version=2020.000\" target=\"_blank\">I_MatrixConsolidationRptEnhcdC</a></p>\n</li>\n<li><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/8308e6d301d54584a33cd04a9861bc52/06cbdbe8ccbc437cb1c67cb3521c7278.html?version=2020.000\" target=\"_blank\">I_MatrixConsolidationReportC</a></li>\n</ul>\n<p><span> </span></p>\n<p><strong>4. Monitor Tasks</strong></p>\n<p><strong><br/>4.1. Why does the <em>Release Universal Journals </em>task have the Irrelevant status for a consolidation unit or a consolidation group even though the data transfer method is set to <em>Read </em></strong><strong><em>from Universal Journals </em>for the consolidation unit or all consolidation units within that consolidation group?</strong></p>\n<p>The status <em>Task is irrelevant</em> of the release task is stored in the database based on the settings when the <em>Data Monitor</em> was called for the first time in a specific period. If the data transfer method setting is changed later on, the user needs to reset the status by choosing <strong>Tasks </strong>-&gt;<strong> Reset Status</strong> in <em>Data Monitor</em>. <br/>Note that if all units within a group have the data transfer method <strong>Flexible Upload</strong>, the release task has the status <em>Task is irrelevant</em>. <br/>Additionally, the same situation can occur for the <em>Currency Translation</em> task, that is after the local currency of a consolidation unit is changed, users need to reset the task status.</p>\n<p><strong>4.2. When running the <em>Release Universal Journal</em> task in the<em> Data Monitor,</em> how is the trading partner treated in group reporting?</strong></p>\n<p>The trading partner is taken over into the partner unit field, if the FS item has a breakdown category containing <strong>Partner Unit</strong> assigned.</p>\n<p><strong>4.3. In currency translation, is it possible to translate based on the posting date field by accessing the exchange rate table with the posting date, and therefore achieve a currency translation based on the date?</strong></p>\n<p>No, this is not possible.<strong> </strong></p>\n<p><strong>4.4. Why is the status in the task log not updated when running the validation task in the Data or Consolidation Monitor?</strong></p>\n<p>If you are working in release 1809, the status is not updated automatically, instead you need to manually refresh the status.</p>\n<p><strong>4.5. In which period does the balance carryforward task appear in the Data Monitor? </strong></p>\n<p>The balance carryforward task appears in each period in the Data Monitor as long as it hasn't been run in update mode. Once it's been run in update mode, it appears only in the period in which it was run in update mode. <br/>Balance carryforward writes data into period 000. So, if you want to check the data that was posted by balance carryforward, check period 000.</p>\n<p><strong>5. Data Volume and Data Granularity</strong></p>\n<p><strong>5.1. How can I influence the volume and granularity of data in ACDOCU and avoid unnecessary information in Group Reporting, which results in a better performance and hardware resource consumption?</strong></p>\n<p>The level of granularity comes from the Universal Journal (ACDOCA) via the Release Task and has a major impact on the performance/runtime of the Release task itself and on the data-volume being posted into ACDOCU table.</p>\n<p>The reported financial data is then the basis for all subsequent consolidation tasks such as currency translation, validation, reclassification etc. and the data volume being processed by several automatic consolidation tasks heavily influences the response time and resource consumption (memory and CPU).</p>\n<p>So, when implementing Group Reporting – as early as possible in the project – the granularity of the transferred data from ACDOCA into ACDOCU should be analyzed. If some additional characteristics are not needed by the business, these fields should not be taken over into Consolidation, to reduce data volume and hence to potentially have a better performance and lower resource consumption.</p>\n<p>For more information see the KBA Note <a href=\"/notes/3000108\" target=\"_blank\">3000108</a>.</p>\n<p><strong>6. Usage of Group Reporting Preparation Ledger (GRPL)</strong></p>\n<p><strong>6.1. What needs to be considered when planning to activate Group Reporting Preparation Ledger and where do I find the latest information?</strong></p>\n<p>As of SAP S/4HANA 2021 FSP01, it is possible to switch on a tighter integration with accounting (aka TAI, now GRPL).</p>\n<p>For preparing the activation the following information is available:</p>\n<ul>\n<li>Documentation: <a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/4ebf1502064b406c964b0911adfb3f01/2b27073df8264896993169f6ebe764c2.html?locale=en-US&amp;version=2023.001\" target=\"_blank\">Integration with Group Reporting Preparation Ledger</a> <a href=\"Integration%20with%20Group%20Reporting%20Preparation%20Ledger\" target=\"_blank\"><br/></a>\n<p>How to Guide: <a href=\"https://i7p.wdf.sap.corp/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001646532018&amp;iv_version=0180&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F4933B334B688F734378A3730B40A4AF1AACCF5B32C082E773435AD08A8C8330DB34CCA7631760C7032F0CDF64E4F35CD0828CE74F4D0750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00&amp;iv_guid=00109B36DBDE1EEE9FDB2A066EB37C1D\" target=\"_blank\">How to Integration with S4HANA Accounting Using GRPL_V3.pdf</a>, which is also attached to this note.</p>\n</li>\n</ul>\n<p>The following notes contain the latest information which is already important during the Design Phase of the project:</p>\n<ul>\n<li>Collective note <a href=\"/notes/3468806\" target=\"_blank\">3468806</a> - Preparation Ledger: Collection of notes</li>\n<li>Restriction note <a href=\"/notes/3283874\" target=\"_blank\">3283874</a> - Realign Group Reporting Preparation Ledger: restriction</li>\n<li>Troubleshooting note: <a href=\"/notes/3412034\" target=\"_blank\">3412034</a> - Troubleshooting Tips for Realignment Function (RF) or Release of Universal Journals (RUJ) in S/4HANA Group Reporting with Integration with Preparation Ledger (GRPL) active</li>\n</ul>\n<p><strong>6.2. What is the best practice for the transition, especially for customers who have already data in Accounting?</strong></p>\n<p>SAP recommends to perform a transition at the end of the year/beginning of the year. The balance carryforward in accounting could be used to initialize data records in Financial Accounting (table ACDOCA) data in period 000 for the year that the integration with group reporting preparation ledger is activated. Please refer to chapters 3 &amp; 4 in the How-to-Guide for more detailed information.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>See above.</p>\n<p 'apple=\"\" 'segoe=\"\" color=\"\" emoji',=\"\" sans-serif;\"=\"\" segoe=\"\" system-ui,=\"\" ui=\"\" ui',=\"\">Here is a note which can support big number of validation rules in one method</p>\n<p 'apple=\"\" 'segoe=\"\" color=\"\" emoji',=\"\" sans-serif;\"=\"\" segoe=\"\" system-ui,=\"\" ui=\"\" ui',=\"\">\"note: 3082123 - Fix: Validation method activation issue when a big number of validation rules are assigned \"</p>\n<p 'apple=\"\" 'segoe=\"\" color=\"\" emoji',=\"\" sans-serif;\"=\"\" segoe=\"\" system-ui,=\"\" ui=\"\" ui',=\"\">• If there is any limitation for the number of validation rules in GR</p>\n<p 'apple=\"\" 'segoe=\"\" color=\"\" emoji',=\"\" sans-serif;\"=\"\" segoe=\"\" system-ui,=\"\" ui=\"\" ui',=\"\">A: After applying the note, ideally, one validation method can assign up to 3000 rules.</p>\n<p 'apple=\"\" 'segoe=\"\" color=\"\" emoji',=\"\" sans-serif;\"=\"\" segoe=\"\" system-ui,=\"\" ui=\"\" ui',=\"\"><br/>• What would be the impact of having a large number of validation rules on the performance?<br/>A: In some bad case, hana memory will be used out during processing large number of rules on large volume of data.</p>\n<p 'apple=\"\" 'segoe=\"\" color=\"\" emoji',=\"\" sans-serif;\"=\"\" segoe=\"\" system-ui,=\"\" ui=\"\" ui',=\"\"><br/>- If the impact would be impact devastating, how can we mitigate the risk?<br/>A:<br/>1. keep number of rules as small as possible, keep number of operands in each rule as small as possible<br/>2. when define conditions in rules, use value field first, attribute or hierarchy fields will cost more memory<br/>3. if similar rule can apply for a same dimension, consider using group by in rule definition</p>\n<p 'apple=\"\" 'segoe=\"\" color=\"\" emoji',=\"\" sans-serif;\"=\"\" segoe=\"\" system-ui,=\"\" ui=\"\" ui',=\"\">• Is there any recommended note that we can implement to increase the performance for it?<br/>note: 3082123</p>", "noteVersion": 186}], "activities": [{"Activity": "Business Decision", "Phase": "Any time", "Condition": "Mandatory", "Additional_Information": "Define your consolidation strategy and when to migrate to alternative solution."}, {"Activity": "Fiori Implementation", "Phase": "After conversion project", "Condition": "Conditional", "Additional_Information": "When implementing SAP S/4HANA for Group Reporting set-up required Fiori Apps."}, {"Activity": "User Training", "Phase": "After conversion project", "Condition": "Conditional", "Additional_Information": "When moving SAP S/4HANA for Group Reporting train users in changed processes."}, {"Activity": "Implementation project required", "Phase": "After conversion project", "Condition": "Conditional", "Additional_Information": "For a transition to SAP S/4HANA for Group Reporting proceed as described in SAP note 2833748."}, {"Activity": "Data correction", "Phase": "After conversion project", "Condition": "Conditional", "Additional_Information": "As preparation for a migration to SAP S/4HANA for Group Reporting run consistency check transaction FINCS_CHECK_LEDGER according to note 3017796."}, {"Activity": "Others", "Phase": "After conversion project", "Condition": "Conditional", "Additional_Information": "If you use SAP S/4HANA 1909 or later and decide for a hybrid phase, contact SAP support."}]}