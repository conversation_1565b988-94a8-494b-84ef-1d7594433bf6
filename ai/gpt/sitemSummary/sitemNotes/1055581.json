{"Request": {"Number": "1055581", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1445, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016286492017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001055581?language=E&token=6B579E78963F24914581D358B5ADF990"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001055581", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001055581/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1055581"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 33}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.08.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BW"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Business Warehouse"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1055581 - Recommendations for Support Package Stacks for BI 7.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note lists the Support Package Stacks that we recommend you use for SAP NetWeaver BI 7.0.<br /><br />This note is classified as information for release planning. It does NOT describe procedures to correct problems or a specific problem. If you want information about this, refer to the SAPBINEWS notes for the Support Packages, for example, Note 1136882 for Support Package Stack 16 (BI Support Package 18).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Business Explorer, BEx Web, J2EE, Java, NW2004s</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B><U>General recommendation for Support Package (Stack)</U></B><br />Irrespective of the scenarios that you are using, we recommend that you import at least Support Package Stack (SPS) 17 and front-end Support Package 8 Patch 1 (front-end Support Package 801), in particular if you want to go live. Ideally, you should import the latest Support Package Stack. Note especially if you are using BI Java that a patch can be provided for unknown errors in an appropriate period of time with justifiable risk only for the last three Support Package Stacks. For more information about this, see Note 1072576. See the information contained in Note 375631, which describes the advantages of a current Support Package.<br />As part of the maintenance agreement, each reported problem and its symptom are analyzed.<br /><br /><B><U>General notes</U></B><br />Mainstream maintenance begins with the \"restricted shipment\" phase and continues in the \"unrestricted shipment\" phase. SAP provides corrections such as notes, legal changes, Support Packages and Support Package Stacks for the whole mainstream maintenance period.<br />The different packages in which corrections are delivered are now explained:<br /><B>Support Package Stack</B><br />The corrections in a Support Package Stack are in Java, ABAP and .NET (Frontend Business Explorer tools). A Support Package Stack can contain functional enhancements. In particular, changes and corrections that require adjustments in Java and ABAP can only be delivered in a Support Package Stack.<br /><B>Support Package</B><br />All the corrections in a Support Package are in ABAP. All notes in a Support Package have correction instructions. In addition, a Support Package can contain extensive changes and adjustments that cannot be delivered using a note with correction instructions, for example, changes to table contents. Functional enhancements are only possible in a Support Package.<br />Note 1013369 describes the dependencies between Support Package Stacks and Support Packages (BI-ABAP).<br />No separate Support Packages are delivered for Java. The Support Packages for Java are always delivered in a Support Package Stack.<br /><B>Note with correction instructions</B><br />A note with correction instructions contains minor changes and corrections that you can implement using transaction SNOTE.<br /><B>Patch</B><br />A patch contains corrections and minor changes in Java that you can implement using the Software Deployment Manager (SDM). A patch cannot contain minor functional enhancements because only limited corrections are possible. New parameters usually require adjustments in ABAP, for example. Therefore, functional enhancements can only be delivered in a Support Package Stack.<br /><B>Frontend Patch / Frontend Support Package</B><br />A Front-end patch is the equivalent of a Support Package for Business Explorer tools, for example, BEx Query Designer or BEx Web Application Designer. A front-end patch must be installed on the client.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026949)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026949)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001055581/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001055581/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001055581/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001055581/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001055581/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001055581/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001055581/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001055581/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001055581/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "927530", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BI Java sizing", "RefUrl": "/notes/927530"}, {"RefNumber": "853505", "RefComponent": "BW", "RefTitle": "Release Restr.: Usage Type BI-Java of SAP NetWeaver 2004s", "RefUrl": "/notes/853505"}, {"RefNumber": "375631", "RefComponent": "BW", "RefTitle": "Which Support Package is recommended for BW-customers?", "RefUrl": "/notes/375631"}, {"RefNumber": "1751390", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Customer Language on RFC (JCo) Communication", "RefUrl": "/notes/1751390"}, {"RefNumber": "1751318", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: variables filled by pre-query in Java Web", "RefUrl": "/notes/1751318"}, {"RefNumber": "1751251", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Sorting by attribute is not working in Java Web Runtime", "RefUrl": "/notes/1751251"}, {"RefNumber": "1748475", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1748475"}, {"RefNumber": "1746611", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC 730 : Document list item UI changed in 730 only", "RefUrl": "/notes/1746611"}, {"RefNumber": "1745594", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1745594"}, {"RefNumber": "1745250", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Cache warm up for binding: follow-up correction for 1598020", "RefUrl": "/notes/1745250"}, {"RefNumber": "1744912", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1744912"}, {"RefNumber": "1744706", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Order of structure elements and key figures not stored", "RefUrl": "/notes/1744706"}, {"RefNumber": "1744421", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification command - Results rows/totals rows", "RefUrl": "/notes/1744421"}, {"RefNumber": "1744401", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1744401"}, {"RefNumber": "1744304", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Percent (%) not exported when using cumulative calculation", "RefUrl": "/notes/1744304"}, {"RefNumber": "1743305", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Removal of the securelib in 700 and 701", "RefUrl": "/notes/1743305"}, {"RefNumber": "1742571", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Selector: New search functionality", "RefUrl": "/notes/1742571"}, {"RefNumber": "1739716", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1739716"}, {"RefNumber": "1736098", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drill State is lost after adding a local formula in Java", "RefUrl": "/notes/1736098"}, {"RefNumber": "1735180", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1735180"}, {"RefNumber": "1728063", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1728063"}, {"RefNumber": "1727134", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1727134"}, {"RefNumber": "1725344", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1725344"}, {"RefNumber": "1724953", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Report Item Export with ADS PDF - Blank Pages", "RefUrl": "/notes/1724953"}, {"RefNumber": "1724848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1724848"}, {"RefNumber": "1723007", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "'TARTITLE' error message is thrown while loading KM Bookmark", "RefUrl": "/notes/1723007"}, {"RefNumber": "1722983", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Recommendations to resolve 'NO ESID FOUND' error", "RefUrl": "/notes/1722983"}, {"RefNumber": "1721864", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: KM Documents Integration: Performance Issues", "RefUrl": "/notes/1721864"}, {"RefNumber": "1721338", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Limitations in creating and displying Documents in BEx Web", "RefUrl": "/notes/1721338"}, {"RefNumber": "1720423", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1720423"}, {"RefNumber": "1717916", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Broadcast to excel contains hierarchy images.", "RefUrl": "/notes/1717916"}, {"RefNumber": "1717730", "RefComponent": "BI-RA-BICS", "RefTitle": "Fetaure :BICS Consumer Sorting", "RefUrl": "/notes/1717730"}, {"RefNumber": "1717722", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1717722"}, {"RefNumber": "1717226", "RefComponent": "BI-RA-BICS", "RefTitle": "Korrekturen in Privot Table", "RefUrl": "/notes/1717226"}, {"RefNumber": "1715642", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1715642"}, {"RefNumber": "1715006", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1715006"}, {"RefNumber": "1714871", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: compound characteristics variables in Java", "RefUrl": "/notes/1714871"}, {"RefNumber": "1713344", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Error text missing in the Document Browser Dialog", "RefUrl": "/notes/1713344"}, {"RefNumber": "1711936", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Limit number of member selections", "RefUrl": "/notes/1711936"}, {"RefNumber": "1710200", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1710200"}, {"RefNumber": "1709614", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammel Hinweis für Transiente Hierarchien", "RefUrl": "/notes/1709614"}, {"RefNumber": "1709094", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1709094"}, {"RefNumber": "1708919", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Template dialog functionality", "RefUrl": "/notes/1708919"}, {"RefNumber": "1703363", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Sammelhinweis für HANA Provider", "RefUrl": "/notes/1703363"}, {"RefNumber": "1700878", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Improper button size in the selector dialog with Firefox.", "RefUrl": "/notes/1700878"}, {"RefNumber": "1699440", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "java.lang.NullPointerException occurs while closing dialog", "RefUrl": "/notes/1699440"}, {"RefNumber": "1699153", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1699153"}, {"RefNumber": "1698072", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Application state changes after OK_TEMPLATE_DIALOG command", "RefUrl": "/notes/1698072"}, {"RefNumber": "1696718", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Xcelsius: <PERSON><PERSON><PERSON> message logged for the characteristic.", "RefUrl": "/notes/1696718"}, {"RefNumber": "1696706", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Assertion: selector<PERSON><PERSON><PERSON><PERSON><PERSON> is already assigned", "RefUrl": "/notes/1696706"}, {"RefNumber": "1695304", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1695304"}, {"RefNumber": "1694649", "RefComponent": "BI-RA-BICS", "RefTitle": "Search for members using characteristic attributes (Java)", "RefUrl": "/notes/1694649"}, {"RefNumber": "1693478", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: improvement of info object member cache in Java", "RefUrl": "/notes/1693478"}, {"RefNumber": "1692658", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1692658"}, {"RefNumber": "1691681", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1691681"}, {"RefNumber": "1691626", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1691626"}, {"RefNumber": "1690127", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1690127"}, {"RefNumber": "1689984", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Sel. option variable value entered with '*' will show a '\\\\'", "RefUrl": "/notes/1689984"}, {"RefNumber": "1689880", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1689880"}, {"RefNumber": "1689060", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Thread <PERSON> in class ObjectFactory", "RefUrl": "/notes/1689060"}, {"RefNumber": "1689059", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Unauthorized modification of displayed content in BEx Web", "RefUrl": "/notes/1689059"}, {"RefNumber": "1688919", "RefComponent": "BI-RA-BICS", "RefTitle": "Providing the capability information of the provider", "RefUrl": "/notes/1688919"}, {"RefNumber": "1688217", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "ConcurrentModificationException for iView with ContentLink", "RefUrl": "/notes/1688217"}, {"RefNumber": "1687803", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1687803"}, {"RefNumber": "1686374", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1686374"}, {"RefNumber": "1684950", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1684950"}, {"RefNumber": "1683605", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Zero values with units looses units when exporting to Excel", "RefUrl": "/notes/1683605"}, {"RefNumber": "1682487", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web ODOC: ABAP Side Improvements in 7.3x systems", "RefUrl": "/notes/1682487"}, {"RefNumber": "1681475", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis Modifikation Kommentar - Export (BLOCK_ROWS_SIZE) 2", "RefUrl": "/notes/1681475"}, {"RefNumber": "1679965", "RefComponent": "BI-RA-BICS", "RefTitle": "W/o result set raises supportsCurrencyTranslation() error", "RefUrl": "/notes/1679965"}, {"RefNumber": "1679834", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1679834"}, {"RefNumber": "1679787", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Drag & Drop operation on input ready query causes exception", "RefUrl": "/notes/1679787"}, {"RefNumber": "1676337", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export to Excel: Wrong value exported for 01/01/1901", "RefUrl": "/notes/1676337"}, {"RefNumber": "1672858", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Invalid Base64-encoded character encountered - WD ALV Print", "RefUrl": "/notes/1672858"}, {"RefNumber": "1669587", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "ENTRIES_MAXCOUNT set to zero does not show all values.", "RefUrl": "/notes/1669587"}, {"RefNumber": "1667925", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Result set is invalid because you set a custom parameter", "RefUrl": "/notes/1667925"}, {"RefNumber": "1667463", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1667463"}, {"RefNumber": "1666825", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification command - JavaScript by clicking mouse", "RefUrl": "/notes/1666825"}, {"RefNumber": "1666354", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1666354"}, {"RefNumber": "1663366", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Percent sign not displayed or inconsistent for some cells", "RefUrl": "/notes/1663366"}, {"RefNumber": "1662420", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1662420"}, {"RefNumber": "1662108", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1662108"}, {"RefNumber": "1660837", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drag and Drop: Usability enhancement selector dialog", "RefUrl": "/notes/1660837"}, {"RefNumber": "1660488", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export internal mime images to excel", "RefUrl": "/notes/1660488"}, {"RefNumber": "1659826", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when you hide a hierarchy level", "RefUrl": "/notes/1659826"}, {"RefNumber": "1656845", "RefComponent": "BI-RA-BICS", "RefTitle": "Internal: Sammelhinweis für Refactoring", "RefUrl": "/notes/1656845"}, {"RefNumber": "1656768", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1656768"}, {"RefNumber": "1656602", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1656602"}, {"RefNumber": "1656360", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammelhinweis: Korrekturen im Umfeld Export ResultSet", "RefUrl": "/notes/1656360"}, {"RefNumber": "1655853", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1655853"}, {"RefNumber": "1653742", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Unexpected drill operation state loss in Java Web Runtime", "RefUrl": "/notes/1653742"}, {"RefNumber": "1653260", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1653260"}, {"RefNumber": "1653153", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "No data in result set leads to <PERSON><PERSON><PERSON><PERSON> exception", "RefUrl": "/notes/1653153"}, {"RefNumber": "1652480", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammelhinweis für Hierarchies in BICS HANA Provider", "RefUrl": "/notes/1652480"}, {"RefNumber": "1652425", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammelhinweis für BICS Tests", "RefUrl": "/notes/1652425"}, {"RefNumber": "1651972", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1651972"}, {"RefNumber": "1651968", "RefComponent": "BW-BEX-ET-WJR-BOE", "RefTitle": "Bi Web Applications in BI 4.0: Logon with Windows AD", "RefUrl": "/notes/1651968"}, {"RefNumber": "1651050", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Internal: Infrastructure changes in RIC and NW 730", "RefUrl": "/notes/1651050"}, {"RefNumber": "1651044", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1651044"}, {"RefNumber": "1650992", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1650992"}, {"RefNumber": "1650853", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ZEN corrections in RIC", "RefUrl": "/notes/1650853"}, {"RefNumber": "1650395", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Navigation Pane Item: Sorting of free characteritics", "RefUrl": "/notes/1650395"}, {"RefNumber": "1649606", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Dot displayed for empty values in grid", "RefUrl": "/notes/1649606"}, {"RefNumber": "1648389", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Format background color changes font family", "RefUrl": "/notes/1648389"}, {"RefNumber": "1642017", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Xcelcius: blank values in filter lead to empty result", "RefUrl": "/notes/1642017"}, {"RefNumber": "1640307", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Issue on Set Item Parameters on Items with Commands", "RefUrl": "/notes/1640307"}, {"RefNumber": "1637675", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Date appears as ####### after export to excel", "RefUrl": "/notes/1637675"}, {"RefNumber": "1637197", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS:Query displays key instead of empty text for attributes", "RefUrl": "/notes/1637197"}, {"RefNumber": "1634117", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Display of null attributes in HANA", "RefUrl": "/notes/1634117"}, {"RefNumber": "1629171", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1629171"}, {"RefNumber": "1627409", "RefComponent": "BI-RA-BICS", "RefTitle": "Exception when resetting an expanded hierarchy", "RefUrl": "/notes/1627409"}, {"RefNumber": "1623428", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Export to PDF hangs for info field item.", "RefUrl": "/notes/1623428"}, {"RefNumber": "1622475", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: minor improvement in member access", "RefUrl": "/notes/1622475"}, {"RefNumber": "1622134", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Safety belt: Results quantity is too large (Excel/PDF)", "RefUrl": "/notes/1622134"}, {"RefNumber": "1620645", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS Hana Provider: Reduction of SQL Query Statements", "RefUrl": "/notes/1620645"}, {"RefNumber": "1618051", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "Empty description in \"Save As\" dialog results in overwrite", "RefUrl": "/notes/1618051"}, {"RefNumber": "1617936", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Save query view missing template id in backend table.", "RefUrl": "/notes/1617936"}, {"RefNumber": "1617533", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drilling with errors in combination with zero suppression", "RefUrl": "/notes/1617533"}, {"RefNumber": "1617004", "RefComponent": "BI-RA-BICS", "RefTitle": "Cell locking administration in the backend", "RefUrl": "/notes/1617004"}, {"RefNumber": "1617002", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "NullPointerException for rpts w/ref. to HANA Analytical View", "RefUrl": "/notes/1617002"}, {"RefNumber": "1614013", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "OSGi Activation for Visual Composer (BI-KIT) removed", "RefUrl": "/notes/1614013"}, {"RefNumber": "1613879", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "EXPORT_XML - Parameter REDIRECT_ABAP_FUNCTION_MODULE", "RefUrl": "/notes/1613879"}, {"RefNumber": "1613110", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Text Item is not wrapped while exporting", "RefUrl": "/notes/1613110"}, {"RefNumber": "1613090", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect values when you collapse a hierarchical structure", "RefUrl": "/notes/1613090"}, {"RefNumber": "1610963", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Column Width incorrect for universal hierarchy", "RefUrl": "/notes/1610963"}, {"RefNumber": "1610036", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Cancelation with sorting button for Attributes", "RefUrl": "/notes/1610036"}, {"RefNumber": "1607636", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Language for Web Applicatins opened in new window", "RefUrl": "/notes/1607636"}, {"RefNumber": "1602447", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification termination when you merge columns", "RefUrl": "/notes/1602447"}, {"RefNumber": "1601289", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Expanding of Universal Hierarchy not working in Java Runtime", "RefUrl": "/notes/1601289"}, {"RefNumber": "1601080", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: Column headers / No data", "RefUrl": "/notes/1601080"}, {"RefNumber": "1600962", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: URL Parameter LANGUAGE not always working", "RefUrl": "/notes/1600962"}, {"RefNumber": "1598498", "RefComponent": "BI-RA-BICS", "RefTitle": "Cells are not ready for input after you collapse a structure", "RefUrl": "/notes/1598498"}, {"RefNumber": "1598020", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: selection state binding in Java Web Template", "RefUrl": "/notes/1598020"}, {"RefNumber": "1597308", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Incorrect variants displayed when you execute a query", "RefUrl": "/notes/1597308"}, {"RefNumber": "1597173", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Backward navigation selectorData<PERSON>rov<PERSON> is already assigned", "RefUrl": "/notes/1597173"}, {"RefNumber": "1596751", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Saving bookmark functionality results in peculiar behaviour", "RefUrl": "/notes/1596751"}, {"RefNumber": "1593072", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx web on IE9: JavaScripts error while calling popup dialog", "RefUrl": "/notes/1593072"}, {"RefNumber": "1592991", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Filter Pane Item overlaps in firefox and Safari", "RefUrl": "/notes/1592991"}, {"RefNumber": "1592848", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.x / BOE 4.0 Javascript error in IE9", "RefUrl": "/notes/1592848"}, {"RefNumber": "1591534", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "SET_DATA_CELL_PROPERTIES command on structure node incorrect", "RefUrl": "/notes/1591534"}, {"RefNumber": "1589860", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: multiple variable values in WJR", "RefUrl": "/notes/1589860"}, {"RefNumber": "1589382", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1589382"}, {"RefNumber": "1589123", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Delete link from Show Details-> Action tab window removed", "RefUrl": "/notes/1589123"}, {"RefNumber": "1587376", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Broadcaster in Template Include Item scenario", "RefUrl": "/notes/1587376"}, {"RefNumber": "1585680", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Korrekturen RIG Modification Module", "RefUrl": "/notes/1585680"}, {"RefNumber": "1584387", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Problems with Firefox toolbar button width", "RefUrl": "/notes/1584387"}, {"RefNumber": "1583928", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Javascript error if analysis item is placed inside container", "RefUrl": "/notes/1583928"}, {"RefNumber": "1581923", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Optimization on Back with Many Data Providers", "RefUrl": "/notes/1581923"}, {"RefNumber": "1581167", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Totals row missing after drill op.on hier.key figure struct.", "RefUrl": "/notes/1581167"}, {"RefNumber": "1578941", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java Runtime: issue with pattern selection in ODOC scenario", "RefUrl": "/notes/1578941"}, {"RefNumber": "1577293", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java dump: Hierarchical structure and structure axis", "RefUrl": "/notes/1577293"}, {"RefNumber": "1573030", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Web application dumps when STATELESS is set to non-boolean", "RefUrl": "/notes/1573030"}, {"RefNumber": "1569787", "RefComponent": "BW-BEX-ET-WJR-DIA-CO", "RefTitle": "Condition Dialog does not validate interval range correctly", "RefUrl": "/notes/1569787"}, {"RefNumber": "1568251", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Sorting is disabled when F4 is called on Structural elements", "RefUrl": "/notes/1568251"}, {"RefNumber": "1568175", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: minor improvement for variants in Java Web", "RefUrl": "/notes/1568175"}, {"RefNumber": "1567416", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NullPointerException for system message without expiry date.", "RefUrl": "/notes/1567416"}, {"RefNumber": "1565796", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Sorting is disabled when F4 is called on Structural elements", "RefUrl": "/notes/1565796"}, {"RefNumber": "1565207", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Drag and drop in second row inserts new line in first row", "RefUrl": "/notes/1565207"}, {"RefNumber": "1564517", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Invalid member selection on info object with conversion exit", "RefUrl": "/notes/1564517"}, {"RefNumber": "1563643", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Modules RIG - Javascript error during export", "RefUrl": "/notes/1563643"}, {"RefNumber": "1562554", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Drag and Drop: incorrect behaviour on structural elements", "RefUrl": "/notes/1562554"}, {"RefNumber": "1561846", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Wildcard entry for SELECTION OPTION variable in selector", "RefUrl": "/notes/1561846"}, {"RefNumber": "1561738", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Wrong dropdown box when drag and drop between filter panes", "RefUrl": "/notes/1561738"}, {"RefNumber": "1561086", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Possible dump by loading a bookmark with an object variable", "RefUrl": "/notes/1561086"}, {"RefNumber": "1560665", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification RIG Document Content performance enhancement", "RefUrl": "/notes/1560665"}, {"RefNumber": "1558368", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Corrections in Support Desktool", "RefUrl": "/notes/1558368"}, {"RefNumber": "1556545", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Case conversion for filter values on compounded char", "RefUrl": "/notes/1556545"}, {"RefNumber": "1555612", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Wrong result set size calculation during drill operations", "RefUrl": "/notes/1555612"}, {"RefNumber": "1554104", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Close button on Document browser when you click details", "RefUrl": "/notes/1554104"}, {"RefNumber": "1552647", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Scaling factor reset to default while changing formula", "RefUrl": "/notes/1552647"}, {"RefNumber": "1552272", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Modal template dialog can not be closed", "RefUrl": "/notes/1552272"}, {"RefNumber": "1550398", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Handling of parameter SCRIPT in Web Applications", "RefUrl": "/notes/1550398"}, {"RefNumber": "1549846", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1549846"}, {"RefNumber": "1548840", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Condition state toggling: usability enhancement", "RefUrl": "/notes/1548840"}, {"RefNumber": "1548704", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "RRI:open mode REPLACE_WEB_APPLICATION opens a new window", "RefUrl": "/notes/1548704"}, {"RefNumber": "1548255", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Calculate Based On Results Of Former Details Calculations is", "RefUrl": "/notes/1548255"}, {"RefNumber": "1547871", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Corrections in Support Desktool", "RefUrl": "/notes/1547871"}, {"RefNumber": "1547714", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Parcer error for variable values with special operators", "RefUrl": "/notes/1547714"}, {"RefNumber": "1546012", "RefComponent": "BW-BEX-ET-WJR-DIA-FM", "RefTitle": "Java ClassCast exception in query (writeStateIntoDom)", "RefUrl": "/notes/1546012"}, {"RefNumber": "1545679", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Result with hierarchy symbol", "RefUrl": "/notes/1545679"}, {"RefNumber": "1545545", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC: Broadcasting to MHTML displays all columns in doc item", "RefUrl": "/notes/1545545"}, {"RefNumber": "1544415", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module CopyPaste:pasting more cells than the space available", "RefUrl": "/notes/1544415"}, {"RefNumber": "1543561", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "Display settings are incorrectly set in Open dialog", "RefUrl": "/notes/1543561"}, {"RefNumber": "1542355", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Removing DEBUG=X parameter for non-admin users", "RefUrl": "/notes/1542355"}, {"RefNumber": "1540416", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Javadoc adjusted in IRsAxisTuple.getAxisHierarchyInfo()", "RefUrl": "/notes/1540416"}, {"RefNumber": "1539790", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Exception during export of info field item.", "RefUrl": "/notes/1539790"}, {"RefNumber": "1538793", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Sorting on local formulas not saved after bookmark or undo", "RefUrl": "/notes/1538793"}, {"RefNumber": "1538753", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.x - comments option in context menu", "RefUrl": "/notes/1538753"}, {"RefNumber": "1538640", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BiNamespace characteristics not displyed in Assignment colum", "RefUrl": "/notes/1538640"}, {"RefNumber": "1538289", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "'X' appears in result cells (Calculate Result Set as \"Hide\")", "RefUrl": "/notes/1538289"}, {"RefNumber": "1538014", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Number of decimal resets to default while changing formula", "RefUrl": "/notes/1538014"}, {"RefNumber": "1537922", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "context menu with reference to selector dataprovider fails", "RefUrl": "/notes/1537922"}, {"RefNumber": "1537039", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "RTL(Right-to-Left) display not supported for PDF,EXCEL & CSV", "RefUrl": "/notes/1537039"}, {"RefNumber": "1536756", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Filter value lost in BACK_TO_PREVIOUS_STATE for DPs >= 2", "RefUrl": "/notes/1536756"}, {"RefNumber": "1536499", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Safari Browser: Adding bookmark to browser favorites", "RefUrl": "/notes/1536499"}, {"RefNumber": "1536476", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Web template execution recording incorrect object name", "RefUrl": "/notes/1536476"}, {"RefNumber": "1535651", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Object variables are not restored correctly from a bookmark", "RefUrl": "/notes/1535651"}, {"RefNumber": "1534863", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Tooltip for hierarchies in characteristic properties pane", "RefUrl": "/notes/1534863"}, {"RefNumber": "1534743", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.30 / BOE 4.0 Excel export freeze web app", "RefUrl": "/notes/1534743"}, {"RefNumber": "1534100", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Unexpected filter is applied if key contains special chars", "RefUrl": "/notes/1534100"}, {"RefNumber": "1534083", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Nullpointer when time-dependent hierarchies are loaded", "RefUrl": "/notes/1534083"}, {"RefNumber": "1533296", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module Copy-Paste: Values pasted at incorrect cell", "RefUrl": "/notes/1533296"}, {"RefNumber": "1532816", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Local formula is not deserialzed correctly (bookmark, undo)", "RefUrl": "/notes/1532816"}, {"RefNumber": "1532017", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "Order of login modules for the ticket component in the VA", "RefUrl": "/notes/1532017"}, {"RefNumber": "1531694", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Adding Bookmark to Browser Favorites in FPN brings wrong URL", "RefUrl": "/notes/1531694"}, {"RefNumber": "1528687", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Selection screen range value is validated incorrectly", "RefUrl": "/notes/1528687"}, {"RefNumber": "1527393", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "java.lang.nullpointerexception in support desk tool", "RefUrl": "/notes/1527393"}, {"RefNumber": "1527186", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Export to Excel: \"0.0%\" value converted to \"0,0\"", "RefUrl": "/notes/1527186"}, {"RefNumber": "1527184", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Termination during export to PDF or Excel with mod modules", "RefUrl": "/notes/1527184"}, {"RefNumber": "1526210", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Export of external MIMES (Intranet/Internet)", "RefUrl": "/notes/1526210"}, {"RefNumber": "1525392", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Planning function: error messages not reset (F4 selector)", "RefUrl": "/notes/1525392"}, {"RefNumber": "1525226", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Personalization flag in web Java runtime", "RefUrl": "/notes/1525226"}, {"RefNumber": "1524799", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Selection screen not shown with SET_VARIABLES_STATE command", "RefUrl": "/notes/1524799"}, {"RefNumber": "1524296", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "List calculation: Normalize acc next result without subtotal", "RefUrl": "/notes/1524296"}, {"RefNumber": "1524022", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Hierarchy Node with huge number in selector dialog", "RefUrl": "/notes/1524022"}, {"RefNumber": "1523974", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "java.lang.ClassCastException in ListBox item", "RefUrl": "/notes/1523974"}, {"RefNumber": "1523854", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "Personalization leads to exception with drop down item", "RefUrl": "/notes/1523854"}, {"RefNumber": "1523581", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC : Close button displayed in formatted dialog window.", "RefUrl": "/notes/1523581"}, {"RefNumber": "1522108", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Parcer error for hierarchy value with special characters", "RefUrl": "/notes/1522108"}, {"RefNumber": "1521831", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Pasting too many values in input text field for variable", "RefUrl": "/notes/1521831"}, {"RefNumber": "1521630", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Number of rows in analysis item when paging type is by item", "RefUrl": "/notes/1521630"}, {"RefNumber": "1520776", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BIBaseRuntimeException during creation of data provider", "RefUrl": "/notes/1520776"}, {"RefNumber": "1517597", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Error in processing of command SET_VARIABLES_STATE", "RefUrl": "/notes/1517597"}, {"RefNumber": "1516784", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "NullPointerException for Characteristic Selection", "RefUrl": "/notes/1516784"}, {"RefNumber": "1513148", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error occurs for various exception operators and filtering", "RefUrl": "/notes/1513148"}, {"RefNumber": "1512555", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "SAP NW BI 7.0: Truncated header text or footer text", "RefUrl": "/notes/1512555"}, {"RefNumber": "1512464", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC:java.lang.NullPointerException when KM cache is corrupt", "RefUrl": "/notes/1512464"}, {"RefNumber": "1509455", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Empty cells are not ready for input on hierarchical axis", "RefUrl": "/notes/1509455"}, {"RefNumber": "1507068", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Negative value in Calendar tool is incorrectly processed", "RefUrl": "/notes/1507068"}, {"RefNumber": "1507065", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Adding BI Bookmarks to browser favorites in CRM system", "RefUrl": "/notes/1507065"}, {"RefNumber": "1506722", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Generic Note for BI Java Patches and Support Packages", "RefUrl": "/notes/1506722"}, {"RefNumber": "1505718", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Export to PDF in ODOC: Font size is scaled down", "RefUrl": "/notes/1505718"}, {"RefNumber": "1505639", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Input ready cells look bigger than usual cells", "RefUrl": "/notes/1505639"}, {"RefNumber": "1505375", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "Missing translations for default BW portal content", "RefUrl": "/notes/1505375"}, {"RefNumber": "1505289", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Display line breaks or multiple spaces on BW Text Item", "RefUrl": "/notes/1505289"}, {"RefNumber": "1505221", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Parallel operations of loading and deleting a variant", "RefUrl": "/notes/1505221"}, {"RefNumber": "1504676", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module Copy-Paste: input values not taken over by cells", "RefUrl": "/notes/1504676"}, {"RefNumber": "1504675", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW BI 7.0: Problem with context menu on chart w/ Firefox 3.6", "RefUrl": "/notes/1504675"}, {"RefNumber": "1503986", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Semantic Text Style of header cells", "RefUrl": "/notes/1503986"}, {"RefNumber": "1503243", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "Loading animation disappears when submitting dialogs", "RefUrl": "/notes/1503243"}, {"RefNumber": "1502713", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Error in Opening Selector dialog from an Exception wizard", "RefUrl": "/notes/1502713"}, {"RefNumber": "1501042", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module Scrolling: input ready cells not changeable", "RefUrl": "/notes/1501042"}, {"RefNumber": "1500651", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module Scrolling: some columns not visible", "RefUrl": "/notes/1500651"}, {"RefNumber": "1500477", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "SDT: BI JAVA and ABAP Compatibility check", "RefUrl": "/notes/1500477"}, {"RefNumber": "1499920", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "java.lang.UnsupportedOperationException in ListBox Item", "RefUrl": "/notes/1499920"}, {"RefNumber": "1499814", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Personalization \"Key already exists the entry was ignored\"", "RefUrl": "/notes/1499814"}, {"RefNumber": "1498818", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC : Close button displayed in New document dialog window", "RefUrl": "/notes/1498818"}, {"RefNumber": "1498557", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Modules RIG - Invoking context menu or snippets", "RefUrl": "/notes/1498557"}, {"RefNumber": "1498512", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Font color of the tab strip item is incorrect", "RefUrl": "/notes/1498512"}, {"RefNumber": "1496507", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "500 Internal Server Error on clicking a portal favorite", "RefUrl": "/notes/1496507"}, {"RefNumber": "1496495", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Display Hierachy: Context menu expand does not work", "RefUrl": "/notes/1496495"}, {"RefNumber": "1495857", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification formatting of rows and columns", "RefUrl": "/notes/1495857"}, {"RefNumber": "1495754", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "addSharedMessage: convinience method for 700/701 code lines", "RefUrl": "/notes/1495754"}, {"RefNumber": "1494073", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Missing elements in the XML document for XPath evaluation", "RefUrl": "/notes/1494073"}, {"RefNumber": "1479703", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Calendar date is incorrect while invoking a selector dialog", "RefUrl": "/notes/1479703"}, {"RefNumber": "1451171", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Path entries are displayed twice", "RefUrl": "/notes/1451171"}, {"RefNumber": "1437358", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "NullPointerException due to incorrect template definition", "RefUrl": "/notes/1437358"}, {"RefNumber": "1425720", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: User-defined symbols for exception II", "RefUrl": "/notes/1425720"}, {"RefNumber": "1174124", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Condition not used with active axis hierarchy", "RefUrl": "/notes/1174124"}, {"RefNumber": "1127156", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Safety belt: Result set is too large", "RefUrl": "/notes/1127156"}, {"RefNumber": "1101143", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Collective note: BEx Analyzer performance", "RefUrl": "/notes/1101143"}, {"RefNumber": "1072576", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Frequently Asked Questions: BI Java Support Packages/patches", "RefUrl": "/notes/1072576"}, {"RefNumber": "1030279", "RefComponent": "BW-BEX", "RefTitle": "Reports with very large result sets/BI Java", "RefUrl": "/notes/1030279"}, {"RefNumber": "1025307", "RefComponent": "BW", "RefTitle": "Composite note for BW 7.00 / BW 7.01 performance: Reporting", "RefUrl": "/notes/1025307"}, {"RefNumber": "1013369", "RefComponent": "BW", "RefTitle": "SAP NetWeaver 7.0 BI - intermediate Support Packages", "RefUrl": "/notes/1013369"}, {"RefNumber": "1003344", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Various problems with variables and hierarchies", "RefUrl": "/notes/1003344"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2641239", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "Patch level 0 check in the SAP NetWeaver BI Diagnostics & Support Desk Tool", "RefUrl": "/notes/2641239 "}, {"RefNumber": "2598282", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Broadcasting error when clickjacking protection is enabled", "RefUrl": "/notes/2598282 "}, {"RefNumber": "2549714", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.3+: Scrolling while Drag and Drop is not working", "RefUrl": "/notes/2549714 "}, {"RefNumber": "2515982", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: BW4HANA as backend system", "RefUrl": "/notes/2515982 "}, {"RefNumber": "2463600", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Google Chrome browser support for BEx Web Analyzer", "RefUrl": "/notes/2463600 "}, {"RefNumber": "2460187", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Error with external value help for variables", "RefUrl": "/notes/2460187 "}, {"RefNumber": "2449835", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Integer conversion of large decimal values", "RefUrl": "/notes/2449835 "}, {"RefNumber": "2436794", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Searching in value help throws error if too many values are processed", "RefUrl": "/notes/2436794 "}, {"RefNumber": "2394712", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Unparseable Date Error when Export to Excel in Design Studio", "RefUrl": "/notes/2394712 "}, {"RefNumber": "2381043", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Default encoding of CSV is UTF16", "RefUrl": "/notes/2381043 "}, {"RefNumber": "2371972", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA Wrong member instance due to duplicate cache entry", "RefUrl": "/notes/2371972 "}, {"RefNumber": "2349419", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "BI Export Services : Enhanced Logs and traces", "RefUrl": "/notes/2349419 "}, {"RefNumber": "2349141", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "Enhanced Tracing for SDT", "RefUrl": "/notes/2349141 "}, {"RefNumber": "2339397", "RefComponent": "BI-RA-BICS", "RefTitle": "Separate variant and variant catalog to enable reuse", "RefUrl": "/notes/2339397 "}, {"RefNumber": "2338383", "RefComponent": "BI-RA-BICS", "RefTitle": "Check child tuple size when drilling to correct node drill state", "RefUrl": "/notes/2338383 "}, {"RefNumber": "2319606", "RefComponent": "BI-RA-BICS", "RefTitle": "Optional ignore flag to support retrieving master system settings for non master systems", "RefUrl": "/notes/2319606 "}, {"RefNumber": "2304451", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "NullPointerException in Result Set after formula deletion", "RefUrl": "/notes/2304451 "}, {"RefNumber": "2287474", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30+ - Accessibility: Usage of misleading UI properties parts", "RefUrl": "/notes/2287474 "}, {"RefNumber": "2284698", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.3+: MOD Scrolling: synchronization of row heights not working", "RefUrl": "/notes/2284698 "}, {"RefNumber": "2272779", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA: Allow cancelling of HTTP requests", "RefUrl": "/notes/2272779 "}, {"RefNumber": "2268338", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA: Remove variable submit request", "RefUrl": "/notes/2268338 "}, {"RefNumber": "2265041", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS InA Support for hierarchy node variable default values", "RefUrl": "/notes/2265041 "}, {"RefNumber": "2264336", "RefComponent": "BI-RA-BICS", "RefTitle": "Maximum number of characteristics on axis", "RefUrl": "/notes/2264336 "}, {"RefNumber": "2228348", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "BICS HANA Cancel without submit causes exception \"invalid variable values\"", "RefUrl": "/notes/2228348 "}, {"RefNumber": "2170789", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BICS: Duplicate sorting types (JUnit fix)", "RefUrl": "/notes/2170789 "}, {"RefNumber": "2195363", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "<PERSON><PERSON><PERSON> Font not set while exporting the document via BEx Broadcaster", "RefUrl": "/notes/2195363 "}, {"RefNumber": "2189156", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "TEMPLATE ENGLISH: BW Java / BOE 4.0 / BICS", "RefUrl": "/notes/2189156 "}, {"RefNumber": "2178138", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: BI/BW Java network requirements, recommendations and limitations (WAN)", "RefUrl": "/notes/2178138 "}, {"RefNumber": "2170782", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.0x: NullPointerException when exporting no data resultset", "RefUrl": "/notes/2170782 "}, {"RefNumber": "2121454", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Query hangs during page navigation", "RefUrl": "/notes/2121454 "}, {"RefNumber": "2117583", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Sort by selection incorrect in case of a hierarchy drill operation", "RefUrl": "/notes/2117583 "}, {"RefNumber": "2117546", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Hierarchy drill state lost after refresh", "RefUrl": "/notes/2117546 "}, {"RefNumber": "2103149", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Error in value help for variable", "RefUrl": "/notes/2103149 "}, {"RefNumber": "2096136", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "HANA Limitations for BI Clients using BICS HTTP Connection", "RefUrl": "/notes/2096136 "}, {"RefNumber": "1999784", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW7.0 SPS 31 Patch 20 note for BI Java", "RefUrl": "/notes/1999784 "}, {"RefNumber": "2092088", "RefComponent": "BI-RA-BICS", "RefTitle": "Optimizations for the relational mapper and CSV provider", "RefUrl": "/notes/2092088 "}, {"RefNumber": "2078795", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Scrolling: Planning does not work with module after upgrade", "RefUrl": "/notes/2078795 "}, {"RefNumber": "2078794", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ExceptionImage: Symbol reverts to default when document icon visible", "RefUrl": "/notes/2078794 "}, {"RefNumber": "2053210", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.01+: Input of leaf returns hierarchy node presentation", "RefUrl": "/notes/2053210 "}, {"RefNumber": "2053007", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Numerous Certificate pop ups during Excel Export", "RefUrl": "/notes/2053007 "}, {"RefNumber": "2044560", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Bookmark user language", "RefUrl": "/notes/2044560 "}, {"RefNumber": "2033984", "RefComponent": "BI-RA-BICS", "RefTitle": "Collective Note for Table Design Development Fixes Affected archive", "RefUrl": "/notes/2033984 "}, {"RefNumber": "2027277", "RefComponent": "BI-RA-BICS", "RefTitle": "Optimzation for accessing already retrieved members", "RefUrl": "/notes/2027277 "}, {"RefNumber": "2022953", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Container Layout Item: Exporting to PDF with Fit to Page Width option does not show multiple pages", "RefUrl": "/notes/2022953 "}, {"RefNumber": "2014839", "RefComponent": "BI-RA-BICS", "RefTitle": "'java.lang.UnsupportedOperationException: No Delta Operation Support' for Universal Hierarchy in BICS Relational Provider", "RefUrl": "/notes/2014839 "}, {"RefNumber": "1968692", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW7.0 SPS 31 Patch 10 note for BI Java", "RefUrl": "/notes/1968692 "}, {"RefNumber": "2010286", "RefComponent": "BI-RA-BICS", "RefTitle": "Attribute planning in BICS (Java)", "RefUrl": "/notes/2010286 "}, {"RefNumber": "2006630", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS DesignTime API: NPE for fixed time-dependent hierarchy", "RefUrl": "/notes/2006630 "}, {"RefNumber": "1968735", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW7.0 SPS 30 Patch 10 note for BI Java", "RefUrl": "/notes/1968735 "}, {"RefNumber": "1934084", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW7.0 SPS 30 Patch 20 note for BI Java", "RefUrl": "/notes/1934084 "}, {"RefNumber": "1970186", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Performance issue while loading the BI dashboard", "RefUrl": "/notes/1970186 "}, {"RefNumber": "1961654", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Change in the format of cell in exported excel file in BEx web.", "RefUrl": "/notes/1961654 "}, {"RefNumber": "1951306", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "JAVA Documents: Performance is bad due plenty of BICS_PROV_GET_MEMBERS", "RefUrl": "/notes/1951306 "}, {"RefNumber": "1953474", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "AbortMessageRuntimeException when searching with an invalid presentation", "RefUrl": "/notes/1953474 "}, {"RefNumber": "1943401", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Dynamic Grid in Analysis Office 2.0", "RefUrl": "/notes/1943401 "}, {"RefNumber": "1941805", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: BicsResourceBwRuntimeException when using list calculations after moving hierarchical structure to free characteristics axis", "RefUrl": "/notes/1941805 "}, {"RefNumber": "1940743", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Internal encodeBase64 in AcImage", "RefUrl": "/notes/1940743 "}, {"RefNumber": "1932399", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Dynamic Grid", "RefUrl": "/notes/1932399 "}, {"RefNumber": "1931856", "RefComponent": "BI-RA-BICS", "RefTitle": "HANA Hierarchy Features for SP7", "RefUrl": "/notes/1931856 "}, {"RefNumber": "1651050", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Internal: Infrastructure changes in RIC and NW 730", "RefUrl": "/notes/1651050 "}, {"RefNumber": "1724953", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Report Item Export with ADS PDF - Blank Pages", "RefUrl": "/notes/1724953 "}, {"RefNumber": "1921167", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Inconsistent due to same index/key values", "RefUrl": "/notes/1921167 "}, {"RefNumber": "1913659", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3+: Context Menu does not open in Safari", "RefUrl": "/notes/1913659 "}, {"RefNumber": "1895383", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.x: Exception symbols are not exported to Excel", "RefUrl": "/notes/1895383 "}, {"RefNumber": "1907637", "RefComponent": "BI-RA-BICS", "RefTitle": "Issues in Levelvisibility and Drilloperations", "RefUrl": "/notes/1907637 "}, {"RefNumber": "1889734", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Format: Custom formatting is lost after tab navigations", "RefUrl": "/notes/1889734 "}, {"RefNumber": "1882750", "RefComponent": "BI-RA-BICS", "RefTitle": "Application crash with key figures of type date", "RefUrl": "/notes/1882750 "}, {"RefNumber": "1830200", "RefComponent": "BI-RA-BICS", "RefTitle": "Not existing operands in formulas", "RefUrl": "/notes/1830200 "}, {"RefNumber": "1873448", "RefComponent": "BI-RA-BICS", "RefTitle": "Level visibility on both axes returns wrong values", "RefUrl": "/notes/1873448 "}, {"RefNumber": "1875755", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "[intern!] BI Web App in BI Platform - Package Size Reduction", "RefUrl": "/notes/1875755 "}, {"RefNumber": "1656845", "RefComponent": "BI-RA-BICS", "RefTitle": "Internal: Sammelhinweis für Refactoring", "RefUrl": "/notes/1656845 "}, {"RefNumber": "1652425", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammelhinweis für BICS Tests", "RefUrl": "/notes/1652425 "}, {"RefNumber": "1703363", "RefComponent": "BI-RA-BICS-HAN", "RefTitle": "Sammelhinweis für HANA Provider", "RefUrl": "/notes/1703363 "}, {"RefNumber": "1652480", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammelhinweis für Hierarchies in BICS HANA Provider", "RefUrl": "/notes/1652480 "}, {"RefNumber": "1721864", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: KM Documents Integration: Performance Issues", "RefUrl": "/notes/1721864 "}, {"RefNumber": "1829407", "RefComponent": "BI-RA-BICS", "RefTitle": "<PERSON> von Variablen Cancel in BICS", "RefUrl": "/notes/1829407 "}, {"RefNumber": "1840832", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BI Document is not working with compound characteristics", "RefUrl": "/notes/1840832 "}, {"RefNumber": "927530", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BI Java sizing", "RefUrl": "/notes/927530 "}, {"RefNumber": "1866475", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "New comment displays symbols while using other languages", "RefUrl": "/notes/1866475 "}, {"RefNumber": "1880043", "RefComponent": "BW-BEX-ET-WJR-DIA-FM", "RefTitle": "BEx Web 7.x: Formula editor not working in Firefox", "RefUrl": "/notes/1880043 "}, {"RefNumber": "1879450", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Alignment issue in export while using RIG Modules", "RefUrl": "/notes/1879450 "}, {"RefNumber": "1869477", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Unknown error when doing drag and drop to Filter Pane area", "RefUrl": "/notes/1869477 "}, {"RefNumber": "1869381", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "NullPointerException during export when data entry is active", "RefUrl": "/notes/1869381 "}, {"RefNumber": "1861128", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Data missing when wrapping enabled", "RefUrl": "/notes/1861128 "}, {"RefNumber": "1863880", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BEx Web 7.x: Data area commands causing exception", "RefUrl": "/notes/1863880 "}, {"RefNumber": "1869747", "RefComponent": "BI-RA-BICS", "RefTitle": "Eventing issues in Hana Provider", "RefUrl": "/notes/1869747 "}, {"RefNumber": "1864488", "RefComponent": "BI-RA-BICS", "RefTitle": "General Note for BICS consumer layer", "RefUrl": "/notes/1864488 "}, {"RefNumber": "1858626", "RefComponent": "BI-RA-BICS", "RefTitle": "supportsValueHelp returns true on EmptyVariable (HANA / BAE)", "RefUrl": "/notes/1858626 "}, {"RefNumber": "1127156", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Safety belt: Result set is too large", "RefUrl": "/notes/1127156 "}, {"RefNumber": "1622134", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Safety belt: Results quantity is too large (Excel/PDF)", "RefUrl": "/notes/1622134 "}, {"RefNumber": "1766253", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Hierarchy icons in Analysis Item incorrect when RTL", "RefUrl": "/notes/1766253 "}, {"RefNumber": "1840386", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: ALL_ROWS_WRAPPING parameter does not work", "RefUrl": "/notes/1840386 "}, {"RefNumber": "1853798", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "BEx Web 7.x: Drag and drop issue in Firefox", "RefUrl": "/notes/1853798 "}, {"RefNumber": "1821717", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "7.0x:Metadata error messages for bookmarks or favorites.", "RefUrl": "/notes/1821717 "}, {"RefNumber": "1846403", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Webtemplate gets hanged while doing right click.", "RefUrl": "/notes/1846403 "}, {"RefNumber": "1820370", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Fixed text of error message about missing master data", "RefUrl": "/notes/1820370 "}, {"RefNumber": "1850703", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Variable values description in the variable screen", "RefUrl": "/notes/1850703 "}, {"RefNumber": "1836846", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Command: Cell content is not exported", "RefUrl": "/notes/1836846 "}, {"RefNumber": "1824375", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnWidth: Cells are displaying ellipsis in IE", "RefUrl": "/notes/1824375 "}, {"RefNumber": "1826870", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web 7.x: Javascript deadlock in case of RRI to web", "RefUrl": "/notes/1826870 "}, {"RefNumber": "1820339", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Unexpected hierarchy node during input validation", "RefUrl": "/notes/1820339 "}, {"RefNumber": "1781468", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ChartItem: Chart contains too much data", "RefUrl": "/notes/1781468 "}, {"RefNumber": "1835868", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Extra \"New\" is appended in New Document title on using $R", "RefUrl": "/notes/1835868 "}, {"RefNumber": "1838690", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export: Unable to download in IE8", "RefUrl": "/notes/1838690 "}, {"RefNumber": "1650853", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "ZEN corrections in RIC", "RefUrl": "/notes/1650853 "}, {"RefNumber": "1827848", "RefComponent": "BI-RA-BICS", "RefTitle": "Hierarchy instances not updated when prov. uses same instncs", "RefUrl": "/notes/1827848 "}, {"RefNumber": "1827404", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BW System locks when saving a Report through CRM system", "RefUrl": "/notes/1827404 "}, {"RefNumber": "1818225", "RefComponent": "BI-RA-BICS", "RefTitle": "NULLPOINTER while executing a query against NW 7.40", "RefUrl": "/notes/1818225 "}, {"RefNumber": "1824206", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD DocumentContent: Long text is displayed incorrectly", "RefUrl": "/notes/1824206 "}, {"RefNumber": "1815978", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Currency output incorrect when using division formula", "RefUrl": "/notes/1815978 "}, {"RefNumber": "1815314", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error when executing planning function", "RefUrl": "/notes/1815314 "}, {"RefNumber": "1817272", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export to excel exports heirarchy images", "RefUrl": "/notes/1817272 "}, {"RefNumber": "1783021", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD Format: Wrong text color when exported", "RefUrl": "/notes/1783021 "}, {"RefNumber": "1763087", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Authorization check while saving the comment", "RefUrl": "/notes/1763087 "}, {"RefNumber": "1778471", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "<PERSON><PERSON>pointer exception when creating comments", "RefUrl": "/notes/1778471 "}, {"RefNumber": "1810307", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC 7.X : Display Name is blank in New Document dialog", "RefUrl": "/notes/1810307 "}, {"RefNumber": "1660837", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drag and Drop: Usability enhancement selector dialog", "RefUrl": "/notes/1660837 "}, {"RefNumber": "1809418", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC : 400 bad request when web dispatcher is used", "RefUrl": "/notes/1809418 "}, {"RefNumber": "1744421", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification command - Results rows/totals rows", "RefUrl": "/notes/1744421 "}, {"RefNumber": "1808977", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "High CPU utilization is observed by the jlaunch process", "RefUrl": "/notes/1808977 "}, {"RefNumber": "1798326", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD ColumnMerge: ArrayIndexOutOfBoundsException", "RefUrl": "/notes/1798326 "}, {"RefNumber": "1759747", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BEx Web: Customer Mask for New Document Title in Dialog", "RefUrl": "/notes/1759747 "}, {"RefNumber": "1782290", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "MOD DocumentContent: Wrong exception color when exported", "RefUrl": "/notes/1782290 "}, {"RefNumber": "1550398", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Handling of parameter SCRIPT in Web Applications", "RefUrl": "/notes/1550398 "}, {"RefNumber": "1506722", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Generic Note for BI Java Patches and Support Packages", "RefUrl": "/notes/1506722 "}, {"RefNumber": "1763305", "RefComponent": "BI-RA-BICS", "RefTitle": "Prototype Serenity Provider for FireFly", "RefUrl": "/notes/1763305 "}, {"RefNumber": "1786691", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.x: Javascript error if body onLoad event is used", "RefUrl": "/notes/1786691 "}, {"RefNumber": "1771551", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Hidden item are exported from container layout item", "RefUrl": "/notes/1771551 "}, {"RefNumber": "1773465", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "cell format text when a cell has input ready and static sect", "RefUrl": "/notes/1773465 "}, {"RefNumber": "1758171", "RefComponent": "BI-RA-BICS", "RefTitle": "SET_VARIABLE_STATE Kommando funktioniert nicht bei Knoten", "RefUrl": "/notes/1758171 "}, {"RefNumber": "1780093", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "System displays very long texts as ### in Excel 2007", "RefUrl": "/notes/1780093 "}, {"RefNumber": "1709614", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammel Hinweis für Transiente Hierarchien", "RefUrl": "/notes/1709614 "}, {"RefNumber": "1742571", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Selector: New search functionality", "RefUrl": "/notes/1742571 "}, {"RefNumber": "1600962", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: URL Parameter LANGUAGE not always working", "RefUrl": "/notes/1600962 "}, {"RefNumber": "1679787", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Drag & Drop operation on input ready query causes exception", "RefUrl": "/notes/1679787 "}, {"RefNumber": "1751390", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Customer Language on RFC (JCo) Communication", "RefUrl": "/notes/1751390 "}, {"RefNumber": "1613110", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Text Item is not wrapped while exporting", "RefUrl": "/notes/1613110 "}, {"RefNumber": "1568175", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: minor improvement for variants in Java Web", "RefUrl": "/notes/1568175 "}, {"RefNumber": "1666825", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification command - JavaScript by clicking mouse", "RefUrl": "/notes/1666825 "}, {"RefNumber": "1751251", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Sorting by attribute is not working in Java Web Runtime", "RefUrl": "/notes/1751251 "}, {"RefNumber": "1698072", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Application state changes after OK_TEMPLATE_DIALOG command", "RefUrl": "/notes/1698072 "}, {"RefNumber": "1755701", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: Own symbols for exception - MS Excel", "RefUrl": "/notes/1755701 "}, {"RefNumber": "1758231", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Error while generating HTML after F4 help screen", "RefUrl": "/notes/1758231 "}, {"RefNumber": "1757529", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: suboptimal logic in InfoFieldItem for Variables", "RefUrl": "/notes/1757529 "}, {"RefNumber": "1753511", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Incorrect display of changed values of input ready queries", "RefUrl": "/notes/1753511 "}, {"RefNumber": "1745250", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Cache warm up for binding: follow-up correction for 1598020", "RefUrl": "/notes/1745250 "}, {"RefNumber": "1744304", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Percent (%) not exported when using cumulative calculation", "RefUrl": "/notes/1744304 "}, {"RefNumber": "1761933", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "BEx Web 7.x: Unnecessary padding around input controls", "RefUrl": "/notes/1761933 "}, {"RefNumber": "1751318", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: variables filled by pre-query in Java Web", "RefUrl": "/notes/1751318 "}, {"RefNumber": "1711936", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.3x: Limit number of member selections", "RefUrl": "/notes/1711936 "}, {"RefNumber": "1496507", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "500 Internal Server Error on clicking a portal favorite", "RefUrl": "/notes/1496507 "}, {"RefNumber": "1682487", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web ODOC: ABAP Side Improvements in 7.3x systems", "RefUrl": "/notes/1682487 "}, {"RefNumber": "1746611", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC 730 : Document list item UI changed in 730 only", "RefUrl": "/notes/1746611 "}, {"RefNumber": "1744706", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Order of structure elements and key figures not stored", "RefUrl": "/notes/1744706 "}, {"RefNumber": "1722983", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Recommendations to resolve 'NO ESID FOUND' error", "RefUrl": "/notes/1722983 "}, {"RefNumber": "1736098", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drill State is lost after adding a local formula in Java", "RefUrl": "/notes/1736098 "}, {"RefNumber": "1743305", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Removal of the securelib in 700 and 701", "RefUrl": "/notes/1743305 "}, {"RefNumber": "1694649", "RefComponent": "BI-RA-BICS", "RefTitle": "Search for members using characteristic attributes (Java)", "RefUrl": "/notes/1694649 "}, {"RefNumber": "1713344", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Error text missing in the Document Browser Dialog", "RefUrl": "/notes/1713344 "}, {"RefNumber": "1717730", "RefComponent": "BI-RA-BICS", "RefTitle": "Fetaure :BICS Consumer Sorting", "RefUrl": "/notes/1717730 "}, {"RefNumber": "1717916", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Broadcast to excel contains hierarchy images.", "RefUrl": "/notes/1717916 "}, {"RefNumber": "1689060", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Thread <PERSON> in class ObjectFactory", "RefUrl": "/notes/1689060 "}, {"RefNumber": "1723007", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "'TARTITLE' error message is thrown while loading KM Bookmark", "RefUrl": "/notes/1723007 "}, {"RefNumber": "1714871", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: compound characteristics variables in Java", "RefUrl": "/notes/1714871 "}, {"RefNumber": "1721338", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Limitations in creating and displying Documents in BEx Web", "RefUrl": "/notes/1721338 "}, {"RefNumber": "1717226", "RefComponent": "BI-RA-BICS", "RefTitle": "Korrekturen in Privot Table", "RefUrl": "/notes/1717226 "}, {"RefNumber": "1688919", "RefComponent": "BI-RA-BICS", "RefTitle": "Providing the capability information of the provider", "RefUrl": "/notes/1688919 "}, {"RefNumber": "1700878", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Improper button size in the selector dialog with Firefox.", "RefUrl": "/notes/1700878 "}, {"RefNumber": "1708919", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Template dialog functionality", "RefUrl": "/notes/1708919 "}, {"RefNumber": "1681475", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis Modifikation Kommentar - Export (BLOCK_ROWS_SIZE) 2", "RefUrl": "/notes/1681475 "}, {"RefNumber": "1683605", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Zero values with units looses units when exporting to Excel", "RefUrl": "/notes/1683605 "}, {"RefNumber": "1688217", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "ConcurrentModificationException for iView with ContentLink", "RefUrl": "/notes/1688217 "}, {"RefNumber": "1676337", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export to Excel: Wrong value exported for 01/01/1901", "RefUrl": "/notes/1676337 "}, {"RefNumber": "1696706", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Assertion: selector<PERSON><PERSON><PERSON><PERSON><PERSON> is already assigned", "RefUrl": "/notes/1696706 "}, {"RefNumber": "1693478", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance: improvement of info object member cache in Java", "RefUrl": "/notes/1693478 "}, {"RefNumber": "1689984", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Sel. option variable value entered with '*' will show a '\\\\'", "RefUrl": "/notes/1689984 "}, {"RefNumber": "1696718", "RefComponent": "BW-BEX-ET-XC", "RefTitle": "Xcelsius: <PERSON><PERSON><PERSON> message logged for the characteristic.", "RefUrl": "/notes/1696718 "}, {"RefNumber": "1699440", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "java.lang.NullPointerException occurs while closing dialog", "RefUrl": "/notes/1699440 "}, {"RefNumber": "1479703", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Calendar date is incorrect while invoking a selector dialog", "RefUrl": "/notes/1479703 "}, {"RefNumber": "1547714", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Parcer error for variable values with special operators", "RefUrl": "/notes/1547714 "}, {"RefNumber": "1679965", "RefComponent": "BI-RA-BICS", "RefTitle": "W/o result set raises supportsCurrencyTranslation() error", "RefUrl": "/notes/1679965 "}, {"RefNumber": "1649606", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Dot displayed for empty values in grid", "RefUrl": "/notes/1649606 "}, {"RefNumber": "1584387", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Problems with Firefox toolbar button width", "RefUrl": "/notes/1584387 "}, {"RefNumber": "1648389", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Format background color changes font family", "RefUrl": "/notes/1648389 "}, {"RefNumber": "1672858", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Invalid Base64-encoded character encountered - WD ALV Print", "RefUrl": "/notes/1672858 "}, {"RefNumber": "1663366", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Percent sign not displayed or inconsistent for some cells", "RefUrl": "/notes/1663366 "}, {"RefNumber": "1669587", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "ENTRIES_MAXCOUNT set to zero does not show all values.", "RefUrl": "/notes/1669587 "}, {"RefNumber": "1596751", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Saving bookmark functionality results in peculiar behaviour", "RefUrl": "/notes/1596751 "}, {"RefNumber": "1656360", "RefComponent": "BI-RA-BICS", "RefTitle": "Sammelhinweis: Korrekturen im Umfeld Export ResultSet", "RefUrl": "/notes/1656360 "}, {"RefNumber": "1659826", "RefComponent": "BI-RA-BICS", "RefTitle": "Error when you hide a hierarchy level", "RefUrl": "/notes/1659826 "}, {"RefNumber": "1667925", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Result set is invalid because you set a custom parameter", "RefUrl": "/notes/1667925 "}, {"RefNumber": "1660488", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Export internal mime images to excel", "RefUrl": "/notes/1660488 "}, {"RefNumber": "1620645", "RefComponent": "BI-RA-BICS", "RefTitle": "BICS Hana Provider: Reduction of SQL Query Statements", "RefUrl": "/notes/1620645 "}, {"RefNumber": "1598020", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: selection state binding in Java Web Template", "RefUrl": "/notes/1598020 "}, {"RefNumber": "1614235", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "BEx Web 7.30 Report Item: MIMEs from MIME repository missing", "RefUrl": "/notes/1614235 "}, {"RefNumber": "1617002", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "NullPointerException for rpts w/ref. to HANA Analytical View", "RefUrl": "/notes/1617002 "}, {"RefNumber": "1617936", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Save query view missing template id in backend table.", "RefUrl": "/notes/1617936 "}, {"RefNumber": "1568251", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Sorting is disabled when F4 is called on Structural elements", "RefUrl": "/notes/1568251 "}, {"RefNumber": "1589123", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Delete link from Show Details-> Action tab window removed", "RefUrl": "/notes/1589123 "}, {"RefNumber": "1587376", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Broadcaster in Template Include Item scenario", "RefUrl": "/notes/1587376 "}, {"RefNumber": "1618051", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "Empty description in \"Save As\" dialog results in overwrite", "RefUrl": "/notes/1618051 "}, {"RefNumber": "1583928", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Javascript error if analysis item is placed inside container", "RefUrl": "/notes/1583928 "}, {"RefNumber": "1617004", "RefComponent": "BI-RA-BICS", "RefTitle": "Cell locking administration in the backend", "RefUrl": "/notes/1617004 "}, {"RefNumber": "1613879", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "EXPORT_XML - Parameter REDIRECT_ABAP_FUNCTION_MODULE", "RefUrl": "/notes/1613879 "}, {"RefNumber": "1573030", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Web application dumps when STATELESS is set to non-boolean", "RefUrl": "/notes/1573030 "}, {"RefNumber": "1640307", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Issue on Set Item Parameters on Items with Commands", "RefUrl": "/notes/1640307 "}, {"RefNumber": "1653742", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Unexpected drill operation state loss in Java Web Runtime", "RefUrl": "/notes/1653742 "}, {"RefNumber": "1653153", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "No data in result set leads to <PERSON><PERSON><PERSON><PERSON> exception", "RefUrl": "/notes/1653153 "}, {"RefNumber": "1650395", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Navigation Pane Item: Sorting of free characteritics", "RefUrl": "/notes/1650395 "}, {"RefNumber": "1610036", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Cancelation with sorting button for Attributes", "RefUrl": "/notes/1610036 "}, {"RefNumber": "1569787", "RefComponent": "BW-BEX-ET-WJR-DIA-CO", "RefTitle": "Condition Dialog does not validate interval range correctly", "RefUrl": "/notes/1569787 "}, {"RefNumber": "1567416", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NullPointerException for system message without expiry date.", "RefUrl": "/notes/1567416 "}, {"RefNumber": "1642017", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Xcelcius: blank values in filter lead to empty result", "RefUrl": "/notes/1642017 "}, {"RefNumber": "1637675", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Date appears as ####### after export to excel", "RefUrl": "/notes/1637675 "}, {"RefNumber": "1591534", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "SET_DATA_CELL_PROPERTIES command on structure node incorrect", "RefUrl": "/notes/1591534 "}, {"RefNumber": "1651968", "RefComponent": "BW-BEX-ET-WJR-BOE", "RefTitle": "Bi Web Applications in BI 4.0: Logon with Windows AD", "RefUrl": "/notes/1651968 "}, {"RefNumber": "1607636", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "BEx Web: Language for Web Applicatins opened in new window", "RefUrl": "/notes/1607636 "}, {"RefNumber": "1602447", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification termination when you merge columns", "RefUrl": "/notes/1602447 "}, {"RefNumber": "1601289", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Expanding of Universal Hierarchy not working in Java Runtime", "RefUrl": "/notes/1601289 "}, {"RefNumber": "1601080", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: Column headers / No data", "RefUrl": "/notes/1601080 "}, {"RefNumber": "1598498", "RefComponent": "BI-RA-BICS", "RefTitle": "Cells are not ready for input after you collapse a structure", "RefUrl": "/notes/1598498 "}, {"RefNumber": "1593072", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx web on IE9: JavaScripts error while calling popup dialog", "RefUrl": "/notes/1593072 "}, {"RefNumber": "1589860", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: multiple variable values in WJR", "RefUrl": "/notes/1589860 "}, {"RefNumber": "1562554", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Drag and Drop: incorrect behaviour on structural elements", "RefUrl": "/notes/1562554 "}, {"RefNumber": "1623428", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Export to PDF hangs for info field item.", "RefUrl": "/notes/1623428 "}, {"RefNumber": "853505", "RefComponent": "BW", "RefTitle": "Release Restr.: Usage Type BI-Java of SAP NetWeaver 2004s", "RefUrl": "/notes/853505 "}, {"RefNumber": "1634117", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Display of null attributes in HANA", "RefUrl": "/notes/1634117 "}, {"RefNumber": "1637197", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS:Query displays key instead of empty text for attributes", "RefUrl": "/notes/1637197 "}, {"RefNumber": "1627409", "RefComponent": "BI-RA-BICS", "RefTitle": "Exception when resetting an expanded hierarchy", "RefUrl": "/notes/1627409 "}, {"RefNumber": "1581923", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Optimization on Back with Many Data Providers", "RefUrl": "/notes/1581923 "}, {"RefNumber": "1622475", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Performance: minor improvement in member access", "RefUrl": "/notes/1622475 "}, {"RefNumber": "1617533", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Drilling with errors in combination with zero suppression", "RefUrl": "/notes/1617533 "}, {"RefNumber": "1610963", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Column Width incorrect for universal hierarchy", "RefUrl": "/notes/1610963 "}, {"RefNumber": "1607637", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web: Spinning Wheel disappears too early in AJAX mode", "RefUrl": "/notes/1607637 "}, {"RefNumber": "1614013", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "OSGi Activation for Visual Composer (BI-KIT) removed", "RefUrl": "/notes/1614013 "}, {"RefNumber": "1613090", "RefComponent": "BI-RA-BICS", "RefTitle": "Incorrect values when you collapse a hierarchical structure", "RefUrl": "/notes/1613090 "}, {"RefNumber": "1507065", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Adding BI Bookmarks to browser favorites in CRM system", "RefUrl": "/notes/1507065 "}, {"RefNumber": "1512555", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "SAP NW BI 7.0: Truncated header text or footer text", "RefUrl": "/notes/1512555 "}, {"RefNumber": "1512464", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC:java.lang.NullPointerException when KM cache is corrupt", "RefUrl": "/notes/1512464 "}, {"RefNumber": "1558368", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Corrections in Support Desktool", "RefUrl": "/notes/1558368 "}, {"RefNumber": "1554104", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Close button on Document browser when you click details", "RefUrl": "/notes/1554104 "}, {"RefNumber": "1532017", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "Order of login modules for the ticket component in the VA", "RefUrl": "/notes/1532017 "}, {"RefNumber": "1527393", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "java.lang.nullpointerexception in support desk tool", "RefUrl": "/notes/1527393 "}, {"RefNumber": "1565207", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Drag and drop in second row inserts new line in first row", "RefUrl": "/notes/1565207 "}, {"RefNumber": "1581167", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Totals row missing after drill op.on hier.key figure struct.", "RefUrl": "/notes/1581167 "}, {"RefNumber": "1585680", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Korrekturen RIG Modification Module", "RefUrl": "/notes/1585680 "}, {"RefNumber": "1547871", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BEx Web 7.30: Corrections in Support Desktool", "RefUrl": "/notes/1547871 "}, {"RefNumber": "1597173", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Backward navigation selectorData<PERSON>rov<PERSON> is already assigned", "RefUrl": "/notes/1597173 "}, {"RefNumber": "1592848", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.x / BOE 4.0 Javascript error in IE9", "RefUrl": "/notes/1592848 "}, {"RefNumber": "1597308", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Incorrect variants displayed when you execute a query", "RefUrl": "/notes/1597308 "}, {"RefNumber": "1538793", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Sorting on local formulas not saved after bookmark or undo", "RefUrl": "/notes/1538793 "}, {"RefNumber": "1592991", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Filter Pane Item overlaps in firefox and Safari", "RefUrl": "/notes/1592991 "}, {"RefNumber": "1503243", "RefComponent": "BW-BEX-ET-WJR-DIA", "RefTitle": "Loading animation disappears when submitting dialogs", "RefUrl": "/notes/1503243 "}, {"RefNumber": "1564517", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Invalid member selection on info object with conversion exit", "RefUrl": "/notes/1564517 "}, {"RefNumber": "1527184", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Termination during export to PDF or Excel with mod modules", "RefUrl": "/notes/1527184 "}, {"RefNumber": "1563643", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Modules RIG - Javascript error during export", "RefUrl": "/notes/1563643 "}, {"RefNumber": "1534083", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Nullpointer when time-dependent hierarchies are loaded", "RefUrl": "/notes/1534083 "}, {"RefNumber": "1578941", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java Runtime: issue with pattern selection in ODOC scenario", "RefUrl": "/notes/1578941 "}, {"RefNumber": "1577293", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Java dump: Hierarchical structure and structure axis", "RefUrl": "/notes/1577293 "}, {"RefNumber": "1526210", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Export of external MIMES (Intranet/Internet)", "RefUrl": "/notes/1526210 "}, {"RefNumber": "1565796", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Sorting is disabled when F4 is called on Structural elements", "RefUrl": "/notes/1565796 "}, {"RefNumber": "1540416", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Javadoc adjusted in IRsAxisTuple.getAxisHierarchyInfo()", "RefUrl": "/notes/1540416 "}, {"RefNumber": "1437358", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "NullPointerException due to incorrect template definition", "RefUrl": "/notes/1437358 "}, {"RefNumber": "1533296", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module Copy-Paste: Values pasted at incorrect cell", "RefUrl": "/notes/1533296 "}, {"RefNumber": "1543561", "RefComponent": "BW-BEX-ET-WJR-DIA-OS", "RefTitle": "Display settings are incorrectly set in Open dialog", "RefUrl": "/notes/1543561 "}, {"RefNumber": "1539790", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Exception during export of info field item.", "RefUrl": "/notes/1539790 "}, {"RefNumber": "1425720", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification: User-defined symbols for exception II", "RefUrl": "/notes/1425720 "}, {"RefNumber": "1522108", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Parcer error for hierarchy value with special characters", "RefUrl": "/notes/1522108 "}, {"RefNumber": "1538753", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.x - comments option in context menu", "RefUrl": "/notes/1538753 "}, {"RefNumber": "1538640", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "BiNamespace characteristics not displyed in Assignment colum", "RefUrl": "/notes/1538640 "}, {"RefNumber": "1538289", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "'X' appears in result cells (Calculate Result Set as \"Hide\")", "RefUrl": "/notes/1538289 "}, {"RefNumber": "1536756", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Filter value lost in BACK_TO_PREVIOUS_STATE for DPs >= 2", "RefUrl": "/notes/1536756 "}, {"RefNumber": "1536499", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Safari Browser: Adding bookmark to browser favorites", "RefUrl": "/notes/1536499 "}, {"RefNumber": "1505639", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Input ready cells look bigger than usual cells", "RefUrl": "/notes/1505639 "}, {"RefNumber": "1505289", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Display line breaks or multiple spaces on BW Text Item", "RefUrl": "/notes/1505289 "}, {"RefNumber": "1505221", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Parallel operations of loading and deleting a variant", "RefUrl": "/notes/1505221 "}, {"RefNumber": "1504675", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "NW BI 7.0: Problem with context menu on chart w/ Firefox 3.6", "RefUrl": "/notes/1504675 "}, {"RefNumber": "1503986", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Semantic Text Style of header cells", "RefUrl": "/notes/1503986 "}, {"RefNumber": "1502713", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Error in Opening Selector dialog from an Exception wizard", "RefUrl": "/notes/1502713 "}, {"RefNumber": "1500651", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module Scrolling: some columns not visible", "RefUrl": "/notes/1500651 "}, {"RefNumber": "1499920", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "java.lang.UnsupportedOperationException in ListBox Item", "RefUrl": "/notes/1499920 "}, {"RefNumber": "1498818", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC : Close button displayed in New document dialog window", "RefUrl": "/notes/1498818 "}, {"RefNumber": "1498557", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification Modules RIG - Invoking context menu or snippets", "RefUrl": "/notes/1498557 "}, {"RefNumber": "1498512", "RefComponent": "BW-BEX-ET-WJR-ITM", "RefTitle": "Font color of the tab strip item is incorrect", "RefUrl": "/notes/1498512 "}, {"RefNumber": "1495857", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Analysis modification formatting of rows and columns", "RefUrl": "/notes/1495857 "}, {"RefNumber": "1495754", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "addSharedMessage: convinience method for 700/701 code lines", "RefUrl": "/notes/1495754 "}, {"RefNumber": "1494073", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Missing elements in the XML document for XPath evaluation", "RefUrl": "/notes/1494073 "}, {"RefNumber": "1552647", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Scaling factor reset to default while changing formula", "RefUrl": "/notes/1552647 "}, {"RefNumber": "1561738", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Wrong dropdown box when drag and drop between filter panes", "RefUrl": "/notes/1561738 "}, {"RefNumber": "1561086", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Possible dump by loading a bookmark with an object variable", "RefUrl": "/notes/1561086 "}, {"RefNumber": "1560665", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "Modification RIG Document Content performance enhancement", "RefUrl": "/notes/1560665 "}, {"RefNumber": "1556545", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Case conversion for filter values on compounded char", "RefUrl": "/notes/1556545 "}, {"RefNumber": "1561846", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Wildcard entry for SELECTION OPTION variable in selector", "RefUrl": "/notes/1561846 "}, {"RefNumber": "1534863", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Tooltip for hierarchies in characteristic properties pane", "RefUrl": "/notes/1534863 "}, {"RefNumber": "1534100", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Unexpected filter is applied if key contains special chars", "RefUrl": "/notes/1534100 "}, {"RefNumber": "1532816", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Local formula is not deserialzed correctly (bookmark, undo)", "RefUrl": "/notes/1532816 "}, {"RefNumber": "1528687", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Selection screen range value is validated incorrectly", "RefUrl": "/notes/1528687 "}, {"RefNumber": "1525392", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Planning function: error messages not reset (F4 selector)", "RefUrl": "/notes/1525392 "}, {"RefNumber": "1525226", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Personalization flag in web Java runtime", "RefUrl": "/notes/1525226 "}, {"RefNumber": "1524799", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Selection screen not shown with SET_VARIABLES_STATE command", "RefUrl": "/notes/1524799 "}, {"RefNumber": "1524296", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "List calculation: Normalize acc next result without subtotal", "RefUrl": "/notes/1524296 "}, {"RefNumber": "1524022", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Hierarchy Node with huge number in selector dialog", "RefUrl": "/notes/1524022 "}, {"RefNumber": "1523974", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "java.lang.ClassCastException in ListBox item", "RefUrl": "/notes/1523974 "}, {"RefNumber": "1523854", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "Personalization leads to exception with drop down item", "RefUrl": "/notes/1523854 "}, {"RefNumber": "1523581", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC : Close button displayed in formatted dialog window.", "RefUrl": "/notes/1523581 "}, {"RefNumber": "1517597", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Error in processing of command SET_VARIABLES_STATE", "RefUrl": "/notes/1517597 "}, {"RefNumber": "1521630", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Number of rows in analysis item when paging type is by item", "RefUrl": "/notes/1521630 "}, {"RefNumber": "1520776", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "BIBaseRuntimeException during creation of data provider", "RefUrl": "/notes/1520776 "}, {"RefNumber": "1516784", "RefComponent": "BW-BEX-ET-WJR-ITM-FL", "RefTitle": "NullPointerException for Characteristic Selection", "RefUrl": "/notes/1516784 "}, {"RefNumber": "1513148", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Error occurs for various exception operators and filtering", "RefUrl": "/notes/1513148 "}, {"RefNumber": "1509455", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Empty cells are not ready for input on hierarchical axis", "RefUrl": "/notes/1509455 "}, {"RefNumber": "1507068", "RefComponent": "BW-BEX-ET-WJR-DIA-SL", "RefTitle": "Negative value in Calendar tool is incorrectly processed", "RefUrl": "/notes/1507068 "}, {"RefNumber": "1496495", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Display Hierachy: Context menu expand does not work", "RefUrl": "/notes/1496495 "}, {"RefNumber": "1535651", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Object variables are not restored correctly from a bookmark", "RefUrl": "/notes/1535651 "}, {"RefNumber": "1555612", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Wrong result set size calculation during drill operations", "RefUrl": "/notes/1555612 "}, {"RefNumber": "1451171", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Path entries are displayed twice", "RefUrl": "/notes/1451171 "}, {"RefNumber": "1552272", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Modal template dialog can not be closed", "RefUrl": "/notes/1552272 "}, {"RefNumber": "1548255", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Calculate Based On Results Of Former Details Calculations is", "RefUrl": "/notes/1548255 "}, {"RefNumber": "1548840", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "Condition state toggling: usability enhancement", "RefUrl": "/notes/1548840 "}, {"RefNumber": "1537922", "RefComponent": "BW-BEX-ET-WJR-ITM-AN", "RefTitle": "context menu with reference to selector dataprovider fails", "RefUrl": "/notes/1537922 "}, {"RefNumber": "1174124", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Condition not used with active axis hierarchy", "RefUrl": "/notes/1174124 "}, {"RefNumber": "1546012", "RefComponent": "BW-BEX-ET-WJR-DIA-FM", "RefTitle": "Java ClassCast exception in query (writeStateIntoDom)", "RefUrl": "/notes/1546012 "}, {"RefNumber": "1545679", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Result with hierarchy symbol", "RefUrl": "/notes/1545679 "}, {"RefNumber": "1545545", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "ODOC: Broadcasting to MHTML displays all columns in doc item", "RefUrl": "/notes/1545545 "}, {"RefNumber": "1548704", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "RRI:open mode REPLACE_WEB_APPLICATION opens a new window", "RefUrl": "/notes/1548704 "}, {"RefNumber": "1536476", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Web template execution recording incorrect object name", "RefUrl": "/notes/1536476 "}, {"RefNumber": "1500477", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "SDT: BI JAVA and ABAP Compatibility check", "RefUrl": "/notes/1500477 "}, {"RefNumber": "1538014", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Number of decimal resets to default while changing formula", "RefUrl": "/notes/1538014 "}, {"RefNumber": "1534743", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "SAP NW BI JAVA 7.30 / BOE 4.0 Excel export freeze web app", "RefUrl": "/notes/1534743 "}, {"RefNumber": "1531694", "RefComponent": "BW-BEX-ET-WJR-RT", "RefTitle": "Adding Bookmark to Browser Favorites in FPN brings wrong URL", "RefUrl": "/notes/1531694 "}, {"RefNumber": "1537039", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "RTL(Right-to-Left) display not supported for PDF,EXCEL & CSV", "RefUrl": "/notes/1537039 "}, {"RefNumber": "1544415", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module CopyPaste:pasting more cells than the space available", "RefUrl": "/notes/1544415 "}, {"RefNumber": "1527186", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Export to Excel: \"0.0%\" value converted to \"0,0\"", "RefUrl": "/notes/1527186 "}, {"RefNumber": "1504676", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module Copy-Paste: input values not taken over by cells", "RefUrl": "/notes/1504676 "}, {"RefNumber": "1521831", "RefComponent": "BW-BEX-ET-WJR-DIA-VA", "RefTitle": "Pasting too many values in input text field for variable", "RefUrl": "/notes/1521831 "}, {"RefNumber": "1501042", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Module Scrolling: input ready cells not changeable", "RefUrl": "/notes/1501042 "}, {"RefNumber": "1505718", "RefComponent": "BW-BEX-ET-WJR-ODOC", "RefTitle": "Export to PDF in ODOC: Font size is scaled down", "RefUrl": "/notes/1505718 "}, {"RefNumber": "1505375", "RefComponent": "BW-BEX-ET-WJR-EP", "RefTitle": "Missing translations for default BW portal content", "RefUrl": "/notes/1505375 "}, {"RefNumber": "1499814", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Personalization \"Key already exists the entry was ignored\"", "RefUrl": "/notes/1499814 "}, {"RefNumber": "1025307", "RefComponent": "BW", "RefTitle": "Composite note for BW 7.00 / BW 7.01 performance: Reporting", "RefUrl": "/notes/1025307 "}, {"RefNumber": "1013369", "RefComponent": "BW", "RefTitle": "SAP NetWeaver 7.0 BI - intermediate Support Packages", "RefUrl": "/notes/1013369 "}, {"RefNumber": "1072576", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Frequently Asked Questions: BI Java Support Packages/patches", "RefUrl": "/notes/1072576 "}, {"RefNumber": "1101143", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Collective note: BEx Analyzer performance", "RefUrl": "/notes/1101143 "}, {"RefNumber": "1003344", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Various problems with variables and hierarchies", "RefUrl": "/notes/1003344 "}, {"RefNumber": "1030279", "RefComponent": "BW-BEX", "RefTitle": "Reports with very large result sets/BI Java", "RefUrl": "/notes/1030279 "}, {"RefNumber": "375631", "RefComponent": "BW", "RefTitle": "Which Support Package is recommended for BW-customers?", "RefUrl": "/notes/375631 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}