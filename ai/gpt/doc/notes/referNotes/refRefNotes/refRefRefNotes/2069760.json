{"Request": {"Number": "2069760", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 253, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017946682017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002069760?language=E&token=8C5B2FE2D310F803FE0F04D42F1F8EAD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002069760", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2069760"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.02.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-LNX-OLNX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle Linux"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Linux", "value": "BC-OP-LNX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-LNX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle Linux", "value": "BC-OP-LNX-OLNX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-LNX-OLNX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2069760 - Oracle Linux 7.x SAP Installation and Upgrade"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to install SAP software on Oracle Linux 7.x.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle Linux 7, Oracle Linux, Oracle Unbreakable Enterprise Kernel, ULN, UEK, OL</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to use of SAP server software on Oracle Linux 7.x.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note does not replace the installation guideline for the SAP software or the installation guideline for Oracle Linux. This note deals only with points that affect the combination of operating system, hardware, Oracle database software and SAP software.</p>\r\n<p><strong>Environment</strong></p>\r\n<p>This document deals with the installation and configuration of SAP server software on Oracle Linux 7. In the following, \"OL 7\" means any version of Oracle Linux 7.0 and higher unless specified otherwise. If Oracle Linux&#160;7 is listed as supported in the SAP PAM this means that any update release of OL 7, for example OL&#160;7 Update&#160;1 (OL 7.1), can be used as well.</p>\r\n<p><strong>Supportability prerequistes</strong></p>\r\n<p>In order&#160;to ensure that&#160;your SAP system&#160;is fully supported on Oracle Linux 7, the following prerequisites must be fulfilled (this list is not exhaustive, other notes may apply):</p>\r\n<ul>\r\n<li>You must purchase an Oracle Linux Premier Support subscription from Oracle for Oracle Linux 7. Please visit <a target=\"_blank\" href=\"http://linux.oracle.com\">http://linux.oracle.com</a>&#160;for more information or contact your local Oracle sales representative.</li>\r\n<li>Your machine must be able to retrieve additional software and updates from the \"Unbreakable Linux Network\" (ULN) either directly via HTTP or an ULN proxy.</li>\r\n<li>You&#160;must use hardware that is certified for SAP on Linux by your hardware vendor. See the respective notes on certified hardware, which are listed in SAP note <a target=\"_blank\" href=\"/notes/171356\">171356</a>.</li>\r\n<li>You can use any Linux kernel version shipped by Oracle for Oracle Linux 7:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Unbreakable Enterprise Kernel Release 3 (UEK R3), starting with 3.8.13, based on mainline Linux version 3.8. This is the default kernel.</p>\r\n<p style=\"padding-left: 60px;\">Red Hat Compatible Kernel (RHCK), based on mainline Linux version 3.10.</p>\r\n<ul>\r\n<li>The original glibc version shipped with OL 7.0 is NOT sufficient for SAP&#160;(see Oracle Linux Security Advisory ELSA-2014-2023). The minimum required version of glibc for running SAP software on OL 7 is glibc-2.17-55.0.4.el7_0.3. Higher versions of glibc built for OL7 can be used as well.</li>\r\n<li>It is recommended to always install the latest patches provided by Oracle for all OL 7 packages installed on the system.</li>\r\n</ul>\r\n<p><strong>Supported Hardware Platform</strong></p>\r\n<p>Certification of specific SAP products on specific versions of Oracle Linux notwithstanding, the following hardware platforms are certified for use of SAP software on Oracle Linux:</p>\r\n<p style=\"padding-left: 30px;\">x86_64 (64bit Intel or AMD processors)</p>\r\n<p><strong>Supported File Systems</strong></p>\r\n<p>In general any file system supported by&#160;Oracle for OL 7 can also be used for SAP installations. This currently includes XFS, EXT3 and EXT4. Since the database vendors can limit the support of their database to certain file systems, please check with your&#160;database vendor if the file system you plan to use is also supported by them.</p>\r\n<p><strong>Upgrading from a previous Oracle Linux release to OL 7</strong></p>\r\n<p>In-place upgrades from OL 6 to OL 7&#160;are supported, however you must have at least OL 6.5 installed on the system if you plan to upgrade to OL 7. Direct OS upgrades from OL 5 to OL 7 are not supported. Please see&#160;Oracle Linux Installation Guide for Release 7 (<a target=\"_blank\" href=\"http://docs.oracle.com/cd/E52668_01/\">http://docs.oracle.com/cd/E52668_01/</a>) for the list of supported use-cases for in-place upgrades to OL7.</p>\r\n<p>Before starting the upgrade&#160;you have to&#160;make sure that all SAP and DB instances running on the server have been updated to a level that is supported on OL 7 and that a working backup of the server exists. All SAP and DB instances running on the server must be stopped, and all file systems belonging to the SAP installation (/usr/sap, /sapmnt, /&lt;DB&gt;) must be unmounted before starting the OS upgrade procedure to avoid damage to the SAP installation during the OS upgrade.</p>\r\n<p><strong>Installing Oracle Linux 7</strong></p>\r\n<p>Install the operating system as described in the Oracle Linux Installation Guide for Release&#160;7&#160;(available at <a target=\"_blank\" href=\"http://docs.oracle.com/cd/E52668_01/\">http://docs.oracle.com/cd/E52668_01/</a>) using the following guidelines:</p>\r\n<ul>\r\n<li>\r\n<p style=\"text-align: justify;\">Select \"English\" as the installation and system language.</p>\r\n</li>\r\n<li style=\"text-align: justify;\">\r\n<p>You can change the keyboard layout to your local preference in the \"Keyboard\" configuration screen.</p>\r\n</li>\r\n<li style=\"text-align: justify;\">Manually partitioning the disks is strongly recommended to reserve space for the application.</li>\r\n<li>Select the correct timezone and make sure the date and time are set correctly in the \"Date &amp; Time\" configuration screen. If a local NTP server is available you should also configure it in this screen.</li>\r\n<li>In the \"Network &amp; Hostname\" configuration screen enter the short name (e. g. \"sapserver1\") and not the fully qualified hostname (e. g. \"sapserver1.example.com\"). How to map the short hostname to the fully qualified hostname is described below under \"Setting the Hostname\".</li>\r\n<li>In the \"Software Selection\" screen it is recommended to select the software groups listed in the following at a minimum for an SAP server machine:</li>\r\n</ul>\r\n<p style=\"padding-left: 90px;\">Infrastructure Server<br />Large Systems Performance<br />Network File System Client<br />Performance Tools<br />Compatibiliy Libraries</p>\r\n<ul>\r\n<li>Some additional packages are required for running SAP software on OL 7 which can't be selected during the interactive OS installation process:</li>\r\n</ul>\r\n<p style=\"padding-left: 90px;\">uuidd (see SAP Note <a target=\"_blank\" href=\"/notes/1391070\">1391070</a>)</p>\r\n<p>&#160;</p>\r\n<p><strong>Configuration changes required after the initial OS installation</strong></p>\r\n<p>If you leave the firewall enabled, you need to open up the ports for your SAP product. The ports that have to be opened are listed during theinstallation of the SAP software but can also be found in /etc/services after it is installed. See the Oracle Linux 7 Security Guide for more information on how to configure the firewall.</p>\r\n<p>Oracle Linux uses SELinux technology for additional security which is enabled by default. Because several components of an SAP server system (like the installation tools or some underlying RDBMS) are not aware of SELinux, we recommend setting SELinux to \"Permissive\" mode for the time being so that these components won't break. We don't recommend setting it to \"Disabled\" as this would require relabeling the whole filesystem if you want to enable it again at a later point for additional security when these components are made compatible or suitable procedures exist to make them function properly with SELinux. You can change SELinux settings by editing /etc/sysconfig/selinux for future boot processes. On a running system, you can switch between \"Enforcing\" and \"Permissive\" modes using the command \"setenforce\".</p>\r\n<p>You have to register the system on the Oracle Linux ULN Portal or a local ULN Proxy server to retrieve update packages for your machine. It is recommended that you update all packages (including kernel and glibc) to the latest version available in the official OL 7 channels after the first OS installation and at regular intervalls later on.</p>\r\n<p><strong>Setting the Hostname</strong></p>\r\n<p>Ensure that the system hostname is set to the short name as described above, i.e. both commands \"hostname\" and \"hostname -s\" must return the hostname without domain, \"hostname -f\" must return the fully qualified hostname and domain:</p>\r\n<p style=\"padding-left: 30px;\"># hostname<br />sapserver1</p>\r\n<p style=\"padding-left: 30px;\"># hostname -s<br />sapserver1</p>\r\n<p style=\"padding-left: 30px;\"># hostname -f<br />sapserver1.example.com</p>\r\n<p>To set the hostname permanenty, please use the \"hostnamectl\" command. Also set up /etc/hosts so that it is configured similar to the following example:</p>\r\n<p style=\"padding-left: 30px;\"># cat /etc/hosts<br />127.0.0.1&#160;&#160;&#160;&#160; localhost.localdomain localhost<br />***********&#160;&#160; sapserver1.example.com sapserver1<br />(any additional hosts should be added after these two lines)</p>\r\n<p>It is important that the fully qualified domain name is in the second column, followed by any alias names and that the hostname of the machine is not associated with the IP address 127.0.0.1.</p>\r\n<p>If you entered the fully qualified hostname during installation you can run the command 'hostname &lt;shorthostname&gt;' to set the short hostname without having to reboot the system. See SAP Note <a target=\"_blank\" href=\"/notes/611361\">611361</a>&#160;for further information about hostname requirements for SAP NetWeaver based systems.</p>\r\n<p><strong>Linux Kernel Parameters</strong></p>\r\n<p>Some Linux kernel parameters have to be adjusted to meet the requirements of SAP software. To do this create a file /etc/sysctl.d/sap.conf with the following content (these are the required minimum values, higher values can be used as well):</p>\r\n<p style=\"padding-left: 30px;\"># SAP settings<br />kernel.sem=1250 256000 100 1024<br />vm.max_map_count=2000000 (see SAP Note <a target=\"_blank\" href=\"/notes/900929\">900929</a>&#160;for more information)</p>\r\n<p><br />Please check SAP Note <a target=\"_blank\" href=\"/notes/941735\">941735</a> for recommendations on how to configure the kernel parameters kernel.shmmax and kernel.shmall and other memory related settings for 64bit systems. Run the command \"sysctl --system\" to activate the modified kernel parameters. You can use the command \"ipcs -l --human\" to check the current limits for shared memory, semaphores and message queues in the Linux kernel.</p>\r\n<p><strong>Process Resource Limits</strong></p>\r\n<p>Some components (e.g. the SAP J2EE engine, Oracle RDBMS software, ...) need to keep a large number of file handles opened simultaneously. To increase the limit of files one process can open at a time for all OS users of the SAP system and DB, please create the file /etc/security/limits.d/99-sap.conf with the following content (these are the recommended minimum values, higher values can be used too):</p>\r\n<p style=\"padding-left: 30px;\">@sapsys&#160;&#160;&#160; &#160;&#160;&#160;&#160; soft&#160;&#160;&#160;&#160; nofile&#160;&#160;&#160; 32800<br />@sapsys&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;hard&#160;&#160;&#160; nofile&#160;&#160;&#160; 32800<br />@&lt;DB group&gt;&#160;soft&#160;&#160;&#160;&#160; nofile&#160;&#160;&#160; 32800<br />@&lt;DB group&gt;&#160;hard&#160;&#160;&#160; nofile&#160;&#160;&#160; 32800</p>\r\n<p>(replace &lt;DB group&gt; with the name of the OS group of the OS database users, e. g. for Oracle &lt;DB group&gt; should be replaced with \"oinstall\" (without the double-quotes))</p>\r\n<p>By default OL 7 limits the number of simultaneous processes for each user (except root) to 1024 via the file /etc/security/limits.d/90-nproc.conf to prevent so called \"fork-bomb\" attacks (see also Oracle MOS note 1626037.1). This can cause problems, for example, when running multiple SAP JAVA application server instances under the same userid.</p>\r\n<p>If you plan to run such a setup on OL 7, please also add the following line in /etc/security/limits.d/99-sap.conf:</p>\r\n<p style=\"padding-left: 30px;\">@sapsys&#160;&#160;&#160; soft&#160;&#160;&#160; nproc&#160;&#160;&#160; unlimited</p>\r\n<p>If you are running the database instance for an SAP system with a large number of dialog instances it might also be necessary to set the \"nproc\" limit to unlimited for the group of the database users as well.</p>\r\n<p>For example for Oracle database&#160;you should also add the following line:</p>\r\n<p style=\"padding-left: 30px;\">@oinstall&#160;&#160;&#160; soft&#160;&#160;&#160; nproc&#160;&#160;&#160; unlimited</p>\r\n<p>Please logout and login all users belonging to these groups and restart all processes running under those users for the settings in the</p>\r\n<p>/etc/security/limits.d/99-sap.conf to take effect.</p>\r\n<p>To ensure that the process resource limits also get adjusted when the SAP system is started via sapcontrol or a web service client (e. g. SAP MMC) make sure to update your SAP system at least to SAP kernel 720 PL 400. See SAP Note <a target=\"_blank\" href=\"/notes/1771258\">1771258</a> for more information.</p>\r\n<p><br /><strong>Installing Additional Software Packages</strong></p>\r\n<p>You can also install or reinstall a package or a package group at a later point after the OS installation with the following commands, provided that your system can access the OL software channels via the officially supported ways (directly with ULN or via ULN Proxy):</p>\r\n<p>For installing individual packages:</p>\r\n<p style=\"padding-left: 30px;\">yum install &lt;package1&gt; [&lt;package2&gt; [&lt; package3&gt; [...]]]</p>\r\n<p>where &lt;package*&gt; are the names of the packages to be installed, e.g.:</p>\r\n<p style=\"padding-left: 30px;\">yum install uuidd</p>\r\n<p>For installing package groups:</p>\r\n<p style=\"padding-left: 30px;\">yum groupinstall '&lt;group1&gt;' ['&lt;group2&gt;' ['&lt; group3' [...]]]</p>\r\n<p>where &lt;group*&gt; are the names of the groups you want to install.</p>\r\n<p>The following yum groups correspond to the groups listed for the interactive installation above:</p>\r\n<p style=\"padding-left: 30px;\">directory-client<br />large-systems<br />network-file-system-client<br />performance<br />compat-libraries</p>\r\n<p>If your system can't use the officially supported ways to access the OL software channels \"yum\" cannot determine the individual packages contained in a package group. In this case, you need to install the individual packages as described above. To find out which packages fulfill a certain requirement or are part of a certain package group, please contact your operating system support.</p>\r\n<p><strong>Additional Notes for Installing SAP Systems</strong></p>\r\n<p>You will&#160;get a warning from the SAP Prerequisite Checker that Oracle Linux 7.x is not supported. You can ignore this warning. Since OL 7 ships with a Linux 3.x kernel some components of the SAP system need to be updated to be able to properly recognize the Linux 3.x kernel. Please see SAP Note <a target=\"_blank\" href=\"/notes/1629558\">1629558</a> for more information.</p>\r\n<p>To be able to use some older SAP NetWeaver releases in an LDAP environment on OL 7/x86_64, you need to install a symbolic link for two libraries, because the SAP binaries used to access LDAP were built against versions of these libraries with a non-standard SONAME:</p>\r\n<p style=\"padding-left: 30px;\">ln -s /usr/lib64/libldap-2.3.so.0 /usr/lib64/libldap.so.199</p>\r\n<p style=\"padding-left: 30px;\">ln -s /usr/lib64/liblber-2.3.so.0 /usr/lib64/liblber.so.199</p>\r\n<p>For this to work you need to have the compat-openldap package installed.</p>\r\n<p><strong>Additional Notes for Installing Oracle Database</strong></p>\r\n<p>For OL 7 it is required to install Oracle Database&#160;release ******** or higher.</p>\r\n<p>Planning the filesystem for Oracle database use SAP note <a target=\"_blank\" href=\"/notes/1114181\">1114181</a>.</p>\r\n<p>When installing Oracle 11gR2 (********) the installer may fail with an error like</p>\r\n<p>&#160;&#160;&#160;&#160;&#160; Error in invoking target 'agent nmhs' of makefile&#160;&#160; '&lt;ORACLE_HOME&gt;/sysman/lib/ins_emagent.mk'. See '&lt;installation log&gt;' for details.</p>\r\n<p>If that happens, please refer to SAP note <a target=\"_blank\" href=\"/notes/2130122\">2130122</a> for further instructions.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-OP-LNX (Linux)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5120534)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5004095)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002069760/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002069760/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002069760/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002069760/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002069760/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002069760/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002069760/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002069760/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002069760/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3408032", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "Oracle Linux: operating system support process", "RefUrl": "/notes/3408032"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2535340", "RefComponent": "BC-INS-UNX", "RefTitle": "ABAP processes of instance  [ABAP: UNKNOWN] did not start after 10:10 minutes.", "RefUrl": "/notes/2535340 "}, {"RefNumber": "2663418", "RefComponent": "BC-OP-LNX", "RefTitle": "Semaphore error  - e=28 semget No space left on device", "RefUrl": "/notes/2663418 "}, {"RefNumber": "1565179", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "SAP software and Oracle Linux", "RefUrl": "/notes/1565179 "}, {"RefNumber": "2015553", "RefComponent": "BC-OP-NT-AZR", "RefTitle": "SAP on Microsoft Azure: Support prerequisites", "RefUrl": "/notes/2015553 "}, {"RefNumber": "2369910", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/2369910 "}, {"RefNumber": "1391070", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux UUID solutions", "RefUrl": "/notes/1391070 "}, {"RefNumber": "2052912", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "SAP Software and Oracle Private Cloud Appliance (PCA)", "RefUrl": "/notes/2052912 "}, {"RefNumber": "405827", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Recommended file systems", "RefUrl": "/notes/405827 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}