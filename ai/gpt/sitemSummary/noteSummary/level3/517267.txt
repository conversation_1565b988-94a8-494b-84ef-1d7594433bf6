SAP Note 517267 outlines additional steps and considerations specific to upgrading to SAP R/3 Enterprise 4.7 on DB2 UDB for z/OS (also known as DB2/390). Here is a summary of the key points from the note:

1. **Applicability**: The note is exclusively for upgrades to R/3 Enterprise 4.7 on the DB2 for z/OS database platform.
   
2. **Caution**: The note is regularly updated, and users are advised to re-read it immediately before starting the upgrade to ensure they have the latest instructions.

3. **General Information**:
   - Supported operating systems for the upgrade are z/OS or Windows only.
   - DB2 attribute DEFINE NO should be set according to SAP Note 423726.
   - For MCI upgrades from 4.6B and 4.6C, refer to SAP Note 569821.
   
4. **Pre-Upgrade Actions**:
   - Delete QCM#PUT tables as elaborated in SAP Note 705629 and repeat this if the upgrade is reset.
   - Configure Network File System (NFS) on z/OS if starting releases are ≤ 4.5B.
   - Create additional catalog indexes (see SAP Note 618675).
   - Implement shared memory for z/OS as directed in SAP Note 326949.
   - Set the environment variable _EDC_PUTENV_COPY=YES for z/OS systems with a specific PTF applied.
   - Grant usage rights on the SYSDEFLT storage group.
   - Modify permissions of 'saposcol' for AIX central instances for start releases ≤ 4.5B.
   - Set profile parameter 'dbs/db2/use_set_data_type = 1' when the start release is ≤ 4.0B.
   - Check partitioned tables and bufferpools configurations according to respective SAP Notes and installation guides.
   
5. **During Upgrade**:
   - Exchange upgrade tools with minimum versions specified in the note.
   - Perform PTF-Check and apply necessary upgrade corrections mentioned in various SAP Notes.
   - Messages in CHECKS.LOG about start profiles (different from instance profiles) can be ignored under specific circumstances (upgrading on z/OS with AIX central instance of start releases ≤ 4.5B).
   
6. **Post-Upgrade Actions**:
   - Grant additional privileges to user SAPR3S for start releases ≤ 4.5B with AIX.
   - Refer to SAP Note 529267 for troubleshooting problems during the upgrade.
   - Install the J2EE Engine as detailed in SAP Note 534949 after the upgrade.

The note emphasizes the importance of referring to linked SAP Notes for detailed information on specific tasks and adjustments. Users are also instructed to ensure they are working with the most current versions of the upgrade tools and SAP Notes to avoid any problems during the upgrade process.