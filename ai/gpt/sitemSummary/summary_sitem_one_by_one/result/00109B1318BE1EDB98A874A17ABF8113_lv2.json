{"guid": "00109B1318BE1EDB98A874A17ABF8113", "sitemId": "SI06: ACM_Tmp_Lots_Draft_Contracts", "sitemTitle": "S4TWL - ACM Temporary Lots for Draft Contracts", "note": 3086524, "noteTitle": "3086524 - S4TWL - ACM Temporary Lots for Draft Contracts", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<p>A system upgrade/conversion is about to be done and the source system is one of the following releases: -</p>\n<ul>\n<li>ACM 4.0</li>\n<li><span>ACM on SAP S/4HANA 1709 - All releases</span></li>\n<li><span>ACM on SAP S/4HANA 1809 - FPS00</span></li>\n<li><span>ACM on SAP S/4HANA 1809 - FPS01</span></li>\n<li><span>ACM on SAP S/4HANA 1809 - FPS02</span></li>\n<li><span>ACM on SAP S/4HANA 1909 - FPS00</span></li>\n<li><span>ACM on SAP S/4HANA 1909 - FPS01</span></li>\n<li><span>ACM on SAP S/4HANA 1909 - FPS02</span></li>\n<li><span>ACM on SAP S/4HANA 2020 - FPS00</span></li>\n</ul>\n<p><span>The target system is 'ACM on SAP S/4HANA 2020 FPS01' or a higher release.</span></p>\n<p><span>Post-conversion/upgrade, dumps occurs because of the existence of multiple active BAdI implementations of the BAdI definition<br/>BADI_CPE_FE_RED_DATE_ROUTINE for the same filter value '9999999'. This prevents execution of various ACM transactions<br/>and prevents successful execution of the SDM /ACCGO/CL_SDM_ACM_V_SDMNPEL.</span></p>\n<p><span><strong>Important Note</strong></span>: -</p>\n<ul>\n<li>The manual instruction in this note must only be implemented after all the productive activities in the system have been<br/>halted in preparation for the conversion/upgrade. The changes need to be performed before any workbench locks are made as part of the upgrade activities.</li>\n<li>The transport-request in which the implementation of this note is captured must be released to all follow-on systems to <br/>prevent occurence of this issue in those systems.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Pre-upgrade/conversion step for upgrade/conversion to ACM on S/4HANA 2021 FPS01 or higher releases</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This step is needed to prevent the occurrence of dumps because of the existence of multiple BAdI implementations of the BAdI definition <br/>BADI_CPE_FE_REF_DATE_ROUTINE for the same filter value '9999999'.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ul>\n<li>Run the steps mentioned in SAP Note - 3094714- S4TC ACM Pre-Checks for Data Migration of /ACCGO/T_NPELOTS table in the source system</li>\n<li>The manual instruction in this note must only be implemented after all the productive activities in the system have been<br/>halted in preparation for the conversion/upgrade.</li>\n<li>The transport-request in which the implementation of this note is captured must be released to all follow-on systems to <br/>prevent occurence of this issue in those systems.</li>\n<li>Please implement the manual correction instructions attached to this note.</li>\n</ul>", "noteVersion": 9, "refer_note": [], "activities": [{"Activity": "Customizing / Configuration", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}