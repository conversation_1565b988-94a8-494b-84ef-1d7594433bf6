SAP Note 397105 addresses a specific issue where a system dump with the error CALL_FUNCTION_NOT_FOUND occurs while adjusting the accrual/deferral period of flows upon giving notice on an OTC (Over The Counter) interest rate instrument. This problem is caused by the missing function module TB_FLOW_CHANGE_FOR_NOTICE, which should handle such changes.

Upon investigation, the root cause is found to be a misplacement of the function module. In standard Release 4.6C, the function module TB_FLOW_CHANGE_FOR_NOTICE was created in include LTB06U04. However, in Release CFM 1.0, due to the pre-existence of another function module (TB_CONVERT_VTBFHAPO_VTBBEWE) in include LTB06U04, the TB_FLOW_CHANGE_FOR_NOTICE was placed in include LTB06U13 instead. As a result, the system fails to locate the required function module, leading to the dump error.

To resolve this issue, customers are instructed to import Support Package SAPKIPBI04. For those who have already imported Support Package SAPKIPBI03, there is an alternative solution provided through SAPserve3 transport note 0397105, which can be found in the specified directory. Moreover, the note references SAP Note 13719, implying that customers should take into consideration the information provided in that note when implementing the suggested solution.