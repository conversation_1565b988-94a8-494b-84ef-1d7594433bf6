SAP Note 1610295 addresses the issue that arises when trying to upgrade from SAP NetWeaver version 7.0x, which includes BI_CONT 706, to NetWeaver 7.30. The problem is that the maintenance optimizer (MOPZ) either suggests the wrong BI_CONT upgrade package (SAPK-735AHINBICONT for BI_CONT 735) or displays an error due to the unavailability of the appropriate package for BI_CONT 736. This happens because although BI_CONT 706 is equivalent to BI_CONT 736, the latter has not been released for customer use yet.

Key points from the note:

- Upgrading from NetWeaver 700 with BI_CONT 706 to NetWeaver 730 is not supported until the proper BI_CONT 736 upgrade package is available.
- SAP's recommendation is to wait for the BI_CONT 736 package to be released before proceeding with the upgrade.
- For testing purposes, an upgrade involving the BI_CONT 735 package can be considered.
- Manual intervention is required in the Solution Manager System Landscape (SMSY) to change the assignment from "Business Intelligence" to "Application Server ABAP." This is important because incorrect assignments can lead to issues in subsequent MOPZ transactions (as described in Note 1610201).
- During the maintenance transaction with MOPZ, make sure it recognizes the system as "Application Server ABAP" and does not include BI_CONT packages in the download basket.
- Instructions for upgrading to BI_CONT 735 and the subsequent post-upgrade steps are provided, referring to Notes 1484437 and 1578396. During the upgrade binding phase (BIND_PATCH), one should include all available support packages for BI_CONT 735.
- Post-upgrade, the user should revert changes made in SMSY and if BI_CONT 736 becomes available, proceed with an add-on update as per Note 1578396.

In short, SAP Note 1610295 provides guidance on how to cope with the current limitation around upgrading to NetWeaver 730 with BI_CONT 706 due to the unavailability of BI_CONT 736. It gives detailed steps on how to manually adjust the SMSY, proceed with MOPZ under certain conditions, and carry out the upgrade to NetWeaver 730 either with BI_CONT 735 temporarily or by waiting for the release of BI_CONT 736.