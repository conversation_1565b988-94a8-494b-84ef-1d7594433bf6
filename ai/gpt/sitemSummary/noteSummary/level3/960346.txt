SAP Note 960346 addresses an issue where the BIA (Business Intelligence Accelerator) index for an InfoCube is deleted upon deletion of the InfoCube's contents, either manually or through a process chain. Prior to Support Package Stack 9 (SPS9), deleting an InfoCube's contents would result in the BIA index being completely removed, requiring a manual re-setup of the index.

With the introduction of SPS9, the behavior changes such that the BIA index is no longer deleted. Instead, only the data within the BIA index's fact tables and dimension indexes is removed (TRUNCATE operation), leaving the BIA index status as "Active" and "Filled." This improvement ensures that after new data is loaded into the InfoCube and a rollup is performed, the new data can be loaded into the BIA index seamlessly, without the need for reinitializing the index.

The solution provided in this note advises clients to update to Support Package 09 for SAP NetWeaver 2004s BI (corresponding to BI Patch09 or SAPKW70009). Additional information about the contents and details of this Support Package is available in SAP Note 0914303 titled "SAPBINews BI 7.0 SP09". This note outlines the features and enhancements included in the SP09.

Note 960346 clarifies that the actual function to prevent the deletion of BIA indexes during InfoCube content removal cannot be provided via a simple source code correction attached to the note. Instead, the required changes are included in the mentioned Support Package. Clients are recommended to look out for the release of SAP Note 0914303 which will signal when the Support Package is available for customer use. Prior to the official release, any available notes may be preliminary versions and will indicate as such in their short text.