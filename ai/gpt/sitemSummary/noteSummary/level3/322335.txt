SAP Note 322335 addresses an issue with correct stock values representation on storage locations in user-defined (customer-defined) information structures of type D. Standard SAP analyses contain a correction function to ensure accurate stock value reporting, but this function is not automatically available for custom information structures.

Key Points of SAP Note 322335:
- The issue explained in Note 322335 is that the required correction function to adjust stock values on storage locations is not available in custom information structures, which can lead to incorrect representation of these values in analyses.
- A custom method must be created in SAP Customizing, using a copied FORM routine KORREKTUR_BESTANDSWERT from report RMCB01F0 as a template, to correct stock values.
- Field names of key figures may need to be adjusted, and algorithms could require modifications.
- Individual support can be sought through an SAP consultant.
- An example is provided with info structure S888, which does not update automatically and contains characteristics such as Plant (WERKS), Storage Location (LGORT), Material (MATNR), and key figures like stock quantity, stock value, receipt value, issue value, receipt quantity, and issue quantity.
- To test the custom method, breakpoints can be set at the beginning and end of the new routine.
- The note advises that correct values can only be ensured if the selection of storage locations is not limited when running analyses; the storage location field should be left empty.
- To select by storage location, the standard analysis report must be modified since it cannot be handled within Customizing.
- There are steps described for copying and adjusting the relevant programs into the customer namespace, ensuring that custom changes are not lost when standard analysis is regenerated.

This note is intended to guide users on how to handle stock values in custom info structures by creating and implementing custom enhancements that mimic the functionality found in standard SAP analyses.