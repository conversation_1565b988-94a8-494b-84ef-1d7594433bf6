SAP Note 1009987 addresses a specific issue encountered when executing a query on a MultiProvider, where the MultiProvider accesses only a subset of the data from one or more of its part providers due to a compounding problem. This issue involves characteristics within a MultiProvider that do not match with characteristics in a part provider.

The note explains that when a compounded characteristic (e.g., characteristic C compounded to characteristic V) is present in the MultiProvider but not in a part provider, the part provider returns records where the characteristic has an initial key (SID of '0'). However, the complication arises with compounded characteristics. If characteristic V is restricted in the query to a non-initial value and C has a compounding issue, the part provider returns records with V equal to the restricted value and C as initial, but due to stricter consistency requirements for SAP NetWeaver 2004s, especially in planning applications, the data manager must return correct SIDs for compounded characteristics.

The note gives examples with specific InfoObjects (0COSTCENTER, 0CO_AREA, and 0COSTELMNT) to illustrate differences in query behavior between SAP Release 3.x and Release 7.x. It describes how PCENTER would show incorrect data under certain circumstances in Release 3.x, but in Release 7.x, the data is delivered accurately, reflecting the importance of having the correct compounded keys.

To address the issue, the solution provided by the SAP Note advises checking the query definition or adjusting the hierarchy for the characteristic with a compounding problem. Additionally, for SAP NetWeaver 2004s, the note recommends checking whether the selection nodes contain the '##' leaf for all 3.x queries with node selections for characteristics with a compounding issue. This ensures data integrity when performing selections in queries.