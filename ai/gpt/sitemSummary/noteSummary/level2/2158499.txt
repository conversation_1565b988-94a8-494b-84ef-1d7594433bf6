SAP Note 2158499 addresses a performance issue in the execution of rapid replenishment planning (transaction WRP1R), which utilizes Core Data Services (CDS). The note identifies that updating database statistics for certain tables before invoking CDS can lead to performance gains, specifically for systems using the SYBASE (now known as SAP ASE) database.

To summarize, the SAP Note outlines:

- The **Symptom**: Users are experiencing performance issues when running rapid replenishment planning (transaction WRP1R), which uses CDS. It suggests that updating database statistics for involved tables can improve performance on systems running on a SYBASE database.

- **Other Terms**: Keywords related to the note are WRP1, WRP1R, RWRPLPRO, RWRPLRRP, ISR_APPL_RRPL, performance, runtime, data volume, SAP ASE, and Sybase ASE.

- **Reason and Prerequisites**: The note applies to users utilizing rapid replenishment planning in conjunction with a SYBASE database system.

- **Solution**: The note recommends that users manually implement the corrections or use the Note Assistant before importing the specified support packages to enhance performance.

The note does not provide any additional reference materials, indicating that the information contained is self-contained and focused solely on addressing the specific performance issue at hand.