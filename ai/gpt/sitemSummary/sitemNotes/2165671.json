{"Request": {"Number": "2165671", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 360, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002165671?language=E&token=79E6DAB6B9B99FDEFF335589704A1112"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002165671", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002165671/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2165671"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 35}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Documentation error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "For Checking"}, "ReleasedOn": {"_label": "Released On", "value": "22.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-EWM-DLP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Delivery Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Extended Warehouse Management", "value": "SCM-EWM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-EWM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Delivery Processing", "value": "SCM-EWM-DLP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-EWM-DLP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2165671 - SAP EWM 9.3 Corrections to Online Documentation"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p style=\"padding-left: 30px;\">Corrections to the online documentation are required.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Library, BI documentation,&#160;documentation for SOA services,&#160;help center documentation, Extended Warehouse Management</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>See below for a list of chapters that must be changed or added (sorted by subject area).</p>\r\n<p><strong>SAP EWM 9.3 SP20:</strong></p>\r\n<p><strong>Quality Management (QM)</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>Quality Inspection</strong></p>\r\n<p style=\"padding-left: 60px;\"><strong>Inspection Document Summaries</strong></p>\r\n<p style=\"padding-left: 60px;\">See the attached PDF file of this new chapter.</p>\r\n<p><strong>Cross-Docking</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>Transportation Cross-Docking (TCD)</strong></p>\r\n<p style=\"padding-left: 30px;\">In the&#160;Restrictions section, the bullet point \"In a supply chain in TCD, only the final place of destination can be located domestically.\" should read \"Non-domestic locations in the supply chain in TCD can only be supported if they are the final place of destination.&#8221;</p>\r\n<p><strong>**************************************************</strong></p>\r\n<p><strong>SAP EWM 9.3 SP16:</strong></p>\r\n<p><strong>Putaway and Stock Removal Strategies</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>Capacity Check</strong></p>\r\n<p style=\"padding-left: 30px;\">See the attached PDF file with a revised version of this chapter.</p>\r\n<p><strong><strong><strong><strong><strong>***********************************************</strong></strong></strong></strong></strong></p>\r\n<p><strong><strong><strong><strong>SAP EWM 9.3 SP15:</strong></strong></strong></strong></p>\r\n<p><strong>EH&amp;S Services in Extended Warehouse Management</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>Dangerous Goods Management</strong></p>\r\n<p style=\"padding-left: 30px;\">In the Note in the Integration section, the Customizing path should be changed to \"Customizing for Extended Warehouse Management under Goods Issue Process &gt; Outbound Delivery &gt; Route Determination\".</p>\r\n<p style=\"padding-left: 30px;\">The formatting of the Note should be changed to the following:</p>\r\n<p style=\"padding-left: 30px;\">You make the settings for this in Customizing for Extended Warehouse Management under Goods Issue Process &gt; Outbound Delivery &gt; Route Determination and Extended Warehouse Management &gt; Business Add-Ins (BAdIs) for Extended Warehouse Management &gt; Goods Issue Process &gt; Transportation Management &gt; Route Determination.</p>\r\n<p><strong>Putaway and Stock Removal Strategies</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>Strategy: Near Fixed Picking Bin</strong></p>\r\n<p style=\"padding-left: 30px;\">In the third paragraph, the sentence \"If it does not find an empty bin, it searches to the right of the fixed storage bin and then to the left in the same aisle, before searching in the adjoining aisles.\" should be changed to \"If it does not find an empty bin, it searches to the right of the fixed storage bin and then to the left in the same aisle.\"</p>\r\n<p><strong>Archiving in Extended Warehouse Management (SCM-EWM)<label for=\"star-favorite-button\" title=\"Add to Favorites\"></label></strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>Archiving Value-Added Service Orders (SCM-EWM-VAS)</strong></p>\r\n<p style=\"padding-left: 30px;\">In the bullet point&#160;\"Also for Qty Discrepancies\", the sentence \"If you do not set this indicator, only value-added service orders which have database status completed with quantity deviations are archived.\" should be changed to \"If you do not set this indicator, only value-added service orders which have database status completed without quantity deviations are archived.\"</p>\r\n<p><strong><strong><strong><strong>***********************************************</strong></strong></strong></strong></p>\r\n<p><strong><strong><strong>SAP EWM 9.3 SP14:</strong></strong></strong></p>\r\n<p><strong>Integration of Production Supply (PP) in Extended Warehouse Management (EWM)</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong>Production Supply Area (PSA)</strong></p>\r\n<ul>\r\n<li>The figure should be removed.</li>\r\n<li>The bullet point \"One storage bin can be used by multiple PSAs.\" should be removed</li>\r\n<li>The bullet point \"You can assign multiple PSAs to a storage bin.\" should be changed to \"If you are working with production material requests, you can assign only one PSA to a storage bin. If you are not working with production material requests, you can assign multiple PSAs to a storage bin.\"</li>\r\n</ul>\r\n<p><strong>Product Warehouse Task</strong></p>\r\n<p>The sentence \"When you confirm a product WT, it consists of either an item with the source destination bin or destination storage bin, or it consists of more than one item in the case of partial confirmations.\" should read \"When you confirm a product WT, it consists of either an item with the source storage bin or destination storage bin, or it consists of more than one item in the case of partial confirmations.\"</p>\r\n<p style=\"padding-left: 30px;\"><strong>Warehouse Order Creation</strong></p>\r\n<p style=\"padding-left: 30px;\">Connecting a Fully Automated Warehouse as a Black Box</p>\r\n<p style=\"padding-left: 30px;\">Change the&#160;<em>Process</em>&#160;&gt;&#160;<em>Handling Differences</em>&#160;section to the following:</p>\r\n<p style=\"padding-left: 30px;\">If differences occur in the storage type managed by the non-SAP system while processing a warehouse task, the non-SAP system reports these differences to EWM using the message type /SCWM/WMTOCO.</p>\r\n<p style=\"padding-left: 30px;\"><strong>Note</strong>:&#160;<br />The non-SAP system must not send product movements within the storage type managed by the non-SAP system, because EWM only manages the summary stocks for this storage type.</p>\r\n<p><strong>Continuous Physical Inventory Procedures</strong></p>\r\n<p>In the section \"Low Stock Check (Storage-Bin-Specific)\", the bullet point \"Mixed storage is not permitted for the relevant storage type, in other words, only one quant exists.\" should be changed to \"Only one quant exists in the storage bin.\"</p>\r\n<p>In the section \"Putaway Physical Inventory (Storage-Bin-Specific)\", the bullet point \"Mixed storage is not permitted for the relevant storage type, in other words, only one quant exists.\" should be changed to \"The WT is for putaway of exactly one quant.\"</p>\r\n<p><strong>Shipping and Receiving</strong></p>\r\n<p>Chapter \"Complex Loading and Unloading\"</p>\r\n<ul>\r\n<li>In the section \"Loading\", the following text should be removed from the chapter:</li>\r\n</ul>\r\n<p class=\"p\" style=\"padding-left: 60px;\">Alternatively, the creation of the loading WT can be triggered automatically by a status change when the TU arrives at the door. This occurs via a PPF action (PPF = Post Processing Framework).</p>\r\n<p class=\"p\" style=\"padding-left: 60px;\">The system creates loading WTs with the default destination storage bin&#160;Door&#160;. If a WT is confirmed at a door where a TU is positioned, the system automatically changes the destination storage bin in the WT to&#160;TU&#160;.&#160;Ad hoc movements&#160;are the exception to this rule.</p>\r\n<p class=\"p\" style=\"padding-left: 60px;\">You can use loading rules to influence the time of loading. In this way, you can specify, for example, that loading is not to start until 24 hours after you have confirmed the WT with the destination storage bin&#160;Staging Bay&#160;. You choose loading rules when defining staging areas.</p>\r\n<ul>\r\n<li>In the section \"Unloading\" the text \"goods issue\" should be changed to \"goods receipt\"</li>\r\n</ul>\r\n<p><strong><strong>***********************************************</strong></strong></p>\r\n<p><strong>SAP EWM 9.3 SP08:</strong></p>\r\n<p><strong><strong>Enterprise Services in SAP Extended Warehouse Management</strong></strong></p>\r\n<p style=\"padding-left: 30px;\"><strong><strong>TM-EWM Integration</strong></strong></p>\r\n<p class=\"p\" style=\"padding-left: 30px;\">\"Software Component Version ESM EWM 9.10\" should read \"Software Component Version&#160;EWM 9.10\"</p>\r\n<p><strong>Layout-Oriented Storage Control</strong></p>\r\n<p>The note \"The layout-oriented storage control only operates with handling units. One exception is the process display with picking or identification points.\" should be altered to the following:</p>\r\n<p>The layout-oriented storage control only operates with handling units.</p>\r\n<p>The following scenarios do not need handling units to trigger the process, but they still require HUs to run the process:</p>\r\n<ul>\r\n<li>In the identification point scenario, the initial warehouse task for the process can be created without an HU. However, you have to specify the destination HU when you confirm the warehouse task .</li>\r\n<li>In the pick point scenario, the pick task can be created without entering a source HU. The system determines the required HU during the warehouse task creation and saves it in the picking warehouse task. The system also creates an HU warehouse task to bring the source HU to the pick point.</li>\r\n</ul>\r\n<p class=\"p\"><strong>***********************************************</strong></p>\r\n<p><strong>SAP EWM 9.3 SP07:</strong></p>\r\n<h3 data-toc-skip class=\"sansserif\">Monitoring</h3>\r\n<p style=\"padding-left: 30px;\">Monitor Methods</p>\r\n<p style=\"padding-left: 30px;\">In the table, make the following changes:</p>\r\n<ul>\r\n<li>Add the following row for&#160;Outbound Delivery Order Item:</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Method Name</strong></td>\r\n<td><strong>Method Description</strong></td>\r\n<td><strong>Remark</strong></td>\r\n</tr>\r\n<tr>\r\n<td>/SCWM/WHRITEM_OUT_UPD_LOAD_SEQ</td>\r\n<td><em>Update Loading Sequence</em></td>\r\n<td>Enables you to update the loading sequence for outbound delivery items.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<li>Add the following row for&#160;Outbound Delivery Order:</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Method Name</strong></td>\r\n<td><strong>Method Description</strong></td>\r\n<td><strong>Remark</strong></td>\r\n</tr>\r\n<tr>\r\n<td>/SCWM/WHRHEAD_OUT_UPD_LOAD_SEQ</td>\r\n<td><em>Update Stop Sequence</em></td>\r\n<td>Enables you to update the stop sequence for outbound deliveries.<br /><br /></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<h3 data-toc-skip class=\"sansserif\" id=\"N26292\">Radio Frequency Framework</h3>\r\n<ul>\r\n<li>RF Function Keys</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Add the following note at the end of the&#160;<em>Features &gt;&#160;Standard Functions</em>&#160;<em>Keys</em> section:</p>\r\n<p style=\"padding-left: 30px;\">\"The function key F9 or shortcut 09 also displays the text of deliveries or hazardous substances on a separate screen. Normally, there is an indicator on the screen that shows you that text is available. If there is a message and text available, you see the message first and use the function key F9 on the message screen to see the text.\"</p>\r\n<ul>\r\n<li>Loading and Unloading Using Radio Frequency</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Add the following note at the end of the document:</p>\r\n<p style=\"padding-left: 30px;\">\"If you activated the loading sequence validation, you can bypass or overrule the validation using an exception code. You define the exception code in Customizing for&#160;<em>Extended Warehouse Management</em>&#160;under&#160;<em>Cross-Process Settings &gt; Exception Handling &gt; Define Exception Codes</em>. You must define the exception code for business context&#160;<em>LPI-Loading of HUs</em>&#160;(Stock Removal) with the execution step&#160;<em>L1-RF Loading HU Selection</em>&#160;and the internal process code&#160;<em>SKLO-Skip Loading Sequence</em>.\"</p>\r\n<ul>\r\n<li>Skip Pick-HU Screen During RF Picking for Replenishment (new, see attachment)</li>\r\n</ul>\r\n<p><strong>***********************************************</strong></p>\r\n<p><strong>SAP EWM 9.3 SP06:</strong></p>\r\n<p><strong>Material Flow System (MFS)</strong></p>\r\n<ul>\r\n<li>Aisles and Levels Accessible by Communication Point</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">In the&#160;first&#160;section, above the note, insert the sentence \"You define the aisles and levels to be accessible by a CP in Customizing for&#160;<em>Extended Warehouse Management</em>&#160;under&#160;<em>Material Flow System (MFS) &gt; Master Data &gt; Define Aisles and Levels Accessible by Communication Point</em>.\"</p>\r\n<ul>\r\n<li>Resource</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">At the end of the document, change the menu path \"<em>Resource Management &gt; Define Modes</em>\" to \"<em>Cross-Process Settings &gt; Resource Management &gt; Define Modes</em>\".</p>\r\n<ul>\r\n<li>Aisles and Levels Accessible by&#160;Resource</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">In the&#160;first&#160;section, above the note, insert the sentence \"You define the aisles and levels to be accessible by a resource on the&#160;<em>SAP Easy Access</em>&#160;screen under&#160;<em>Logistics &gt; SCM Extended Warehouse Management &gt; Extended Warehouse Management &gt; Master Data &gt; Material Flow System &gt; Define Aisles and Levels for MFS Resources</em>&#160;(transaction /SCWM/MFS_RSRC_AISLE).\"</p>\r\n<p><strong>Quality Management (QM)</strong></p>\r\n<ul>\r\n<li>Customizing Settings for QM in EWM</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In step 5 of the&#160;<em>Basic Settings for QM in SAP EWM</em>&#160;section, change&#160;the menu path \"<em>Cross-Process Settings</em>&#160;&gt;&#160;<em>Quality Management</em>&#160;&gt;&#160;<em>Define Item Types\" to&#160;\"<em>Cross-Process Settings</em>&#160;&gt;&#160;<em>Quality Management</em>&#160;&gt;&#160;Settings for Inspection Rules&#160;&gt;&#160;<em>Define Item Types\".</em></em></li>\r\n<li>In step 6 of the&#160;<em><em><em><em>Basic Settings for QM in SAP EWM</em>&#160;</em></em></em>section, change<em><em><em> \"<em>Inspection Planning at Delivery Activation</em></em></em></em>\" to \"<em>Inspection Planning at Activation of Delivery<em>\".&#160;</em></em></li>\r\n<li>In step 5 of the <em><em><em><em><em><em>Integration Settings for QM of SAP ERP</em></em></em></em></em></em>&#160;section, change menu path \"<em>Environment</em>&#160;&gt;&#160;<em>Tools</em>&#160;&gt;&#160;<em>Communication with Quality Inspection Engines</em>&#160;&gt;&#160;<em>Event Type Linkages</em>\" to \"<em>Environment</em>&#160;&gt;&#160;<em>Tools</em>&#160;&gt;&#160;<em>Communication with Quality Inspection Engines</em>&#160;&gt;&#160;<em>Activation of Events for Transferring QM Data in Procurement &gt;&#160;Event Type Linkage&#160;for QM Data in Procurement</em>\".</li>\r\n<li>In step 7 of the&#160;<em>Integration Settings for QM of SAP ERP</em>&#160;section, change the menu path \"<em>Environment</em>&#160;&gt;&#160;<em>Tools</em>&#160;&gt;&#160;<em>Communication with Quality Inspection Engines</em>&#160;&gt;&#160;<em>Activate Event Type Linkage for Confirmations</em>&#160;&gt;&#160;<em>Activation of BTE Application</em>\" to \"<em>Environment</em>&#160;&gt;&#160;<em>Tools</em>&#160;&gt;&#160;<em>Communication with Quality Inspection Engines&#160;&gt;&#160;Activation of Events for Transferring QM Data in Procurement&#160;&gt;&#160;Activation of BTE Application</em>\".&#160;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Inspection Object Types</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">In the&#160;<em>IOT 4: Q-Inspection Product/Batch Inbound Delivery</em>&#160;section, change the text under the&#160;<em>Presampling in production</em>&#160;bullet to the following:</p>\r\n<p style=\"padding-left: 30px;\">\"The integration to quality management (QM) and production planning and control in SAP ERP is mandatory. The process starts when a manufacturing order (production order or process order) is created and released in production planning and control in SAP ERP. SAP Extended Warehouse Management (SAP EWM) releases an inspection document for this order and immediately QM in SAP ERP creates a corresponding inspection lot. Therefore the inspection creation (and possible decision) can take place in QM in SAP ERP before the goods receipt is actually posted in the warehouse.\"</p>\r\n<ul>\r\n<li>Creation of Inspection Rule</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">In the&#160;<em>Use</em>&#160;section, decrease the indentation of&#160;the&#160;<em>Specific arguments</em>&#160;bullet so it is aligned with the&#160;<em>General arguments</em>&#160;bullet.</p>\r\n<p><strong><strong><strong>***********************************************</strong></strong></strong></p>\r\n<p><strong><strong>SAP EWM 9.3 SP03:</strong></strong></p>\r\n<p><strong>Shipping and Receiving</strong></p>\r\n<p>SAP Transportation Management entered in error for Transportation Management in SAP EWM.</p>\r\n<p>In the following documents, the term SAP Transportation Management or SAP TM should read Transportation Management:</p>\r\n<ul>\r\n<li>Transportation Management in EWM</li>\r\n<li>Shipment</li>\r\n<li>Freight Document</li>\r\n<li>Creation of Freight Documents</li>\r\n<li>Integration of TM with the Loading Process</li>\r\n<li>Authorizations in Transportation Management</li>\r\n</ul>\r\n<p><strong>Radio Frequency Framework</strong></p>\r\n<p style=\"padding-left: 30px;\">Working with RF for Pick by Voice</p>\r\n<ul>\r\n<li>In the <em>Features</em> section, in the \"Source storage bin for product WT (PVMTTO)\" step, change the sentence \"For the stack, the validation is either the value or the verification value from the storage bin master.\" to \"For the level, the validation is either the value or the verification value from the storage bin master.\"</li>\r\n<li>In the <em>Features</em> section, in the <em>Split Storage Bin into Separate Fields</em> subsection, change the sentence \"For level, the validation field of the storage bin master is used.\" to \"For the level, the validation is either the value or the verification value from the storage bin master.\"</li>\r\n</ul>\r\n<p><strong><strong><strong>***********************************************</strong></strong></strong></p>\r\n<p>The following corrections related to SP01 and SP02 are available on the Help Portal for SAP EWM 9.3 and as documentation download (material number <span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: #1f497d; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">50131895</span>) as of November 2015.</p>\r\n<p><strong><strong>SAP EWM 9.3 SP02:</strong></strong></p>\r\n<p><strong><strong>Monitoring</strong></strong></p>\r\n<ul>\r\n<li>Warehouse Management Monitoring</li>\r\n<li>Monitor Basic Operations</li>\r\n<li>Monitor Methods</li>\r\n<li>&#65279;Automatic Refresh (new)</li>\r\n</ul>\r\n<p><strong><strong>Warehouse Order Creation</strong></strong></p>\r\n<ul>\r\n<li>Description of the IDocs</li>\r\n<li>IDoc for Sending WTs to a Non-SAP System</li>\r\n<li>IDoc for Receiving WTs&#160;from a Non-SAP System</li>\r\n<li>IDoc for Confirming Warehouse Tasks</li>\r\n<li>IDoc for Canceling Warehouse Tasks</li>\r\n<li>IDoc for Releasing Waves</li>\r\n<li>IDoc for Blocking Storage Bins</li>\r\n<li>IDoc for Stock Movement with HUs</li>\r\n<li>IDoc for Creating and Sending Pick-Handling Units (new)</li>\r\n</ul>\r\n<p><strong>Shipping and Receiving</strong></p>\r\n<p>Print Forms in Shipping and Receiving</p>\r\n<p><strong>Delivery Processing</strong></p>\r\n<p>Printer Determination in Delivery Processing</p>\r\n<p><strong>Physical Inventory</strong></p>\r\n<p>Difference Analysis</p>\r\n<p><strong><strong>Warehouse Billing</strong></strong></p>\r\n<p>Request for Warehouse Billing Measurement</p>\r\n<p><strong>Wave Management</strong></p>\r\n<p>Wave Template</p>\r\n<p><strong>Radio Frequency Framework</strong></p>\r\n<ul>\r\n<li>RF Picking Flow</li>\r\n<li>Placement of Multiple HUs (new)</li>\r\n</ul>\r\n<p><strong>***********************************************</strong></p>\r\n<p><strong>SAP EWM 9.3 SP01:</strong></p>\r\n<p><strong>Transit Warehousing</strong></p>\r\n<p>Integration of Transit Warehousing with SAP TM</p>\r\n<p><strong>Catch Weight</strong></p>\r\n<p>Effects on Processes in EWM</p>\r\n<p><strong>Quality Management (QM)</strong></p>\r\n<p>Logistical Follow-Up Actions</p>\r\n<p><strong>***********************************************</strong></p>\r\n<p><strong>SAP EWM 9.3:</strong></p>\r\n<p><strong>Goods Issue</strong></p>\r\n<p style=\"padding-left: 30px;\">In the&#160;<em>Process</em> section, under point 4:</p>\r\n<p style=\"padding-left: 30px;\">Change the sentence&#160;\"You create a warehouse request for the warehouse request for picking from your warehouse.\" to \"You create a warehouse task for picking from your warehouse for the warehouse request.\"</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SCM-EWM-MFS (Material Flow System)"}, {"Key": "Other Components", "Value": "SCM-EWM-QM (Quality Management)"}, {"Key": "Other Components", "Value": "SCM-EWM-PMR (Production Material Request)"}, {"Key": "Other Components", "Value": "SCM-EWM-PI (Physical Inventory)"}, {"Key": "Other Components", "Value": "SCM-EWM-SR (Shipping and Receiving)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I034425)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I034425)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002165671/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002165671/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002165671/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002165671/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002165671/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002165671/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002165671/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002165671/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002165671/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "IDoc_for_Creating_and_Sending_Pick-Handling_Units.pdf", "FileSize": "818", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000745442015&iv_version=0035&iv_guid=4D18E77902EEED4EAD617B1A47E76278"}, {"FileName": "Automatic_Refresh.PDF", "FileSize": "870", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000745442015&iv_version=0035&iv_guid=B4612CAFF34DBC4FB1496B62C4ACFEF4"}, {"FileName": "Capacity Check.pdf", "FileSize": "407", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000745442015&iv_version=0035&iv_guid=00109B36D6621EECA4FEB74EB4D1D6E0"}, {"FileName": "Skip_Pick-HU_Screen_During_RF_Picking_for_Replenishment.pdf", "FileSize": "792", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000745442015&iv_version=0035&iv_guid=6CAE8B27FB9C1ED7A5DDD8C1BF0C40CF"}, {"FileName": "Inspection_Document_Summaries.pdf", "FileSize": "104", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000745442015&iv_version=0035&iv_guid=00109B36BC961EEEB4B1B7B746015765"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2238445", "RefComponent": "SCM", "RefTitle": "Integration of Supply Chain Management Applications to SAP S/4HANA", "RefUrl": "/notes/2238445 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SCMEWM", "From": "930", "To": "930", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}