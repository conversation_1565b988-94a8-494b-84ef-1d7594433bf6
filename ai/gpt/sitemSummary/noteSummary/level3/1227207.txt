SAP Note 1227207 outlines the necessary steps to ensure the correct determination of Funds Management (FM) account assignments for tax lines and profit and loss adjustments when document splitting is active in Financial Accounting (FI). Here's a summarized version:

**Symptom:** 
The note focuses on the correct FM account assignments for tax lines and profit and loss adjustments in the presence of document splitting in FI.

**Other Terms:** 
It mentions terms such as value-added tax, follow-up costs, minor differences, and distribution according to expenses.

**Reason and Prerequisites:** 
The note is applicable when using Funds Management along with document splitting in the general ledger.

**Solution:**

**Profit and Loss Adjustment:**
- Financial accounting with document splitting no longer requires the report SAPF181 because FI distributes certain expenses (e.g., cash discounts, exchange rate differences) during posting.
- To distribute these postings to appropriate FM account assignments, customization is needed in document splitting settings.
- Commitment item (FIPOS) as a document splitting characteristic will also split for the funds center (FISTL), and it will overwrite other account assignments if necessary.
- Functional areas can be activated as splitting characteristics as of ERP 6.0 Enhancement Package 4.
- Separate document splitting for profit and loss adjustments or tax updates is not necessary after VOBELNR.
- Proper FM update does not require additional update of fields set as document splitting characteristics.

**Tax Update in FM:**
- Document splitting characteristics are required for accurate FM tax updates.
- The tax account should not be used as a cost element if document splitting is active.
- An alternative is to use the BAdI implementation FMRI_BADI_10 for the BAdI FMRI to handle tax distribution within FM rather than through document splitting.
- Certain types of tax calculations or splitting (e.g., with a jurisdiction code) can only be handled by FI document splitting.

**Implementing FM Retroactively:**
- It's necessary to set required document splitting characteristics from the beginning.
- In ERP, migration scenario 6 can be used for subsequent implementation of FM and document splitting.
- If document splitting was already in use without the necessary characteristics, a data conversion project is needed.

**Simplified Update of Tax or Profit and Loss Adjustment in FM:**
- For a simplified update, when document splitting is used but distribution is not required in FM, updates can be done in fixed FM account assignments. Use the derivation strategy in FM (FMDERIVE).
- For tax updates, implement the method FMRI_BADI_10 as previously mentioned.

**Invoices Posted Net Cash Discount:**
- The 'Adjust Tax Update in GL to FM' function is not compatible with invoices posted with net cash discount, and related settings should not be configured.

This note contains detailed instructions on how to accommodate certain postings in Funds Management when using document splitting characteristics in Financial Accounting, as well as advice on customization for best results and cautions regarding specific scenarios where standard processes might not suffice.