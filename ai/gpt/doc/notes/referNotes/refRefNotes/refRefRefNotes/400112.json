{"Request": {"Number": "400112", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 997, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015003182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000400112?language=E&token=28B6549CA7D6973B2C891FD8DAAE3973"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000400112", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000400112/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "400112"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.06.2001"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-SDP-004"}, "SAPComponentKeyText": {"_label": "Component", "value": "Strategic Development Project 004 (DIVA)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Strategic Development Projects", "value": "XX-PROJ-SDP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-SDP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Strategic Development Project 004 (DIVA)", "value": "XX-PROJ-SDP-004", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-SDP-004*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "400112 - DIVA ICP1/2: Preliminary Transport II (05/2001)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>************************************************************************<br />This note is only valid for SAP Add-On/Preliminary Development PSA-DIVA.<br />It should not be used by standard customers.<br />************************************************************************<br />PSA-DIVA ICP1/ICP2 Transport of developments for:<br />R/3 Release 4.6C<br /><br />The developments are to be supplied by the SDP development department<br />for import into the PSA system.<br />It includes following</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>PSA DIVA ICP1 ICP2 SCE</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Preliminary transport II of developments for ICP1 and ICP2 to PSA.<br />IF Support-Package 4 is not imported, see Note 392573.<br />Import of Note 0392177<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The data is supplied for import into the PSA system on the service computer sapserv3.<br />In order to import the data into the PSA system please first check note 13719!<br />Preparation of the import concerning R/3:<br />Documentation:<br />IMG -&gt; Logistic -General -&gt; Variant Configuration -&gt; Filter for<br />Knowledge-Base-Version<br /><br />Actions in R/3-System:<br />Before importing the data, create the following development classes:<br />YDIVA_PROCESS_TPS34&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(DIVA: Process Interface Up/downloads)<br />YDIVA_PROCESS_CRMO_200_SCE (DIVA: Before Download SCE)<br />YDIVA_PROCESS_CUSTOMER&#x00A0;&#x00A0;&#x00A0;&#x00A0; (DIVA: Up/Download Customer Enhanced Data)<br />YDIVA_BUPA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(DIVA: Customer enhancement of BP)<br /><br />Actions in CRM-System:<br />Before importing the data, create the following objects:<br />Development class:<br />YDIVA_BUPA_SHIP&#x00A0;&#x00A0; (DIVA: Business Partner Enhancement SHIP-TO-PARTY)<br />Domain:&#x00A0;&#x00A0;&#x00A0;&#x00A0; YYDIVA_BP_LTP_PARK, Char 1,<br />Datalement: YYDIVA_BP_LTP_PARK<br />Append of BUT000: ZABUT000 with the field YYDIVA_LTP_PARK<br /><br />You find the file with the data for the R/3 development as follows:<br />SAPSERV3-directory: ~ftp/specific/sdp/psa-diva/note.0400112<br />Containing the Zip-File: Note400112_II_R3.zip with the two files:<br />K900111.zbb<br />R900111.zbb<br />You find the file with the data for the CRM development in the same<br />folder. ZIP-File: Note400112_II_CRM.zip with the two files:<br />K900404.q2u<br />R900404.q2u<br /><br />Use note 13719 in order to collect and import the data.<br /><br />After importing im CRM-System following modules must be re-adjusted.<br />Development class: YDIVA_BUPA_SHIP<br />-&gt; Functiongroup: YDIVA_BUPA_SHIP_DIALOG<br />-&gt; Include LYDIVA_BUPA_DIALOGF02<br />-&gt; Comment line after Q2U and decomment line after PSA<br /><br />Development class: YDIVA_BUPA_SHIP<br />-&gt; Functiongroup: YDIVA_BUPA_SHIP_DIALOG<br />-&gt; Include LYDIVA_BUPA_DIALOGF04<br />-&gt; Comment line after Q2U and decomment line after PSA<br /><br />Development class: YDIVA_BUPA_SHIP<br />-&gt; Functiongroup: YDIVA_BUPA_SHIP_BAPI<br />-&gt; Function module: Y_DIVA_BP_DATA_SHIP_CHANGE<br />-&gt; Comment line after Q2U and decomment line after PSA<br /><br />Development class: YDIVA_BUPA_SHIP<br />-&gt; Functiongroup: YDIVA_BUPA_SHIP_BAPI<br />-&gt; Function module: Y_DIVA_BP_DATA_SHIP_GET<br />-&gt; Comment line after Q2U and decomment line after PSA<br /><br />Necessary customizing is described in the document:<br />DESIGN_CRM_BP_ENHANCEMENT_LTP_FLAG.DOC ( in folder<br />E:\\Projects\\PSA\\04_PHASE_DEVELOPMENT\\01_DESIGN\\SP4_SALES_PROCESS\\ICP1&amp;2)<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028814)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400112/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400112/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400112/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400112/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400112/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400112/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400112/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400112/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000400112/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "428398", "RefComponent": "XX-PROJ-SDP-004-CRM", "RefTitle": "DIVA ICP1/2: Preliminary Transport III (07/2001)", "RefUrl": "/notes/428398"}, {"RefNumber": "392177", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "DIVA ICP1/2: Preliminary Transport (03/2001)", "RefUrl": "/notes/392177"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "428398", "RefComponent": "XX-PROJ-SDP-004-CRM", "RefTitle": "DIVA ICP1/2: Preliminary Transport III (07/2001)", "RefUrl": "/notes/428398 "}, {"RefNumber": "392177", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "DIVA ICP1/2: Preliminary Transport (03/2001)", "RefUrl": "/notes/392177 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}