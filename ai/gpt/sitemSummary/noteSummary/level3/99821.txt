SAP Note 99821 addresses issues with long runtimes experienced in consolidation archiving, the absence of consolidation archive read programs, and the lack of linkage between consolidation archiving and the archive information system prior to SAP Release 4.5A.

Summary of SAP Note 99821:

- **Symptom**: Users experiencing long runtimes during consolidation archiving, missing read programs for consolidation archives, and no connection to the archive information system.
  
- **Other Terms**: Lists the relevant archiving objects, programs, and transactions affected by the issue.

- **Reason**: Explains that before Release 4.5A, archiving in consolidation used the 'GLG' logical database, which inefficiently read totals and selected journal entries hierarchically through totals records.

- **Solution**: Introduces revised object-oriented archiving with its own selection routines starting from Release 4.5A, which can be backported to earlier releases (3.0D to 3.1I and 4.0B).

The solution includes:

1. **New Archiving Objects**: The old archiving object 'FLC_OBJECT' is replaced with two new objects, 'FI_LC_ITEM' for journal entries and 'FI_LC_SUM' for totals records.

2. **New Archiving Programs**: Each of the new archiving objects includes an archive write, delete, reload, and read program, which separates the archiving of totals records and journal entries, and supports document/account-sorted archiving.

3. **Integration with Archive Information System**: Both new objects are now linked to the archive information system for faster access to archives.

4. **Detailed F1 Help**: Provides comprehensive guidance on selecting parameters for archiving.

**Instructions for Import**:

1. Information on importing new FI-LC code and table entries for archiving is provided, including ensuring the use of the latest program versions.
  
2. Specifics on pre-import procedures, like configuring variants in the delete program via Transaction SARA and checking the connectivity of read programs, are outlined.

3. Importing the link of FI-LC archiving to the archive information system is detailed, including activating the new information structures via Transaction SARI.

**Known Problems**:

- An issue with the archiving write programs causing DB lock overflows is referenced, with the solution detailed in SAP Note 183164.

This note is essential for customers experiencing issues with consolidation archiving performance and those looking to improve their archiving processes for financial consolidation in SAP systems. It provides resources and instructions for updating to the revised archiving system, which aims to modernize and streamline the consolidation archiving process.