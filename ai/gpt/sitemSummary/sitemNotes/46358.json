{"Request": {"Number": "46358", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 262, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014438182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000046358?language=E&token=BFB281D2F82438350A70A75174DA3EA9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000046358", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000046358/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "46358"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "HotNews"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.01.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-BMT-WFM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Workflow"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Management", "value": "BC-BMT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Workflow", "value": "BC-BMT-WFM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT-WFM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "46358 - Shutdown of the log of workflow events"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Creating a workflow event slows down the triggering program.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Workflow, performance, SWE_EVENT_CREATE, tuning, event log, SWE4, SWEL, ARFC, ARFC:*.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>A log entry is written for every receiver found for every workflow event triggered by function module SWE_EVENT_CREATE. This also applies to the workflow events triggered by message control, change documents or status management. This log takes time to write.<br />This log is mostly only interesting in test situations, that is until the workflow administrator can be sure that all of the event linkages of the used workflows are set correctly. For this reason, the SAP default provides for the writing of the log entries to simplify a possible error search.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The log of the workflow events should be deactivated at the latest by the time production starts. It should only be active in production system if this is necessary for an error analysis.<br />You can deactivate the log of the workflow events as follows:</p> <OL>1. Go to the workflow developer menu:<br />Tools -&gt; Business Engineering -&gt; Business Workflow -&gt; Development</OL> <OL>2. There, deactivate the event log:<br />Utilities -&gt; Event log -&gt; on/off</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC (Basis Components)"}, {"Key": "Responsible                                                                                         ", "Value": "D019512"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000046358/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000046358/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046358/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046358/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046358/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046358/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046358/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046358/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000046358/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98407", "RefComponent": "BC-BMT-WFM", "RefTitle": "Performance workflow", "RefUrl": "/notes/98407"}, {"RefNumber": "89471", "RefComponent": "BC-MID-ALE", "RefTitle": "Collective note ALE input processing", "RefUrl": "/notes/89471"}, {"RefNumber": "72923", "RefComponent": "BC-BMT-WFM", "RefTitle": "Business Workflow Performance, Collective note", "RefUrl": "/notes/72923"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "128293", "RefComponent": "IS-U", "RefTitle": "Collective note: IS-U & IS-T performance problems", "RefUrl": "/notes/128293"}, {"RefNumber": "123418", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/123418"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "89471", "RefComponent": "BC-MID-ALE", "RefTitle": "Collective note ALE input processing", "RefUrl": "/notes/89471 "}, {"RefNumber": "98407", "RefComponent": "BC-BMT-WFM", "RefTitle": "Performance workflow", "RefUrl": "/notes/98407 "}, {"RefNumber": "72923", "RefComponent": "BC-BMT-WFM", "RefTitle": "Business Workflow Performance, Collective note", "RefUrl": "/notes/72923 "}, {"RefNumber": "128293", "RefComponent": "IS-U", "RefTitle": "Collective note: IS-U & IS-T performance problems", "RefUrl": "/notes/128293 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 30D", "SupportPackage": "SAPKH30D19", "URL": "/supportpackage/SAPKH30D19"}, {"SoftwareComponentVersion": "SAP_APPL 30F", "SupportPackage": "SAPKH30F04", "URL": "/supportpackage/SAPKH30F04"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H28", "URL": "/supportpackage/SAPKH31H28"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H28", "URL": "/supportpackage/SAPKE31H28"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I06", "URL": "/supportpackage/SAPKH31I06"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I06", "URL": "/supportpackage/SAPKE31I06"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B04", "URL": "/supportpackage/SAPKH40B04"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B04", "URL": "/supportpackage/SAPKE40B04"}, {"SoftwareComponentVersion": "SAP_APPL 45A", "SupportPackage": "SAPKH45A01", "URL": "/supportpackage/SAPKH45A01"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}