{"Request": {"Number": "367964", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 407, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014943432017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000367964?language=E&token=69AD5421A4A278033877805BA56DE9DB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000367964", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000367964/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "367964"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.01.2001"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-APO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Advanced Planning and Optimization"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Advanced Planning and Optimization", "value": "SCM-APO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "367964 - APO Support Package 8 for APO Release 3.0A"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes how you import the APO Support Package 8 (SAPKY30A08) for APO Release 3.0A in your system.<br />APO Support Package 8 contains all notes that were created in the period from 08/Nov/2000 to 04/Dec/2000. These notes contain corrections for the following areas:<br /><br /><B>Upgrade information on SAPGUI</B><br />The maintenance for the frontend Release 4.6C ended on 31/Dec/2000. Thus, it is recommended to upgrade to frontend Release 4.6D. For further information on this subject, refer to the following notes:</p> <UL><LI>0147519<br />Maintenance strategy SAPGUI</LI></UL> <UL><LI>0361222<br />SapPatch: Importing Gui Patches<br /></LI></UL> <p><B>!!! Important information for customers who imported Support Package 6 and earlier Support Packages in their APO system for Release 3.0A.!!</B><br />If you imported APO Support Package 6 in your system, you MUST consider the following steps for the upgrade to APO Support Package 8. <B>Data loss might occur otherwise.</B></p> <OL>1. Upgrade your system to APO Support Package status 6 as described in the notes for the APO Support Packages. DO NOT import a higher Support Package status.</OL> <OL>2. Prior to importing APO Support Package 7, create a data backup. To do this, read Notes 314218 and 361635 carefully. Data loss might occur if you do not follow the described procedure. Then, import APO Support Package 7 (Note 361683) entirely. For point 5 (COM routines), refer to Note 157265. Finally, initialize the liveCache (see last step in Note 157265).</OL> <OL>3. After this, import APO Support Package 8 as described in this note.<br />------------------------------------------------------------------------</OL> <p>Customers who already imported APO Support Package 7 in their system can import APO Support Package 8 as described in this note.<br />!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</p> <UL><LI> APO Support Package 8 for APO Release 3.0A (SAPKY30A08)<br /></LI></UL> <UL><LI> OCX update<br /></LI></UL> <UL><LI> Update of the liveCache version -&gt; no update.<br /></LI></UL> <UL><LI> COM routines<br /></LI></UL> <UL><LI> Optimizer corrections<br /></LI></UL> <UL><LI> Additional notes<br /></LI></UL> <p>APO Support Package 8 for APO Release 3.0A is available in the following languages. The languages are contained in APO Support Package 8.<br />German, English, French, Spanish, Danish, Finnish, Hungarian, Italian, Japanese, Korean, Dutch, Norwegian, Polish, Portuguese, Swedish, Russian, Czech.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAPKY30A01,SAPKY30A02,SAPKY30A03,SAPKY30A04,SAPKY30A05,SAPKY30A06, SAPKY30A06, SAPKY30A07, SAPKY30A08, APO Support Package, APO Patch, APO Release 3.0A</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>APO Support Package 8 (SAPKY30A08) for APO Release 3.0A requires a complete installation / upgrade (delivery 15/May/2000).<br />In addition APO Support Packages 1, 2, 3, 4, 5, 6 and 7 for APO Release 3.0A must have been imported.<br />If the system was set up with service Release 1 for APO Release 3.0A, APO Support Packages 5, 6 and 7 for APO Release 3.0A must be imported additionally.<br /><br />========================================================================<br /></p> <UL><LI>Basis Support Package (4.6C)</LI></UL> <p>For APO Support Package 8 it is required that the Basis Support Packages up to and inlcuding Support Package 11 (SAPKB46C11) have been imported.</p> <UL><LI>ABA Support Package for Basis 4.6C</LI></UL> <p>It is absolutely necessary to import the ABA Support Packages up to and including ABA Support Package 11 (SAPKA46C11) in addition to the Basis Support Packages.</p> <UL><LI>BW Support Package (2.0B)</LI></UL> <p>For APO Support Package 8 it is required that the BW Support Packages up to and including BW Suppport Package 10 (SAPKW20B10) have been imported.<br />Note: 328237 Importing Support Packages into a BW 2.0B system<br />Basis Support Packages / BW Support Packages are always imported separately from the APO Support Packages. Import the Basis Support Packages / BW Support Packages. Directly after that, import the APO Support Package.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />In order to completely import APO Support Package 8 proceed as described in the following:<br /><B>CAUTION!!</B><br />DO NOT import several APO Support Packages for Release 3.0A in a queue even if SPAM suggests this.<br />Import each APO Support Package separately.</p> <OL>1. <B>SPAM - Update</B></OL> <p>Before you start importing the Support Packages, it is required that you upgrade the SPAM manager to the most current status. To do this, download the most current version of the SPAM update from the OSS or the SAPNET.<br />Import the most current SPAM update in your system. For more information on this subject, click the 'i' button on the initial screen of Transaction SPAM or refer to the online documentation (Help -&gt; Application help).</p> <OL>2. <B>APO corrections</B></OL> <p>You can download the APO Support Package SAPKY30A08 from Online Service System (OSS) or from the SAPnet. The URL is: http://sapnet.sap.de/ocs-download. Follow the path: APO Support Package -&gt; APO 3.0A. Download the APO Support Package SAPKY30A08. For further details refer to Note 83458.</p> <OL>3. <B>OCX update</B></OL> <p>The OCX files which are required for APO Support Package 8, APO Release 3.0A are located on the sapervX FTP server. For all further information on importing the new OCX files refer to Note:<br />368306 APO 3.0 frontend patch 9 (Jan.2001)<br /><B>Prior to the update of the liveCache and the exchange of the COM object</B><br />you must read Note 361635 (point 1. Preparations) and Note 314218.</p> <OL>4. <B>Update to liveCache version 7.2.4 Build 9</B></OL> <p>No new liveCache VersionBuild for APO Support Package 8.</p> <OL>5. <B>COM routines</B></OL> <p>Download the COM routines from the sapservX.<br />In the directory <B>ftp://sapservX/specific/apo/apo30/sp8/</B>the file <B>SAPCOM_30_n.SAR</B> (n = version counter) is located.<br />In order to import the new COM routines, proceed as described in the following notes:<br />371939 SAPAPO 3.0 COM Object Build 14<br />157265 Exchanging the COM objects for liveCache in APO 3.0<br />As of liveCache version 7.2.4, also consider the following note:<br />336470 Environment var. DBROOT no longer exists<br /><br />!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!<br />If you imported APO Support Package 7 completely according to Note 361683 and if you initialized the liveCache as described in Note 157265, you can upgrade to APO Support Package 8 without having to re-initialize the liveCache.<br />!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</p> <OL>6. <B>Optimizer corrections</B></OL> <p>You can download the Optimizer corrections via the sapservX. In the directory <B>ftp://sapservX/specific/apo/apo30/sp8/ </B>the file <B>SAPAPO_n.SAR</B> (n = version counter) is located.<br /><br />In order to import the new files, proceed as described in the following notes:</p> <UL><LI>371567: APO 3.0 Optimizer Support Package 8</LI></UL> <UL><LI>300930: APO 3.0: Importing an optimizer version<br /></LI></UL> <UL><LI> <B>Additional notes that must be imported manually!</B><br /></LI></UL> <p>Note 353197<br />Users for synchronous RFC dialog as of Release 4.6<br />Note 352844<br />Authorizations for RFC users: APO &lt;-&gt; R/3<br />Note 147218<br />SAP APO Demand Planning - datamart ==&gt; BW patches<br />Note 371552<br />DP/SNP: Required notes after Support Package 8<br />Note 371841<br />Dump in Engineer when moving a location: APO 3.0 SP9<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-OCS (Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr))"}, {"Key": "Responsible                                                                                         ", "Value": "D027030"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000367964/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000367964/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000367964/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000367964/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000367964/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000367964/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000367964/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000367964/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000367964/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "83458", "RefComponent": "BC-UPG-OCS", "RefTitle": "@19@OCS Info: Patch download from SAP Service Marketplace", "RefUrl": "/notes/83458"}, {"RefNumber": "390212", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/390212"}, {"RefNumber": "383890", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 9 for APO Release 3.0A", "RefUrl": "/notes/383890"}, {"RefNumber": "381953", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/381953"}, {"RefNumber": "376378", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/376378"}, {"RefNumber": "371939", "RefComponent": "BC-DB-LCA", "RefTitle": "SAPAPO 3.0 COM Object Build 14", "RefUrl": "/notes/371939"}, {"RefNumber": "371841", "RefComponent": "SCM-APO-MD-LO", "RefTitle": "Dump in Engineer when moving location: APO 3.0 SP9", "RefUrl": "/notes/371841"}, {"RefNumber": "371567", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 Optimizer R/3 Support Package 8", "RefUrl": "/notes/371567"}, {"RefNumber": "371552", "RefComponent": "SCM-APO-SNP", "RefTitle": "DP/SNP: notes required after Support Package 8", "RefUrl": "/notes/371552"}, {"RefNumber": "368306", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/368306"}, {"RefNumber": "361683", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 7 for APO Release 3.0A", "RefUrl": "/notes/361683"}, {"RefNumber": "361635", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/361635"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "353197", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/353197"}, {"RefNumber": "352844", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/352844"}, {"RefNumber": "336470", "RefComponent": "BC-DB-SDB", "RefTitle": "Environment var. DBROOT no longer exists", "RefUrl": "/notes/336470"}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237"}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218"}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "371939", "RefComponent": "BC-DB-LCA", "RefTitle": "SAPAPO 3.0 COM Object Build 14", "RefUrl": "/notes/371939 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "83458", "RefComponent": "BC-UPG-OCS", "RefTitle": "@19@OCS Info: Patch download from SAP Service Marketplace", "RefUrl": "/notes/83458 "}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237 "}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265 "}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218 "}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218 "}, {"RefNumber": "383890", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 9 for APO Release 3.0A", "RefUrl": "/notes/383890 "}, {"RefNumber": "371552", "RefComponent": "SCM-APO-SNP", "RefTitle": "DP/SNP: notes required after Support Package 8", "RefUrl": "/notes/371552 "}, {"RefNumber": "371841", "RefComponent": "SCM-APO-MD-LO", "RefTitle": "Dump in Engineer when moving location: APO 3.0 SP9", "RefUrl": "/notes/371841 "}, {"RefNumber": "371567", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 Optimizer R/3 Support Package 8", "RefUrl": "/notes/371567 "}, {"RefNumber": "336470", "RefComponent": "BC-DB-SDB", "RefTitle": "Environment var. DBROOT no longer exists", "RefUrl": "/notes/336470 "}, {"RefNumber": "361683", "RefComponent": "SCM-APO", "RefTitle": "APO Support Package 7 for APO Release 3.0A", "RefUrl": "/notes/361683 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APO", "From": "30A", "To": "30A", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}