SAP Note 1709868 introduces the VMC NewDebugger as an enhancement over the previous ABAP report RSVMCRT_MINI_DEBUGGER, which offered a text interface for debugging VMC function modules. The VMC NewDebugger comes with a graphical user interface that can be operated with a mouse, incorporating new features such as syntax coloring and the ability to upload source code from local sources.

To use this new debugging tool, customers need to ensure they have the necessary kernel patch level specified in this note's attachment on the 'SP Patch Level' tab. The VMC NewDebugger is intended for use in a VMC system.

The note also states that the enhancements on the ABAP side related to this change are described in SAP Note 1678626. 

Furthermore, it refers to SAP Note 1728283 for general information about the SAP Kernel 721, which includes details on its purpose, enhancements, and deployment. This kernel brings multiple improvements across areas like security, supportability, and performance for NetWeaver based systems.

Additionally, SAP Note 1709871 addresses user experience improvements, specifically a feature that allows for the direct starting of the VMC NewDebugger without manual invocation. This is enabled by setting a user parameter as described in the note.

The key takeaway from SAP Note 1709868 is the availability of an improved graphical debugging tool for VMC, known as the VMC NewDebugger, offering enhanced features for development and troubleshooting within the SAP environment.