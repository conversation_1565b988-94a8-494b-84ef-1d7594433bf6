<h3>SI6: CT_BNS-ARI-SE</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2341836">2341836 - 2341836 - S4TWL - Ariba Network Integration in SAP S/4HANA on-premise edition</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are planning a system conversion from SAP ERP to SAP S/4HANA on premise. The information below is important.</p>
<p>The SAP Note 2341836 is relevant if you use the SAP ERP add-on of Ariba Network Integration 1.0 for SAP Business Suite. In SAP S/4HANA, Ariba Network integration functionality is not provided as an add-on but as part of the SAP S/4HANA core product. Not all integration processes and messages that are provided with Ariba Network Integration 1.0 for SAP Business Suite are available in SAP S/4HANA. After the conversion from SAP ERP to SAP S/4HANA, the integration of processes and cXML messages listed below are not natively supported by SAP S/4HANA.</p>
<p>Please take into account that there will be no further development of new features or functionalities for the S/4HANA native Ariba Integration. You should consider a transition to the SAP Ariba Cloud Integration Gateway as soon as possible. More details are provided in the note <a href="https://launchpad.support.sap.com/#/notes/2705047" target="_blank">2705047</a> (Roadmap for Ariba Integration Solutions).</p>
<p>The preferred approach to migrate the ERP add-on of Ariba Network Integration to the S/4HANA on-premise edition would be first to convert this add-on to the SAP Ariba Cloud Integration Gateway and only then start the ERP system conversion. See <a href="https://help.sap.com/viewer/bd42e085ddc4449e8face9bfeff5b0f3/CIG_2019_D/en-US/694d968753644ccf952fab848f33cbbf.html" target="_blank">Migrating from SAP Business Suite Add-On</a>.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Note the restrictions for SAP S/4HANA, on-premise edition 1511 listed below.</p>
<p>The following processes are not supported:
<br>• Processing of CC invoices (invoices transferred from SAP S/4HANA to Ariba Network)
<br>• Service procurement/invoicing process
<br>• Payment process
<br>• Discount management process
<br>• Update of Advanced Shipping Notifications
<br>• Ariba Supply Chain Collaboration
<br> - Processing of scheduling agreement releases
<br> - Consignment process
<br> - Subcontracting process
<br> - Processing of forecast data
<br> - Multi-tier purchase order processing
<br> - Purchase order return items processing
<br> - Integration processes for suppliers
<br> - Quality Management Process (e.g. Quality Notification, Quality Inspection, Quality Certificates...)<p>
<p>The following cXML message types are not available:
<br>• ReceiptRequest
<br>• CopyRequest.InvoiceDetailRequest
<br>• ServiceEntryRequest
<br>• PaymentRemittanceRequest
<br>• PaymentRemittanceStatusUpdateRequest
<br>• PaymentProposalRequest
<br>• CopyRequest.PaymentProposalRequest
<br>• ProductActivityMessage
<br>• ComponentConsumptionRequest
<br>• ProductReplenishmentMessage
<br>• QuoteRequest
<br>• QuoteMessage
<br>• QualityNotificationRequest
<br>• QualityInspectionRequest
<br>• QualityInspectionResultRequest
<br>• QualityInspectionDecisionRequest
<br>• ApprovalRequest.ConfirmationRequest
<br>• OrderStatusRequest</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>• Conversions to 1511 or 1610:  Implement the master conversion pre-check SAP Note “<a href="https://launchpad.support.sap.com/#/notes/2182725">2182725 - S4TC Delivery of the SAP S/4HANA System Conversion Checks</a>" and follow the instructions in this note.<br>
• Conversions 1709 and later: Implement the master conversion pre-check SAP Note “<a href="https://launchpad.support.sap.com/#/notes/2502552">2502552  - S4TC - SAP S/4HANA Conversion & Upgrade new Simplification Item Checks</a>" and follow the instructions in this note.<br>
• For Ariba Network Integration, also implement the pre-check SAP Note “<a href="https://launchpad.support.sap.com/#/notes/2237932">2237932 - S4TC Ariba BS Add-On Master Check for SAP S/4HANA System Conversion Checks</a>". This note is also listed within the master note.<br>
• Regarding customer code relevant changes please read the following SAP Note “<a href="https://launchpad.support.sap.com/#/notes/2406571">2406571 - SAP S/4HANA Simplification: Ariba Network Integration (custom code relevant changes)</a>”<br>
• If pre-checks fail, see the following SAP Note: “<a href="https://launchpad.support.sap.com/#/notes/2246865">2246865 - S/4 Transition: Activities after failed PreChecks</a>”.<br>
• Access SAP Note: “<a href="https://launchpad.support.sap.com/#/notes/2341836">2341836 - S4TWL - Ariba Network Integration in SAP S/4HANA on-premise edition</a>”, download the following documents, and follow the steps described.<br>
  - Before the conversion:  S4H_CONV_INTEGRATION_ARIBA_NETWORK_PREPC.PDF<br>
  - After the conversion: S4H_CONV_INTEGRATION_ARIBA_NETWORK_POSTC.PDF</p>
<p><strong>Reference Notes:</strong>
<a href="https://launchpad.support.sap.com/#/notes/2237932">2237932 - S4TC Ariba BS Add-On Master Check for S/4 System Conversion Checks</a>,
<a href="https://launchpad.support.sap.com/#/notes/2182725">2182725 - S4TC Delivery of the SAP S/4HANA System Conversion Checks for SAP S/4HANA 1511 or 1610</a>,
<a href="https://launchpad.support.sap.com/#/notes/2502552">2502552 - S4TC - SAP S/4HANA Conversion & Upgrade new Simplification Item Checks</a>,
<a href="https://launchpad.support.sap.com/#/notes/2705047">2705047 - Roadmap for Ariba Integration Solutions</a>,
<a href="https://launchpad.support.sap.com/#/notes/2246865">2246865 - S/4 conversion: Activities after failed checks executed before the conversion.</a>,
<a href="https://launchpad.support.sap.com/#/notes/2406571">2406571 - SAP S/4HANA Simplification: Ariba Network Integration (custom code relevant changes)</a>,
<a href="https://launchpad.support.sap.com/#/notes/2400737">2400737 - Ariba Cloud Integration Solutions for SAP: Supported SAP Product Versions</a>,
</p>