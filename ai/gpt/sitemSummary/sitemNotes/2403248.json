{"Request": {"Number": "2403248", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 558, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018454852017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=5D345F51B7DDA8E87BB8D35C3C42244B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2403248"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.09.2018"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Asset Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Accounting", "value": "FI-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2403248 - FI-AA (new): Availability of RAFABNEW"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to subsequently create a new depreciation area in Asset Accounting (new) (FI-AA (new)).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Automatic opening of a new depreciation area; subsequent creation of a new depreciation area; RE&#x00A0;RAFABNEW; RE FAA_RAFAB_COPY_AREA; BAdI FAA_AA_COPY_AREA; add new depreciation area; transaction AFBN; new accounting principle; scenario 7; migration scenario 7; sFIN; SAP S/4HANA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You use Asset Accounting (new) productively.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>For the subsequent implementation of a new depreciation area or a new accounting principle, the following programs and functions were or are available:</p>\r\n<ul>\r\n<li><strong>Program RAFABNEW</strong>:</li>\r\n<ul>\r\n<li><strong>Usage</strong>: subsequent implementation of a new depreciation area</li>\r\n<li><strong>More information</strong>: See the program documentation in the system.</li>\r\n<li><strong>No longer</strong> available as of the following releases: SAP Simple Finance 1503 and SAP S/HANA, on-premise Edition 1511<br />Reason: Introduction of the journal entry with changed data structures.</li>\r\n</ul>\r\n<li><strong>Program RAFAB_COPY_AREA</strong>:</li>\r\n<ul>\r\n<li><strong>Usage</strong>: subsequent implementation of a new depreciation area in an existing valuation<br /><span style=\"text-decoration: underline;\">Typical example</span>: Within your \"Local GAAP\" local accounting principle, you require a depreciation area in which the insurance values are displayed. The \"Local GAAP\" local accounting principle already exists in the system, but there is no valuation area for insurance values yet.<br />Adjust the program RAFAB_COPY_AREA to your requirements, and implement the new depreciation area for insurance values.</li>\r\n<li><strong>More information</strong>: See the program documentation in the system.</li>\r\n<li><strong>Available as of</strong> SAP S/HANA 1809</li>\r\n</ul>\r\n<li><strong><em>Subsequent Implementation of a Further Accounting Principle</em> function</strong>:</li>\r\n<ul>\r\n<li><strong>Usage</strong>: subsequent implementation of a new accounting principle, that is, a new valuation<br /><span style=\"text-decoration: underline;\">Typical example</span>: In your system, currently only the values for the \"Local GAAP\" local accounting principle are displayed in Asset Accounting. In future, you also want to display values according to another accounting principle (for example, according to IFRS).<br />Start a project and use the <em>Subsequent Implementation of a Further Accounting Principle</em> function to implement the new accounting principle.</li>\r\n<li><strong>More information</strong>: in the IMG activity with the same name and in the application documentation</li>\r\n<li><strong>Available as of</strong> SAP S/4HANA Finance 1605 SP05 and SAP S/4HANA 1610</li>\r\n</ul>\r\n</ul>\r\n<p>The following table provides an overview of the Asset Accounting (new) releases in which the programs and functions are available:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"32%\">\r\n<p>&#x00A0;</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>SAP ERP 6.0 EHP 7 or higher and</p>\r\n<p>SAP Simple Finance add-on 1.0</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>SAP Simple Finance 1503 and</p>\r\n<p>SAP S/HANA, on-premise Edition 1511</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>As of SAP S/4HANA Finance 1605, SP05 and</p>\r\n<p>SAP S/4HANA 1610</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>As of SAP S/4HANA 1809</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"32%\">\r\n<p>Program RAFABNEW</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>X</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>--</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>--</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>--</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"32%\">\r\n<p>Program RAFAB_COPY_AREA</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>--</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>--</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>--</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"32%\">\r\n<p><em>Subsequent Implementation of a Further Accounting Principle</em> function</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>--</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>--</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>X</p>\r\n</td>\r\n<td valign=\"top\" width=\"17%\">\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Additional note</strong>:<br />In SAP ERP, in General Ledger Accounting (new) with migration scenarios 7 and 8, you can subsequently implement a new ledger and subsequently switch from the accounts approach to the ledger approach. These migration scenarios are supported in SAP ERP in classic Asset Accounting, but <strong>not</strong> in Asset Accounting (new).</p>\r\n<p>&#x00A0;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "AFBN"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022141)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022141)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2398938", "RefComponent": "FI-AA-AA-A", "RefTitle": "No insurance fields in Asset Accounting", "RefUrl": "/notes/2398938 "}, {"RefNumber": "2368280", "RefComponent": "FI-AA", "RefTitle": "FAQ in a S/4HANA implementation in Asset Accounting", "RefUrl": "/notes/2368280 "}, {"RefNumber": "2935503", "RefComponent": "FI-AA-AA-B", "RefTitle": "RAFAB_COPY_AREA - Possibility to copy specific values to new DepArea only?", "RefUrl": "/notes/2935503 "}, {"RefNumber": "2785813", "RefComponent": "FIN-MIG-AA", "RefTitle": "RASFIN_MIGR_PRECHECK raises error ACC_AA 218", "RefUrl": "/notes/2785813 "}, {"RefNumber": "1646412", "RefComponent": "FI-AA-AA-A", "RefTitle": "Depreciation Area deactivated in Asset Class", "RefUrl": "/notes/1646412 "}, {"RefNumber": "2270388", "RefComponent": "FIN-MIG-AA", "RefTitle": "S4TWL - Asset Accounting: Parallel valuation and journal entry", "RefUrl": "/notes/2270388 "}, {"RefNumber": "1378630", "RefComponent": "FI-GL-MIG", "RefTitle": "Subsequent implementation of a new general ledger", "RefUrl": "/notes/1378630 "}, {"RefNumber": "2405554", "RefComponent": "FI-GL-MIG-TL", "RefTitle": "Incompatibility between Standard Migration Scenarios (Scenario : 7 & 8) and active New Asset Accounting", "RefUrl": "/notes/2405554 "}, {"RefNumber": "1070629", "RefComponent": "FI-GL-MIG-BO", "RefTitle": "FAQs: Migration to General Ledger Accounting (new)", "RefUrl": "/notes/1070629 "}, {"RefNumber": "1619168", "RefComponent": "FI-GL-MIG", "RefTitle": "Overview of the different migration scenarios", "RefUrl": "/notes/1619168 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": "X"}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": "X"}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": "X"}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": "X"}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10006INS4CORE", "URL": "/supportpackage/SAPK-10006INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10104INS4CORE", "URL": "/supportpackage/SAPK-10104INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10201INS4CORE", "URL": "/supportpackage/SAPK-10201INS4CORE"}, {"SoftwareComponentVersion": "SAP_FIN 720", "SupportPackage": "SAPK-72008INSAPFIN", "URL": "/supportpackage/SAPK-72008INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 730", "SupportPackage": "SAPK-73010INSAPFIN", "URL": "/supportpackage/SAPK-73010INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 2, "URL": "/corrins/**********/15841"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 3, "URL": "/corrins/**********/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-72007INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-73003INSAPFIN - SAPK-73009INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 100&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10005INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10103INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;w/o Support Packages&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Create the following new text symbol in the program RAFABNEW:</P> <UL><LI>N00 SAP Note 2403248</LI></UL> <P><br/>Save and activate your changes.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}