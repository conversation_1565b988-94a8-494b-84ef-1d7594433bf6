SAP Note 1776186 addresses a performance issue in SAP BW on HANA environments that are configured in a scale-out (multi-node) architecture. The issue arises from a routing problem where stored procedure-based queries in BW are sent to an indexserver that does not hold the relevant data (e.g., table, view, etc.). As a result, the query must be forwarded to the correct indexserver, which causes performance degradation.

Key points of the SAP Note include:

1. **Symptom**: Stored procedure-based BW queries in a scale-out HANA environment are not always routed to the indexserver containing the actual query objects, leading to performance problems.

2. **Important Information**: Particularly relevant for customers with ABAP Basis Release 731. If they are running on ABAP Basis Support Package 6 and ABAP Kernel Version below 721, there are partial changes available. To prevent BW queries from breaking ABAP sessions, the remaining ABAP changes from this note must be applied immediately, which are available as source code corrections. However, to gain performance improvements, an ABAP kernel update to version 721 or higher will eventually be required.

3. **Other Terms**: Keywords related to this issue including trexviadbsl, TREX_EXT_AGGREGATE, and scaleout.

4. **Reason**: The current Round-Robin routing mechanism does not account for the physical location of the query objects, leading to performance inefficiencies.

5. **Solution**: The SAP Note prescribes a combination of fixes:
   - An ABAP support package or source code correction.
   - SAP HANA SPS 5 (Revision 45) or later.
   - ABAP kernel version must be updated to 721 or later (refer to SAP Note 1716826).
   After applying these patches, authorization for required DB users/schemas needs to be updated in the HANA Studio by granting EXECUTE privileges (without grant option) on the object TREXVIADBSLWITHPARAMETER (SYS).

Additional considerations:
- The fixes need to be applied in no specific order, but all three corrections (ABAP, HANA, ABAP Kernel) must be implemented to activate the feature.
- Missing any of the corrections means the feature will not be activated and the system will operate as before.
- Applying the corrections in a single-node system is safe. The feature will simply not be utilized.
- It is also recommended to apply SAP Note 1798043, as the feature may encounter problems described in that note when many queries are executed in parallel.