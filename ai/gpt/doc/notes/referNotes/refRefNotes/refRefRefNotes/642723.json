{"Request": {"Number": "642723", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1132, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015495562017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000642723?language=E&token=E3D5C1E5AA14D66A8382A8FD1FC67E9E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000642723", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000642723/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "642723"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.02.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Monitors for Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Monitors for Oracle", "value": "BC-CCM-MON-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "642723 - Monitoring of Oracle 9i V$UNDOSTAT view."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>A new monitor was delivered to display data from V$UNDOSTAT.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Oracle9, Oracle 9i, Undo Statistics, Undo view, RSORAUND, STD9.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Up to Oracle 9i it has become necessary to provide a new monitor to<br />display data from the Oracle view V$UNDOSTAT with some additional<br />statistics to make it easier to deal with the Undo data.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>NOTE : PLEASE NOTE THAT THE REPORT RSORAUND IS NO LONGER SUPPORTED.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; THE FUNCTIONALITY IS AVAILABLE IN THE TRANSACTION ST04N.<br /><br />Purpose of the monitor :<br /><br />The monitor is a standard R/3 screen that displays the contents of the<br />Oracle view V$UNDOSTAT with some statistical features(total, average,<br />minimum and maximum).<br />The monitor name is RSORAUND. It can be executed by calling transaction<br />se38.<br /><br />Contents of the view V$UNDOSTAT :<br /><br />Each row in the view V$UNDOSTAT contains statistics collected in the<br />instance over a ten-minute interval. The rows are sorted in descending<br />order by column BEGIN_TIME. Each row belongs to the time interval marked by (BEGIN_TIME, END_TIME).<br />Each column represents the data collected for the particular statistic<br />in that time interval. The first row of the view contains statistics<br />for the (partial) current time period. The view contains a total of<br />1008 rows, spanning a 7 day cycle.<br /><br />Description of the monitor :<br /><br />Through this monitor, the end user will have the possibility to see<br />all the rows and all the columns of the view V$UNDOSTAT.<br />The monitor will show a snapshot of the data retrieved via this view.<br />The user will be able to refresh the data on command.<br />Once the data is displayed, the user can perform a Sort operation on<br />any column he wants. Sorting according to more than one column will<br />not be permitted. By default, sorting will be carried out with respect<br />to the first column BEGIN_TIME.<br />For every column whose type is number, and for each day, the maximum,<br />minimum, average and total are displayed.<br />In order to provide an average number of transactions per second over<br />a 10 minute interval, a new column Number of Transactions per Second<br />has been added besides the column Number of Transactions.<br /><br />When running the monitor, the following functionalities are available:<br />-Hide Summaries :&#x00A0;&#x00A0;Display of the contents of the view V$UNDOSTAT<br />sorted by the column BEGIN_TIME.<br />-Refresh data : retrieves the data afresh from the Oracle database.<br />-Show Summaries: formatting the output in the screen so that all the<br />rows whose BEGIN_TIME belong to the same day are grouped together, and<br />the statistics (maximum, minimum, average and total) are displayed for<br />each column.<br />-Sort Ascending : Display of the contents of the view V$UNDOSTAT<br />sorted by the selected field in the ascending order, no more<br />than one field selection is possible.<br />-Sort Descending : Display of the contents of the view V$UNDOSTAT<br />sorted by the selected field in the descending order, no more than one<br />field selection is possible.<br />-Max Space Consumption : provides a list showing the maximum space<br />consumption for all UNDO_RETENTION time frames.<br /><br />Support packages distribution :<br />31I&#x00A0;&#x00A0; SP Level B2<br />40B&#x00A0;&#x00A0; SP Level 82<br />45B&#x00A0;&#x00A0; SP Level 60<br />46B&#x00A0;&#x00A0; SP Level 55<br />46C&#x00A0;&#x00A0; SP Level 45<br />46D&#x00A0;&#x00A0; SP Level 34<br />610&#x00A0;&#x00A0; SP Level 38<br />620&#x00A0;&#x00A0; SP Level 30<br />640&#x00A0;&#x00A0; SP Level 0</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I023385"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D021680)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000642723/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000642723/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000642723/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000642723/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000642723/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000642723/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000642723/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000642723/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000642723/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Datadisplay.zip", "FileSize": "14", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000639652003&iv_version=0002&iv_guid=9C16AC5E6AEA9941AD48622F745DE1E9"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "724078", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Monitoring of Undo statistics Oracle 9 monitor.", "RefUrl": "/notes/724078"}, {"RefNumber": "600141", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle9i: Automatic UNDO Management", "RefUrl": "/notes/600141"}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "724078", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Monitoring of Undo statistics Oracle 9 monitor.", "RefUrl": "/notes/724078 "}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46B", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "620", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31IB2", "URL": "/supportpackage/SAPKH31IB2"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31IC9", "URL": "/supportpackage/SAPKE31IC9"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B82", "URL": "/supportpackage/SAPKH40B82"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B60", "URL": "/supportpackage/SAPKH45B60"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B55", "URL": "/supportpackage/SAPKB46B55"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C45", "URL": "/supportpackage/SAPKB46C45"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D34", "URL": "/supportpackage/SAPKB46D34"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61038", "URL": "/supportpackage/SAPKB61038"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62030", "URL": "/supportpackage/SAPKB62030"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP129", "SupportPackagePatch": "000129", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP129", "SupportPackagePatch": "000129", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP129", "SupportPackagePatch": "000129", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP129", "SupportPackagePatch": "000129", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP067", "SupportPackagePatch": "000067", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP067", "SupportPackagePatch": "000067", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP067", "SupportPackagePatch": "000067", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP067", "SupportPackagePatch": "000067", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP130", "SupportPackagePatch": "000130", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP130", "SupportPackagePatch": "000130", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP130", "SupportPackagePatch": "000130", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP130", "SupportPackagePatch": "000130", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP203", "SupportPackagePatch": "000203", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP203", "SupportPackagePatch": "000203", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP203", "SupportPackagePatch": "000203", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP203", "SupportPackagePatch": "000203", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D 32-BIT", "SupportPackage": "SP2348", "SupportPackagePatch": "002348", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200002427&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D 64-BIT", "SupportPackage": "SP2348", "SupportPackagePatch": "002348", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200002567&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 32-BIT", "SupportPackage": "SP2348", "SupportPackagePatch": "002348", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007105&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 4.6D_EXT 64-BIT", "SupportPackage": "SP2348", "SupportPackagePatch": "002348", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200007108&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}