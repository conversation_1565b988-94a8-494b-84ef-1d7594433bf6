{"Request": {"Number": "2270407", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 294, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018249292017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=C7A72266A677132E1C4F3B98D080F885"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2270407"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.12.2020"}, "SAPComponentKey": {"_label": "Component", "value": "CO-OM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Overhead Cost Controlling"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Controlling", "value": "CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Overhead Cost Controlling", "value": "CO-OM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-OM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2270407 - S4TWL - Profit and Loss Planning,  profit center planning, cost center planning, order planning, and project planning"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ACDOCP</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This Simplification Item is relevant if:</p>\r\n<ul>\r\n<li>Classic Profit Center Planning is used if entries exist in Table GLPCP (check with SE16).</li>\r\n<li>CO-OM Planning is used if entries exist in Table COEJ (check with SE16).</li>\r\n<li>P&amp;L Planning is used: Use ST03N (on the production system, if possible) to check transaction profile of the complete last month, whether the following transactions have been used: FSE5N, FSE6N, FAGLPLSET, GP12N, GP12NA</li>\r\n<li>Production/process order planning is used if planned cost calculation is active for the combination of plant and order type and is carried out automatically when the production order (CO01 or CO02) or the process order (COR1 or COR2) is created, changed or released.Use Customizing T-codes COR4 and OPL8 to check whether plan costs are determined for production order and process orders.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Business Value</strong></p>\r\n<p>The universal journal is the unifying element for Accounting. From SAP S/4HANA 1610 it is possible to store&#160;the results of planning in a new table, ACDOCP,&#160;that has the same structure as the universal journal (ACDOCA). The same unifying approach is taken in planning, meaning that wages and salaries planned on a cost center are automatically assigned to the correct profit centers, functional areas, company codes, and so on and targets set by profit center can automatically be broken down to the assigned cost objects. In many organizations planning tasks had been moved to a data warehouse on account of the lack of flexibility in the ERP tables. This flexibility is now achieved by using SAP Analytics Cloud for Planning. This holds true for S/4HANA On-Premise and S/4HANA Cloud. Master data and transactional data can be sourced from SAP S/4HANA to SAP Analytics Cloud. In SAP Analytics Cloud pre-defined business content for Integrated Financial Planning can be leveraged as a project foundation. The business content is aligned to structures in S/4HANA and enables the E2E planning process.</p>\r\n<p><strong>Description</strong></p>\r\n<p>CO-OM planning, P&amp;L planning, and profit center planning are now covered by SAP Analytics Cloud for S/4HANA Finance. If you do not want to use SAP Analytics Cloud with S/4HANA On-Premise, but classic FI-GL and CO-OM planning functions instead, you&#160;can continue to use&#160;the classic planning transactions. Bear in mind, however, that in this case the planning results are written to the totals tables and require \"plan integration\" to share data between plans. Reports designed specifically for use with S/4 HANA Finance do not read these totals tables, so the planned data will only be visible in Report Writer/Painter transactions.</p>\r\n<ul>\r\n<li>FI-GL Planning - SAP Note 2253067 describes how to reactivate</li>\r\n<li>Profit Center Planning (classic) - SAP Notes 2345118 and 2313341 describe how to reactivate</li>\r\n<li>Cost Center Planning by Cost Element - no longer in menu but can be used in SAP S/4 HANA</li>\r\n<li>Order Planning by Cost Element - no longer in menu but can be used in SAP S/4 HANA</li>\r\n<li>Project Planning by Cost Element - no longer in menu but can be used in SAP S/4 HANA</li>\r\n<li>Project Planning Overall - no longer in menu but can be used in SAP S/4 HANA</li>\r\n</ul>\r\n<p>Customer project requires to import business content &#8220;Integrated Financial Planning for S/4HANA&#8221; from SAP Analytics Cloud content network and adapt the delivered objects to customer needs. Customers preferring to delay such a project can reactivate the classic planning transactions using the SAP Notes described above. Note that classic profit center planning is part of the compatibility scope and usage rights will expire in future.</p>\r\n<p>Note that it is also possible to run the two solutions in parallel, however this should only be taken into account to support a transition to ACDOCP with SAP Analytics Cloud. Running two solutions in parallel, either classic CO planning with SAP BPC for S/4HANA Finance or with SAP Analytics Cloud is not the strategic direction of SAP. SAP Note 2061419 describes how to retract data from SAP BPC for S/4HANA Finance into the totals tables so that this data can be used as a basis for cost center allocations and activity price calculation and order/project budget setting and settlements and to extract the data back to SAP BPC for S/4HANA Finance so that it is available for reporting when the planning cycle is complete.</p>\r\n<p>Transaction KEPM (Profitability Planning) supports both account-based CO-PA within the universal journal and costing-based CO-PA and is still in the menu. The investment planning and budgeting transactions are still in the menu and supported. As an alternative to transaction KEPM, you can now use the new planning applications in SAP BPC for market segment planning (amounts by market segment - available from 1511), sales planning (quantities and prices by market segment&#160;- available from 1610) and product cost simulation (valuation of sales quantities using product cost estimate - available from 1610).</p>\r\n<p>Planning data is also created automatically when production/process orders are saved or released. This planned data currently updates both table CKIS and COSP/COSS and the new planning table, ACDOCP. The new table stores two types of planned values for the production order: the planned costs (as in CKIS) and the standard costs adjusted for the order lot size (previously only accessed during target cost calculation). The two types of planned values are assigned to plan categories (PLANORD01:Production order plan costs and&#160;PLANORD02:production order standard costs) and provide the basis for target cost calculation in the SAP Fiori apps Production Cost Analysis and Analyze Costs by Work Center/Operation.</p>\r\n<p><strong>Business Process related information</strong></p>\r\n<p>To start with Integrated Financial Planning for S/4HANA the business content needs to be imported to SAP Analytics Cloud. See SAP Note 2977560 for more details. Implications:</p>\r\n<ul>\r\n<li>Customer roles (menus and authorizations) may need to be adapted.</li>\r\n<li>Decision needed as to when plan/actual reporting will be based on Universal Journal or take place in Integrated Financial Planning content in SAP Analytics Cloud.</li>\r\n<li>Decision needed as to whether planning data is needed for follow-on processes in S/4HANA Finance (e.g. target cost calculation for cost centers, project budgeting or order budgeting )</li>\r\n<li>Decision needed as to whether order and project planning is required for reporting and budgeting purposes in Investment Management</li>\r\n<li>Implications : SAP Analytics Cloud needs to be set up and configured.</li>\r\n</ul>\r\n<p>If SAP BPC for S/4HANA Finance is desired or required, then SAP BPC for S/4HANA Finance has to be set up (SAP Note 2081400). Implications:</p>\r\n<ul>\r\n<li>SAP BPC is not the strategic planning solution anymore. If an on-premise planning solution is a must, SAP BPC is a comprehensive and mature option. Anyhow this should only be considered as an intermediate step towards a transformation to SAP Analytics Cloud. Please check end-of-maintenance dates prior to any new BPC project.</li>\r\n<li>Customer roles (menus and authorizations) may need to be adapted.</li>\r\n<li>Decision needed as to whether plan/actual reporting will be based on totals tables or take place using SAP Fiori apps.&#160;</li>\r\n<li>Decision needed as to whether planning data is needed for follow-on processes in S/4HANA Finance (e.g. target cost calculation for cost centers, project budgeting or order budgeting )</li>\r\n<li>Decision needed as to whether order and project planning is required for reporting and budgeting purposes in Investment Management</li>\r\n<li>Implications : SAP BPC needs to be installed and configured, respective front-ends chosen, and so on.</li>\r\n<li>If you are intending to implement project planning, please ensure that there is a master data status in place to prevent changes to the WBS number (business transaction: Change WBS number) once planned data has been entered, since the content delivered for project planning (/ERP/PROJECT and /ERP/WBSELMT) uses a view to read the business definition of the project definition and WBS element rather than the internal ID. If the WBS number is changed, you will lose the link to the associated planned data. Please refer to the following SAP knowledge base article for further details.<br />https://launchpad.support.sap.com/#/notes/2162222</li>\r\n<li>If you currently calculate planned costs for production orders, no business process changes are required, but be aware that the information in the new planning table is only visible in Fiori apps, such as Production Cost Analysis and Analyze Costs by Work Center/Operation. The information shown in the SAPGUI transactions (CO01-3, COR1-3 and KKBC_ORD) is based on the old tables so you will not see costs by work center or operation in these transactions even though the additional detail is stored in the ACDOCP table.</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"186\">\r\n<p><strong>Transactions which have been removed from the menu in SAP S/4HANA on-premise edition 1511</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"569\">\r\n<p>GP12N, GP12NA, FAGLP03 (plan data entry in FI) plus plan assessment cycles (FAGLGA4* transactions) and plan distribution cycles (FAGLGA2* transactions) - can be reactivated using&#160;SAP Note 2253067.</p>\r\n<p>CJ40/CJ42 (overall planning on projects) KP06/KP07 (cost centre planning), CJR2/CJR3 (project planning) and KPF6/KPF7 (order planning) - femoved from menu but can still be used.</p>\r\n<p>7KE1-6 and 7KEP (profit center planning), IKE0 (transfer CO-OM plan to profit center plan) - part of compatability scope</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Required and Recommended Action(s)</strong></p>\r\n<ul>\r\n<li>Decision needed concerning scope of existing planning processes, requirements for plan/actual reporting and ability to use delivered business content for SAP Analytics Cloud or SAP BPC for S/4HANA Finance.</li>\r\n<li>No changes to existing tables, but table ACDOCP offered in addition from 1610.</li>\r\n<li>Follow steps in attachment provided with SAP Note 2661581 to set up plan categories. During system installation, upgrade, the pre-delivered table entries are only imported into client 000. For new client, the table entries can be copied to a new client during client copy automatically. But for existing client before the upgrade, the table entries should be imported into the target client manually.</li>\r\n<ul></ul>\r\n<li>Activate business content for Integrated Financial Planning in SAP Analytics Cloud.</li>\r\n</ul>\r\n<p><strong>Related SAP Notes</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"210\">\r\n<p>Conversion Pre-Checks</p>\r\n</td>\r\n<td valign=\"top\" width=\"545\">\r\n<p>Please check scope of existing planning activities and determine whether they can be covered using the business content currently available for SAP BPC for S/4HANA Finance. If you are currently using activity price calculation to set activity rates or Investment Planning, you can still use Integrated Business Planning for Finance but will need to retract the results to tables COSP_BAK, COSS_BAK to perform the follow-on processes. If you are using costing-based CO-PA the table structure will remain unchanged and there will be no loss of functionality.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"210\">\r\n<p>Related SAP Notes</p>\r\n</td>\r\n<td valign=\"top\" width=\"545\">\r\n<p>SAP Note: 2081400, 1972819</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL (General Ledger Accounting)"}, {"Key": "Other Components", "Value": "CO-PC-OBJ-ORD (Product Cost by Order)"}, {"Key": "Other Components", "Value": "EC-PCA-PLN (Planning Data)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D002766)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D002766)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2977560", "RefComponent": "CO-FIO-SAC-PL", "RefTitle": "Integrated Financial Planning for SAP S/4HANA with SAP Analytics Cloud", "RefUrl": "/notes/2977560"}, {"RefNumber": "2661581", "RefComponent": "CO-PC-OBJ", "RefTitle": "SAP S/4HANA: On-the-Fly Target Cost Calculation for Manufacturing Orders (Until SAP S/4HANA 2021)", "RefUrl": "/notes/2661581"}, {"RefNumber": "2345118", "RefComponent": "FI-GL", "RefTitle": "Reactivation of old planning functions in Profit Center Accounting", "RefUrl": "/notes/2345118"}, {"RefNumber": "2313341", "RefComponent": "FI-GL", "RefTitle": "Reactivation of transaction 1KE0", "RefUrl": "/notes/2313341"}, {"RefNumber": "2269324", "RefComponent": "XX-SER-REL", "RefTitle": "Compatibility Scope Matrix for SAP S/4HANA", "RefUrl": "/notes/2269324"}, {"RefNumber": "2253067", "RefComponent": "FI-GL-GL-G", "RefTitle": "Object changes for reactivation of G/L Planning", "RefUrl": "/notes/2253067"}, {"RefNumber": "2081400", "RefComponent": "CO-OM", "RefTitle": "SAP BPC Optimized for S/4 HANA Finance (aka: Integrated Business Planning for Finance): Compilation of Information", "RefUrl": "/notes/2081400"}, {"RefNumber": "1972819", "RefComponent": "CO-OM", "RefTitle": "Setup SAP BPC optimized for S/4 HANA Finance and Embedded BW Reporting (aka Integrated Business Planning for Finance)", "RefUrl": "/notes/1972819"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2977560", "RefComponent": "CO-FIO-SAC-PL", "RefTitle": "Integrated Financial Planning for SAP S/4HANA with SAP Analytics Cloud", "RefUrl": "/notes/2977560 "}, {"RefNumber": "2993220", "RefComponent": "EC-PCA", "RefTitle": "S4TWL - EC-PCA - Classical profit center accounting", "RefUrl": "/notes/2993220 "}, {"RefNumber": "2742613", "RefComponent": "CO-OM", "RefTitle": "Obsolete or replaced transaction codes and programs in Finance applications of S/4", "RefUrl": "/notes/2742613 "}, {"RefNumber": "2270335", "RefComponent": "FIN-MIG", "RefTitle": "S4TWL - Replaced Transaction Codes and Programs in FIN", "RefUrl": "/notes/2270335 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}