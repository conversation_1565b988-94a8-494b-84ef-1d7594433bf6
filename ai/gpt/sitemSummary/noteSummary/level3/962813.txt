SAP Note 962813 addresses a specific issue where the HEIGHT and WIDTH parameters of the Web items T<PERSON>KE<PERSON> and INFO FIELD are not being analyzed during runtime when set using the Web Application Designer (WAD).

Summary of the SAP Note:

- **Symptom**: The set values for HEIGHT and WIDTH of TICKER and INFO FIELD Web items are ignored at runtime.
  
- **Other Terms**: Keywords related to this note include WAD, parameter, height, width.

- **Reason**: The cause for this issue is identified as a program error.

- **Prerequisites**: The issue occurs when values are entered for the height and width of the TICKER or INFO FIELD Web items in the WAD.

- **Solution**: To resolve the problem, users must import Support Package 09 for SAP NetWeaver Java 2004s BI into their BI system. The specific files mentioned for the import are BIIBC09_0.SCA, BIBASES09_0.SCA, and BIWEBAPP09_0.SCA. The Support Package details are also described in SAP Note 0924709 titled "SAPBINEWS NW04s BI JAVA SP09". 

  Users may find preliminary information in the above-mentioned notes before the Support Package release, including details on the patch name. The note also emphasizes that both ABAP and Java Support Packages must be imported synchronously.

  For further details on deploying Java Support Packages with the Software Deployment Manager (SDM), the note references SAP Note 656711.

In summary, SAP Note 962813 informs users of a programming error affecting the processing of height and width parameters for specific Web items, and it provides instructions for obtaining and applying the appropriate Support Packages to correct the error.