SAP Note 1842974 addresses multiple issues identified with the system copy automation process in SAP NetWeaver Business Warehouse (BW) systems. Below is a summary of the issues and the proposed solution:

**Symptoms:**
1. The TSPREFIX renaming process does not correctly convert hierarchy transfer structures, IDoc segments, message types, and function module assignments.
2. Deleting obsolete source systems is not carried out in parallel.
3. Required SAP Notes are not checked in the source system during check mode.
4. Some tasks display the incorrect phase in the system.
5. The class `CL_RSO_LOGICAL_SYSTEM_DELETE` incorrectly deletes systems during refresh that are connected both to the original and target systems.
6. The class `CL_RSO_UPDATE_BWMANDTRFC_HOST` doesn't refresh the destination if it lacks a host but has a user, for example.

**Reason and Prerequisites:**
The issues are caused by a program error.

**Solution:**
The solution involves importing specific Support Packages for various versions of SAP NetWeaver BW:
- SAP NetWeaver BW 7.00: Import Support Package 31 (SAPKW70031)
- SAP NetWeaver BW 7.01: Import Support Package 14 (SAPKW70114)
- SAP NetWeaver BW 7.02: Import Support Package 14 (SAPKW70214)
- SAP NetWeaver BW 7.11: Import Support Package 12 (SAPKW71112)
- SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010)
- SAP NetWeaver BW 7.31: Import Support Package 08 (SAPKW73108)
- SAP NetWeaver BW 7.40: Import Support Package 3 (SAPKW74003)

The related SAP Notes for each Support Package provide more details on the contents and will be released for customers once available. In urgent situations, correction instructions can be implemented as an advance correction.

It's also recommended to read SAP Note 1668882 for information about using transaction SNOTE.

Further, the SAP Notes with "Preliminary version" in the short text are available in advance before the release of the Support Package to provide early information to users.