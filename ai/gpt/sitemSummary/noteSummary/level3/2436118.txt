SAP Note 2436118 addresses an update in the consistency checks within the SAP Revenue Accounting module. The note summarizes the following points:

Symptom:
- The previous check for profitability segment number (known as E16) in posting entries is outdated and will be turned off.

Solution:
- The note introduces a new E16 check that ensures the total value of transferred invoices in local currency on POB (Performance Obligation) level matches with the posted invoice values. This new check is designed to confirm consistency between the values in table FARR_D_INVOICE (which stores invoice data) and table FARR_D_POSTING (which stores invoice correction entries), both denominated in the local currency.

Other Terms:
- The note lists a number of related terms and technical components such as function modules and includes, specifically the function module FARR_CONS_CHECK_CONTRACT and the include LFARR_CONS_CHECKF01.

Reason and Prerequisites:
- The old E16 check was made obsolete because it did not represent a genuine inconsistency check. Its primary function was to ensure that profitability segment numbers were not missing, which was a safeguard for revenue accounting posting runs.
- The new E16 check is introduced to address actual inconsistencies in local currency values at the POB level and is considered to be in line with the logic of another existing check, E13, but specifically for local currency adjustments.

In summary, this note deprecates an old consistency check regarding profitability segment numbers and introduces a new check focusing on the equality of invoice values in local currency within the Revenue Accounting module. The technical details provided ensure that system users and administrators can understand the changes and implement them as needed.