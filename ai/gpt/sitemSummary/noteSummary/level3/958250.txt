SAP Note 958250 addresses an issue with query definitions in the SAP Query Designer. After a query definition is changed and executed, it may display correctly the first time but could revert to the obsolete definition upon subsequent executions. This issue is attributed to buffering within a database table, which can cause outdated information to be presented if the query is executed from a different application server.

The note specifies that the problem stems from the buffering of the RSRREPDIR table. To manually correct this error before a formal correction is available, the note advises to deactivate the buffering of this table. This can be done by going to Transaction SE11, accessing the technical settings, and ensuring that 'Buffering not permitted' is selected while 'fully buffered' is deselected.

For a more structured solution, users are guided to import Support Package 09 for SAP NetWeaver 2004s BI (BI Patch 09 or SAPKW70009) into their BI system. Details on this Support Package are provided in Note 0914303 titled "SAPBINews BI 7.0 SP09".

In case of urgency, correction instructions may be available before the official release of Support Packages. Preliminary versions of notes like these will specify their pre-release status by including the words "preliminary version" in the short texts.