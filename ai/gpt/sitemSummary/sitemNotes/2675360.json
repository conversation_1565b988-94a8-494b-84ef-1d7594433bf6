{"Request": {"Number": "2675360", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 444, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001845152018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=AB38B632E66210B8B5BDB8B45ACB7BEE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2675360"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.04.2022"}, "SAPComponentKey": {"_label": "Component", "value": "FI-RA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Revenue Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Revenue Accounting", "value": "FI-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2675360 - Revenue Accounting and Reporting with SAP S/4HANA 1809: Release Information Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This Release Information Note (RIN) contains information and references to notes for using Revenue Accounting and Reporting with SAP S/4HANA 1809. Starting with SAP S/4HANA 1809, the former Revenue Accounting and Reporting add-on including the add-on SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0 have become an integral part of SAP S/4HANA.</p>\r\n<p>Development close between SAP Revenue Accounting and Reporting 1.3 Feature Pack 6 and SAP S/4HANA 1809 have been aligned. As a result, features available in RAR 1.3 Feature Pack 6 are considered to be available in S/4HANA 1809.</p>\r\n<p>The same applies to RAR 1.3 Feature Pack 7 and S/4HANA 1809 Feature Pack 1.</p>\r\n<p>Please note that some of the referenced notes below refer to RAR 1.3. As far as the notes mention additional information, this information also applies to SAP S/4HANA 1809.</p>\r\n<p><strong>Note:</strong> This SAP note is subject to change. Check this note for changes on a regular basis. All important changes are documented in section \"Important Changes after Release of SAP S/4HANA 1809\".</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to use Revenue Accounting and Reporting with S/4HANA 1809.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note covers the following topics:</p>\r\n<ul>\r\n<li><a target=\"_self\" href=\"#Important\">Important Information</a></li>\r\n<li><a target=\"_self\" href=\"#Activation\">Activation of Revenue Accounting and Reporting</a></li>\r\n<li><a target=\"_self\" href=\"#Technical_Landscape\">Technical System Landscape</a></li>\r\n<li><a target=\"_self\" href=\"#Important_Changes\">Important changes with S/4HANA 1809</a></li>\r\n<li><a target=\"_self\" href=\"#Implementation_Information\">Implementation information after upgrade</a></li>\r\n<li><a target=\"_self\" href=\"#Important_Notes\">Important SAP Notes</a></li>\r\n<li><a target=\"_self\" href=\"#Sizing\">Sizing information</a></li>\r\n<li><a target=\"_self\" href=\"#Inflight_Checks\">Inflight Checks</a></li>\r\n<li><a target=\"_self\" href=\"#Data_Validation\">Data Validation Checks</a></li>\r\n<li><a target=\"_self\" href=\"#Migration_Transition\">Migration and Transition</a></li>\r\n<li><a target=\"_self\" href=\"#External_Sender\">Integrating with external sender components</a></li>\r\n<li><a target=\"_self\" href=\"#SD\">Integrating with SAP Sales and Distribution (SD)</a></li>\r\n<li><a target=\"_self\" href=\"#Security\">Security Information</a></li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"Important\"></a>&#65279;Important Information</strong></p>\r\n<p>To integrate with sender components such as SAP Sales and Distribution or other sender components, Revenue Accounting uses RFCs as part of the Inbound Processing configuration.</p>\r\n<p>For proper integration, you have to either implement SAP Note&#160;<a target=\"_blank\" href=\"/notes/2957643\">2957643 - RFC Fast Serialization Error in compatibility mode</a>&#160;or you must not use RFC Destination 'NONE'.</p>\r\n<p><strong><a target=\"_blank\" name=\"Activation\"></a>&#65279;Activation of Revenue Accounting and Reporting</strong></p>\r\n<p>Revenue Accounting and Reporting was subject to a release restriction&#160;(similar to the add-on early adopter care process).</p>\r\n<p>Depending on the support package you have applied, you may get&#160;information message FARR_FOUNDATION 301, which prevents the configuration of accounting principle information for Revenue Accounting.</p>\r\n<p>In the meanwhile, Revenue Accounting and Reporting can be implemented and used without restrictions. Please implement note 2675322. The correction in the note will remove the above mentioned message and unlock the configuration of accounting principle settings for Revenue Accounting.</p>\r\n<p><strong><a target=\"_blank\" name=\"Technical_Landscape\"></a>&#65279;Technical System Landscape</strong></p>\r\n<div>\r\n<div>\r\n<div>\r\n<p>As of SAP S/4HANA 1809, the former SAP Revenue Accounting and Reporting add-on has become an integral part of SAP S/4HANA. This relates to product version SAP REVENUE ACCOUNTING including software component version REVREC.</p>\r\n<p>The Revenue Accounting and Reporting functionality still needs to be integrated into operational components which send order and billing information to Revenue Accounting. With SAP S/4HANA 1809, the following operational components, or products, support integration with Revenue Accounting:</p>\r\n<ul>\r\n<li>Sales and Distribution (SD)</li>\r\n<li>Billing and Revenue Innovation Management (BRIM) also known as SAP Hybris Billing</li>\r\n<li>SAP Customer Relationship Management (CRM)</li>\r\n</ul>\r\n<p>For the integration with Sales and Distribution, the integration functionality previously deployed through the software component SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0 (SAP SALES INTEGR SAP RAR 1.0) has also been added to the SAP S/4HANA 1809 stack.</p>\r\n<p>For more information especially on distributed system scenarios, please refer to the product assistance for S/4 HANA</p>\r\n<p>Choose Enterprise Business Applications -&gt; Finance -&gt; Accounting and Financial Close -&gt; Revenue Accounting and Reporting -&gt; Integration of Sender Components&#160;&#160; -&gt; Technical System Landscape</p>\r\n<p><strong><a target=\"_blank\" name=\"Important_Changes\"></a>&#65279;Important changes with S/4HANA 1809</strong></p>\r\n<p>From SAP S/4HANA OP 1809 on, SAP changed the data types for the contract and performance obligation IDs from NUMC14 to CHAR14 and from NUMC16 to CHAR16 respectively. The change from NUMC to CHAR was done to simplify and optimize (performance) the internal handling of temporary IDs. There is no migration needed. Data will be migrated on the fly where needed. Nevertheless, this change might have an impact on your custom code. Please follow the guidelines in note <a target=\"_blank\" href=\"/notes/2672794\">2672794</a>.</p>\r\n<p><strong><a target=\"_blank\" name=\"Implementation_Information\"></a>&#65279;Implementation Information after upgrade</strong></p>\r\n<p>A direct upgrade from Revenue Accounting and Reporting 1.2 (anyDB or S/4HANA) is not supported. If you had previously deployed the add-on, you will first have to upgrade the add-on to RAR 1.3. Please note that&#160;RAR 1.3 is not yet available for general download. SAP decided to utilize the <strong>Early Adopter Care</strong> process for download, to get a better insight into running projects. To download the solution please issue a nomination under: https://influence.sap.com/sap/ino/#/campaign/70-&gt; Register as Customer or Register as Partner, which will then be approved.</p>\r\n<p>An upgrade from Revenue Accounting 1.3 on anyDB or an earlier S/4HANA release follow the common upgrade process. There are no specific procedures in the case of Revenue Accounting. Especially no changes requiring mandatory migration steps with regards to database tables are expected. The same applies to the user interface and process flows.</p>\r\n<p>Depending on the feature pack of RAR 1.3 you upgrade from, you may need to regenerate the RAI classes as new fields had been added:</p>\r\n<p>To regenerate the RAI classes, you need to take the following steps:</p>\r\n<ul>\r\n<li>Start transaction FARR_RAI_CONF.</li>\r\n<li>Mark a Revenue Accounting Item Class.</li>\r\n<li>Press the Selected entries button.</li>\r\n</ul>\r\n<p>The system checks whether the configuration of the revenue accounting item class contains all fields that are available in the interface components.</p>\r\n<p>If none of the applied interface components were enhanced, it is not necessary to update the configuration and the configuration keeps the status active. In such a case, you can stop here and immediately proceed with the next revenue accounting item class.</p>\r\n<p>If an interface component was enhanced during the upgrade, the system detects this and automatically updates the configuration. In this case, the configuration of the revenue accounting item class sets the status to Modified. You then need to take the following steps:</p>\r\n<ol>\r\n<li>Save the updated configuration of your revenue accounting item class.</li>\r\n<li>Activate the configuration. Afterwards you can see that the status of the configuration of the revenue accounting item class is active.</li>\r\n<li>Start transaction FARR_RAI_GEN by choosing Environment -&gt; Generation.</li>\r\n<li>Mark the updated revenue accounting item class and press Generate.</li>\r\n<li>Choose Yes when you are asked whether you want to run the generation immediately.</li>\r\n<li>Choose No when you are asked whether you want to delete revenue accounting items.</li>\r\n</ol>\r\n<p><strong>Note</strong></p>\r\n<p>You cannot delete revenue accounting items in production systems. You are therefore not asked whether you want to delete revenue accounting items that are available in production systems. Once this step is complete, you will receive a popup with the generated results. All status icons should be green.</p>\r\n<p>7. Repeat these steps for all revenue accounting item classes.</p>\r\n<div>\r\n<div>\r\n<div>\r\n<p><strong><a target=\"_blank\" name=\"Important_Notes\"></a>&#65279;Important SAP Notes </strong></p>\r\n<p>Please read the following SAP Notes before you start the implementation of SAP Revenue Accounting and Reporting as part of S/4HANA 1809.</p>\r\n<p>Make sure that you have the most up-to-date version of each SAP Note, which you can find on the SAP Support Portal at <a target=\"_blank\" href=\"https://support.sap.com/notes\">https://support.sap.com/notes</a> Information published on SAP site.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 1339px;\"><colgroup> <col width=\"108\" /> <col width=\"65\" /> <col width=\"1166\" /></colgroup>\r\n<tbody>\r\n<tr>\r\n<td class=\"xl66\" height=\"18\" width=\"108\"><strong>SAP Component</strong></td>\r\n<td class=\"xl66\" width=\"65\"><strong>Number</strong></td>\r\n<td class=\"xl66\" width=\"1166\"><strong>Title</strong></td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA-VAL</td>\r\n<td class=\"xl75\">2653855</td>\r\n<td>RA - Data Validation / Contract combination results in E06 and E07 errors</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">SD-BIL-RA</td>\r\n<td class=\"xl69\">2656328</td>\r\n<td>Condition amount of SDPI differs from invoiced amount</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA-INV</td>\r\n<td class=\"xl76\">2662861</td>\r\n<td>Transaction price not updated with invoiced amount if invoiced amount is greater than transaction price (II)</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA</td>\r\n<td class=\"xl69\">2666119</td>\r\n<td>Fully Fulfilled Flag of a Performance Obligation Should Be Marked as True If the Effective Quantity is Zero And Last Change On and Last Change By is Not Updated</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA</td>\r\n<td class=\"xl75\">2666949</td>\r\n<td>Clear All Statistic Line Items After The First Revenue Posting</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\"><strong>SD-BIL-RA</strong></td>\r\n<td class=\"xl72\"><strong>2670345</strong></td>\r\n<td><strong>Credit Memo Request for Return Order - credit memo RAI items not generated</strong></td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA-VAL</td>\r\n<td class=\"xl75\">2671716</td>\r\n<td>RA - Data Validation/ E03 When Sum Of Fulfilled Quantity Numerator/Denominator Has Minimal Difference Compared to Effective Quantity</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA-MIG</td>\r\n<td class=\"xl69\">2673769</td>\r\n<td>Fix Dump When Customized Message Is Switched Off</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA</td>\r\n<td class=\"xl69\">2676282</td>\r\n<td>A Dump Occurs When You Run Revenue Transfer for Thousands Performance Obligations</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl70\" height=\"18\"><strong>SD-BIL-RA</strong></td>\r\n<td class=\"xl73\"><strong>2677479</strong></td>\r\n<td><strong>Operational load: LOAD and RESET of SD Revenue Recognition documents deactivated in S4CORE systems&#160;</strong></td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">SD-BIL-RA</td>\r\n<td class=\"xl69\">2677492</td>\r\n<td>Operational load: Wrong determination of predecessor item when migrating return orders</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA-CP</td>\r\n<td class=\"xl69\">2679358</td>\r\n<td>Zero Amount Deferral Item Is Deleted After Contract Modification</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA-CP</td>\r\n<td class=\"xl69\">2679389</td>\r\n<td>System Raises A Dump Error CONVT_NO_NUMBER From Method CL_FARR_CONTRACT_MGMT-&gt;ADJUST_POB_STRUCTURE</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl70\" height=\"18\">SD-BIL-RA</td>\r\n<td class=\"xl71\">2679649</td>\r\n<td>No Condition RAI for Intercompany Condition</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA-VAL</td>\r\n<td class=\"xl75\">2680610</td>\r\n<td>RA - Inflight Check: No Change Type Record with Change Mode 'C' results in C26 Error</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA-VAL</td>\r\n<td class=\"xl75\">2682909</td>\r\n<td>RA - Inflight Check: Data selection issue due to NUMC to CHAR conversion of POB_ID</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl70\" height=\"18\">SD-BIL-RA</td>\r\n<td class=\"xl71\">2683430</td>\r\n<td>Incorrect currency in IC Fulfillment RAI</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl70\" height=\"18\">SD-BIL-RA</td>\r\n<td class=\"xl71\">2684255</td>\r\n<td>No Fulfillment RAI for Intercompany Invoice</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl70\" height=\"18\">SD-BIL-RA</td>\r\n<td class=\"xl71\">2684586</td>\r\n<td>Error FARR_RAI 842 when processing order RAI with IC condition</td>\r\n</tr>\r\n<tr>\r\n<td height=\"18\">FI-RA-PC</td>\r\n<td class=\"xl75\">2686122</td>\r\n<td>Migration Reconciliation Key Is Not Saved To Database Table</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl70\" height=\"18\">FI-RA-CP</td>\r\n<td class=\"xl71\">2692974</td>\r\n<td class=\"xl70\">Reconciliation Key Buffer is Not Refreshed</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><a target=\"_blank\" name=\"Sizing\"></a>&#65279;Sizing Information</strong></p>\r\n<p>Performance and memory consumption is an important topic, especially if you have to process millions of Revenue Accounting contracts.</p>\r\n<p>Generally, the following SAP Notes should be considered:</p>\r\n<p><a target=\"_blank\" href=\"/notes/2551667\">2551667</a> - SAP Revenue and Reporting: Special Considerations for Large Number of POBs in one Revenue Accounting Contract</p>\r\n<p><a target=\"_blank\" href=\"/notes/2616387\">2616387</a> - Important Performance Considerations when implementing SAP Revenue Accounting and Reporting</p>\r\n<p>These notes apply to the add-on SAP RAR 1.3 as for Revenue Accounting within SAP S/4HANA 1809.</p>\r\n<p>You can find the sizing guide for Revenue Accounting and Reporting and further details on sizing on CPU sizing and Disk sizing on the SAP Help Portal: <a target=\"_blank\" href=\"https://www.sap.com/about/benchmark/sizing.html\">https://www.sap.com/about/benchmark/sizing.html</a></p>\r\n<p>Sizing Guidelines&#160;-&gt; SAP Business Suite Applications -&gt; SAP ERP&#160;-&gt; &#160;Sizing Guideline for SAP Revenue Accounting and Reporting 1.3.</p>\r\n<p><strong><a target=\"_blank\" name=\"Inflight_Checks\"></a>&#65279;Inflight Checks</strong></p>\r\n<p>Inflight Checks are proactive runtime checks that are implemented in the solution to validate data before committing it to the database.</p>\r\n<p>The software also contains post database commit checks which validate data that is already written to the database tables. This part of the solution is called Data Validation Check which implements verifications of equivalent error categories.</p>\r\n<p>You can find a detailed chapter on Inflight Checks in SAP Note <a target=\"_blank\" href=\"/notes/2533254\">2533254</a> or the SAP S/4HANA 1809 product assistance for Revenue Accounting (Choose Revenue Accounting and Reporting -&gt; Administration and Maintenance -&gt; Operation Information -&gt; Inflight Checks).</p>\r\n<p><strong><a target=\"_blank\" name=\"Data_Validation\"></a>&#65279;Data Validation Checks</strong></p>\r\n<p>Revenue Accounting and Reporting provides you with two reports to perform data validation in the Revenue Accounting engine. The reports are designed to validate revenue accounting data and spot potential issues as early as possible.</p>\r\n<p>You can find additional information concerning the data validation check in SAP Note <a target=\"_blank\" href=\"/notes/2567106\">2567106</a> or the SAP S/4HANA 1809 product assistance for Revenue Accounting (Choose Revenue Accounting and Reporting -&gt; Administration and Maintenance -&gt; Operation Information -&gt; Data Validation).</p>\r\n<p><strong><a target=\"_blank\" name=\"Migration_Transition\"></a>&#65279;Migration and Transition</strong></p>\r\n<p>Data Migration from operational systems to SAP Revenue Accounting is a critical part in the implementation and operation of SAP Revenue Accounting and Reporting. In addition, customers may want to switch from an existing accounting standard to a new one, for example, IFRS15. This process is referred to as Transition.</p>\r\n<p>For migration, you can find additional information in the SAP S/4HANA 1809 product assistance for Revenue Accounting in the chapter Migration.</p>\r\n<p>Please carefully follow the instructions in the application help specific to the implementation scenario, e.g. integration with SAP SD or Third Party Sender integration.</p>\r\n<p>For transition, you can find additional information in the SAP S/4HANA 1809 product assistance for Revenue Accounting in the chapter Transition.</p>\r\n<p>The following notes provide additional information in the context of migration and transition.</p>\r\n<p><a target=\"_blank\" href=\"/notes/2580961\">2580961</a> - RAR Data Migration and Transition - Additional Information</p>\r\n<p><a target=\"_blank\" href=\"/notes/2569950\">2569950</a> - FAQ: Migration &amp; Operational Load in the SD Integration Component</p>\r\n<p><strong><a target=\"_blank\" name=\"External_Sender\"></a>&#65279;Integrating with external sender components</strong></p>\r\n<p>For integrating with external sender components please refer to the user assistance of S/4HANA. Choose Revenue Accounting and Reporting -&gt; Integration of Sender Components -&gt; Integration of External Sender Components.</p>\r\n<p>In addition, please consider the following note:</p>\r\n<p><a target=\"_blank\" href=\"/notes/2392956\">2392956</a> - SAP Revenue Accounting and Reporting 1.3: Integrating External (non-SAP) Sender Components</p>\r\n<p><strong><a target=\"_blank\" name=\"SD\"></a>&#65279;Integrating with SAP Sales and Distribution (SD)</strong></p>\r\n<div>\r\n<div>\r\n<div>\r\n<p>For integration with Sales and Distribution (SD) please refer to the user assistance of S/4HANA. Choose Revenue Accounting and Reporting -&gt; Integration of Sender Components -&gt; Sales and Distribution Integration with Revenue Accounting.</p>\r\n<p>In addition, please consider the following notes:</p>\r\n<p><a target=\"_blank\" href=\"/notes/2225170\">2225170</a> - S/4 HANA SD-Revenue Recognition</p>\r\n<p><a target=\"_blank\" href=\"/notes/2341717\">2341717</a> - FAQ: Future of SD Revenue Recognition after IFRS15 is released</p>\r\n<p><a target=\"_blank\" href=\"/notes/2591055\">2591055</a> - Functional limitations in the SD Integration Component</p>\r\n<p><a target=\"_blank\" href=\"/notes/2610856\">2610856</a> - FAQ: Condition handling in SD Integration Component</p>\r\n<p><a target=\"_blank\" href=\"/notes/2624932\">2624932</a> - Handling of bill of material scenarios in the SD integration component</p>\r\n<p><strong><a target=\"_blank\" name=\"Security\"></a>&#65279;Security </strong><strong>Information</strong></p>\r\n<div>\r\n<div>\r\n<div>\r\n<p>You can find additional security related information about Revenue Accounting and Reporting in the Security Guide for SAP S/4HANA 1809.</p>\r\n<p>Choose SAP S/4HANA Business Applications -&gt; Finance -&gt; Financial Accounting -&gt; Revenue Accounting and Reporting (FI-RA).</p>\r\n<p>In addition, you may find the following information about roles and authorization controls helpful.</p>\r\n<p>Revenue Accounting and Reporting uses the authorization concept provided by SAP NetWeaver AS ABAP. Therefore, the recommendations and guidelines for authorizations, as described in the SAP NetWeaver AS Security Guide ABAP, also apply to Revenue Accounting and Reporting.</p>\r\n<p>The SAP NetWeaver authorization concept is based on assigning authorizations to users based on roles. For role maintenance, use the profile generator (transaction PFCG) on the AS ABAP.</p>\r\n<p><strong>Standard Roles</strong></p>\r\n<p>The table below shows the standard roles that are used by Revenue Accounting and Reporting.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"208\">\r\n<p><strong>Role</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"397\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"208\">\r\n<p>SAP_SR_FARR_REV_ACCOUNTANT</p>\r\n</td>\r\n<td valign=\"top\" width=\"397\">\r\n<p>Revenue Accountant</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"208\">\r\n<p>SAP_SR_FARR_REV_ACCOUNTANT_A</p>\r\n</td>\r\n<td valign=\"top\" width=\"397\">\r\n<p>Revenue Accountant (Authorization)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"208\">\r\n<p>SAP_SR_FARR_REV_ADMIN</p>\r\n</td>\r\n<td valign=\"top\" width=\"397\">\r\n<p>Revenue Accounting Administrator</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"208\">\r\n<p>SAP_SR_FARR_REV_ADMIN_A</p>\r\n</td>\r\n<td valign=\"top\" width=\"397\">\r\n<p>Revenue Accounting Administrator (Authorization)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"208\">\r\n<p>SAP_SR_FARR_REV_AUDITOR</p>\r\n</td>\r\n<td valign=\"top\" width=\"397\">\r\n<p>Revenue Accounting Auditor</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"208\">\r\n<p>SAP_SR_FARR_REV_AUDITOR_A</p>\r\n</td>\r\n<td valign=\"top\" width=\"397\">\r\n<p>Revenue Accounting Auditor (Authorization)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"208\">\r\n<p>SAP_SR_FARR_REV_RFCUSER_A</p>\r\n</td>\r\n<td valign=\"top\" width=\"397\">\r\n<p>Revenue Accounting RFC User (Authorization)</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>For more information about roles delivered for Revenue Accounting and Reporting, see the documentation under Roles in the product assistance.</p>\r\n<p><strong>Standard Authorization Objects</strong></p>\r\n<p>The table below shows the security-relevant authorization objects that are used in Revenue Accounting and Reporting.</p>\r\n<p>&#160;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 604px;\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p><strong>Authorization Object&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p><strong>Field&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p><strong>Description&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p><strong>Value&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RR_ACCR</p>\r\n<p>With this authorization object, you can restrict the authorization to perform revenue postings and run reconciliation reports</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>BUKRS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Company code</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>03 Display</p>\r\n<p>10 Post</p>\r\n<p>48 Simulate</p>\r\n<p>93 Calculate</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACCRULE</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Accounting Principle (AP)</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RR_ACCTD</p>\r\n<p>With this authorization object, you can configure the authorization check for account determination configuration in Revenue Accounting. You can restrict the authorizations to display or change Account Determination configurations on the Account Determination user interface launched from Customizing.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>02 = Change</p>\r\n<p>03 = Display</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RR_CLOSE</p>\r\n<p>With this authorization object, you can configure the authorization check for the following tasks in Revenue Accounting.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACCRULE</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Accounting Principle (AP)</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>03 = Display</p>\r\n<p>For running the audit report</p>\r\n<p>50 = Move</p>\r\n<p>For shifting contracts into the next period</p>\r\n<p>PB = Close Period</p>\r\n<p>For opening and closing revenue accounting periods</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>BUKRS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Company Code</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RR_CONTR</p>\r\n<p>With this authorization object, you can configure the authorization check for revenue accounting contracts in Revenue Accounting. This authorization check is only for access performed from the Contract Management user interface.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>01 = Create or generate</p>\r\n<p>02 = Change</p>\r\n<p>03 = Display</p>\r\n<p>A3 = Change status</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>BUKRS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Company Code</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>FARR_VKORG</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Sales Organization (for Example, Compatible with SD or CRM)</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RR_INITD</p>\r\n<p>With this authorization object, you can restrict the authorization to clean up leftover data that was generated by a previous initial load job. You need to run a cleanup typically when you find some incorrect data after completing an initial load job.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>01 = Create or generate</p>\r\n<p>06 = Delete</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACCRULE</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Accounting Principle (AP)</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>BUKRS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Company Code</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RR_MFUFI</p>\r\n<p>With this authorization object, you can restrict the authorization to manually fulfill performance obligations.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>16 = Execute</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RR_PRODD</p>\r\n<p>With this authorization object, you can restrict the authorization to clean up productive data.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>06 = Delete</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>BUKRS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Company Code</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RR_STATU</p>\r\n<p>With this authorization object, you can restrict the authorization to perform POB status and review reason change.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>02 = Change</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>POB_STATUS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Performance Obligation Status</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>REVIEWREAS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Review Reason Code</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RRBIZREC</p>\r\n<p>With this authorization object, you can restrict the authorization to execute the business reconciliation program and restrict the authorization to display the business reconciliation result.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACCRULE</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Accounting Principle (AP)</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>03 = Display</p>\r\n<p>16 = Execute</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>BUKRS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Company Code</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RRCONSIS</p>\r\n<p>With this authorization object, you can restrict the authorization to execute the data validation program (transaction codes: FARR_CONTR_CHECK and FARR_CONTR_MON) and restrict the authorization to display the data validation result.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACCRULE</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Accounting Principle (AP)</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>03 = For executing and displaying the data validation result</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>BUKRS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Company Code</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RRNEWACP</p>\r\n<p>With this authorization object you can restrict the authorization for the following processes performed during transition:</p>\r\n<p>&#160;</p>\r\n<p>Copy data from one accounting principle to another new accounting principle.</p>\r\n<p>Cleanup data which resulted from this copying process. Typically, you need to run a cleanup when you find some incorrect data after copying.</p>\r\n<p>Execution of the Cumulative Catchup and Copy of Deferred and Unbilled Receivables</p>\r\n<p>Calculation of the Comparative Results</p>\r\n<p>Display of the comparative report results</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACCRULE</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Accounting Principle (AP)</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>01 Copy Accounting Principle</p>\r\n<p>03 Comparative Report: Display of Results</p>\r\n<p>06 Cleanup new Accounting Principle</p>\r\n<p>16 Execute Cumulative Catchup and Reversal of Migrated Unbilled Receivable/Deferred Revenue in Target Accounting Principle at Transition</p>\r\n<p>93 Comparative Report Calculation</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>BUKRS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Company Code</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RRRAI</p>\r\n<p>You use this authorization object to define which activities a user is allowed to execute for revenue accounting items</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>01 Create raw revenue accounting items and transfer items to status processable</p>\r\n<p>03 Display revenue accounting items</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>BUKRS</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Company Code</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RRRAIADM</p>\r\n<p>You use this authorization object to define whether a user is allowed to change revenue accounting items when displaying them in transaction FARR_RAI_MON.</p>\r\n<p>&#160;</p>\r\n<p>As manual changes to revenue accounting items can lead to problems during reconciliation, this authorization should be restricted to a limited number of experienced users.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>02 Change</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RRRAICON</p>\r\n<p>&#160;</p>\r\n<p>This authorization object protects the configuration of revenue accounting item classes.</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>02 Create and change configuration</p>\r\n<p>07 Generateobjects for class</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RRRAIEXC</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>01 -&#160; Add</p>\r\n<p>&#160;</p>\r\n<p>The user must have this authorization to be able to exempt a revenue accounting item from further processing (exemption).</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>06 - Delete</p>\r\n<p>&#160;</p>\r\n<p>The user must have this authorization to be able to delete exempted revenue accounting items.</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>85 - Reverse</p>\r\n<p>&#160;</p>\r\n<p>The user must have this authorization to be able to remove the exemption from an exempted item (restoration).</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"151\">\r\n<p>F_RRRAITST</p>\r\n<p>You use this authorization object to define whether a user can generate test data (revenue accounting items).</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>ACTVT</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>Activity</p>\r\n<p>&#160;</p>\r\n</td>\r\n<td valign=\"top\" width=\"151\">\r\n<p>01 Create test data with report FARR_RAI_SAMPLEF</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Additional relevant Authorization Objects</strong></p>\r\n<p>The following table displays authorization checks that BRFplus uses for Revenue Accounting.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"201\">\r\n<p><strong>Authorization Object</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p><strong>Field</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"201\">\r\n<p>FDT_OBJECT <br />Authority-Check on Object Level</p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p>FDT_APPL</p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p>Check during BRFplus maintenance</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"201\">\r\n<p>FDT_PROC Processing (Web Service/RFC)</p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p>FDT_APPL</p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p>Check during BRFplus function call</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"201\">\r\n<p>FDT_TRACE Authority-Check on Trace</p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p>FDT_APPL</p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p>Checked during BRFplus tracing</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following table displays authorization checks that Information Lifecycle Management (ILM) uses for archiving.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"201\">\r\n<p><strong>Authorization Object</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p><strong>Field</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p><strong>Description</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"201\">\r\n<p>S_ARCHIVE</p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p>ARCH_OBJ</p>\r\n</td>\r\n<td valign=\"top\" width=\"201\">\r\n<p>Enables the customer to control the archiving and read archived data at Archiving Object level</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SD-BIL-RA (SD Integration Revenue Accounting & Reporting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON>k<PERSON> (D028561)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I012318)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2692974", "RefComponent": "FI-RA-CP", "RefTitle": "Reconciliation Key Buffer is Not Refreshed", "RefUrl": "/notes/2692974"}, {"RefNumber": "2686122", "RefComponent": "FI-RA-PC", "RefTitle": "Migration Reconciliation Key Is Not Saved To Database Table", "RefUrl": "/notes/2686122"}, {"RefNumber": "2684586", "RefComponent": "SD-BIL-RA", "RefTitle": "Error FARR_RAI 842 when processing order RAI with IC condition", "RefUrl": "/notes/2684586"}, {"RefNumber": "2684255", "RefComponent": "SD-BIL-RA", "RefTitle": "No Fulfillment RAI for Intercompany Invoice", "RefUrl": "/notes/2684255"}, {"RefNumber": "2683430", "RefComponent": "SD-BIL-RA", "RefTitle": "Incorrect currency in IC Fulfillment RAI", "RefUrl": "/notes/2683430"}, {"RefNumber": "2682909", "RefComponent": "FI-RA", "RefTitle": "RA - Inflight Check: Data selection issue due to NUMC to CHAR conversion of POB_ID", "RefUrl": "/notes/2682909"}, {"RefNumber": "2680610", "RefComponent": "FI-RA", "RefTitle": "RA - Inflight Check: No Change Type Record with Change Mode 'C' results in C26 Error", "RefUrl": "/notes/2680610"}, {"RefNumber": "2679649", "RefComponent": "SD-BIL-RA", "RefTitle": "No Condition RAI for Intercompany Condition", "RefUrl": "/notes/2679649"}, {"RefNumber": "2679389", "RefComponent": "FI-RA-CP", "RefTitle": "System Raises A Dump Error CONVT_NO_NUMBER From Method CL_FARR_CONTRACT_MGMT->ADJUST_POB_STRUCTURE", "RefUrl": "/notes/2679389"}, {"RefNumber": "2679358", "RefComponent": "FI-RA-CP", "RefTitle": "Zero Amount Deferral Item Is Deleted After Contract Modification", "RefUrl": "/notes/2679358"}, {"RefNumber": "2677492", "RefComponent": "SD-BIL-RA", "RefTitle": "Operational load: Wrong determination of predecessor item when migrating return orders", "RefUrl": "/notes/2677492"}, {"RefNumber": "2677479", "RefComponent": "SD-BIL-RA", "RefTitle": "Operational load: LOAD and RESET of SD Revenue Recognition documents deactivated in S4CORE systems", "RefUrl": "/notes/2677479"}, {"RefNumber": "2676282", "RefComponent": "FI-RA", "RefTitle": "A Dump Occurs When You Run Revenue Transfer for Thousands Performance Obligations", "RefUrl": "/notes/2676282"}, {"RefNumber": "2675322", "RefComponent": "FI-RA-IP", "RefTitle": "Activation of Revenue Accounting for SAP S/4HANA 1809 and later", "RefUrl": "/notes/2675322"}, {"RefNumber": "2673769", "RefComponent": "FI-RA-MIG", "RefTitle": "Fix Dump When Customized Message Is Switched Off", "RefUrl": "/notes/2673769"}, {"RefNumber": "2671716", "RefComponent": "FI-RA-VAL", "RefTitle": "RA - Data Validation/ E03 When Sum Of Fulfilled Quantity Numerator/Denominator Has Minimal Difference Compared to Effective Quantity", "RefUrl": "/notes/2671716"}, {"RefNumber": "2670345", "RefComponent": "SD-BIL-RA", "RefTitle": "Credit Memo Request for Return Order - credit memo RAI items not generated", "RefUrl": "/notes/2670345"}, {"RefNumber": "2666949", "RefComponent": "FI-RA", "RefTitle": "Clear All Statistic Line Items After The First Revenue Posting", "RefUrl": "/notes/2666949"}, {"RefNumber": "2666119", "RefComponent": "FI-RA", "RefTitle": "Fully Fulfilled Flag of a Performance Obligation Should Be Marked as True If the Effective Quantity is Zero And Last Change On and Last Change By is Not Updated", "RefUrl": "/notes/2666119"}, {"RefNumber": "2662861", "RefComponent": "FI-RA-INV", "RefTitle": "Transaction price not updated with invoiced amount if invoiced amount is greater than transaction price (II)", "RefUrl": "/notes/2662861"}, {"RefNumber": "2656328", "RefComponent": "SD-BIL-RA", "RefTitle": "Condition amount of SDPI differs from invoiced amount", "RefUrl": "/notes/2656328"}, {"RefNumber": "2653855", "RefComponent": "FI-RA", "RefTitle": "RA - Data Validation / Contract combination results in E06 and E07 errors", "RefUrl": "/notes/2653855"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2682903", "RefComponent": "FI-RA", "RefTitle": "Implementing higher support packages without implementing mid support packages.", "RefUrl": "/notes/2682903 "}, {"RefNumber": "2825523", "RefComponent": "BC-UPG-MP", "RefTitle": "Unable to install  Revenue Accounting and Reporting 1.3 on S4HANA 1809", "RefUrl": "/notes/2825523 "}, {"RefNumber": "2778982", "RefComponent": "FI-RA-CP", "RefTitle": "Error \"Not Authorized to change status/review reason\" when processing rai items", "RefUrl": "/notes/2778982 "}, {"RefNumber": "2659710", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1809: Restriction Note", "RefUrl": "/notes/2659710 "}, {"RefNumber": "2625407", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1809: Release Information Note", "RefUrl": "/notes/2625407 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}