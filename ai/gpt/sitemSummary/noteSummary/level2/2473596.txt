SAP Note 2473596 addresses the changes in SAP S/4HANA Retail functionality regarding the use of Material Requirements Planning (MRP) areas. It stresses that master data maintenance and MRP options have become more straightforward in SAP S/4HANA by only offering MRP areas as the option for modeling whether to exclude storage locations from MRP or to plan them separately. The note highlights that, as a result, MRP area data must be considered instead of the storage location MRP indicator (MARD-DISKZ) for excluding storage location stock in various Retail functions and processes.

Key points from the note include:

- In SAP S/4HANA, MRP areas have become the sole method for specific MRP configurations in place of the storage location MRP indicators.
- The changes require users to adapt their Retail processes to consider MRP area data for proper functionality and accurate stock planning.
- To ensure correct data handling in Retail processes, users should implement the solutions provided in this note or the corresponding support package.

The note also directs users to consult other related SAP Notes for additional context and guidance:

- SAP Note 2268045, which explains the simplification of master data maintenance in S/4HANA and the focus on MRP area-level planning instead of storage location-level planning.
- SAP Note 2466264, which should be referred to for more details on the replacement of storage location MRP indicator considerations with MRP area data in Retail functions.
- SAP Note 2473577, which covers the hiding of fields in the article master that have been replaced by MRP area functionality.

In summary, SAP Note 2473596 informs users about the need to take into account MRP area data in Retail functionality within SAP S/4HANA and advises the implementation of this note or associated support package for resolution. It also connects to other relevant SAP Notes for more extensive information on the related changes and required actions for proper Retail process management and article maintenance within SAP S/4HANA environments.