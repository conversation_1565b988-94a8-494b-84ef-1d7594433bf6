SAP Note 1628255 addresses an issue encountered during the profit center or segment reorganization process in SAP's New General Ledger Accounting (New GL) system. The problem arises when a user enters an account assignment in a reorganization plan under "General restrictions -> Assignments" that is used both as an old account assignment and as a new account assignment within the assignments table.

For example, profit center PCA is entered as an old profit center and PCB is the new one, but if PCB is also an old one with PCM being its new assignment, the system generates the error message FAGL_R<PERSON><PERSON><PERSON>NIZATION 029 stating that the same assignment exists as both sender and recipient.

The SAP Note explains that while trying to configure this message in the message control, users might receive a contradiction with another message indicating that "Message number 029 is not allowed."

This issue had no previous solution, as the function to configure this message did not exist before. The SAP Note provides correction instructions to resolve the problem, allowing message <PERSON>GL_REORGANIZATION 029 to be set as a warning instead of an error.

Implementing the instructions from the SAP Note will enable users to bypass the issue, but it also means that they must be cautious because correctly assigned objects might be added to the reorganization plan inadvertently as a result of changing the message severity to a warning.

To conclude, SAP Note 1628255 offers a fix to allow configuration of message <PERSON>GL_REORGANIZATION 029, with a reminder to users that careful consideration is required when using the correction to prevent inadvertent assignments.