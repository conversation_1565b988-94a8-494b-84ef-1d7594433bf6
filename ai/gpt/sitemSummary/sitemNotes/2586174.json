{"Request": {"Number": "2586174", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 656, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000981462018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002586174?language=E&token=11175B04CB03BA044832C4398308230D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002586174", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002586174/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2586174"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.10.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2586174 - SAPBWNews BW 7.50 ABAP SP 12"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note deals with ABAP Support Package&#160;12 for SAP NW BW Release 7.50</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for 7.50,&#160;&#160;B<PERSON> Patches, BI, BI 7.45, SAPBINews</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBWNews for ABAP Support Package&#160;12 of BW Release 7.50<br />It provides a list of all notes describing the corrections or enhancements in Support Package&#160;12 This note will be updated when other notes are added.<br /><br /></p>\r\n<p><strong>Factors you must take into account when you import the Support Package:</strong></p>\r\n<ul>\r\n<li>To customers who power their BW with SAP HANA, SAP strongly recommends to apply the latest available SAP HANA revision.</li>\r\n<li>\r\n<p><strong>Before</strong> you import the Note please read note <a target=\"_blank\" href=\"/notes/2248091\">2248091 - Change to reimplementation handling </a></p>\r\n</li>\r\n</ul>\r\n<p><br /><strong>Issues that may occur after you import the Support Package:</strong></p>\r\n<ul>\r\n<li>\r\n<p>Please read note 2691689 &#8211; &#8222;Wrong BW query results with FEMS pushdown for specific filter conditions&#8221;</p>\r\n</li>\r\n<li>For further information on fixes please see the referenced notes</li>\r\n</ul>\r\n<p><br /><strong>Errors corrected /Important Enhancements delivered with this Support Package:</strong></p>\r\n<ul>\r\n<li>For full list please see&#160; <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-75008INSAPBW\">https://launchpad.support.sap.com/#/supportpackage/SAPK-75011INSAPBW</a></li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><br /><br /></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D031867)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002586174/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002586174/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002586174/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002586174/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002586174/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002586174/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002586174/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002586174/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002586174/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAP_BW_750_SP_12_Release_Notes.xlsx", "FileSize": "49", "MimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800000103902018&iv_version=0004&iv_guid=00109B36D5921ED8A1A83678C84AA0C1"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2549792", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "IN 299: CL_RSR_RRK0_HIERARCHY FILL_SNID_LEAF_01-02-", "RefUrl": "/notes/2549792 "}, {"RefNumber": "2634255", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for Query: warning message RS2HANA_VIEW 134", "RefUrl": "/notes/2634255 "}, {"RefNumber": "2633632", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External HANA ViewGeneration Log Contains False Warnings", "RefUrl": "/notes/2633632 "}, {"RefNumber": "2634125", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Fixed hierarchy node selection is misread as leaf filter", "RefUrl": "/notes/2634125 "}, {"RefNumber": "2625340", "RefComponent": "BW4-UI", "RefTitle": "BW4 Modeling WebUI: maintaining process variants for type ABAP: error in variant value help", "RefUrl": "/notes/2625340 "}, {"RefNumber": "2632010", "RefComponent": "BW", "RefTitle": "Master Data: HANA Interface: BW/4 1.0 SP09 16.04.2018", "RefUrl": "/notes/2632010 "}, {"RefNumber": "2633823", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Report RSWR_PERSONALIZATION_SUPPORT dumps with SAPSQL_SQLS_INVALID_CURSOR", "RefUrl": "/notes/2633823 "}, {"RefNumber": "2633457", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception aggregation in SAP HANA/BWA, intermediate results materialized.", "RefUrl": "/notes/2633457 "}, {"RefNumber": "2632498", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Memory problems with large datasets and several calculated members", "RefUrl": "/notes/2632498 "}, {"RefNumber": "2632483", "RefComponent": "BW4-ME-ADSO", "RefTitle": "ADSO: Incorrect filter for filling SID columns", "RefUrl": "/notes/2632483 "}, {"RefNumber": "2632216", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Flattening: OLAP access for empty set", "RefUrl": "/notes/2632216 "}, {"RefNumber": "2631889", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for BW Query: precision for formula with key figures with unit conversion", "RefUrl": "/notes/2631889 "}, {"RefNumber": "2631799", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for BW Query: activation error $$_REQUEST_PARA$$", "RefUrl": "/notes/2631799 "}, {"RefNumber": "2625135", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "No authorization to display historical versions of a semantically partitioned object", "RefUrl": "/notes/2625135 "}, {"RefNumber": "2606560", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Empty initial value for Hierarchy Date Variable", "RefUrl": "/notes/2606560 "}, {"RefNumber": "2564954", "RefComponent": "BW-MT-ELEM", "RefTitle": "BWMT Query - BI Client description information \"Use Short Description\" from InfoObjects not available in Query editor", "RefUrl": "/notes/2564954 "}, {"RefNumber": "2630919", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Error when invalidating an archiving request in the verification phase", "RefUrl": "/notes/2630919 "}, {"RefNumber": "2624453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Inconsistent behaviour between CKFs and Formulas", "RefUrl": "/notes/2624453 "}, {"RefNumber": "2625060", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External HANA Viewof Query - Check or Activation Failure Due to Missing Navigation Attribute Error", "RefUrl": "/notes/2625060 "}, {"RefNumber": "2629731", "RefComponent": "BW4-DM-ADSO", "RefTitle": "Manage API for (ADSO, IOBJ): Incorrect compilation of request list", "RefUrl": "/notes/2629731 "}, {"RefNumber": "2630482", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Note 2624453 - Prerequisite UDO Report", "RefUrl": "/notes/2630482 "}, {"RefNumber": "2630373", "RefComponent": "BW4-ME-ADSO", "RefTitle": "ADSO: Termination when writing to ADSO in update task", "RefUrl": "/notes/2630373 "}, {"RefNumber": "2620272", "RefComponent": "BW4-ME-ADSO", "RefTitle": "Internal: Change to metadata", "RefUrl": "/notes/2620272 "}, {"RefNumber": "2629541", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in program CL_RSR_RRK0_MULTIPROV_BUFH and form _FILL_PARTPROV-01-", "RefUrl": "/notes/2629541 "}, {"RefNumber": "2629676", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Runtime optimization for the classes CL_RSR_MDX_OPERATOR and CL_RSR_MDX_IIF", "RefUrl": "/notes/2629676 "}, {"RefNumber": "2628665", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Long runtime for GetMembers when accessing BW display hierarchy/text properties", "RefUrl": "/notes/2628665 "}, {"RefNumber": "2629124", "RefComponent": "BW-MT-IOBJ", "RefTitle": "BWMT InfoObject - When deleting the InfoObject, referenced queries are also getting deleted", "RefUrl": "/notes/2629124 "}, {"RefNumber": "2628088", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query execution is slow.", "RefUrl": "/notes/2628088 "}, {"RefNumber": "2627952", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZTABLES: Mandatory Variables without VARINPUT and Default Values", "RefUrl": "/notes/2627952 "}, {"RefNumber": "2623523", "RefComponent": "BW4-DM-DTO", "RefTitle": "CSV output conversion is not compatible with HANA NVARCHAR type", "RefUrl": "/notes/2623523 "}, {"RefNumber": "2622468", "RefComponent": "BW4-ME-ADSO", "RefTitle": "ADSO: New indicator in metadata for \"AutoRefresh\"", "RefUrl": "/notes/2622468 "}, {"RefNumber": "2625201", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Runtime optimization for metadata BAPIs", "RefUrl": "/notes/2625201 "}, {"RefNumber": "2625361", "RefComponent": "BW4-ME-ADSO", "RefTitle": "UDO for: New indicator in metadata for \"AutoRefresh\"", "RefUrl": "/notes/2625361 "}, {"RefNumber": "2624898", "RefComponent": "BW", "RefTitle": "Master Data: HANA Interface: BW/4 1.0 SP09 28.03.2018", "RefUrl": "/notes/2624898 "}, {"RefNumber": "2624204", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Error when doing an F4 search on an attribute", "RefUrl": "/notes/2624204 "}, {"RefNumber": "2624937", "RefComponent": "BW4-AE", "RefTitle": "CDS Based BW Query: Support for Zerosuppression and @Consumption.valueHelpDefinition", "RefUrl": "/notes/2624937 "}, {"RefNumber": "2624814", "RefComponent": "BW-BEX-OT-BICS-PROV", "RefTitle": "Dynamic Key Figures after Refresh or Variable Re-Submit: ITAB_DUPLICATE_KEY", "RefUrl": "/notes/2624814 "}, {"RefNumber": "2624565", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZC: Copy of a query does not copy all properties of query elements", "RefUrl": "/notes/2624565 "}, {"RefNumber": "2621870", "RefComponent": "BW", "RefTitle": "Master Data: HANA Interface: BW/4 1.0 SP09 21.03.2018", "RefUrl": "/notes/2621870 "}, {"RefNumber": "2624154", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZDELETE: deletion of queries does not delete Bookmarks", "RefUrl": "/notes/2624154 "}, {"RefNumber": "2623806", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "InA: Include OLAP MessageClass", "RefUrl": "/notes/2623806 "}, {"RefNumber": "2623524", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A Query with ambiguous Join delivers wrong data", "RefUrl": "/notes/2623524 "}, {"RefNumber": "2623429", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Change of read mode at runtime/unexpected data for time characteristics", "RefUrl": "/notes/2623429 "}, {"RefNumber": "2623584", "RefComponent": "BW4-AE-CORE", "RefTitle": "Note 2623524 - Prerequisite UDO Report", "RefUrl": "/notes/2623584 "}, {"RefNumber": "2623508", "RefComponent": "BW-WHM-DBA-HCPR", "RefTitle": "INFOCUBE field in RSZWVIEW is not updated after the query conversion using the report RSO_CONVERT_IPRO_TO_HCPR", "RefUrl": "/notes/2623508 "}, {"RefNumber": "2623480", "RefComponent": "BW4-AE-CORE", "RefTitle": "Operations in BWA/HANA and restriction to part provider or restriction outside dynamic filter", "RefUrl": "/notes/2623480 "}, {"RefNumber": "2623107", "RefComponent": "BW", "RefTitle": "Master Data: New Update: BW/4 1.0 SP09 23.03.2018", "RefUrl": "/notes/2623107 "}, {"RefNumber": "2622449", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Runtime optimization for calculated member", "RefUrl": "/notes/2622449 "}, {"RefNumber": "2622853", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSECADMIN: Search for DataStore objects (advanced) for characteristic 0TCAIPROV not possible", "RefUrl": "/notes/2622853 "}, {"RefNumber": "2622242", "RefComponent": "BW4-UI", "RefTitle": "Activation fails for process types MDACTIVAT and MDCHGLGDEL", "RefUrl": "/notes/2622242 "}, {"RefNumber": "2618237", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "SEGR: Errors during generation if no member", "RefUrl": "/notes/2618237 "}, {"RefNumber": "2621669", "RefComponent": "BW4-ME-ADSO", "RefTitle": "ADSO: Error for creation from \"classic DSO\" template", "RefUrl": "/notes/2621669 "}, {"RefNumber": "2621497", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "BEx Analyzer: Resolution of manual input values for Hierarchy Node Variables doesn't work correctly - Back-end Part", "RefUrl": "/notes/2621497 "}, {"RefNumber": "2621505", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "unposted, no data, FAGGR result not NULL", "RefUrl": "/notes/2621505 "}, {"RefNumber": "2619543", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unassigned Workarea using formula calculation in SAP HANA", "RefUrl": "/notes/2619543 "}, {"RefNumber": "2620990", "RefComponent": "BW-BEX-ET-OSD", "RefTitle": "BEx Open Dialogs: Not all Folders of Role Menu are visible.", "RefUrl": "/notes/2620990 "}, {"RefNumber": "2616172", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Subsequent correction for SAP Note 2354734", "RefUrl": "/notes/2616172 "}, {"RefNumber": "2617347", "RefComponent": "BW4-DM-DTO", "RefTitle": "Changes to column label in BW data volume statistics", "RefUrl": "/notes/2617347 "}, {"RefNumber": "2620434", "RefComponent": "BW", "RefTitle": "Process Chain Modellng using Process Type \"Activate Master Data\": Error on Activation", "RefUrl": "/notes/2620434 "}, {"RefNumber": "2620399", "RefComponent": "BW-BEX-OT-ODP", "RefTitle": "CDS based BW Query: Default value for hierarchy is not  supported", "RefUrl": "/notes/2620399 "}, {"RefNumber": "2620191", "RefComponent": "BW-BEX-ET", "RefTitle": "BEx: Connecting to a BW System undergoing In-Place Conversion to BW4HANA is not possible with Patches for SAP GUI 7.40", "RefUrl": "/notes/2620191 "}, {"RefNumber": "2619244", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "Import failing: 0bw:pruning:r<PERSON><PERSON><PERSON><PERSON><PERSON>, 0bw:pruning:rs<PERSON>appart", "RefUrl": "/notes/2619244 "}, {"RefNumber": "2618763", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "ADSO: Prevention of loading during transfer", "RefUrl": "/notes/2618763 "}, {"RefNumber": "2618755", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query returns no data after implementing version 1 of SAP Note 2611559", "RefUrl": "/notes/2618755 "}, {"RefNumber": "2610455", "RefComponent": "BW", "RefTitle": "Master Data: HANA Interface: BW/4 1.0 SP09 27.02.2018", "RefUrl": "/notes/2610455 "}, {"RefNumber": "2618673", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "ADSO: Allowing fields with prefix 'ZZ' or 'YY'", "RefUrl": "/notes/2618673 "}, {"RefNumber": "2611952", "RefComponent": "BW4-ME-ADSO", "RefTitle": "SEGR: Termination for save and activation", "RefUrl": "/notes/2611952 "}, {"RefNumber": "2618304", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions for deltas", "RefUrl": "/notes/2618304 "}, {"RefNumber": "2617491", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in Program CL_RSR_RRI2_LRECH and Form FILL_LRECH_F_BCL_CONST-03-", "RefUrl": "/notes/2617491 "}, {"RefNumber": "2617916", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for Query: current member variable not supported", "RefUrl": "/notes/2617916 "}, {"RefNumber": "2618004", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "RSDA_MASS_ACTIVATION does not work if A version is used", "RefUrl": "/notes/2618004 "}, {"RefNumber": "2618118", "RefComponent": "BW4-DM-DTO", "RefTitle": "Correction program for Hive/Spark adapter to detect and repair character values with trailing blanks", "RefUrl": "/notes/2618118 "}, {"RefNumber": "2616087", "RefComponent": "BW4-DM-RSPM", "RefTitle": "RSPM housekeeping: Runtime error due to high memory requirement", "RefUrl": "/notes/2616087 "}, {"RefNumber": "2617278", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Workbook was saved with incorrect structure member hierarchy i.e. the parent information is invalid as the structure member hierarchy is changed in the Query Designer.", "RefUrl": "/notes/2617278 "}, {"RefNumber": "2617324", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Cell Selection is not input ready", "RefUrl": "/notes/2617324 "}, {"RefNumber": "2616180", "RefComponent": "BW-PLA-IP", "RefTitle": "Auto-refresh option fails for transactional provider", "RefUrl": "/notes/2616180 "}, {"RefNumber": "2616125", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "ADSO: Incorrect message number", "RefUrl": "/notes/2616125 "}, {"RefNumber": "2615339", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Wrong Data using Composite Provider (HCPR) with a non-cumulative Part Provider", "RefUrl": "/notes/2615339 "}, {"RefNumber": "2609742", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime when using a calculated member", "RefUrl": "/notes/2609742 "}, {"RefNumber": "2615136", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Wrong data for query with non-unique hierarchy", "RefUrl": "/notes/2615136 "}, {"RefNumber": "2614781", "RefComponent": "BW-BEX-OT-BICS-PROV", "RefTitle": "BICS: user-created text of selection in Static Filter is not displayed", "RefUrl": "/notes/2614781 "}, {"RefNumber": "2614614", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Error 34.011, read mode 'Only values in InfoProvider' (MetadataProvider)", "RefUrl": "/notes/2614614 "}, {"RefNumber": "2612302", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Problems when searching in text/SAP HANA view", "RefUrl": "/notes/2612302 "}, {"RefNumber": "2613739", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query containing not mapped InfoObjects is inconsistent after conversion using the report RSO_CONVERT_IPRO_TO_HCPR", "RefUrl": "/notes/2613739 "}, {"RefNumber": "2613345", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "RSDA_SYB_PARTITION_MONITOR displays zero values", "RefUrl": "/notes/2613345 "}, {"RefNumber": "2611597", "RefComponent": "BW", "RefTitle": "Subsequent correction for SAP Note 2602055", "RefUrl": "/notes/2611597 "}, {"RefNumber": "2611559", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "FEMSn with list of key figures", "RefUrl": "/notes/2611559 "}, {"RefNumber": "2611571", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Incorrect internal key for the value 'Not assigned'", "RefUrl": "/notes/2611571 "}, {"RefNumber": "2610877", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Missing unit/unexpected number of decimal places for key figure attribute", "RefUrl": "/notes/2610877 "}, {"RefNumber": "2610850", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "C (-143) [42S22] [Sybase][ODBC Driver][Sybase IQ]Column 'ARCHREQUID_SID' not found", "RefUrl": "/notes/2610850 "}, {"RefNumber": "2610902", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "NCUM: Max. values ignored during recompilation of validity table", "RefUrl": "/notes/2610902 "}, {"RefNumber": "2610731", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Partition pruning: Incorrect time derivation", "RefUrl": "/notes/2610731 "}, {"RefNumber": "2609775", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Currency/Unit conversion on a CKF is not working without rsadmin parameter 'RZI_CONSIDER_CKF_COLLISION'", "RefUrl": "/notes/2609775 "}, {"RefNumber": "2610162", "RefComponent": "BW-MT-IOBJ", "RefTitle": "BWMT InfoObject : Runtime Properties are enabled by default", "RefUrl": "/notes/2610162 "}, {"RefNumber": "2610112", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Long runtime in method AUTHORITY_04 of class CL_RSR_RRK0_AUTHORIZATION", "RefUrl": "/notes/2610112 "}, {"RefNumber": "2609349", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Behavior of hierarchy node variables when authorization is complete", "RefUrl": "/notes/2609349 "}, {"RefNumber": "2608371", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "\"Key Figure Definition\" Steps in RSTT Traces don't return data", "RefUrl": "/notes/2608371 "}, {"RefNumber": "2508938", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: New line check service and master data planning (ABAP)", "RefUrl": "/notes/2508938 "}, {"RefNumber": "2608432", "RefComponent": "BW4-ME-ADSO", "RefTitle": "ADSO: Error in GET_RELATED", "RefUrl": "/notes/2608432 "}, {"RefNumber": "2608276", "RefComponent": "BW4-ME-ADSO", "RefTitle": "ADSO: Data activation error", "RefUrl": "/notes/2608276 "}, {"RefNumber": "2608112", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Error message EXCEPTION TYPE CX_RSD_IOBJ_NOT_EXIST", "RefUrl": "/notes/2608112 "}, {"RefNumber": "2607569", "RefComponent": "BW4-AE-CORE-NC", "RefTitle": "NCUM: Query on HCPR fails", "RefUrl": "/notes/2607569 "}, {"RefNumber": "2607507", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view: CX_RSD_INFOPROV_NOT_FOUND", "RefUrl": "/notes/2607507 "}, {"RefNumber": "2607408", "RefComponent": "BW-WHM-DBA-SPO", "RefTitle": "Error during repartitioning of semantically partitioned object", "RefUrl": "/notes/2607408 "}, {"RefNumber": "2607370", "RefComponent": "BW-BEX-OT-BICS-EQ", "RefTitle": "ODATA: Generation of OData service fails", "RefUrl": "/notes/2607370 "}, {"RefNumber": "2606845", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime for usage of Count function", "RefUrl": "/notes/2606845 "}, {"RefNumber": "2605168", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZC: incorrect copy of a query component within one InfoProvider", "RefUrl": "/notes/2605168 "}, {"RefNumber": "2604083", "RefComponent": "BW4-ME-ADSO", "RefTitle": "SEGR: Error when saving", "RefUrl": "/notes/2604083 "}, {"RefNumber": "2604810", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception aggregation in SAP HANA and variable or constant in BNR_LRECH", "RefUrl": "/notes/2604810 "}, {"RefNumber": "2604992", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Error 34.011 (MetadataProvider)", "RefUrl": "/notes/2604992 "}, {"RefNumber": "2604864", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Exception aggregation average returns incorrect non-cumulative data", "RefUrl": "/notes/2604864 "}, {"RefNumber": "2604460", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Missing texts after SAP Note 2528889 is implemented", "RefUrl": "/notes/2604460 "}, {"RefNumber": "2604515", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Execute and Explain: Exception Aggregation in SAP HANA/BWA", "RefUrl": "/notes/2604515 "}, {"RefNumber": "2582044", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Extraction from write-optimized DSO fails", "RefUrl": "/notes/2582044 "}, {"RefNumber": "2602670", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Search for text cannot find 'Not assigned' values", "RefUrl": "/notes/2602670 "}, {"RefNumber": "2602123", "RefComponent": "BW4-DM-DTO", "RefTitle": "HDFS access from SAP GUI", "RefUrl": "/notes/2602123 "}, {"RefNumber": "2602055", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Wrong data for FI-GL queries", "RefUrl": "/notes/2602055 "}, {"RefNumber": "2601115", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Conversion of InfoCube in star schema to flat cube terminates due to timeout", "RefUrl": "/notes/2601115 "}, {"RefNumber": "2600668", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The Mass Maintenance for Query Properties changes all Queries displayed", "RefUrl": "/notes/2600668 "}, {"RefNumber": "2600579", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "InfoObject 1CUDIM is not available after conversion using the report RSO_CONVERT_IPRO_TO_HCPR", "RefUrl": "/notes/2600579 "}, {"RefNumber": "2600516", "RefComponent": "BW-BEX-OT-BICS-PROV", "RefTitle": "BICS InA: Faster search for non-transient DefaultQueries", "RefUrl": "/notes/2600516 "}, {"RefNumber": "2600508", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Analysis Office: Support of Data Source For Defining Formulas in Analysis Office 2.7 (ABAP part)", "RefUrl": "/notes/2600508 "}, {"RefNumber": "2600322", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZTABLES: improvements and enhancements (2)", "RefUrl": "/notes/2600322 "}, {"RefNumber": "2600078", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Sorting on an Attribute leads to Runtime Exception", "RefUrl": "/notes/2600078 "}, {"RefNumber": "2599588", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Corrections for creation from template", "RefUrl": "/notes/2599588 "}, {"RefNumber": "2460830", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Support tool for Hadoop NLS", "RefUrl": "/notes/2460830 "}, {"RefNumber": "2581360", "RefComponent": "BW-WHM-MTD-SRCH", "RefTitle": "BW metadata repository - query elements are displayed as inconsistent", "RefUrl": "/notes/2581360 "}, {"RefNumber": "2598991", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: RSDSO_ACTIVATION 013", "RefUrl": "/notes/2598991 "}, {"RefNumber": "2593335", "RefComponent": "BW-BEX-OT-BICS-EQ", "RefTitle": "oData BICS: json response for input-ready query contains \"X\" instead of \"true\"", "RefUrl": "/notes/2593335 "}, {"RefNumber": "2598094", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Characteristic Values are sorted incorrectly", "RefUrl": "/notes/2598094 "}, {"RefNumber": "2597998", "RefComponent": "BW-BEX-OT-BICS-RSRT", "RefTitle": "Hierarchy node variable with multiple values is invalid", "RefUrl": "/notes/2597998 "}, {"RefNumber": "2597372", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The Mass Maintenance for Query Runtime Properties runs into an error message when saving", "RefUrl": "/notes/2597372 "}, {"RefNumber": "2597653", "RefComponent": "BW4-AE-DBIF", "RefTitle": "Partition pruning: Incorrect designation in message", "RefUrl": "/notes/2597653 "}, {"RefNumber": "2597182", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX execution on SAP HDB: Message E003(RSPLF)", "RefUrl": "/notes/2597182 "}, {"RefNumber": "2597215", "RefComponent": "BW-BEX-ET", "RefTitle": "BICS: Information message of inactive conditions", "RefUrl": "/notes/2597215 "}, {"RefNumber": "2597134", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Function module RSEC_GET_AUTH_FOR_USER returns unexpected result", "RefUrl": "/notes/2597134 "}, {"RefNumber": "2595384", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime when calculating a calculated member", "RefUrl": "/notes/2595384 "}, {"RefNumber": "2595780", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error Message: Setting NLS:\"\" is not allowed for this query.", "RefUrl": "/notes/2595780 "}, {"RefNumber": "2595772", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Fix for Note 2550759", "RefUrl": "/notes/2595772 "}, {"RefNumber": "2595729", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Logging of hierarchy deletions", "RefUrl": "/notes/2595729 "}, {"RefNumber": "2594240", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Further optimization of accesses to OLAP processor", "RefUrl": "/notes/2594240 "}, {"RefNumber": "2594629", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: No generation of TADIR entry in non-changeable system", "RefUrl": "/notes/2594629 "}, {"RefNumber": "2593188", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW/4HANA Conversion (IOBJ, DEST): Measure for Runtime Required for Conversion", "RefUrl": "/notes/2593188 "}, {"RefNumber": "2593529", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Correction of invalid hash definition", "RefUrl": "/notes/2593529 "}, {"RefNumber": "2594186", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "CDS view: Initial entries for display of text", "RefUrl": "/notes/2594186 "}, {"RefNumber": "2584696", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Derivation of adso flag from adso template", "RefUrl": "/notes/2584696 "}, {"RefNumber": "2593206", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Runtime error 'UNCAUGHT_EXCEPTION'", "RefUrl": "/notes/2593206 "}, {"RefNumber": "2572849", "RefComponent": "BW4-ME-ADSO", "RefTitle": "ADSO: Termination of data activation with non-cumulatives (II)", "RefUrl": "/notes/2572849 "}, {"RefNumber": "2592157", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Long runtime when calling the function module 'BICS_PROV_GET_EFFECTIVE_SELECT'", "RefUrl": "/notes/2592157 "}, {"RefNumber": "2592347", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP: Deletion of records from Error Stack via Error Stack maintenance does not work correctly.", "RefUrl": "/notes/2592347 "}, {"RefNumber": "2591012", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Runtime improvement for .[LEVEL<XX>].MEMBERS", "RefUrl": "/notes/2591012 "}, {"RefNumber": "2591305", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Cells are empty with TREXOPS 7 or higher.", "RefUrl": "/notes/2591305 "}, {"RefNumber": "2591284", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Analysis Office: search function does not return a transient query", "RefUrl": "/notes/2591284 "}, {"RefNumber": "2591052", "RefComponent": "BW4-AE-CORE", "RefTitle": "System error in program LCL_STABLE_SID and form IF_RRSI_LOCAL_SID~SID_VAL_CONVERT-1-", "RefUrl": "/notes/2591052 "}, {"RefNumber": "2590828", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: No values displayed for a hierarchy", "RefUrl": "/notes/2590828 "}, {"RefNumber": "2590584", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "HDB Compression on non optimized NCUM cube fails sometimes", "RefUrl": "/notes/2590584 "}, {"RefNumber": "2590082", "RefComponent": "BW-SYS-DB", "RefTitle": "Error CX_SY_DYNAMIC_OSQL_SEMANTICS for execution of query after conversion of cube", "RefUrl": "/notes/2590082 "}, {"RefNumber": "2587633", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "DrillDownMember and DrillDownLevel: Runtime improvement", "RefUrl": "/notes/2587633 "}, {"RefNumber": "2583256", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Use of flat cubes in Oracle systems", "RefUrl": "/notes/2583256 "}, {"RefNumber": "2588159", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Exit variables not reprocessed in planning sequences", "RefUrl": "/notes/2588159 "}, {"RefNumber": "2587846", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Dump ITAB_DUPLICATE_KEY when workbook with restrictions is refreshed", "RefUrl": "/notes/2587846 "}, {"RefNumber": "2589266", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BPC: No authorization despite sufficient data access profile", "RefUrl": "/notes/2589266 "}, {"RefNumber": "2587781", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Error for QUANTITY field without aggregation in corp. mem.", "RefUrl": "/notes/2587781 "}, {"RefNumber": "2587509", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "nearline storage and conversion in SAP HANA/BWA", "RefUrl": "/notes/2587509 "}, {"RefNumber": "2575794", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "InA: Batch processing of InA requests not working in case of complex selections", "RefUrl": "/notes/2575794 "}, {"RefNumber": "2586926", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Incorrect data when using THJ hierarchy (2)", "RefUrl": "/notes/2586926 "}, {"RefNumber": "2586790", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for Query: activation fails: Variable attribute column not found", "RefUrl": "/notes/2586790 "}, {"RefNumber": "2586430", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Values missing in query with current member variables", "RefUrl": "/notes/2586430 "}, {"RefNumber": "2586346", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Variable is not referenced to the query definition", "RefUrl": "/notes/2586346 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "750", "To": "750", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}