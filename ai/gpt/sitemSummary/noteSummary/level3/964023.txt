SAP Note 964023 addresses an issue where the selection type does not adjust as expected after the customer enhancement RSR00004 (identified also as BAdI SMOD_RSR00004) is called. As a consequence of this program error, under certain conditions, variables may not be filled correctly.

The note declares this as a program error and provides a solution that involves importing different support packages depending on the BW (Business Warehouse) system version:

1. For BW 3.0B, import Support Package 33 (BW3.0B Patch33 or SAPKW30B33), which is detailed further by Note 0914950 titled "SAPBWNews BW3.0B Support Package 33".

2. For BW 3.10 Content, import Support Package 27 (BW3.10 Patch27 or SAPKW31027), with further details found in Note 0935963, "SAPBWNews BW3.1 SP Content 27".

3. For BW 3.50, import Support Package 19 (BW3.50 Patch19 or SAPKW35019) which is described in more detail by Note 0928662, "SAPBWNews BW SP19 NetWeaver'04 Stack 19".

4. For BW 7.0, import Support Package 09 (BW7.0 Patch09 or SAPKW70009), which is detailed by Note 0914303, "SAPBWNews BW 7.0 SP09".

The note advises that if the issue is urgent, correction instructions can be used. It also states that the above-referenced notes may be available in advance of the Support Package release, in which case their short text may state "preliminary version" indicating they are not the final version.

In summary, SAP Note 964023 outlines a bug related to selection type adjustment following the use of enhancement RSR00004 and provides guidance on resolving the issue through the importation of system-specific support packages, with references to additional notes for detailed descriptions of these packages.