{"Request": {"Number": "195616", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2157, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000195616?language=E&token=490698C5475FA4EBB17D519E14C58DEF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000195616", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000195616/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "195616"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-SDB-UPG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MaxDB", "value": "BC-DB-SDB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SDB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade", "value": "BC-DB-SDB-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SDB-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "195616 - Dummy indexes on SAP DB in R3 Releases 3.0x & 3.1y"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><b>The upgrade to 4.0B Service Release 1, 4.5X, 4.6A or 4.6B terminates with the error message \"Duplicate Key\" during the insert.</b><br /><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><b>Dummy index on SAP DB</b><br /><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><b>In R/3 Systems built on Release 3.0E or smaller, so-called dummy indexes were created on the database. These represent a 1:1 mapping of the primary key of the database table. These indexes were used for internal purposes in the R/3 System up to Relase 3.0E. If they are not removed, they cause the above mentioned error.</b><br /><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>The following solutions are possible:</b><br /> <UL><LI>The Prepare for the upgrade to 4.0B Service Release 1, 4.5X, 4.6A or 4.6B is started with SAP DB Release 6.1.</LI></UL> <UL><LI>The Prepare for the upgrade to 4.0B Service Release 1, 4.5X, 4.6B or 4.6C was carried out with SAP DB Release 6.2 and the production operation of the R/3 System had not yet been stopped for the upgrade.</LI></UL> <p></p> <UL><LI>The error occurs during the upgrade.</LI></UL> <UL><LI>The error did not occur during the upgrade.<br /></LI></UL> <b>There is a specific solution for each of these individual error scenarios, which are described in the following:<br /></b><br /> <OL>1. The Prepare for the upgrade to 4.0B Service Release 1, 4.5X, 4.6B or 4.6C is started with SAP DB Release 6.1.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the Prepare in the upgrade phase PRE_IMPORT0, all dummy indexes are determined and deleted by the R3up itself. By doing so, the cause for the above problem is eliminated.<br /> <OL>2. The Prepare for the upgrade to 4.0B Service Release 1, 4.5X, 4.6B or 4.6C was carried out with SAP DB Release 6.2 and the production operation of the R/3 System had not yet been stopped for the upgrade.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Prior to the upgrade you need to download the transport request QE0K900002 from the sapserv and import the request in the system to be upgraded. (If you are not very familiar with correction and transport <p>systems, see Note 13719)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You find the transport request under: <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /general/R3server/abap/note.0114266<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Through the transport, a report ADARMIDX is implemented in the system. This report ADARMIDX must be executed before or after the PREPARE.<br />The dummy indexes are removed by executing this report.<br /> <OL>3. The error occurs during the upgrade.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the upgrade, the definition of the primary key was changed. As a consequence, the dummy index was changed into a regular index. This index contains the old primary key structure, it is unique and its name ends with the character '0'. Check the definition of the table for which the error \"Duplicate Key\" occured whether such an index exists. To do so, use the database command 'show tabledef &lt;Table_name&gt;' and 'show index &lt;Table_name&gt;'. If such an index exists, delete it by using the SQL command 'DROP INDEX &lt;index_name&gt; ON &lt;Table_name&gt;'.<br /> <OL>4. The error did not occur during the upgrade.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For this upgrade, no problems occurred with the dummy indexes. Check the dummy indexes again prior to a further upgrade. All dummy indexes are removed automatically by the database release upgrade to 7.2. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "ADABAS D"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D024975)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D024975)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000195616/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000195616/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195616/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195616/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195616/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195616/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195616/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195616/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195616/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "180456", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6B SAP DB", "RefUrl": "/notes/180456"}, {"RefNumber": "170297", "RefComponent": "BC-DB-SDB", "RefTitle": "SAP DB release upgrade to 6.2", "RefUrl": "/notes/170297"}, {"RefNumber": "159241", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.6A  SAPDB (ADABASforR/3)", "RefUrl": "/notes/159241"}, {"RefNumber": "147563", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.0B SR ADABAS for R/3", "RefUrl": "/notes/147563"}, {"RefNumber": "140431", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.5B ADABASforR/3", "RefUrl": "/notes/140431"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "170297", "RefComponent": "BC-DB-SDB", "RefTitle": "SAP DB release upgrade to 6.2", "RefUrl": "/notes/170297 "}, {"RefNumber": "180456", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6B SAP DB", "RefUrl": "/notes/180456 "}, {"RefNumber": "140431", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.5B ADABASforR/3", "RefUrl": "/notes/140431 "}, {"RefNumber": "147563", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.0B SR ADABAS for R/3", "RefUrl": "/notes/147563 "}, {"RefNumber": "159241", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions upgrade to 4.6A  SAPDB (ADABASforR/3)", "RefUrl": "/notes/159241 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "31I", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}