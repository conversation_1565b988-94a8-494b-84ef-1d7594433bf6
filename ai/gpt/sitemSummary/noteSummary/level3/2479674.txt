SAP Note 2479674 addresses issues related to the "Myself" Source System when customers migrate to either SAP BW/4HANA or SAP Datasphere, SAP BW bridge. The note explains that the "Myself" Source System will not be available in these new environments and outlines two data load scenarios that will be affected by the migration.

1. For the target SAP BW/4HANA:

- 3.x Export DataSources (with prefix 8): These data load scenarios are deprecated as of BW 7.00 and cannot be transferred using the transfer tool. They should be replaced by DTP (Data Transfer Process) loads. There's a tool (transaction RSMIGRATE) that helps with the migration of such scenarios from BW 7.30 onward.
- DataSources with custom or application-specific prefixes (e.g., Z): These need to be migrated to ODP (Operational Data Provisioning) extraction. Although it is recommended to use ODP-CDS (Core Data Services), the transfer to ODP-SAPI (Service API) is also supported. No new ODP-SAPI DataSources can be created in BW/4, but existing ones must be transferred. A report (RS_B4HANA_CREATE_OSOA_TRANSP) can be used to help manage the required transport tasks.

The note emphasizes that the "Myself" Source System will be deleted automatically when switching to "Ready for conversion mode", provided that there are no more DataSources existing for it. Failure to meet this condition will result in an error that prevents conversion. This implies that all "Myself" DataSources must be either deleted or migrated as instructed.

2. For the target Datasphere, SAP BW bridge:

- 3.x Export DataSources (with prefix 8): These scenarios are deprecated and cannot be transferred. They should be converted to DTP loads.
- Extraction from custom or other application-specific prefixed DataSources: These scenarios are not supported in SAP Datasphere, SAP BW bridge.

The note also provides references to related SAP Notes and documentation that further elaborate on related topics, such as SAP Source Systems and creating an ODP Source System, to assist users with the migration process.