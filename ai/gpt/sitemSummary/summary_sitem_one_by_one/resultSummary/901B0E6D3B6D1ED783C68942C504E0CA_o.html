<h3>SI9: Industry_DIMP_AD</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2445653">2445653 - 2445653 - S4TWL - Obsolete transactions and enhancements in GPD</a></strong></p>
<p><strong>Description:</strong></p>
<p>Grouping, Pegging, Distribution (GPD) is an enhancement to the standard functions in the project-based production environment. You can combine material requirements from different work breakdown structures (WBS) elements (across several projects or plants) into one or more grouping WBS elements for common inventory management and material requirements planning (MRP). Therefore, you can achieve greater efficiency and cost savings when procuring material.</p>
<p>The ability to group and plan material requirements across different projects, however, should not be at the expense of good financial monitoring and allocation. GPD also contains functions that allow you to allocate the individual cost types from grouped stock to the original requirements and also to the WBS elements that caused the requirements. Benefits such as optimized lot sizes and resource-related billing help you control project costs.</p>
<p>Following transactions are not available with SAP S/4HANA:</p>
<ul>
<li>CANCDIS01 - Transaction for canceling distribution results is not available in SAP S/4HANA. From the functional standpoint, distribution should never be canceled. There is no alternative transaction available for this.</li>
<li>PEG12 - Transaction is used to execute pegging for only one grouping WBS element that participated in the group to group transfer. Old pegging transaction (PEG01) is no longer available with SAP S/4HANA.</li>
<li>PEG13/PEG15 - New pegging transaction(PEG01N) doesn't consider gathering replenishment objects in to scope. And this makes the transactions PEG13 and PEG15 in SAP S/4HANA obsolete.</li>
</ul>
<p>New pegging transaction (PEG01N) in SAP S/4HANA doesn't support the above mentioned functionality with one WBS element and all the grouping WBS's participating in the group to group transfer are considered. There is no alternative transaction available to the above mentioned obsolete functionality.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The entries in the table 'PEG_TASS' indicates the usage of the functionality by the customer and this business impact note is relevant. The below described objects related to GPD are no longer available in SAP S4HANA.</p>
<p>The following Business Add-Ins (BAdIs) are no longer available in SAP S/4HANA:</p>
<ul>
<li>BADI_GPD_02 - Pegging: Distribution of Surplus</li>
<li>BADI_GPD_03 - Pegging: Distribution of Scrap</li>
<li>BADI_GPD_04 - Pegging: Distribution of Stock Differences</li>
<li>BADI_GPD_05 - Pegging: Customer-Specific Adjustment of Assignments</li>
<li>BADI_GPD_06 - Pegging: Breakpoint Determination</li>
<li>BADI_GPD_07 - Distribution of Other Costs/Payments</li>
<li>BADI_GPD_09 - Distribution: BADI Before Database Update of Distrib. Values</li>
<li>BADI_GPD_11 - Pegging: Fixing rule - Building real pegging assignments</li>
<li>BADI_GPD_12 - Pegging: Distribution of Surplus</li>
<li>BADI_GPD_14 - Pegging: Distribution of scrap</li>
<li>BADI_GPD_15 - Pegging: Distribution of lost and found</li>
<li>BADI_GPD_17 - Pegging: Customer-Specific Adjustment of Assignments</li>
<li>BADI_GPD_18 - Pegging: Read customer specific data into pegging</li>
<li>BADI_GPD_19 - Pegging: Create the tables for pegging each mat-grp segment</li>
<li>GDP_MM_EKKO_WRITE - MM_EKKO: Write GPD-specific data</li>
</ul>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Following enhancement spots can be used as an alternative to the above specified BAdIs:</p>
<table border="1" cellpadding="3" cellspacing="0">
<tbody>
<tr>
<td><strong>Obsolete BADI</strong></td>
<td><strong>Alternative Enhancement Spot</strong></td>
<td><strong>Alternative BADI Definition</strong></td>
</tr>
<tr>
<td>BADI_GPD_02</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_SURPLUS</td>
</tr>
<tr>
<td>BADI_GPD_03</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_SCRAP</td>
</tr>
<tr>
<td>BADI_GPD_04</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_LOST</td>
</tr>
<tr>
<td>BADI_GPD_05</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_ADJ_BEFORE_COMPL</td>
</tr>
<tr>
<td>BADI_GPD_06</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_BREAKPOINT</td>
</tr>
<tr>
<td>BADI_GPD_07</td>
<td>ES_BADI_GPD_DISTRIBUTION</td>
<td>BADI_GPD_DIS_ADJUST_COST_CASH</td>
</tr>
<tr>
<td>BADI_GPD_09</td>
<td>ES_BADI_GPD_DISTRIBUTION</td>
<td>BADI_GPD_DIS_BEFORE_DB_UPDATE</td>
</tr>
<tr>
<td>BADI_GPD_11</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_FIXING_RULE</td>
</tr>
<tr>
<td>BADI_GPD_12</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_SURPLUS</td>
</tr>
<tr>
<td>BADI_GPD_14</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_SCRAP</td>
</tr>
<tr>
<td>BADI_GPD_15</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_LOST</td>
</tr>
<tr>
<td>BADI_GPD_17</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_ADJ_BEFORE_COMPL</td>
</tr>
<tr>
<td>BADI_GPD_18</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>
<p>BADI_GPD_PEG_TECO_LOGIC</p>
<p>BADI_GPD_PEG_CUST_DATA</p>
</td>
</tr>
<tr>
<td>BADI_GPD_19</td>
<td>ES_BADI_GPD_PEGGING</td>
<td>BADI_GPD_PEG_MAT_GRP_SEGM</td>
</tr>
</tbody>
</table>
<p><strong>Mandatory Activities:</strong></p>
<ul>
<li>Process Design / Blueprint: Transactions not available in SAP S/4HANA CANCDIS01 - Collective Processing Distribution PEG12 - Filling of Pegging Worklist PEG13 - Pegging: Unassigned Replenishments PEG15 - Transfer Pegging Program, to be addressed before or during conversion project.</li>
<li>Custom Code Adaption: Business Add-Ins not available in SAP S/4HANA, alternative BAdI could be used, see SAP Note 2492055 BADI_GPD_02 - Pegging: Distribution of Surplus BADI_GPD_03 - Distribution of Scrap BADI_GPD_04 - Distribution of Stock Differences BADI_GPD_05 - Customer-Specific Adjustment of Assignments BADI_GPD_06 - Pegging: Breakpoint Determination BADI_GPD_07 - Pegging: Other Costs/Payments BADI_GPD_09 - Distribution: Before DB Update of Distrib. Values BADI_GPD_11 - Fixing rule - Real pegging assignments BADI_GPD_12 - Surplus BADI_GPD_14 - Scrap BADI_GPD_15 - Lost and found BADI_GPD_17 - Customer-Specific Adjustment BADI_GPD_18 - Read customer-specific data BADI_GPD_19 - Pegging tables for each mat-grp segment GDP_MM_EKKO_WRITE - Write GPD-specific data, to be addressed during conversion project.</li>
</ul>
<p><strong>Optional Activities:</strong></p>
<ul>
<li>User Training: Recommended during the conversion project.</li>
</ul>