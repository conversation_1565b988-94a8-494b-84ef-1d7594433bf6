{"Request": {"Number": "446226", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 979, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015116702017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000446226?language=E&token=53D5B48ADB210C6EFAA78FA0C27C0794"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000446226", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000446226/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "446226"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.02.2003"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "446226 - DB2/390: Additions upgrade to EBP/CRM  3.0 SR1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Additions to the guide or to the procedure of the upgrade:</p> <UL><LI>to EBP/CRM 3.0 SR1</LI></UL> <UL><LI>on DB2 UDB for OS/390 (DB2/390)</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DB2 UDB on OS/390 OS390<br />Update Upgrade Release update Release Maintenance level<br />Web Application Server 610 6.10<br />EBP/CRM 3.0 SR1<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p> ----------------------------------------------------------------------<br />This note only contains information on the upgrade to EBP/CRM 3.0 SR1 to DB2/390. ----------------------------------------------------------------------<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p> -----------------------------------------------------------------------</p> <b>CAUTION: This note is continually updated! Therefore read it again immediately before the upgrade. -----------------------------------------------------------------------</b><br /> <p></p> <b>Contents</b><br /> <OL>1. General information </OL> <OL>2. Actions before the PREPARE </OL> <OL>3. Information on individual upgrade phases</OL> <OL>4. Actions after the upgrade </OL> <p> ----------------------------------------------------------------------</p> <OL>1. General information</OL> <UL><LI>The upgrade of Basis Release 4.6x to Basis Release 6.10 is only supported for Windows 2000 and OS/390 UnixSystem Services (USS). If your central instance runs on another UNIX derivative (e.g. AIX or Solaris), you must change it to OS/390USS before starting the PREPARE.</LI></UL> <UL><LI>Only upgrade to OS/390 USS:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To perform the upgrade to OS/390 USS, you need a S/390 G5 (or compatible) processor which supports OS/390.</p> <UL><LI>Gigabit Ethernet (only Windows 2000)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please note that when you use Gigabit Ethernet for the upgrade you can expect performance improvements in the order of about 40%, compared to a conventional network log (e.g. Fast Ethernet).</p> <UL><LI>DB2 PM</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DB2 PM is not a prerequisite for the upgrade. The transaction ST04 can no longer be used from the upgrade phase KX_SWITCH. However, instead of this you can call an ICLI-based version of ST04, by starting the report RSDB3001_OLD via the transaction SE38.</p> <UL><LI>ICLI server</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Refer to note 136806.</p> <UL><LI>Remote shadow instance</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The installation of the shadow instance on a second server (remote shadow instance) is not supported for DB2/390.</p> <UL><LI>DB2 attribute DEFINE NO</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please refer to Note 423726, if all new tablespaces and indexes are to be creted with DEFINE NO during the upgrade.</p> <OL>2. Actions before the PREPARE</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before you start the PREPARE you must carry out the following actions: <UL><LI>RACF</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the upgrade, a shadow system with individual scheme is temporarily created. The name is formed from the scheme name of the original system by appending&#x00A0;&#x00A0;an 'S' (e.g.: SAPR3 -&gt; SAPR3S, SAPPRO -&gt; SAPPROS). When RACF is used, you need to</p> <UL><UL><LI>define a secondary authorization ID,</LI></UL></UL> <UL><UL><LI>create an RACF group with the same name,</LI></UL></UL> <UL><UL><LI>enter the user IDs with the database (&lt;sapsid&gt;adm or ICLIRUN) as members of this RACF group<br />for this additional scheme.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Details regarding the general proceeding are contained in the \"Planning Guide SAP Web AS 6.10\". For other security products (than RACF) similar steps need to be carried out.</p> <UL><LI>All UNIX operating systems (except for OS/390 USS)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Install and start a central instance for your system on OS/390 USS (SAP system number stays the same!). After the upgrade the central instance can be reset to the previous UNIX operating system.</p> <UL><LI>Import required PTFs</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ensure that all PTFs listed in note 81737 were imported. With report RSDB2FIX you can run an automated PTF check (see note 183311).</p> <UL><LI>Import the necessary DDIC corrections</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Make sure that the DDIC corrections required for the Basis source release (transportation and kernel patch) have been imported. You will find details on this in: </p> <UL><UL><LI>Note 162818 for Basis source release 4.5A, 4.5B</LI></UL></UL> <UL><UL><LI>Note 184399 for Basis source release 4.6A, 4.6B, 4.6C, 4.6D</LI></UL></UL> <UL><LI>Exchange R3ldctl and R3szchk</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Copy the newest versions of the executables R3ldctl and R3szchk into the directory /use/sap/&lt;SAPSID&gt;/SYS/exe/run (see note 434946).</p> <UL><LI>Indexes on SYSIBM.SYSTABLES and SYSIBM.SYSTABLESPACE</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The indexes created for the SAP System on the DB2 catalog tables SYSIBM.SYSTABLES and SYSIBM.SYSTABLESPACE should be checked and enlarged if necessary to avoid problems in the phase NEWTAB_CRE. The indexes carry the name:</p> <UL><UL><LI>SYSTABLE__0 and SYSTBLSP__0&#x00A0;&#x00A0;(source release &lt;= 3.1I)</LI></UL></UL> <UL><UL><LI>SYSTABLE~0 and SYSTBLSP~0&#x00A0;&#x00A0;&#x00A0;&#x00A0;(source release &gt;= 4.0B)</LI></UL></UL> <UL><LI>Only source Release &gt;= 4.6A</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Enlarge PRIQTY and SECQTY of the table space that contains the table GLOSSARY to 50000 and 10000 Kbytes, to avoid problems in the phase TABIM.</p> <UL><LI>Incremental conversion</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the instance profile, set parameter <BR/> &#x00A0;&#x00A0;dbs/db2/pcon = 1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;to prevent SQLCODE -750 from occurring during the initialization of incremental conversion (ICNV). To be able to activate this parameter, you need to restart the application server.</p> <UL><LI>Only source release 4.5B and central instance on AIX</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Under certain circumstances, the upgrade for source release 4. 5B and central instance on AIX (R3up runs under OS/390) abends in phase STOPR3_PROD. For this reason, use the current version of the 610/2 R3up (patch level &gt;= 9.121) for the upgrade to OS/390.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please refer to Note 504959, if you want to use the Upgrade Assistant on z/OS 1.2 (or higher).<br /></p> <OL>3. Information on individual upgrade phases</OL> <UL><LI>Phase DBPREP_CHK</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Detailed information on the DB2/390-specific checks can be found in note 400565. <br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the upgrade abends in phase STOPR3_PROD, replace the executable R3up by the current version (for details see above). </p> <OL>4. Actions after the upgrade</OL> <UL><LI>Deletion of unnecessary database objects</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For reasons of performance, during the upgrade tables that are no longer required are only renamed after QCM#PUTnnnnnn (n=0,1..,9) instead of deleting them (DROP takes 6 times longer than RENAME).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These tables and all empty stogroups, databases and tablespaces should be deleted after the upgrade. The report RSDB2CLN accomplishes this task. Refer to the description in the upgrade guide.</p> <UL><LI>Tuning newly-created tables</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See note 427392.</p> <UL><LI>Tables that are no longer required</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The tables D4NTF, D4NTT, D8NTF, D8NTT are no longer needed after the upgrade and can be deleted with database resources.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-DB2 (DB2 for z/OS)"}, {"Key": "Database System", "Value": "DB2/390"}, {"Key": "Transaction codes", "Value": "SE38"}, {"Key": "Transaction codes", "Value": "DB2"}, {"Key": "Transaction codes", "Value": "ST04"}, {"Key": "Transaction codes", "Value": "ICNV"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022631)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D000269)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000446226/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000446226/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000446226/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000446226/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000446226/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000446226/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000446226/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000446226/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000446226/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "81737", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: APAR List", "RefUrl": "/notes/81737"}, {"RefNumber": "705629", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Drop QCM#PUT tables before upgrade (re)start", "RefUrl": "/notes/705629"}, {"RefNumber": "647882", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: PTF check in PREPARE overlooks missing PTFs", "RefUrl": "/notes/647882"}, {"RefNumber": "618675", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Bad performance in upgrade (RUN_INDC_UPG, ...)", "RefUrl": "/notes/618675"}, {"RefNumber": "529267", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/529267"}, {"RefNumber": "504959", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade Assistant on z/OS", "RefUrl": "/notes/504959"}, {"RefNumber": "434946", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: R3szchk & R3ldclt for upgrade to >= 6.10", "RefUrl": "/notes/434946"}, {"RefNumber": "423726", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/423726"}, {"RefNumber": "407286", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/407286"}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717"}, {"RefNumber": "400797", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/400797"}, {"RefNumber": "400565", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/400565"}, {"RefNumber": "396690", "RefComponent": "CRM", "RefTitle": "SAP CRM 3.0: Information on Upgrade", "RefUrl": "/notes/396690"}, {"RefNumber": "184399", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: DDIC corrections (Releases 4.6A,4.6B,4.6C,4.6D)", "RefUrl": "/notes/184399"}, {"RefNumber": "183311", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Automated PTF Check", "RefUrl": "/notes/183311"}, {"RefNumber": "162818", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/162818"}, {"RefNumber": "136806", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/136806"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "184399", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: DDIC corrections (Releases 4.6A,4.6B,4.6C,4.6D)", "RefUrl": "/notes/184399 "}, {"RefNumber": "504959", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade Assistant on z/OS", "RefUrl": "/notes/504959 "}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717 "}, {"RefNumber": "618675", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Bad performance in upgrade (RUN_INDC_UPG, ...)", "RefUrl": "/notes/618675 "}, {"RefNumber": "705629", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Drop QCM#PUT tables before upgrade (re)start", "RefUrl": "/notes/705629 "}, {"RefNumber": "647882", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: PTF check in PREPARE overlooks missing PTFs", "RefUrl": "/notes/647882 "}, {"RefNumber": "434946", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: R3szchk & R3ldclt for upgrade to >= 6.10", "RefUrl": "/notes/434946 "}, {"RefNumber": "396690", "RefComponent": "CRM", "RefTitle": "SAP CRM 3.0: Information on Upgrade", "RefUrl": "/notes/396690 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "610", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "300", "To": "300", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}