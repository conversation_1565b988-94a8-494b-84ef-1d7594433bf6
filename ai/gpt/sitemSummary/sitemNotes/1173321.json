{"Request": {"Number": "1173321", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 567, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016525282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001173321?language=E&token=78EB2998828B450921A72E781AB4E6F7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001173321", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001173321/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1173321"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.06.2011"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-SRC"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Product and REACH Compliance"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Product and REACH Compliance", "value": "EHS-SRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-SRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1173321 - SAP REACH Compliance 1.1 installation on SAP ECC 600"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use transaction SAINT to install the SAP REACH Compliance ABAP add-on on Release SAP ERP Central Component ECC 600 (referred to here as SAP ECC 600).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAINT, add-on, installation, SAP REACH Compliance, SAP REACH Compliance 1.1, TDAGBCA 110_600, TechniData Basic Components,<B> </B>CD51033855, SAPK-260COINTDAGBCA<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You use transaction SAINT to install an add-on.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>This note is updated on a regular basis. Make sure you have the current version of this note before you start the installation.</B><br /><br />Contents<br />&#x00A0;&#x00A0;1. Change history<br />&#x00A0;&#x00A0;2. Prerequisites for installing the SAP REACH Compliance 1.1<br />&#x00A0;&#x00A0;3. Preparing the SAP REACH Compliance 1.1 installation<br />&#x00A0;&#x00A0;4. Executing the SAP REACH Compliance 1.1 installation<br />&#x00A0;&#x00A0;5. After you have installed the SAP REACH Compliance 1.1<br />&#x00A0;&#x00A0;6. Language support<br />&#x00A0;&#x00A0;7. Password<br />&#x00A0;&#x00A0;8. Additional information<br /></p> <b>1. Change history</b><br /> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Date</TH><TH ALIGN=LEFT> Topic</TH><TH ALIGN=LEFT> Short Description</TH></TR> <TR><TD>2008/05/30</TD><TD> All</TD><TD> Initial creation</TD></TR> <TR><TD></TD></TR> <TR><TD>2008/11/06</TD><TD> 3. Preparing the</TD><TD> Additional information</TD></TR> <TR><TD></TD><TD> installation</TD><TD> for ERP Enhancement</TD></TR> <TR><TD></TD><TD></TD><TD> Package 3</TD></TR> </TABLE> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>2008/12/04</TD><TD> 3. Preparing the</TD><TD> Additional information</TD></TR> <TR><TD></TD><TD> installation</TD><TD> for ERP Enhancement</TD></TR> <TR><TD></TD><TD> </TD><TD> Package 4</TD></TR> <TR><TD></TD></TR> <TR><TD>2010/09/22</TD><TD> 6. Language Support</TD><TD> Reference to SAP Note</TD></TR> <TR><TD></TD><TD> </TD><TD> 1437750 on supported</TD></TR> <TR><TD></TD><TD> </TD><TD> </TD><TD> Languages</TD></TR> <TR><TD></TD></TR> <TR><TD>2011/03/14</TD><TD> 3. Preparing the</TD><TD> Additional Information</TD></TR> <TR><TD></TD><TD> installation</TD><TD> for ERP Enhancement</TD></TR> <TR><TD></TD><TD> </TD><TD> Package 5</TD></TR> <TR><TD></TD></TR> <TR><TD>2011/03/29</TD><TD> 3. Preparing the</TD><TD> Attribute Change Package</TD></TR> <TR><TD></TD><TD> installation</TD></TR> <TR><TD></TD></TR> <TR><TD>2011/06/17</TD><TD> 3. Preparing the</TD><TD> ACP files on Service</TD></TR> <TR><TD></TD><TD> installation</TD><TD> Market Place</TD></TR> </TABLE> <p><br /><br /></p> <b>2. Prerequisites for installing the SAP REACH Compliance 1.1</b><br /> <UL><LI>It is not possible to uninstall SAP REACH Compliance.<br />Before you install the SAP REACH Compliance, keep in mind that you cannot uninstall ABAP add-ons. Further restrictions that concern the upgrade and maintenance of your SAP system and that occur as a result of installing an add-on are described in Release Strategy Note 1134530.</LI></UL> <UL><LI>Required release<br />You require SAP ERP Central Component 600 (SAP ECC 600).</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Support Package SP04 of REACH Compliance 1. 1 must be applied for SAP ECC 6.0 Enhancement Package 5 or higher.</p> <UL><LI>Import the latest SPAM/SAINT update.<br />Make sure that you have installed the latest SPAM/SAINT update on your system. If a newer version is available on SAP Service Marketplace, import the new SPAM/SAINT update.</LI></UL> <UL><LI>Import the latest R3trans and tp.<br />Ensure that you have imported the latest kernel version into your system. If a newer version is available on SAP Service Marketplace, import the most recent kernel.</LI></UL> <UL><LI>Obtain the following notes before you begin the installation:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>Add-ons: Conditions:</TD><TD> </TD><TD> 70228</TD></TR> <TR><TD>Overview Note:</TD><TD> </TD><TD> 1120597</TD></TR> <TR><TD>Release strategy Note:</TD><TD> </TD><TD> 1134530</TD></TR> <TR><TD>Problems with transaction SAINT:</TD><TD> </TD><TD> 822380</TD></TR> <TR><TD>Other:</TD><TD> </TD><TD> 1083802</TD></TR> <TR><TD></TD></TR> </TABLE></UL> <UL><LI>Prerequisites:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Check that your system meets the following prerequisites:</p> <UL><UL><LI>Required Components and Support Packages for ABAP stack</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Component</TH><TH ALIGN=LEFT> Release</TH><TH ALIGN=LEFT> Support Package</TH></TR> <TR><TD>SAP_BASIS</TD><TD> 700</TD><TD> SAPKB70009</TD></TR> <TR><TD>SAP_ABA</TD><TD> 700</TD><TD> SAPKA70009</TD></TR> <TR><TD></TD></TR> <TR><TD>SAP_APPL</TD><TD> 600</TD><TD> SAPKH60006</TD></TR> <TR><TD>EA-APPL</TD><TD> 600</TD><TD> SAPKGPAD06</TD></TR> <TR><TD></TD></TR> <TR><TD>If you have not yet installed these component Support Packages, you can include them when you install the SAP REACH Compliance 1.1. For more information, see Note 83458.</TD></TR></TABLE></UL></UL> <UL><UL><LI>It is mandatory to use a unicode system as SAP backend system if you enter data in languages that are not English or German.<br /><br />If you can ensure that data is only entered in English or German you can also use a non-unicode system. We do not support problems or errors that occur because of data maintained in other languages than English or German, though using a non-unicode system.</LI></UL></UL> <UL><LI>SAP REACH Compliance contains the below mentioned objects in customer namespace</LI></UL> <UL><UL><LI>Development Class: YCFP<br />Transaction SE80</LI></UL></UL> <UL><UL><LI>Authorization Objects:<br />YCFP_CPA01<br />YCFP_CPA02<br />YCFP_CPA03<br />Transaction SU21</LI></UL></UL> <UL><UL><LI>You should make sure that none of the above mentioned objects exist in the system you are going to import the package. If any object exists, then you should not import the package and contact SAP Support or the existing objects will be overwritten.</LI></UL></UL> <UL><LI>Additional Component Support Packages<br />The add-on SAP REACH Compliance 1.1 does not contain modifications. You can also perform the installation if you have already imported more Support Packages into your system than are specified in the section 'Required Components and Support Packages'.</LI></UL> <UL><LI>SAP Enterprise Extensions activation switch.<br />The SAP REACH Compliance 1.1 add-on requires the Enterprise Extension EA-PLM. If you have not yet activated them, you must do so now. For more information, see Note 1049251.</LI></UL> <UL><LI>Additional information about the installation:</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD>CD material number add-on installation</TD><TD> 51033855</TD></TR> <TR><TD></TD></TR> </TABLE></UL> <b>3. Preparing the SAP REACH Compliance 1.1 installation</b><br /> <UL><LI>Make the add-on SAP REACH Compliance 1.1 available by installation of TDAGBCA 110_600 by following procedure:</LI></UL> <UL><UL><LI>The installation CD for SAP REACH Compliance 1.1 is not automatically sent to all customers. Request the CD with material number 51033855 from your local subsidiary or download the CD from SAP Service Marketplace.</LI></UL></UL> <UL><UL><LI>Log on with either user dependent on the operating system:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt; sid&gt;adm on UNIX or Windows or &lt;SID&gt;OFR on AS/400.</p> <UL><UL><LI>Switch to the &lt;DIR_EPS_ROOT&gt; directory of your SAP system (usually /usr/sap/trans/EPS). The &lt;DIR_EPS_ROOT&gt; directory is also displayed under DIR_EPS_ROOT after you execute the RSPFPAR report.</LI></UL></UL> <UL><UL><LI>Go to the higher-level directory of &lt;DIR_EPS_ROOT&gt;.</LI></UL></UL> <UL><UL><LI>Unpack the SAR archive K-260COINTDAGBCA.SAR on the CD with either of the following statements dependent on the operating system:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;UNIX: SAPCAR -xvf /&lt;CD_DIR&gt;/TDAGBCA_110_600/INST/DATA/K-260COINTDAGBCA.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AS/400: SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/TDAGBCA_110_600/INST/DATA/K-260COINTDAGBCA.SAR'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Windows: SAPCAR -xvf &lt;CD_DRIVE&gt;:TDAGBCA_110_600\\INST\\DATA\\K-260COINTDAGBCA.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The file TDI0110006861_0000020.PAT should now be in the &lt; DIR_EPS_ROOT&gt;/in directory.</p> <UL><LI>Making the add-on SAP REACH Compliance 1.1 available on ECC 600 with any Enhancement Package installed, additionally requires following steps:</LI></UL> <UL><UL><LI>Download the Attribute Change Package (ACP) according to SAP Note 1600792.</LI></UL></UL> <UL><UL><LI>Ensure you have at least SPAM Version 25 installed, which is a prerequisite for the processing of ACPs.</LI></UL></UL> <UL><UL><LI>Then you can use SPAM or SAINT to define the required queue.</LI></UL></UL> <UL><UL><LI>See SAP Note 1143022 for details about installation on SAP ECC 6.0 Enhancement Package 4 or higher.</LI></UL></UL> <p><br /></p> <b>4. Executing the SAP REACH Compliance 1.1 installation</b><br /> <UL><LI>User to be used<br />Log on to your SAP system in client 000 as a user with SAP_ALL authorization. Do NOT use the SAP* or DDIC users.</LI></UL> <UL><LI>Displaying the add-on installation package<br />Call transaction SAINT and choose 'Start' and 'Load'.<br />After the list of uploaded packages is displayed, you can return to the initial screen of transaction SAINT by choosing F3 or 'Back'.</LI></UL> <UL><LI>Starting the installation<br />Call transaction SAINT and choose 'Start'. Select the add-on TDAGBCA and choose 'Continue'. <B>Do not install the add-on together with its support packages in the same queue.</B> If there are support packages required, install each one separately after the predecessor was installed. If all of the necessary conditions for importing the add-on have been fulfilled, the system will now display the relevant queue.<br />To start the installation process, choose 'Continue'. For more information, call transaction SAINT and choose 'Info' on the application toolbar.<br />The system prompts you to enter a password. This password is provided below.<br /></LI></UL> <b>5. After you have installed the SAP REACH Compliance 1.1</b><br /> <UL><LI>Importing Support Packages after the installation<br />You can import Support Packages of the other components that SAP REACH Compliance 1.1 does not modify without CRTs.</LI></UL> <UL><LI>Delivery Customizing</LI></UL> <UL><UL><LI>Delivery Customizing is imported into client 000 and may need to be copied to other clients. For more information, see Note 337623.</LI></UL></UL> <UL><UL><LI>To transfer the classes and characteristics from client 000 to your productive client use the client copier SCC1 with component piece list /TDAG/RCS_CLASSES.<br /></LI></UL></UL> <b>6. Language support</b><br /> <UL><LI>German and English are supported by SAP REACH Compliance 1.1.</LI></UL> <UL><LI>Refer to SAP Note 1437750 for the support of additional languages.</LI></UL> <UL><LI>If you import a new standard language into your system after installing the SAP REACH Compliance, you must manually ensure that the corresponding language-dependent part of the add-on is filled with one of the default languages.<br /></LI></UL> <b>7. Password</b><br /> <UL><LI>During the installation, the system prompts you to enter a password. This password is: 3CAB2D1998<br /></LI></UL> <b>8. Additional information</b><br /> <p><br />After having installed the ABAP stack you install the Java stack.</p> <UL><LI>You need the following installation archives:</LI></UL> <UL><UL><LI>TDAGBCEHS00 Patchlevel 0</LI></UL></UL> <UL><UL><LI>TDAGCPCORE03 Patchlevel 0</LI></UL></UL> <UL><UL><LI>TDAGCPSCOLL03 Patchlevel 9</LI></UL></UL> <UL><UL><LI>TDAGRCSINV00 Patchlevel 0</LI></UL></UL> <UL><UL><LI>TDAGRCSINVUI00 Patchlevel 0</LI></UL></UL> <UL><UL><LI>TDAGRCSPC00 Patchlevel 0</LI></UL></UL> <UL><LI>Required Components and Support Packages for Java stack</LI></UL> <UL><UL><LI>SAP Netweaver 7.0 Application Server Java SP Stack 12</LI></UL></UL> <UL><UL><LI>SAP Netweaver 7.0 Enterprise Portal SP Stack 12</LI></UL></UL> <p><br />If you want to setup a double stack system (including ABAP and Java), the higher support package is required (in this case SAP NetWeaver 700 SP12).</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TD></TD></TR> </TABLE></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D054665)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D054818)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173321/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173321/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173321/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173321/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173321/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173321/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173321/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173321/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173321/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1613731", "RefComponent": "XX-SER-REL", "RefTitle": "Browser Compatibility of Add-On REACH Compliance", "RefUrl": "/notes/1613731"}, {"RefNumber": "1600792", "RefComponent": "EHS-SRC", "RefTitle": "Installation/Switch-Upgrade with ERP 6.0 Enhancement Package", "RefUrl": "/notes/1600792"}, {"RefNumber": "1586828", "RefComponent": "EHS-SRC-SCC", "RefTitle": "Supporting the eSDS process", "RefUrl": "/notes/1586828"}, {"RefNumber": "1437750", "RefComponent": "EHS-SRC", "RefTitle": "SRC 1.1; SPRC 2.0: Supported Languages", "RefUrl": "/notes/1437750"}, {"RefNumber": "1404115", "RefComponent": "EHS-SRC", "RefTitle": "Using the SRC/SPRC BOMBOS or EH&S standard BOMBOS", "RefUrl": "/notes/1404115"}, {"RefNumber": "1300577", "RefComponent": "EHS-SRC", "RefTitle": "Release information note for SAP REACH Compliance 1.1 SP03", "RefUrl": "/notes/1300577"}, {"RefNumber": "1271733", "RefComponent": "EHS-SRC", "RefTitle": "Release information note for SAP REACH Compliance 1.1 SP02", "RefUrl": "/notes/1271733"}, {"RefNumber": "1240476", "RefComponent": "EHS-SRC", "RefTitle": "SAP REACH Compliance 1.1: Add-On Support Packages", "RefUrl": "/notes/1240476"}, {"RefNumber": "1232092", "RefComponent": "EHS-SRC", "RefTitle": "Specific information for SP01 of SAP REACH Compliance 1.1", "RefUrl": "/notes/1232092"}, {"RefNumber": "1231665", "RefComponent": "EHS-SRC", "RefTitle": "Additional information for general availability", "RefUrl": "/notes/1231665"}, {"RefNumber": "1120597", "RefComponent": "EHS-SRC", "RefTitle": "SAP REACH Compliance: Additional Information", "RefUrl": "/notes/1120597"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1795553", "RefComponent": "EHS-SRC", "RefTitle": "Change of the application component", "RefUrl": "/notes/1795553 "}, {"RefNumber": "1600792", "RefComponent": "EHS-SRC", "RefTitle": "Installation/Switch-Upgrade with ERP 6.0 Enhancement Package", "RefUrl": "/notes/1600792 "}, {"RefNumber": "1613731", "RefComponent": "XX-SER-REL", "RefTitle": "Browser Compatibility of Add-On REACH Compliance", "RefUrl": "/notes/1613731 "}, {"RefNumber": "1586828", "RefComponent": "EHS-SRC-SCC", "RefTitle": "Supporting the eSDS process", "RefUrl": "/notes/1586828 "}, {"RefNumber": "1231665", "RefComponent": "EHS-SRC", "RefTitle": "Additional information for general availability", "RefUrl": "/notes/1231665 "}, {"RefNumber": "1120597", "RefComponent": "EHS-SRC", "RefTitle": "SAP REACH Compliance: Additional Information", "RefUrl": "/notes/1120597 "}, {"RefNumber": "1437750", "RefComponent": "EHS-SRC", "RefTitle": "SRC 1.1; SPRC 2.0: Supported Languages", "RefUrl": "/notes/1437750 "}, {"RefNumber": "1271733", "RefComponent": "EHS-SRC", "RefTitle": "Release information note for SAP REACH Compliance 1.1 SP02", "RefUrl": "/notes/1271733 "}, {"RefNumber": "1240476", "RefComponent": "EHS-SRC", "RefTitle": "SAP REACH Compliance 1.1: Add-On Support Packages", "RefUrl": "/notes/1240476 "}, {"RefNumber": "1300577", "RefComponent": "EHS-SRC", "RefTitle": "Release information note for SAP REACH Compliance 1.1 SP03", "RefUrl": "/notes/1300577 "}, {"RefNumber": "1404115", "RefComponent": "EHS-SRC", "RefTitle": "Using the SRC/SPRC BOMBOS or EH&S standard BOMBOS", "RefUrl": "/notes/1404115 "}, {"RefNumber": "1232092", "RefComponent": "EHS-SRC", "RefTitle": "Specific information for SP01 of SAP REACH Compliance 1.1", "RefUrl": "/notes/1232092 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "TDAGBCA", "From": "110_600", "To": "110_600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}