SAP Note 1428709 provides instructions on how to create an ODBC (Open Database Connectivity) trace for analyzing the communication between SAP MaxDB and the SAP Content Server when using ODBC driver version 7.7 or higher. The note is applicable if you are encountering issues with this communication and require a detailed log (trace) to diagnose problems.

Key points from the note:

1. To get an overview of all trace options, you can refer to SAP Note 822239.
2. It's recommended to use the most current version of the ODBC driver, for which you can refer to SAP Note 1571193 for information about upgrading (also see SAP Note 698915).
3. The Content Server uses ODBC driver version 7.8 or higher and always uses version 7.9 or higher as of SAP Content Server version 6.50.
4. The command line tool `odbc_cons` is used to manage ODBC trace files in a user-specific manner.
5. Detailed steps on how to perform the trace using `odbc_cons` are provided. These include:
    - Determining the operating system user running the Web server.
    - Checking and setting the directory path for the trace log files.
    - Activating the ODBC trace with standard options (`trace sql on` and `trace api on`).
    - Deactivating the trace (`trace off`) after reproducing the error.

The note provides specific commands for both UNIX/Linux and Microsoft Windows environments, indicating differences in file paths and user privileges that need to be considered.

This SAP Note is essential for system administrators or support personnel who need to capture and analyze ODBC communications in troubleshooting scenarios related to the integration between SAP MaxDB and the SAP Content Server.