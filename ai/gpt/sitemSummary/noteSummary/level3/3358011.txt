SAP Note 3358011 addresses an issue encountered during data migration into SAP S/4HANA versions ranging from 1909 to 2023 while using the SAP S/4HANA Migration Cockpit for the migration object "FI - G/L account balance and open/line item". Users experience an error message KE109 stating "Value 'xxxx' is not allowed for characteristic 'WBS Element'", even though the WBS element value is correctly maintained in the master data and manual postings with this WBS element are successful.

The root cause identified is an incorrect data type for the CO-PA related field WBS element, which leads to truncation of the source value (e.g., a value '12345' being truncated into '12').

Solutions are provided in the form of Transport-based Correction Instructions (TCIs) specific to different SAP S/4HANA releases and their corresponding Support Package (SP) levels:

- For 1909: Implement TCI Note 3364987 (for SP00 to SP08).
- For 2020: Implement TCI Note 3364988 (for SP00 to SP06).
- For 2021: Implement TCI Note 3364959 (for SP00 to SP04).
- For 2022: Implement TCI Note 3365022 (for SP00 to SP02).
- For 2023: Implement TCI Note 3398244 (for SP00); The issue is resolved in SP01 and above and should not occur.

Users are directed to implement the specific TCI Note for their SAP S/4HANA version and Support Package level to resolve the issue.