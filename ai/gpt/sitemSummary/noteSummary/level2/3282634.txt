Summary of SAP Note 3282634 - SAF-T Poland - PLVAT 100: Release note PLVAT 100 SP10:

The note announces the release of Support Package 10 (SP10) for the "PLVAT 100" software, which is part of the SAP ERP add-on for Polish SAF-T regulatory requirements and the private cloud edition. The key points of the note are as follows:

1. **New Features in SP10**: 
   - Numerous enhancements have been added to the application, KR Module, WB Module, MAG Module, VAT Module, and FA Module, such as:
     - New XML folder configuration and transaction file options.
     - Application mode switching without document exit.
     - Download capabilities for XML files larger than 2GB.
     - Various other configurations related to Company Codes, settlement accounts, journal items numbering, transaction dates, reporting levels, and VAT data monitoring.

2. **Minor Extensions and Bug Fixes**: 
   - The SP10 includes various minor enhancements and fixes. Detailed information can be found in the newest versions of the Configuration Guide (1.19) and Application Help (1.16) available on the SAP Help Portal.

3. **Prerequisites**: 
   - The system must have PLVAT 100 SP09 installed before upgrading to SP10.

4. **Solution**: 
   - The software package and documentation are exclusively available to customers who have licensed the "SAP ERP, add-on for Polish SAF-T regulatory requirements 1.0".
   - Guidance is provided on how to download SP10 from the SAP Service Marketplace, upgrade the software using the SPAM transaction, and configure the software according to updated documentation.
   - Special instructions and attachments are provided for customers upgrading from SP09 to SP10.

5. **Additional Information**: 
   - Updates on the import file structures and customer support through the component XX-PROJ-CDP-571 are included.

The note serves as an essential resource for organizations that utilize SAP ERP and need to comply with Polish SAF-T requirements, describing the steps to access and implement the latest support package and its enhanced functionalities.