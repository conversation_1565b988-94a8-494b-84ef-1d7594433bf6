{"Request": {"Number": "2216559", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 359, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013111392017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002216559?language=E&token=B9C09D25D7EA35B8C04B3C75F494027B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002216559", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002216559/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2216559"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.08.2017"}, "SAPComponentKey": {"_label": "Component", "value": "CA-FLE-MAT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Field Lenght Extension for Material"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cross Application Field Lenght Extension", "value": "CA-FLE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Field Lenght Extension for Material", "value": "CA-FLE-MAT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE-MAT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2216559 - S4TC SAP_APPL Precheck for SAP S/4HANA System Conversion of ALE Change Pointers and MFLE"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Before all XPRAS are started to change the material number field length from 18 to 40 digits, some checks have to be performed.<br />One of these check&#160;establishes whether there are&#160; anyopen ALEs. This processe&#160;must be&#160;completed manually before the system is migrated.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S4TC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Some ALE message types&#160;handle a material number. In most cases, the material number is in a concatenanted field.&#160;Transforming these millions of entries would extend the downtime dramatically.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>How to process the open ALE change pointers&#160;depends on the&#160;message type. Therefore, you should get in contact with a consultant.</p>\r\n<p>Using&#160;report <PERSON><PERSON><PERSON><PERSON>, you can delete all obsolete change pointers. Using&#160;report <PERSON><PERSON><PERSON><PERSON>, you can process valid ALE change pointers.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "LO-MD-M<PERSON> (Material Master)"}, {"Key": "Responsible                                                                                         ", "Value": "D039493"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002216559/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002216559/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002216559/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002216559/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002216559/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002216559/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002216559/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002216559/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002216559/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2211435", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Precheck for ALE Change Pointers and MFLE", "RefUrl": "/notes/2211435"}, {"RefNumber": "2185960", "RefComponent": "LO-AB", "RefTitle": "S4TC SAP_APPL Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2185960"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2461175", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Class CLS4H_CHECKS_BC_BMT_OM_ALE does not exist. Implement the newest version of the SAP_APPL master note 2185960 error during S/4HANA precheck", "RefUrl": "/notes/2461175 "}, {"RefNumber": "2215871", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - Data Migration", "RefUrl": "/notes/2215871 "}, {"RefNumber": "2185960", "RefComponent": "LO-AB", "RefTitle": "S4TC SAP_APPL Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2185960 "}, {"RefNumber": "2211435", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Precheck for ALE Change Pointers and MFLE", "RefUrl": "/notes/2211435 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "619", "To": "619", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60029", "URL": "/supportpackage/SAPKH60029"}, {"SoftwareComponentVersion": "SAP_APPL 602", "SupportPackage": "SAPKH60218", "URL": "/supportpackage/SAPKH60218"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60317", "URL": "/supportpackage/SAPKH60317"}, {"SoftwareComponentVersion": "SAP_APPL 604", "SupportPackage": "SAPKH60418", "URL": "/supportpackage/SAPKH60418"}, {"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60515", "URL": "/supportpackage/SAPKH60515"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60617", "URL": "/supportpackage/SAPKH60617"}, {"SoftwareComponentVersion": "SAP_APPL 616", "SupportPackage": "SAPKH61610", "URL": "/supportpackage/SAPKH61610"}, {"SoftwareComponentVersion": "SAP_APPL 617", "SupportPackage": "SAPKH61712", "URL": "/supportpackage/SAPKH61712"}, {"SoftwareComponentVersion": "SAP_APPL 617", "SupportPackage": "SAPKH61714", "URL": "/supportpackage/SAPKH61714"}, {"SoftwareComponentVersion": "SAP_APPL 617", "SupportPackage": "SAPKH61711", "URL": "/supportpackage/SAPKH61711"}, {"SoftwareComponentVersion": "SAP_APPL 618", "SupportPackage": "SAPK-61803INSAPAPPL", "URL": "/supportpackage/SAPK-61803INSAPAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0002216559/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "618", "Number": "2216559 ", "URL": "/notes/2216559 ", "Title": "S4TC SAP_APPL Precheck for SAP S/4HANA System Conversion of ALE Change Pointers and MFLE", "Component": "CA-FLE-MAT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}