{"Request": {"Number": "104838", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 282, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014567362017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000104838?language=E&token=4B0FE08AF438F9C38FFBB6DABE77C48B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000104838", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000104838/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "104838"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.08.2000"}, "SAPComponentKey": {"_label": "Component", "value": "PP-IS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Information System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Production Planning and Control", "value": "PP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Information System", "value": "PP-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PP-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "104838 - Collective note SFIS, Release 3.1I"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Different errors occur in the Shop Floor Information System.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by a program error. No modifications were made in the programs of the Shop Floor Information System or no modification notes were implemented.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note solves all known problems of the Shop Floor Information System, except for those listed further down. You only have to import the transports from directory /general/R3server/abap/note.0104838 of a Sapserv computer and execute a few manual corrections. If possible, carry it out in a test system. Do not import the transport into the production system while it is in operation.<br />All cross-application errors of the LIS are not corrected with it.<br />Execute program RMCFNE22 once in the background before you carry out the operation analysis.<br />The following notes are not contained in the collective note and must be maintained manually, if necessary:<br />Area repetitive manufacturing:<br />92453 No or incorrect LIS data for orders<br />101381 For the very rare case that for a user-defined information structure in the area repetitive manufacturing goods receipt and goods issue dates are combined, existing units might be deleted during goods issue backflush (Transaction MF4U).<br />Miscellaneous:<br />78940 Various errors can occur when you copy standard information structures S021, S022, S023 and S024.<br />79258 In the work center analysis, the \"Responsible planner group\" exists as a characteristic. However, you would expect the \"Person responsible for work center\" or the \"Planer group of the capacity\". This note is only partially contained, RMCSTEXT and RMCSS024 must be adjusted.<br /><br />Caution: Note 119466 \"EMS: Incorrect lines in planned/actual comparison\" must have been implemented.<br /><br />The following objects are contained in the transport:<br />Function modules:<br />MCF_STATISTICS_LIS, MCF_STATISTICS_LIS_SMRES, MCF_VERSION_UPD_V2<br />Programs:<br />FMCF1F01, FMCF2F01, FMCF3F01, FMCF4F01, LMCF2F01, LMCF2TOP, RMCF01F0, RMCF02F0, RMCF02TP, RMCF04F0, RMCF05F0, RMCF08F0, RMCFNEUA, RMCFNEUC, RMCFNEUE, RMCSS021, RMCSS022, RMCSS023, RMCSS024<br />Program texts:<br />RMCFNEUA<br />Structures and tables:<br />MCFKENNZ, MCKALK, MCKALKW, S022<br />Function groups:<br />MCF4<br />Table contents:<br />T804A, TMC2, TMC23, TMC2D, TMC2F, TMC2K, TMC73, TMCEX</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-PP (Production Planning and Control)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027230)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000104838/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104838/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104838/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104838/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104838/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104838/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104838/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104838/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000104838/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96849", "RefComponent": "PP-IS-DC", "RefTitle": "Completing technically --> empty entries in S026", "RefUrl": "/notes/96849"}, {"RefNumber": "93075", "RefComponent": "PP-IS-DC", "RefTitle": "Deadlocks during update in PPIS", "RefUrl": "/notes/93075"}, {"RefNumber": "89593", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion indicator and 'Partially released' status", "RefUrl": "/notes/89593"}, {"RefNumber": "200653", "RefComponent": "PP-IS-REP", "RefTitle": "Repeated display of document/master data", "RefUrl": "/notes/200653"}, {"RefNumber": "200239", "RefComponent": "PP-IS-REP", "RefTitle": "In operation analysis not enough data is displayed", "RefUrl": "/notes/200239"}, {"RefNumber": "200154", "RefComponent": "PP-IS-DC", "RefTitle": "Incorrect costs with no planned costs intended", "RefUrl": "/notes/200154"}, {"RefNumber": "194347", "RefComponent": "PP-IS-DC", "RefTitle": "Deadlocks in table MCAFPOV", "RefUrl": "/notes/194347"}, {"RefNumber": "188966", "RefComponent": "PP-IS-DC", "RefTitle": "Statistical data setup for event PF", "RefUrl": "/notes/188966"}, {"RefNumber": "188202", "RefComponent": "PP-IS-DC", "RefTitle": "Date initial in S023", "RefUrl": "/notes/188202"}, {"RefNumber": "185039", "RefComponent": "PP-IS-DC", "RefTitle": "Actual scrap quantity order item is incorrect", "RefUrl": "/notes/185039"}, {"RefNumber": "182585", "RefComponent": "PP-IS-DC", "RefTitle": "Capacity split causes update errors", "RefUrl": "/notes/182585"}, {"RefNumber": "181537", "RefComponent": "PP-IS-DC", "RefTitle": "PERFORM ON COMMIT: NESTED_PERFORM_ON_COMMIT", "RefUrl": "/notes/181537"}, {"RefNumber": "180461", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date of last confirmation not filled", "RefUrl": "/notes/180461"}, {"RefNumber": "172674", "RefComponent": "PP-IS-DC", "RefTitle": "Secondary resources not complete", "RefUrl": "/notes/172674"}, {"RefNumber": "170576", "RefComponent": "PP-IS-REP", "RefTitle": "Selection log in standard analysis repetitive mfg", "RefUrl": "/notes/170576"}, {"RefNumber": "170083", "RefComponent": "PP-IS-DC", "RefTitle": "By-products/co-products S026/S027", "RefUrl": "/notes/170083"}, {"RefNumber": "166200", "RefComponent": "PP-IS", "RefTitle": "Planned quantities in release versions incorrect", "RefUrl": "/notes/166200"}, {"RefNumber": "165034", "RefComponent": "PP-IS-DC", "RefTitle": "Statistical setup: MESSAGE_TYPE_X", "RefUrl": "/notes/165034"}, {"RefNumber": "162202", "RefComponent": "PP-IS-DC", "RefTitle": "Actual execution times are too long", "RefUrl": "/notes/162202"}, {"RefNumber": "160872", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date in manufacture of co-products", "RefUrl": "/notes/160872"}, {"RefNumber": "160322", "RefComponent": "PP-IS-DC", "RefTitle": "Queue time in hours", "RefUrl": "/notes/160322"}, {"RefNumber": "159124", "RefComponent": "PP-IS-DC", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC in MCF_VERSION_UPD_V1", "RefUrl": "/notes/159124"}, {"RefNumber": "150965", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion flag and S026/MCCOMP", "RefUrl": "/notes/150965"}, {"RefNumber": "145294", "RefComponent": "PP-IS-DC", "RefTitle": "Text items in the material consumption", "RefUrl": "/notes/145294"}, {"RefNumber": "145120", "RefComponent": "PP-IS-REP", "RefTitle": "Text display in flexible analysis", "RefUrl": "/notes/145120"}, {"RefNumber": "138860", "RefComponent": "PP-IS-DC", "RefTitle": "MCAFPO: Event PB not for Partial confirmation", "RefUrl": "/notes/138860"}, {"RefNumber": "138319", "RefComponent": "PP-IS-DC", "RefTitle": "Setup repetitive manufacturing by-products", "RefUrl": "/notes/138319"}, {"RefNumber": "137340", "RefComponent": "PP-IS-DC", "RefTitle": "Setup of statistical data f. unplanned goods issues", "RefUrl": "/notes/137340"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "136057", "RefComponent": "PP-IS-REP", "RefTitle": "MCP9 - no time series for production line possible", "RefUrl": "/notes/136057"}, {"RefNumber": "135045", "RefComponent": "PP-IS-REP", "RefTitle": "Field overflow in production order/operation analysis", "RefUrl": "/notes/135045"}, {"RefNumber": "132614", "RefComponent": "PP-IS-REP", "RefTitle": "Resource and process order in standard analysis", "RefUrl": "/notes/132614"}, {"RefNumber": "128906", "RefComponent": "PP-IS-REP", "RefTitle": "Planned/actual comparison in order/operation analys", "RefUrl": "/notes/128906"}, {"RefNumber": "128705", "RefComponent": "PP-IS-REP", "RefTitle": "MCP1: Key figures can no longer be selected", "RefUrl": "/notes/128705"}, {"RefNumber": "128535", "RefComponent": "PP-IS-DC", "RefTitle": "CM25: DBIF_RTAB_NO_MEMORY", "RefUrl": "/notes/128535"}, {"RefNumber": "128290", "RefComponent": "PP-IS-DC", "RefTitle": "Capacity requirements is empty", "RefUrl": "/notes/128290"}, {"RefNumber": "128020", "RefComponent": "PP-IS-DC", "RefTitle": "Setup is too slow", "RefUrl": "/notes/128020"}, {"RefNumber": "126710", "RefComponent": "PP-IS-DC", "RefTitle": "Update problems in collective orders", "RefUrl": "/notes/126710"}, {"RefNumber": "125874", "RefComponent": "PP-IS", "RefTitle": "RMCFNEUA - 'SQL error 1562'", "RefUrl": "/notes/125874"}, {"RefNumber": "121674", "RefComponent": "PP-IS-EWS", "RefTitle": "EWS work center analysis: Capacity key figures", "RefUrl": "/notes/121674"}, {"RefNumber": "121052", "RefComponent": "PP-IS-DC", "RefTitle": "Incomplete update if S026/S027 is inactive", "RefUrl": "/notes/121052"}, {"RefNumber": "119927", "RefComponent": "PP-IS-DC", "RefTitle": "Update problems with different currencies (1)", "RefUrl": "/notes/119927"}, {"RefNumber": "117190", "RefComponent": "PP-IS-DC", "RefTitle": "Too large key fig.f.costs during setup of stat.data", "RefUrl": "/notes/117190"}, {"RefNumber": "113952", "RefComponent": "PP-IS-DC", "RefTitle": "Incomplete update", "RefUrl": "/notes/113952"}, {"RefNumber": "111967", "RefComponent": "PP-IS-DC", "RefTitle": "Update date information structure S023 and S024", "RefUrl": "/notes/111967"}, {"RefNumber": "111644", "RefComponent": "PP-IS-DC", "RefTitle": "Problems: Setup rep. manufacturing, document log", "RefUrl": "/notes/111644"}, {"RefNumber": "111299", "RefComponent": "PP-IS", "RefTitle": "Flexible analysis via Operations: too large values", "RefUrl": "/notes/111299"}, {"RefNumber": "110661", "RefComponent": "PP-IS-DC", "RefTitle": "Too large cap.key fgs in user-defined info structrs", "RefUrl": "/notes/110661"}, {"RefNumber": "110275", "RefComponent": "PP-IS-DC", "RefTitle": "Units in S021 and S023 / update log", "RefUrl": "/notes/110275"}, {"RefNumber": "109906", "RefComponent": "PP-IS-DC", "RefTitle": "Order confirmation: BUDAT, GRUND, PERNR", "RefUrl": "/notes/109906"}, {"RefNumber": "107973", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion of partially delivered orders", "RefUrl": "/notes/107973"}, {"RefNumber": "107909", "RefComponent": "PP-IS-DC", "RefTitle": "Automatic goods movements by-/co-products", "RefUrl": "/notes/107909"}, {"RefNumber": "107122", "RefComponent": "PP-IS-DC", "RefTitle": "Problems with automatic goods movements", "RefUrl": "/notes/107122"}, {"RefNumber": "104523", "RefComponent": "PP-IS-DC", "RefTitle": "No costs during change of work center", "RefUrl": "/notes/104523"}, {"RefNumber": "104131", "RefComponent": "PP-IS-DC", "RefTitle": "No update rules for information structure S029", "RefUrl": "/notes/104131"}, {"RefNumber": "101694", "RefComponent": "PP-IS-DC", "RefTitle": "Double components are updated incorrectly", "RefUrl": "/notes/101694"}, {"RefNumber": "100515", "RefComponent": "PP-IS-DC", "RefTitle": "Setup orders with co-products and by-products", "RefUrl": "/notes/100515"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "137340", "RefComponent": "PP-IS-DC", "RefTitle": "Setup of statistical data f. unplanned goods issues", "RefUrl": "/notes/137340 "}, {"RefNumber": "119927", "RefComponent": "PP-IS-DC", "RefTitle": "Update problems with different currencies (1)", "RefUrl": "/notes/119927 "}, {"RefNumber": "138860", "RefComponent": "PP-IS-DC", "RefTitle": "MCAFPO: Event PB not for Partial confirmation", "RefUrl": "/notes/138860 "}, {"RefNumber": "104131", "RefComponent": "PP-IS-DC", "RefTitle": "No update rules for information structure S029", "RefUrl": "/notes/104131 "}, {"RefNumber": "194347", "RefComponent": "PP-IS-DC", "RefTitle": "Deadlocks in table MCAFPOV", "RefUrl": "/notes/194347 "}, {"RefNumber": "89593", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion indicator and 'Partially released' status", "RefUrl": "/notes/89593 "}, {"RefNumber": "188966", "RefComponent": "PP-IS-DC", "RefTitle": "Statistical data setup for event PF", "RefUrl": "/notes/188966 "}, {"RefNumber": "188202", "RefComponent": "PP-IS-DC", "RefTitle": "Date initial in S023", "RefUrl": "/notes/188202 "}, {"RefNumber": "135045", "RefComponent": "PP-IS-REP", "RefTitle": "Field overflow in production order/operation analysis", "RefUrl": "/notes/135045 "}, {"RefNumber": "128290", "RefComponent": "PP-IS-DC", "RefTitle": "Capacity requirements is empty", "RefUrl": "/notes/128290 "}, {"RefNumber": "200653", "RefComponent": "PP-IS-REP", "RefTitle": "Repeated display of document/master data", "RefUrl": "/notes/200653 "}, {"RefNumber": "162202", "RefComponent": "PP-IS-DC", "RefTitle": "Actual execution times are too long", "RefUrl": "/notes/162202 "}, {"RefNumber": "111967", "RefComponent": "PP-IS-DC", "RefTitle": "Update date information structure S023 and S024", "RefUrl": "/notes/111967 "}, {"RefNumber": "170083", "RefComponent": "PP-IS-DC", "RefTitle": "By-products/co-products S026/S027", "RefUrl": "/notes/170083 "}, {"RefNumber": "96849", "RefComponent": "PP-IS-DC", "RefTitle": "Completing technically --> empty entries in S026", "RefUrl": "/notes/96849 "}, {"RefNumber": "166200", "RefComponent": "PP-IS", "RefTitle": "Planned quantities in release versions incorrect", "RefUrl": "/notes/166200 "}, {"RefNumber": "200154", "RefComponent": "PP-IS-DC", "RefTitle": "Incorrect costs with no planned costs intended", "RefUrl": "/notes/200154 "}, {"RefNumber": "128705", "RefComponent": "PP-IS-REP", "RefTitle": "MCP1: Key figures can no longer be selected", "RefUrl": "/notes/128705 "}, {"RefNumber": "128020", "RefComponent": "PP-IS-DC", "RefTitle": "Setup is too slow", "RefUrl": "/notes/128020 "}, {"RefNumber": "121052", "RefComponent": "PP-IS-DC", "RefTitle": "Incomplete update if S026/S027 is inactive", "RefUrl": "/notes/121052 "}, {"RefNumber": "113952", "RefComponent": "PP-IS-DC", "RefTitle": "Incomplete update", "RefUrl": "/notes/113952 "}, {"RefNumber": "100515", "RefComponent": "PP-IS-DC", "RefTitle": "Setup orders with co-products and by-products", "RefUrl": "/notes/100515 "}, {"RefNumber": "136057", "RefComponent": "PP-IS-REP", "RefTitle": "MCP9 - no time series for production line possible", "RefUrl": "/notes/136057 "}, {"RefNumber": "172674", "RefComponent": "PP-IS-DC", "RefTitle": "Secondary resources not complete", "RefUrl": "/notes/172674 "}, {"RefNumber": "182585", "RefComponent": "PP-IS-DC", "RefTitle": "Capacity split causes update errors", "RefUrl": "/notes/182585 "}, {"RefNumber": "160322", "RefComponent": "PP-IS-DC", "RefTitle": "Queue time in hours", "RefUrl": "/notes/160322 "}, {"RefNumber": "159124", "RefComponent": "PP-IS-DC", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC in MCF_VERSION_UPD_V1", "RefUrl": "/notes/159124 "}, {"RefNumber": "128535", "RefComponent": "PP-IS-DC", "RefTitle": "CM25: DBIF_RTAB_NO_MEMORY", "RefUrl": "/notes/128535 "}, {"RefNumber": "107122", "RefComponent": "PP-IS-DC", "RefTitle": "Problems with automatic goods movements", "RefUrl": "/notes/107122 "}, {"RefNumber": "111644", "RefComponent": "PP-IS-DC", "RefTitle": "Problems: Setup rep. manufacturing, document log", "RefUrl": "/notes/111644 "}, {"RefNumber": "101694", "RefComponent": "PP-IS-DC", "RefTitle": "Double components are updated incorrectly", "RefUrl": "/notes/101694 "}, {"RefNumber": "107973", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion of partially delivered orders", "RefUrl": "/notes/107973 "}, {"RefNumber": "109906", "RefComponent": "PP-IS-DC", "RefTitle": "Order confirmation: BUDAT, GRUND, PERNR", "RefUrl": "/notes/109906 "}, {"RefNumber": "121674", "RefComponent": "PP-IS-EWS", "RefTitle": "EWS work center analysis: Capacity key figures", "RefUrl": "/notes/121674 "}, {"RefNumber": "125874", "RefComponent": "PP-IS", "RefTitle": "RMCFNEUA - 'SQL error 1562'", "RefUrl": "/notes/125874 "}, {"RefNumber": "132614", "RefComponent": "PP-IS-REP", "RefTitle": "Resource and process order in standard analysis", "RefUrl": "/notes/132614 "}, {"RefNumber": "150965", "RefComponent": "PP-IS-DC", "RefTitle": "Deletion flag and S026/MCCOMP", "RefUrl": "/notes/150965 "}, {"RefNumber": "185039", "RefComponent": "PP-IS-DC", "RefTitle": "Actual scrap quantity order item is incorrect", "RefUrl": "/notes/185039 "}, {"RefNumber": "117190", "RefComponent": "PP-IS-DC", "RefTitle": "Too large key fig.f.costs during setup of stat.data", "RefUrl": "/notes/117190 "}, {"RefNumber": "145294", "RefComponent": "PP-IS-DC", "RefTitle": "Text items in the material consumption", "RefUrl": "/notes/145294 "}, {"RefNumber": "165034", "RefComponent": "PP-IS-DC", "RefTitle": "Statistical setup: MESSAGE_TYPE_X", "RefUrl": "/notes/165034 "}, {"RefNumber": "170576", "RefComponent": "PP-IS-REP", "RefTitle": "Selection log in standard analysis repetitive mfg", "RefUrl": "/notes/170576 "}, {"RefNumber": "200239", "RefComponent": "PP-IS-REP", "RefTitle": "In operation analysis not enough data is displayed", "RefUrl": "/notes/200239 "}, {"RefNumber": "128906", "RefComponent": "PP-IS-REP", "RefTitle": "Planned/actual comparison in order/operation analys", "RefUrl": "/notes/128906 "}, {"RefNumber": "160872", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date in manufacture of co-products", "RefUrl": "/notes/160872 "}, {"RefNumber": "110661", "RefComponent": "PP-IS-DC", "RefTitle": "Too large cap.key fgs in user-defined info structrs", "RefUrl": "/notes/110661 "}, {"RefNumber": "111299", "RefComponent": "PP-IS", "RefTitle": "Flexible analysis via Operations: too large values", "RefUrl": "/notes/111299 "}, {"RefNumber": "104523", "RefComponent": "PP-IS-DC", "RefTitle": "No costs during change of work center", "RefUrl": "/notes/104523 "}, {"RefNumber": "181537", "RefComponent": "PP-IS-DC", "RefTitle": "PERFORM ON COMMIT: NESTED_PERFORM_ON_COMMIT", "RefUrl": "/notes/181537 "}, {"RefNumber": "145120", "RefComponent": "PP-IS-REP", "RefTitle": "Text display in flexible analysis", "RefUrl": "/notes/145120 "}, {"RefNumber": "138319", "RefComponent": "PP-IS-DC", "RefTitle": "Setup repetitive manufacturing by-products", "RefUrl": "/notes/138319 "}, {"RefNumber": "126710", "RefComponent": "PP-IS-DC", "RefTitle": "Update problems in collective orders", "RefUrl": "/notes/126710 "}, {"RefNumber": "110275", "RefComponent": "PP-IS-DC", "RefTitle": "Units in S021 and S023 / update log", "RefUrl": "/notes/110275 "}, {"RefNumber": "107909", "RefComponent": "PP-IS-DC", "RefTitle": "Automatic goods movements by-/co-products", "RefUrl": "/notes/107909 "}, {"RefNumber": "93075", "RefComponent": "PP-IS-DC", "RefTitle": "Deadlocks during update in PPIS", "RefUrl": "/notes/93075 "}, {"RefNumber": "180461", "RefComponent": "PP-IS-DC", "RefTitle": "Posting date of last confirmation not filled", "RefUrl": "/notes/180461 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I60", "URL": "/supportpackage/SAPKH31I60"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I60", "URL": "/supportpackage/SAPKE31I60"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}