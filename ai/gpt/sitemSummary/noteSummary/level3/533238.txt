SAP Note 533238 addresses an issue encountered in non-cumulative key figure queries within SAP BW (Business Warehouse). The problem arises when query results show data inconsistently for combinations of characteristics: data is shown only if there are instances with updated markers after compression; if, however, the query only considers instances without markers (which might occur after compression without a marker update), no data is displayed despite there being relevant value changes in the E fact table.

This behavior is due to a program error which can lead to incorrect query results. The corrective action provided in this note involves importing specific support packages into the SAP BW system. The recommended solutions are:

1. For BW 2.0B: Import Support Package 26 (BW2.0B patch 26 or SAPKW20B26). Further details on this package are expected to be provided in Note 0523133 titled "SAPBWNews BW 2.0B Support Package 26". The short text of this note might still indicate "preliminary version" if the note is released before the support package itself.

2. For BW 2.1C: Import Support Package 18 (BW2.1C patch 18 or SAPKW21C18). Information on this can be found in Note 0523196 titled "SAPBWNews BW 2.1C Support Package 18".

Additionally, the note references Note 110934 for more detailed information on BW Support Packages, suggesting that users refer to it for comprehensive guidance on the support package update process.