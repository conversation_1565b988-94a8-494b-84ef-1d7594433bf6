{"Request": {"Number": "2858884", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 377, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002092302019"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=9D46D7CC48D3651DC6A29000C0E993DF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2858884"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.12.2019"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-MGM-RAS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Risk Assessment"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP EHS Management", "value": "EHS-MGM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Risk Assessment", "value": "EHS-MGM-RAS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM-RAS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2858884 - Risk Assessment: Error due to date format settings in PDF forms"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Component:</td>\r\n<td>Risk Assessment</td>\r\n</tr>\r\n<tr>\r\n<td>Module:</td>\r\n<td>SAP Interactive Forms</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In your inbox application (worklist), you receive a work item for processing an control evaluation-related offline form that was received in the system. However, when you open the work item, a short dump occurs in the application.</p>\r\n<p>The error also occurs in any other modules of Risk Assessment where PDF Forms are being created:</p>\r\n<ul>\r\n<li>Job Hazard Analysis Report</li>\r\n<li>&#65279;Risk Assessment Report</li>\r\n<li>Risk Report</li>\r\n<li>Control Inspection Form</li>\r\n<li>Accessable Personal Exposure Profile</li>\r\n<li>Personal Exposure Profile</li>\r\n<li>Control Effectiveness Evaluation</li>\r\n<li>Example Safety Instruction</li>\r\n<li>Example Sampling Field Sheet</li>\r\n<li>Example Sampling Notification Letter</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Date specifications require the formatting defined in user defaults<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CM_EHHSS_AIF_RAS 030</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_RAS_JHA_MAIN</span> Job Hazard Analysis Report<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_RAS_MAIN</span> Risk Assessment Report<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_RSK_MAIN</span> Risk Report<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_CINSP_MAIN</span> Control Inspection Form<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_PEP_ACCESSABLE</span> Accessable Personal Exposure Profile<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_PEP_EXPOSURE</span> Personal Exposure Profile<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_PEP_EXPOS_ACCESSABLE</span> Accessable Personal Exposure Profile<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_PEP_MAIN</span> Personal Exposure Profile<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_RAS_CTRL_MAIN</span> Control Effectiveness Evaluation<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_SI_EXAMPLE</span> Example Safety Instruction corr<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_SPLFLDSHT_EXAMPLE</span> Example Sampling Field Sheet<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">EHHSS_AIF_SPLNOTLTR_EXAMPLE</span> Example Sampling Notification Letter</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHFND_DATE_HELPER</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHFND_DATE_HELPER_DAC</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHHSS_AIF_RAS_CTX_HELPER</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHHSS_AIF_CINSP_DPROV&#160;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHHSS_AIF_PEP_DPROV</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHHSS_AIF_RAS_CTRL_DPROV</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHHSS_AIF_RAS_DPROV</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHHSS_AIF_RAS_JHA_DPROV</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHHSS_AIF_RSK_DPROV</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHHSS_AIF_SI_DPROV</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHHSS_AIF_SPLFLDSHT_DPROV</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">CL_EHHSS_AIF_SPLNOTLTR_DPROV</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><span style=\"text-decoration: underline;\">Reason:</span><br />The&#160;date format settings of the user who processes the work item are different from the settings&#160;of the user, who created the form (usually a workflow batch user). Since most forms contain hidden time stamps, the inbound processing fails, due to the difference in these settings. Any country specific settings of the form are ignored.</p>\r\n<p><span style=\"text-decoration: underline;\">Prerequisites:</span><br />For information about the validity of the corrections, see the correction instructions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The attached correction instructions will remove the hidden time stamps from the form and clear them from&#160;the form interface. The&#160;removal&#160;applies when&#160;the date format&#160;settings of the user who creates the form are different from the country that was&#160;specified during the form creation. In all other cases the time stamps remain available but hidden&#160;in the form interface. <br />In addition, now there is&#160;a check&#160;during inbound processing, when the&#160;task opens. If the user who opens the task&#160;has different date format&#160;settings than the form specific settings, the error message<em> \"Date format &amp;1 of form processor and date format &amp;2 of form are different\" </em>is displayed. In its long text, the error message&#160;provides&#160;additional description of the problem and possible solution.</p>\r\n<p><span style=\"text-decoration: underline;\">Limitations</span>:<br />The attached correction&#160;does not repair already sent forms, or forms that have been received but not&#160;yet processed. In case there are open workflow tasks, it is recommended to temporarily change the date format settings in the user&#160;profile (transaction code <em>SU01).</em></p>\r\n<p>The \"Support Packages\" section lists the Support Packages that contain these corrections.<br /><br />Alternatively, you can implement the attached correction instructions.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "EHS-SUS-HS (Health & Safety Management)"}, {"Key": "Responsible                                                                                         ", "Value": "Al<PERSON>t <PERSON> (D054616)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D054619)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2595076", "RefComponent": "EHS-MGM-INC", "RefTitle": "Error due to date format settings in incident management", "RefUrl": "/notes/2595076"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2147718", "RefComponent": "XX-SER-REL", "RefTitle": "Component Extension 6.0 for SAP EHS Management: RIN", "RefUrl": "/notes/2147718 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10010INS4CORE", "URL": "/supportpackage/SAPK-10010INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10108INS4CORE", "URL": "/supportpackage/SAPK-10108INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10206INS4CORE", "URL": "/supportpackage/SAPK-10206INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10304INS4CORE", "URL": "/supportpackage/SAPK-10304INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10402INS4CORE", "URL": "/supportpackage/SAPK-10402INS4CORE"}, {"SoftwareComponentVersion": "EHSM 400", "SupportPackage": "SAPK-40007INEHSM", "URL": "/supportpackage/SAPK-40007INEHSM"}, {"SoftwareComponentVersion": "EHSM 500", "SupportPackage": "SAPK-50006INEHSM", "URL": "/supportpackage/SAPK-50006INEHSM"}, {"SoftwareComponentVersion": "EHSM 600", "SupportPackage": "SAPK-60006INEHSM", "URL": "/supportpackage/SAPK-60006INEHSM"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 4, "URL": "/corrins/**********/19773"}, {"SoftwareComponent": "EHSM", "NumberOfCorrin": 3, "URL": "/corrins/**********/9587"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 100&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10009INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10107INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10205INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 103&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10303INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 104&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10401INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Create new message CM_EHHSS_AIF_RAS 030:</P> <UL><LI>navigate into transaction SE91</LI></UL> <UL><LI>enter CM_EHHSS_AIF_RAS in field message class and</LI></UL> <UL><LI>click on button 'change'</LI></UL> <UL><LI>create new message with the No. 030 and the message short text ' Date  format &amp;1 of form processor and date format &amp;2 of form are different'.</LI></UL> <UL><LI>Save and activate the changes.</LI></UL> <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EHSM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 400&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-40006INEHSM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 500&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-50005INEHSM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60005INEHSM&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Create new message CM_EHHSS_AIF_RAS 030:</P> <UL><LI>navigate into transaction SE91</LI></UL> <UL><LI>enter CM_EHHSS_AIF_RAS in field message class and</LI></UL> <UL><LI>click on button 'change'</LI></UL> <UL><LI>create new message with the No. 030 and the message short text ' Date  format &amp;1 of form processor and date format &amp;2 of form are different'.</LI></UL> <UL><LI>Save and activate the changes.</LI></UL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 7, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 2, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2595076 ", "URL": "/notes/2595076 ", "Title": "Error due to date format settings in incident management", "Component": "EHS-MGM-INC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2595076 ", "URL": "/notes/2595076 ", "Title": "Error due to date format settings in incident management", "Component": "EHS-MGM-INC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2595076 ", "URL": "/notes/2595076 ", "Title": "Error due to date format settings in incident management", "Component": "EHS-MGM-INC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2595076 ", "URL": "/notes/2595076 ", "Title": "Error due to date format settings in incident management", "Component": "EHS-MGM-INC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2595076 ", "URL": "/notes/2595076 ", "Title": "Error due to date format settings in incident management", "Component": "EHS-MGM-INC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2595076 ", "URL": "/notes/2595076 ", "Title": "Error due to date format settings in incident management", "Component": "EHS-MGM-INC"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2885794", "RefTitle": "Risk Assessment: Error messages when trying to display a Person Exposure Profile", "RefUrl": "/notes/0002885794"}]}}}}}