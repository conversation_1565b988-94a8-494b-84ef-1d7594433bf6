{"Request": {"Number": "1005238", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 462, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005987352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001005238?language=E&token=9736E32BB346367C3D4BC5E795F5E79D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001005238", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001005238/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1005238"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 26}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.11.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON-TUN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Workload Monitoring Tool"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Workload Monitoring Tool", "value": "BC-CCM-MON-TUN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON-TUN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1005238 - Migration of workload statistics data to NW2004s"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>For Release NetWeaver2004s (Basis Release 7.00), we developed a completely new workload statistics collector to meet the requirements of large systems. This newly developed workload statistics collector is incompatible with earlier workload statistics data. After you upgrade to Netweaver2004s, you can no longer display workload statistics data in ST03N that has been entered before the upgrade.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ST03N, workload statistics collector</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Upgrade from 4.5B, 4.6C, 6.20 or 6.40 to NetWeaver2004s.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To ensure that the workload statistics data from releases 4.5B, 4.6B, 4.6C, 4.6D, 6.10, 6.20 and 6.40 can still be read after you upgrade to NetWeaver 2004s (or higher), you must save and convert it <B>before</B> the upgrade and import it into the new data store after the upgrade.  This note describes the steps you must perform and the programs you must implement when you do this.<br /><br />These are:</p> <UL><LI>Table ZWNCMOMIGR</LI></UL> <UL><UL><LI>You can use this table to import converted workload statistics data. You must create this table manually.</LI></UL></UL><UL><LI>Program RSMIGR12</LI></UL> <UL><UL><LI>You can use this program to convert workload statistics data, and save it in the ZWNCMOMIGR table.</LI></UL></UL> <UL><LI>Program SWNCMIGRATION1 (see Note 1006116)</LI></UL> <UL><UL><LI>You can use this program to read the ZWNCMOMIGR table, and to convert the workload statistics data to the NetWeaver2004s format.</LI></UL></UL> <p></p> <b>Procedure</b><br /> <p>You have to perform the following two steps to convert the workload statistics data of Releases 4.5B, 4.6C, 6.20 and 6.40:</p> <OL>1. Before the upgrade, you use the RSMIGR12 program to transfer the workload statistics data in an intermediate format (that is identical for all source releases), and to save the data in the ZWNCMOMIGR table.</OL> <OL>2. After the upgrade, the workload statistics data is read from the ZWNCMOMIGR table using the SWNCMIGRATION1 program. This data is converted into the new NetWeaver2004s format and saved in the SWNCMONI table. You can display data in ST03N as a result.</OL> <p><br /></p> <b>Notes concerning the application</b><br /> <p><B>The RSMIGR12 program </B> can be started as many times as you want. During each execution, the system examines which workload statistics data has changed since the last execution. The system only processes the changed data.<br />You can start the program both in dialog and in the background. In both cases, the system regularly informs you when the runtime ends approximately, either in the status bar of SAPGUI or in the job log of the background job.<br />The program runtime depends on the amount of workload statistics data to be converted and may take up to several hours. Therefore, we recommend that you start the program in due time before the upgrade (for example two weeks) to save most of the workload statistics data. The runtime for further regular executions is lower because the system must process only changed data. You should perform a last run directly before you start the upgrade to save the most current changes you made to the workload statistics data.<br /><br />Note the following: The total size of all data objects of an EXPORT statement that is used in the report RSMIGR12 to save the statistics aggregates to an intermediate format should not exceed 2 GB. Otherwise, this statistics aggregate (for example, an RFC server profile) cannot be exported. When the report is output, the system then provides you with information about which ST03N profiles were not exported.<br /><br /><B>The ZWNCMOMIGR table</B> may reach the size of the MONI table. Therefore, ensure that there is sufficient space.<br /><br /><B>The instance names</B> in the workload statistics data get a prefix consisting of&#x00A0;&#x00A0;the release identification before the upgrade and an underscore. The obsolete data in ST03N is clearly separated from the new data and any confusion can be avoided as a result (for example with month dates). For example: After you upgrade from Release 4.6C to NetWeaver2004, the system displays the workload statistics data of the \"abc123_XYZ_42\" instance in ST03N as the data of the \"46C_abc123_XYZ_42\" instance.<br /><br /><B>The SWNCMIGRATION1 program</B> is contained in Note 1006116. Implement the most current program version that is included in this note. Execute the SWNCMIGRATION1 program once. During the run, the system starts displaying the data that has already been converted in ST03N.<br />The program does not delete the data contained in the ZWNCMOMIGR table. Otherwise, the workload statistics data may be lost when a problem occurs. Ensure that the system displays all the relevant data in ST03N before you delete the ZWNCMOMIGR table using transaction SE11.<br /><br /></p> <b>Implementation in Releases 4.5B, 4.6C, 6.20 and 6.40</b><br /> <p><br />You must carry out an implementation manually, since the ZWNCMOMIGR table must be in the customer namespace. The RSMIGR12 program is <B>not </B>delivered with a Support Package. You are not required to change existing objects.<br /><br />First, to create the table ZWNCMOMIGR in a package of your choice (for example as a local object [no transport to receiving systems] or in the package or delivery class SAPWL_OLD_COLLECTOR), proceed as follows:<br /></p> <b>Table ZWNCMOMIGR</b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Short description \"Data migration for workload statistics when upgrading to 7.0\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Delivery class \"L\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Table maintenance allowed \"X \" (yes).<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Field</TH><TH ALIGN=LEFT> Key</TH><TH ALIGN=LEFT> Data Type / Length</TH></TR> <TR><TD>RELID</TD><TD> X</TD><TD> CHAR 2</TD></TR> <TR><TD>COMPONENT</TD><TD> X</TD><TD> CHAR 40</TD></TR> <TR><TD>COMPTYPE</TD><TD> X</TD><TD> CHAR 20</TD></TR> <TR><TD>ASSIGNDSYS</TD><TD> X</TD><TD> CHAR</TD><TD> 32</TD></TR> <TR><TD>PERIODTYPE</TD><TD> X</TD><TD> CHAR 1</TD></TR> <TR><TD>PERIODSTRT</TD><TD> X</TD><TD> DATS 8</TD></TR> <TR><TD>SRTF2</TD><TD> X</TD><TD> INT4 10</TD></TR> <TR><TD>PROFILVERS</TD><TD> </TD><TD> INT4 10</TD></TR> <TR><TD>CLUSTR</TD><TD> </TD><TD> INT2 5</TD></TR> <TR><TD>CLUSTD</TD><TD> </TD><TD> LRAW 2886</TD></TR> </TABLE> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Note:</B> To enter the data types, you must first switch to the direct type entry: Choose \"Data element/Direct type\" (4.6C) or \"Predefined Type\" (6.20, 6.40). The aforementioned data types are NOT data elements.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Technical settings of the ZWNCMOMIGR table:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Data class</TH><TH ALIGN=LEFT> Size category</TH><TH ALIGN=LEFT> Buffering</TH></TR> <TR><TD>APPL1</TD><TD> &lt;maximum value&gt;</TD><TD> Buffering not allowed</TD></TR> </TABLE> <p><br /></p> <b>Program RSMIGR12</b><br /> <p>You must also manually create the program RSMIGR12; the source text of the program for the supported releases is attached to this note. The program belongs to the package or delivery class SAPWL_OLD_COLLECTOR. Deactivate the modification assistant (Edit -&gt; Modification Operations -&gt; Switch Off Assistant).<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D052511)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D052511)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001005238/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001005238/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005238/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005238/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005238/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005238/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005238/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005238/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001005238/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "RSMIGR12_640.zip", "FileSize": "11", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000564182006&iv_version=0026&iv_guid=33BB8A095BADE14C81EF512620EFC427"}, {"FileName": "RSMIGR12_620.zip", "FileSize": "11", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000564182006&iv_version=0026&iv_guid=C16694188D241E43B227325FEB88EF0F"}, {"FileName": "RSMIGR12-46C.zip", "FileSize": "10", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000564182006&iv_version=0026&iv_guid=E37B5D889E5B9B44A151B2D273F9E188"}, {"FileName": "RSMIGR12-45B.zip", "FileSize": "9", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000564182006&iv_version=0026&iv_guid=F9A6DCDC740CC0409FD38A1286BCC702"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861"}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513"}, {"RefNumber": "961512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961512"}, {"RefNumber": "961511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961511"}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783"}, {"RefNumber": "947991", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947991"}, {"RefNumber": "925240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925240"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "913849", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913849"}, {"RefNumber": "913848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913848"}, {"RefNumber": "905029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/905029"}, {"RefNumber": "826488", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826488"}, {"RefNumber": "826487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826487"}, {"RefNumber": "826093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826093"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "818322", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP", "RefUrl": "/notes/818322"}, {"RefNumber": "1146580", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1146580"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1108700", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108700"}, {"RefNumber": "1108510", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR3 ABAP", "RefUrl": "/notes/1108510"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}, {"RefNumber": "1006116", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Migration of workload statistics data to NW2004s (2)", "RefUrl": "/notes/1006116"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2591577", "RefComponent": "BC-UPG-OCS", "RefTitle": "Missing ST03 data after an Upgrade of", "RefUrl": "/notes/2591577 "}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1006116", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Migration of workload statistics data to NW2004s (2)", "RefUrl": "/notes/1006116 "}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783 "}, {"RefNumber": "818322", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP", "RefUrl": "/notes/818322 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513 "}, {"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861 "}, {"RefNumber": "1108510", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR3 ABAP", "RefUrl": "/notes/1108510 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46B", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C55", "URL": "/supportpackage/SAPKB46C55"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C54", "URL": "/supportpackage/SAPKB46C54"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C66", "URL": "/supportpackage/SAPKB46C66"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62062", "URL": "/supportpackage/SAPKB62062"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62063", "URL": "/supportpackage/SAPKB62063"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64021", "URL": "/supportpackage/SAPKB64021"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64020", "URL": "/supportpackage/SAPKB64020"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP138", "SupportPackagePatch": "000138", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT UNICODE", "SupportPackage": "SP138", "SupportPackagePatch": "000138", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004835&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT", "SupportPackage": "SP138", "SupportPackagePatch": "000138", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004836&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 64-BIT UNICODE", "SupportPackage": "SP138", "SupportPackagePatch": "000138", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004837&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 3, "URL": "/corrins/0001005238/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}