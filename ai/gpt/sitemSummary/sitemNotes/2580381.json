{"Request": {"Number": "2580381", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 287, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000020494392017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002580381?language=E&token=12A9A7343390989292BCD388B0CA09C2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002580381", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002580381/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2580381"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.06.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-GEN"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT."}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT.", "value": "BW-BCT-GEN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-GEN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2580381 - SAP HANA-optimized BW Content for Inventory Management: Error in formula to calculate Valuated Stock Qty & Value"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>- Formulas&#160;updating Key Figures 0RECVALSTCK, 0ISSVALSTCK are incorrect in transformation&#160;TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM01. Therefore key figure <em>Quantity of Valuated Stock</em> 0VALSTCKQTY might show improper stock quantities.</p>\r\n<p>- Formulas updating Key Figures 0RECVS_VAL, 0ISSVS_VAL are incorrect&#160;in transformation TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM02. Therefore key figure <em>Value of Valuated Stock</em>&#160;0VALSTCKVAL might show improper stock values.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>HANA-optimized BW Content, BW/4 HANA&#160;Content,&#160;Inventory Management, 2LIS_03_BF, /IMO/MMIM_IS01, /IMO/D_MMIM01, /IMO/D_MMIM02, /IMO/V_MMIM01, 0RECVALSTCK, 0ISSVALSTCK, 0VALSTCKQTY, 0RECVS_VAL, 0ISSVS_VAL, 0VALSTCKVAL</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Formulas for&#160;Key Figures&#160;0RECVALSTCK and 0ISSVALSTCK&#160;are&#160;incorrect&#160;in following transformation:</p>\r\n<p style=\"padding-left: 30px;\">TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM01 (0R4QN2QWJSLIZGGOQQT17GKNIG85MLEI)</p>\r\n<p>Formulas for&#160;Key Figures&#160;0RECVS_VAL and&#160;0ISSVS_VAL&#160;are&#160;incorrect&#160;in following transformation:</p>\r\n<p style=\"padding-left: 30px;\">TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM02 (01AZMRZRF969E236CNLVN7N4HAHGS974)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The mentioned transformation formulas&#160;are&#160;corrected&#160;in the newest support packages of the SAP HANA-optimized BW Content.</p>\r\n<p>We recommend to install</p>\r\n<p>- BI Content 757 SP17 (SAPK-75717INBICONT)&#160;or later</p>\r\n<p>- BW/4 HANA Content 100 SP05 (SAPK-100005INBW4CONT)&#160;or later</p>\r\n<p>and reactivate the content.</p>\r\n<p><span style=\"text-decoration: underline;\">Additional critical SAP Note:</span> Please also check if <a target=\"_blank\" href=\"/notes/2543774\">SAP Note 2543774&#160;</a>\"<em>Data loading to Non Cumulative&#160;Cube Like ADSO&#160;doesn't work correctly</em>\" is applied in your system (with manual acitvities).</p>\r\n<p>&#160;</p>\r\n<p>In case you want to adapt the content objects in the 'A' version&#160;in your BW on HANA or BW/4 system, please proceed with the following changes:</p>\r\n<p><strong>In SAP GUI (SAP BW and SAP BW/4HANA &lt; SP08):</strong></p>\r\n<p>1) In transformation TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM01 (0R4QN2QWJSLIZGGOQQT17GKNIG85MLEI)&#160;replace the formula for 0RECVALSTCK&#160;by the formula&#160;statement below:</p>\r\n<p style=\"padding-left: 30px;\"><em>IF( BWAPPLNM = 'MM' AND ( PROCESSKEY = '&#160; 0' OR PROCESSKEY = '&#160; 1' OR PROCESSKEY = '&#160; 4' OR PROCESSKEY = '&#160; 5' OR PROCESSKEY = '&#160; 6' OR PROCESSKEY = ' 10' ) AND</em><br /><em>STOCKRELEV = '1' AND</em><br /><em>( STOCKCAT = '' OR ( <strong>(</strong> STOCKCAT = 'E' OR STOCKCAT = 'Q' <strong>)</strong> AND <strong>(</strong> INDSPECSTK = 'A' OR INDSPECSTK = 'M' <strong>)</strong> ) ), CPQUABU, 0 )</em></p>\r\n<p>2) In same&#160;transformation TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM01 (0R4QN2QWJSLIZGGOQQT17GKNIG85MLEI) replace the formula for&#160;0ISSVALSTCK&#160;by the formula&#160;statement below and activate the transformation:</p>\r\n<p style=\"padding-left: 30px;\"><em>IF( BWAPPLNM = 'MM' AND ( PROCESSKEY = '100' OR PROCESSKEY = '101' OR PROCESSKEY = '104' OR PROCESSKEY = '105' OR PROCESSKEY = '106' OR PROCESSKEY = '110' ) AND</em><br /><em>STOCKRELEV = '1' AND</em><br /><em>( STOCKCAT = '' OR ( <strong>(</strong> STOCKCAT = 'E' OR STOCKCAT = 'Q' <strong>)</strong> AND <strong>(</strong> INDSPECSTK = 'A' OR INDSPECSTK = 'M' <strong>)</strong> ) ), CPQUABU, 0 )</em></p>\r\n<p>3) In transformation TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM02 (01AZMRZRF969E236CNLVN7N4HAHGS974)&#160;replace the formula for&#160;0RECVS_VAL&#160;by the formula&#160;statement below:</p>\r\n<p style=\"padding-left: 30px;\"><em>IF( BWAPPLNM = 'MM' AND ( PROCESSKEY = '&#160; 0' OR PROCESSKEY = '&#160; 1' OR PROCESSKEY = '&#160; 4' OR PROCESSKEY = '&#160; 5' OR PROCESSKEY = '&#160; 6' OR PROCESSKEY = ' 10' ) AND</em><br /><em>STOCKRELEV = '1' AND</em><br /><em>( STOCKCAT = ''&#160; OR ( <strong>(</strong> STOCKCAT = 'E' OR STOCKCAT = 'Q' <strong>)</strong> AND <strong>(</strong> INDSPECSTK = 'A' OR INDSPECSTK = 'M' <strong>)</strong> ) ), CPPVLC, 0 )</em></p>\r\n<p>4) In same transformation TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM02 (01AZMRZRF969E236CNLVN7N4HAHGS974)&#160;replace the formula for&#160;0ISSVS_VAL&#160;by the formula&#160;statement below and activate the transformation:</p>\r\n<p style=\"padding-left: 30px;\"><em>IF( BWAPPLNM = 'MM' AND ( PROCESSKEY = '100' OR PROCESSKEY = '101' OR PROCESSKEY = '104' OR PROCESSKEY = '105' OR PROCESSKEY = '106' OR PROCESSKEY = '110' ) AND</em><br /><em>STOCKRELEV = '1' AND</em><br /><em>( STOCKCAT = '' OR ( <strong>(</strong> STOCKCAT = 'E' OR STOCKCAT = 'Q' <strong>)</strong> AND <strong>(</strong> INDSPECSTK = 'A' OR INDSPECSTK = 'M' <strong>)</strong> ) ), CPPVLC, 0 )</em></p>\r\n<p>&#160;</p>\r\n<p><strong>In BW-Modeling Tool in Eclipse (SAP BW/4HANA 1.0 &gt; SP08 and SAP BW/4HANA 2.0):</strong></p>\r\n<p>1) In transformation TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM01 (0R4QN2QWJSLIZGGOQQT17GKNIG85MLEI)&#160;replace the formula for 0RECVALSTCK&#160;by the formula&#160;statement below:</p>\r\n<p style=\"padding-left: 30px;\"><em>IF( BWAPPLNM = 'MM' AND ( PROCESSKEY = '&#160; 0' OR PROCESSKEY = '&#160; 1' OR PROCESSKEY = '&#160; 4' OR PROCESSKEY = '&#160; 5' OR PROCESSKEY = '&#160; 6' OR PROCESSKEY = ' 10' ) AND</em><br /><em>STOCKRELEV = '1' AND</em><br /><em>( STOCKCAT = '' OR ( <strong>(</strong> STOCKCAT = 'E' OR STOCKCAT = 'Q' <strong>)</strong> AND <strong>(</strong> INDSPECSTK = 'A' OR INDSPECSTK = 'M' <strong>)</strong> ) ); CPQUABU; 0 )</em></p>\r\n<p>2) In same&#160;transformation TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM01 (0R4QN2QWJSLIZGGOQQT17GKNIG85MLEI) replace the formula for&#160;0ISSVALSTCK&#160;by the formula&#160;statement below and activate the transformation:</p>\r\n<p style=\"padding-left: 30px;\"><em>IF( BWAPPLNM = 'MM' AND ( PROCESSKEY = '100' OR PROCESSKEY = '101' OR PROCESSKEY = '104' OR PROCESSKEY = '105' OR PROCESSKEY = '106' OR PROCESSKEY = '110' ) AND</em><br /><em>STOCKRELEV = '1' AND</em><br /><em>( STOCKCAT = '' OR ( <strong>(</strong> STOCKCAT = 'E' OR STOCKCAT = 'Q' <strong>)</strong> AND <strong>(</strong> INDSPECSTK = 'A' OR INDSPECSTK = 'M' <strong>)</strong> ) ); CPQUABU; 0 )</em></p>\r\n<p>3) In transformation TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM02 (01AZMRZRF969E236CNLVN7N4HAHGS974)&#160;replace the formula for&#160;0RECVS_VAL&#160;by the formula&#160;statement below:</p>\r\n<p style=\"padding-left: 30px;\"><em>IF( BWAPPLNM = 'MM' AND ( PROCESSKEY = '&#160; 0' OR PROCESSKEY = '&#160; 1' OR PROCESSKEY = '&#160; 4' OR PROCESSKEY = '&#160; 5' OR PROCESSKEY = '&#160; 6' OR PROCESSKEY = ' 10' ) AND</em><br /><em>STOCKRELEV = '1' AND</em><br /><em>( STOCKCAT = ''&#160; OR ( <strong>(</strong> STOCKCAT = 'E' OR STOCKCAT = 'Q' <strong>)</strong> AND <strong>(</strong> INDSPECSTK = 'A' OR INDSPECSTK = 'M' <strong>)</strong> ) ); CPPVLC; 0 )</em></p>\r\n<p>4) In same transformation TRCS /IMO/MMIM_IS01 -&gt; ADSO /IMO/D_MMIM02 (01AZMRZRF969E236CNLVN7N4HAHGS974)&#160;replace the formula for&#160;0ISSVS_VAL&#160;by the formula&#160;statement below and activate the transformation:</p>\r\n<p style=\"padding-left: 30px;\"><em>IF( BWAPPLNM = 'MM' AND ( PROCESSKEY = '100' OR PROCESSKEY = '101' OR PROCESSKEY = '104' OR PROCESSKEY = '105' OR PROCESSKEY = '106' OR PROCESSKEY = '110' ) AND</em><br /><em>STOCKRELEV = '1' AND</em><br /><em>( STOCKCAT = '' OR ( <strong>(</strong> STOCKCAT = 'E' OR STOCKCAT = 'Q' <strong>)</strong> AND <strong>(</strong> INDSPECSTK = 'A' OR INDSPECSTK = 'M' <strong>)</strong> ) ); CPPVLC; 0 )</em></p>\r\n<p>Please be adviced that a reload of the data from Corporate Memory /IMO/CMMMIM01H with DTP 'Historical Transactions'&#160;and /IMO/CMMMIM01 with normal Delta DTP&#160;is necessary after the change is applied.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I037341)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I037341)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002580381/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002580381/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002580381/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002580381/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002580381/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002580381/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002580381/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002580381/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002580381/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2543774", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "740SP19: Data loading to Non Cumulative Cube Like ADSO doesn't work correctly", "RefUrl": "/notes/2543774"}, {"RefNumber": "2400585", "RefComponent": "BW-BCT-GEN", "RefTitle": "Collective Note: SAP BW/4HANA Content 1.0 (BW4CONT 100 & BW4CONTB 100)", "RefUrl": "/notes/2400585"}, {"RefNumber": "1817520", "RefComponent": "BW-BCT-DOC", "RefTitle": "Collective Note: SAP HANA-optimized BI Content shipped with BI_CONT 737 / 747 / 757", "RefUrl": "/notes/1817520"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2678507", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "SAP ERP / S/4HANA Inventory Management in SAP BW/4HANA (Composite SAP Note)", "RefUrl": "/notes/2678507 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BW4CONT", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "757", "To": "757", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}