{"Request": {"Number": "493315", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 256, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015166592017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000493315?language=E&token=C6CE39BE51C138AE6EBCD6FC006F5224"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000493315", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000493315/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "493315"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.11.2013"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-REQ"}, "SAPComponentKeyText": {"_label": "Component", "value": "Purchase Requisitions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchase Requisitions", "value": "MM-PUR-REQ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-REQ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "493315 - FAQ: Purchase requisition (general)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note contains a list of frequently asked questions about purchase requisition. For questions regarding the Enjoy purchase requisition (transaction ME51N) in particular, also refer to FAQ Note 493318.</p>\r\n<p><strong>Questions:</strong></p>\r\n<ol>\r\n<li>You delete a purchase order item with reference to the purchase requisition. Why is the last purchase order in the purchase requisition not reset and why is <strong>processing status</strong> &quot;B&quot; not changed?</li>\r\n<li>From a purchase requisition, you first create a request for quotation and then a contract. You then delete the contract. Why is the <strong>processing status</strong> not reset to &quot;A&quot;?</li>\r\n<li>A purchase requisition has an incorrect <strong>processing status</strong>. How can the status be corrected?</li>\r\n<li>When is a purchase requisition <strong>&quot;fixed&quot;</strong>?</li>\r\n<li>On the initial screen of transaction ME51, you specify a <strong>purchasing group</strong> or a <strong>material group</strong>. Why is this data overwritten with the values from the material master?</li>\r\n<li>You create a purchase requisition from an order. Why is no <strong>planning file entry</strong> updated for the material?</li>\r\n<li>The <strong>ordered quantity</strong> of a purchase requisition is incorrect. How can you detect and eliminate such inconsistencies?</li>\r\n<li>What do you enter in the <strong>&quot;Created by&quot;</strong> field of a purchase requisition?</li>\r\n<li>The <strong>&quot;Batch&quot;</strong> field was set as a required entry field in Customizing. When you create a purchase requisition, why does the system also issue message ME 083 (&quot;Enter a batch&quot;) for materials that are not flagged as subject to batch management requirement in the material master?</li>\r\n<li>Why are the fields &quot;Planned Delivery Time&quot;, &quot;Unit of Measure&quot;, &quot;Material Short Text&quot;, &quot;Goods Receipt Processing Time&quot;, &quot;Material Category&quot;, and &quot;MRP Controller&quot; redetermined from the material master and not copied from the reference purchase requisition when you <strong>copy</strong> a purchase requisition?</li>\r\n<li>Are the purchase requisition transactions <strong>ME51</strong>, <strong>ME52</strong>, and <strong>ME53</strong> still <strong>available</strong> in addition to the new Enjoy transactions for purchase requisitions?</li>\r\n<li><strong>Generic articles</strong> are not exploded in the purchase requisition.</li>\r\n<li>Why does the system not recalculate the delivery date in the purchase requisition when the source of supply is assigned or changed retroactively?</li>\r\n</ol>\r\n<p><br/>--------------------------</p>\r\n<ol>\r\n<li><strong>Question: </strong>You delete a purchase order item with reference to the purchase requisition. Why is the last purchase order in the purchase requisition not reset and why is <strong>processing status</strong> &quot;B&quot; not changed?<br/><br/><strong>Answer</strong>: See SAP Note 65839.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>From a purchase requisition, you first create a request for quotation and then a contract. You then delete the contract. Why is the <strong>processing status</strong> not reset to &quot;A&quot;?<br/><br/><strong>Answer</strong>: For technical reasons, this is unfortunately not possible. The processing status is set to &quot;N&quot;.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>A purchase requisition has an incorrect <strong>processing status</strong>. How can the status be corrected?<br/><br/><strong>Answer</strong>: See SAP Note 81723.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>When is a purchase requisition <strong>&quot;fixed&quot;</strong>?<br/><br/><strong>Answer</strong>: See SAP Note 88266.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>On the initial screen of transaction ME51, you specify a <strong>purchasing group</strong> or a <strong>material group</strong>. Why is this data overwritten with the values from the material master?<br/><br/><strong>Answer</strong>: This is because the data from the material master is more specific. See also SAP Note 178306.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>You create a purchase requisition from an order. Why is no <strong>planning file entry</strong> updated for the material?<br/><br/><strong>Answer</strong>: See SAP Note 182787.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>The <strong>ordered quantity</strong> of a purchase requisition is incorrect. How can you detect and eliminate such inconsistencies?<br/><br/><strong>Answer</strong>: The correction report RM06HL04 is available for this. (See also SAP Note 171331.) In case of inconsistent data in connection with sales orders, refer also to SAP Note 530351.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>What do you enter in the <strong>&quot;Created by&quot;</strong> field of a purchase requisition?<br/><br/><strong>Answer</strong>: See SAP Note 328907.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>The <strong>&quot;Batch&quot;</strong> field was set as a required entry field in Customizing. When you create a purchase requisition, why does the system also issue message ME 083 &quot;Enter a batch&quot; for materials that are not flagged as subject to batch management requirement in the material master? <br/><br/><strong>Answer</strong>: For technical reasons, it is too difficult to distinguish between materials with and without batch management requirement here. The setting option in Customizing is only provided to hide the field or to set it as a display field.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>Why are the fields &quot;Planned Delivery Time&quot;, &quot;Unit of Measure&quot;, &quot;Material Short Text&quot;, &quot;Goods Receipt Processing Time&quot;, &quot;Material Category&quot;, and &quot;MRP Controller&quot; redetermined from the material master and not copied from the reference purchase requisition when you <strong>copy</strong> a purchase requisition?<br/><br/><strong>Answer</strong>: This system behavior is intentional as many companies are more likely to change the data in the material master than to copy a purchase requisition that contains changed material data.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>Are the purchase requisition transactions <strong>ME51</strong>, <strong>ME52</strong>, and <strong>ME53</strong> still <strong>available</strong> in addition to the new Enjoy transactions for purchase requisitions?<br/><br/><strong>Answer</strong>: See also SAP Note 1803189.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong><strong>Generic articles</strong> are not exploded in the purchase requisition.<br/><br/><strong>Answer</strong>: This is the standard R/3 behavior. When a purchase order is made, the generic articles are exploded.<br/><br/>-------------------------------</li>\r\n<li><strong>Question: </strong>Why does the system not recalculate the delivery date in the purchase requisition when the source of supply is assigned or changed retroactively?<br/><br/><strong>Answer</strong>: This is the standard R/3 behavior. When the purchase requisition is converted to a purchase order, the delivery date is transferred 1:1 from the purchase requisition, regardless of whether a source of supply is assigned or not. In many cases, the delivery date of the purchase requisition is already specified by the MRP run or manually specified by the user; the delivery date may lie in the past.<br/><br/><strong>Solution</strong>: We recommend that you convert the purchase requisition to a purchase order promptly or that you maintain a unique source of supply.<br/><br/>-------------------------------</li>\r\n</ol>\r\n<p>&#x00A0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ, PReq, purchase requisition, ME51, ME52, ME53, ME51N, ME52N, ME53N, MEREQ, sales order, production order, maintenance order, network, processing status, STATU, EBAN-STATU, fixed indicator, planning file entry, stock transport requisition, purchasing group, material group, material master, RM06HL04, EBAN, EBAN-BSMNG, BSMNG, batch, material category, MRP controller, planned delivery time, material short text, processing time, goods receipt</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>-</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>-</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025793)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025793)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000493315/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493315/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493315/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493315/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493315/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493315/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493315/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493315/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000493315/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "88266", "RefComponent": "MM-PUR-REQ", "RefTitle": "Fixed indicator PReq - MD04", "RefUrl": "/notes/88266"}, {"RefNumber": "81723", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/81723"}, {"RefNumber": "65839", "RefComponent": "MM-PUR-REQ", "RefTitle": "Purchase order statistics in purchase requisition", "RefUrl": "/notes/65839"}, {"RefNumber": "65557", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/65557"}, {"RefNumber": "635511", "RefComponent": "MM-SRV", "RefTitle": "FAQ: Purchase requisitions in the service", "RefUrl": "/notes/635511"}, {"RefNumber": "530351", "RefComponent": "SD-SLS-SO-TP", "RefTitle": "Purchase requisition: Reference to non-existent purchase order", "RefUrl": "/notes/530351"}, {"RefNumber": "493318", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "FAQ: Purchase requisition (ME51N, ME52N, ME53N)", "RefUrl": "/notes/493318"}, {"RefNumber": "328907", "RefComponent": "PP-MRP-PP", "RefTitle": "MRP controller as creator of purchase requisitions from MRP run", "RefUrl": "/notes/328907"}, {"RefNumber": "182787", "RefComponent": "MM-PUR-REQ", "RefTitle": "No planning file entry with PReqs from SD/PP/PS", "RefUrl": "/notes/182787"}, {"RefNumber": "178306", "RefComponent": "MM-PUR-REQ", "RefTitle": "ME51: Proposal purchasing group and material group", "RefUrl": "/notes/178306"}, {"RefNumber": "171331", "RefComponent": "MM-PUR-REQ", "RefTitle": "Correction report for quantity ordered in PReq", "RefUrl": "/notes/171331"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "81723", "RefComponent": "MM-PUR-REQ", "RefTitle": "ME52/ME53: Incorrect status 'N' in requisitions", "RefUrl": "/notes/81723 "}, {"RefNumber": "530351", "RefComponent": "SD-SLS-SO-TP", "RefTitle": "Purchase requisition: Reference to non-existent purchase order", "RefUrl": "/notes/530351 "}, {"RefNumber": "493318", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "FAQ: Purchase requisition (ME51N, ME52N, ME53N)", "RefUrl": "/notes/493318 "}, {"RefNumber": "88266", "RefComponent": "MM-PUR-REQ", "RefTitle": "Fixed indicator PReq - MD04", "RefUrl": "/notes/88266 "}, {"RefNumber": "635511", "RefComponent": "MM-SRV", "RefTitle": "FAQ: Purchase requisitions in the service", "RefUrl": "/notes/635511 "}, {"RefNumber": "65839", "RefComponent": "MM-PUR-REQ", "RefTitle": "Purchase order statistics in purchase requisition", "RefUrl": "/notes/65839 "}, {"RefNumber": "182787", "RefComponent": "MM-PUR-REQ", "RefTitle": "No planning file entry with PReqs from SD/PP/PS", "RefUrl": "/notes/182787 "}, {"RefNumber": "65557", "RefComponent": "MM-PUR", "RefTitle": "Attaching documents to requisition line items", "RefUrl": "/notes/65557 "}, {"RefNumber": "328907", "RefComponent": "PP-MRP-PP", "RefTitle": "MRP controller as creator of purchase requisitions from MRP run", "RefUrl": "/notes/328907 "}, {"RefNumber": "171331", "RefComponent": "MM-PUR-REQ", "RefTitle": "Correction report for quantity ordered in PReq", "RefUrl": "/notes/171331 "}, {"RefNumber": "178306", "RefComponent": "MM-PUR-REQ", "RefTitle": "ME51: Proposal purchasing group and material group", "RefUrl": "/notes/178306 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}