{"guid": "6CAE8B3E8C3B1ED78EE96843DAE940C5", "sitemId": "SI03: CPD_FIORI_APPS", "sitemTitle": "S4TWL - <PERSON><PERSON> Apps of SAP Commercial Project Management", "note": 2320143, "noteTitle": "2320143 - S4TWL – <PERSON><PERSON> Apps of SAP Commercial Project Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP Commercial Project Management for SAP S/4HANA. The following SAP Commercial Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Commercial Project Management, CA-CPD, S/4HANA, Fiori apps</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Note that the following Smart Business analytical and transactional apps are not immediately available with <em>SAP Commercial Project Management for SAP S/4HANA</em>.</p>\n<ul>\n<li>F0709             My Commercial Projects</li>\n<li>F0710             Action Items in Commercial Projects</li>\n<li>F0779             Status Management in Commercial Projects</li>\n<li>F0784             Overview of Commercial Projects</li>\n<li>F0786             Costs and Revenues of Commercial Projects</li>\n<li>F0787             Analysis of Commercial Projects</li>\n</ul>\n<p><strong>BuInformation Related to Business Processes</strong></p>\n<p>No influence on business processes is expected.</p>\n<p><strong>Required and Recommended Actions</strong></p>\n<p>None</p>", "noteVersion": 2, "refer_note": [{"note": "2217279", "noteTitle": "2217279 - Uninstallation of the Fiori UI Component UICPD001 100 from the Product  version SAP Fiori 1.0 for SAP Commercial Project Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Uninstallation of Fiori UI component UICPD001 100 using SAINT</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAINT, Add-on, UICPD001 100,SAPK-100AGINUICPD001,SAP FIORI FOR SAP CPM 1.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You would like to remove/uninstall the software component UICPD001 100 from your system. To do so, you must ensure that all business processes related to this software component are completed before the uninstallation can be  started.</p>\n<p>1. Read the following SAP Notes before you begin the uninstallation:<br/>       70228 - Add-ons: Conditions and upgrade planning<br/>       2011192 - Uninstalling ABAP add-ons</p>\n<p>2. Manual preparation steps before uninstalling</p>\n<p><span>Disconnect the users from the FIORI apps delivered with the component:</span></p>\n<ol>\n<li>\n<div>Make sure that the roles provided with the software component,that is,SAP_CPM_BCR_PROJCONSULTANT_T,SAP_CPM_BCR_PROJMANAGER_T and SAP_CPM_TCR_T are not assigned to any users in the system. To check this, call transaction PFCG in the system for these roles. The assigned users are displayed on the <em>User</em> tab page. Delete all assignments.</div>\n</li>\n<li>Make sure you have not created any customer roles (transaction PFCG) that refer to these roles. If necessary, delete these assignments.</li>\n<li>Make sure you have not created any customer FIORI Launchpad roles or catalogs that refer to these roles or catalogs, respectively. If necessary, delete these assignments (in FIORI Launchpad Administration).</li>\n</ol>\n<p>3. To Uninstall the software component UICPD001 100 you can use the Add-On Installation Tool to uninstall add-ons. Deletion of some add-ons is only possible if certain prerequisites apply, however.</p>\n<p>Make sure that the latest available ACP of the software component version is installed in the system<br/><br/>    Prerequisites:<br/><br/>       a. You are logged on to client 000.</p>\n<p>       b. You have entered transaction code SAINT to start the Add-On Installation Tool.</p>\n<p>       c. The add-on you want to delete is displayed<em> </em>in the tab page of components that can be uninstalled.</p>\n<p>       d. Your system is configured correctly and you have imported the latest update of SPAM/SAINT.</p>\n<p>       e. You have read the uninstall SAP Note for the add-on you want to delete and have followed the instructions there.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Procedure</p>\n<ol>1. Choose the tab page for components that can be uninstalled.</ol><ol>2. From the list, select the add-on that you want to uninstall.</ol><ol>3. Choose Start to start the uninstall process.</ol><ol>4. A confirmation prompt describes potential dangers of uninstallation and refers to an SAP Note with additional important information. Be sure to read this SAP Note and follow its instructions.</ol><ol>5. If you have read the SAP Note and followed its instructions, choose \"Yes\".</ol>\n<p>To display the SAP Note, choose \"Display\".<br/>To cancel the uninstall process, choose \"Cancel\".</p>\n<ol>6. After you start the uninstall process, the Add-On Installation Tool runs a predefined sequence of phases. Should an error occur in any of these phases, the uninstall process is stopped and the error is described to the extent possible. Once you have corrected the problem, you can choose \"Continue\" to continue the uninstall process.7. At first, the Add-On Installation Tool performs preparation and check steps. If the errors cannot be corrected during these phases, you can choose \"Back\" to stop and reset the uninstall process. In later phases, when the system has already made changes and deletions, a reset is no longer possible and the system issues an appropriate error message. In this case, you have to correct any errors and then complete the uninstall process</ol><ol>8. Once the add-on has been uninstalled successfully, you can choose \"Logs\" to see the import logs or choose \"Exit\" to complete the uninstall process</ol>", "noteVersion": 1, "refer_note": [{"note": "2011192", "noteTitle": "2011192 - Uninstallation of ABAP add-ons", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note provides information about uninstalling ABAP add-ons with the SAP Add-On Installation Tool (transaction SAINT).</p>\n<p><strong>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</strong></p>\n<p><strong>Due to the rapidly increasing number of uninstallable </strong></p>\n<p><strong>add-ons, this list is not complete. <strong><strong>Additional SAP Notes about uninstallable add-ons </strong></strong></strong></p>\n<p><strong><strong>are located in the reference list of this SAP Note.</strong></strong></p>\n<p><strong><strong><strong>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</strong> </strong></strong></p>\n<p><strong>A list of deletable objects is contained in the attachment \"Deletable_Objects\". The </strong></p>\n<p><strong>list is updated with every SPAM update. </strong></p>\n<p><strong>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAINT, add-on uninstallation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>As of Version 0053 of the SAP Add-On Installation Tool, not only can you install ABAP add-ons, but you can also uninstall them again in certain circumstances.<br/>The following sections provide the most important information about uninstalling ABAP add-ons as well as a list of previously available add-ons that can be uninstalled (<strong>examples</strong>).</p>\n<p><strong>Caution</strong>: The uninstallation of ABAP add-ons can result in unintentional data loss. Read this SAP Note thoroughly before starting an uninstallation. It contains further information and steps that might be required for the preparation and postprocessing of the uninstallation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Technical Prerequisites:</strong></p>\n<ul>\n<li>The system is based on SAP NetWeaver Release 7.0 or above.</li>\n<li>You have installed at least SPAM/SAINT Version 0053.</li>\n<li>You use a kernel with at least Release 7.2.</li>\n<li>The transport tool tp has at least Version 380.07.22.</li>\n<li>The transport tool R3trans has at least the version from AUG/06/2013.</li>\n<li>The last ACP of the add-on to be uninstalled has been installed in the system.</li>\n</ul>\n<p>During the uninstall process, the system checks and ensures the existence of the minimum versions of the kernel, tp, and R3trans.</p>\n<p><strong>Uninstall process:</strong></p>\n<p>When you trigger the uninstall process for an add-on with the SAP Add-On Installation Tool, the tool searches the system for content that belongs to the add-on that is to be deleted:</p>\n<ul>\n<li>All content contained in installation packages, upgrade packages, and support packages of the add-on</li>\n<li>Content generated automatically by SAP Notes for the add-on</li>\n<li>Content created manually in development packages that belong to the add-on</li>\n</ul>\n<p>Based on this, the tool creates a list of objects that must be deleted to uninstall the add-on. The tool also checks the following:</p>\n<ul>\n<li>Are there any dependencies between the objects in the piece list and other content?</li>\n<li>Have the objects been modified by the customer?</li>\n<li>Are the objects active?</li>\n<li>Do the objects belong to different software components?</li>\n</ul>\n<p>If objects with errors are identified during these checks, these objects cannot be deleted until the errors have been corrected. This occurs at the end of the check phase, partly automatically and partly manually.</p>\n<p>When all checks have been successfully completed, the SAP Add-On Installation Tool removes the objects to be deleted from the system and changes the system status accordingly.</p>\n<p>The product data of the add-on can then be deleted using the CISI process as described in SAP Note 1816146.</p>\n<p><strong></strong> <strong>List of ABAP add-ons that can be uninstalled:</strong></p>\n<p><strong><em>CAUTION:</em></strong> <em>This list is not complete. Please do not make any changes or additions. </em></p>\n<p>Since the deletion of add-ons without errors cannot be ensured only by using the functions of the SAP Add-On Installation Tool, the add-ons must fulfill the relevant prerequisites, must be explicitly earmarked for deletion, and must be flagged as deletable.</p>\n<p>Currently, the following add-ons can be uninstalled:</p>\n<ul>\n<li>ARIBA CLOUD INT SAP ERP 1.0 (component ARBCI1 100, ARBCI2 100, SAP Note 3243704)</li>\n<li>Ariba Network Integration 1.0 for SAP Business Suite (components ARBFNDI1 100, ARBFNDI2 100, ARBSRMI1 100, ARBERPI1 100, SAP Note 2067891)</li>\n<li>Cloud Lifecycle Management 100, SAP Note 2398413</li>\n<li>Concur Integration (components CTE_HCM 100, CTE_FIN 100, CTE_INV 100 , CTE_FND 100, CTE_FGM 100, SAP Note 2313330)</li>\n<li>Concur Integration (components CTE_HCM 10S , CTE_FIN 10S, CTE_INV 10S, CTE_FND 10S, SAP Note 2407737)</li>\n<li>DATA XCHANGE SWISS UTILITY 1.0 (component IDXPF 604, SAP Note 2525068)</li>\n<li>Desktop Connection for SAP CRM (component CRMGWS 700, SAP Note 2200413)</li>\n<li>Duet Enterprise 2.0 (component IW_TNG 200, SAP Note 2503641)</li>\n<li>Flexible Benefits for Utilities UI 600 (component FB4R 600, SAP Note 2152381)</li>\n<li><span 107%;=\"\" ar-sa;=\"\" arial;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;\"=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" verdana',sans-serif;=\"\">IDEX F SWISS ELECTRIC COMP 1.0 (component IDEXCH 604, SAP Note 2609756)</span></li>\n<li><span 107%;=\"\" ar-sa;=\"\" arial;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;\"=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" verdana',sans-serif;=\"\">Integration to ERP 6.0 Mobile Instance value application ERP SD 601 (component </span><span 107%;=\"\" ar-sa;=\"\" arial;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;\"=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" verdana',sans-serif;=\"\">MIVERPSD 601, SAP Note 2313327)</span></li>\n<li>RCS Asset Accounting Multiple Calendar Tool (component MTO 100, SAP Note 2132260)</li>\n<li>Regulatory reporting 3.0 by iBS (component RRA 300_BA70, SAP Note 2244901)</li>\n<li>SAP Access Approver 2.0.0 (component LWMGRC01 100, SAP Note 2662186)</li>\n<li>SAP ACCESS CONTROL 10.1/SAP Process CONTROL 10.1 (component GRCPIERP V1100_700, SAP Note 2456300, components GRCPINW V1100_731 &amp; 710 &amp; 700, SAP Note 2453593, components GRCFND_A V800 &amp; V1100, SAP Note 2463636)</li>\n<li>SAP ACCESS CONTROL 10.0/SAP Process CONTROL 10.0 (component GRCPIERP V1000_700, SAP Note 2456300, components GRCPINW V1000_700 &amp; V1000_731, SAP Note 2453593, component GRCFND_A V800, SAP Note 2463636)</li>\n<li>SAP Add-On Assembly Kit 4.0 (component AOFTOOLS 400_*, SAP Note 921103)</li>\n<li>SAP Add-On Assembly Kit 5.0 (component AOFTOOLS 500_7*, SAP Note 2179441)</li>\n<li>SAP Advanced Track and Trace for Pharmaceuticals 1.0 (component STTP 100, SAP Note 2441827)</li>\n<li>SAP AR Warehouse Picker 1.0, QR code generator (component NWMQC01 100, SAP Note 2202923)</li>\n<li>SAP Citizen Connect 1.0.0 (component LWMCR002 700, SAP Note 2206380)</li>\n<li>SAP Cloud for Customer 1208 integration with SAP ERP (component CODEXTCT 600, SAP Note 2373504)</li>\n<li>SAP Cloud for Customer 4.0, integration with SAP ERP (component CODEXTCT 600, SAP Note 2373504 and NWSEXTFW 600, SAP Note 2228009)</li>\n<li>SAP Cloud for Travel and Expense integration 4.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\n<li>SAP Cloud for Travel and Expense integration 4.0 (components TEMODFI 600, OTM_EXTR 600, ODTHCMER 600, ODTGEN 600, ODTFINCO 600, ODTFINCC 600, and DCFLPROC 600, SAP Note 2227939)</li>\n<li>SAP Cloud for Travel and Expense integration 5.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\n<li>SAP Cloud for Travel and Expense integration 5.0 (components TEMODFI 600, OTM_EXTR 600, ODTHCMER 600, ODTGEN 600, ODTFINCO 600, ODTFINCC 600, and DCFLPROC 600, SAP Note 2227939)</li>\n<li>SAP Configure, Price, and Quote for Solution Sales Configuration 2.0 (component SLCE 607, SAP Note 2437183)</li>\n<li>SAP Cross-Channel Order Management for Retail 2.0 (component WOM 200, SAP Note 2529018)</li>\n<li>SAP Customer Financial Fact Sheet 2.0.0 (component LWMFI001 600, SAP Note 2373183)</li>\n<li>SAP Deduction Management Component 6.0 (Component GSCDMC 600, SAP Note 2189971)</li>\n<li>SAP DMP 2.0 (component FMFMS 604, SAP Note 2319813)</li>\n<li>SAP DMP 2.0 (component PSFMS 600, SAP Note 2319803)</li>\n<li>SAP EHP1 for SAP NetWeaver Mobile 7.3 (component SUPDOE 731, SAP Note 2420945)</li>\n<li>SAP EHP 4 for SAP ERP 6.0, localization for Russia for public sector accounting (components GSEAFS 100 &amp; 604, SAP Note 2410217)</li>\n<li>SAP EHS Management, Add-In for Genifix 2.2 (component TDAGGF 220_600, SAP Note 2307907)</li>\n<li>SAP Employee Lookup 2.0.0 (component MIVHREMP 601, SAP Note 2356000)</li>\n<li>SAP ERP Add-On for MULTICHANNEL UTILITIES/PS 1.0 (component UMCERP01 604, SAP Note 2461782)</li>\n<li>SAP ERP add-on for shop floor dispatching and monitoring tool 1.0 (component SFDM_ABAP 100, SAP Note 2261544)</li>\n<li>SAP ERP Order Status 600 (component LWMSD001 600, SAP Note 2333985)</li>\n<li>SAP ERP Quality Issue 1.0.0 (component LWMQAMMI 600, SAP Note 2318477)</li>\n<li>SAP EWM 9.3 (components SCM_EXT 713 &amp; SCMB_EXT 713, SAP Note 2403053)</li>\n<li>SAP EWM 9.3/9.4 (component SCM_BASIS 713/714, SAP Note 2403053)</li>\n<li>SAP EWM 9.4 (components SCMEWMUI 940 &amp; SCMEWM 940, SAP Note 2360806)</li>\n<li>SAP Extended Warehouse Management 9.3 (components SCMEWM UI 930 &amp; SCMEWM 930, SAP Note 2360806, component SCM_BASIS 713 &amp; 714, SAP Note 2403053, component SCM_EXT 713, SAP Note 2403053, component QIE 200, SAP Note 2403694)</li>\n<li>SAP Fiori App Implementation Foundation 1.0 (component SAPUIFT 100, SAP Note 2622613)</li>\n<li>SAP Fiori 1.0 for SAP Demand Signal Management (component UIDSM001 100, SAP Note 2409404)</li>\n<li>SAP Fiori Front-End Server 2.0 (component UIBAS001 100, SAP Note 2256000)</li>\n<li>SAP Fiori 1.0 for SAP Business Suite foundation component (component UIFND001 100, SAP Note 2217323)</li>\n<li>SAP Fiori 1.0 for SAP Commercial Project Management (component CPD001 100, SAP Note 2217279)</li>\n<li>SAP Fiori 1.0 for SAP Customer Activity Repository retail applications bundle (components UIRAP001 100 and UISCAR01, SAP Note 2217230)</li>\n<li>SAP Fiori 1.0 for SAP Demand Signal Management (components UIDDF001 100 and UIDDF0011 100, SAP Note 2409404)</li>\n<li>SAP Fiori 1.0 for SAP EHS Management (component UIEHSM01 100, SAP Note 2203691)</li>\n<li>SAP Fiori 1.0 for SAP ERP HCM (component UIHR001 100, SAP Note 2167372)</li>\n<li>SAP Fiori 1.0 for SAP Event Management (component UIEM001 100, SAP Note 2209418)</li>\n<li>SAP Fiori 1.0 for SAP HANA Live for SAP Advanced Planning and Optimization (component UIHSCM01 100, SAP Note 2176724)</li>\n<li>SAP Fiori 1.0 for SAP HANA Live for SAP solutions for GRC (component UIHGRC01 100, SAP Note 2200172)</li>\n<li>SAP Fiori 1.0 for SAP hybris Marketing (component UICUAN 100, SAP Note 2200656)</li>\n<li>SAP Fiori 1.0 for SAP Information Lifecycle Management (component UIILM001 100, SAP Note 2195035)</li>\n<li>SAP Fiori 1.0 for SAP Master Data Governance (component UIMDG001 100, SAP Note 2209288)</li>\n<li>SAP Fiori 1.0 for SAP Portfolio and Project Management (component UIPPM001 100, SAP Note 2176725)</li>\n<li>SAP Fiori 1.0 for SAP solutions for GRC (component UIGRC001 100, SAP Note 2176696)</li>\n<li>SAP Fiori 1.0 for SAP SRM (component UISRM200 100, SAP Note 2176792)</li>\n<li>SAP Fiori 1.0 for the SAP Simple Finance add-on for SAP Business Suite powered by SAP HANA (component UIFSCM70 100, SAP Note 2144806, und component UIAPFI70 100, SAP Note 2144806)</li>\n<li>SAP Fiori 2.0 for SAP Customer Activity Repository retail applications bundle (component UICAR001 100, SAP Note 2433703)</li>\n<li>SAP Fiori for request approvals 1.0 (component UIX01CA1 100, SAP Note 2217255)</li>\n<li>SAP Fiori for SAP DMIS 1.0 (component UICSLO01 100, SAP Note 2209387)</li>\n<li>SAP Fiori for SAP ERP 1.0 (components UIEAAP01 100, UIEAPS01 100, UIFICA01 100, UIGLT001 100, UIISPSCA 100, UIRT401 100, SAP Note 2134432)</li>\n<li>SAP Fiori for SAP ERP HCM 1.0 (components GBX01HR 600 and GBX01HRS5 605, SAP Note 2180598)</li>\n<li>SAP Fiori for SAP S/4HANA 1709 (component UIS4HOP1 300, SAP Note 2408541)</li>\n<li>SAP Fiori oData component for Approve Purchase Orders 1.0 (component GBAPP002 600, SAP Note 2131368)</li>\n<li>SAP Fiori oData component for Approve Purchase Contracts 1.0 (component SRA001 600, SAP Note 2128051)</li>\n<li>SAP Fiori oData component for Approve Timesheets 1.0 (component SRA010 600, SAP Note 2131301)</li>\n<li>SAP Fiori oData component for Approve Travel Expenses 1.0 (component SRA008 600, SAP Note 2131274)</li>\n<li>SAP Fiori oData component for Approve Travel Requests 1.0 (component SRA009 600, SAP Note 2131278)</li>\n<li>SAP Fiori oData component for Change Sales Orders 1.0 (component SRA003 600, SAP Note 2131814)</li>\n<li>SAP Fiori oData component for Check Price and Availability 1.0 (component SRA016 600, SAP Note 2131351)</li>\n<li>SAP Fiori oData component for Create Sales Orders 1.0 (component SRA017 600, SAP Note 2131352)</li>\n<li>SAP Fiori oData component for Customer Invoices 1.0 (component SRA021 600, SAP Note 2131364)</li>\n<li>SAP Fiori oData component for My Benefits 1.0 (component SRA007 600, SAP Note 2131187)</li>\n<li>SAP Fiori oData component for My Paystubs 1.0 (component SRA006 600, SAP Note 2131186)</li>\n<li>SAP Fiori oData component for My Spend 1.0 (component SRA012 600, SAP Note 2131303)</li>\n<li>SAP Fiori oData component for My Timesheet 1.0 (component SRA002 600, SAP Note 2131147)</li>\n<li>SAP Fiori oData component for My Travel Requests 1.0 (component SRA004 600, SAP Note 2131183)</li>\n<li>SAP Fiori oData component for Order from Requisitions 1.0 (component SRA013 600, SAP Note 2131310)</li>\n<li>SAP Fiori oData component for Track Purchase Orders 1.0 (component SRA020 600, SAP Note 2131360)</li>\n<li>SAP Fiori oData component for Track Sales Orders 1.0 (component SRA018 600, SAP Note 2131353)</li>\n<li>SAP Fiori oData component for Track Shipments 1.0 (component SRA019 600, SAP Note 2131358)</li>\n<li>SAP Fiori principal apps 1.0 for SAP SRM (component UIX01SRM 100, SAP Note 2217260)</li>\n<li>SAP Fiori principal apps for SAP ERP 1.0 - Central App (component UIX01EAP 100, SAP Note 2167334)</li>\n<li>SAP Fiori principal apps for SAP ERP 1.0 - HCM (component UIX01HCM 100, SAP Note 2091515, component GBHCM002 600, SAP Note 2131381, component GBHCM003, SAP Note 2131383)</li>\n<li>SAP Fiori principal apps for SAP ERP 1.0 - Travel (component UIX01TRV 100, SAP Note 2167334)</li>\n<li>SAP Fiori Travel Expense Approval 2.0 (component GBTRV002 600, SAP Note 2124793)</li>\n<li>SAP Fiori UI SAP ANALYTICAL SERVICES 1.0 (component UISSB001 100, SAP Note 2266130)</li>\n<li>SAP Fiori UI Approve Leave Requests 1.0 (component UIHCM003 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Purchase Contracts 1.0 (component UISRA001 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Purchase Orders 1.0 (component UIAPP002 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Requests 1.0 (component UIGIB001 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Requisitions 1.0 (component UIAPP001 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Shopping Carts 1.0 (component UISRM001 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Timesheets 1.0 (component UISRA010 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Travel Expenses 1.0 (component UISRA008 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Travel Requests 1.0 (component UISRA009 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Change Sales Orders 1.0 (component UISRA003 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Check Price and Availability 1.0 (component UISRA016 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Create Sales Orders 1.0 (component UISRA017 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Customer Invoices 1.0 (component UISRA021 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI for SAP ERP, add-on for embedded production planning and detailed scheduling 1.0 (component UIPPDS01 100, SAP Note 2280928)</li>\n<li>\n<div>SAP Fiori for SAP Fashion Management 1.0 (component UIFM001 100, SAP Note 2229796)</div>\n</li>\n<li>SAP Fiori UI for SAP S/4HANA Finance 1605 (component UIAPPL01 100, SAP Note 2280900)</li>\n<li>SAP Fiori UI for SAP Master Data Governance 1.0 (component <span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">UIMDC001 100</span>, SAP Note <span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">2230429</span>)</li>\n<li>SAP Fiori UI for SAP SCM 1.0 (component SCMB_UI 100, SAP Note 2203656)</li>\n<li>SAP Fiori UI for SAP Simple Finance On-Premise Edition 1503 (component UIAPPFI702 600, SAP Note 2238575)</li>\n<li>SAP Fiori UI for SAP Supply Network Collaboration 1.0 (component SCMSNCE1 100, SAP Note 2176346)</li>\n<li>SAP Fiori UI for SAP S/4HANA (component UIX01CA1 200, SAP Note 2280715)</li>\n<li>SAP Fiori UI My Benefits 1.0 (component UISRA007 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Leave Requests 1.0 (component UIHCM002 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Paystubs 1.0 (component UISRA006 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Shopping Cart 1.0 (component UISRA014 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Spend 1.0 (component UISRA012 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Timesheet 1.0 (component UISRA002 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Travel Requests 1.0 (component UISRA004 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Order from Requisitions 1.0 (component UISRA013 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Track Purchase Orders 1.0 (component UISRA020 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Track Sales Orders 1.0 (component UISRA018 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Track Shipments 1.0 (component UISRA019 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Track Shopping Carts 1.0 (component UISRA011 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI SAP ANALYTICAL SERVICES 1.0 (component UISSB001 100, SAP Note 2266130)</li>\n<li>SAP Fiori transactional apps 1.0 for SAP CRM (components UIX02CRM 100 and UICRM001 100, SAP Note 2189023)</li>\n<li>SAP Fiori transactional apps for SAP ERP 1.0 (component UIX02EA4 100, SAP Note 2125074, component UIX02EAP 100, SAP Note 2125074, and component UIX02RT4 100, SAP Note 2125074)</li>\n<li>SAP Fiori/Hana Live Content for ERP 100 (component UIHERP01 100, SAP Note 2131580)</li>\n<li>SAP Fiori/Hana Live Content for Sentiment Analysis 100 (component UIHFND01 100, SAP Note 2132758)</li>\n<li>SAP Funding Management 3.0 (component 300, SAP Note 2198972)</li>\n<li>SAP GATEWAY 2.0 (components IW_CBS 200 &amp; IW_CNT 200, SAP Note 2312680, component IW_FNDGC 100, SAP Note 2319096, component IW_GIL, SAP Note <span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\">2319114</span>, component IW_SPI 100, SAP Note 2313222 &amp; component IW_SCS 200, SAP Note 2319097, component WEBCUIF 746 &amp; 747 &amp; 748, SAP Note 2417905)</li>\n<li>SAP Global Batch Trace 1.0 ERP Integration (component GBTRINT 100, SAP Note 2149065)</li>\n<li>SAP Global Trade Services (GTS) 10.0 (component SLL-LEG 900, SAP Note 2356692)</li>\n<li>SAP Global Trade Services (GTS) 10.1 (component SLL-LEG 901, SAP Note 2356692)</li>\n<li>SAP Global Trade Services (GTS) 7.0 (component SLL-LEG 7.00, SAP Note 2356692)</li>\n<li>SAP Global Trade Services (GTS) 7.1 (component SLL-LEG 710, SAP Note 2356692)</li>\n<li>SAP Global Trade Services (GTS) 7.2 (component SLL-LEG 720, SAP Note 2356692)</li>\n<li>SAP Global Trade Services (GTS) 8.0 (component SLL-LEG 800, SAP Note 2356692)</li>\n<li>SAP Global Trade Services, identity-based preference processing 1.0 (add-on IBPP_100, SAP Note 2795075, add-on IBPP_PI 100, SAP Note 2832367)</li>\n<li>SAP Global Trade Services, identity-based preference processing 2.0 (add-on IBPP_200, SAP Note 2795075, add-on IBPP_PI 200 and IBPP_PI 800, SAP Note 2832367)</li>\n<li>SAP GRC Access Control 5.3 (components VIRSANH 530_700 &amp; 530_731, SAP Note 2536230)</li>\n<li>SAP GTS 11.0 (component SLL-LEG V1100, SAP Note 2356692)</li>\n<li>SAP In-Store Product Lookup 1.0.0 (component LWMRT401 604, SAP Note 2209914)</li>\n<li>SAP Manager Insight 1.0.0 (component LWMHR401 604, SAP Note 2611360)</li>\n<li>SAP Management of Change 1.0 (component MOC 100, SAP Note 2355120)</li>\n<li>SAP MOB FIELD SERV ERP INT 2.0.0 (component MCRMFERP 200, SAP Note 2370516)</li>\n<li>SAP Multichannel Foundation for Utilities and Public Sector 1.0 (component UMCUI501 100, SAP Note 2217321)</li>\n<li>SAP Multiresource Scheduling 9.0 (components MRSS 900 and MRSS_NW 900, SAP Note 2169600)</li>\n<li>SAP NETWEAVER 7.5 (component BW4HANA 100, SAP Note 2246699)</li>\n<li>SAP NetWeaver Master Data Management 7.1 (component MDM_TECH 710_700, SAP Note 2342708)</li>\n<li>SAP Payment Approvals 2.0.0 (component LWMFI401 604, SAP Note 2571634)</li>\n<li>SAP PC 10.1 For SAP NW (component POASBC 100_731, SAP Note 2322477)</li>\n<li>SAP ProductivityPak by RWD adapter 1.0 for SAP Solution Manager 7.0 (component ST-SPA, SAP Note 1109650)</li>\n<li>SAP QIM 1.0 (component QAM 100, SAP Note 2357898)</li>\n<li>SAP REACH COMPLIANCE 2.0 (component TDAGBCA 200_600, SAP Note 2298456)</li>\n<li>SAP RealSpend 1.0 (component LWMSIM01 600, SAP Note 2234715)</li>\n<li><span 107%;=\"\" ar-sa;=\"\" arial;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;\"=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" verdana',sans-serif;=\"\">SAP Retail Execution integration with SAP CRM (component MOB_CRM 100, SAP Note 2324260)</span></li>\n<li>SAP Screen Personas 1.0 (component PERSOS 100, SAP Note 2226123)</li>\n<li>SAP Screen Personas 2.0 (component PERSOS 200, SAP Note 2226123)</li>\n<li>SAP Screen Personas 3.0 (component PERSONAS 300, SAP Note 2246593)</li>\n<li>SAP Solution Manager adapter for SAP Quality Center 1.0 by HP (component ST-QCA, SAP Note 1109650)</li>\n<li>SAP Smart Business 1.0 for retail promotion execution (component UISRTL01 100, SAP Note 2201852)</li>\n<li>SAP Smart Business 1.0 for SAP CRM (component UISCRM01 100, SAP Note 2176772)</li>\n<li>SAP Smart Business 1.0 for SAP ERP (component UISERP01 100, SAP Note 2176775)</li>\n<li>SAP Smart Business 1.0 for SAP Fashion Management (component UISFM001 100, SAP Note 2201905)</li>\n<li>SAP Smart Business 1.0 for SAP Information Lifecycle Management (component UISDTG01 100, SAP Note 2201850)</li>\n<li>SAP Smart Business 1.0 for SAP PLM (component UIHPLM01 100, SAP Note 2201843)</li>\n<li>SAP Smart Business 1.0 foundation component (component UISAFND1 100, SAP Note 2201856)</li>\n<li>SAP Smart Business 1.0, component for KPI modeling (component UISKPI01 100, SAP Note 2176779)</li>\n<li>SAP Smart Business for the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (component UIHSFIN01 100, SAP Note 2201846)</li>\n<li>SAP Supplier Lifecycle Management 1.0 (component SMCERPIT 100, SAP Note 2192644)</li>\n<li>SAP Supplier Lifecycle Management 2.0 (component SMCERPIT 100, SAP Note 2192644)</li>\n<li>SAP Supplier Lifecycle Management 3.0 (component SMCERPIT 100, SAP Note 2192644)</li>\n<li>SAP Travel Expense Report 1.0.0 (component GBTRV001 600, SAP Note 2162719)</li>\n<li>SAP Travel Receipt Capture 2.0.0 (component MIVHRTRV 601, SAP Note 2195588)</li>\n<li>SAP Travel OnDemand integration 1.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\n<li>SAP Travel OnDemand integration 1.0 (components TEMODFI 600 and DCFLPROC 600, SAP Note 2227939)</li>\n<li>SAP Travel OnDemand integration 3.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\n<li>SAP Travel OnDemand integration 3.0 (components TEMODFI 600, ODTFINCO 600, and DCFLPROC 600, SAP Note 2227939)</li>\n<li>SAP Workforce Deployment for Retail and Wholesale Distribution 1.0 (component WFMCORE 200, SAP Note 2409846, component LCAPPS 2005_700, SAP Note 2424906)</li>\n<li>SAP Utilities Customer Engagement 1.0.0, 2.1.0 &amp; 2.1.0, cloud edition (component MUTILCRM 100, SAP Note 2388251)</li>\n<li>SRM Shopping Cart Approval 700 (component GBSRM001 700, SAP Note: 2362137)</li>\n<li>SuccessFactors Employee Central Payroll 1.0 (component Cloud Pay 100, SAP Note 2457573)</li>\n<li>SuccessFactors Integration Add-on 1.0 (component SFIHCM01 600, SAP Note: 2375289)</li>\n<li>SuccessFactors Integration Add-on 2.0 (component SFIHCM02 600, SAP Note: 2375320)</li>\n<li>SuccessFactors Integration Add-on 3.0 (component SFIHCM03 600, SAP Note: 2276816)</li>\n<li>Web interface for SAP EHS Management 2.6 (component TDAGWI 260_600, SAP Note 2307624)</li>\n</ul>\n<p><strong>More information:</strong></p>\n<p>For detailed information about the uninstall process, see transaction SAINT in your ABAP system.</p>", "noteVersion": 60}]}], "activities": [{"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "The following Smart Business analytical and transactional apps are not immediately available with SAP Commercial Project Management for SAP S/4HANA: (1) F0709 My Commercial Projects, (2) F0710 Action Items in Commercial Projects, (3) F0779 Status Management in Commercial Projects, (4) F0784 Overview of Commercial Projects, (5) F0786 Costs and Revenues of Commercial Projects, (6) F0787 Analysis of Commercial Projects, (7) BuInformation Related to Business Processes. Please, see also SAP Note 2320143 an inform your business user."}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Implement replacements for obsolete Fiori UI"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Inform business users about change Fiori apps."}]}