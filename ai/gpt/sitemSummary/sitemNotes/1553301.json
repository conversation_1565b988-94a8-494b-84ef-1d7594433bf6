{"Request": {"Number": "1553301", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 739, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001553301?language=E&token=37DC35A5FE42E951EA27E192C2151049"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001553301", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001553301/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1553301"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "31.03.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST"}, "SAPComponentKeyText": {"_label": "Component", "value": "Client/Server Technology"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1553301 - 7.20 <PERSON><PERSON> Kernel - Usage"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to use the SAP 7.20 EXT kernel in your SAP system environment as an alternative to the SAP 7.20 kernel.<br />This SAP Note describes how to deploy the 7.20 EXT kernel in your SAP system that is currently using the standard 7.20 kernel by default.<br />If you intend to use the 7.20 EXT kernel as a downward compatible kernel you can find more details in SAP note 1636252.</p>\r\n<p><strong>Remark: The maintenance of SAP kernel release 7.20 ended in Q1/2015. After this period, SAP kernel release 7.21 will replace SAP kernel release 7.20 and will become the standard SAP kernel release, for which corrections will be supplied.</strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>EXT, kernel, EXT kernel, 7.02, 7.20, 7.30, maintenance, 720_EXT, AKK, DCK, Downward Compatible Kernel</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>For general information about the SAP 7.20 EXT kernel, see SAP Note 1553300.<br /><br />The usage of the 7.20 EXT kernel as an alternative to the 7.20 kernel is supported for all SAP NetWeaver releases which are supported with the 7.20 kernel. The following NetWeaver releases are supported with 7.20 and 7.20 EXT kernel:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.0</li>\r\n</ul>\r\n<ul>\r\n<li>SAP EhP1 for SAP NetWeaver 7.0</li>\r\n</ul>\r\n<ul>\r\n<li>SAP EhP2 for SAP NetWeaver 7.0</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>SAP EhP1 for SAP NetWeaver 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver CE 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>SAP EhP1 for SAP NetWeaver CE 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.2</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.3</li>\r\n</ul>\r\n<p><br />In addition the following NetWeaver releases are supported with the 7.20 EXT kernel:</p>\r\n<ul>\r\n<li>SAP EhP3 for SAP NetWeaver 7.0</li>\r\n</ul>\r\n<ul>\r\n<li>SAP EhP1 for SAP NetWeaver 7.3</li>\r\n</ul>\r\n<p><br />You can find more information about the 7.20 downward compatible kernel in SAP note 1629598 and SAP note 1636252.</p>\r\n<p><strong>Attention</strong></p>\r\n<ul>\r\n<li>To use the 7.20 EXT kernel in your SAP systems, you might have to upgrade the operating system and/or the database release used for the operation of your SAP system. To check if your operating system and/or database release is supported with the 7.20 EXT kernel, see the Product Availability Matrix (PAM) on SAP Service Marketplace at<br />https://service.sap.com/pam.</li>\r\n</ul>\r\n<ul>\r\n<li>The 7.20 EXT kernel is only supported and available in a 64-bit version.</li>\r\n</ul>\r\n<ul>\r\n<li>If you are installing a new SAP system based on SAP NetWeaver 7.0 or higher or if you are creating a system copy of an existing SAP system which is based on SAP NetWeaver 7.0 or higher you have to use the Software Provision Manager (SWPM). The SWPM supports the usage of the 7.20 EXT kernel. For details please see SAP note 1680045.</li>\r\n</ul>\r\n<ul>\r\n<li>When you implement the 7.20 EXT kernel, you must replace the kernels of all application servers of the affected system.</li>\r\n</ul>\r\n<ul>\r\n<li>When you implement the 7.20 EXT kernel, you also have to exchange the Internet Graphics Server (IGS) by SAP IGS 7.20 EXT.</li>\r\n</ul>\r\n<ul>\r\n<li>After updating the system with the new 7.20 EXT kernel, you do not need to update SAP GUI. The installed SAP GUI also works with the 7.20 EXT kernel.</li>\r\n</ul>\r\n<ul>\r\n<li>The 7.20 EXT kernel is a \"binary only\" release. This means that customers can implement the newer kernel without upgrading the SAP system.</li>\r\n</ul>\r\n<ul>\r\n<li>After installing the 7.20 EXT kernel, customers must continue to import the support packages for the installed SAP release when updating or implementing corrections to the repository objects.</li>\r\n</ul>\r\n<ul>\r\n<li>After installing the 7.20 EXT kernel, customers must use only 7.20 EXT patches when implementing corrections to the kernel.</li>\r\n</ul>\r\n<ul>\r\n<li>The database Sybase ASE for Business Suite is only supported with the 7.20 EXT kernel. For the NetWeaver releases supported with Sybase ASE in general check the product availability matrix (PAM).</li>\r\n</ul>\r\n<p><strong>Platform-Specific Information</strong></p>\r\n<ul>\r\n<li>Microsoft Windows<br />The 7.20 EXT kernel on Windows requires a specific C runtime that is included in the SAPEXE.SAR archive. SAP Note 1553465 describes how to apply the C runtime to your system.</li>\r\n</ul>\r\n<ul>\r\n<li>AIX<br />Please see SAP Note 1780629 for the minimal OS release and C/C++ runtime requirements.</li>\r\n</ul>\r\n<ul>\r\n<li>IBM i<br />No particular activities are required.</li>\r\n</ul>\r\n<ul>\r\n<li>Linux<br />Before the installation of the 7.20 EXT kernel, see SAP Note 1563102.</li>\r\n</ul>\r\n<ul>\r\n<li>Oracle Database<br />On IBM Power Linux and on Linux IA64 (Intel Itanium), only Oracle 10g is supported. For these two platforms you do not have to make any changes to your database client.<br /><br />On all other platforms, make sure that the following prerequisites are met before the installation of the 7.20 EXT kernel:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The database server runs with Oracle 11.2.0.2 or higher. The latest SAP Bundle Patch needs to be applied on top of the existing patchset release.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Install the Oracle 11g Instant Client on all application servers and the database server as described in SAP Note 1431794: Oracle 11.2.0 Instant Client.<br />You can download the Oracle client software from SAP Service Marketplace at: https://service.sap.com/oracle-download<br />IMPORTANT: After the installation of the Oracle 11g Instant Client, make sure that the library path for the &lt;sid&gt;adm user (LIBPATH, SHLIB_PATH, LD_LIBRARY_PATH) no longer contains any references to the old Oracle 10g Instant Client location.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Other platforms<br />No specific information besides the Platform Availability Matrix (PAM) has to be taken into account.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Content</strong><br /><strong>1. Manual Exchange of the 7.20 EXT Kernel in Existing SAP Systems</strong><br /><strong>2. Applying the 7.20 EXT Kernel During Upgrade and Update of SAP Systems</strong><br /><strong>3. Applying the 7.20 EXT Kernel Within SP Stacks</strong><br /><strong>4. Updating SAP NetWeaver CE Using the Update Management Service</strong></p>\r\n<p><strong>1. Manual Exchange of the 7.20 EXT Kernel in Existing SAP Systems</strong></p>\r\n<p>The 7.20 EXT kernel is supported only on recent versions of the database systems and the operating systems. Refer to the first part of this Note and make sure that your computing environment fullfills all requirements.</p>\r\n<p><strong>Obtaining the 7.20 EXT Kernel</strong></p>\r\n<p>Download the latest 7.20 EXT kernel stack for your operating system and database from SAP Service Marketplace.<br />For Windows and UNIX/Linux platforms and IBM i, the 7.20 EXT kernel stack consists of the database-independent SAPEXE.SAR and the database-dependent SAPEXEDB.SAR archives.<br /><br />Download the latest support package for the IGS from the SAP IGS 7.20 EXT branch. The package constists of the IGSEXE.SAR and the IGSHELPER.SAR archives.<br /><br />If you are using SAPCRYPTOLIB, download the EXT version of the corresponding archive in accordance with SAP Note 397175.<br /><br />For Oracle, you also need the archive DBATOOLS.SAR.</p>\r\n<p><strong>Replacing the 7.20 Standard Kernel by the 7.20 EXT Kernel for an ABAP-Only Stack and for an ABAP/Java Dual Stack</strong></p>\r\n<p>If no explicit other statement is given, the following tasks have to be performed as user &lt;SAPSID&gt;adm. The kernel has to be exchanged in the shared directory of the first installation host. The program sapcpe automatically copies all required files to the local directories during the next start of the SAP system.</p>\r\n<p><strong>Preliminary Tasks</strong></p>\r\n<ol>1. Stop the SAP system (there is no need to stop the database).</ol><ol>2. Terminate the SAProuter.&lt;DVEBMGS00&gt;/data/stat).</ol>\r\n<p><strong>Deploying the Kernel</strong><br /><strong>A. On UNIX / Linux Platforms</strong></p>\r\n<ol>1. Log on to your first installation host as user &lt;SID&gt;adm.</ol><ol>2. Copy the kernel archives SAPEXE.SAR and SAPEXEDB.SAR and the IGS archives IGSEXE.SAR and IGSHELPER.SAR to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, /tmp).</ol><ol>3. If you are using the Oracle database, copy the archive DBATOOLS.SAR to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, /tmp).</ol><ol>4. Switch to one of the following kernel directories:</ol>\r\n<ul>\r\n<ul>\r\n<li>/usr/sap/&lt;SAPSID&gt;/SYS/exe/run<br />on systems based on EhP 2 for SAP NetWeaver 7.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>/usr/sap/&lt;SAPSID&gt;/SYS/exe/&lt;codepage&gt;/&lt;platform&gt;<br />on systems based on SAP NetWeaver 7.30</li>\r\n</ul>\r\n</ul>\r\n<ol><ol>5. We recommend to save the old kernel before deploying the EXT kernel.</ol></ol><ol><ol>Save the old kernel by creating a tar archive of the complete kernel directory using the following command:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol>tar -cvf ../sapexe.tar</ol><ol>6. Save the SAPCAR program to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, /tmp).</ol><ol>7. Switch to the user root and change the owner of all files to &lt;SAPSID&gt;adm using one of the following commands:</ol>\r\n<ul>\r\n<ul>\r\n<li>su - root<br />chown &lt;SAPSID&gt;adm /usr/sap/&lt;SAPSID&gt;/exe/run/*<br />exit<br />on systems based on EhP 2 for SAP NetWeaver 7.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>su - root<br />chown &lt;SAPSID&gt;adm /usr/sap/&lt;SAPSID&gt;/SYS/exe/&lt;codepage&gt;/&lt;platform&gt;<br />exit<br />on systems based on SAP NetWeaver 7.30<br /><br />Caution: With the OS/390 operating system, you have to use the command \"su &lt;user&gt;\" instead of \"su - root\", where &lt;user&gt; must have the UID 0.</li>\r\n</ul>\r\n</ul>\r\n<ol><ol>8. Unpack the new kernel using the following commands:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&lt;TEMPDIR&gt;/SAPCAR -xvf &lt;TEMPDIR&gt;/SAPEXE.SAR</ol></ol><ol>&lt;TEMPDIR&gt;/SAPCAR -xvf &lt;TEMPDIR&gt;/SAPEXEDB.SAR</ol><ol><ol>9. If you are using the Oracle database, unpack the archive DBATOOLS.SAR using the following command:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol>&lt;TEMPDIR&gt;/SAPCAR -xvf &lt;TEMPDIR&gt;/DBATOOLS.SAR</ol><ol><ol>10. Unpack the new IGS using the following commands:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&lt;TEMPDIR&gt;/SAPCAR -xvf &lt;TEMPDIR&gt;/IGSEXE.SAR</ol></ol><ol>&lt;TEMPDIR&gt;/SAPCAR -xvf &lt;TEMPDIR&gt;/IGSHELPER.SAR</ol><ol>11. Switch to the user root and run the shell script saproot.sh, which is available in the kernel directory.</ol>\r\n<p><br />In addition to the deployment of the IGSHELPER.SAR archive in the installation directory as described in the previous section, you have to deploy the IGSHELPER.SAR archive additionally in the instance directory of the primary application server instance and for every additional application server instance.<br /><br />For the primary application server instance and every application server instance proceed as follows:</p>\r\n<ol>1. Logon to your host as user &lt;SID&gt;adm</ol><ol>2. Copy the IGS archive IGSHELPER.SAR to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, /tmp).</ol><ol><ol>3. Switch to one of the following instance directories:</ol></ol>\r\n<ul>\r\n<ul>\r\n<li>/usr/sap/&lt;SAPSID&gt;/DVEBMGS<br />on the primary application server instance</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>/usr/sap/&lt;SAPSID&gt;/D<br />on the additional application server instance</li>\r\n</ul>\r\n</ul>\r\n<ol><ol>4. Unpack the IGSHERLPER.SAR archive using the following command:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol>./exe/SAPCAR -xvf &lt;TEMPDIR&gt;/IGSHELPER.SAR</ol>\r\n<p><strong>B. On Windows</strong></p>\r\n<ol>1. Copy the kernel archives SAPEXE.SAR and SAPEXEDB.SAR and the IGS archives IGSEXE.SAR and IGSHELPER.SAR and the archive for the DBA tools DBATOOLS.SAR (Oracle only) to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, c:\\temp).</ol><ol>2. Determine the value of the SAP profile parameter DIR_CT_RUN according to SAP Note 997848.</ol><ol>3. On the global host, log on as &lt;SAPSID&gt;adm and open a command prompt (cmd.exe) as administrator. Set the working directory to your kernel staging directory DIR_CT_RUN, for example, &lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\[uc|nuc]\\&lt;platform&gt;.</ol><ol><ol>4. Copy sapcar.exe to the parent directory of DIR_CT_RUN</ol></ol><ol>(copy sapcar.exe ..).</ol><ol>5. If you want to keep the old kernel executables, you must use the copy function to create a backup and not the rename function. For more information, see SAP Note 142100, point 18.</ol><ol><ol>6. Unpack the new kernel from the kernel archives stored in the temporary directory in the specified sequence.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>IMPORTANT: Do not call SAPCAR.EXE directly without a path specification. Instead, use the copied version of SAPCAR.EXE in the parent directory of the current kernel directory.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>..\\sapexe.car -xvf &lt;TEMPDIR&gt;\\SAPEXE.SAR</ol></ol><ol><ol>..\\sapexe.car -xvf &lt;TEMPDIR&gt;\\SAPEXEDB.SAR</ol></ol><ol><ol>..\\sapcar.exe -xvf &lt;TEMPDIR&gt;\\DBATOOLS.SAR (Oracle only)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Note:</ol></ol><ol><ol>If your path to &lt;TEMPDIR&gt; contains blanks you need to put the path in double quotes and include a trailing comma:</ol></ol><ol><ol>Example:</ol></ol><ol>..\\sapcar.exe -xvf \"c:\\&lt;directory_with_blanks&gt;\\dbatools.sar,\"</ol><ol><ol>7. Unpack the new IGS from the IGS archives stored in the temporary directory in the specified sequence.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol>..\\sapexe.car -xvf &lt;TEMPDIR&gt;\\IGSEXE.SAR</ol><ol><ol>8. Deploy the IGSHELPER.SAR archive in the instance directory of the primary application server instance and for every additional application server instance.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol>For the primary application server instance and every application server instance proceed as follows:</ol><ol><ol>a) Log on to your host as user &lt;SID&gt;adm.</ol></ol><ol><ol>b) Copy the IGS archive IGSHELPER.SAR to a temporary directory &lt;TEMPDIR&gt;&#160;&#160;of your choice (for example, c:\\temp).</ol></ol><ol><ol>c) Switch to one of the following instance directories:</ol></ol>\r\n<ul>\r\n<ul>\r\n<li>&lt;drive&gt;:\\usr\\&lt;SAPSID&gt;\\DVEBMGS&lt;No&gt;<br />on the primary application server instance</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\D&lt;No&gt;<br />on the additional application server instance</li>\r\n</ul>\r\n</ul>\r\n<ol><ol><ol>d) Unpack the IGSHERLPER.SAR archive using the following command:</ol></ol></ol><ol><ol>sapcar.exe -xvf &lt;TEMPDIR&gt;\\IGSHELPER.SAR</ol></ol><ol>9. Install the current C runtime library by executing vcredist_&lt;platform&gt;.msi in the command box (or by double-clicking this file in the Windows Explorer). Before you start the system for the first time, and if you have a distributed system environment, perform this step manually on each node where a component of the system is configured to run.</ol><ol><ol>10. MSCS only:</ol></ol><ol>Update the files on all cluster nodes in the Windows\\SAPCLUSTER directory with the corresponding files in the central kernel staging directory.</ol>\r\n<p><strong>C. On IBM i</strong></p>\r\n<ol>1. Log on to your first installation host as user &lt;SID&gt;adm.</ol><ol>2. Copy the kernel archives SAPEXE.SAR and SAPEXEDB.SAR and the IGS archives IGSEXE.SAR and IGSHELPER.SAR to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, /tmp).</ol><ol><ol>3. Run the APYSIDKRN command as follows:</ol></ol><ol><ol>&#160;&#160; APYSIDKRN SID(&lt;SAPSID&gt;)</ol></ol><ol><ol>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ARCHIVES('&lt;TEMPDIR&gt;/SAPEXE.SAR'</ol></ol><ol><ol>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;'&lt;TEMPDIR&gt; /SAPEXEDB.SAR'</ol></ol><ol><ol>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;'&lt;TEMPDIR&gt; /IGSEXE.SAR'</ol></ol><ol><ol>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;'&lt;TEMPDIR&gt;/IGSHELPER.SAR')</ol></ol><ol>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;MODE(*FULLY) CARPATH(*BUILTIN)</ol>\r\n<p><strong>D. On all platforms if you are using SAPCRYPTOLIB</strong></p>\r\n<p>Reinstall SAPCRYPTOLIB according to SAP Note 510007, step 1.<br /><br />The installation procedure is also described in the SAP Help Portal, http://help.sap.com. Search Documentation for</p>\r\n<ul>\r\n<li>\"Installing the SAP Cryptographic Library on the AS ABAP\"</li>\r\n</ul>\r\n<ul>\r\n<li>\"Installing the SAP Cryptographic Library on the AS Java\"</li>\r\n</ul>\r\n<p><br />(enter the search string with quotation marks), Product Type \"SAP<br />NetWeaver\", Release \"7.0 EHP2\".</p>\r\n<p><strong>Replacing the 7.20 Standard Kernel by the 7.20 EXT Kernel for a Java-Only Stack</strong></p>\r\n<p>To replace the 7.20 standard kernel with the 7.20 EXT kernel in a Java-only stack, proceed as described in the SAP Knowledge Base article 1505299. Be aware of choosing the correct 7.20 EXT kernel archives for your platform (correct DB/OS combination, 64 bit, Unicode).<br />To replace the IGS archives IGSEXE.SAR and IGSHELPER.SAR, you have to follow the instructions provided in the section \"Replacing the 7.20 Standard Kernel by the 7.20 EXT Kernel for an ABAP-Only Stack and for an ABAP/Java Dual Stack\".<br /><br /></p>\r\n<p><strong>2. Applying the 7.20 EXT Kernel During Upgrade and Update of SAP Systems</strong></p>\r\n<p>Before starting the upgrade or update of your SAP system, make sure that the database and the operating system fulfill the minimum requirements as described in the first section of this Note and in the \"Platform-Specific Information\" section.</p>\r\n<p><strong>Defining the SP Stack for Upgrade or Update Using the SAP Maintenance Optimizer</strong></p>\r\n<p>While creating your SP stack definition, you have to select the appropriate SAP kernel for your SAP system. Instead of selecting the 7.20 standard kernel in the SAP Maintenance Optimizer (MOPZ), you have to select the respective 7.20 EXT kernel for your system landscape. Make sure that you select the correct 7.20 EXT kernel for your platform (correct DB/OS combination, 64 bit, Unicode/Non-Unicode). For IGS, choose SAP IGS 7.20 EXT.</p>\r\n<p><strong>Defining the SP Stack for Upgrade or Update on SAP Service Marketplace</strong></p>\r\n<p>While creating your SP stack definition, you have to select the appropriate SAP kernel for your SAP system. Instead of selecting the 7.20 standard kernel on SAP Service Marketplace, you have to select the respective 7.20 EXT kernel for your system landscape. Make sure that you select the correct 7.20 EXT kernel for your platform (correct DB/OS combination, 64 bit, Unicode/Non-Unicode). For IGS, choose SAP IGS 7.20 EXT.<br /><br />If you upgrade or update an SAP CE system, refer to section 4 of this Note: <strong>Updating SAP NetWeaver CE Using the Update Management Service</strong><br />The update of SAP NetWeaver CE 7.0 to SAP NetWeaver CE 7.2 and EhP 1 for SAP NetWeaver CE 7.0 to SAP NetWeaver CE 7.2 requires to define the SP stack on SAP Service Marketplace as described above.<br /><br /></p>\r\n<p><strong>3. Applying the 7.20 EXT Kernel Within SP Stacks</strong></p>\r\n<p>Before starting the installation of the SAP stack on your SAP system, make sure that the database and the operating system fulfill the minimum requirements as described in the first section of this Note and in the \"Platform-Specific Information\" section.<br />While creating your SP stack definition, you have to select the appropriate SAP kernel for your SAP system. Instead of selecting the 7.20 standard kernel, you have to select the correct 7.20 EXT kernel for your platform (correct DB/OS combination, 64 bit, Unicode/Non-Unicode). This automatically puts the right archives to your download basket. All other steps in the update procedure of your system do not change. For IGS, choose SAP IGS 7.20 EXT.<br /><br /></p>\r\n<p><strong>4. Updating SAP NetWeaver CE Using the Update Management Service</strong></p>\r\n<p>The Update Management Service helps to automate the process of updating the SAP NetWeaver CE system. Using the Update Management Service, you can update your CE system in one of the following ways:</p>\r\n<ul>\r\n<li>Using a locally downloaded Support Package Stack</li>\r\n</ul>\r\n<ul>\r\n<li>Using an SMP Automatic Update</li>\r\n</ul>\r\n<p><br />If you intend to apply the 7.20 EXT kernel to your CE system only, the method by using a locally downloaded Support Package Stack is supported. This applies to the following:</p>\r\n<ul>\r\n<li>System upgrade and system update</li>\r\n</ul>\r\n<ul>\r\n<li>Deployment of an SP stack</li>\r\n</ul>\r\n<ul>\r\n<li>Any other software lifecycle operation if you are already using the 7.20 EXT kernel in your system</li>\r\n</ul>\r\n<p><br />While creating your SP stack definition, you have to select the appropriate SAP kernel for your SAP system. Instead of selecting the 7.20 standard kernel, you have to select the correct 7.20 EXT kernel for your platform (correct DB/OS combination, 64 bit, Unicode). This automatically puts the right archives to your download basket. All other steps in the update procedure of your system remain unchanged. For IGS, choose SAP IGS 7.20 EXT.<br /><br /><strong>The Update Management Service using an SMP Automatic Update is not supported with the 7.20 EXT kernel at all.</strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-OP-HPX (HP-UX)"}, {"Key": "Other Components", "Value": "BC-OP-SUN (SUN Solaris)"}, {"Key": "Other Components", "Value": "BC-DB-DB6 (DB2 Universal Database for Unix / NT)"}, {"Key": "Other Components", "Value": "BC-DB-MSS (SQL Server in SAP NetWeaver Products)"}, {"Key": "Other Components", "Value": "BC-DB-SDB (MaxDB)"}, {"Key": "Other Components", "Value": "BC-OP-AIX (IBM AIX)"}, {"Key": "Other Components", "Value": "BC-DB-SYB (Business Suite on Adaptive Server Enterprise)"}, {"Key": "Other Components", "Value": "BC-OP-AS4 (IBM AS/400)"}, {"Key": "Other Components", "Value": "BC-OP-ZLNX (Linux on IBM Z)"}, {"Key": "Other Components", "Value": "BC-DB-DB4 (DB2 for AS/400)"}, {"Key": "Other Components", "Value": "BC-DB-DB2 (DB2 for z/OS)"}, {"Key": "Other Components", "Value": "BC-DB-ORA (Oracle)"}, {"Key": "Other Components", "Value": "BC-OP-S390 (IBM z/OS)"}, {"Key": "Other Components", "Value": "BC-OP-NT (Windows)"}, {"Key": "Other Components", "Value": "BC-OP-LNX (Linux)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026110)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D002892)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001553301/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001553301/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001553301/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001553301/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001553301/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001553301/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001553301/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001553301/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001553301/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "510007", "RefComponent": "BC-SEC-SSL", "RefTitle": "Additional considerations about setting up SSL on Application Server ABAP", "RefUrl": "/notes/510007"}, {"RefNumber": "397175", "RefComponent": "BC-SEC", "RefTitle": "SAP Cryptographic software - export control", "RefUrl": "/notes/397175"}, {"RefNumber": "1780629", "RefComponent": "BC-OP-AIX", "RefTitle": "AIX: Minimal OS Requirements for SAP Kernel", "RefUrl": "/notes/1780629"}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826"}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986"}, {"RefNumber": "171356", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/171356"}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173"}, {"RefNumber": "1680045", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 (recommended: SWPM 1.0 SP40)", "RefUrl": "/notes/1680045"}, {"RefNumber": "1678024", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: NetWeaver 7.0 EhP 3 with SAP kernel 720", "RefUrl": "/notes/1678024"}, {"RefNumber": "1638356", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Additional information about BR*Tools Version 7.20 EXT", "RefUrl": "/notes/1638356"}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252"}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598"}, {"RefNumber": "1609441", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info about the upgrade to SAP NetWeaver 7.3 EHP1", "RefUrl": "/notes/1609441"}, {"RefNumber": "1597627", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "SAP HANA connection", "RefUrl": "/notes/1597627"}, {"RefNumber": "1591607", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel for SAP EhP 3 for SAP NetWeaver 7.0 and SAP EhP 1 for SAP NetWeaver 7.3", "RefUrl": "/notes/1591607"}, {"RefNumber": "1569874", "RefComponent": "BC-FES-IGS", "RefTitle": "Initial delivery of SAP IGS / SAP IGS Helper 7.20_EXT", "RefUrl": "/notes/1569874"}, {"RefNumber": "1563102", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux Requirements for 7.20 EXT and higher kernel", "RefUrl": "/notes/1563102"}, {"RefNumber": "1553465", "RefComponent": "BC-OP-NT", "RefTitle": "Installation requirements for SAP Kernels on Windows (C++ runtime environment, VCredist versions)", "RefUrl": "/notes/1553465"}, {"RefNumber": "1553300", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1553300"}, {"RefNumber": "1431794", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Instant Client", "RefUrl": "/notes/1431794"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3346502", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 SP38 - covers no longer supported Operating Systems", "RefUrl": "/notes/3346502 "}, {"RefNumber": "3220901", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 SP35 - covers no longer supported CPU and operation system versions", "RefUrl": "/notes/3220901 "}, {"RefNumber": "2505142", "RefComponent": "BC-INS-RMP", "RefTitle": "Release Note for *RMOS*.SAR of Software Provisioning Manager 1.0 - covers no longer supported operation system versions", "RefUrl": "/notes/2505142 "}, {"RefNumber": "2369910", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/2369910 "}, {"RefNumber": "2003265", "RefComponent": "BC-OP-PLNX", "RefTitle": "Install SAP Application Server on IBM PowerLinux for Oracle based 3-tier SAP landscape", "RefUrl": "/notes/2003265 "}, {"RefNumber": "1972803", "RefComponent": "BC-OP-AIX", "RefTitle": "SAP on AIX: Recommendations", "RefUrl": "/notes/1972803 "}, {"RefNumber": "1926209", "RefComponent": "BC-CST", "RefTitle": "Questions on subjects of 7.20/7.21 EXT kernel and C/C++ runtime", "RefUrl": "/notes/1926209 "}, {"RefNumber": "1680045", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 (recommended: SWPM 1.0 SP40)", "RefUrl": "/notes/1680045 "}, {"RefNumber": "1609441", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info about the upgrade to SAP NetWeaver 7.3 EHP1", "RefUrl": "/notes/1609441 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986 "}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173 "}, {"RefNumber": "1716826", "RefComponent": "BC-CST", "RefTitle": "Usage of the downward compatible kernel 721 (EXT)", "RefUrl": "/notes/1716826 "}, {"RefNumber": "1780629", "RefComponent": "BC-OP-AIX", "RefTitle": "AIX: Minimal OS Requirements for SAP Kernel", "RefUrl": "/notes/1780629 "}, {"RefNumber": "1553465", "RefComponent": "BC-OP-NT", "RefTitle": "Installation requirements for SAP Kernels on Windows (C++ runtime environment, VCredist versions)", "RefUrl": "/notes/1553465 "}, {"RefNumber": "1678024", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: NetWeaver 7.0 EhP 3 with SAP kernel 720", "RefUrl": "/notes/1678024 "}, {"RefNumber": "1563102", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux Requirements for 7.20 EXT and higher kernel", "RefUrl": "/notes/1563102 "}, {"RefNumber": "1629598", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel 720 will replace older kernel versions", "RefUrl": "/notes/1629598 "}, {"RefNumber": "1431794", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Instant Client", "RefUrl": "/notes/1431794 "}, {"RefNumber": "1597627", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "SAP HANA connection", "RefUrl": "/notes/1597627 "}, {"RefNumber": "1638356", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Additional information about BR*Tools Version 7.20 EXT", "RefUrl": "/notes/1638356 "}, {"RefNumber": "1569874", "RefComponent": "BC-FES-IGS", "RefTitle": "Initial delivery of SAP IGS / SAP IGS Helper 7.20_EXT", "RefUrl": "/notes/1569874 "}, {"RefNumber": "1591607", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel for SAP EhP 3 for SAP NetWeaver 7.0 and SAP EhP 1 for SAP NetWeaver 7.3", "RefUrl": "/notes/1591607 "}, {"RefNumber": "397175", "RefComponent": "BC-SEC", "RefTitle": "SAP Cryptographic software - export control", "RefUrl": "/notes/397175 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}