{"Request": {"Number": "1040911", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 745, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006237612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001040911?language=E&token=3DC65FAA7C55F37D18B92C92295B2F72"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001040911", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001040911/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1040911"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.08.2007"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1040911 - HBRRAIS0 - 13th Salary advanced for the next year"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The wage type /C00 when customized to Evaluation Class 12, with value 01 is not informed in the field 13th salary advanced.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Relatorio Anual de Informacoes Sociais, 13th Salary, D&#x00E9;cimo Terceiro Sal&#x00E1;rio;Brasil; Brazil;RAIS;2007; Record 2</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>A new value to evaluation class 12 was created, the number is 10, in this case from now on, the wage type /C00 and any other wage type used to pay 13th salary advanced for the next year, should be customized with the value 10 in the evaluation class 12.<br /><br />The program will process one month before of the initial period informed in the selection screen and will consider the payments of 13th salary advanced for the next year.<br /><br /><br /> The support package includes:<br /></p> <UL><LI>Changes in includes HBRRAIS0, PCRAIBR0, PCRAIBRD.</LI></UL> <UL><LI>Changes in type group PBR99.</LI></UL> <UL><LI>New value added in the view cluster VC_T52DE.</LI></UL> <p><br /><br />An Advanced Delivery including changes done in the Data Dictionary is available in the attached files according to the following list(\"xxxxxx\" means numbers):<br /><br />L7DKxxxxxx_600_SYST.CAR - Release ERP 2005<br />L6DKxxxxxx_500_SYST.CAR - Release ERP 2004<br />L6BKxxxxxx_470_SYST.CAR - Release 4.70(Enterprise)<br />L9CKxxxxxx_46C_SYST.CAR - Release 4.6C<br /><br /><br />An Advanced Delivery including changes done in the Customizing is available in the attached files according to the following list(\"xxxxxx\" means numbers):<br /><br />L7DKxxxxxx_600_CUST.CAR - Release ERP 2005<br />L6DKxxxxxx_500_CUST.CAR - Release ERP 2004<br />L6BKxxxxxx_470_CUST.CAR - Release 4.70(Enterprise)<br />L9CKxxxxxx_46C_CUST.CAR - Release 4.6C<br /><br /><br />The changes done in ABAP code are not in the Advanced Delivery they must be installed using the correction instructions.<br /><br /><br /> For more details about Advance Delivery installation procedure please read the notes listed in \"Related Notes\" item.<br /><br /> IMPORTANT:<br /> Be aware of an Advance Delivery delivers the last version of the object, it means that if you do not have the last HR Support Package installed in you system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the code changes manually according to the Correction Instructions available in this note.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I810950)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I812654)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001040911/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001040911/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001040911/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001040911/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001040911/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001040911/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001040911/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001040911/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001040911/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L9CK225016_46C_SYST.CAR", "FileSize": "13", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000059612007&iv_version=0002&iv_guid=2EA1181D0F42474DA7B452CF79D662B0"}, {"FileName": "L6BK135507_470_SYST.CAR", "FileSize": "13", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000059612007&iv_version=0002&iv_guid=3AA43392AAF6D342ADB08430EB330EAA"}, {"FileName": "L6BK135504_470_CUST.CAR", "FileSize": "3", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000059612007&iv_version=0002&iv_guid=3AA2CDCD8CBD504D9846C736B7E51212"}, {"FileName": "L7DK043086_600_CUST.CAR", "FileSize": "4", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000059612007&iv_version=0002&iv_guid=18A876F3691D964CB81B485EDA7D39AE"}, {"FileName": "L9CK225018_46C_CUST.CAR", "FileSize": "2", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000059612007&iv_version=0002&iv_guid=59F4EA814EE56E40BAF04C920493EF23"}, {"FileName": "L7DK043088_600_SYST.CAR", "FileSize": "14", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000059612007&iv_version=0002&iv_guid=54CC96B4E8DE8040AD786CB06D2919AB"}, {"FileName": "L6DK066278_500_CUST.CAR", "FileSize": "3", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000059612007&iv_version=0002&iv_guid=5D2E813699B58249A6F4DDDBA6D468C5"}, {"FileName": "L6DK066279_500_SYST.CAR", "FileSize": "13", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000059612007&iv_version=0002&iv_guid=CA17BCBA3CDDD04BAA5780B08ABC343F"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CC3", "URL": "/supportpackage/SAPKE46CC3"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47068", "URL": "/supportpackage/SAPKE47068"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50034", "URL": "/supportpackage/SAPKE50034"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60017", "URL": "/supportpackage/SAPKE60017"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 4, "URL": "/corrins/0001040911/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 33, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "203897 ", "URL": "/notes/203897 ", "Title": "HBRRAIS - Alterações diversas para emissão RAIS/99", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "327840 ", "URL": "/notes/327840 ", "Title": "HBRRAIS0 - Acertos gerais", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "384995 ", "URL": "/notes/384995 ", "Title": "HBRRAIS0 - Correções para tipo de admissão", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "815438 ", "URL": "/notes/815438 ", "Title": "RAIS absence code", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "500", "Number": "816281 ", "URL": "/notes/816281 ", "Title": "RAIS: incorrect remuneration assignment", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "390938 ", "URL": "/notes/390938 ", "Title": "Neg. WT are displayed as if there were pos. ones", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "438382 ", "URL": "/notes/438382 ", "Title": "HR: Adjustment of 13th Salary in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "494599 ", "URL": "/notes/494599 ", "Title": "HBRRAIS: Complete reviewed RAIS report", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "496862 ", "URL": "/notes/496862 ", "Title": "HBRRAIS: Legal Changes 2002 Aviso Previo not printed", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "498135 ", "URL": "/notes/498135 ", "Title": "HBRRAIS: Missing init for AVISO Previo", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "500161 ", "URL": "/notes/500161 ", "Title": "hbrrais: SALARY under some circumstances zero, CBO not found", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "519162 ", "URL": "/notes/519162 ", "Title": "HBRRAIS: <PERSON><PERSON><PERSON> not disp., only two month processed", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "593233 ", "URL": "/notes/593233 ", "Title": "RAIS - termination and hiring codes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "597039 ", "URL": "/notes/597039 ", "Title": "RAIS - company change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "600559 ", "URL": "/notes/600559 ", "Title": "RAIS - employee transfered and fired during the year", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "600781 ", "URL": "/notes/600781 ", "Title": "HBRRAIS0 - 13th salary & termination", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "706350 ", "URL": "/notes/706350 ", "Title": "HBRRAIS0: Rais 2004 does not print email address", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "778824 ", "URL": "/notes/778824 ", "Title": "HBRRAIS0 Visa document warning", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "816121 ", "URL": "/notes/816121 ", "Title": "RAIS: Absence of one branch considered in another branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "821726 ", "URL": "/notes/821726 ", "Title": "HBRRAIS0 does not read 13DI offcycle in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "830026 ", "URL": "/notes/830026 ", "Title": "HBRRAIS0 - Filling up additional fields in record type 1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "879822 ", "URL": "/notes/879822 ", "Title": "HBRRAIS0 and inactive branches", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "925164 ", "URL": "/notes/925164 ", "Title": "HBRRAIS0 Legal Changes 2006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "942778 ", "URL": "/notes/942778 ", "Title": "HBRRAIS0 - at first record 1 the responsible company/branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1014911 ", "URL": "/notes/1014911 ", "Title": "HBRRAIS0 Legal Changes 2007", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1024059 ", "URL": "/notes/1024059 ", "Title": "HBRRAIS0 - Serie CTPS is truncated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1027053 ", "URL": "/notes/1027053 ", "Title": "RAIS - Employee with union Indicator is not correctly filled", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1027767 ", "URL": "/notes/1027767 ", "Title": "HBRRAIS0 - registry 9 with invalid length", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1031597 ", "URL": "/notes/1031597 ", "Title": "HBRRAIS0 - Overtime Hours", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1037334 ", "URL": "/notes/1037334 ", "Title": "RAIS:Contributions CNPJ may not be filled when IT0057 change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1037831 ", "URL": "/notes/1037831 ", "Title": "RAIS - Customizing field \"Bonus\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "494599 ", "URL": "/notes/494599 ", "Title": "HBRRAIS: Complete reviewed RAIS report", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "519162 ", "URL": "/notes/519162 ", "Title": "HBRRAIS: <PERSON><PERSON><PERSON> not disp., only two month processed", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "593233 ", "URL": "/notes/593233 ", "Title": "RAIS - termination and hiring codes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "597039 ", "URL": "/notes/597039 ", "Title": "RAIS - company change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "600559 ", "URL": "/notes/600559 ", "Title": "RAIS - employee transfered and fired during the year", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "600781 ", "URL": "/notes/600781 ", "Title": "HBRRAIS0 - 13th salary & termination", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "698095 ", "URL": "/notes/698095 ", "Title": "RAIS: Legal change 2004", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "706350 ", "URL": "/notes/706350 ", "Title": "HBRRAIS0: Rais 2004 does not print email address", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "778824 ", "URL": "/notes/778824 ", "Title": "HBRRAIS0 Visa document warning", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "816121 ", "URL": "/notes/816121 ", "Title": "RAIS: Absence of one branch considered in another branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "830026 ", "URL": "/notes/830026 ", "Title": "HBRRAIS0 - Filling up additional fields in record type 1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "879822 ", "URL": "/notes/879822 ", "Title": "HBRRAIS0 and inactive branches", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "925164 ", "URL": "/notes/925164 ", "Title": "HBRRAIS0 Legal Changes 2006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "942778 ", "URL": "/notes/942778 ", "Title": "HBRRAIS0 - at first record 1 the responsible company/branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1014911 ", "URL": "/notes/1014911 ", "Title": "HBRRAIS0 Legal Changes 2007", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1024059 ", "URL": "/notes/1024059 ", "Title": "HBRRAIS0 - Serie CTPS is truncated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1027053 ", "URL": "/notes/1027053 ", "Title": "RAIS - Employee with union Indicator is not correctly filled", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1031597 ", "URL": "/notes/1031597 ", "Title": "HBRRAIS0 - Overtime Hours", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1035862 ", "URL": "/notes/1035862 ", "Title": "RAIS:Missing remuneration field when period less 12 months", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "1037334 ", "URL": "/notes/1037334 ", "Title": "RAIS:Contributions CNPJ may not be filled when IT0057 change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "500", "Number": "821726 ", "URL": "/notes/821726 ", "Title": "HBRRAIS0 does not read 13DI offcycle in December", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "778824 ", "URL": "/notes/778824 ", "Title": "HBRRAIS0 Visa document warning", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "816121 ", "URL": "/notes/816121 ", "Title": "RAIS: Absence of one branch considered in another branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "830026 ", "URL": "/notes/830026 ", "Title": "HBRRAIS0 - Filling up additional fields in record type 1", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "879822 ", "URL": "/notes/879822 ", "Title": "HBRRAIS0 and inactive branches", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "925164 ", "URL": "/notes/925164 ", "Title": "HBRRAIS0 Legal Changes 2006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "942778 ", "URL": "/notes/942778 ", "Title": "HBRRAIS0 - at first record 1 the responsible company/branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1014911 ", "URL": "/notes/1014911 ", "Title": "HBRRAIS0 Legal Changes 2007", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1024059 ", "URL": "/notes/1024059 ", "Title": "HBRRAIS0 - Serie CTPS is truncated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1027053 ", "URL": "/notes/1027053 ", "Title": "RAIS - Employee with union Indicator is not correctly filled", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1031597 ", "URL": "/notes/1031597 ", "Title": "HBRRAIS0 - Overtime Hours", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1035862 ", "URL": "/notes/1035862 ", "Title": "RAIS:Missing remuneration field when period less 12 months", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1037334 ", "URL": "/notes/1037334 ", "Title": "RAIS:Contributions CNPJ may not be filled when IT0057 change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "1037831 ", "URL": "/notes/1037831 ", "Title": "RAIS - Customizing field \"Bonus\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "879822 ", "URL": "/notes/879822 ", "Title": "HBRRAIS0 and inactive branches", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "925164 ", "URL": "/notes/925164 ", "Title": "HBRRAIS0 Legal Changes 2006", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "942778 ", "URL": "/notes/942778 ", "Title": "HBRRAIS0 - at first record 1 the responsible company/branch", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1014911 ", "URL": "/notes/1014911 ", "Title": "HBRRAIS0 Legal Changes 2007", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1024059 ", "URL": "/notes/1024059 ", "Title": "HBRRAIS0 - Serie CTPS is truncated", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1027053 ", "URL": "/notes/1027053 ", "Title": "RAIS - Employee with union Indicator is not correctly filled", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1031597 ", "URL": "/notes/1031597 ", "Title": "HBRRAIS0 - Overtime Hours", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1035862 ", "URL": "/notes/1035862 ", "Title": "RAIS:Missing remuneration field when period less 12 months", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1037334 ", "URL": "/notes/1037334 ", "Title": "RAIS:Contributions CNPJ may not be filled when IT0057 change", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1037831 ", "URL": "/notes/1037831 ", "Title": "RAIS - Customizing field \"Bonus\"", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}