{"Request": {"Number": "1135589", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 456, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016454112017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001135589?language=E&token=75DD75299D4C038168EE42E24570102D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001135589", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001135589/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1135589"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "External error"}, "Priority": {"_label": "Priority", "value": "HotNews"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.01.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1135589 - IOT Corruptions a. Upgrade from COMPATIBLE <= 9.2 to >= 10.1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This is a copy from the Oracle Alert Note: 471479.1<br /><br />*****************************************************************<br />This note is released as a hot news. Check this note regularly<br />for updates. Otherwise you will not be informed about major<br />changes regarding the prerequisites, consequences and solutions.<br />There will be no new hot news in case of an update of this note.<br />*****************************************************************<br /><br />This problem affects databases running with COMPATIBLE &gt;= ******** which have previously run at a lower COMPATIBLE level, such as *******. This is generally the case for databases upgraded from 9.2 to 10g.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IOT, Index Organized Tables, Corruption, ORA-00600</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Index Organized Tables (IOT's) created / updated with COMPATIBLE<br />&lt;= ******* which are subsequently used with COMPATIBLE &gt;= ******** can become corrupt. The initial corruption does not show any external symptoms, but subsequent operations on a corrupt block can lead to noticeable corruptions and resulting ORA-600 errors or dumps.<br /><br />This issue is quite rare but can have a high impact if it occurs.<br /><br />There is a chance of hitting this problem if you have Index Organized Tables (IOT's) which have been created with COMPATIBLE &lt;= ******* which are subsequently used at a 10g (or higher) compatibility level.<br /><br />The corruption can only occur if non-key columns of the IOT have ever been updated such that they were reduced in size, and even then only occurs under certain circumstances. The problem does not affect updates to columns in IOT overflow segments.<br /><br />You can get a list of Index Organized Tables on a system using the query:<br /><br /> SELECT owner, table_name<br /> &#x00A0;&#x00A0;FROM dba_tables<br /> WHERE iot_type='IOT';<br /><br />Whilst in theory this problem can affect IOT's used as Advanced Queue (AQ) queue tables this is very unlikely due to the way such IOT's are modified by AQ operations.<br /><br />The initial form of corruption is not always detected by DBVerify (DBV) and shows no external symptoms.<br /><br />If DB_BLOCK_CHECKING is enabled then updates to a corrupt block will typically fail with an ORA-600 [kddummy_blkchk] error.<br /><br />If DB_BLOCK_CHECKING is not enabled then updates to a corrupt block can lead to more serious block corruption , and corruption of adjacent buffers in the buffer cache. This can present as various ORA-600 errors or dumps , such as ORA-600 [kcbgtcr_1].<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>If you are currently running Oracle 9.2 (or earlier) and plan to upgrade to Oracle 10.1 (or higher) then ensure that the release you are upgrading to includes the fix for Bug 6646613 before commencing the upgrade.<br /><br />Alternatively ensure that you keep COMPATIBLE at 9.2 after upgrading to 10g until such time as a patch or Patch Set can be installed which includes the fix.<br /><br />If you are already running 10g or higher and the database has previously run with COMPATIBLE &lt;= 9.2 then it is recommended to take the following steps:<br /><br />- Apply a patch or patch set which includes the fix for Bug 6646613<br /><br />- Start the instance with DB_BLOCK_CHECKING = FULL<br /><br />- Obtain a list of all IOT's in the database<br /><br />- Perform a \"SELECT FOR UPDATE\" operation on all rows of each IOT which<br />&#x00A0;&#x00A0;is potentially affected. (SELECT FOR UPDATE cleans out the blocks of<br />&#x00A0;&#x00A0;the IOT removing the initial corruption scenario)<br /><br />- If any SELECT FOR UPDATE fails with an ORA-600 [kddummy_blkchk] type<br />&#x00A0;&#x00A0;error then that IOT will need rebuilding as it contains corrupt<br />&#x00A0;&#x00A0;blocks.<br /><br /><br />The patches for the Oracle Bug 6646613 are requested.<br /><br />There is a patch conflict with a still recommended Patch<br />&#x00A0;&#x00A0; (4742607 -&gt; SAPNet note 1020225)<br />Therefore a Merge-Patch is needed for both patches.<br /><br />This Merge-Patch (6771608) is now available (-&gt; SAPNet note 1020225)<br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB-ORA (BW ORACLE)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5041128)"}, {"Key": "Processor                                                                                           ", "Value": "C5025123"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001135589/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001135589/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001135589/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001135589/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001135589/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001135589/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001135589/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001135589/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001135589/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "641435", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle index-organized tables (IOTs)", "RefUrl": "/notes/641435"}, {"RefNumber": "1020225", "RefComponent": "BC-DB-ORA", "RefTitle": "High number of 'cache buffer chains' in Oracle 10.2.0.2", "RefUrl": "/notes/1020225"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "641435", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle index-organized tables (IOTs)", "RefUrl": "/notes/641435 "}, {"RefNumber": "1020225", "RefComponent": "BC-DB-ORA", "RefTitle": "High number of 'cache buffer chains' in Oracle 10.2.0.2", "RefUrl": "/notes/1020225 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}