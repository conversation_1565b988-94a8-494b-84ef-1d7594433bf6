import csv
import logging
import os
import json

from request_utils import load_summary_csv
from summarize_util import call_orchestration_service

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def get_all_records_from_csv(csv_text):
    import io
    reader = csv.DictReader(io.StringIO(csv_text))
    records = []
    for row in reader:
        records.append({
            "test_id": row["guid"],
            "content": row["summary"]
        })
    return records

def test_for_sitem_summary():
    logging.info("Start testing sitem summary with orchestration service")
    result_file = "sitem_summary_test_results_orchestration_final.csv"

    # Load all records
    csv_text = load_summary_csv()
    records = get_all_records_from_csv(csv_text)

    results = []

    # Process each record
    for i, record in enumerate(records[1:], 1):
        fields = record.split(',')
        guid = fields[0].strip('"')
        content = fields[3].strip('"')

        if not content:
            logging.warning(f"No content for {guid}, skipping")
            continue

        logging.info(f"Processing record {i}/{len(records) - 1}, guid: {guid}")

        # Process with filter
        filter_response = call_orchestration_service(content, guid, type="filter")
        filter_result = "Fail"
        filter_details = ""

        if filter_response:
            if "error" not in filter_response:
                filter_result = "Pass"
                filter_details = "Input Filter passed successfully"
            else:
                filter_details = filter_response.get("error", {}).get("message", "Unknown error")

        # Process with mask
        mask_response = call_orchestration_service(content, guid, type="mask")
        mask_result = "Fail"
        mask_details = ""

        if mask_response:
            if "error" not in mask_response:
                # Check if the masked template is different from original content
                try:
                    original_content = mask_response.get("intermediate_results", {}).get("templating", [])[0].get("content", "")
                    masked_template_json = mask_response.get("intermediate_results", {}).get("input_masking", {}).get("data", {}).get("masked_template", "[]")
                    masked_template = json.loads(masked_template_json)[0].get("content", "")

                    if original_content == masked_template:
                        mask_result = "Pass"
                        mask_details = "No personal information detected"
                    else:
                        mask_result = "Pass"
                        mask_details = "Personal information was masked"
                except (IndexError, KeyError, json.JSONDecodeError) as e:
                    mask_details = f"Error parsing response: {str(e)}"
            else:
                mask_details = mask_response.get("error", {}).get("message", "Unknown error")

        # Save results
        results.append({
            "guid": guid,
            "filter_result": filter_result,
            "filter_details": filter_details,
            "mask_result": mask_result,
            "mask_details": mask_details
        })

        # Write results after each record to avoid data loss
        write_results_to_csv(results, result_file)

    logging.info(f"Testing completed. Results saved to {result_file}")
    return results


def write_results_to_csv(results, filename):
    with open(filename, 'w', newline='') as file:
        fieldnames = ["guid", "filter_result", "filter_details", "mask_result", "mask_details"]
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            writer.writerow(result)


if __name__ == "__main__":
    test_for_sitem_summary()
