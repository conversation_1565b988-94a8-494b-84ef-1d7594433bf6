SAP Note 2015638 deals with a specific issue encountered when custom fields are maintained through the include CI_FAGL_R_MAP_CUST, potentially after applying SAP Note 2011705 for determining values for these fields from object master data. During execution, the user may experience a short dump with an error indicating that a table type 'XXXX' was not found.

The root cause of this short dump is identified as an uncaught exception raised by the method `cl_abap_structdescr=>describe_by_name( lv_tabname )` within the function `CHECK_ADD_BAL_FIELDS`.

To resolve this issue, the SAP Note suggests manual implementation of the correction or downloading the corresponding support package to prevent the unhandled exception and thus avoid the short dump. The note does not elaborate on the exact steps for manual implementation or which support package contains the fix.

Additionally, this note references SAP Note 1471153, which is a composite note concerning the reorganization of profit centers and funds management in SAP and provides guidance on related notes for those features. However, the referenced note’s details are not directly related to the technical issue addressed in SAP Note 2015638.