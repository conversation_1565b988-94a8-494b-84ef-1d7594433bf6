SAP Note 3063159 addresses an issue where material numbers longer than 18 characters are incorrectly truncated to 32 characters when extracted using view DataSources 0MAT_PLANT_TEXT, 0MAT_SALES_TEXT, or 0MAT_ST_LOC_TEXT. The problem is identified as a program error and occurs when long material numbers are used in the S/4HANA source system and linked to extended InfoObjects in the SAP BW system, including 0Material and other custom InfoObjects that are greater than 18 characters.

The solution provided is the delivery of new DataSources - 0MAT_PLANT_LM_TEXT, 0MAT_SALES_LM_TEXT, and 0MAT_ST_LOC_LM_TEXT - via a support package. Users working with long material numbers in S/4HANA and loading texts for the mentioned materials into BW should activate the new DataSources in transaction RSA5 and configure the connections to the corresponding InfoObjects in BW through transformations.

For SAP BW/4HANA systems with SAP BW/4HANA Content Add-On, users can copy transformations for the new DataSources from the content using transaction RSORBCT. Additionally, after importing the support package in the S/4HANA source system, the system needs to be reactivated in BW using transaction RSA1 to generate pseudo-D versions of the content, as detailed in SAP Note 2433354.

Finally, for users who need to extract texts for long material numbers before the support package is available, manual correction instructions are provided.