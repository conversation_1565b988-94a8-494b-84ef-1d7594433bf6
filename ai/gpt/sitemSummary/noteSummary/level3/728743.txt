SAP Note 728743 informs users about the release of DB2 V8 for z/OS for use with SAP systems. It highlights:

Symptom:
- Introduction of DB2 V8 to be run on z/OS for SAP systems.

Other Terms:
- Relevant terms include zSeries, System z, IBM, DB2 for z/OS, DB2 Connect, and V8.

Reason and Prerequisites:
- IBM's general availability of DB2 V8 does not imply its automatic use with SAP systems. SAP has a staged verification and certification process for different releases. Customers should note the specific prerequisites before migrating to DB2 V8, including having at least z/OS 1.4 on their database server.

Solution:
- The release of DB2 V8 for SAP includes data sharing support and has been completed for all currently supported SAP software versions on DB2.
- However, there is a caution that specific inconsistencies in the database table definitions must be rectified before migration, as detailed in SAP Note 848384.
- For R/3 versions (3.1I, 4.0B, 4.5B, 4.6B) and components based on them, as well as SAP NetWeaver based on Web AS 6.40 and Business Suite 2004/2005, specific minimum prerequisites for the SAP Kernel, LIB_DBSL, and ICLI PTF are listed.
- There are several limitations, mainly no Unicode support, no support for IBM's zIIP processors, and other restrictions detailed within each component's prerequisites.
- OEM customers receive a full DB2 package that includes the DB2 V8 engine, utility suite, and DB2 Connect V8 for connectivity.

The note ends with a reference to Bernhard Heininger, Development Manager at SAP AG, and additional support information for SAP MDM 5.5 SP6 with DB2 for z/OS on various platforms.