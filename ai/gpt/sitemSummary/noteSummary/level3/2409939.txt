SAP Note 2409939 addresses the unavailability of the Business Partner (BP) Hierarchy functionality following a system conversion to SAP S/4HANA, on-premise edition. The main points of this note are:

- It describes a change related to the transition to SAP S/4HANA where the BP hierarchy has been removed.
- The removal is due to the aim to eliminate redundancy as the customer hierarchy already exists and serves a similar purpose.
- This note outlines that because of the removal, users will no longer have access to BP hierarchy data after migrating to S/4HANA.
- Specific technical components that are no longer available in S/4HANA include certain transactions (e.g., BPH, BPH_TYPE), BAPIs (e.g., BUHI_ASSIGN_BP_TO_NODE), and other settings in the IMG.
- It's also mentioned that S/4HANA is considering a better hierarchy solution conceptualization stage.
- Actions recommended include knowledge transfer to key and end users impacted by this change.
- The note helps determine the relevance of this change by suggesting to check if BP Hierarchies are in use by looking at the transaction BPH or the table BUT_HIER_TREE.

Users who rely on BP hierarchy need to be aware of this change and adjust their business processes accordingly.