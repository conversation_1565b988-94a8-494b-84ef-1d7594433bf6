<h3>SI5: FIN_TRM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270470">2270470 - S4TWL - Credit Risk Analyzer Link to Cash Management Disabled in S/4HANA</a></strong></p>
<p><strong>Description:</strong></p>
<p>The link from classical Cash Management to the Treasury and Risk Management analyzers (transactions RMCM and KLMAP) has been disabled in S/4 HANA. Instead, with SAP S/4HANA 1709, Bank Account Management is integrated with the Market Risk Analyzer and the Credit Risk Analyzer of the Treasury and Risk Management by a new type of financial object for bank account balances.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The link from classic cash management to the analyzers (transactions RMCM and KLMAP) is no longer supported in SAP S/4HANA. This will issue a warning if entries are found in the corresponding customizing table KLCMMAPPING in any client of the system. The detected customer code using deprecated SAP objects will no longer function, and integration setups between Bank Account Management and Treasury and Risk Management will need to be configured anew.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Read the following information in case the pre-upgrade-check in your system issues a message for the check ID SI5_CM_TO_CRA "Classic cash management link to analyzers":
Ensure that there are no entries in the customizing table KLCMMAPPING (transaction KLMAP) in all clients of the system. Detected customer code related to this functionality must be abandoned or adapted. For integration of Bank Account Management with Treasury and Risk Management in SAP S/4HANA 1709, set up the automatic integration of financial objects for bank accounts.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2190420">2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code</a>, <a href="https://launchpad.support.sap.com/#/notes/2241080">2241080 - SAP S/4HANA: Content for checking customer specific code</a>, <a href="https://launchpad.support.sap.com/#/notes/2436688">2436688 - Recommended SAP Notes for Using S/4HANA Custom Code Checks in ATC or Custom Code Migration App</a>, <a href="https://launchpad.support.sap.com/#/notes/2294371">2294371 - S4TC EA-FINSERV Master Check for S/4 System Conversion Checks</a></p>