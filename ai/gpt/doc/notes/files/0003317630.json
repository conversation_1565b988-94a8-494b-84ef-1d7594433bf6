{"Request": {"Number": "0003317630", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 204, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003317630?language=E&token=62B467AC66109A8450951E0BA078DFD3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003317630", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003317630/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3317630"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Known Error"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.09.2023"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-DVM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Volume Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "3317630 - DVM as part of SAP Readiness Check: Syntax error in program /SDF/SAPLFDQ_ASSESSMENT for Financial Data Quality - SAP ERP System"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>A <strong>SYNTAX_ERROR</strong> dump in program <strong>/SDF/SAPLFDQ_ASSESSMENT</strong> occurs in the affected <strong>SAP ERP system</strong> during <strong>DVM </strong>as part of <strong>SAP Readiness Check</strong> for <strong>SAP S/4HANA</strong>:</p>\r\n<p>Category &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; <strong>ABAP Programming Error</strong><br />Runtime Errors &#x00A0; &#x00A0; &#x00A0; &#x00A0; <strong>SYNTAX_ERROR</strong><br />ABAP Program &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; <strong>/SDF/SAPLFDQ_ASSESSMENT</strong><br />Application Component &#x00A0;<strong>SV-SMG-DVM</strong><br />Date and Time &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;<strong>dd/MM/YYYY hh:mm:ss</strong><br />----------------------------------------------------------------------------------------------------</p>\r\n<p>----------------------------------------------------------------------------------------------------<br />|Short Text&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;<br />| &#x00A0; &#x00A0;<strong>Syntax error in program &#34;/SDF/SAPLFDQ_ASSESSMENT</strong> &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#34;.&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;<br />----------------------------------------------------------------------------------------------------</p>\r\n<p>----------------------------------------------------------------------------------------------------<br />|What happened?&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;<br />| &#x00A0; &#x00A0;Error in the ABAP Application Program&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;<br />|&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;<br />| &#x00A0; &#x00A0;The current ABAP program &#34;<strong>RC_COLLECT_ANALYSIS_DATA</strong>&#34; had to be terminated&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;<br />| &#x00A0; &#x00A0; because it has&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;<br />| &#x00A0; &#x00A0;come across a statement that unfortunately cannot be executed.&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;<br />| &#x00A0; &#x00A0;In include &#34;/<strong>SDF/LFDQ_ASSESSMENTTOP</strong> &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#34;, in line 4 of program&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;<br />| &#x00A0; &#x00A0; &#34;<strong>/SDF/SAPLFDQ_ASSESSMENT</strong> &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#34;, the following syntax errors&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;<br />| &#x00A0; &#x00A0;have occurred:&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;&#x00A0;<br />| &#x00A0; &#x00A0;REPORT or PROGRAM statement already exists. &#x00A0;&#x00A0;</p>\r\n<p>----------------------------------------------------------------------------------------------------<br />|Source Code Extract&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;<br />----------------------------------------------------------------------------------------------------<br />|Line |SourceCode&#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0; &#x00A0;<br />----------------------------------------------------------------------------------------------------<br /><strong>|&gt;&gt;&gt;&gt;&gt;| &#x00A0; &#x00A0;call function lc_fm_2_check_prerequisites &#34;/SDF/FDQ_CHECK_PREREQUISITES</strong><br />----------------------------------------------------------------------------------------------------</p><h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3><p>SAP ERP system</p><h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3><ol><li>Execute T-code <strong>SE38</strong></li><li>Enter the program <strong>RC_COLLECT_ANALYSIS_DATA</strong></li><li>DVM Checkbox flagged</li></ol><h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3><p>A programming error causes the dump to occur because the SAP Note&#x00A0;<a href=\"https://me.sap.com/notes/2972792\" target=\"_blank\" rel=\"nofollow noopener noreferrer\">2972792</a>&#x00A0;has not been implemented successfully.&#x00A0;This note allows to install APIs needed for the FDQ Analysis and is usually a reason for getting a DUMP /SDF/SAPLFDQ_ASSESSMENT.&#x00A0;Since it is not correctly implemented, no API exists in the system. Then start FDQ analysis &gt; no API exist &gt; get a dump.</p><h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3><p>The <strong>SAP Note <a href=\"https://me.sap.com/notes/2972792\" target=\"_blank\" rel=\"nofollow noopener noreferrer\">2972792</a></strong> must be <strong>de-implemented</strong> and <strong>re-implement</strong> again in the <strong>latest version</strong>.</p><h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3><ul><li>SAP Note <a href=\"https://me.sap.com/notes/2721530\" target=\"_blank\" rel=\"nofollow noopener noreferrer\">2721530</a>&#x00A0;- FAQ for collecting DVM data during S/4 HANA Readiness Check</li><li>SAP Note <a href=\"https://me.sap.com/notes/2909538\" target=\"_blank\" rel=\"nofollow noopener noreferrer\">2909538</a>&#x00A0;- Financial Data Quality: Trigger FDQ Data Collection - DDIC Objects</li><li>KBA <a href=\"https://me.sap.com/notes/3317630\" target=\"_blank\" rel=\"nofollow noopener noreferrer\">3317630</a> - DVM as part of SAP Readiness Check: Syntax error in program /SDF/SAPLFDQ_ASSESSMENT for Financial Data Quality - SAP ERP System</li></ul><h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3><p>RC_COLLECT_ANALYSIS_DATA, /SDF/SAPLFDQ_ASSESSMENT, /SDF/FDQ_CHECK_PREREQUISITES, /SDF/LFDQ_ASSESSMENTTOP, Readiness Check, Financial Data Quality, FDQ</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SCS-S4R (SAP Readiness Check)"}, {"Key": "Requires Action", "Value": "0"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I556630)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I556947)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003317630/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003317630/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003317630/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003317630/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003317630/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003317630/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003317630/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003317630/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003317630/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP ERP 6.0"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}