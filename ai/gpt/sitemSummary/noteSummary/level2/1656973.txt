Summary of SAP Note 1656973 - Authorization check:

The SAP Note 1656973 addresses an error related to authorization checks in the SAP system. The specific issue is a runtime error that occurs within the function module `SUSR_AUTHORITY_CHECK_SIMULATE`, which is invoked by the class `CL_FAGL_R_PLAN_FACTORY`.

Key Points of the SAP Note:
- **Symptom**: A runtime error during the authorization check process.
- **Other Terms**: The note mentions exceptions and authorization checks as related terms.
- **Reason and Prerequisites**: The underlying cause of the issue is cited as a program error.
- **Solution**: The note instructs users to implement the corrections attached to the note.

The note references two other composite SAP Notes that deal with reorganization processes but these references do not directly relate to the authorization check issue at hand. Instead, they provide context to other reorganization processes within SAP, including segment reorganization in SAP Note 1627018 and profit center and Funds Management reorganization in SAP Note 1471153. The provided references are more for informational purposes and guide users to further notes for detailed solutions regarding reorganization tasks, not for the authorization check issue itself.

The summary of SAP Note 1656973 is confined to recognizing a program error related to authorization checks, advising to apply the provided corrections to resolve the error. It doesn't directly tie into the specifics of the referenced notes for reorganization tasks.