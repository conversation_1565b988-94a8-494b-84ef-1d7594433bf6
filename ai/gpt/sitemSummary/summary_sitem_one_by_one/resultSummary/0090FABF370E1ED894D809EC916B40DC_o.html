<h3>SI2: FIN_AA</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270388">2270388 - S4TWL - Asset Accounting: Parallel valuation and journal entry</a></strong></p>
<p><strong>Description:</strong></p>
<p>You have been using classic Asset Accounting until now and will be using new Asset Accounting in SAP S/4HANA. Other scenarios (such as the switch from SAP ERP 6.0, EhP7 with new Asset Accounting to SAP S/4HANA) are <strong>not</strong> considered here.</p>
<p><strong>Business Process Impact:</strong></p>
<p>To use new Asset Accounting in SAP S/4HANA, new General Ledger Accounting must also be used. If classic General Ledger Accounting was used before the conversion, new General Ledger Accounting is activated automatically with the conversion. In new Asset Accounting, parallel valuations can be mapped using either the ledger approach or the accounts approach, but a mixed approach (ledger with accounts) is not supported. The system posts the actual values of the leading and parallel valuations in real time. Redundant data stores and periodic posting runs for APC values are no longer necessary, and reconciliation postings are not required.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>
<ul>
<li><strong>Customizing / Configuration:</strong> Customizing might be required if Simplification Item Check shows an error message.</li>
<li><strong>Process Design / Blueprint:</strong> A decision for the ledger approach needs to be made prior to the conversion project.</li>
<li><strong>Interface Adaption:</strong> Adapt or replace obsolete or changed legacy data transfer methods during the conversion project.</li>
<li><strong>Data cleanup / archiving:</strong> Optional reduction in the number of asset accounting documents to avoid unnecessary data error corrections and minimize conversion downtime.</li>
<li><strong>Data correction:</strong> Check and correct inconsistent data relevant for the conversion.</li>
<li><strong>Data migration:</strong> Perform data migration to the new SAP S/4HANA data model.</li>
<li><strong>User Training:</strong> Inform users about changes in Asset Accounting during the conversion project.</li>
<li><strong>Business Operations:</strong> Execute year-end and month-end closing activities during the conversion project.</li>
<li><strong>Custom Code Adaption:</strong> Refer to simplification item SI1: FIN_AA and SAP note 2270387 for required custom code adaptation.</li>
<li><strong>Business Decision:</strong> Decide about future ledger approach for asset accounting before the conversion project.</li>
<li><strong>Process Design / Blueprint:</strong> Create a blueprint for the required Asset Accounting configuration in SAP S/4HANA before or during the conversion project.</li>
</ul>
</p>
<p><strong>Reference Notes:</strong></p>
<p>
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/2913340">2913340 - Error during cross-company code depreciation postings or interest postings</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2270404">2270404 - S4TWL - Technical Changes in Controlling</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2403248">2403248 - FI-AA (new): Availability of RAFABNEW</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/1498047">1498047 - Changeover from old to new depreciation calculation</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/965032">965032 - Differences between old and new depreciation calculation</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/1121965">1121965 - Documentation Enterprise Extension EA-FIN</a></li>
</ul>
</p>