SAP Note 948466 addresses an issue in which an error occurs when attempting to modify the global settings for indexing in the BIA monitor due to the non-existence of the variant for the "TREXINDEXP" process type.

Summary:

Symptom: Error when changing global indexing settings in the BIA monitor.
Other Terms: TREXINDEXP, global parameter, BIA Monitor.
Cause: The default variant for the batch process type "TREXINDEXP" has not been maintained.
Solution: Two potential solutions are provided:
1. Manually maintain the settings by calling transaction RSBATCH, selecting "TREXINDEXP," and saving the settings, which will allow for the maintenance of the parallel processing level in the BIA monitor thereafter.
2. Implement source code corrections as per SAP Note 955731, which will result in the default variant for the "TREXINDEXP" process type being created automatically.