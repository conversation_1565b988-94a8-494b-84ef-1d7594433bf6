{"guid": "0050569455E21ED5B3E17678391EC09E", "sitemId": "SI_1_Logistics_QM", "sitemTitle": "S4TWL - Results Recording", "note": 2270124, "noteTitle": "2270124 - S4TWL - Results Recording for handheld devices", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Worklist</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Renovation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The functionality to create worklists for mobile devices such as PalmPilot (transaction QEH1) is not available within SAP S/4HANA, on-premise edition 1511.</p>\n<p><strong>Business Process related information</strong></p>\n<p>As an alternative, you could use the POWL and the WebDynpro application for results recording that is included in the PFCG role for NWBC Quality Inspector (SAP_SR_QUALITY_INSPECT_5).</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"493\">\n<p><strong> </strong>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td valign=\"top\" width=\"262\">\n<p>QEH1</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Knowledge transfer to key and end users</p>", "noteVersion": 2, "refer_note": [{"note": "2207271", "noteTitle": "2207271 - Worklist for results recording on mobile devices (QEH1) no longer available", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Transaction QEH1 is no longer available.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Palm Pilot</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In S/4HANA, functions that are hardly used are no longer available.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The mobile application should always select your worklist on its own.</p>", "noteVersion": 1}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "The functionality to create worklists for mobile devices such as PalmPilot (transaction QEH1) is not available within SAP S/4HANA, you could use the POWL and the WebDynpro application for results recording that is included in the PFCG role for NWBC Quality Inspector (SAP_SR_QUALITY_INSPECT_5)"}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "Adapt your Custom Code as per Note 2207271"}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}