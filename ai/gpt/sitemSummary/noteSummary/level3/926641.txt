SAP Note 926641 deals with an issue encountered while maintaining partner data for ownership shares in the real estate sector within SAP's flexible real estate management, starting from Release 470x200. Specifically, the problem arises when entering data for owners without specifying ownership shares, which by default requires a conversion factor. If a conversion factor exists but no shares are maintained, the system incorrectly expects the maintenance of the shares and results in an error message "Enter the value of a coownership share."

The root cause of this issue is an incompatibility between the classic Real Estate application and the flexible real estate management within SAP.

To resolve this problem, the note provides detailed instruction on how to adjust the message settings. Users are advised to start the maintenance transaction OBMSG, navigate to the application area REBPBP for Business Partner, and within the messages settings, create an entry with certain specified parameters that allow for the message to be output either as an error, warning, or to be entirely suppressed based on the configuration.

Furthermore, the note suggests implementing attached corrections in the source code to change the program behavior and control whether the error is enforced or is simply a warning or suppressed, depending on the user settings in transaction OBA5.

In summary, SAP Note 926641 provides a solution for users experiencing issues with ownership share maintenance in the real estate module by modifying system message settings and applying source code corrections to allow for flexibility in message handling.