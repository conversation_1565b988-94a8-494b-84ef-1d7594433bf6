{"Request": {"Number": "955670", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 292, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016115462017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000955670?language=E&token=741829217A470F03A1B292BCC2D1BE7F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000955670", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000955670/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "955670"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.12.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DBI"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB Independent Database Interface"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB Independent Database Interface", "value": "BC-DB-DBI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DBI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "955670 - DB multiconnect with SAP MaxDB as secondary database"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Access to a non-R/3 SAP DB/MaxDB database from an application server does not work.<br />The developer trace contains the following entries, for example:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;B&#x00A0;&#x00A0;create_con (con_name=SDBTEST)</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>B&#x00A0;&#x00A0;Loading DB library '...\\SYS\\exe\\run\\dbadaslib.dll' ...</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>M&#x00A0;&#x00A0;*** ERROR =&gt; DlLoadLib: LoadLibrary(...\\SYS\\exe\\run\\dbadaslib.dll) Error 126 [dlnt.c&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 237]</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Error 126 = \"The specified module could not be found.\"</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>B&#x00A0;&#x00A0;*** ERROR =&gt; Couldn't load library '...\\SYS\\exe\\run\\dbadaslib.dll' [dbcon.c&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4667]</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>B&#x00A0;&#x00A0;***LOG BYG=&gt; could not load library for database connection SDBTEST [dbds#3 @ 1035] [dbds&#x00A0;&#x00A0;&#x00A0;&#x00A0;1035 ]&#xFEFF;</em></span><br /><br />or<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;C&#x00A0;&#x00A0;*** ERROR =&gt; CONNECT failed : sqlcode=-4008 &#x00A0;&#x00A0;&#x00A0;&#x00A0;(Unknown user name/password combination) [dbadautl.c&#x00A0;&#x00A0; 311]</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>B&#x00A0;&#x00A0;***LOG BY2=&gt; sql error -4008&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;performing CON &#x00A0;&#x00A0; [dbds#3 @ 1044] [dbds&#x00A0;&#x00A0;&#x00A0;&#x00A0;1044 ]</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>B&#x00A0;&#x00A0;***LOG BY0=&gt; Unknown user name/password combination &#x00A0;&#x00A0; [dbds#3 @ 1044] [dbds&#x00A0;&#x00A0;&#x00A0;&#x00A0;1044 ]&#xFEFF;</em></span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Client software, DBCON, DBCONSUSR, DBCONS, dbadaslib, precompiler, SAP DB, MaxDB, DB59</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The implementation steps required for the use of secondary database connections with SAP DB/MaxDB were not performed or were performed incorrectly.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>In order to access an SAP DB/MaxDB database by means of a secondary database connection, the following prerequisites must be met:</p>\r\n<ol>1. The SAP DB/MaxDB client software and the corresponding SAP DBI interface library (dbadaslib) must be installed. This is only necessary once, even if multiple SAP DB/MaxDB databases are to be connected up and even if these have different version levels.</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For information about the installation of the client software and the <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbadaslib&#xFEFF;</em></span>, see the following SAP Notes:</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<a target=\"_blank\" href=\"/notes/649814\">649814</a> - Installation/update of SAP MaxDB/liveCache client software<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<a target=\"_blank\" href=\"/notes/822271\">822271</a> - FAQ: SAP MaxDB client software</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<a target=\"_blank\" href=\"/notes/822239\">822239</a> - FAQ: SAP MaxDB interfaces</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<a target=\"_blank\" href=\"/notes/789086\">789086</a> - Precompiler Runtime to be used for Extended Kernel</p>\r\n<p>2. For each individual database to be addressed, you must make an entry in the table <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;DBCON&#xFEFF;</em></span> using the maintenance transaction <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;DBCO&#xFEFF;</em></span>.<br /> This entry must be created as follows: <br />DB Connection: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&lt;connection_name&gt;&#xFEFF;</em></span><br />DBMS: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;ADA&#xFEFF;</em></span><br />User Name: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&lt;user_name_on_target_database&gt;&#xFEFF;</em></span><br />Password: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&lt;user_password_on_target_database&gt;&#xFEFF;</em></span><br />Conn. info: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&lt;host_name_target_database&gt;-&lt;database_name&gt;&#xFEFF;</em></span></p>\r\n<p><strong>CAUTION:</strong> In the table <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;DBCON&#xFEFF;</em></span>, enter the password in UPPERCASE LETTERS. If you enter the password in lowercase letters, the error <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;-4008 \"Unknown user name/password combination\"&#xFEFF;</em></span> occurs when the next connection attempt takes place.<br /><br />For general and more detailed information about secondary database connections, see SAP Note <a target=\"_blank\" href=\"/notes/323151\">323151</a>.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-SDB (MaxDB)"}, {"Key": "Responsible                                                                                         ", "Value": "I028297"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D019124)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000955670/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955670/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955670/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955670/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955670/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955670/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955670/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955670/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000955670/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "822271", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB client software", "RefUrl": "/notes/822271"}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239"}, {"RefNumber": "789086", "RefComponent": "BC-DB-SDB", "RefTitle": "Precompiler Runtime to be used for Extended Kernel", "RefUrl": "/notes/789086"}, {"RefNumber": "649814", "RefComponent": "BC-DB-SDB", "RefTitle": "Installation/update of SAP MaxDB/liveCache client software", "RefUrl": "/notes/649814"}, {"RefNumber": "520647", "RefComponent": "BW-SYS-DB-SDB", "RefTitle": "External database connect to an SAP MaxDB database", "RefUrl": "/notes/520647"}, {"RefNumber": "323151", "RefComponent": "BC-DB-DBI", "RefTitle": "Several DB connections with Native SQL", "RefUrl": "/notes/323151"}, {"RefNumber": "1037016", "RefComponent": "BC-DB-SDB", "RefTitle": "IBM i: MaxDB Client Support for IBM i application server", "RefUrl": "/notes/1037016"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239 "}, {"RefNumber": "323151", "RefComponent": "BC-DB-DBI", "RefTitle": "Several DB connections with Native SQL", "RefUrl": "/notes/323151 "}, {"RefNumber": "1037016", "RefComponent": "BC-DB-SDB", "RefTitle": "IBM i: MaxDB Client Support for IBM i application server", "RefUrl": "/notes/1037016 "}, {"RefNumber": "822271", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB client software", "RefUrl": "/notes/822271 "}, {"RefNumber": "520647", "RefComponent": "BW-SYS-DB-SDB", "RefTitle": "External database connect to an SAP MaxDB database", "RefUrl": "/notes/520647 "}, {"RefNumber": "789086", "RefComponent": "BC-DB-SDB", "RefTitle": "Precompiler Runtime to be used for Extended Kernel", "RefUrl": "/notes/789086 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}