SAP Note 2478683 describes restrictions associated with Catch Weight Management (CWM) in the SAP S/4HANA 1709 release. A summary of the note is as follows:

1. Fiori Application Limitations: CWM-specific adjustments are not available in Fiori applications for this release, and they do not support Catch Weight (CW) material.

2. CWM Old Architecture and 'B' Unit of Measure: The older CWM architecture, alongside materials with unit of measure category 'B', is not supported in SAP S/4HANA 1709. Users are directed to pre-check notes 2182725, 2335828, and 2335793 for more information.

3. Flexible Material Prices: The functionality for Flexible Material Prices is discontinued and unsupported in SAP S/4HANA. Those seeking further details are referred to SAP Note 2414624.

4. Solution-Specific Restrictions: Many of the restrictions previously found in the ECC Industry Solution IS-CWM are still applicable to CWM in SAP S/4HANA. An attached document is mentioned for detailed information on these restrictions.

The note also references:
- SAP Note 2612327, which discusses the incompatibility of CWM and the Retail Business Function in SAP S/4HANA systems and the need not to activate them together.
- SAP Note 2358928, which details the status of CWM in SAP S/4HANA and highlights various discontinuations and supports related to the old CWM architecture.
- SAP Note 2335828, which contains pre-transition checks for the IS-CWM software component before moving to SAP S/4HANA, with checks for old architecture and 'B' materials.

Customers using CWM within SAP S/4HANA 1709 are advised to be aware of these restrictions and to refer to the detailed documents and related notes for guidance on managing their processes and system functionalities effectively.