{"guid": "0050569455E21ED5B3E176783915809E", "sitemId": "SI6: Logistics_PP", "sitemTitle": "S4TWL - Sales and Operation Planning", "note": 2268064, "noteTitle": "2268064 - S4TWL - Sales and Operation Planning", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Transactions MC74, MC75, MC76, MC78, MC80, MC81, MC82, MC83, MC87, MC88, MC89</p>\n<p>Package MCP2</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Sales &amp; Operations Planning (SOP) is a forecasting and planning tool for setting targets for sales and production based on historical, current, or estimated data. SOP is used for long-term strategic planning, not short-term tactical planning. Resource requirements can be computed to determine work center load and utilization. SOP is often performed on aggregated levels such as product groups and work-center hierarchies. Sales &amp; Operations Planning includes standard SOP and flexible planning. Standard SOP comes preconfigured with the system. Flexible planning offers options for customized configuration.</p>\n<p>Sales &amp; Operations Planning (SOP) will be replaced by <a href=\"https://help.sap.com/viewer/p/SAP_INTEGRATED_BUSINESS_PLANNING\" target=\"_blank\">Integrated Business Planning IBP</a> (Functionality available in SAP S/4HANA delivery but not considered as future technology). Integrated Business Planning supports all SOP features plus advanced statistical forecasting, multi-level supply planning, an optimizer, collaboration tools, an Excel-based UI, and Web-based UIs.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>PP SOP is intended as a bridge or interim solution, which allows you a stepwise system conversion from SAP ERP to SAP S/4HANA on-premise edition and SAP IBP.</p>\n<p><strong>Custom Code related information</strong></p>\n<p>Integrated Business Planning (IBP) is an all-new implementation for sales and operations planning. Any enhancements or modifications of SOP will not work once IBP is used.</p>\n<p><strong>Exceptions</strong></p>\n<p>ERP Sales &amp; Operations Planning (SOP) will be replaced by Integrated Business Planning. The following parts of SOP are needed also for other business processes. They are part of core SAP S/4HANA and will not be replaced:</p>\n<ul>\n<li>Using table view V_T001W_DR or using transaction V_T001W_DR you can maintain the node type of an ERP plant in the distribution network. Node-types are also needed by various other applications such as APO and IBP integration. </li>\n<li>Using transactions MC84, MC85, and MC86 you can maintain product groups. Product groups are a popular selection condition in MRP apps. </li>\n</ul>", "noteVersion": 4, "refer_note": [], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "After conversion project", "Condition": "Optional", "Additional_Information": "The successor of PP SOP will be SAP IBP. PP SOP is still available as an interim solution. SAP recommend to make yourself familiar with SAP IBP."}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Decide if and when to do the migration to alternative solution."}]}