{"Request": {"Number": "2340092", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 230, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018362412017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002340092?language=E&token=AD7F596048171D940E205CCAC52A40EB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002340092", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002340092/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2340092"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 30}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.12.2021"}, "SAPComponentKey": {"_label": "Component", "value": "PPM-PRO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Project Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Portfolio and Project Management", "value": "PPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project Management", "value": "PPM-PRO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PPM-PRO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2340092 - PPM 1.0 for S4H: Performance"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note contains a collection of the notes relevant for SAP Portfolio and Project Management&#160;1.0 for SAP S/4HANA&#160;performance.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Project and Portfolio Management, cProjects, SAP RPM, EPPM, 100, runtime, response time, performance</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains a collection of the notes relevant for SAP Portfolio and Project Management&#160;1.0 for SAP S/4HANA&#160;performance.</p>\r\n<p class=\"longtext MonoSpace\"><strong>Performance relevant notes for SAP Portfolio and Project Management&#160;1.0 for SAP S/4HANA&#160;:</strong></p>\r\n<p class=\"longtext MonoSpace\">When&#160;doing a conversion&#160;from a release lower than SAP Portfolio and Project Management&#160;6.0 and if metrics in Portfolio Management have been used, it is higly recommended to run the report given in SAP Note 1816121 once after the upgrade.<br /><br />The following SAP Notes relevant for performance have been created after SP00 and will be included in <strong>SP01</strong>:</p>\r\n<ul>\r\n<li>\r\n<div class=\"longtext MonoSpace\">2371360 - Query on table T009B is not optimal</div>\r\n</li>\r\n<li>2378825 - Performance issue on saving the resource availability</li>\r\n<li>2384015 - Performance problem accessing classification hierarchy</li>\r\n<li>2381038 - Performance improvement in the /RPM/FICO_INT_PLANNING report for 'Rollup' option</li>\r\n<li>2347124 - Performance Improvement in FM /RPM/GET_SCHEDULE_STATUS_INFO1</li>\r\n</ul>\r\n<p>The following SAP Notes relevant for performance have been created after SP01 and will be included in <strong>SP02</strong>:</p>\r\n<ul>\r\n<li>2410480 - Performance improvement report DPR_MIGRATE_OBJECT_LINK_TYPE</li>\r\n<li>2412777 - Fiori app My Tasks performance optimization</li>\r\n</ul>\r\n<p>The following SAP Notes relevant for performance have been created after SP02 and will be included in <strong>SP03</strong>:</p>\r\n<ul>\r\n<li>2379787 - Performance improvements in the report /RPM/FICO_INT_PLANNING while handling BW data and fetching the capacity data</li>\r\n<li>2464293 - Accessing Assigned Items dashboard from initiative overview screen takes long time</li>\r\n<li>2471803 - Opening a project takes a long time.</li>\r\n<li>2503753 - RPM_DX_ITEM report performance</li>\r\n<li>2523599 - Performance issue while accessing classification hierarchy</li>\r\n<li>2520429 - Performance improvement in the function module /RPM/GET_VALUE_HELP</li>\r\n<li>2521101 - Performance improvement in the method CONSIDER_AUTHORIZATIONS of the class /RPM/CL_ITEM_D_API</li>\r\n<li>2511252 - Performance improvement for icon tooltips</li>\r\n</ul>\r\n<p>The following SAP Notes relevant for performance have been created after SP03 and will be included in <strong>SP04</strong>:</p>\r\n<ul>\r\n<li>2537917 - Performance improvement in the method GET_VALUE_HELP of the class /RPM/CL_COMMON_UI_FIELDSERVICE</li>\r\n<li>2533807 - Long runtime when creating a project from a project template</li>\r\n<li>2550111 - Long loading times when navigating to \"Milestone Trend Analysis\" tab page</li>\r\n<li>2553921 - Navigation to \"Milestone Trend Analysis\" tab page takes a very long time</li>\r\n<li>2561921 - 'Save' of an item under initaitive takes long time.</li>\r\n<li>2548572 - Performance improvement during creation of item under initiative II</li>\r\n<li>2568791 - Changing the fields on Classification Tab during Item Creation from Initiative</li>\r\n<li>2564530 - Initiative Dashboard takes long time to load</li>\r\n<li>2572922 - Unecessary RFC Calls with Destination NONE</li>\r\n<li>2538950 - Performance improvement during resource search</li>\r\n<li>2572099 - Long runtime when saving a project</li>\r\n<li>2567015 - Long runtime when creating a mirrored task</li>\r\n<li>2517654 - Long runtime / high memory consumption because resources are linked to many roles.</li>\r\n<li>2580100 - Performance improvement for Financial/Capacity Roll up code</li>\r\n<li>2588774 - Unnecessary RFC calls for the integration report if PS and PPM are in same system</li>\r\n</ul>\r\n<p>The following SAP Notes relevant for performance have been created after SP04 and will be included in <strong>SP05</strong>:</p>\r\n<ul>\r\n<li>2576092 - Synchronization triggers too redendent calls to /RPM/FINALIZE_ITEM</li>\r\n<li>2623754 - <span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">Send e-mail: Long runtime when adding additional recipients</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2644499 - Performance issue in Financial Planning - Sponsor field</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2593426 - &#8220;Include Subobjects&#8221; button in FI planning UI is slow</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2653987 - Long runtimes when selecting a project element in the project structure</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2633484 - Long runtime when switching between tasks</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2616734 - Dashboard and multi project monitor: Long runtime</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2651031 - Performance of Resource Management Dashboard</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2533719 - \"All Projects\" Dashboard: Long runtime</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2673985 - Performance when searching using text</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2730022 -&#160;Long runtime in class CL_DPR_AUTHORIZATION_SERVICES</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2724655 -&#160;Long runtime occurs when trying to delete tasks</span></li>\r\n</ul>\r\n<p><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">The following SAP Notes relevant for performance have been created after SP05 and will be included in&#160;<strong>SP06</strong>:</span></p>\r\n<ul>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2698151 - Adding multiple objects to Favorites dashboard takes a very long time</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2749520 - Opening item dashboard with large number of custom fields takes more time</span></li>\r\n<li><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">2708199 - Performance optimization of report /RPM/FICO_INT_PLANNING for financial rollup scenario.</span></li>\r\n</ul>\r\n<p><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">The following SAP Notes relevant for performance have been created after SP06 and will be included in&#160;<strong>SP07</strong>:</span></p>\r\n<ul>\r\n<li>2767363 - Send e-mail: Long runtime when calling the \"Add Recipient\" dialog box</li>\r\n<li>2777493 - Long runtime in method CLASS_CONSTRUCTOR of class CL_DPR_BW_EXTRACT_DELTAQUEUE</li>\r\n<li>2624696 - Filling the \"My Tasks\" view (task dashboard) takes a very long time</li>\r\n<li>2764993 - Performance optimization in 'Include Subobjects' in bucket financial planning.</li>\r\n<li>2826720 - Performance issue with Function Module /RPM/GET_VALUE_HELP</li>\r\n</ul>\r\n<p>The following SAP Notes relevant for performance have been created after SP07 and will be included in&#160;<strong>SP08</strong>:</p>\r\n<ul>\r\n<li>2812226 - Performance Issues on 'Switch Hierarchy / Bucket' and 'Move-To' buttons</li>\r\n<li>2803131 - Performance issue while loading the bucket dashboard</li>\r\n<li>2836743 - Performance of the list view/result filter</li>\r\n<li>2841870 - Long run time with Function Module /RPM/FIN_CATEGORY_GETLIST.</li>\r\n<li>2880765 - Report DPR_CHECK_CONSISTENCY: Long runtime</li>\r\n</ul>\r\n<p>The following SAP Notes relevant for performance have been created after SP08 and will be included in&#160;<strong>SP09</strong>:</p>\r\n<ul>\r\n<li>2932087 - Read takes more time if there are more orders assigned to the item</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">The following SAP Notes relevant for performance have been created after SP09 and will be included in&#160;</span><strong style=\"font-size: 14px;\">SP10</strong><span style=\"font-size: 14px;\">:</span></p>\r\n<ul>\r\n<li>3008592 - Task Dashboard: Refresh of \"My Tasks\" has no effect</li>\r\n<li>3000396 - Performance Improvement during Mass Versioning</li>\r\n<li>2988898 - PPM Performance: /RPM/GET_VALUE_HELPS</li>\r\n</ul>\r\n<p>The following SAP Notes relevant for performance have been created after SP10 and will be included in&#160;<strong>SP11</strong>:</p>\r\n<ul>\r\n<li>3032384 - Lange Laufzeit beim &#214;ffnen von Projekten im Multiprojektmonitor</li>\r\n<li>3044717 - Lange Laufzeit beim &#214;ffnen von Projekten im Multiprojektmonitor II</li>\r\n<li>3035180 - Long runtime when exporting GANTT to PDF</li>\r\n</ul>\r\n<p>The following SAP Notes relevant for performance have been created after SP11 and will be included in&#160;<strong>SP12</strong>:</p>\r\n<ul>\r\n<li>3091353 - Long runtimes when navigating to \"Staffing\" tab page</li>\r\n</ul>\r\n<p>Independent from particular Support Packages and corrections, see the following notes/recommendations <strong>for general information about system</strong><br /><strong>performance, product restrictions, project structure and runtimes:</strong><br /><br /><strong>For Project Management:</strong></p>\r\n<ul>\r\n<li>756670 Structure size</li>\r\n</ul>\r\n<ul>\r\n<li>940861 Exploding the organizational structure</li>\r\n</ul>\r\n<ul>\r\n<li>976787 Navigation in expanded project structures</li>\r\n</ul>\r\n<ul>\r\n<li>976789 Threshold value check when saving</li>\r\n</ul>\r\n<ul>\r\n<li>1014150 Loading Java applet for GANTT</li>\r\n</ul>\r\n<ul>\r\n<li>1014381 Saving problems with Java applet, GANTT</li>\r\n</ul>\r\n<ul>\r\n<li>1036233 Performance of the Microsoft Project import</li>\r\n</ul>\r\n<ul>\r\n<li>1104501 Distribution of costs</li>\r\n</ul>\r\n<ul>\r\n<li>1126395 Business Rules for MSP client integration</li>\r\n</ul>\r\n<ul>\r\n<li>1061240 Slow web browser due to JavaScript virus scan</li>\r\n</ul>\r\n<ul>\r\n<li>1149179 BAdI: Dashboard and the view \"Last Used Projects\"</li>\r\n</ul>\r\n<ul>\r\n<li>1564533 Long runtime when opening table view (DMS, SAP_APPL)</li>\r\n</ul>\r\n<p><strong>Restriction List:</strong><br />SAP Note&#160;2340116 contains a list of product restrictions concerning SAP Portfolio and Project Management 1.0 for SAP S/4HANA&#160;.<br /><br /><strong>Performance Behavior (Project Management):</strong><br />In the PDF file attached to this note, some details about the performance behavior of Project Management can be found.<br />Please note that these details are only hints to understand the behavior of the application but they are not a commitment about performance behavior. This is due to the fact that there are a lot of factors influencing performance, e.g. hardware sizing, network bandwidth, client PC sizing, structure of projects, number of project elements, number of project roles, used integrations, and a lot of other factors.<br /><br /><strong>Performance Analysis Checklist (Project Management):</strong><br />SAP Note 1265367 provides a checklist for performance analysis for Project Management.<br /><br /><strong>Sizing Guide (Server Sizing):</strong><br />The Sizing Guide for SAP Portfolio and Project Management&#160;1.0 for SAP S/4HANA can be found under (available at start of Ramp-Up):<br /><br /><a target=\"_blank\" href=\"http://www.sap.com/sizing\">http://www.sap.com/sizing</a><br />&#160;&#160;-&gt; Sizing Guidelines<br />&#160;&#160;&#160;&#160;-&gt;&#160;SAP S/4HANA<br />&#160;&#160;&#160;&#160;&#160;&#160;-&gt;&#160;SAP Portfolio and Project Management 1.0 for SAP S/4HANA&#160;<br /><br />A more detailed sizing assessment for large implementation projects is available on request (contact: See sizing guide).<br /><br /><strong>Client Sizing:</strong><br />Please see SAP Note 1612245 (Frontend Hardware Requirements for Browser-Based SAP UI).</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PPM-FIO (Fiori UI for Portfolio and Project Management)"}, {"Key": "Other Components", "Value": "PPM-PFM (Portfolio Management)"}, {"Key": "Other Components", "Value": "PPM-CF (Common Functions)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D038913)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031049)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002340092/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340092/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340092/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340092/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340092/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340092/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340092/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340092/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340092/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "PPM_S4_Project_Management_Performance_Behaviour.pdf", "FileSize": "11", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000874222016&iv_version=0030&iv_guid=6EAE8B27FB891ED69AD804F101D3A0C1"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2340075", "RefComponent": "PPM-PRO", "RefTitle": "FAQs - SAP Portfolio and Project Management 1.0 for SAP S/4HANA", "RefUrl": "/notes/2340075"}, {"RefNumber": "2340065", "RefComponent": "PPM-PRO", "RefTitle": "PPM 1.0 for S4H: Support package information, notes, and schedule", "RefUrl": "/notes/2340065"}, {"RefNumber": "1816121", "RefComponent": "PPM-PRO", "RefTitle": "Delete Initial Metric values from database", "RefUrl": "/notes/1816121"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2340108", "RefComponent": "PPM-PRO", "RefTitle": "PPM 1.0 for S4H: Configuration Content", "RefUrl": "/notes/2340108 "}, {"RefNumber": "2340116", "RefComponent": "PPM-PRO", "RefTitle": "SAP Portfolio and Project Management SAP S/4HANA: Restrictions", "RefUrl": "/notes/2340116 "}, {"RefNumber": "2340078", "RefComponent": "PPM-PRO", "RefTitle": "PPM 1.0 for S4H: Supported Browsers, Java versions, etc.", "RefUrl": "/notes/2340078 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EPPM", "From": "100", "To": "100", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}