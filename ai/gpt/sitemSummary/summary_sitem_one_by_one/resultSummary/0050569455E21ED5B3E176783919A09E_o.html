<h3>SI2: Logistics_PS</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270261">2270261 - S4TWL - Navigation to Project Builder instead of special maintenance functions</a></strong></p>
<p><strong>Description:</strong></p>
<p>With SAP S/4HANA 1511 the target for links and references in reports CJI3N, CJI4N, CN41, CNS0 is going to the single maintenance transaction CJ20N (Project Builder).</p>
<p><strong>Business Process Impact:</strong></p>
<p>The navigation from multiple transaction screens in project-related reports will now be consolidated into the Project Builder (CJ20N). This affects how project maintenance and updates are managed within the SAP system.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>End user knowledge transfer required to switch from multiple transactions to single maintenance transaction CJ20N.</p>
<p>In case of custom code for special maintenance functions only, customer should consider to enable the custom code for the Project Builder transactions.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2270246">2270246 - S4TWL - Simplification of maintenance transactions</a></p>