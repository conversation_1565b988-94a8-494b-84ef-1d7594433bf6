SAP Note 385099 addresses the issue of serializing data transfer into a BW (Business Warehouse) system, where ODS (Operational Data Store) objects are updated in a way that includes overwriting previous data.

Symptom:
When using an ODS in the BW system that allows overwriting data, it's critical to transfer data in the correct sequence to ensure that the latest modifications to a document are reflected accurately. If a document is changed multiple times before being transferred to the BW system, and these changes are transmitted separately, they must be sent to the BW system in the correct order. However, due to the existing transfer method for logistics data, it's possible (though highly improbable) that a later change to a document could be transferred before an earlier change, leading to inconsistencies.

Other Terms:
LBWE - This term is used in the note but is not elaborated upon.

Reason and Prerequisites:
The collective update technology used as the basis for data transfer does not support the serialization of data transfer required to guarantee the correct order of document changes.

Solution:
The note suggests implementing attached source code corrections to address this issue. However, it also points out that the correct order can only be guaranteed when implemented in conjunction with SAP Note 384211. Additionally, the note mentions that there are different correction instructions for application '17' (Plant Maintenance) and '18' (Customer Service).

In summary, SAP Note 385099 provides a solution for ensuring the serialized transfer of document changes to the BW system by implementing source code corrections and following the instructions included in the supplementary SAP Note 384211. It also highlights specific considerations for applications related to Plant Maintenance and Customer Service.