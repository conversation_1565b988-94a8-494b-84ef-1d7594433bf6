<h3>SI3: Utilities_UIS</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270505">2270505 - S4TWL - IS-U Stock, Transaction and Master Data Statistics (UIS, BW)</a></strong></p>
<p><strong>Description:</strong></p>
<p>IS-U Stock and Transaction Statistics based on Logistics Information System (LIS) is not available within SAP S/4HANA, on-premise edition 1511. Successor functionality: CDS view based virtual data model for Utilities and SAP BW/4HANA content for Utilities. The corresponding functionality and transactions can be found in SAP Business Suite in the SAP Easy Access menu under "Utilities Industry" - "Information System" - "Utilities Information System" - "Stock Statistics" and "Transaction Statistics", respectively. In SAP S/4HANA, "Stock Statistics" and "Transaction Statistics" are not available anymore. Following transactions are not available in SAP S/4HANA: EI35, EP01, EP02. Usage of UIS is indicated by entries in database tables TE789C and TE790C with ACTIVE field set to 'X'. Following BW Data Sources and Master Data attributes are not available anymore, with certain alternatives provided.</p>

<p><strong>Business Process Impact:</strong></p>
<p>The transition removes the IS-U Stock and Transaction Statistics based on LIS, leading to reliance on the successor functionalities such as CDS views for virtual data model for Utilities and SAP BW/4HANA content for Utilities.</p>

<p><strong>Required and Recommended Actions:</strong></p>
<p>
- <strong>Interface Adaption</strong> (During conversion project) - Mandatory: Please also refer to SAP note 2500202 of simplification item S4TWL - BW Extractors in SAP S/4HANA. <br>
- <strong>Business Decision</strong> (Before conversion project) - Mandatory: IS-U Stock and Transaction Statistics based on Logistics Information System (LIS) is not available within SAP S/4HANA. Successor functionality: CDS view based virtual data model for Utilities. <br>
- <strong>Customizing / Configuration</strong> (During conversion project) - Conditional: This activity depends on the decision whether CDS view based queries and info cubes will be used on SAP S/4HANA. <br>
- <strong>User Training</strong> (During or after conversion project) - Conditional: This activity depends on the decision whether CDS view based queries and info cubes will be used on SAP S/4HANA. <br>
</p>

<p><strong>Reference Notes:</strong> 
<br>2500202 - S4TWL - BW Extractors in SAP S/4HANA
</p>