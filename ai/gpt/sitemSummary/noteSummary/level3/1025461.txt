Summary of SAP Note 1025461 - IS-H AT: Clinical Work Station (AT Field Filling)

Symptom:
This note addresses an issue exclusive to the Austrian version of SAP (country version AT) within the Clinical Work Station (CWS), specifically in the "Coding Analyses" view. The problem is that the Length of Stay column is not being populated correctly by the system.

Other Terms:
CWS refers to the Clinical Work Station, and the issue pertains to the Coding Analyses View interface.

Reason and Prerequisites:
The underlying cause of this issue is identified as a program error.

Solution:
The system is intended to calculate the Length of Stay by subtracting the admission date from the discharge date. In cases where the discharge date is not available, the system is supposed to use the current date as a substitute. To resolve the issue, users are instructed to:

1. Implement the attached correction instructions provided in the ZIP files:
   - HW1025461_600.zip for IS-H Version 6.0
   - HW1025461_472.zip for IS-H Version 4.72
It is important to note that these files cannot be downloaded via OSS but only through the SAP Service Marketplace, and users should also refer to SAP Notes 480180 and 13719 for more information on importing attachments.

2. After unpacking and implementing the corrections from the attachment, apply the source code corrections outlined in this SAP Note.

Please note that the disclaimer at the beginning of the note emphasizes that the document has been machine-translated and may not be accurate. It encourages giving feedback on the translation and provides a link to access the original document in German.