{"Request": {"Number": "662441", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 5424, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000662441?language=E&token=38EAE9D16F0BFF520A417989CFF51FB2"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000662441", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000662441/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "662441"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 164}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with High Priority"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "09.02.2018"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-INS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Solution Manager Installation, Configuration and Upgrade"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Solution Manager Installation, Configuration and Upgrade", "value": "SV-SMG-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "662441 - Solution Manager 7.0: SAP Notes for Support Packages"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=662441&TargetLanguage=EN&Component=SV-SMG-INS&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/662441/D\" target=\"_blank\">/notes/662441/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ul>\r\n<li>You want to import individual Support Packages or a Support Package Stack for SAP Solution Manager and want to find out beforehand whether there are already important notes for these Support Packages or this stack.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-left: 30px;\">\r\n<tbody style=\"padding-left: 30px;\">\r\n<tr style=\"padding-left: 30px;\">\r\n<td><strong><span style=\"text-decoration: underline;\">Software Component</span></strong></td>\r\n<td style=\"padding-left: 30px;\"><strong><span style=\"text-decoration: underline;\">Description</span></strong></td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>PC</td>\r\n<td style=\"padding-left: 30px;\">Solution Manager Tool</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>ST-SER</td>\r\n<td style=\"padding-left: 30px;\">Solution Manager Service Tools</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>ST-PI</td>\r\n<td style=\"padding-left: 30px;\">Solution Tools Plug-In</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>ST-SUP</td>\r\n<td style=\"padding-left: 30px;\">Solution Manager Service Desk</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>TMWFLOW</td>\r\n<td style=\"padding-left: 30px;\">Change Request Management</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>ST-ICO</td>\r\n<td style=\"padding-left: 30px;\">Solution Manager Implementation Content</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>SAP_BASIS</td>\r\n<td style=\"padding-left: 30px;\">SAP Basis Component</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>SAP_ABA</td>\r\n<td style=\"padding-left: 30px;\">SAP Application Basis</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>BBPCRM</td>\r\n<td style=\"padding-left: 30px;\">BBP / CRM</td>\r\n</tr>\r\n<tr style=\"padding-left: 30px;\">\r\n<td>CPRXRPM</td>\r\n<td style=\"padding-left: 30px;\">cProjects and others</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\r\n<ul>\r\n<li>This composite SAP Note refers to selected SAP Notes and to information that is directly relevant in connection with importing Support Packages of the software components mentioned above into an SAP Solution Manager system.</li>\r\n<li>We recommend that you implement the SAP Notes specified in this SAP Note because they often deal with possible short dumps, runtime errors, data inconsistencies, and even data losses, and so on.</li>\r\n<li>The target group of this SAP Note is users who import Support Packages into an SAP Solution Manager system or the administrators of these systems.<br /><br /></li>\r\n<li>How to understand and work with collective note 662441?<br /><br /></li>\r\n<ul>\r\n<li>You can either read this SAP Note continuously or search for search terms that you know from transaction SPAM, such as the technical names of the Support Packages to be implemented or the names of the software components in question (the search terms can appear several times in the SAP Note).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For the software components SAP_BASIS, SAP_ABA, and BBPCRM, only the SAP Notes that refer directly to the functions of SAP Solution Manager are mentioned. These SAP Notes are listed below according to the software components of SAP Solution Manager.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The notes are selected as soon as the Support Packages are published. Any SAP Notes created afterwards are only included if they are important, that is, if they refer to the error categories mentioned above. This is not a complete list of all notes that can be implemented for each software package. Refer to the maintenance section in release strategy note 394616 for SAP Solution Manager (it is generally strongly recommended to implement support package stacks in your system at least once a year).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The SAP Notes listed here are generally no longer relevant for subsequent Support Packages. In individual cases, you should check whether a SAP Note has been revised and whether a new version is valid.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>This composite SAP Note does not list any SAP Notes that refer to installation packages and upgrade packages. These cases are dealt with in the guide for the relevant release (see SAP Support Portal at <a target=\"_blank\" href=\"https://support.sap.com/solutionmanager\">https://support.sap.com/solutionmanager</a> →</li>\r\n</ul>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">solutionmanager&quot;, area for the installation guides; alternatively, see SAP Service Marketplace, quick link &quot;/instguides&quot;, area &quot;SAP Components&quot;, &quot;SAP Solution Manager&quot;).</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3>\r\n<p><br />Are there similar scenario-related composite SAP Notes?</p>\r\n<ul>\r\n<ul>\r\n<li>For certain usage scenarios of SAP Solution Manager, there are additional composite SAP Notes that refer to corrections specifically for these scenarios:</li>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"4\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong><span style=\"text-decoration: underline;\">Scenario / topic</span></strong></td>\r\n<td><strong><span style=\"text-decoration: underline;\">Additional notes</span></strong></td>\r\n</tr>\r\n<tr>\r\n<td>Maintenance Optimizer</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Implementation</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Test Workbench</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Quality Center</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Quality Gate Management</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Service Desk</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Solution Monitoring</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Change Request Management</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Delivery of SAP Services</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>RCA / Solution Manager Diagnostics</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>WILY INTROSCOPE</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Central System Administration</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Business Process Monitoring</td>\r\n<td> </td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>SAP Note</th><th>Usage Scenario</th></tr>\r\n<tr>\r\n<td>1024932</td>\r\n<td>Maintenance Optimizer</td>\r\n</tr>\r\n<tr>\r\n<td>949220</td>\r\n<td>Implementation</td>\r\n</tr>\r\n<tr>\r\n<td>896632</td>\r\n<td>Test Workbench</td>\r\n</tr>\r\n<tr>\r\n<td>1059350</td>\r\n<td>Quality Center</td>\r\n</tr>\r\n<tr>\r\n<td>949292</td>\r\n<td>Service Desk</td>\r\n</tr>\r\n<tr>\r\n<td>949293</td>\r\n<td>Solution Monitoring</td>\r\n</tr>\r\n<tr>\r\n<td>907768</td>\r\n<td>Change Request Management (Release 7.0)</td>\r\n</tr>\r\n<tr>\r\n<td>770693</td>\r\n<td>Change Request Management 3.20</td>\r\n</tr>\r\n<tr>\r\n<td>930747</td>\r\n<td>Delivery of SAP Services</td>\r\n</tr>\r\n<tr>\r\n<td>1061383</td>\r\n<td>Solution Manager Diagnostics (Release 7.0, as of Support Package 13)</td>\r\n</tr>\r\n<tr>\r\n<td>1010428</td>\r\n<td>Solution Manager Diagnostics (Release 7.0, before Support Package 12)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<li>These additional SAP Notes focus on a more extensive area of corrections than SAP Note 662441. They relate to a greater extent to the functionality of the corresponding scenario.</li>\r\n</ul>\r\n<ul>\r\n<li>Therefore, the target group for these SAP Notes is also enhanced with, for example, project leads and scenario owners.</li>\r\n</ul>\r\n<ul>\r\n<li>There is an additional composite SAP Note with optional cross-scenario information: 948871.</li>\r\n</ul>\r\n<p><br />Where can I find information about system configuration changes?</p>\r\n<ul>\r\n<li>SAP Note 903274 and its attachment explain the IMG activities that have changed with a new Support Package. This will help you decide if your system configuration needs to be changed with the new support package.</li>\r\n</ul>\r\n<p><br />General update information for the Java and ABAP stack:</p>\r\n<ul>\r\n<li>For the simultaneous import of the Java stack and the ABAP stack, refer to the SUM guide. The guide is available at <a target=\"_blank\" href=\"https://service.sap.com/sltoolset\">https://service.sap.com/sltoolset</a> -> Software Logistics Toolset 1.0 -> Documentation -> System Maintenance -> <a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700000783082011\">Updating SAP Systems Using Software Update Manager &lt;Version></a>. </li>\r\n<li>If you import the latest support package stack into an SAP Solution Manager system, you have the option to update the Java and ABAP stacks individually by using different tools. Use version SP16 or higher of Software Update Manager (SUM) to update the Java stack and transaction SPAM or SAINT to update the ABAP stack. Regardless of the order in which you execute these tools, you must use the same stack configuration file (XML) to update the system consistently.</li>\r\n<ul>\r\n<li>To update the Java stack of the system using SUM, follow the section &quot;Running the Software Update Manager&quot; of the SUM guide (see link to this guide above).</li>\r\n</ul>\r\n</ul>\r\n<p>                   However, you must use the following procedure to start SUM, which ensures that SUM only updates the Java stack of the system:<br />                   1. Before you start SUM, navigate to the file &quot;startup.props&quot;, which you can find in the directory &lt;your SUM directory>\\sdf\\param.<br />                   2. Edit this file by adding the line &quot;ABAP=false&quot;.<br />                   3. Save and close these files.<br />                   4. Start SUM with the SL Common GUI as described in the SUM guide.</p>\r\n<ul>\r\n<ul>\r\n<li>To change the ABAP stack of the system using transaction SPAM or SAINT, follow the explanation on SAP Help Portal at <a target=\"_blank\" href=\"http://help.sap.com/nw701\">http://help.sap.com/nw701</a> -> System Administration and Maintenance Information -> Technical Operations for SAP NetWeaver &lt;language> -> Technical Operations for SAP NetWeaver -> Administration of SAP NetWeaver Systems -> Application Server ABAP (AS ABAP) -> Software Logistics -> Software Maintenance -> Support Package Manager (SPAM) or &quot;SAP Add-On Installation Tool (SAINT)&quot;.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\">Note: Using transaction SPAM or SAINT depends on the type of support packages included in your current support package stack queue.</p>\r\n<p>Notes on Support Packages of SAP Solution Manager Software Components:<br /><br /><br /><strong>SAP Solution Manager 7.0 (Cross-Component Notes)</strong></p>\r\n<ul>\r\n<li>Support Package Stack 17 (08/2008)</li>\r\n<ul>\r\n<li>To be able to send Service Desk messages to SAP, implement SAP Note 1165980 after you import Support Package 16 for SAP_ABA (SAPKA70016).</li>\r\n</ul>\r\n<li>Support Package Stack 15 (01/2008)</li>\r\n<ul>\r\n<li>Stack 15 has been released. Before you can import all Support Packages from Stack 15, you must use transaction SAINT to upgrade the software component ST-SER to the current release if you have not already done so. The current ST-SER release is: ST-SER 700_2008_1 (SAPKITLONB). You can find the installation and upgrade packages on SAP Service Marketplace at http://service.sap.com/swdc, Search for all Categories: Search Term &quot;SAPKITLONB&quot; (search in all categories: Search Term &quot;SAPKITLONB&quot;).<br /><br />If you have already implemented SAPKITLOM6 in your system, the system issues a warning during the implementation of SAPKITLONB: The equivalence support package level is not reached. SAPKITLOO1 should be implemented. You can ignore this warning. Your system is not downgraded.</li>\r\n</ul>\r\n<ul>\r\n<li>Implement Note 1128026 in your Solution Manager system after you have loaded ST 400 Support Package 15 (SAPKITL425). The correction from this note is valid for SAP_ABA 700 Support Package 14 (SAPKA70014), which is required for Support Package 15 and is part of the stack. This correction is required so that you can work in service sessions.</li>\r\n</ul>\r\n<li>Support Package Stack 13 (10/2007)</li>\r\n<ul>\r\n<li>For the software component CPRXRPM, there is a Conflict Resolution Transport (CRT) that must be imported together with Support Package 13 of SAP_ABA 700. Since a CRT cannot be inserted into a support package stack, you must download the CRT &quot;CPRXRPM 400: CRT for SAPKA70013&quot; (SAPK-400C4INCPRXRPM) separately before you download the stack.</li>\r\n</ul>\r\n<ul>\r\n<li>If you load Support Package 13 of SAP_ABA 700 (SAPKA70013), which is required by Support Package 13 of SAT 400 (SAPKITL423), and at the same time load Support Package 4 of ST-SER 700_2007_1 (SAPKITLOM4), you must implement Note 1075012. The implementation of this note triggers the generation of many repository objects. Note that this takes some time. See also SAP Note 1042866, which is mentioned twice below.</li>\r\n</ul>\r\n<li>Support Package Stack 12 (06/2007)</li>\r\n<ul>\r\n<li>Before you can import all Support Packages from Stack 12, you must use transaction SAINT to upgrade the software component ST-SER to the current release if you have not already done so. Before this upgrade, you must implement Note 1042866 (for details, see SAPKITL422 below). The current ST-SER release is: ST-SER 700_2007_1 (SAPKITLOLB). You can find the installation and upgrade packages on SAP Service Marketplace under the path: http://service.sap.com/swdc, Search for all Categories: Search Term &quot;SAPKITLOLB&quot; (Search in All Categories: Search Term &quot;SAPKITLOLB&quot;).</li>\r\n</ul>\r\n<li>Support Package Collection 07 and Support Package Stack 06 (08/2006)</li>\r\n<ul>\r\n<li>With the correction from Note 973995, you can create Service Desk messages correctly again after you have loaded ST 400 Support Package 07 (SAPKITL417) or implemented Note 963002. The correction is valid for SAP_BASIS 700 SP09 (SAPKA70009) required for SP07.</li>\r\n</ul>\r\n<ul>\r\n<li>By implementing note 964651 you avoid possible inconsistencies when creating Service Desk messages. This correction is valid for CRM 5.0 SP05 (SAPKU50005) which is required for ST 400 SP06 (SAPKITL416) and the latter is required for ST 400 SP07 (SAPKITL417).</li>\r\n</ul>\r\n<li>Support Package Stack 06 (08/2006) and earlier</li>\r\n<ul>\r\n<li>Note 954496 delivers a correction for the Post Processing Framework (PPF) that prevents a runtime error. This note is valid for SAP_BASIS 700 SP08 (SAPKB70008) required for ST 400 SP06 (SAPKITL416).</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Note 948129 prevents a short dump that may occur when you call the Service Session Workbench directly or indirectly. The correction is also valid for SAP_ABA 700 Support Package 08 (SAPKA70008), which is required by ST 400 Support Package 06 (SAPKITL416). This correction also applies indirectly to older ST 400 Support Packages (SAPKITL415, SAPKITL414, SAPKITL413, SAPKITL412, SAPKITL411).</li>\r\n</ul>\r\n<li>Support Package Collection 05 (05/2006)</li>\r\n<ul>\r\n<li>For Service Desk, Change Request Management and Issue Management, you must implement Note 946961 to avoid a short dump when creating or changing messages, change requests, or issues. The correction is valid for CRM 5.0 SP04 (SAPKU50004) required by ST400 SP05 (SAPKITL415).</li>\r\n</ul>\r\n<li>Support Package Collection 03 (03/2006)</li>\r\n<ul>\r\n<li>Note 909236 prevents possible data inconsistencies in the management of change requests. This correction is valid for CRM 5.0 SP03 (SAPKU50003). Support Package SAPKU50003 is required by ST400 Support Package 03 (SAPKITL413).</li>\r\n</ul>\r\n<li>Support Package Stack 02 (02/2006)</li>\r\n<ul>\r\n<li>SAP_BASIS 700 Support Package 06 (SAPKB70006) delivered deletions that lead to generation errors when you import CPRXRPM 400 Support Package 02 (SAPK-40002INCPRXRPM) in transaction SPAM if the generation is activated using SPAM. You can ignore this generation error. You can recognize the generation error by the keywords &quot;CL_DPR_SC_SERVICES&quot; and &quot;/RPM/CL_BUCKET_O&quot;.</li>\r\n</ul>\r\n<ul>\r\n<li>Note 725658 prevents a possible short dump. The correction is valid for CRM 5.0 Support Package 02 (SAPKU50002), which is required by ST400 Support Package 02 (SAPKITL412).</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Note 905469 provides a language package that you should implement again in the Solution Manager after you import CRM 5.0 Support Package 02 (SAPKU50002). Support Package SAPKU50002 is required by ST400 Support Package 02 (SAPKITL412).</li>\r\n</ul>\r\n<li>Support Package Collection 01 (12/2005)</li>\r\n<ul>\r\n<li>SAP Note 908186 provides ramp-up customers with an overview of selected SAP Notes for the target configuration of the upgrade to the new release. The leading Support Package is Support Package 01 of ST 400 (SAPKITL411, also SAPKITL40G).</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 7.0 (software component ST 400)</strong></p>\r\n<ul>\r\n<li>ST 400 SP16 (SAPKITL426)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For SP16 (SAPKITL426) it is highly recommended to upgrade your ST-SER to release 700_2008_1 or higher. To do this, use transaction SAINT instead of transaction SPAM to correct your system on SAP Solution Manager 7.0. If you try to repair your system using transaction SPAM and your ST-SER release is  700_2007_1 or lower, does the system return in transaction SPAM in phase ADDON_CONFLICTS_? one of the following information:</li>\r\n</ul>\r\n</ul>\r\n<p>                    a) Conflicts between Add-On ST-SER 700_2005_2 and Support Packages SAPKITL426 Include CRT<br />                    b) Conflicts between Add-On ST-SER 700_2006_1 and Support Packages SAPKITL426 Include CRT<br />                    c) Conflicts between Add-On ST-SER 700_2006_2 and Support Packages SAPKITL426 Include CRT<br />                    c) Conflicts between Add-On ST-SER 700_2007_1 and Support Packages SAPKITL426 Include CRT<br />                    Note that there is no physical Conflict Resolution Transport (CRT) available to resolve this conflict (there is also no plan to provide such a CRT). You must correct your system using transaction SAINT and not using SPAM.<br />                    1. Delete your SPAM queue by choosing &quot;Display/Define&quot; in transaction SPAM.<br />                    2. Download all ST-SER delta upgrade packages from the Software Download Center to your EPS inbox.<br />                    3. Call transaction SAINT and choose &quot;Start&quot;.<br />                    4. Select ST-SER 700_2008_1 or higher and click on continue button.<br />                    All required component packages are automatically inserted into a valid SAINT queue.</p>\r\n<ul>\r\n<li>ST 400 SP15 (SAPKITL425)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For SP15 (SAPKITL425) it is highly recommended to upgrade your ST-SER to release 700_2007_1 or higher. To do this, use transaction SAINT instead of transaction SPAM to upgrade your system to SAP Solution Manager 7.0 Stack 15 (01/2008). If you try to repair your system using transaction SPAM and your ST-SER release is 700_2006_1 or lower, does the system return in transaction SPAM in phase ADDON_CONFLICTS_? one of the following information:</li>\r\n</ul>\r\n</ul>\r\n<p>                    a) Conflicts between Add-On ST-SER 700_2005_2 and Support Packages SAPKITL425 Include CRT<br />                    b) Conflicts between Add-On ST-SER 700_2006_1 and Support Packages SAPKITL425 Include CRT<br />                    c) Conflicts between Add-On ST-SER 700_2006_2 and Support Packages SAPKITL425 Include CRT<br />                    Note that there is no physical Conflict Resolution Transport (CRT) available to resolve this conflict (there is also no plan to provide such a CRT). You must correct your system using transaction SAINT and not using SPAM.<br />                    1. Delete your SPAM queue by choosing &quot;Display/Define&quot; in transaction SPAM.<br />                    2. Download all ST-SER delta upgrade packages from the Software Download Center to your EPS inbox.<br />                    3. Call transaction SAINT and choose &quot;Start&quot;.<br />                    4. Select ST-SER 700_2007_1 or higher and click on continue button.<br />                    All required component packages are automatically inserted into a valid SAINT queue.</p>\r\n<ul>\r\n<ul>\r\n<li>Joint import of SAPKITL425 and SAPKITL422 into a SAINT or SPAM queue<br />To avoid the following error message during the main import:<br />duplicate key error during insert into table AGSTWB_RSTAT_VAL occured.<br />Download the attached allowance file AITL422.SAR to your &lt;DIR_TRANS> directory and extract it with SAPCAR -xvf AITL412.SAR.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li></li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1128453<br />This note summarizes some corrections regarding the implementation guide in the Solution Manager system (SAP Reference IMG, transaction SPRO). If you perform configuration activities that are based on Support Package 15, you do not need to take this SAP Note into account.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Support Package 15 (SAPKITL425) adds work centers to SAP Solution Manager. For details, check the development news for Support Package 15 on SAP Service Marketplace at http://service.sap.com/solutionmanager -> Medie Library -> Technical Papers -> Development News SAP Solution Manager SPS15. Currently, only these work centers are released for customers with Support Package 15: Implementation &amp; Upgrade, Solution Landscape &amp; Operation Setup, Change Lifecycle Management, Incident Management, Service Delivery, Job Scheduling Management, System Monitoring, System Administration, and System Landscape Management. The work centers are released to be called by transaction SOLMAN_WORKCENTER. This transaction is part of the user menu of the role SAP_SMWORK_BASIC. The other three entries in this user menu are not released for use.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1136916 (Work Center)<br />If you implement Note 1136916 for Support Package 15 (SAPKITL425), solutions are created in a work center in the logon language. (Scenario-specific note  949293 lists more work center relevant functional corrections for Solution Monitoring scenario. These additional notes correct issues that are considered less severe.)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Notes 1130172, 1122966 and 1137683 (Maintenance Optimizer):</li>\r\n</ul>\r\n</ul>\r\n<p>                    Implement Note 1130172 after loading Support Package 15 (SAPKITL425).<br />                    Note 1122966 is relevant for every customer who wants to import ERP Enhancement Packages using the Maintenance Optimizer. This SAP Note lists required correction notes that must be implemented in addition to a specific Support Package level for SAP Solution Manager 7.0.<br />                    Note 1137683 provides customers who use the Maintenance Optimizer with an overview of selected SAP Notes that relate to the configuration and use of the Software Lifecycle Manager. This SAP Note describes deviations and enhancements to the IMG and KM documentation.</p>\r\n<ul>\r\n<ul>\r\n<li>Note 1138350 (Change Request Management)<br />Note 1138350 is an urgent correction for the Change Request Management scenario.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1137338 (Expertise on Demand)<br />Implement this note if you are using Issue Management Expertise on Demand functionality. Note the prerequisites described in this SAP Note.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Notes 1137739, 1138046, 1138049 (Issue Management)<br />Implement these notes if you are using transaction SOLMAN_ISSUE_MGMT (additional keyword: Collaboration Platform).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1128026 (Service Level Reporting)<br />Implement this SAP Note to avoid a runtime error for SL reporting.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1140822<br />After you have imported Support Package 15 (SAPKITL425), the function for managing multiple SAP customer numbers is active. As a result, the system issues error message AI_SC_EN 097 &quot;SAP customer number for installation number &amp;1 unknown&quot;. Implement note 1140822 to disable this functionality.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP14 (SAPKITL424)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1106236<br />If you are using Solution Manager Change Request Management, you need to implement note 1106236. The condition check regarding the successful export of the transport requests must be corrected with this note after you have imported Support Package 14.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP13 (SAPKITL423)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1084173<br />With Support Package 13, the scheduling of background jobs for central system administration (Central System Administration) has been changed. After applying Support Package 13, perform the activities described in SAP Note 1084173.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP12 (SAPKITL422)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1077050<br />Before you import Support Package 12 into your Solution Manager system, check whether you want to import the updated EPS file for Support Package 12 (EPS file name: CSR0120031469_0027444.PAT), which can be downloaded since July 23, 2007. If you want to import an EPS file other than the updated EPS file for Support Package 12, see SAP Note 1077050, which provides an overview of the procedure. Importing an older EPS file for Support Package 12 together with older Support Packages for ST 400 can lead to errors during the import process.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1067602<br />The interface between the Maintenance Optimizer and SAP Service Marketplace must be enhanced by implementing SAP Note 1067602. This enhancement is required for calculating the support packages to be downloaded and for generating a stack XML file for the installation of enhancement packages.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1042866<br />If you have installed ST 400 Support Package 12 (SAPKITL422) or higher, the termination &quot;Syntax error in program &quot;RDSVASASPLI_CBP____________062&quot; (syntax error in program &quot;RDSVASASPLI_CBP____________062&quot;) may occur when opening the business process details in the Solution Directory. To avoid this dump, implement the source code correction from SAP Note 1042866 if you are on ST-SER 700_2006_2 and before you install ST-SER 700_2007_1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1052319<br />This correction prevents a short dump during System Monitoring.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1059271<br />The general settings of transaction SOLUTION_MANAGER are read by several SAP Solution Manager scenarios. This SAP Note prevents an error that may occur when reading these settings.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP11 (SAPKITL421)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1042815<br />This SAP Note prevents SMSY data from being written back to the System Landscape Directory (SLD).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP09 to SP10 (SAPKITL419, SAPKITL420)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1012979<br />This SAP Note prevents a possible data inconsistency between SMSY data and solutions.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP06 to SP10 (SAPKITL416, SAPKITL417, SAPKITL418, SAPKITL419, SAPKITL420)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1015744<br />This SAP Note correction prevents system monitoring settings from being lost, which is possible under certain circumstances: in this case, the system displays the same name  for all checks. If you save at this point, the original names of the checks are overwritten and the changes to the settings for System Monitoring are not applied.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP09 (SAPKIT419)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1005323<br />This note prevents a runtime error in job &quot;SM:CSA SESSION REFRESH&quot;.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1006711<br />Without this SAP Note, data may be lost if a system is not available during a data refresh in transaction SMSY.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1011376<br />Customers using Service Desk for Service Provider and action profile AI_SDK_STANDARD need to implement this note to be able to create and save incidents.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1008717<br />The SAP Solution Manager functions using the HTML Viewer Control (such as Issue Management and Maintenance Optimizer) will not work without this note if you have implemented SAP_BASIS 700 Note 975872 or Support Package 10 (SAPKB70010) and you are using SAP GUI 640 Patch Level 22.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP06 to SP08 (SAPKITL416, SAPKITL417, SAPKITL418)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1012979<br />This SAP Note prevents a possible data inconsistency between SMSY data and solutions. You only need to implement note 1012979 if you have implemented note 987122 with support package SP06, SP07 or SP08.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP06 (SAPKITL416)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 966398<br />The correction of this note prevents a short dump that can occur when executing the &quot;Initial Load IBase&quot; in transaction SOLUTION_MANAGER.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP05 (SAPKITL415)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 946605<br />Issue Management requires the implementation of this note to avoid a short dump that occurs when updating the status.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP03 to SP05 (SAPKITL413, SAPKITL414, SAPKITL415)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 948644<br />This SAP Note prevents the occurrence of a runtime error in the Solution Directory.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP03 (SAPKITL413)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 919790<br />This note contains an additional correction for SP03.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP01 to SP03 (SAPKITL411, SAPKITL412, SAPKITL413)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 929928<br />Without this correction, the issue contexts for Solution Manager 3.2 issues are lost after the upgrade.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 928985<br />Implement this note to avoid a runtime error during the creation of a Service Desk message.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 400 SP02 (SAPKITL412)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 917896<br />This SAP Note prevents a short dump. You must implement this SAP Note before you migrate the solutions from Release 3.1 to 7.0.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Notes 919790, 912618, 910177<br />These SAP Notes provide corrections for the use of solutions, issues, top issues, expertise on demand, and business process monitoring.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 906340<br />Support Package 02 provided a performance improvement when working with solutions. This note provides a correction for this optimization.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><br /><strong>SAP Solution Manager 7.0 (software component ST-ICO)</strong></p>\r\n<ul>\r\n<li>ST-ICO 150 SP10 (SAPK-1507AINSTPL)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>To install this package, you require at least an R3trans version dated June 11, 2007.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><br /><strong>SAP Solution Manager 7.0 (software component ST-SER)</strong></p>\r\n<ul>\r\n<li>ST-SER 700_2007_1 Support Package SP01 (SAPKITLOM1)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If you import Support Package SAPLITLOM1 together with the delta upgrade package SAPKITLOLB in a SAINT queue, an error occurs during the test import:<br />The function DSWP_BPMO_STXT_GET (DSWP_BPM_FUNH 04) does not fit into the existing function group (DSWP_BPMO 12).<br />Ignore this error by choosing the option &quot;Ignore Test Import Errors&quot; in the menu &quot;Extras&quot; in transaction SAINT.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><br /><strong>SAP Solution Manager 3.2 (Cross Notes)</strong></p>\r\n<ul>\r\n<li>Support Package Stack 15 (12/2006) and earlier</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>By implementing note 964651 you avoid possible inconsistencies when creating Service Desk messages. This correction is valid for CRM 3.1 SP12 and SP13 (SAPKU31012, SAPKU31013) required by ST-SUP 320 SP13, SP14 and SP15 (SAPKITLC53, SAPKITLC54, SAPKITLC55).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Support Package Collection 07 and Support Package Stack 13 (06/2006)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>With the correction from Note 973995, you can create Service Desk messages correctly again after you have loaded ST 320 Support Package 14 (SAPKITL324) or implemented Note 963002. The correction is valid for SAP_BASIS 620 Support Package 60 (SAPKA62060), which is required for Support Package 14.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>By implementing note 964651 you avoid possible inconsistencies when creating Service Desk messages. This correction is valid for CRM 3.1 SP12 (SAPKU31012), which is (indirectly) required for ST-SUP 320 SP13 (SAPKITLC53), which in turn is required for ST-SUP 320 SP14 (SAPKITLC54).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Support Package Stack 13 (06/2006) and earlier</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Note 948129 prevents a short dump that may occur when you call the Service Session Workbench directly or indirectly. This correction is also valid for SAP_ABA 620 Support Package 59 (SAPKA62059), which is required by ST 320 Support Package 13 (SAPKITL323). This correction also applies indirectly to older ST 320 Support Packages (SAPKITL322, SAPKITL321, SAPKITL320, SAPKITL319, SAPKITL318, SAPKITL317, SAPKITL316, SAPKITL315, SAPKITL314, SAPKITL313, SAPKITL312, SAPKITL311).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Support Package Collection 11 (12/2005)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Note 902804 enables the saving of texts from CRM transactions again. This affects Service Desk (Support Package 11 for ST-SUP 320, SAPKITLC51), Change Request Management (Support Package 11 for TMWFLOW 320, SAPK-32011INTMWFLOW), and Issues (Support Package 11 for ST 320, SAPKITL321). The note can be implemented with Support Package 12 of BBPCRM 310 (SAPKU31012).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Support Package Stack 10 (10/2005)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>This stack contains Support Package 10 (SAPKITL320) for ST 320, which may lead to data loss in its first version during the import. For the EPS file name of the corrected, newly created EPS file, see SAP Note 888413.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Support Package Stack 08 (06/2005)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Before you import the Support Packages from Stack 08, you must use transaction SAINT to upgrade some software components to the current release if you have not already done so. These current releases are: ST-SER 620_2005_1 (SAPKITLO8B) and ST-A/PI 01F_CRM315 (SAPKITAC27). You can find the installation and upgrade packages on SAP Service Marketplace under the path: http://service.sap.com/swdc, Search for all Categories: &quot;SAP Solution Manager 3.2&quot;, SAP Solution Manager 3.2 (Installation): Open the node Content for ST-SER, node Plug-In Satellite System for ST-A/PI.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 3.2 (software component ST)</strong></p>\r\n<ul>\r\n<li>ST 320 SP12 to SP15 (SAPKITL322, SAPKITL323, SAPKITL324, SAPKITL325)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1015744<br />This note correction prevents system monitoring settings from being lost, which is possible under certain circumstances: in this case, the system displays the same name  for all checks. If you save at this point, the original names of the checks are overwritten and the changes to the settings for System Monitoring are not applied.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP12 to SP13 (SAPKITL322, SAPKITL323)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 948644<br />This SAP Note prevents the occurrence of a runtime error in the Solution Directory.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP04 to SP12 (SAPKITL314, SAPKITL315, SAPKITL316, SAPKITL317, SAPKITL318, SAPKITL319 SAPKITL320, SAPKITL321, SAPKITL322)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 928985<br />Implement this note to avoid a runtime error during the creation of a Service Desk message.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP11 (SAPKITL321)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 917896<br />This SAP Note prevents a short dump. You must implement this SAP Note before you migrate the solutions from Release 3.1 to 3.2.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 901063<br />This SAP Note corrects an error in the Expertise on Demand function. The corrections also include ST-SUP 320 SP11 (SAPKITLC51).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 Support Package 01 to Support Package 11 (SAPKITL311, SAPKITL312, SAPKITL313, SAPKITL314, SAPKITL315, SAPKITL316, SAPKITL317, SAPKITL318, SAPKITL319, SAPKITL320, SAPKITL321, also SAPKITL30A or SAPKITL30A). SAPKITL30B)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 900891<br />To upgrade your SAP Solution Manager 3.1 to 3.2 (initial configuration: ST 310 with support package level 16 and 16 (inclusive), you <strong>must</strong> implement this note after the upgrade. You can still do this later. This is not necessary if you have already implemented this SAP Note in Release 3.1 before the upgrade.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 900585<br />After generating test plans for Solution Manager projects, data may be lost in the corresponding test packages. This SAP Note prevents this data loss. You can implement this SAP Note in Solution Manager 3.2 up to and including Support Package 57 for SAP_BASIS 620 (SAPKB62045, SAPKB62046, SAPKB62047, SAPKB62048, SAPKB62049, SAPKB62050, SAPKB62051, SAPKB62052, SAPKB62053, SAPKB62054, SAPKB62056, SAPK62055).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 900005<br />If you delete certain data in transaction SMSY, dependent systems are also deleted in the solutions. This is incorrect. This SAP Note, which can be implemented automatically, prevents this.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP08 to SP11 (SAPKITL318, SAPKITL319, SAPKITL320 and SAPKITL321)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 896632<br />This SAP Note contains a collection of SAP Notes that correct known problems for test plans in the Solution Manager. For performance reasons, it is advisable to import Support Package 10 or higher if you want to work with test plans.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP10 (SAPKITL320)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 888413<br />Since the first version of Support Package 10 can lead to a loss of data, use the corrected, newly created EPS file to import Support Package 10. For the correct EPS file name, see SAP Note 888413.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 877547<br />After you have implemented this SAP Note, you can create solutions again.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP09 (SAPKITL319)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Notes 866280, 868998, 863398, 865684, 863101, 869438<br />These notes prevent errors in specific Solution Manager functions (e-learning management, roadmaps, projects, reporting).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP08 (SAPKITL318)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 847366<br />This SAP Note contains the corrected template and the macro for generating the test report document using Microsoft Word 2000. This SAP Note can be implemented in a system with Solution Manager 3.2 and Support Package 52 for SAP_BASIS (SAPKB62052).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 Support Package 01 up to Support Package 06 (SAPKITL311, SAPKITL312, SAPKITL313, SAPKITL314, SAPKITL315, SAPKITL316, also SAPKITL30A or SAPKITL30B)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 824893<br />It is possible that only a new start EarlyWatch alert is created instead of a successor EarlyWatch Alert. This SAP Note corrects this behavior.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP05 and SP06 (SAPKITL315, SAPKITL316)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 826080<br />In SAP Solution Manager sessions, data may disappear from the display. They still exist in the database but are not available for the current processing.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP04 (SAPKITL314)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 809311<br />Short dumps may occur after saving a project in the Solution Manager configuration phase. This note prevents this and can be implemented automatically.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP02 (SAPKITL312)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 794704<br />Certain system data may not be displayed correctly in Solution Manager Operations and Solution Directory. The note describes a manual correction for this issue.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 320 SP01 (SAPKITL311)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 788295<br />After you import Support Package 01, a runtime error may occur in transaction DSWP during the migration of a solution to the solution directory. A manual correction prevents this error.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Notes 783317 and 783189<br />After you import Support Package 01, errors may occur in transaction DSWP in connection with the internal data model. The two SAP Notes prevent this.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 783225<br />After saving a solution in the system landscape that contains non-ABAP components, the system issues an error message informing you that you must enter an installation number. For this reason, it is not possible to save the solution. This SAP Note describes manual preparations and contains correction instructions that can be implemented automatically.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 3.2 (software component TMWFLOW)</strong></p>\r\n<ul>\r\n<li>TMWFLOW 320 Support Package 09 (SAPK-32009INTMWFLOW)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 866051<br />This SAP Note corrects an error that may occur when you work with projects in the Solution Manager (ST 320 Support Package 09, SAPKITL319).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>TMWFLOW 320 SP04, SP05, SP08 (SAPK-32004INTMWFLOW, SAPK-32005INTMWFLOW, SAPK-32008INTMWFLOW)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 770693<br />For information about Change Request Management, see SAP Note 770693.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>TMWFLOW 320 SP06 (SAPK-32006INTMWFLOW)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 826750<br />An incorrect RFC type was used to determine the RFC destination for the remote programs. As a result, a program may terminate in the background if an online RFC connection is used.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Notes 817289, 817518, and 815999<br />Implement these notes after implementing TMWFLOW 320 SP05.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 804821<br />If a maximum of Support Package 48 (SAPKA62048) for the software component SAP_ABA has been implemented, follow Note 804821.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 3.2 (software component ST-SUP)</strong></p>\r\n<ul>\r\n<li>ST-SUP 320 Support Package 11 (SAPKITLC51)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 901063<br />This SAP Note corrects an error in the Expertise on Demand function. The correction also includes ST 320 Support Package 11 (SAPKITL321).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-SUP 320 SP09 (SAPKITLC49)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 870063<br />This SAP Note contains customizing for issues (ST 320 Support Package 09, SAPKITL319).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-SUP 320 SP08 (SAPKITLC48)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 863679<br />After you have imported the package ST-SUP 08, Service Desk messages can no longer be created correctly.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-SUP 320 SP01 to SP10 (SAPKITLC41, SAPKITLC42, SAPKITLC43, SAPKITLC44, SAPKITLC45, SAPKITLC46, SAPKITLC47, SAPKITLC48, SAPKITLC49, SAPKITLC50, also SAPKITLC3A and SAPKITLC3B)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 845433<br />With this correction, CRM transactions (CRM incidents) can no longer be deleted if they are linked to a basis message. The note is applicable in Solution Manager 3.2 up to and including BBPCRM 3.1 SP11 (SAPKU31009, SAPKU31010, SAPKU31011).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-SUP 320 Support Package 01 to Support Package 06 (SAPKITLC41, SAPKITLC42, SAPKITLC43, SAPKITLC44, SAPKITLC45, SAPKITLC46, also SAPKITLC3A or SAPKITLC3B)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 832810<br />This SAP Note contains missing translations for the Service Desk and Change Request Management. The translations are Customizing entries in a BC Set.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 3.2 (software component ST-SER)</strong></p>\r\n<ul>\r\n<li>ST-SER 620_2005_1 Support Package 03 (SAPKITLO93)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>With SP03, multilingual options (Chinese, English and Japanese) are offered for different sessions in SAP Solution Manager 3.2.<br />Support Package 03 contains the relevant language package delivery. If you want to run the multilingual Solution Manager sessions in Chinese or Japanese, please run report RSLANG20 to delete the old language load and activate the new load after applying SP03.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-SER 620_2005_1 SP01 to SP03 (SAPKITLO91, SAPKITLO92, SAPKITLO93)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 863853<br />This SAP Note contains important corrections for the display of issues in sessions and reports.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-SER 320 SP01 (SAPKITLO71)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 787314<br />If ST 310 Support Package 16 (SAPKITL28) or higher is installed, the termination RAISE_EXCEPTION OBJECT_TYPE_NOT_FOUND occurs in the background job SM:EXEC SERVICES.in Early Watch Alert processing. This SAP Note prevents the termination.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>ST-SER 320 was triggered by ST-SER 620_2005_1.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 3.2 (software component ST-ICO)</strong></p>\r\n<ul>\r\n<li>ST-ICO 150 SP10 (SAPK-1506AINSTPL)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>To install this package, you require at least an R3trans version dated June 11, 2007.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-ICO 140 Support Package 01 (SAPK-14001INSTPL)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 826586<br />The ASAP implementation roadmap delivered with ST-ICO 140 is not displayed correctly. The report in the correction instructions corrects the problem.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 3.1 (Cross Notes)</strong></p>\r\n<ul>\r\n<li>Support Package Collection 27 (December 2005)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Note 902804 enables the saving of texts of CRM transactions again. This affects Service Desk (ST-SUP 310 SP10; SAPKITLC20) and Issues (ST 27 for ST 310, SAPKITL297).  You can implement the note with BBPCRM 310 SP12 (SAPKU31012).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Support Package Stack 24 (06/2005)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Before you import the Support Packages from Stack 24, you must use transaction SAINT to upgrade some software components to the current release if you have not already done so. These current releases are: ST-SER 620_2005_1 (SAPKITLO8B), ST-A/PI 01F_CRM315 (SAPKITAC27), ST-PI 2005_1_620 (SAPKITLQGB), and PI_BASIS 2004_1_620 (SAPKINBA7A). You can find the installation and upgrade packages on SAP Service Marketplace under the path: http://service.sap.com/swdc, Search for all Categories: &quot;SAP Solution Manager 3,1&quot;, SAP Solution Manager 3.1 (Installation): Open the node Content for ST-SER; Search for all Categories: &quot;ST-A/PI&quot;, &quot;ST-PI&quot;, &quot;PI_BASIS&quot;.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 3.1 (software component ST)</strong></p>\r\n<ul>\r\n<li>ST 310 SP20 to SP28 (SAPKITL290, SAPKITL291, SAPKITL292, SAPKITL293, SAPKITL294, SAPKITL295, SAPKITL296, SAPKITL297, SAPKITL298)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 928985<br />Implement this note to avoid a runtime error when creating Service Desk messages.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 310 SP16 to SP26 (SAPKITL286, SAPKITL287, SAPKITL288, SAPKITL289, SAPKITL290, SAPKITL291, SAPKITL292, SAPKITL293, SAPKITL294, SAPKITL295, SAPKITL296)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 900891<br />You must implement this SAP Note before you upgrade your SAP Solution Manager from version 3.1 to a higher release.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 310 Support Package 25 (SAPKITL295)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Notes 866280, 868998 and 869438<br />These notes avoid errors in specific Solution Manager functions (E-Learning Management, Reporting).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 310 Support Package 20 (SAPKITL290)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 822082<br />After you import Support Package 20, you cannot create support notifications in the Solution Manager, Roadmap, Test Workbench, and transaction NOTIF_CREATE. Note 822082 corrects this error.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 310 Support Package 02 to Support Package 16 (SAPKITL272, SAPKITL273, SAPKITL274, SAPKITL275, SAPKITL276, SAPKITL277, SAPKITL278, SAPKITL279, SAPKITL280, SAPKITL281, SAPKITL282, SAPKITL285, SAPKITL283, SAPKITL28K28K28K28</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 775159<br />After you delete a server in transaction SMSY, the system is deleted from the solution. The note can be implemented automatically.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 310 Support Package 15 (SAPKITL285)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 769465<br />After you have imported Support Package 43 (SAPKB62043) of the software component SAP_BASIS 620 into the Solution Manager system, a short dump may occur when you create a test plan or the field for the system role cannot be selected. The note needs to be implemented only once. Alternatively, you can import Support Packages 44 (SAPKB62044) and 45 (SAPKB62045).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST 310 SP05 (SAPKITL275)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 673106<br />After you import Support Package 05, a short dump occurs when you call the operation setup for the solution. The note needs to be implemented only once.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 3.1 (software component ST-SUP)</strong></p>\r\n<ul>\r\n<li>ST-SUP 310 SP01 to SP09 (SAPKITLC11, SAPKITLC12, SAPKITLC13, SAPKITLC14, SAPKITLC15, SAPKITLC16, SAPKITLC17, SAPKITLC18, SAPKITLC19, also SAPKITLC10)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 845433<br />With this correction, CRM transactions (CRM incidents) can no longer be deleted if they are linked to a basis message. The note is applicable in Solution Manager 3.1 up to and including Support Package 11 for BBPCRM 3.1 (SAPKU31006, SAPKU31007, SAPKU31008, SAPKU31009, SAPKU31010, SAPKU31011).</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 3.1 (software component ST-SER)</strong></p>\r\n<ul>\r\n<li>ST-SER 620_2005_1 Support Package 03 (SAPKITLO93)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Support Package 03 provides the multilingual options (Chinese, English and Japanese) for different sessions for SAP Solution Manager 3.2.<br />Support Package 03 contains the language delivery. If you want to execute the multilingual SAP Solution Manager sessions in Chinese or Japanese, execute the report RSLANG20 to delete the old language load and activate the new load after you import Support Package 03.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-SER 620_2005_1 SP01 to SP03 (SAPKITLO91, SAPKITLO92, SAPKITLO93)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 863853<br />This SAP Note contains important corrections for the display of issues in sessions and reports.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-SER 320 SP01 (SAPKITLO71)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 787314<br />If ST 310 SP16 (SAPKITL28) or higher is installed, the termination RAISE_EXCEPTION OBJECT_TYPE_NOT_FOUND occurs in Early Watch Alert processing in the background job SM:EXEC SERVICES. This SAP Note prevents the termination.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>ST-SER 320 has been replaced by ST-SER 620_2005_1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 611536<br />If you have not loaded any service definitions from SAPOSS for a long time, generation errors may occur when you import Support Packages.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>ST-SER 311 has been replaced by ST-SER 320.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Manager 3.1 (software component ST-ICO)</strong></p>\r\n<ul>\r\n<li>ST-ICO 150 SP10 (SAPK-1506AINSTPL)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>To install this package, you require at least an R3trans version dated June 11, 2007.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-ICO 140 Support Package 01 (SAPK-14001INSTPL)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 826586<br />The ASAP implementation roadmap delivered with ST-ICO 140 is not displayed correctly. The report specified in the corrections of this SAP Note solves the problem.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li></li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>SAP Solution Tools Plug-In (software component ST-PI)</strong></p>\r\n<ul>\r\n<li>This software component is relevant for the Solution Manager system and for the connected satellite systems.</li>\r\n</ul>\r\n<ul>\r\n<li>ST-PI 2005_1_[46C, 620, 640] SP06, ST-PI 2005_1_[700] SP04 and ST-PI 2005_1_[710] SP02</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1049478<br />This note contains both report /SDF/RSORAVSE and report /SDF/RSORAVSH. The report /SDF/RSORAVSE collects the statistical data from the Oracle statistics table V$SYSTEM_EVENT once an hour using the batch job BTCH_RSORAVSH and this is displayed by the report /SDF/RSORAVSH. If these reports are missing, the batch job terminates with a dump. If you do not implement this note, short dumps will occur every hour in an SAP system connected to the ORACLE database.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-PI 003C_40B SP02 (SAPKITLPL2), ST-PI 003C_45B SP02 (SAPKITLPM2), ST-PI 003C_46C SP02 (SAPKITLPO2), ST-PI 003C_46D SP02 (SAPKITLPP2), ST-PI 003C_610 ST02 (SAPKIT-P00KPQ2)<br />Not relevant for ST-PI 003C_46B SP02 (SAPKITLPN2).</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 766425<br />This note contains a correction for report /SDF/RSORAVSE. If you do not implement this correction, the historical performance data for the database provided by Oracle will be lost during the execution of the report. The report is an SAP expert tool that can be used by SAP Support if required.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>ST-PI 003C was triggered by ST-PI 2005_1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>ST-PI 2008_1_46C SP4 (SAPKITLRA4), ST-PI 2008_1_620 SP4 (SAPKITLRB4), ST-PI 2008_1_640 SP4 (SAPKITLRC4), ST-PI 2008_1_700 SP4 (SAPKITLRD4), ST-PI 2008_1_710 SP4 SP4</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1564508<br />After you have loaded ST-PI 2008_1 SP4 into the SAP Solution Manager system or managed systems, EarlyWatch Alerts no longer contain expensive SQL statement data. You can correct this system behavior by implementing SAP Note 1564508 in the individual systems.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>SP 6 for ST-PI 2008_1_700 (SAPKITLRD6), SP 6 for ST-PI 2008_1_710 (SAPKITLRE6)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note 1476315<br />You need to implement note 1476315 before starting the import queue with ST-PI 2008_1 SP 6 SP 6 to prevent the import queue from terminating during loading ST-PI 2008_1_700 or ST-PI 20081_1_710 SP6 into your managed system in the DDIC activation phase. This statement only applies if the software component SAP_BASIS has one of the following release-dependent patch levels at the start of the import queue:</li>\r\n</ul>\r\n</ul>\r\n<p>                    SP 22 for SAP_BASIS 700 (SAPKB70022)<br />                    SP 7 for SAP_BASIS 701 (SAPKB70107)<br />                    SP 4 for SAP_BASIS 702 (SAPKB70204)<br />                    SP 10 for SAP_BASIS 710 (SAPKB71010)<br />                    SP 5 for SAP_BASIS 711 (SAPKB71105)<br />                    SP 3 for SAP_BASIS 720 (SAPKB72003)<br />                    The DDIC activation issue does not occur if you are on lower or higher support package levels than the ones mentioned above.<br /><br /></p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (upgrade add-on components)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON> (D036662)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON> (D036662)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000662441/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "AITL422.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000182342003&iv_version=0164&iv_guid=9E3ADCCFD0392C44A49D2E75EA476628"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "949293", "RefComponent": "SV-SMG-MON", "RefTitle": "Solution Manager: SAP Notes for Solution Monitoring", "RefUrl": "/notes/949293"}, {"RefNumber": "949292", "RefComponent": "SV-SMG-SUP", "RefTitle": "SAP Solution Manager: Service Desk SAP Notes", "RefUrl": "/notes/949292"}, {"RefNumber": "949220", "RefComponent": "SV-SMG-IMP", "RefTitle": "Solution Manager: SAP Notes for Implementation Scenarios", "RefUrl": "/notes/949220"}, {"RefNumber": "930747", "RefComponent": "SV-SMG", "RefTitle": "Service Delivery in SAP Solution Manager (recommended Notes)", "RefUrl": "/notes/930747"}, {"RefNumber": "907768", "RefComponent": "SV-SMG-CM", "RefTitle": "General SAP Note for Change Request Management ST 400", "RefUrl": "/notes/907768"}, {"RefNumber": "770693", "RefComponent": "SV-SMG-CM", "RefTitle": "General Note on Change Request Management 3.20", "RefUrl": "/notes/770693"}, {"RefNumber": "1595736", "RefComponent": "SV-SMG-INS", "RefTitle": "SAP Solution Manager: Overview of Notes with Release Notes", "RefUrl": "/notes/1595736"}, {"RefNumber": "1161294", "RefComponent": "SV-SMG", "RefTitle": "Renaming of SAP Solution Manager 4.0 to Solution Manager 7.0", "RefUrl": "/notes/1161294"}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742"}, {"RefNumber": "1022797", "RefComponent": "SV-SMG-MAI", "RefTitle": "Maintenance Optimizer: Product version cannot be selected", "RefUrl": "/notes/1022797"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "907768", "RefComponent": "SV-SMG-CM", "RefTitle": "General SAP Note for Change Request Management ST 400", "RefUrl": "/notes/907768 "}, {"RefNumber": "1149742", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP (CQC) Going Live Support", "RefUrl": "/notes/1149742 "}, {"RefNumber": "930747", "RefComponent": "SV-SMG", "RefTitle": "Service Delivery in SAP Solution Manager (recommended Notes)", "RefUrl": "/notes/930747 "}, {"RefNumber": "949292", "RefComponent": "SV-SMG-SUP", "RefTitle": "SAP Solution Manager: Service Desk SAP Notes", "RefUrl": "/notes/949292 "}, {"RefNumber": "949220", "RefComponent": "SV-SMG-IMP", "RefTitle": "Solution Manager: SAP Notes for Implementation Scenarios", "RefUrl": "/notes/949220 "}, {"RefNumber": "949293", "RefComponent": "SV-SMG-MON", "RefTitle": "Solution Manager: SAP Notes for Solution Monitoring", "RefUrl": "/notes/949293 "}, {"RefNumber": "1161294", "RefComponent": "SV-SMG", "RefTitle": "Renaming of SAP Solution Manager 4.0 to Solution Manager 7.0", "RefUrl": "/notes/1161294 "}, {"RefNumber": "770693", "RefComponent": "SV-SMG-CM", "RefTitle": "General Note on Change Request Management 3.20", "RefUrl": "/notes/770693 "}, {"RefNumber": "1022797", "RefComponent": "SV-SMG-MAI", "RefTitle": "Maintenance Optimizer: Product version cannot be selected", "RefUrl": "/notes/1022797 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "ST-BCO", "From": "400", "To": "400", "Subsequent": "X"}, {"SoftwareComponent": "ST", "From": "220", "To": "320", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "003C_40B", "To": "2005_1_40B", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "003C_45B", "To": "2005_1_45B", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "003C_46B", "To": "2005_1_46B", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "003C_46C", "To": "2005_1_46C", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "003C_46D", "To": "2005_1_46D", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "003C_610", "To": "2005_1_610", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "003C_620", "To": "2005_1_620", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "003C_640", "To": "2005_1_640", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "2005_1_700", "To": "2005_1_700", "Subsequent": "X"}, {"SoftwareComponent": "ST-SER", "From": "310", "To": "620_2005_2", "Subsequent": "X"}, {"SoftwareComponent": "ST-SER", "From": "700_2005_2", "To": "700_2005_2", "Subsequent": "X"}, {"SoftwareComponent": "ST-ICO", "From": "100", "To": "140", "Subsequent": "X"}, {"SoftwareComponent": "ST-SUP", "From": "310", "To": "320", "Subsequent": "X"}, {"SoftwareComponent": "TMWFLOW", "From": "320", "To": "320", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "ST-PI 2005_1_46C", "SupportPackage": "SAPKITLQD6", "URL": "/supportpackage/SAPKITLQD6"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=662441&TargetLanguage=EN&Component=SV-SMG-INS&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/662441/D\" target=\"_blank\">/notes/662441/D</a>."}}}}