{"Request": {"Number": "635511", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 278, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015480662017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000635511?language=E&token=B89F21A8906AA9B92CA59A27A550F6E5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000635511", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000635511/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "635511"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.11.2003"}, "SAPComponentKey": {"_label": "Component", "value": "MM-SRV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Services Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Services Management", "value": "MM-SRV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-SRV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "635511 - FAQ: Purchase requisitions in the service"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains a list of the frequently asked questions on purchase requisitions for services and blanket purchase requisitions. More information on purchase requisitions is available in the following notes:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 493315 - FAQ: Purchase requisition (general)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 493318 - FAQ: Enjoy purchase requisition<br /></p> <b>Questionnaire</b><br /> <OL>1. Why are no sources of supply proposed <B>during</B> the creation of purchase requisitions for services even though the automatic source determination is activated?</OL> <OL>2. You create a purchase requisition and only maintain value limits. Why is the <B>expected value</B> mandatory?</OL> <OL>3. Why does the system display message SE026 (You cannot maintain service specs. due to incomplete transfer structure) when you create <B>a purchase</B> requisition for services even though all necessary data was entered?</OL> <OL>4. When you edit a service the system <B>displays message</B> 00089 (Entry too long (please enter in the format &amp;)) <B>or SE371</B> (Value of service item exceeds allowed value limit). What is to be done?</OL> <OL>5. How is the <B>total</B> value calculated in a purchase requisition for services?</OL> <OL>6. Can I <B>change</B> the value limit of a PReq after the PReq was converted into a purchase order?</OL> <OL>7. You want to create <B>a</B> purchase requisition for services from an order. However, the system repeatedly asks you to enter <B>the</B> valuation price even though it was already entered. Why is that?</OL> <OL>8. I want to <B>transfer</B> service specifications with all corresponding outlines to a purchase order. Why are pushbutton <B>Completed</B> (or 'Transfer everything (F9)') and function <B>'Transfer path' not available?</B></OL> <OL>9. Using Transaction ME51, a purchase requisition <B>can be created with reference</B> to some model service specifications. Why are these functions not supported by Transaction ME51N?</OL><OL>10. You want to convert a purchase requisition for services into a purchase order using Transaction <B>ME58</B> or <B>ME59</B>. Why does the system output message <B>ME</B><B>261</B> (No suitable purchase requisitions found)?</OL> <p><br />--------------------------<br /></p> <OL>1. <B>Question:</B></OL> <p>Why are no sources of supply proposed <B>during</B> the creation of purchase requisitions for services even though the automatic source determination is activated?<br /></p> <b>Answer:</b><br /> <p>Refer to Note 183762.<br /><br />--------------------------<br /></p> <OL>1. <B>Question:</B></OL> <p>You create a purchase requisition and only maintain value limits. Why is the <B>expected value</B> mandatory?<br /></p> <b>Answer:</b><br /> <p>Refer to Note 440601.<br /><br />--------------------------<br /></p> <OL>1. <B>Question:</B></OL> <p>Why does the system display message SE026 (You cannot maintain service specs. due to incomplete transfer structure) when you create <B>a purchase</B> requisition for services even though all necessary data was entered?<br /></p> <b>Answer:</b><br /> <p>Refer to Note 437658.<br /><br />--------------------------<br /></p> <OL>1. <B>Question:</B></OL> <p>When you edit a service the system <B>displays message</B> 00089 (Entry too long (please enter in the format &amp;)) <B>or SE371</B> (Value of service item exceeds allowed value limit). What is to be done?<br /></p><b>Answer:</b><br /> <p>Refer to Note 584997.<br /><br />--------------------------<br /></p> <OL>1. <B>Question:</B></OL> <p>How is the <B>total</B> value calculated in a purchase requisition for services?<br /></p> <b>Answer:</b><br /> <p>The total value (Field EBAN-PREIS or MEREQ3320-GSWRT) is composed of the total value of the planned services and the expected value of the limit.<br /><br />--------------------------<br /></p> <OL>1. <B>Question:</B></OL> <p>Can I <B>change</B> the value limit of a PReq after the PReq was converted into a purchase order?<br /></p> <b>Answer:</b><br /> <p>No, this is not possible. In order to change the limit of the PReq, you must delete or cancel all follow-on documents first.<br /><br />--------------------------<br /></p> <OL>1. <B>Question:</B></OL> <p>You want to create <B>a</B> purchase requisition for services from an order. However, the system repeatedly asks you to enter <B>the</B> valuation price even though it was already entered. Why is that?<br /></p> <b>Answer:</b><br /> <p>Refer to Note 393356.<br /><br />--------------------------<br /></p> <OL>1. <B>Question:</B></OL> <p>I want to <B>transfer</B> service specifications with all corresponding outlines to a purchase order. Why are pushbutton <B>Completed</B> (or 'Transfer everything (F9)') and function <B>'Transfer path' not available?</B><br /></p> <b>Answer:</b><br /> <p>Some complete service specifications can only be transferred under the following requirements:</p> <UL><LI>You are in the service specifications of the purchase requisition (full screen).</LI></UL> <UL><LI>The document item does not yet contain any services.</LI></UL> <p><br />--------------------------<br /></p> <OL>1. <B>Question:</B></OL> <p>Using Transaction ME51, a purchase requisition <B>can be created with reference</B> to some model service specifications. Why are these functions not supported by Transaction ME51N?<br /></p><b>Answer:</b><br /> <p>These functions do not exist do to technical reasons. Only Transaction ME51 can be used for this.<br /><br />--------------------------<br /></p> <OL>1. <B>Question:</B></OL> <p>You want to convert a purchase requisition for services into a purchase order using Transaction <B>ME58</B> or <B>ME59</B>. Why does the system output message <B>ME</B><B>261</B> (No suitable purchase requisitions found)?<br /><br /></p> <b>Answer:</b><br /> <p>In the following situations, a purchase requisition cannot be converted automatically into a purchase order:</p> <UL><LI>No unique source of supply is assigned to the purchase requisition.</LI></UL> <UL><LI>In the vendor master (purchasing data), the 'Automatic purchase order' indicator is set.</LI></UL> <UL><LI>In Customizing, indicator 'Automatic PO generation for service requisitions' is not set for the client of the purchasing organization. (Customizing path: Materials Management -&gt; External Services Management -&gt; Source Determination and Default Values)</LI></UL> <UL><LI>The purchase requisition is subject to a release strategy and is not released.</LI></UL> <UL><LI>In the purchase requisition, indicator 'Closed' is set or the item is deleted.</LI></UL> <UL><LI>The purchase requisition is already converted into a purchase order.</LI></UL> <p><br />--------------------------<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>FAQ, PReq, purchase requisition, ME51, ME52, ME53, ME51N, ME52N, ME53N, source of supply, source determination, expected value, purchase requisition for services, service requisition, SE 026, SE026, service specifications, service, limit, 00 089, 00089, SE 371, SE371, total value, EBAN-PREIS, MEREQ3320-GSWRT, valuation price, outline level, outline, model SS, model service specifications, select, ME 261, ME261<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>-<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>-<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D031268)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D031268)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000635511/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635511/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635511/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635511/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635511/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635511/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635511/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635511/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635511/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "584997", "RefComponent": "MM-SRV", "RefTitle": "Maximum of 11 places for prices for services", "RefUrl": "/notes/584997"}, {"RefNumber": "493318", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "FAQ: Purchase requisition (ME51N, ME52N, ME53N)", "RefUrl": "/notes/493318"}, {"RefNumber": "493315", "RefComponent": "MM-PUR-REQ", "RefTitle": "FAQ: Purchase requisition (general)", "RefUrl": "/notes/493315"}, {"RefNumber": "440601", "RefComponent": "MM-SRV", "RefTitle": "Expected value mandatory in service POs with limits", "RefUrl": "/notes/440601"}, {"RefNumber": "437658", "RefComponent": "MM-SRV-GF", "RefTitle": "SE026: Creation of service specifications not possible", "RefUrl": "/notes/437658"}, {"RefNumber": "393356", "RefComponent": "MM-PUR-REQ", "RefTitle": "Required entry field for price in PReq and service operation", "RefUrl": "/notes/393356"}, {"RefNumber": "183762", "RefComponent": "MM-SRV", "RefTitle": "MM-SRV: No autom. source determination for services", "RefUrl": "/notes/183762"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "493315", "RefComponent": "MM-PUR-REQ", "RefTitle": "FAQ: Purchase requisition (general)", "RefUrl": "/notes/493315 "}, {"RefNumber": "584997", "RefComponent": "MM-SRV", "RefTitle": "Maximum of 11 places for prices for services", "RefUrl": "/notes/584997 "}, {"RefNumber": "493318", "RefComponent": "MM-PUR-REQ-GUI", "RefTitle": "FAQ: Purchase requisition (ME51N, ME52N, ME53N)", "RefUrl": "/notes/493318 "}, {"RefNumber": "393356", "RefComponent": "MM-PUR-REQ", "RefTitle": "Required entry field for price in PReq and service operation", "RefUrl": "/notes/393356 "}, {"RefNumber": "440601", "RefComponent": "MM-SRV", "RefTitle": "Expected value mandatory in service POs with limits", "RefUrl": "/notes/440601 "}, {"RefNumber": "437658", "RefComponent": "MM-SRV-GF", "RefTitle": "SE026: Creation of service specifications not possible", "RefUrl": "/notes/437658 "}, {"RefNumber": "183762", "RefComponent": "MM-SRV", "RefTitle": "MM-SRV: No autom. source determination for services", "RefUrl": "/notes/183762 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}