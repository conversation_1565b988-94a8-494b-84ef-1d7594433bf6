{"Request": {"Number": "2326521", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 407, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013731052017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002326521?language=E&token=9107A543113667987E90948DD72D0517"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002326521", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002326521/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2326521"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04/25/2017"}, "SAPComponentKey": {"_label": "Component", "value": "IS-R"}, "SAPComponentKeyText": {"_label": "Component", "value": "Industry-Specific Component Retail"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Specific Component Retail", "value": "IS-R", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-R*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2326521 - S4TC EA_RETAIL master check class for S/4 system transformation checks"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to migrate an existing SAP ERP for Retail system to an S/4 system. To check whether the prerequisites for the software component EA-RETAIL are met for an S/4 migration, some preparatory checks must be carried out in the existing SAP ERP for Retail system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S/4 precheck master check class EA-RETAIL</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Also implement all SAP Notes that contain the individual prechecks in your system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the correction instructions in your system.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028431)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028431)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002326521/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326521/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326521/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326521/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326521/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326521/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326521/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326521/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002326521/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2451062", "RefComponent": "IS-R-FIO", "RefTitle": "S4 PreChecks EA-RETAIL: Checks for Retail Store Transfer Product/Order Product F&R Enhancements (SAP S4 HANA 1610 only)", "RefUrl": "/notes/2451062"}, {"RefNumber": "2330577", "RefComponent": "LO-FSH", "RefTitle": "SAP S/4HANA Retail : Checks for Fashion Management Functionalities", "RefUrl": "/notes/2330577"}, {"RefNumber": "2325526", "RefComponent": "IS-R-BD-PCT", "RefTitle": "Global data synchronization precheck for migration to S/4", "RefUrl": "/notes/2325526"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2325526", "RefComponent": "IS-R-BD-PCT", "RefTitle": "Global data synchronization precheck for migration to S/4", "RefUrl": "/notes/2325526 "}, {"RefNumber": "2451062", "RefComponent": "IS-R-FIO", "RefTitle": "S4 PreChecks EA-RETAIL: Checks for Retail Store Transfer Product/Order Product F&R Enhancements (SAP S4 HANA 1610 only)", "RefUrl": "/notes/2451062 "}, {"RefNumber": "2330577", "RefComponent": "LO-FSH", "RefTitle": "SAP S/4HANA Retail : Checks for Fashion Management Functionalities", "RefUrl": "/notes/2330577 "}, {"RefNumber": "2339756", "RefComponent": "IS-R-FIO", "RefTitle": "S4 PreChecks EA-RETAIL: Descriptions of checks for Retail Store Fiori app", "RefUrl": "/notes/2339756 "}, {"RefNumber": "2339361", "RefComponent": "IS-R-FIO", "RefTitle": "S4 PreChecks EA-RETAIL: Checks for Retail Store Fiori app", "RefUrl": "/notes/2339361 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-RETAIL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "619", "To": "619", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-RETAIL", "NumberOfCorrin": 1, "URL": "/corrins/0002326521/167"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-RETAIL", "ValidFrom": "600", "ValidTo": "618", "Number": "2325526 ", "URL": "/notes/2325526 ", "Title": "Global data synchronization precheck for migration to S/4", "Component": "IS-R-BD-PCT"}, {"SoftwareComponent": "EA-RETAIL", "ValidFrom": "600", "ValidTo": "618", "Number": "2330577 ", "URL": "/notes/2330577 ", "Title": "SAP S/4HANA Retail : Checks for Fashion Management Functionalities", "Component": "LO-FSH"}, {"SoftwareComponent": "EA-RETAIL", "ValidFrom": "600", "ValidTo": "618", "Number": "2339361 ", "URL": "/notes/2339361 ", "Title": "S4 PreChecks EA-RETAIL: Checks for Retail Store Fiori app", "Component": "IS-R-FIO"}, {"SoftwareComponent": "EA-RETAIL", "ValidFrom": "600", "ValidTo": "618", "Number": "2451062 ", "URL": "/notes/2451062 ", "Title": "S4 PreChecks EA-RETAIL: Checks for Retail Store Transfer Product/Order Product F&R Enhancements (SAP S4 HANA 1610 only)", "Component": "IS-R-FIO"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}