{"guid": "0050569455E21ED5B3E176783925E09E", "sitemId": "SI4: Cross Industry - BRIM - Solution Sales and Billing", "sitemTitle": "S4TWL - BRIM - Solution Sales and Billing", "note": 2271236, "noteTitle": "2271236 - S4TWL - BRIM - Solution Sales and Billing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The BRIM/Hybris Billing solution covers CRM, CI / FI-CA (ERP) and SAP Convergent Charging components/systems.</p>\n<p>The creation of provider contracts starts in CRM. For the integration of provider contracts in SAP Contract Accounts Receivable and Payable, SAP Convergent Invoicing and finally SD Billing with FI-AR, the provider contracts need additional information for the processing in SD. In addition to that, business agreements that are assigned to a business partner need to be created automatically in the background without replication in CRM. As the follow up processing of CI Billing Documents in SD Billing is currently not available in SAP S/4HANA, on-premise edition, this feature is not provided. Billing and invoicing of consumption data is still possible in FI-CA but not with SD and FI-AR.</p>\n<p>In addition, the BRIM SD deployment variant was developed to invoice consumption data also in SD billing with accounting in FI-AR. This solution is currently not available in SAP S/4HANA, on-premise edition. Therefore, the additions on the provider contract with SD Billing relevant data and the background creation of contract accounts in ERP without existing contract accounts in CRM are not required.</p>\n<p> </p>\n<p><strong>UPDATE:</strong></p>\n<p>Meanwhile, the integration of CI Billing Documents in SAP Convergent Invoicing into SD Billing is available for S/4HANA in the form of \"SAP S/4HANA for Subscription Billing, Sales and Distribution Option 1.0\" as part of Hybris Billing. For further details please check note 2347269 - \"Release stategy of the ABAP Add-On SOLINVE 800\". Therefore, the business function FICAX_SOLESALESBILL has been reenabled and set to customer switchable. For further details check note 2348426 - \"Reactivation of Business Function FICAXSOLSALESBILL\".</p>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>To check if you use Solution Sales and Billing, call the transaction SE16 to check for entries in table /SOIN/CI_DOCFLOW.</p>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "Software Upgrade / Maintenance", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Business function FICAX_SOLESALESBILL has been reenabled, apply Support Pack as per Note 2348426"}, {"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "The integration of CI Billing Documents in SAP Convergent Invoicing into SD Billing is available for S/4HANA in the form of \"SAP S/4HANA for Subscription Billing, Sales and Distribution Option 1.0\" as part of Hybris Billing. For further details please check SAP Note 2347269"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If you are using tables indicated in SAP Note 2352295 in your custom coding, revert these changes before the upgrade"}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}