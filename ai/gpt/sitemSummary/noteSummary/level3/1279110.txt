SAP Note 1279110 addresses an issue where incorrect information might be displayed in the ST14 analysis concerning the top tables and their related index sizes for DB6 databases. This problem occurs when SAP_BASIS version 640 or higher is used.

The reason for this error is a program bug, and there are no additional prerequisites or other terms to be aware of, apart from /SSA/ABO and DVM (Data Volume Management).

To solve this issue, SAP plans to fix the bug in the upcoming release of ST-A/PI Plug-In 01L*. In the meantime, users can correct this problem by implementing the correction instructions provided in the note.

Before applying the correction, it is essential to ensure that the ST-A/PI release is implemented across all systems in the transport landscape. Users should execute the report /SSF/SAO_UTILS in transaction SE38 and select the option 'Uncomment/Recomment analysis coding for additional components.' This action must be performed in each system prior to applying the coding correction.

Once the report execution is complete, the implementation of the coding correction can be done through transaction SNOTE, which is used for applying SAP Notes corrections.