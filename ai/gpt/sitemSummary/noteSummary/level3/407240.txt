SAP Note 407240 addresses an issue with backup jobs generated in the DBA planning calendar (Transaction DB13) specifically for DB2 on z/OS (formerly known as OS/390). The problem arises when the primary allocation for a backup job is set to a maximum of 30,000 tracks and the secondary allocation is essentially unlimited. Given that a 3390 volume can only accommodate up to 49,000 tracks, these settings could potentially lead to space allocation problems during the execution of COPY jobs.

The note provides no explicit reasons or prerequisites for why this issue might occur but implies that the allocations for these backup jobs are not being set appropriately within the system.

To resolve the issue, the note advises implementers to ensure they have imported the correct transport for their specific SAP R/3 release. The corresponding transports can potentially address the allocation problem by limiting both the primary and secondary allocations to a maximum of 12,000 tracks each, which would help prevent the volume overflow issue.

The SAP Note references further details and requirements for transport imports in Note 101217 and specifies the actual transport numbers for different releases. For Release 4.6D, implementers should refer to SAP Note 406744, and for Release 4.6C, they should use transport D6DK000183.

Lastly, the note stresses the importance of regularly checking the referenced SAP Note 101217 for updates or additions to ensure that the system remains at the newest maintenance level.