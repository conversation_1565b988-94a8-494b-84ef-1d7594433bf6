SAP Note 2228250 addresses a specific issue for customers who are upgrading from SAP ERP to SAP S/4HANA. The note identifies that the transactions used for Web access of procurement transactions via Internet Application Components (IAC), which include MEW0, MEW1, MEW2, MEW3, <PERSON>W5, <PERSON>W6, and MEWP, have been deprecated in S/4HANA. These transactions may no longer function correctly after the transition to S/4HANA.

Customers who encounter this issue will find that their custom ABAP code, which relies on the aforementioned transactions, is affected by the simplifications made in S/4HANA. As a result, the custom code check during the upgrade-like conversion will refer to this specific note, indicating the need for changes.

The solution offered by this note is straightforward: all usages of the deprecated SAP objects in customer-specific custom code must be removed because the original functionality will not work anymore after the conversion to S/4HANA. The note recommends that customers should use the corresponding successor functionality available via Fiori apps instead.

For further guidance on adapting the custom code for S/4HANA, this note refers to SAP Note 2190420, which contains detailed recommendations for ensuring that custom ABAP code remains compatible with SAP S/4HANA. The referenced guidelines cover various aspects, such as the applicability of most best practices from SAP ERP to S/4HANA, the need for minimal changes to make the majority of custom code compatible, and a reminder of the regular activities that should be undertaken to maintain custom code properly.

In conclusion, SAP Note 2228250 serves as a warning and guide for customers in the process of upgrading to SAP S/4HANA, informing them of the deprecation of certain Internet Application Components and directing them towards the alternative solutions offered by SAP Fiori, as well as to SAP Note 2190420 for broader guidance on custom code adaptation in S/4HANA.