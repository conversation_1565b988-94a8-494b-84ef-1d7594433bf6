SAP Note 2521090 addresses an issue where a short dump (a sudden termination of a process in SAP) occurs during the migration of bank data to SAP S/4HANA when an XML file containing both an IBAN and an account number is uploaded.

Symptom:
Users experience a short dump categorized as an ABAP programming error with a runtime error `MESSAGE_TYPE_X` during the bank data migration process.

Cause:
The short dump occurs because the API that checks the bank data expects either the 'Account data' or the 'IBAN' to be supplied, but not both. The short dump typically happens when both fields are filled or when one of the values provided has the incorrect format.

Resolution:
The solution provided in the note is straightforward. Users must ensure that they maintain either the 'Account' data or the 'IBAN' data in the XML migration template but not both. There is a clear instruction to fill in only one of these mandatory fields to prevent the short dump from occurring during validation in the Migration Cockpit.

The note also includes a reference to SAP Note 2470789, which provides examples of data migration templates for SAP S/4HANA. This referenced note offers guidance on filling out the migration templates correctly, with the intention of helping users avoid errors during the migration process.

Keywords associated with this note include dump, data migration, iban, account, bank, ltmc, and cockpit, which indicates the context in which this issue typically occurs.

In summary, SAP Note 2521090 provides a resolution for a specific data migration error in SAP S/4HANA, where it is crucial to supply either an account number or an IBAN, not both, in the correct format within the bank data migration XML template to prevent short dumps.