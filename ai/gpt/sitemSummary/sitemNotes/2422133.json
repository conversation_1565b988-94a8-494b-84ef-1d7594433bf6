{"Request": {"Number": "2422133", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 447, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018508812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002422133?language=E&token=2F229B1CA486F3A778C174E75A212811"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002422133", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002422133/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2422133"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.11.2017"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Lease Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Lease Accounting", "value": "RE-FX-LA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2422133 - RE-FX: Leasing enhancements and corrections"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note delivers the following corrections and function enhancements:</p>\r\n<ol>\r\n<li>In general, previously known valuation problems are resolved.</li>\r\n<li>Special flows in the context of 'retroactive changes' may be calculated incorrectly if time slots exist for the valuation rule.</li>\r\n<li>In future, initial costs and incentives must be assigned to only the first time slot associated with a valuation rule.</li>\r\n<li>The condition lock logic for valuation-relevant conditions has been enhanced:</li>\r\n<ul>\r\n<li>In future, conditions for initial costs, incentives, and asset retirement obligations will no longer be locked as long as there is no corresponding special flow in the valuation cash flow.</li>\r\n<li>In future, lease installment payments will no longer be locked if the &#x2018;Valid-from&#x2019; date of the condition lies after the last calculation period end for all posted valuation cash flow items.</li>\r\n</ul>\r\n<li>In the valuation overview, the number of decimal places for the contract value and net present value may be displayed incorrectly (this applies only if the number of decimal places is &lt;&gt; 2).</li>\r\n<li>When you switch tab pages in the valuation result, the column header for the amount columns may not be updated correctly.</li>\r\n<li>In the valuation overview, you can now also filter according to a certain valuation rule (this applies only if the number of different valuation rules is &lt;= 10).</li>\r\n<li>The creation of a repayment total in the valuation cash flow overview has been corrected for advance allocation (repayment total = allocation - interest from previous period).</li>\r\n<li>The <strong>first</strong> step associated with foreign currency handling is delivered. Complete foreign currency handling is not yet possible with this first delivery.</li>\r\n<ul>\r\n<li>The transfer posting cash flow is created in the condition currency (in the same way as for the object cash flow). If different currencies have been taken into consideration, a contract value is not saved in the process header. The different contract values for each currency can then be obtained from the valuation cash flow overview.</li>\r\n<li>Valuation and linearization take place in the contract currency.</li>\r\n<ul>\r\n<li>The new flow type relationship CEK ('Asset transaction allocation') is delivered.&#x00A0;</li>\r\n<li>All conditions associated with the relevant valuation rule must have the same condition currency and currency translation rule.</li>\r\n</ul>\r\n<li>In the valuation result, the values are displayed in the local currency if the contract currency differs from the local currency or, in the case of a transfer posting, if the condition currency differs from the local currency.</li>\r\n<ul>\r\n<li>Amounts associated with 'one-time&#x2019; planned cash flow items are translated into the local currency whereby the key date = &#x2018;due date of item'.</li>\r\n<li>Amounts associated with 'periodic' planned cash flow items are translated into the local currency whereby the key date = 'date of translation process CFGEN&#x2019; (see currency translation rule).</li>\r\n</ul>\r\n<li><strong>Please note</strong>: At present, valuation postings must not be carried out for such contracts. Once the posting function is complete, those local currency amounts that were updated during posting will be displayed for posted cash flow items.</li>\r\n<li>A separate SAP Note will be created to provide a more detailed description of foreign currency handling.</li>\r\n</ul>\r\n<li>The check RECETM238 ('Tax information for assigned conditions not identical') is sent only for pure linearization rules and activation rules that also contain transfer posting items.&#x00A0;For all other valuation rules, the relevant zero tax indicator from the basic settings of the company code is used during posting. In addition to this, however, the tax jurisdiction must be identical for all relevant conditions.</li>\r\n<li>The partner cash flow split or the object cash flow limit in conjunction with the start of the consideration period for a valuation rule (essentially for the transition period) will be executed only if at least one valuation cash flow item has actually been posted (actual record) during the periodic valuation posting run. After a valuation posting, you must perform the following tasks manually (automation of these tasks is planned):</li>\r\n<ul>\r\n<li>Open the contract in change mode and save any necessary changes (also possible in transaction RECNCHECK).</li>\r\n<li>You may need to complete the valuation rule (this can be done in transaction RECEISRULECN) and regenerate the valuation cash flow without a new time slot (this can be done in transaction RECEPR where you can force regeneration).</li>\r\n</ul>\r\n</ol>\r\n<p>Note that some of documentation changes (for example, F1 help) are delivered only via Support Packages.</p>\r\n<p><strong>Important</strong>: SAP Note 2362105 is a necessary prerequisite (see the solution description).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>IFRS leasing, lease-in, leasing contract, valuation, right-of-use asset, RoU, new leasing standard, US GAAP, HGB</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Legal requirement</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Implement the following manual changes in advance:</strong></p>\r\n<p>1. Implement SAP Note 2362105 in your system (the contents of this SAP Note have nothing to do with the leasing function, but they deliver an essential program enhancement).</p>\r\n<p>2. SE24: Change or enhance the text elements of the class CL_RECE_DATA_CF:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Symbol</strong></td>\r\n<td><strong>Text</strong></td>\r\n<td><strong>Length</strong></td>\r\n</tr>\r\n<tr>\r\n<td>ALL</td>\r\n<td>All Rules</td>\r\n<td>30</td>\r\n</tr>\r\n<tr>\r\n<td>CS2</td>\r\n<td>Expense/Revenue Leasing</td>\r\n<td>30</td>\r\n</tr>\r\n<tr>\r\n<td>EXC</td>\r\n<td>Restrict to Rule</td>\r\n<td>30</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>3. SE91: Create the following messages in the message class RECEPR:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Number</strong></td>\r\n<td><strong>Text</strong></td>\r\n</tr>\r\n<tr>\r\n<td>102</td>\r\n<td>From amount &amp;1 to &amp;2 (translation date &amp;3)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>4. SE91: Create the following messages in the message class RECETM.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Number</strong></td>\r\n<td><strong>Text</strong></td>\r\n</tr>\r\n<tr>\r\n<td>244</td>\r\n<td>&amp;1, &amp;2: Initial costs/incentives permitted in first time slot only</td>\r\n</tr>\r\n<tr>\r\n<td>245</td>\r\n<td>&amp;1, &amp;2: Tax jurisdiction for assigned conditions not identical</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>5. SE11: Enhance the structure RECE_CASHFLOW_L by adding the following component at the end:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Component</strong></td>\r\n<td><strong>Typing Method</strong></td>\r\n<td><strong>Component Type</strong></td>\r\n<td><strong>Reference table</strong></td>\r\n<td><strong>Reference field</strong></td>\r\n</tr>\r\n<tr>\r\n<td>BBWHR_DEPR_LC</td>\r\n<td>Types</td>\r\n<td>TFM_BBWHR</td>\r\n<td>VICECFRULE_TAB</td>\r\n<td>LOCALCURR</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>6. SE11: In the structure VICECFRULE_TAB, add the following component below the component REFGUID:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Component</strong></td>\r\n<td><strong>Typing Method</strong></td>\r\n<td><strong>Component Type</strong></td>\r\n</tr>\r\n<tr>\r\n<td>REFGUID2</td>\r\n<td>Types</td>\r\n<td>RECDREFGUID</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>7. SM30:&#x00A0;View V_TIVCDREFFLOWRE: Define the following new flow type relationship:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Relationship </strong></td>\r\n<td><strong>Name</strong></td>\r\n</tr>\r\n<tr>\r\n<td>CEK </td>\r\n<td>Asset transaction allocation</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>8. SE38: Enhance the program SAPLRECE_GUI_PROCESS_LIST by adding the following text element:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Symbol</strong></td>\r\n<td><strong>Text</strong></td>\r\n<td><strong>Length</strong></td>\r\n</tr>\r\n<tr>\r\n<td>LCC</td>\r\n<td>LC</td>\r\n<td>10</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Note the following: Here, LC stands for 'local currency'.</p>\r\n<p><strong>Implement the attached program corrections.</strong></p>\r\n<p>&#x00A0;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022894)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D033675)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002422133/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422133/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422133/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422133/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422133/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422133/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422133/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422133/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002422133/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2255555", "RefComponent": "RE-FX-LA", "RefTitle": "Valuation of leasing contracts (SAP Contract and Lease Management based on SAP RE-FX)", "RefUrl": "/notes/2255555"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2910243", "RefComponent": "RE-FX-LA-CN", "RefTitle": "Valuation parameters: Initial costs or Incentives are not allowed for subsequent valuation time slices", "RefUrl": "/notes/2910243 "}, {"RefNumber": "2255555", "RefComponent": "RE-FX-LA", "RefTitle": "Valuation of leasing contracts (SAP Contract and Lease Management based on SAP RE-FX)", "RefUrl": "/notes/2255555 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10004INS4CORE", "URL": "/supportpackage/SAPK-10004INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10102INS4CORE", "URL": "/supportpackage/SAPK-10102INS4CORE"}, {"SoftwareComponentVersion": "EA-APPL 606", "SupportPackage": "SAPK-60619INEAAPPL", "URL": "/supportpackage/SAPK-60619INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 616", "SupportPackage": "SAPK-61611INEAAPPL", "URL": "/supportpackage/SAPK-61611INEAAPPL"}, {"SoftwareComponentVersion": "EA-FIN 617", "SupportPackage": "SAPK-61714INEAFIN", "URL": "/supportpackage/SAPK-61714INEAFIN"}, {"SoftwareComponentVersion": "SAP_FIN 618", "SupportPackage": "SAPK-61806INSAPFIN", "URL": "/supportpackage/SAPK-61806INSAPFIN"}, {"SoftwareComponentVersion": "EA-FIN 700", "SupportPackage": "SAPK-70011INEAFIN", "URL": "/supportpackage/SAPK-70011INEAFIN"}, {"SoftwareComponentVersion": "SAP_FIN 720", "SupportPackage": "SAPK-72007INSAPFIN", "URL": "/supportpackage/SAPK-72007INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 730", "SupportPackage": "SAPK-73007INSAPFIN", "URL": "/supportpackage/SAPK-73007INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 3, "URL": "/corrins/0002422133/15841"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 2, "URL": "/corrins/0002422133/19773"}, {"SoftwareComponent": "EA-FIN", "NumberOfCorrin": 2, "URL": "/corrins/0002422133/15842"}, {"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 1, "URL": "/corrins/0002422133/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 100&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-10003INS4CORE - SAPK-10003INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-10101INS4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>See the solution described in the SAP Note.<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 618&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-61803INSAPFIN - SAPK-61805INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-72005INSAPFIN - SAPK-72006INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-73004INSAPFIN - SAPK-73006INSAPFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>See the solution described in the SAP Note.<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP R/3 Enterpr...|<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60617INEAAPPL - SAPK-60618INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 616&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-61610INEAAPPL - SAPK-61610INEAAPPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>See the solution described in the SAP Note.<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; EA-FIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 617&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-61712INEAFIN - SAPK-61713INEAFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-70009INEAFIN - SAPK-70010INEAFIN&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>See the solution described in the SAP Note.<br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 8, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 4, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 14, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2304138 ", "URL": "/notes/2304138 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2326302 ", "URL": "/notes/2326302 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2338349 ", "URL": "/notes/2338349 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2346651 ", "URL": "/notes/2346651 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2374439 ", "URL": "/notes/2374439 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2380060 ", "URL": "/notes/2380060 ", "Title": "RE-FX: Leasing - depreciation values not up-to-date", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2396901 ", "URL": "/notes/2396901 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2397125 ", "URL": "/notes/2397125 ", "Title": "RE-FX: Leasing - depreciation values not up to date despite SAP Note 2380060", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2406475 ", "URL": "/notes/2406475 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2416355 ", "URL": "/notes/2416355 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2416730 ", "URL": "/notes/2416730 ", "Title": "Corrections: integration in asset accounting", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2420977 ", "URL": "/notes/2420977 ", "Title": "RE-FX: Leasing - one-time conditions and message RECETM240", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "606", "ValidTo": "616", "Number": "2346751 ", "URL": "/notes/2346751 ", "Title": "Leasing: Posting a valuation and canceling it from a contract", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2304138 ", "URL": "/notes/2304138 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2326302 ", "URL": "/notes/2326302 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2338349 ", "URL": "/notes/2338349 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2346651 ", "URL": "/notes/2346651 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2374439 ", "URL": "/notes/2374439 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2380060 ", "URL": "/notes/2380060 ", "Title": "RE-FX: Leasing - depreciation values not up-to-date", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2396901 ", "URL": "/notes/2396901 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2397125 ", "URL": "/notes/2397125 ", "Title": "RE-FX: Leasing - depreciation values not up to date despite SAP Note 2380060", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2406475 ", "URL": "/notes/2406475 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2416355 ", "URL": "/notes/2416355 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2416730 ", "URL": "/notes/2416730 ", "Title": "Corrections: integration in asset accounting", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2420977 ", "URL": "/notes/2420977 ", "Title": "RE-FX: Leasing - one-time conditions and message RECETM240", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2326302 ", "URL": "/notes/2326302 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2338349 ", "URL": "/notes/2338349 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2346651 ", "URL": "/notes/2346651 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2374439 ", "URL": "/notes/2374439 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2380060 ", "URL": "/notes/2380060 ", "Title": "RE-FX: Leasing - depreciation values not up-to-date", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2396901 ", "URL": "/notes/2396901 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2406475 ", "URL": "/notes/2406475 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2416355 ", "URL": "/notes/2416355 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2416730 ", "URL": "/notes/2416730 ", "Title": "Corrections: integration in asset accounting", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2420977 ", "URL": "/notes/2420977 ", "Title": "RE-FX: Leasing - one-time conditions and message RECETM240", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "700", "Number": "2346751 ", "URL": "/notes/2346751 ", "Title": "Leasing: Posting a valuation and canceling it from a contract", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "617", "ValidTo": "700", "Number": "2397125 ", "URL": "/notes/2397125 ", "Title": "RE-FX: Leasing - depreciation values not up to date despite SAP Note 2380060", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2326302 ", "URL": "/notes/2326302 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2338349 ", "URL": "/notes/2338349 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2346651 ", "URL": "/notes/2346651 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2374439 ", "URL": "/notes/2374439 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2380060 ", "URL": "/notes/2380060 ", "Title": "RE-FX: Leasing - depreciation values not up-to-date", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2396901 ", "URL": "/notes/2396901 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2406475 ", "URL": "/notes/2406475 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2416355 ", "URL": "/notes/2416355 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2416730 ", "URL": "/notes/2416730 ", "Title": "Corrections: integration in asset accounting", "Component": "RE-FX-LA"}, {"SoftwareComponent": "EA-FIN", "ValidFrom": "700", "ValidTo": "700", "Number": "2420977 ", "URL": "/notes/2420977 ", "Title": "RE-FX: Leasing - one-time conditions and message RECETM240", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2374439 ", "URL": "/notes/2374439 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2380060 ", "URL": "/notes/2380060 ", "Title": "RE-FX: Leasing - depreciation values not up-to-date", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2397125 ", "URL": "/notes/2397125 ", "Title": "RE-FX: Leasing - depreciation values not up to date despite SAP Note 2380060", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2406475 ", "URL": "/notes/2406475 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2416355 ", "URL": "/notes/2416355 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2416730 ", "URL": "/notes/2416730 ", "Title": "Corrections: integration in asset accounting", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2420977 ", "URL": "/notes/2420977 ", "Title": "RE-FX: Leasing - one-time conditions and message RECETM240", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "101", "Number": "2396901 ", "URL": "/notes/2396901 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2338349 ", "URL": "/notes/2338349 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2346651 ", "URL": "/notes/2346651 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2374439 ", "URL": "/notes/2374439 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2380060 ", "URL": "/notes/2380060 ", "Title": "RE-FX: Leasing - depreciation values not up-to-date", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2397125 ", "URL": "/notes/2397125 ", "Title": "RE-FX: Leasing - depreciation values not up to date despite SAP Note 2380060", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2406475 ", "URL": "/notes/2406475 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2416355 ", "URL": "/notes/2416355 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2416730 ", "URL": "/notes/2416730 ", "Title": "Corrections: integration in asset accounting", "Component": "RE-FX-LA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2420977 ", "URL": "/notes/2420977 ", "Title": "RE-FX: Leasing - one-time conditions and message RECETM240", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2338349 ", "URL": "/notes/2338349 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2346651 ", "URL": "/notes/2346651 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2374439 ", "URL": "/notes/2374439 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2380060 ", "URL": "/notes/2380060 ", "Title": "RE-FX: Leasing - depreciation values not up-to-date", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2396901 ", "URL": "/notes/2396901 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2397125 ", "URL": "/notes/2397125 ", "Title": "RE-FX: Leasing - depreciation values not up to date despite SAP Note 2380060", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2406475 ", "URL": "/notes/2406475 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2416355 ", "URL": "/notes/2416355 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2416730 ", "URL": "/notes/2416730 ", "Title": "Corrections: integration in asset accounting", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2420977 ", "URL": "/notes/2420977 ", "Title": "RE-FX: Leasing - one-time conditions and message RECETM240", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "730", "Number": "2346751 ", "URL": "/notes/2346751 ", "Title": "Leasing: Posting a valuation and canceling it from a contract", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2326302 ", "URL": "/notes/2326302 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2338349 ", "URL": "/notes/2338349 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2346651 ", "URL": "/notes/2346651 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2374439 ", "URL": "/notes/2374439 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2380060 ", "URL": "/notes/2380060 ", "Title": "RE-FX: Leasing - depreciation values not up-to-date", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2396901 ", "URL": "/notes/2396901 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2397125 ", "URL": "/notes/2397125 ", "Title": "RE-FX: Leasing - depreciation values not up to date despite SAP Note 2380060", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2406475 ", "URL": "/notes/2406475 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2416355 ", "URL": "/notes/2416355 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2416730 ", "URL": "/notes/2416730 ", "Title": "Corrections: integration in asset accounting", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2420977 ", "URL": "/notes/2420977 ", "Title": "RE-FX: Leasing - one-time conditions and message RECETM240", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2338349 ", "URL": "/notes/2338349 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2346651 ", "URL": "/notes/2346651 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2374439 ", "URL": "/notes/2374439 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2380060 ", "URL": "/notes/2380060 ", "Title": "RE-FX: Leasing - depreciation values not up-to-date", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2396901 ", "URL": "/notes/2396901 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2397125 ", "URL": "/notes/2397125 ", "Title": "RE-FX: Leasing - depreciation values not up to date despite SAP Note 2380060", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2406475 ", "URL": "/notes/2406475 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2416355 ", "URL": "/notes/2416355 ", "Title": "RE-FX: Leasing - corrections and function enhancements", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2416730 ", "URL": "/notes/2416730 ", "Title": "Corrections: integration in asset accounting", "Component": "RE-FX-LA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2420977 ", "URL": "/notes/2420977 ", "Title": "RE-FX: Leasing - one-time conditions and message RECETM240", "Component": "RE-FX-LA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}