{"Request": {"Number": "1834480", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 502, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010843302017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001834480?language=E&token=4FDE9A1E8356F59F9B561963E493E135"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001834480", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001834480/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1834480"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.12.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BI-RA-BICS"}, "SAPComponentKeyText": {"_label": "Component", "value": "BI consumer services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Business intelligence solutions", "value": "BI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Reporting, analysis, and dashboards", "value": "BI-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BI-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BI consumer services", "value": "BI-RA-BICS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BI-RA-BICS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1834480 - Design Time: Dependencies hierarchies and variables"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note adds the function to display dependencies of version variables to the BICS Design Time interface.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Feature</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This SAP Notes adds a reference about related version variables (if they exist) to the hierarchies.</p>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.30<br /><br />Import Support Package 10 for SAP NetWeaver BW 7.30 (SAPKW73010) into your BW system. The Support Package is available when <strong>SAP Note 1810084</strong> with the short text &quot;SAPBWNews NW 7.30 BW ABAP SP10&quot;, which describes this Support Package in more detail, is released for customers.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.31 (SAP NW BW7.0 EHP 3)<br /><br />Import Support Package 8 for SAP NetWeaver BW 7.31 (SAPKW73108) in your BW system. The Support Package is available when <strong>SAP Note</strong><strong><strong> 1813987</strong></strong> with the short text &quot;SAPBWNews NW BW 7.31/7.03 ABAP SP8&quot;, which describes this Support Package in detail is released for customers.</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />In urgent cases, you can implement the correction instructions as an advance correction.<br /><br /><strong>You must first read SAP Note 875986, which provides information about transaction SNOTE.</strong><br /><br />To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text of the SAP Note contains the words &quot;Preliminary version&quot;.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D047437)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001834480/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001834480/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001834480/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001834480/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001834480/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001834480/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001834480/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001834480/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001834480/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1818593", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.40 ABAP SP 03", "RefUrl": "/notes/1818593"}, {"RefNumber": "1813987", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 08", "RefUrl": "/notes/1813987"}, {"RefNumber": "1810084", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 10", "RefUrl": "/notes/1810084"}, {"RefNumber": "1800952", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 14", "RefUrl": "/notes/1800952"}, {"RefNumber": "1797080", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.11 ABAP SP12", "RefUrl": "/notes/1797080"}, {"RefNumber": "1794836", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 14", "RefUrl": "/notes/1794836"}, {"RefNumber": "1782745", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 31", "RefUrl": "/notes/1782745"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1800952", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 14", "RefUrl": "/notes/1800952 "}, {"RefNumber": "1810084", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 10", "RefUrl": "/notes/1810084 "}, {"RefNumber": "1794836", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 14", "RefUrl": "/notes/1794836 "}, {"RefNumber": "1813987", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 08", "RefUrl": "/notes/1813987 "}, {"RefNumber": "1782745", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 31", "RefUrl": "/notes/1782745 "}, {"RefNumber": "1818593", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.40 ABAP SP 03", "RefUrl": "/notes/1818593 "}, {"RefNumber": "1797080", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.11 ABAP SP12", "RefUrl": "/notes/1797080 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 730", "SupportPackage": "SAPKW73010", "URL": "/supportpackage/SAPKW73010"}, {"SoftwareComponentVersion": "SAP_BW 731", "SupportPackage": "SAPKW73108", "URL": "/supportpackage/SAPKW73108"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 2, "URL": "/corrins/0001834480/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BW&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Business Inform...|<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW73106 - SAPKW73107&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW73008 - SAPKW73009&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Use transaction SE11 to change the following two structures:</P> <OL>1. RSBOLAP_DT_CHARACTERISTIC</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add the following component: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;VERSION_VARIABLE Type RSBOLAP_OBJECT_NAME <P></P> <OL>1. BICS_DT_S_CHARACTERISTIC</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Change the component type of the component VERSION to RSBOLAP_OBJECT_NAME. <P><br/>Then save and activate the structure.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 11, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1736006 ", "URL": "/notes/1736006 ", "Title": "RSBOLAP enhancements for metadata - July 5", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1750788 ", "URL": "/notes/1750788 ", "Title": "BICS metadata service enhancements", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1776999 ", "URL": "/notes/1776999 ", "Title": "Design Time: Incorrect hierarchy sorting", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1811124 ", "URL": "/notes/1811124 ", "Title": "Design Time: Incorrect sort sequence of drill-down characteristics", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1812599 ", "URL": "/notes/1812599 ", "Title": "Design Time: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Datenzellen lie<PERSON>n", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "731", "Number": "1767351 ", "URL": "/notes/1767351 ", "Title": "<PERSON><PERSON><PERSON> missing for Design Time char. in fixed filter", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1778480 ", "URL": "/notes/1778480 ", "Title": "Displaying the dimensions of a characteristic", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1780282 ", "URL": "/notes/1780282 ", "Title": "Design Time texts of dimensions", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1811124 ", "URL": "/notes/1811124 ", "Title": "Design Time: Incorrect sort sequence of drill-down characteristics", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1812599 ", "URL": "/notes/1812599 ", "Title": "Design Time: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Datenzellen lie<PERSON>n", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1817354 ", "URL": "/notes/1817354 ", "Title": "Design Time: Stichtag der Query nicht verfügbar", "Component": "BI-RA-BICS"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "731", "ValidTo": "731", "Number": "1817482 ", "URL": "/notes/1817482 ", "Title": "Design Time: Define whether initial values are read", "Component": "BI-RA-BICS"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}