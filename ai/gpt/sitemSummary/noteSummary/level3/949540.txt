SAP Note 949540 addresses an issue with the incorrect handling of the `CURRENTMEMBER` MDX function when used within a `WITH MEMBER` statement in SAP BW systems. The symptom of the issue is that `CURRENTMEMBER` is not being replaced as expected, which can result in inaccurate data being returned by MDX queries.

The relevant details of this note are as follows:

- The problem defined by this SAP Note is recognized as a program error.
- The note does not list specific relevant terms other than mentioning that the issue pertains to the `CURRENTMEMBER` MDX function (also alluded to as `CURRENT`).

The solution proposed in this note involves importing different Support Packages into the BW system depending on the version in use:

- For BW 3.0B: Import Support Package 32 (BW3.0B Patch32 or SAPKW30B32).
- For BW 3.10 Content: Import Support Package 26 (BW3.10 Patch26 or SAPKW31026).
- For BW 3.50: Import Support Package 18 (BW3.50 Patch18 or SAPKW35018).
- For BW 7.0: Import Support Package 09 (BW7.0 Patch09 or SAPKW70009).

Each Support Package mentioned aims to correct the error and is detailed further in separate SAP Notes specific to those package releases:

- For BW3.0B SP32, refer to Note 0914949.
- For BW3.1 Content SP26, refer to Note 0935962.
- For BW3.5 SP18, refer to Note 0928661.
- For BW7.0 SP09, refer to Note 0914303.

In cases where immediate resolution is required, the note suggests that correction instructions can be used. Additionally, the above-linked notes might provide preliminary information before the official release of the Support Package. In such cases, they will be marked with the term "preliminary version" in the short text.

This SAP Note is providing guidance on resolving specific errors encountered when using MDX queries related to `CURRENTMEMBER` in the SAP BW environment. Users experiencing these issues are advised to import the respective Support Packages to correct the error.